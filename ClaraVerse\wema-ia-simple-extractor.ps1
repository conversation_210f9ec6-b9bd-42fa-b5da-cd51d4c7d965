
# WeMa IA Extracteur Simple
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.IO.Compression.FileSystem

$ErrorActionPreference = "SilentlyContinue"

# Afficher message de démarrage
[System.Windows.Forms.MessageBox]::Show("WeMa IA va se lancer...\nExtraction en cours...", "WeMa IA", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)

try {
    # Créer dossier temporaire unique
    $tempDir = "$env:TEMP\WeMa_IA_" + [System.Guid]::NewGuid().ToString().Substring(0,8)
    New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
    
    # Chemin de l'archive (intégrée dans l'EXE)
    $archivePath = "$PSScriptRoot\wema-ia-app.zip"
    
    if (Test-Path $archivePath) {
        # Extraire l'archive
        [System.IO.Compression.ZipFile]::ExtractToDirectory($archivePath, $tempDir)
        
        # Chercher l'EXE principal
        $exeFiles = Get-ChildItem -Path $tempDir -Filter "*.exe" -Recurse
        $mainExe = $null
        
        foreach ($exe in $exeFiles) {
            if ($exe.Name -like "*WeMa*" -or $exe.Name -like "*IA*") {
                $mainExe = $exe.FullName
                break
            }
        }
        
        if (-not $mainExe -and $exeFiles.Count -gt 0) {
            $mainExe = $exeFiles[0].FullName
        }
        
        if ($mainExe) {
            # Lancer WeMa IA
            Start-Process -FilePath $mainExe -WorkingDirectory (Split-Path $mainExe)
        } else {
            [System.Windows.Forms.MessageBox]::Show("Erreur: Impossible de trouver l'executable WeMa IA", "Erreur", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
        }
    } else {
        [System.Windows.Forms.MessageBox]::Show("Erreur: Archive non trouvee", "Erreur", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
    }
} catch {
    [System.Windows.Forms.MessageBox]::Show("Erreur: $($_.Exception.Message)", "Erreur", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
}
