{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/wgsl/wgsl.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/wgsl/wgsl.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" }\n  ]\n};\nfunction qw(str) {\n  let result = [];\n  const words = str.split(/\\t+|\\r+|\\n+| +/);\n  for (let i = 0; i < words.length; ++i) {\n    if (words[i].length > 0) {\n      result.push(words[i]);\n    }\n  }\n  return result;\n}\nvar atoms = qw(\"true false\");\nvar keywords = qw(`\n\t\t\t  alias\n\t\t\t  break\n\t\t\t  case\n\t\t\t  const\n\t\t\t  const_assert\n\t\t\t  continue\n\t\t\t  continuing\n\t\t\t  default\n\t\t\t  diagnostic\n\t\t\t  discard\n\t\t\t  else\n\t\t\t  enable\n\t\t\t  fn\n\t\t\t  for\n\t\t\t  if\n\t\t\t  let\n\t\t\t  loop\n\t\t\t  override\n\t\t\t  requires\n\t\t\t  return\n\t\t\t  struct\n\t\t\t  switch\n\t\t\t  var\n\t\t\t  while\n\t\t\t  `);\nvar reserved = qw(`\n\t\t\t  NULL\n\t\t\t  Self\n\t\t\t  abstract\n\t\t\t  active\n\t\t\t  alignas\n\t\t\t  alignof\n\t\t\t  as\n\t\t\t  asm\n\t\t\t  asm_fragment\n\t\t\t  async\n\t\t\t  attribute\n\t\t\t  auto\n\t\t\t  await\n\t\t\t  become\n\t\t\t  binding_array\n\t\t\t  cast\n\t\t\t  catch\n\t\t\t  class\n\t\t\t  co_await\n\t\t\t  co_return\n\t\t\t  co_yield\n\t\t\t  coherent\n\t\t\t  column_major\n\t\t\t  common\n\t\t\t  compile\n\t\t\t  compile_fragment\n\t\t\t  concept\n\t\t\t  const_cast\n\t\t\t  consteval\n\t\t\t  constexpr\n\t\t\t  constinit\n\t\t\t  crate\n\t\t\t  debugger\n\t\t\t  decltype\n\t\t\t  delete\n\t\t\t  demote\n\t\t\t  demote_to_helper\n\t\t\t  do\n\t\t\t  dynamic_cast\n\t\t\t  enum\n\t\t\t  explicit\n\t\t\t  export\n\t\t\t  extends\n\t\t\t  extern\n\t\t\t  external\n\t\t\t  fallthrough\n\t\t\t  filter\n\t\t\t  final\n\t\t\t  finally\n\t\t\t  friend\n\t\t\t  from\n\t\t\t  fxgroup\n\t\t\t  get\n\t\t\t  goto\n\t\t\t  groupshared\n\t\t\t  highp\n\t\t\t  impl\n\t\t\t  implements\n\t\t\t  import\n\t\t\t  inline\n\t\t\t  instanceof\n\t\t\t  interface\n\t\t\t  layout\n\t\t\t  lowp\n\t\t\t  macro\n\t\t\t  macro_rules\n\t\t\t  match\n\t\t\t  mediump\n\t\t\t  meta\n\t\t\t  mod\n\t\t\t  module\n\t\t\t  move\n\t\t\t  mut\n\t\t\t  mutable\n\t\t\t  namespace\n\t\t\t  new\n\t\t\t  nil\n\t\t\t  noexcept\n\t\t\t  noinline\n\t\t\t  nointerpolation\n\t\t\t  noperspective\n\t\t\t  null\n\t\t\t  nullptr\n\t\t\t  of\n\t\t\t  operator\n\t\t\t  package\n\t\t\t  packoffset\n\t\t\t  partition\n\t\t\t  pass\n\t\t\t  patch\n\t\t\t  pixelfragment\n\t\t\t  precise\n\t\t\t  precision\n\t\t\t  premerge\n\t\t\t  priv\n\t\t\t  protected\n\t\t\t  pub\n\t\t\t  public\n\t\t\t  readonly\n\t\t\t  ref\n\t\t\t  regardless\n\t\t\t  register\n\t\t\t  reinterpret_cast\n\t\t\t  require\n\t\t\t  resource\n\t\t\t  restrict\n\t\t\t  self\n\t\t\t  set\n\t\t\t  shared\n\t\t\t  sizeof\n\t\t\t  smooth\n\t\t\t  snorm\n\t\t\t  static\n\t\t\t  static_assert\n\t\t\t  static_cast\n\t\t\t  std\n\t\t\t  subroutine\n\t\t\t  super\n\t\t\t  target\n\t\t\t  template\n\t\t\t  this\n\t\t\t  thread_local\n\t\t\t  throw\n\t\t\t  trait\n\t\t\t  try\n\t\t\t  type\n\t\t\t  typedef\n\t\t\t  typeid\n\t\t\t  typename\n\t\t\t  typeof\n\t\t\t  union\n\t\t\t  unless\n\t\t\t  unorm\n\t\t\t  unsafe\n\t\t\t  unsized\n\t\t\t  use\n\t\t\t  using\n\t\t\t  varying\n\t\t\t  virtual\n\t\t\t  volatile\n\t\t\t  wgsl\n\t\t\t  where\n\t\t\t  with\n\t\t\t  writeonly\n\t\t\t  yield\n\t\t\t  `);\nvar predeclared_enums = qw(`\n\t\tread write read_write\n\t\tfunction private workgroup uniform storage\n\t\tperspective linear flat\n\t\tcenter centroid sample\n\t\tvertex_index instance_index position front_facing frag_depth\n\t\t\tlocal_invocation_id local_invocation_index\n\t\t\tglobal_invocation_id workgroup_id num_workgroups\n\t\t\tsample_index sample_mask\n\t\trgba8unorm\n\t\trgba8snorm\n\t\trgba8uint\n\t\trgba8sint\n\t\trgba16uint\n\t\trgba16sint\n\t\trgba16float\n\t\tr32uint\n\t\tr32sint\n\t\tr32float\n\t\trg32uint\n\t\trg32sint\n\t\trg32float\n\t\trgba32uint\n\t\trgba32sint\n\t\trgba32float\n\t\tbgra8unorm\n`);\nvar predeclared_types = qw(`\n\t\tbool\n\t\tf16\n\t\tf32\n\t\ti32\n\t\tsampler sampler_comparison\n\t\ttexture_depth_2d\n\t\ttexture_depth_2d_array\n\t\ttexture_depth_cube\n\t\ttexture_depth_cube_array\n\t\ttexture_depth_multisampled_2d\n\t\ttexture_external\n\t\ttexture_external\n\t\tu32\n\t\t`);\nvar predeclared_type_generators = qw(`\n\t\tarray\n\t\tatomic\n\t\tmat2x2\n\t\tmat2x3\n\t\tmat2x4\n\t\tmat3x2\n\t\tmat3x3\n\t\tmat3x4\n\t\tmat4x2\n\t\tmat4x3\n\t\tmat4x4\n\t\tptr\n\t\ttexture_1d\n\t\ttexture_2d\n\t\ttexture_2d_array\n\t\ttexture_3d\n\t\ttexture_cube\n\t\ttexture_cube_array\n\t\ttexture_multisampled_2d\n\t\ttexture_storage_1d\n\t\ttexture_storage_2d\n\t\ttexture_storage_2d_array\n\t\ttexture_storage_3d\n\t\tvec2\n\t\tvec3\n\t\tvec4\n\t\t`);\nvar predeclared_type_aliases = qw(`\n\t\tvec2i vec3i vec4i\n\t\tvec2u vec3u vec4u\n\t\tvec2f vec3f vec4f\n\t\tvec2h vec3h vec4h\n\t\tmat2x2f mat2x3f mat2x4f\n\t\tmat3x2f mat3x3f mat3x4f\n\t\tmat4x2f mat4x3f mat4x4f\n\t\tmat2x2h mat2x3h mat2x4h\n\t\tmat3x2h mat3x3h mat3x4h\n\t\tmat4x2h mat4x3h mat4x4h\n\t\t`);\nvar predeclared_intrinsics = qw(`\n  bitcast all any select arrayLength abs acos acosh asin asinh atan atanh atan2\n  ceil clamp cos cosh countLeadingZeros countOneBits countTrailingZeros cross\n  degrees determinant distance dot exp exp2 extractBits faceForward firstLeadingBit\n  firstTrailingBit floor fma fract frexp inverseBits inverseSqrt ldexp length\n  log log2 max min mix modf normalize pow quantizeToF16 radians reflect refract\n  reverseBits round saturate sign sin sinh smoothstep sqrt step tan tanh transpose\n  trunc dpdx dpdxCoarse dpdxFine dpdy dpdyCoarse dpdyFine fwidth fwidthCoarse fwidthFine\n  textureDimensions textureGather textureGatherCompare textureLoad textureNumLayers\n  textureNumLevels textureNumSamples textureSample textureSampleBias textureSampleCompare\n  textureSampleCompareLevel textureSampleGrad textureSampleLevel textureSampleBaseClampToEdge\n  textureStore atomicLoad atomicStore atomicAdd atomicSub atomicMax atomicMin\n  atomicAnd atomicOr atomicXor atomicExchange atomicCompareExchangeWeak pack4x8snorm\n  pack4x8unorm pack2x16snorm pack2x16unorm pack2x16float unpack4x8snorm unpack4x8unorm\n  unpack2x16snorm unpack2x16unorm unpack2x16float storageBarrier workgroupBarrier\n  workgroupUniformLoad\n`);\nvar operators = qw(`\n\t\t\t\t\t &\n\t\t\t\t\t &&\n\t\t\t\t\t ->\n\t\t\t\t\t /\n\t\t\t\t\t =\n\t\t\t\t\t ==\n\t\t\t\t\t !=\n\t\t\t\t\t >\n\t\t\t\t\t >=\n\t\t\t\t\t <\n\t\t\t\t\t <=\n\t\t\t\t\t %\n\t\t\t\t\t -\n\t\t\t\t\t --\n\t\t\t\t\t +\n\t\t\t\t\t ++\n\t\t\t\t\t |\n\t\t\t\t\t ||\n\t\t\t\t\t *\n\t\t\t\t\t <<\n\t\t\t\t\t >>\n\t\t\t\t\t +=\n\t\t\t\t\t -=\n\t\t\t\t\t *=\n\t\t\t\t\t /=\n\t\t\t\t\t %=\n\t\t\t\t\t &=\n\t\t\t\t\t |=\n\t\t\t\t\t ^=\n\t\t\t\t\t >>=\n\t\t\t\t\t <<=\n\t\t\t\t\t `);\nvar directive_re = /enable|requires|diagnostic/;\nvar ident_re = /[_\\p{XID_Start}]\\p{XID_Continue}*/u;\nvar predefined_token = \"variable.predefined\";\nvar language = {\n  tokenPostfix: \".wgsl\",\n  defaultToken: \"invalid\",\n  unicode: true,\n  atoms,\n  keywords,\n  reserved,\n  predeclared_enums,\n  predeclared_types,\n  predeclared_type_generators,\n  predeclared_type_aliases,\n  predeclared_intrinsics,\n  operators,\n  symbols: /[!%&*+\\-\\.\\/:;<=>^|_~,]+/,\n  tokenizer: {\n    root: [\n      [directive_re, \"keyword\", \"@directive\"],\n      [\n        // Identifier-like things, but also include '_'\n        ident_re,\n        {\n          cases: {\n            \"@atoms\": predefined_token,\n            \"@keywords\": \"keyword\",\n            \"@reserved\": \"invalid\",\n            \"@predeclared_enums\": predefined_token,\n            \"@predeclared_types\": predefined_token,\n            \"@predeclared_type_generators\": predefined_token,\n            \"@predeclared_type_aliases\": predefined_token,\n            \"@predeclared_intrinsics\": predefined_token,\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@commentOrSpace\" },\n      { include: \"@numbers\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [\"@\", \"annotation\", \"@attribute\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@default\": \"delimiter\"\n          }\n        }\n      ],\n      [/./, \"invalid\"]\n    ],\n    commentOrSpace: [\n      [/\\s+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@blockComment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    blockComment: [\n      // Soak up uninteresting text: anything except * or /\n      [/[^\\/*]+/, \"comment\"],\n      // Recognize the start of a nested block comment.\n      [/\\/\\*/, \"comment\", \"@push\"],\n      // Recognize the end of a nested block comment.\n      [/\\*\\//, \"comment\", \"@pop\"],\n      // Recognize insignificant * and /\n      [/[\\/*]/, \"comment\"]\n    ],\n    attribute: [\n      // For things like '@fragment' both '@' and 'fragment'\n      // are marked as annotations.  This should work even if\n      // there are spaces or comments between the two tokens.\n      { include: \"@commentOrSpace\" },\n      [/\\w+/, \"annotation\", \"@pop\"]\n    ],\n    directive: [\n      // For things like 'enable f16;', 'enable' maps to 'meta'\n      // and 'f16' maps to 'meta.tag'.\n      { include: \"@commentOrSpace\" },\n      [/[()]/, \"@brackets\"],\n      [/,/, \"delimiter\"],\n      [ident_re, \"meta.content\"],\n      [/;/, \"delimiter\", \"@pop\"]\n    ],\n    numbers: [\n      // Decimal float literals\n      // https://www.w3.org/TR/WGSL/#syntax-decimal_float_literal\n      // 0, with type-specifying suffix.\n      [/0[fh]/, \"number.float\"],\n      // Other decimal integer, with type-specifying suffix.\n      [/[1-9][0-9]*[fh]/, \"number.float\"],\n      // Has decimal point, at least one digit after decimal.\n      [/[0-9]*\\.[0-9]+([eE][+-]?[0-9]+)?[fh]?/, \"number.float\"],\n      // Has decimal point, at least one digit before decimal.\n      [/[0-9]+\\.[0-9]*([eE][+-]?[0-9]+)?[fh]?/, \"number.float\"],\n      // Has at least one digit, and has an exponent.\n      [/[0-9]+[eE][+-]?[0-9]+[fh]?/, \"number.float\"],\n      // Hex float literals\n      // https://www.w3.org/TR/WGSL/#syntax-hex_float_literal\n      [/0[xX][0-9a-fA-F]*\\.[0-9a-fA-F]+(?:[pP][+-]?[0-9]+[fh]?)?/, \"number.hex\"],\n      [/0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*(?:[pP][+-]?[0-9]+[fh]?)?/, \"number.hex\"],\n      [/0[xX][0-9a-fA-F]+[pP][+-]?[0-9]+[fh]?/, \"number.hex\"],\n      // Hexadecimal integer literals\n      // https://www.w3.org/TR/WGSL/#syntax-hex_int_literal\n      [/0[xX][0-9a-fA-F]+[iu]?/, \"number.hex\"],\n      // Decimal integer literals\n      // https://www.w3.org/TR/WGSL/#syntax-decimal_int_literal\n      // We need two rules here because 01 is not valid.\n      [/[1-9][0-9]*[iu]?/, \"number\"],\n      [/0[iu]?/, \"number\"]\n      // Must match last\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,SAAS,GAAG,KAAK;AACf,MAAI,SAAS,CAAC;AACd,QAAM,QAAQ,IAAI,MAAM,gBAAgB;AACxC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,QAAI,MAAM,CAAC,EAAE,SAAS,GAAG;AACvB,aAAO,KAAK,MAAM,CAAC,CAAC;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,QAAQ,GAAG,YAAY;AAC3B,IAAI,WAAW,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAyBZ;AACN,IAAI,WAAW,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkJZ;AACN,IAAI,oBAAoB,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,CA0B1B;AACD,IAAI,oBAAoB,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAcxB;AACH,IAAI,8BAA8B,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GA2BlC;AACH,IAAI,2BAA2B,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAW/B;AACH,IAAI,yBAAyB,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,CAgB/B;AACD,IAAI,YAAY,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAgCZ;AACP,IAAI,eAAe;AACnB,IAAI,WAAW,WAAC,uCAAkC,GAAC;AACnD,IAAI,mBAAmB;AACvB,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,SAAS;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,CAAC,cAAc,WAAW,YAAY;AAAA,MACtC;AAAA;AAAA,QAEE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,UAAU;AAAA,YACV,aAAa;AAAA,YACb,aAAa;AAAA,YACb,sBAAsB;AAAA,YACtB,sBAAsB;AAAA,YACtB,gCAAgC;AAAA,YAChC,6BAA6B;AAAA,YAC7B,2BAA2B;AAAA,YAC3B,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,kBAAkB;AAAA,MAC7B,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,KAAK,cAAc,YAAY;AAAA,MAChC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,KAAK,SAAS;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,CAAC,OAAO,OAAO;AAAA,MACf,CAAC,QAAQ,WAAW,eAAe;AAAA,MACnC,CAAC,WAAW,SAAS;AAAA,IACvB;AAAA,IACA,cAAc;AAAA;AAAA,MAEZ,CAAC,WAAW,SAAS;AAAA;AAAA,MAErB,CAAC,QAAQ,WAAW,OAAO;AAAA;AAAA,MAE3B,CAAC,QAAQ,WAAW,MAAM;AAAA;AAAA,MAE1B,CAAC,SAAS,SAAS;AAAA,IACrB;AAAA,IACA,WAAW;AAAA;AAAA;AAAA;AAAA,MAIT,EAAE,SAAS,kBAAkB;AAAA,MAC7B,CAAC,OAAO,cAAc,MAAM;AAAA,IAC9B;AAAA,IACA,WAAW;AAAA;AAAA;AAAA,MAGT,EAAE,SAAS,kBAAkB;AAAA,MAC7B,CAAC,QAAQ,WAAW;AAAA,MACpB,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,UAAU,cAAc;AAAA,MACzB,CAAC,KAAK,aAAa,MAAM;AAAA,IAC3B;AAAA,IACA,SAAS;AAAA;AAAA;AAAA;AAAA,MAIP,CAAC,SAAS,cAAc;AAAA;AAAA,MAExB,CAAC,mBAAmB,cAAc;AAAA;AAAA,MAElC,CAAC,yCAAyC,cAAc;AAAA;AAAA,MAExD,CAAC,yCAAyC,cAAc;AAAA;AAAA,MAExD,CAAC,8BAA8B,cAAc;AAAA;AAAA;AAAA,MAG7C,CAAC,4DAA4D,YAAY;AAAA,MACzE,CAAC,4DAA4D,YAAY;AAAA,MACzE,CAAC,yCAAyC,YAAY;AAAA;AAAA;AAAA,MAGtD,CAAC,0BAA0B,YAAY;AAAA;AAAA;AAAA;AAAA,MAIvC,CAAC,oBAAoB,QAAQ;AAAA,MAC7B,CAAC,UAAU,QAAQ;AAAA;AAAA,IAErB;AAAA,EACF;AACF;", "names": []}