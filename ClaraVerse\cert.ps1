Write-Host "Creation certificat self-signed..." -ForegroundColor Green

$certSubject = "CN=WeMa IA Team, O=WeMa IA, C=FR"
$pfxPassword = "WeMaIA2025!"

try {
    $cert = New-SelfSignedCertificate -Subject $certSubject -Type CodeSigningCert -KeyUsage DigitalSignature -FriendlyName "WeMa IA Code Signing" -CertStoreLocation "Cert:\CurrentUser\My" -KeyLength 2048 -Provider "Microsoft Enhanced RSA and AES Cryptographic Provider" -KeyExportPolicy Exportable -KeySpec Signature -HashAlgorithm SHA256 -NotAfter (Get-Date).AddYears(3)

    Write-Host "Certificat cree!" -ForegroundColor Green
    Write-Host "Thumbprint: $($cert.Thumbprint)" -ForegroundColor Cyan
    
    $pfxPath = "wema-ia-cert.pfx"
    $securePassword = ConvertTo-SecureString -String $pfxPassword -Force -AsPlainText
    Export-PfxCertificate -Cert $cert -FilePath $pfxPath -Password $securePassword
    
    Write-Host "Certificat exporte: $pfxPath" -ForegroundColor Green
    
    $rootStore = Get-Item "Cert:\CurrentUser\Root"
    $rootStore.Open("ReadWrite")
    $rootStore.Add($cert)
    $rootStore.Close()
    
    Write-Host "Certificat ajoute au magasin Trusted Root" -ForegroundColor Green
    Write-Host "CERTIFICAT PRET!" -ForegroundColor Magenta
    Write-Host "Fichier PFX: $pfxPath" -ForegroundColor Yellow
    Write-Host "Mot de passe: $pfxPassword" -ForegroundColor Yellow
    
} catch {
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
}
