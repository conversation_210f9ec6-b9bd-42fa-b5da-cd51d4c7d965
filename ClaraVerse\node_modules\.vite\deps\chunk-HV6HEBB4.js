import {
  require_xml_doc
} from "./chunk-HBOTRME5.js";
import {
  require_xojo
} from "./chunk-TYLYMMXY.js";
import {
  require_xquery
} from "./chunk-STJXT3KM.js";
import {
  require_yang
} from "./chunk-XFDJNTUM.js";
import {
  require_zig
} from "./chunk-SQZY3REL.js";
import {
  require_core
} from "./chunk-QMFGNQ6R.js";
import {
  require_visual_basic
} from "./chunk-ZQOXRBL6.js";
import {
  require_warpscript
} from "./chunk-ESUE7X6F.js";
import {
  require_wasm
} from "./chunk-YPMIWJBZ.js";
import {
  require_web_idl
} from "./chunk-SXN2GOXE.js";
import {
  require_wiki
} from "./chunk-RCLFYJAP.js";
import {
  require_wolfram
} from "./chunk-RTQI4JPY.js";
import {
  require_wren
} from "./chunk-NZOCTC33.js";
import {
  require_xeora
} from "./chunk-TE3VEQRV.js";
import {
  require_uorazor
} from "./chunk-7EL553DD.js";
import {
  require_uri
} from "./chunk-B2ZQPA3F.js";
import {
  require_v
} from "./chunk-L4VRJLNC.js";
import {
  require_vala
} from "./chunk-YXSLRCQH.js";
import {
  require_velocity
} from "./chunk-UROLJGAK.js";
import {
  require_verilog
} from "./chunk-VRNRLFHK.js";
import {
  require_vhdl
} from "./chunk-GQJZ7ULK.js";
import {
  require_vim
} from "./chunk-RXJPXCWT.js";
import {
  require_textile
} from "./chunk-LWKMUAWU.js";
import {
  require_toml
} from "./chunk-HE67S3O4.js";
import {
  require_tremor
} from "./chunk-DFIKPA7T.js";
import {
  require_tsx
} from "./chunk-YBMSJRNB.js";
import {
  require_tt2
} from "./chunk-5QSCTKS4.js";
import {
  require_twig
} from "./chunk-HF4KGWKC.js";
import {
  require_typoscript
} from "./chunk-GBAZNQ7U.js";
import {
  require_unrealscript
} from "./chunk-NHBMPXDC.js";
import {
  require_systemd
} from "./chunk-HJQWESH6.js";
import {
  require_t4_cs
} from "./chunk-YTU3CDHC.js";
import {
  require_t4_vb
} from "./chunk-R6HA27QO.js";
import {
  require_t4_templating
} from "./chunk-BA2AP7RN.js";
import {
  require_vbnet
} from "./chunk-HXQ77RVE.js";
import {
  require_tap
} from "./chunk-MZWVZLRP.js";
import {
  require_yaml
} from "./chunk-HUYGUO6E.js";
import {
  require_tcl
} from "./chunk-FNILEZE3.js";
import {
  require_sparql
} from "./chunk-DK2533VX.js";
import {
  require_turtle
} from "./chunk-NXMMMB4D.js";
import {
  require_splunk_spl
} from "./chunk-YWNJIZPH.js";
import {
  require_sqf
} from "./chunk-FZZWHYNA.js";
import {
  require_squirrel
} from "./chunk-JSTMP6TL.js";
import {
  require_stan
} from "./chunk-ID7NGVGG.js";
import {
  require_stylus
} from "./chunk-LSIC7YOR.js";
import {
  require_swift
} from "./chunk-NWZY764F.js";
import {
  require_shell_session
} from "./chunk-FG473XKQ.js";
import {
  require_smali
} from "./chunk-RHPEDFOI.js";
import {
  require_smalltalk
} from "./chunk-IPSLKII3.js";
import {
  require_smarty
} from "./chunk-GGJZPX2D.js";
import {
  require_sml
} from "./chunk-SZOGME3D.js";
import {
  require_solidity
} from "./chunk-NLJKNNJW.js";
import {
  require_solution_file
} from "./chunk-BNJSF36M.js";
import {
  require_soy
} from "./chunk-5TD2WYIF.js";
import {
  require_rip
} from "./chunk-5UT427DH.js";
import {
  require_roboconf
} from "./chunk-OBBVLZDB.js";
import {
  require_robotframework
} from "./chunk-LH4RLGDI.js";
import {
  require_rust
} from "./chunk-6CC55FMM.js";
import {
  require_sas
} from "./chunk-4PXSP2TU.js";
import {
  require_sass
} from "./chunk-D4DLYJ2U.js";
import {
  require_scala
} from "./chunk-Q2MU2PUP.js";
import {
  require_scss
} from "./chunk-4XGNQ3FK.js";
import {
  require_qsharp
} from "./chunk-I4DKFH53.js";
import {
  require_r
} from "./chunk-Z6UGORBF.js";
import {
  require_racket
} from "./chunk-ZCCSK52X.js";
import {
  require_reason
} from "./chunk-IDMY2HGB.js";
import {
  require_regex
} from "./chunk-QHX3A2UJ.js";
import {
  require_rego
} from "./chunk-JGK4BEUE.js";
import {
  require_renpy
} from "./chunk-2R3VKI2X.js";
import {
  require_rest
} from "./chunk-VFIJLMBN.js";
import {
  require_puppet
} from "./chunk-YYFASRGA.js";
import {
  require_pure
} from "./chunk-JEY2HN5S.js";
import {
  require_purebasic
} from "./chunk-32RAIWUB.js";
import {
  require_purescript
} from "./chunk-SVAAOHYS.js";
import {
  require_python
} from "./chunk-VVY5O5MR.js";
import {
  require_q
} from "./chunk-2IL7S4S4.js";
import {
  require_qml
} from "./chunk-QQ7ACYTO.js";
import {
  require_qore
} from "./chunk-QKXCSWGF.js";
import {
  require_powershell
} from "./chunk-3BPUYJ7R.js";
import {
  require_processing
} from "./chunk-HOBNT444.js";
import {
  require_prolog
} from "./chunk-VB2VW2JK.js";
import {
  require_promql
} from "./chunk-6MFVAL6K.js";
import {
  require_properties
} from "./chunk-AENXDVZF.js";
import {
  require_protobuf
} from "./chunk-EEN2HURC.js";
import {
  require_psl
} from "./chunk-OLAOIJ62.js";
import {
  require_pug
} from "./chunk-QYUVWVOC.js";
import {
  require_pascaligo
} from "./chunk-QQH2XMA4.js";
import {
  require_pcaxis
} from "./chunk-XORPN5EG.js";
import {
  require_peoplecode
} from "./chunk-RCHQJEW7.js";
import {
  require_perl
} from "./chunk-MIX3CFBS.js";
import {
  require_php_extras
} from "./chunk-KEVCWDG5.js";
import {
  require_phpdoc
} from "./chunk-ZG5NNMLW.js";
import {
  require_plsql
} from "./chunk-PFEJNCAN.js";
import {
  require_powerquery
} from "./chunk-UBW5YQV5.js";
import {
  require_objectivec
} from "./chunk-JA3HF4AI.js";
import {
  require_ocaml
} from "./chunk-QHYWMED2.js";
import {
  require_opencl
} from "./chunk-ZWB3ES6T.js";
import {
  require_openqasm
} from "./chunk-FEHONF6I.js";
import {
  require_oz
} from "./chunk-S2A3G6E5.js";
import {
  require_parigp
} from "./chunk-K7O5BKE7.js";
import {
  require_parser
} from "./chunk-HAUZZMYV.js";
import {
  require_pascal
} from "./chunk-54WEZYCR.js";
import {
  require_naniscript
} from "./chunk-Q5ZCEGYF.js";
import {
  require_nasm
} from "./chunk-DVQRWFMK.js";
import {
  require_neon
} from "./chunk-VCVXKDRZ.js";
import {
  require_nevod
} from "./chunk-6OONWFNZ.js";
import {
  require_nginx
} from "./chunk-NLYZ4QEF.js";
import {
  require_nim
} from "./chunk-ON67LIHY.js";
import {
  require_nix
} from "./chunk-R744DBVB.js";
import {
  require_nsis
} from "./chunk-YUJ663T6.js";
import {
  require_mermaid
} from "./chunk-MNFKURCJ.js";
import {
  require_mizar
} from "./chunk-JT7YQBJ2.js";
import {
  require_mongodb
} from "./chunk-JDZ2FEXX.js";
import {
  require_monkey
} from "./chunk-XV2VRXR4.js";
import {
  require_moonscript
} from "./chunk-NQPQCJWJ.js";
import {
  require_n1ql
} from "./chunk-QWVC6FDU.js";
import {
  require_n4js
} from "./chunk-E5EDTRZX.js";
import {
  require_nand2tetris_hdl
} from "./chunk-I5GQLANJ.js";
import {
  require_lolcode
} from "./chunk-KRUPZMYE.js";
import {
  require_magma
} from "./chunk-U7BORWDV.js";
import {
  require_makefile
} from "./chunk-Q5XJ6SQR.js";
import {
  require_markdown
} from "./chunk-LC7PQ2BO.js";
import {
  require_matlab
} from "./chunk-Y6HRTNXA.js";
import {
  require_maxscript
} from "./chunk-2JLGHMTJ.js";
import {
  require_mel
} from "./chunk-P7NN5HH3.js";
import {
  require_less
} from "./chunk-ZQQXX6BT.js";
import {
  require_lilypond
} from "./chunk-5MVAHEAK.js";
import {
  require_scheme
} from "./chunk-BVYS3EHE.js";
import {
  require_liquid
} from "./chunk-H6GBUGE6.js";
import {
  require_lisp
} from "./chunk-7R3W2HUA.js";
import {
  require_livescript
} from "./chunk-B5TYXR7T.js";
import {
  require_llvm
} from "./chunk-GMYB7TUQ.js";
import {
  require_log
} from "./chunk-4UB652WI.js";
import {
  require_keepalived
} from "./chunk-5C7R4KVU.js";
import {
  require_keyman
} from "./chunk-B3UD63N5.js";
import {
  require_kotlin
} from "./chunk-7A545X4C.js";
import {
  require_kumir
} from "./chunk-KZBA4RL7.js";
import {
  require_kusto
} from "./chunk-CWVHLH65.js";
import {
  require_latex
} from "./chunk-7NV5JLVB.js";
import {
  require_latte
} from "./chunk-R2DW2Q52.js";
import {
  require_php
} from "./chunk-7G763IV2.js";
import {
  require_jsdoc
} from "./chunk-MI5RRC76.js";
import {
  require_typescript
} from "./chunk-DUFNIKAP.js";
import {
  require_json5
} from "./chunk-6NOKEMWO.js";
import {
  require_jsonp
} from "./chunk-LGEXXQ7I.js";
import {
  require_json
} from "./chunk-E66GUCVU.js";
import {
  require_jsstacktrace
} from "./chunk-LO4SXVGY.js";
import {
  require_jsx
} from "./chunk-F5QAORRO.js";
import {
  require_julia
} from "./chunk-ULC27EOO.js";
import {
  require_javadoc
} from "./chunk-PROAJMJU.js";
import {
  require_javastacktrace
} from "./chunk-5NYARB3H.js";
import {
  require_jexl
} from "./chunk-MNBUMXCQ.js";
import {
  require_jolie
} from "./chunk-B5TVW7KG.js";
import {
  require_jq
} from "./chunk-HZN3BWE2.js";
import {
  require_js_extras
} from "./chunk-IEPH3GGB.js";
import {
  require_js_templates
} from "./chunk-ZZ3OSUZE.js";
import {
  require_iecst
} from "./chunk-PEU5Q7DF.js";
import {
  require_ignore
} from "./chunk-VSECFODF.js";
import {
  require_inform7
} from "./chunk-YINPUVFM.js";
import {
  require_ini
} from "./chunk-JUG7QYHO.js";
import {
  require_io
} from "./chunk-SSC3ZOOP.js";
import {
  require_j
} from "./chunk-V2XZJ46L.js";
import {
  require_java
} from "./chunk-VJJ43FOJ.js";
import {
  require_javadoclike
} from "./chunk-K7MZCD3N.js";
import {
  require_hoon
} from "./chunk-I2DMEYK7.js";
import {
  require_hpkp
} from "./chunk-KOED4M7G.js";
import {
  require_hsts
} from "./chunk-GLTYBDRF.js";
import {
  require_http
} from "./chunk-NT6IWK5O.js";
import {
  require_ichigojam
} from "./chunk-UO5TUOTW.js";
import {
  require_icon
} from "./chunk-ZLUPFFAL.js";
import {
  require_icu_message_format
} from "./chunk-VCJPZLDA.js";
import {
  require_idris
} from "./chunk-2MR6UE2P.js";
import {
  require_graphql
} from "./chunk-4J5DPNPS.js";
import {
  require_groovy
} from "./chunk-UU2CEDSG.js";
import {
  require_haml
} from "./chunk-CJ64TGID.js";
import {
  require_handlebars
} from "./chunk-UX6J5DDU.js";
import {
  require_haskell
} from "./chunk-XLU3JEDA.js";
import {
  require_haxe
} from "./chunk-LKAPE73F.js";
import {
  require_hcl
} from "./chunk-BZ4V5I7H.js";
import {
  require_hlsl
} from "./chunk-YWWXTNBS.js";
import {
  require_gedcom
} from "./chunk-AQKUODSP.js";
import {
  require_gherkin
} from "./chunk-KFSIW3IW.js";
import {
  require_git
} from "./chunk-V35RCFTO.js";
import {
  require_glsl
} from "./chunk-MAFRCNBI.js";
import {
  require_gml
} from "./chunk-NBSSKB7O.js";
import {
  require_gn
} from "./chunk-J2YGKEO3.js";
import {
  require_go_module
} from "./chunk-ZJYSMOR6.js";
import {
  require_go
} from "./chunk-APIPJM7D.js";
import {
  require_firestore_security_rules
} from "./chunk-2AD66P7M.js";
import {
  require_flow
} from "./chunk-Y4ZRI66K.js";
import {
  require_fortran
} from "./chunk-VT2XJSGA.js";
import {
  require_fsharp
} from "./chunk-LXBHZODK.js";
import {
  require_ftl
} from "./chunk-EGJHLFPW.js";
import {
  require_gap
} from "./chunk-PTWZZOPJ.js";
import {
  require_gcode
} from "./chunk-NMMGQECH.js";
import {
  require_gdscript
} from "./chunk-ZA6KSTHK.js";
import {
  require_elm
} from "./chunk-UCDASILQ.js";
import {
  require_erb
} from "./chunk-7K753R2X.js";
import {
  require_erlang
} from "./chunk-NVGGS2HF.js";
import {
  require_etlua
} from "./chunk-7N6TSQIE.js";
import {
  require_lua
} from "./chunk-E52TISZJ.js";
import {
  require_excel_formula
} from "./chunk-FUHNE33V.js";
import {
  require_factor
} from "./chunk-EGYJNWDF.js";
import {
  require_false
} from "./chunk-IOEJGXEQ.js";
import {
  require_dns_zone_file
} from "./chunk-F7O76JKG.js";
import {
  require_docker
} from "./chunk-4SPALB3T.js";
import {
  require_dot
} from "./chunk-OWNUEE7X.js";
import {
  require_ebnf
} from "./chunk-2RS5I4XH.js";
import {
  require_editorconfig
} from "./chunk-4Y2K7YCS.js";
import {
  require_eiffel
} from "./chunk-7MIV3R63.js";
import {
  require_ejs
} from "./chunk-I2DIGQNE.js";
import {
  require_elixir
} from "./chunk-C2RZI2AN.js";
import {
  require_d
} from "./chunk-WRNUYOLO.js";
import {
  require_dart
} from "./chunk-YNZIQPJ5.js";
import {
  require_dataweave
} from "./chunk-VFRXMGYE.js";
import {
  require_dax
} from "./chunk-ZWCPNOTB.js";
import {
  require_dhall
} from "./chunk-CWVQEJ7U.js";
import {
  require_diff
} from "./chunk-72CIL2VT.js";
import {
  require_django
} from "./chunk-KA2BMCLQ.js";
import {
  require_markup_templating
} from "./chunk-XHZ24OTN.js";
import {
  require_crystal
} from "./chunk-SMVMUFXX.js";
import {
  require_ruby
} from "./chunk-5P4IDC6R.js";
import {
  require_cshtml
} from "./chunk-6W3XGNB3.js";
import {
  require_csp
} from "./chunk-DTZJ4GI2.js";
import {
  require_css_extras
} from "./chunk-CFYFOGOG.js";
import {
  require_csv
} from "./chunk-5TTPPHSI.js";
import {
  require_cypher
} from "./chunk-GXBWWVOX.js";
import {
  require_cil
} from "./chunk-W2TXVSON.js";
import {
  require_clojure
} from "./chunk-FEBKLNZK.js";
import {
  require_cmake
} from "./chunk-ZW6ZUHF7.js";
import {
  require_cobol
} from "./chunk-QPDFIAHZ.js";
import {
  require_coffeescript
} from "./chunk-Y3SOPRQH.js";
import {
  require_concurnas
} from "./chunk-KEW6EN3T.js";
import {
  require_coq
} from "./chunk-JCXIS4UZ.js";
import {
  require_bison
} from "./chunk-5Q7HKYN2.js";
import {
  require_bnf
} from "./chunk-GF3A7NDP.js";
import {
  require_brainfuck
} from "./chunk-IS3SQ4VF.js";
import {
  require_brightscript
} from "./chunk-MOTVEADB.js";
import {
  require_bro
} from "./chunk-YZFMLJS5.js";
import {
  require_bsl
} from "./chunk-WDNEI4K3.js";
import {
  require_cfscript
} from "./chunk-2CFAVYHG.js";
import {
  require_chaiscript
} from "./chunk-RSNUL5UU.js";
import {
  require_avisynth
} from "./chunk-5VHIWGV4.js";
import {
  require_avro_idl
} from "./chunk-UZHOAWS6.js";
import {
  require_bash
} from "./chunk-42V4IWQY.js";
import {
  require_basic
} from "./chunk-CC7DF5RK.js";
import {
  require_batch
} from "./chunk-RKDAJDL6.js";
import {
  require_bbcode
} from "./chunk-QE5UFGZO.js";
import {
  require_bicep
} from "./chunk-EO6A2GER.js";
import {
  require_birb
} from "./chunk-XTCJSUU2.js";
import {
  require_arff
} from "./chunk-5F7FVVG4.js";
import {
  require_asciidoc
} from "./chunk-WYMJI3BK.js";
import {
  require_asm6502
} from "./chunk-L45WPJBY.js";
import {
  require_asmatmel
} from "./chunk-UN2EEZLS.js";
import {
  require_aspnet
} from "./chunk-32ARTQ5X.js";
import {
  require_csharp
} from "./chunk-S64323KV.js";
import {
  require_autohotkey
} from "./chunk-ZDTZGOGP.js";
import {
  require_autoit
} from "./chunk-EJKAC3BU.js";
import {
  require_apex
} from "./chunk-Q6R7RZ7S.js";
import {
  require_sql
} from "./chunk-IZCCKDMU.js";
import {
  require_apl
} from "./chunk-62STQXQW.js";
import {
  require_applescript
} from "./chunk-TPICM7LI.js";
import {
  require_aql
} from "./chunk-JFREUYI7.js";
import {
  require_arduino
} from "./chunk-QH7C3ZWL.js";
import {
  require_cpp
} from "./chunk-VGAC56IX.js";
import {
  require_c
} from "./chunk-QDVHD3AH.js";
import {
  require_abap
} from "./chunk-465LYI5X.js";
import {
  require_abnf
} from "./chunk-65AV5Z7N.js";
import {
  require_actionscript
} from "./chunk-XYZZGH4L.js";
import {
  require_ada
} from "./chunk-SALOQ5LP.js";
import {
  require_agda
} from "./chunk-6ZCFDMYS.js";
import {
  require_al
} from "./chunk-FIWB2SDI.js";
import {
  require_antlr4
} from "./chunk-PWGQ64CM.js";
import {
  require_apacheconf
} from "./chunk-4PX7BWPE.js";
import {
  __commonJS
} from "./chunk-256EKJAK.js";

// node_modules/refractor/index.js
var require_refractor = __commonJS({
  "node_modules/refractor/index.js"(exports, module) {
    var refractor = require_core();
    module.exports = refractor;
    refractor.register(require_abap());
    refractor.register(require_abnf());
    refractor.register(require_actionscript());
    refractor.register(require_ada());
    refractor.register(require_agda());
    refractor.register(require_al());
    refractor.register(require_antlr4());
    refractor.register(require_apacheconf());
    refractor.register(require_apex());
    refractor.register(require_apl());
    refractor.register(require_applescript());
    refractor.register(require_aql());
    refractor.register(require_arduino());
    refractor.register(require_arff());
    refractor.register(require_asciidoc());
    refractor.register(require_asm6502());
    refractor.register(require_asmatmel());
    refractor.register(require_aspnet());
    refractor.register(require_autohotkey());
    refractor.register(require_autoit());
    refractor.register(require_avisynth());
    refractor.register(require_avro_idl());
    refractor.register(require_bash());
    refractor.register(require_basic());
    refractor.register(require_batch());
    refractor.register(require_bbcode());
    refractor.register(require_bicep());
    refractor.register(require_birb());
    refractor.register(require_bison());
    refractor.register(require_bnf());
    refractor.register(require_brainfuck());
    refractor.register(require_brightscript());
    refractor.register(require_bro());
    refractor.register(require_bsl());
    refractor.register(require_c());
    refractor.register(require_cfscript());
    refractor.register(require_chaiscript());
    refractor.register(require_cil());
    refractor.register(require_clojure());
    refractor.register(require_cmake());
    refractor.register(require_cobol());
    refractor.register(require_coffeescript());
    refractor.register(require_concurnas());
    refractor.register(require_coq());
    refractor.register(require_cpp());
    refractor.register(require_crystal());
    refractor.register(require_csharp());
    refractor.register(require_cshtml());
    refractor.register(require_csp());
    refractor.register(require_css_extras());
    refractor.register(require_csv());
    refractor.register(require_cypher());
    refractor.register(require_d());
    refractor.register(require_dart());
    refractor.register(require_dataweave());
    refractor.register(require_dax());
    refractor.register(require_dhall());
    refractor.register(require_diff());
    refractor.register(require_django());
    refractor.register(require_dns_zone_file());
    refractor.register(require_docker());
    refractor.register(require_dot());
    refractor.register(require_ebnf());
    refractor.register(require_editorconfig());
    refractor.register(require_eiffel());
    refractor.register(require_ejs());
    refractor.register(require_elixir());
    refractor.register(require_elm());
    refractor.register(require_erb());
    refractor.register(require_erlang());
    refractor.register(require_etlua());
    refractor.register(require_excel_formula());
    refractor.register(require_factor());
    refractor.register(require_false());
    refractor.register(require_firestore_security_rules());
    refractor.register(require_flow());
    refractor.register(require_fortran());
    refractor.register(require_fsharp());
    refractor.register(require_ftl());
    refractor.register(require_gap());
    refractor.register(require_gcode());
    refractor.register(require_gdscript());
    refractor.register(require_gedcom());
    refractor.register(require_gherkin());
    refractor.register(require_git());
    refractor.register(require_glsl());
    refractor.register(require_gml());
    refractor.register(require_gn());
    refractor.register(require_go_module());
    refractor.register(require_go());
    refractor.register(require_graphql());
    refractor.register(require_groovy());
    refractor.register(require_haml());
    refractor.register(require_handlebars());
    refractor.register(require_haskell());
    refractor.register(require_haxe());
    refractor.register(require_hcl());
    refractor.register(require_hlsl());
    refractor.register(require_hoon());
    refractor.register(require_hpkp());
    refractor.register(require_hsts());
    refractor.register(require_http());
    refractor.register(require_ichigojam());
    refractor.register(require_icon());
    refractor.register(require_icu_message_format());
    refractor.register(require_idris());
    refractor.register(require_iecst());
    refractor.register(require_ignore());
    refractor.register(require_inform7());
    refractor.register(require_ini());
    refractor.register(require_io());
    refractor.register(require_j());
    refractor.register(require_java());
    refractor.register(require_javadoc());
    refractor.register(require_javadoclike());
    refractor.register(require_javastacktrace());
    refractor.register(require_jexl());
    refractor.register(require_jolie());
    refractor.register(require_jq());
    refractor.register(require_js_extras());
    refractor.register(require_js_templates());
    refractor.register(require_jsdoc());
    refractor.register(require_json());
    refractor.register(require_json5());
    refractor.register(require_jsonp());
    refractor.register(require_jsstacktrace());
    refractor.register(require_jsx());
    refractor.register(require_julia());
    refractor.register(require_keepalived());
    refractor.register(require_keyman());
    refractor.register(require_kotlin());
    refractor.register(require_kumir());
    refractor.register(require_kusto());
    refractor.register(require_latex());
    refractor.register(require_latte());
    refractor.register(require_less());
    refractor.register(require_lilypond());
    refractor.register(require_liquid());
    refractor.register(require_lisp());
    refractor.register(require_livescript());
    refractor.register(require_llvm());
    refractor.register(require_log());
    refractor.register(require_lolcode());
    refractor.register(require_lua());
    refractor.register(require_magma());
    refractor.register(require_makefile());
    refractor.register(require_markdown());
    refractor.register(require_markup_templating());
    refractor.register(require_matlab());
    refractor.register(require_maxscript());
    refractor.register(require_mel());
    refractor.register(require_mermaid());
    refractor.register(require_mizar());
    refractor.register(require_mongodb());
    refractor.register(require_monkey());
    refractor.register(require_moonscript());
    refractor.register(require_n1ql());
    refractor.register(require_n4js());
    refractor.register(require_nand2tetris_hdl());
    refractor.register(require_naniscript());
    refractor.register(require_nasm());
    refractor.register(require_neon());
    refractor.register(require_nevod());
    refractor.register(require_nginx());
    refractor.register(require_nim());
    refractor.register(require_nix());
    refractor.register(require_nsis());
    refractor.register(require_objectivec());
    refractor.register(require_ocaml());
    refractor.register(require_opencl());
    refractor.register(require_openqasm());
    refractor.register(require_oz());
    refractor.register(require_parigp());
    refractor.register(require_parser());
    refractor.register(require_pascal());
    refractor.register(require_pascaligo());
    refractor.register(require_pcaxis());
    refractor.register(require_peoplecode());
    refractor.register(require_perl());
    refractor.register(require_php_extras());
    refractor.register(require_php());
    refractor.register(require_phpdoc());
    refractor.register(require_plsql());
    refractor.register(require_powerquery());
    refractor.register(require_powershell());
    refractor.register(require_processing());
    refractor.register(require_prolog());
    refractor.register(require_promql());
    refractor.register(require_properties());
    refractor.register(require_protobuf());
    refractor.register(require_psl());
    refractor.register(require_pug());
    refractor.register(require_puppet());
    refractor.register(require_pure());
    refractor.register(require_purebasic());
    refractor.register(require_purescript());
    refractor.register(require_python());
    refractor.register(require_q());
    refractor.register(require_qml());
    refractor.register(require_qore());
    refractor.register(require_qsharp());
    refractor.register(require_r());
    refractor.register(require_racket());
    refractor.register(require_reason());
    refractor.register(require_regex());
    refractor.register(require_rego());
    refractor.register(require_renpy());
    refractor.register(require_rest());
    refractor.register(require_rip());
    refractor.register(require_roboconf());
    refractor.register(require_robotframework());
    refractor.register(require_ruby());
    refractor.register(require_rust());
    refractor.register(require_sas());
    refractor.register(require_sass());
    refractor.register(require_scala());
    refractor.register(require_scheme());
    refractor.register(require_scss());
    refractor.register(require_shell_session());
    refractor.register(require_smali());
    refractor.register(require_smalltalk());
    refractor.register(require_smarty());
    refractor.register(require_sml());
    refractor.register(require_solidity());
    refractor.register(require_solution_file());
    refractor.register(require_soy());
    refractor.register(require_sparql());
    refractor.register(require_splunk_spl());
    refractor.register(require_sqf());
    refractor.register(require_sql());
    refractor.register(require_squirrel());
    refractor.register(require_stan());
    refractor.register(require_stylus());
    refractor.register(require_swift());
    refractor.register(require_systemd());
    refractor.register(require_t4_cs());
    refractor.register(require_t4_templating());
    refractor.register(require_t4_vb());
    refractor.register(require_tap());
    refractor.register(require_tcl());
    refractor.register(require_textile());
    refractor.register(require_toml());
    refractor.register(require_tremor());
    refractor.register(require_tsx());
    refractor.register(require_tt2());
    refractor.register(require_turtle());
    refractor.register(require_twig());
    refractor.register(require_typescript());
    refractor.register(require_typoscript());
    refractor.register(require_unrealscript());
    refractor.register(require_uorazor());
    refractor.register(require_uri());
    refractor.register(require_v());
    refractor.register(require_vala());
    refractor.register(require_vbnet());
    refractor.register(require_velocity());
    refractor.register(require_verilog());
    refractor.register(require_vhdl());
    refractor.register(require_vim());
    refractor.register(require_visual_basic());
    refractor.register(require_warpscript());
    refractor.register(require_wasm());
    refractor.register(require_web_idl());
    refractor.register(require_wiki());
    refractor.register(require_wolfram());
    refractor.register(require_wren());
    refractor.register(require_xeora());
    refractor.register(require_xml_doc());
    refractor.register(require_xojo());
    refractor.register(require_xquery());
    refractor.register(require_yaml());
    refractor.register(require_yang());
    refractor.register(require_zig());
  }
});

export {
  require_refractor
};
//# sourceMappingURL=chunk-HV6HEBB4.js.map
