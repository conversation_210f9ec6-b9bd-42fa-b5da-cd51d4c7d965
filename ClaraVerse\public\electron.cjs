const { app, BrowserWindow, ipcMain, shell, protocol } = require('electron');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');

let mainWindow;
let backendProcess;

// Configuration pour développement
const isDev = process.env.NODE_ENV === 'development';

// 🏭 MODE PRODUCTION SPÉCIAL
const PRODUCTION_MODE = !isDev;
const isHotReload = process.env.ELECTRON_HOT_RELOAD === 'true';

console.log('🚀 Démarrage WeMa IA Electron');
console.log(`📋 Mode: ${isDev ? 'Développement' : 'Production'}`);
console.log(`🔥 Hot Reload: ${isHotReload ? 'Activé' : 'Désactivé'}`);

function createWindow() {
  console.log('🖥️ Création de la fenêtre principale...');
  
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    frame: false, // Supprimer complètement la barre de titre native
    autoHideMenuBar: true, // Masquer la barre de menu
    menuBarVisible: false, // Forcer la barre de menu invisible
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false, // Désactivé pour permettre le chargement des ressources locales
      allowRunningInsecureContent: true,
      experimentalFeatures: false
    },
    icon: path.join(__dirname, 'wema-icon-256.ico'), // Pour la barre des tâches

    title: 'WeMa IA',
    show: false,
    backgroundColor: '#ffffff'
  });

  // Masquer complètement la barre de menu
  mainWindow.setMenuBarVisibility(false);

  // Titre pour la barre des tâches
  mainWindow.setTitle('WeMa IA');

  // S'assurer que l'icône est bien définie pour la barre des tâches
  const iconPath = path.join(__dirname, 'wema-logo-light.png');
  console.log('🖼️ Chemin de l\'icône:', iconPath);
  if (require('fs').existsSync(iconPath)) {
    mainWindow.setIcon(iconPath);
    console.log('✅ Icône définie pour la barre des tâches');
  } else {
    console.log('❌ Icône non trouvée:', iconPath);
  }

  // Forcer le thème clair pour la barre de titre Windows
  if (process.platform === 'win32') {
    try {
      // Utiliser l'API Windows pour forcer le thème clair
      const { nativeTheme } = require('electron');
      nativeTheme.themeSource = 'light';
    } catch (error) {
      console.log('⚠️ Impossible de configurer le thème:', error.message);
    }
  }

  // Charger l'application
  if (isDev) {
    console.log('🌐 Chargement depuis serveur de développement...');
    mainWindow.loadURL('http://localhost:5173');
    
    // Ouvrir DevTools en développement seulement
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
    
    // Hot reload - recharger quand les fichiers changent
    if (isHotReload) {
      console.log('🔥 Hot reload activé');
      mainWindow.webContents.on('did-finish-load', () => {
        console.log('✅ Page chargée');
      });
    }
  } else {
    console.log('📁 Chargement depuis fichiers statiques...');

    // En production, utiliser le bon chemin selon l'environnement
    let indexPath;

    // Dans un EXE packagé, les fichiers sont dans app.asar
    if (process.resourcesPath) {
      indexPath = path.join(process.resourcesPath, 'app.asar', 'dist', 'index.html');
      console.log('📦 Mode EXE - Chemin ASAR:', indexPath);
    } else {
      // Mode développement local
      indexPath = path.join(__dirname, '../dist/index.html');
      console.log('🔧 Mode local - Chemin relatif:', indexPath);
    }

    console.log('📁 __dirname:', __dirname);
    console.log('📁 process.resourcesPath:', process.resourcesPath);

    // Vérifier si le fichier existe
    if (fs.existsSync(indexPath)) {
      console.log('✅ Fichier index.html trouvé');
      mainWindow.loadFile(indexPath);
    } else {
      console.error('❌ Fichier index.html non trouvé:', indexPath);

      // Fallbacks multiples pour différents environnements
      const fallbackPaths = [
        path.join(process.resourcesPath, 'app', 'dist', 'index.html'),
        path.join(__dirname, 'dist', 'index.html'),
        path.join(__dirname, '../dist/index.html'),
        path.join(process.cwd(), 'dist', 'index.html')
      ];

      let loaded = false;
      for (const fallbackPath of fallbackPaths) {
        console.log('🔄 Tentative:', fallbackPath);
        if (fs.existsSync(fallbackPath)) {
          console.log('✅ Fallback trouvé:', fallbackPath);
          mainWindow.loadFile(fallbackPath);
          loaded = true;
          break;
        }
      }

      if (!loaded) {
        console.error('❌ AUCUN FICHIER INDEX.HTML TROUVÉ !');
        // Dernière tentative : créer une page d'erreur
        mainWindow.loadURL('data:text/html,<h1>Erreur: Impossible de charger WeMa IA</h1><p>Fichier index.html introuvable</p>');
      }
    }
  }

  // Afficher la fenêtre quand elle est prête
  mainWindow.once('ready-to-show', () => {
    console.log('✅ Fenêtre prête - Affichage');
    mainWindow.show();

    if (isDev) {
      mainWindow.focus();
    }
  });

  // Logs pour diagnostiquer les problèmes de chargement
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('❌ Échec de chargement:', errorCode, errorDescription, validatedURL);
  });

  mainWindow.webContents.on('did-finish-load', () => {
    console.log('✅ Chargement terminé avec succès');
  });

  mainWindow.webContents.on('dom-ready', () => {
    console.log('✅ DOM prêt');
  });

  // Ouvrir DevTools automatiquement pour diagnostiquer
  mainWindow.webContents.openDevTools();

  // Gestion des erreurs de chargement
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('❌ Erreur de chargement:', errorCode, errorDescription);
  });

  // Ouvrir les liens externes dans le navigateur par défaut
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    console.log('🔗 Ouverture lien externe:', url);
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Intercepter les clics sur les liens
  mainWindow.webContents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    console.log('🔗 Navigation externe:', navigationUrl);
    shell.openExternal(navigationUrl);
  });

  // Démarrer le backend Python (désactivé pour portable)
  if (!process.env.DISABLE_BACKEND && !process.env.PORTABLE_MODE) {
    startBackend();
  } else {
    console.log('🔧 Backend Python désactivé (mode portable ou DISABLE_BACKEND=true)');
  }
}

// Handlers pour les contrôles de fenêtre personnalisés
ipcMain.handle('window-minimize', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('window-maximize', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.restore();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.handle('window-close', () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

function startBackend() {
  console.log('🐍 Démarrage du backend Python...');

  try {
    // Chemin backend adapté pour EXE et dev
    let backendPath;
    if (app.isPackaged) {
      // Dans l'EXE : resources/py_backend (CORRIGÉ!)
      backendPath = path.join(process.resourcesPath, 'py_backend');
    } else {
      // En développement : ../py_backend
      backendPath = path.join(__dirname, '../py_backend');
    }
    console.log(`📁 Chemin backend: ${backendPath}`);

    // Vérifier que le dossier existe
    if (!require('fs').existsSync(backendPath)) {
      console.error(`❌ Dossier backend introuvable: ${backendPath}`);
      return;
    }

    // Vérifier que main.py existe
    const mainPyPath = path.join(backendPath, 'main.py');
    if (!require('fs').existsSync(mainPyPath)) {
      console.error(`❌ main.py introuvable: ${mainPyPath}`);
      return;
    }

    // Détection Python avec logs détaillés
    const pythonCommands = ['python', 'python3', 'py'];
    let pythonCmd = null;

    for (const cmd of pythonCommands) {
      try {
        const result = require('child_process').execSync(`${cmd} --version`, {
          stdio: 'pipe',
          timeout: 5000
        });
        pythonCmd = cmd;
        console.log(`✅ Python trouvé: ${cmd} - ${result.toString().trim()}`);
        break;
      } catch (e) {
        console.log(`⚠️ ${cmd} non trouvé: ${e.message}`);
      }
    }

    if (!pythonCmd) {
      console.error('❌ Aucune installation Python trouvée !');
      return;
    }

    console.log(`🚀 Lancement: ${pythonCmd} main.py dans ${backendPath}`);

    backendProcess = spawn(pythonCmd, ['main.py'], {
      cwd: backendPath,
      stdio: ['pipe', 'pipe', 'pipe'],
      env: { ...process.env, PYTHONUNBUFFERED: '1' },
      shell: true // Important pour Windows
    });

    backendProcess.stdout.on('data', (data) => {
      const message = data.toString().trim();
      if (message) {
        console.log('🐍 Backend:', message);
      }
    });

    backendProcess.stderr.on('data', (data) => {
      const message = data.toString().trim();
      if (message && !message.includes('DeprecationWarning')) {
        console.log('🐍 Backend Error:', message);
      }
    });

    backendProcess.on('close', (code) => {
      console.log(`🐍 Backend fermé avec le code: ${code}`);
      if (code !== 0) {
        console.error(`❌ Backend fermé avec erreur: ${code}`);
      }
    });

    backendProcess.on('error', (error) => {
      console.error('❌ Erreur backend:', error.message);
    });
    
    console.log('✅ Backend Python démarré');
    
  } catch (error) {
    console.error('❌ Impossible de démarrer le backend:', error.message);
  }
}

// Événements de l'application
app.whenReady().then(async () => {
  console.log('🚀 Electron prêt');
  createWindow();
});

app.on('window-all-closed', () => {
  console.log('🔒 Fermeture de toutes les fenêtres');
  
  // Arrêter le backend
  if (backendProcess && !backendProcess.killed) {
    console.log('🛑 Arrêt du backend Python...');
    backendProcess.kill('SIGTERM');
    
    // Force kill après 3 secondes
    setTimeout(() => {
      if (!backendProcess.killed) {
        backendProcess.kill('SIGKILL');
      }
    }, 3000);
  }
  
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
  console.error('❌ Erreur non capturée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée:', reason);
});

console.log('📋 Configuration Electron chargée');
