import{B as v,D as P,G as S,a as e,c as y,f as i,g as a,i as f,t as b,v as n,y as j,z as d}from"./chunk-6BY5RJGC.mjs";import{a as m}from"./chunk-GTKDMUJJ.mjs";var C=b(Object.keys,Object),T=C;var V=Object.prototype,D=V.hasOwnProperty;function K(r){if(!n(r))return T(r);var t=[];for(var o in Object(r))D.call(r,o)&&o!="constructor"&&t.push(o);return t}m(K,"baseKeys");var O=K;var N=a(e,"DataView"),s=N;var W=a(e,"Promise"),c=W;var B=a(e,"Set"),g=B;var z=a(e,"WeakMap"),u=z;var M="[object Map]",E="[object Object]",h="[object Promise]",x="[object Set]",k="[object WeakMap]",l="[object DataView]",G=i(s),L=i(f),q=i(c),F=i(g),H=i(u),p=y;(s&&p(new s(new ArrayBuffer(1)))!=l||f&&p(new f)!=M||c&&p(c.resolve())!=h||g&&p(new g)!=x||u&&p(new u)!=k)&&(p=m(function(r){var t=y(r),o=t==E?r.constructor:void 0,w=o?i(o):"";if(w)switch(w){case G:return l;case L:return M;case q:return h;case F:return x;case H:return k}return t},"getTag"));var A=p;var I="[object Map]",J="[object Set]",Q=Object.prototype,R=Q.hasOwnProperty;function U(r){if(r==null)return!0;if(v(r)&&(d(r)||typeof r=="string"||typeof r.splice=="function"||P(r)||S(r)||j(r)))return!r.length;var t=A(r);if(t==I||t==J)return!r.size;if(n(r))return!O(r).length;for(var o in r)if(R.call(r,o))return!1;return!0}m(U,"isEmpty");var Cr=U;export{O as a,g as b,A as c,Cr as d};
