#!/usr/bin/env python3
"""
🚀 Test des optimisations de performance
Teste le préchargement des modèles et la stabilité du proxy
"""

import requests
import time
import json

def test_lmstudio_direct():
    """Test de la communication directe LM Studio"""
    print("🚀 Test de la communication directe LM Studio...")

    try:
        # Test direct avec LM Studio
        start_time = time.time()
        response = requests.post(
            'http://localhost:1234/v1/chat/completions',
            json={
                "model": "qwen3-4b",
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 1,
                "stream": False
            },
            timeout=30
        )
        direct_time = time.time() - start_time

        if response.status_code == 200:
            print(f"✅ Communication directe réussie en {direct_time:.2f}s")
            return True
        else:
            print(f"❌ Communication directe échouée: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        print(f"❌ Erreur communication directe: {e}")
        return False

def test_lmstudio_chat_optimized():
    """Test du chat optimisé (non-streaming)"""
    print("🚀 Test du chat optimisé...")
    
    try:
        start_time = time.time()
        response = requests.post(
            'http://localhost:8000/proxy/lmstudio/chat',
            json={
                "model": "qwen3-4b",
                "messages": [{"role": "user", "content": "Hello, respond with just 'Hi!'"}],
                "max_tokens": 10,
                "stream": False
            },
            timeout=30
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ Chat réussi en {response_time:.2f}s: '{content.strip()}'")
            return True
        else:
            print(f"❌ Chat échoué: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur chat: {e}")
        return False

def test_models_availability():
    """Test de la disponibilité des modèles"""
    print("🚀 Test de la disponibilité des modèles...")
    
    try:
        response = requests.get('http://localhost:8000/proxy/lmstudio/models', timeout=10)
        
        if response.status_code == 200:
            models = response.json()
            model_list = [m['id'] for m in models.get('data', [])]
            print(f"✅ Modèles disponibles: {', '.join(model_list)}")
            return len(model_list) > 0
        else:
            print(f"❌ Échec récupération modèles: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur modèles: {e}")
        return False

def test_backend_health():
    """Test de santé du backend"""
    print("🚀 Test de santé du backend...")
    
    try:
        response = requests.get('http://localhost:8000/health', timeout=5)
        
        if response.status_code == 200:
            health = response.json()
            print(f"✅ Backend sain: {health['status']}, RAG: {health['rag_available']}")
            return True
        else:
            print(f"❌ Backend non sain: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur santé: {e}")
        return False

def main():
    """Test complet des optimisations"""
    print("🚀 Test complet des optimisations WeMa IA")
    print("=" * 50)
    
    tests = [
        ("Santé du backend", test_backend_health),
        ("Disponibilité des modèles", test_models_availability),
        ("Communication directe LM Studio", test_lmstudio_direct),
        ("Chat optimisé", test_lmstudio_chat_optimized)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}...")
        success = test_func()
        results.append((test_name, success))
        print()
    
    print("=" * 50)
    print("📊 RÉSULTATS DES TESTS:")
    
    for test_name, success in results:
        status = "✅ RÉUSSI" if success else "❌ ÉCHOUÉ"
        print(f"   {status}: {test_name}")
    
    success_count = sum(1 for _, success in results if success)
    total_count = len(results)
    
    print(f"\n🎯 Score: {success_count}/{total_count} tests réussis")
    
    if success_count == total_count:
        print("🎉 Toutes les optimisations fonctionnent parfaitement !")
    elif success_count >= total_count * 0.75:
        print("✅ La plupart des optimisations fonctionnent bien")
    else:
        print("⚠️ Certaines optimisations nécessitent des ajustements")

if __name__ == "__main__":
    main()
