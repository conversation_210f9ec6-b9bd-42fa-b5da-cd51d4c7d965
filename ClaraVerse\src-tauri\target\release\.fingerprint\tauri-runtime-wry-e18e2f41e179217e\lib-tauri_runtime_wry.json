{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 12311593708891281061, "deps": [[376837177317575824, "softbuffer", false, 3329442235248530754], [2013030631243296465, "webview2_com", false, 6102230966157782237], [2671782512663819132, "tauri_utils", false, 9883568072656655585], [3150220818285335163, "url", false, 14199394255531846341], [3722963349756955755, "once_cell", false, 2184726569431462750], [4143744114649553716, "raw_window_handle", false, 9360050597165366007], [5986029879202738730, "log", false, 2351528888146949315], [6089812615193535349, "tauri_runtime", false, 4824090621243199533], [8826339825490770380, "tao", false, 143116101104619903], [9010263965687315507, "http", false, 9326335740251934562], [9141053277961803901, "wry", false, 9429342471977095291], [11599800339996261026, "build_script_build", false, 3133178312109918788], [14585479307175734061, "windows", false, 3140733581549053838]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-e18e2f41e179217e\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}