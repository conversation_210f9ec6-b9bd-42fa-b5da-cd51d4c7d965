cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rerun-if-changed=C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\tauri.conf.json
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_APP_NAME=app
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_PREFIX=com_wema_1ia
cargo:rustc-check-cfg=cfg(dev)
cargo:PERMISSION_FILES_PATH=C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\build\app-f0f7f7a6031200f0\out\app-manifest\__app__-permission-files
cargo:rerun-if-changed=capabilities
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:rustc-env=TAURI_ENV_TARGET_TRIPLE=x86_64-pc-windows-msvc
cargo:rerun-if-changed=..\py_backend\.env
cargo:rerun-if-changed=..\py_backend\.env.template
cargo:rerun-if-changed=..\py_backend\Dockerfile
cargo:rerun-if-changed=..\py_backend\Speech2Text.py
cargo:rerun-if-changed=..\py_backend\TTS_README.md
cargo:rerun-if-changed=..\py_backend\Text2Speech.py
cargo:rerun-if-changed=..\py_backend\__pycache__\langfuse_service.cpython-312.pyc
cargo:rerun-if-changed=..\py_backend\__pycache__\main.cpython-312.pyc
cargo:rerun-if-changed=..\py_backend\__pycache__\network_config.cpython-312.pyc
cargo:rerun-if-changed=..\py_backend\__pycache__\ocr_processor_premium.cpython-312.pyc
cargo:rerun-if-changed=..\py_backend\__pycache__\ocr_service.cpython-312.pyc
cargo:rerun-if-changed=..\py_backend\__pycache__\perfect_compressor.cpython-312.pyc
cargo:rerun-if-changed=..\py_backend\__pycache__\rag_premium_service.cpython-312.pyc
cargo:rerun-if-changed=..\py_backend\auth_service.py
cargo:rerun-if-changed=..\py_backend\compression_routes.py
cargo:rerun-if-changed=..\py_backend\diffusers_api.py
cargo:rerun-if-changed=..\py_backend\documents.db
cargo:rerun-if-changed=..\py_backend\install_kokoro.py
cargo:rerun-if-changed=..\py_backend\langfuse_service.py
cargo:rerun-if-changed=..\py_backend\main.py
cargo:rerun-if-changed=..\py_backend\network_config.py
cargo:rerun-if-changed=..\py_backend\ocr_processor_premium.py
cargo:rerun-if-changed=..\py_backend\ocr_service.py
cargo:rerun-if-changed=..\py_backend\perfect_compressor.py
cargo:rerun-if-changed=..\py_backend\qdrant_storage\.lock
cargo:rerun-if-changed=..\py_backend\qdrant_storage\meta.json
cargo:rerun-if-changed=..\py_backend\requirements.txt
cargo:rerun-if-changed=..\py_backend\services\__pycache__\internet_search_service.cpython-312.pyc
cargo:rerun-if-changed=..\py_backend\services\__pycache__\startup_service.cpython-312.pyc
cargo:rerun-if-changed=..\py_backend\services\__pycache__\web_scraper_service.cpython-312.pyc
cargo:rerun-if-changed=..\py_backend\services\internet_search_service.py
cargo:rerun-if-changed=..\py_backend\services\startup_service.py
cargo:rerun-if-changed=..\py_backend\services\web_scraper_service.py
cargo:rerun-if-changed=..\py_backend\start_server.py
cargo:rerun-if-changed=..\py_backend\test_compression_tool.py
cargo:rerun-if-changed=..\py_backend\test_non_streaming.py
cargo:rerun-if-changed=..\py_backend\test_optimizations.py
cargo:rerun-if-changed=..\py_backend\test_qdrant_temp\.lock
cargo:rerun-if-changed=..\py_backend\test_qdrant_temp\collection\test_collection\storage.sqlite
cargo:rerun-if-changed=..\py_backend\test_streaming_optimized.py
cargo:rerun-if-changed=..\py_backend\tools\__pycache__\internet_search_tools.cpython-312.pyc
cargo:rerun-if-changed=..\py_backend\tools\internet_search_tools.py
package.metadata does not exist
Microsoft (R) Windows (R) Resource Compiler Version 10.0.10011.16384

Copyright (C) Microsoft Corporation.  All rights reserved.


cargo:rustc-link-arg-bins=C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\build\app-f0f7f7a6031200f0\out\resource.lib
cargo:rustc-link-search=native=C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\build\app-f0f7f7a6031200f0\out
cargo:rustc-link-arg=/NODEFAULTLIB:libvcruntimed.lib
cargo:rustc-link-arg=/NODEFAULTLIB:vcruntime.lib
cargo:rustc-link-arg=/NODEFAULTLIB:vcruntimed.lib
cargo:rustc-link-arg=/NODEFAULTLIB:libcmtd.lib
cargo:rustc-link-arg=/NODEFAULTLIB:msvcrt.lib
cargo:rustc-link-arg=/NODEFAULTLIB:msvcrtd.lib
cargo:rustc-link-arg=/NODEFAULTLIB:libucrt.lib
cargo:rustc-link-arg=/NODEFAULTLIB:libucrtd.lib
cargo:rustc-link-arg=/DEFAULTLIB:libcmt.lib
cargo:rustc-link-arg=/DEFAULTLIB:libvcruntime.lib
cargo:rustc-link-arg=/DEFAULTLIB:ucrt.lib
