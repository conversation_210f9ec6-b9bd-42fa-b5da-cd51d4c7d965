import{O as ln,P as an,Q as y,R as tn,S as $,T as O,V as _,W as un,X as W,Y as rn,Z as B,$ as o,a0 as sn,a1 as on,a2 as fn}from"./index-Bcuwyvq3.js";function cn(l){return l.innerRadius}function yn(l){return l.outerRadius}function gn(l){return l.startAngle}function dn(l){return l.endAngle}function mn(l){return l&&l.padAngle}function pn(l,h,D,S,v,R,X,a){var E=D-l,i=S-h,n=X-v,d=a-R,u=d*E-n*i;if(!(u*u<y))return u=(n*(h-R)-d*(l-v))/u,[l+u*E,h+u*i]}function K(l,h,D,S,v,R,X){var a=l-D,E=h-S,i=(X?R:-R)/B(a*a+E*E),n=i*E,d=-i*a,u=l+n,s=h+d,f=D+n,c=S+d,Y=(u+f)/2,t=(s+c)/2,m=f-u,g=c-s,A=m*m+g*g,T=v-R,P=u*c-f*s,I=(g<0?-1:1)*B(fn(0,T*T*A-P*P)),Q=(P*g-m*I)/A,V=(-P*m-g*I)/A,w=(P*g+m*I)/A,p=(-P*m+g*I)/A,x=Q-Y,e=V-t,r=w-Y,Z=p-t;return x*x+e*e>r*r+Z*Z&&(Q=w,V=p),{cx:Q,cy:V,x01:-n,y01:-d,x11:Q*(v/T-1),y11:V*(v/T-1)}}function hn(){var l=cn,h=yn,D=W(0),S=null,v=gn,R=dn,X=mn,a=null,E=ln(i);function i(){var n,d,u=+l.apply(this,arguments),s=+h.apply(this,arguments),f=v.apply(this,arguments)-an,c=R.apply(this,arguments)-an,Y=un(c-f),t=c>f;if(a||(a=n=E()),s<u&&(d=s,s=u,u=d),!(s>y))a.moveTo(0,0);else if(Y>tn-y)a.moveTo(s*$(f),s*O(f)),a.arc(0,0,s,f,c,!t),u>y&&(a.moveTo(u*$(c),u*O(c)),a.arc(0,0,u,c,f,t));else{var m=f,g=c,A=f,T=c,P=Y,I=Y,Q=X.apply(this,arguments)/2,V=Q>y&&(S?+S.apply(this,arguments):B(u*u+s*s)),w=_(un(s-u)/2,+D.apply(this,arguments)),p=w,x=w,e,r;if(V>y){var Z=sn(V/u*O(Q)),C=sn(V/s*O(Q));(P-=Z*2)>y?(Z*=t?1:-1,A+=Z,T-=Z):(P=0,A=T=(f+c)/2),(I-=C*2)>y?(C*=t?1:-1,m+=C,g-=C):(I=0,m=g=(f+c)/2)}var j=s*$(m),z=s*O(m),F=u*$(T),G=u*O(T);if(w>y){var H=s*$(g),J=s*O(g),L=u*$(A),M=u*O(A),q;if(Y<rn)if(q=pn(j,z,L,M,H,J,F,G)){var N=j-q[0],U=z-q[1],k=H-q[0],b=J-q[1],nn=1/O(on((N*k+U*b)/(B(N*N+U*U)*B(k*k+b*b)))/2),en=B(q[0]*q[0]+q[1]*q[1]);p=_(w,(u-en)/(nn-1)),x=_(w,(s-en)/(nn+1))}else p=x=0}I>y?x>y?(e=K(L,M,j,z,s,x,t),r=K(H,J,F,G,s,x,t),a.moveTo(e.cx+e.x01,e.cy+e.y01),x<w?a.arc(e.cx,e.cy,x,o(e.y01,e.x01),o(r.y01,r.x01),!t):(a.arc(e.cx,e.cy,x,o(e.y01,e.x01),o(e.y11,e.x11),!t),a.arc(0,0,s,o(e.cy+e.y11,e.cx+e.x11),o(r.cy+r.y11,r.cx+r.x11),!t),a.arc(r.cx,r.cy,x,o(r.y11,r.x11),o(r.y01,r.x01),!t))):(a.moveTo(j,z),a.arc(0,0,s,m,g,!t)):a.moveTo(j,z),!(u>y)||!(P>y)?a.lineTo(F,G):p>y?(e=K(F,G,H,J,u,-p,t),r=K(j,z,L,M,u,-p,t),a.lineTo(e.cx+e.x01,e.cy+e.y01),p<w?a.arc(e.cx,e.cy,p,o(e.y01,e.x01),o(r.y01,r.x01),!t):(a.arc(e.cx,e.cy,p,o(e.y01,e.x01),o(e.y11,e.x11),!t),a.arc(0,0,u,o(e.cy+e.y11,e.cx+e.x11),o(r.cy+r.y11,r.cx+r.x11),t),a.arc(r.cx,r.cy,p,o(r.y11,r.x11),o(r.y01,r.x01),!t))):a.arc(0,0,u,T,A,t)}if(a.closePath(),n)return a=null,n+""||null}return i.centroid=function(){var n=(+l.apply(this,arguments)+ +h.apply(this,arguments))/2,d=(+v.apply(this,arguments)+ +R.apply(this,arguments))/2-rn/2;return[$(d)*n,O(d)*n]},i.innerRadius=function(n){return arguments.length?(l=typeof n=="function"?n:W(+n),i):l},i.outerRadius=function(n){return arguments.length?(h=typeof n=="function"?n:W(+n),i):h},i.cornerRadius=function(n){return arguments.length?(D=typeof n=="function"?n:W(+n),i):D},i.padRadius=function(n){return arguments.length?(S=n==null?null:typeof n=="function"?n:W(+n),i):S},i.startAngle=function(n){return arguments.length?(v=typeof n=="function"?n:W(+n),i):v},i.endAngle=function(n){return arguments.length?(R=typeof n=="function"?n:W(+n),i):R},i.padAngle=function(n){return arguments.length?(X=typeof n=="function"?n:W(+n),i):X},i.context=function(n){return arguments.length?(a=n??null,i):a},i}export{hn as d};
