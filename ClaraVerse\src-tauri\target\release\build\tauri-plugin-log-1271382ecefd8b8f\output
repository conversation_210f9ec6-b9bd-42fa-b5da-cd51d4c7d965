cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\build\tauri-plugin-log-1271382ecefd8b8f\out\tauri-plugin-log-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-log-2.6.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
