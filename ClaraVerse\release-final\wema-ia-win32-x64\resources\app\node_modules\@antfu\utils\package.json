{"name": "@antfu/utils", "type": "module", "version": "8.1.1", "packageManager": "pnpm@10.4.0", "description": "Opinionated collection of common JavaScript / TypeScript utils by @antfu", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/utils#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/utils.git"}, "bugs": {"url": "https://github.com/antfu/utils/issues"}, "keywords": ["utils"], "sideEffects": false, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "rollup -c", "dev": "nr build --watch", "lint": "eslint .", "lint-fix": "nr lint --fix", "prepublishOnly": "npm run build", "release": "bumpp --commit --push --tag && npm publish", "start": "tsx src/index.ts", "typecheck": "tsc --noEmit", "test": "vitest"}, "devDependencies": {"@antfu/eslint-config": "^4.2.0", "@antfu/ni": "^23.3.1", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@types/node": "^22.13.4", "@types/throttle-debounce": "^5.0.2", "bumpp": "^10.0.3", "eslint": "^9.20.1", "p-limit": "^6.2.0", "rollup": "^4.34.7", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-esbuild": "^6.2.0", "throttle-debounce": "5.0.0", "tsx": "^4.8.0", "typescript": "^5.7.3", "vite": "^6.1.0", "vitest": "^3.0.5"}}