{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/yaml/yaml.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/yaml/yaml.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    offSide: true\n  },\n  onEnterRules: [\n    {\n      beforeText: /:\\s*$/,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.Indent\n      }\n    }\n  ]\n};\nvar language = {\n  tokenPostfix: \".yaml\",\n  brackets: [\n    { token: \"delimiter.bracket\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" }\n  ],\n  keywords: [\"true\", \"True\", \"TRUE\", \"false\", \"False\", \"FALSE\", \"null\", \"Null\", \"Null\", \"~\"],\n  numberInteger: /(?:0|[+-]?[0-9]+)/,\n  numberFloat: /(?:0|[+-]?[0-9]+)(?:\\.[0-9]+)?(?:e[-+][1-9][0-9]*)?/,\n  numberOctal: /0o[0-7]+/,\n  numberHex: /0x[0-9a-fA-F]+/,\n  numberInfinity: /[+-]?\\.(?:inf|Inf|INF)/,\n  numberNaN: /\\.(?:nan|Nan|NAN)/,\n  numberDate: /\\d{4}-\\d\\d-\\d\\d([Tt ]\\d\\d:\\d\\d:\\d\\d(\\.\\d+)?(( ?[+-]\\d\\d?(:\\d\\d)?)|Z)?)?/,\n  escapes: /\\\\(?:[btnfr\\\\\"']|[0-7][0-7]?|[0-3][0-7]{2})/,\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      // Directive\n      [/%[^ ]+.*$/, \"meta.directive\"],\n      // Document Markers\n      [/---/, \"operators.directivesEnd\"],\n      [/\\.{3}/, \"operators.documentEnd\"],\n      // Block Structure Indicators\n      [/[-?:](?= )/, \"operators\"],\n      { include: \"@anchor\" },\n      { include: \"@tagHandle\" },\n      { include: \"@flowCollections\" },\n      { include: \"@blockStyle\" },\n      // Numbers\n      [/@numberInteger(?![ \\t]*\\S+)/, \"number\"],\n      [/@numberFloat(?![ \\t]*\\S+)/, \"number.float\"],\n      [/@numberOctal(?![ \\t]*\\S+)/, \"number.octal\"],\n      [/@numberHex(?![ \\t]*\\S+)/, \"number.hex\"],\n      [/@numberInfinity(?![ \\t]*\\S+)/, \"number.infinity\"],\n      [/@numberNaN(?![ \\t]*\\S+)/, \"number.nan\"],\n      [/@numberDate(?![ \\t]*\\S+)/, \"number.date\"],\n      // Key:Value pair\n      [/(\".*?\"|'.*?'|[^#'\"]*?)([ \\t]*)(:)( |$)/, [\"type\", \"white\", \"operators\", \"white\"]],\n      { include: \"@flowScalars\" },\n      // String nodes\n      [\n        /.+?(?=(\\s+#|$))/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    // Flow Collection: Flow Mapping\n    object: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      // Flow Mapping termination\n      [/\\}/, \"@brackets\", \"@pop\"],\n      // Flow Mapping delimiter\n      [/,/, \"delimiter.comma\"],\n      // Flow Mapping Key:Value delimiter\n      [/:(?= )/, \"operators\"],\n      // Flow Mapping Key:Value key\n      [/(?:\".*?\"|'.*?'|[^,\\{\\[]+?)(?=: )/, \"type\"],\n      // Start Flow Style\n      { include: \"@flowCollections\" },\n      { include: \"@flowScalars\" },\n      // Scalar Data types\n      { include: \"@tagHandle\" },\n      { include: \"@anchor\" },\n      { include: \"@flowNumber\" },\n      // Other value (keyword or string)\n      [\n        /[^\\},]+/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    // Flow Collection: Flow Sequence\n    array: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      // Flow Sequence termination\n      [/\\]/, \"@brackets\", \"@pop\"],\n      // Flow Sequence delimiter\n      [/,/, \"delimiter.comma\"],\n      // Start Flow Style\n      { include: \"@flowCollections\" },\n      { include: \"@flowScalars\" },\n      // Scalar Data types\n      { include: \"@tagHandle\" },\n      { include: \"@anchor\" },\n      { include: \"@flowNumber\" },\n      // Other value (keyword or string)\n      [\n        /[^\\],]+/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    // First line of a Block Style\n    multiString: [[/^( +).+$/, \"string\", \"@multiStringContinued.$1\"]],\n    // Further lines of a Block Style\n    //   Workaround for indentation detection\n    multiStringContinued: [\n      [\n        /^( *).+$/,\n        {\n          cases: {\n            \"$1==$S2\": \"string\",\n            \"@default\": { token: \"@rematch\", next: \"@popall\" }\n          }\n        }\n      ]\n    ],\n    whitespace: [[/[ \\t\\r\\n]+/, \"white\"]],\n    // Only line comments\n    comment: [[/#.*$/, \"comment\"]],\n    // Start Flow Collections\n    flowCollections: [\n      [/\\[/, \"@brackets\", \"@array\"],\n      [/\\{/, \"@brackets\", \"@object\"]\n    ],\n    // Start Flow Scalars (quoted strings)\n    flowScalars: [\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/'[^']*'/, \"string\"],\n      [/\"/, \"string\", \"@doubleQuotedString\"]\n    ],\n    doubleQuotedString: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    // Start Block Scalar\n    blockStyle: [[/[>|][0-9]*[+-]?$/, \"operators\", \"@multiString\"]],\n    // Numbers in Flow Collections (terminate with ,]})\n    flowNumber: [\n      [/@numberInteger(?=[ \\t]*[,\\]\\}])/, \"number\"],\n      [/@numberFloat(?=[ \\t]*[,\\]\\}])/, \"number.float\"],\n      [/@numberOctal(?=[ \\t]*[,\\]\\}])/, \"number.octal\"],\n      [/@numberHex(?=[ \\t]*[,\\]\\}])/, \"number.hex\"],\n      [/@numberInfinity(?=[ \\t]*[,\\]\\}])/, \"number.infinity\"],\n      [/@numberNaN(?=[ \\t]*[,\\]\\}])/, \"number.nan\"],\n      [/@numberDate(?=[ \\t]*[,\\]\\}])/, \"number.date\"]\n    ],\n    tagHandle: [[/\\![^ ]*/, \"tag\"]],\n    anchor: [[/[&*][^ ]+/, \"namespace\"]]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;;AAOA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAO,kBAAkB,IAAI;AACpC,UAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,kBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,WAAW,4BAA4B,kBAAuB;AAI9D,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ;AAAA,MACE,YAAY;AAAA,MACZ,QAAQ;AAAA,QACN,cAAc,2BAA2B,UAAU,aAAa;AAAA,MAClE;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,UAAU;AAAA,IACR,EAAE,OAAO,qBAAqB,MAAM,KAAK,OAAO,IAAI;AAAA,IACpD,EAAE,OAAO,oBAAoB,MAAM,KAAK,OAAO,IAAI;AAAA,EACrD;AAAA,EACA,UAAU,CAAC,QAAQ,QAAQ,QAAQ,SAAS,SAAS,SAAS,QAAQ,QAAQ,QAAQ,GAAG;AAAA,EACzF,eAAe;AAAA,EACf,aAAa;AAAA,EACb,aAAa;AAAA,EACb,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA;AAAA,MAEtB,CAAC,aAAa,gBAAgB;AAAA;AAAA,MAE9B,CAAC,OAAO,yBAAyB;AAAA,MACjC,CAAC,SAAS,uBAAuB;AAAA;AAAA,MAEjC,CAAC,cAAc,WAAW;AAAA,MAC1B,EAAE,SAAS,UAAU;AAAA,MACrB,EAAE,SAAS,aAAa;AAAA,MACxB,EAAE,SAAS,mBAAmB;AAAA,MAC9B,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB,CAAC,+BAA+B,QAAQ;AAAA,MACxC,CAAC,6BAA6B,cAAc;AAAA,MAC5C,CAAC,6BAA6B,cAAc;AAAA,MAC5C,CAAC,2BAA2B,YAAY;AAAA,MACxC,CAAC,gCAAgC,iBAAiB;AAAA,MAClD,CAAC,2BAA2B,YAAY;AAAA,MACxC,CAAC,4BAA4B,aAAa;AAAA;AAAA,MAE1C,CAAC,0CAA0C,CAAC,QAAQ,SAAS,aAAa,OAAO,CAAC;AAAA,MAClF,EAAE,SAAS,eAAe;AAAA;AAAA,MAE1B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAEA,QAAQ;AAAA,MACN,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA;AAAA,MAEtB,CAAC,MAAM,aAAa,MAAM;AAAA;AAAA,MAE1B,CAAC,KAAK,iBAAiB;AAAA;AAAA,MAEvB,CAAC,UAAU,WAAW;AAAA;AAAA,MAEtB,CAAC,oCAAoC,MAAM;AAAA;AAAA,MAE3C,EAAE,SAAS,mBAAmB;AAAA,MAC9B,EAAE,SAAS,eAAe;AAAA;AAAA,MAE1B,EAAE,SAAS,aAAa;AAAA,MACxB,EAAE,SAAS,UAAU;AAAA,MACrB,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAEA,OAAO;AAAA,MACL,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA;AAAA,MAEtB,CAAC,MAAM,aAAa,MAAM;AAAA;AAAA,MAE1B,CAAC,KAAK,iBAAiB;AAAA;AAAA,MAEvB,EAAE,SAAS,mBAAmB;AAAA,MAC9B,EAAE,SAAS,eAAe;AAAA;AAAA,MAE1B,EAAE,SAAS,aAAa;AAAA,MACxB,EAAE,SAAS,UAAU;AAAA,MACrB,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAEA,aAAa,CAAC,CAAC,YAAY,UAAU,0BAA0B,CAAC;AAAA;AAAA;AAAA,IAGhE,sBAAsB;AAAA,MACpB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW;AAAA,YACX,YAAY,EAAE,OAAO,YAAY,MAAM,UAAU;AAAA,UACnD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,YAAY,CAAC,CAAC,cAAc,OAAO,CAAC;AAAA;AAAA,IAEpC,SAAS,CAAC,CAAC,QAAQ,SAAS,CAAC;AAAA;AAAA,IAE7B,iBAAiB;AAAA,MACf,CAAC,MAAM,aAAa,QAAQ;AAAA,MAC5B,CAAC,MAAM,aAAa,SAAS;AAAA,IAC/B;AAAA;AAAA,IAEA,aAAa;AAAA,MACX,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,KAAK,UAAU,qBAAqB;AAAA,IACvC;AAAA,IACA,oBAAoB;AAAA,MAClB,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,IACxB;AAAA;AAAA,IAEA,YAAY,CAAC,CAAC,oBAAoB,aAAa,cAAc,CAAC;AAAA;AAAA,IAE9D,YAAY;AAAA,MACV,CAAC,mCAAmC,QAAQ;AAAA,MAC5C,CAAC,iCAAiC,cAAc;AAAA,MAChD,CAAC,iCAAiC,cAAc;AAAA,MAChD,CAAC,+BAA+B,YAAY;AAAA,MAC5C,CAAC,oCAAoC,iBAAiB;AAAA,MACtD,CAAC,+BAA+B,YAAY;AAAA,MAC5C,CAAC,gCAAgC,aAAa;AAAA,IAChD;AAAA,IACA,WAAW,CAAC,CAAC,WAAW,KAAK,CAAC;AAAA,IAC9B,QAAQ,CAAC,CAAC,aAAa,WAAW,CAAC;AAAA,EACrC;AACF;", "names": []}