import{a as c,b as n,c as a,d as i,e as m,f as e,g as u,i as d,o as l,p as s}from"./chunk-JVB3IFOF.mjs";import{a as t}from"./chunk-GTKDMUJJ.mjs";var v=class extends s{static{t(this,"PacketTokenBuilder")}static{e(this,"PacketTokenBuilder")}constructor(){super(["packet-beta"])}},p={parser:{TokenBuilder:e(()=>new v,"TokenBuilder"),ValueConverter:e(()=>new l,"ValueConverter")}};function M(k=i){let r=a(n(k),u),o=a(c({shared:r}),d,p);return r.ServiceRegistry.register(o),{shared:r,Packet:o}}t(M,"createPacketServices");e(M,"createPacketServices");export{p as a,M as b};
