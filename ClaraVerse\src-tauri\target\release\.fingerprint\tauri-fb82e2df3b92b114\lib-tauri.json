{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 12881424638952440647, "deps": [[40386456601120721, "percent_encoding", false, 14666230218821310641], [1200537532907108615, "url<PERSON><PERSON>n", false, 7627041243772429328], [2013030631243296465, "webview2_com", false, 6102230966157782237], [2671782512663819132, "tauri_utils", false, 9883568072656655585], [3150220818285335163, "url", false, 14199394255531846341], [3331586631144870129, "getrandom", false, 302092907866380757], [4143744114649553716, "raw_window_handle", false, 9360050597165366007], [4494683389616423722, "muda", false, 14279220663210740627], [4919829919303820331, "serialize_to_javascript", false, 1415409193099265536], [5986029879202738730, "log", false, 2351528888146949315], [6089812615193535349, "tauri_runtime", false, 4824090621243199533], [7573826311589115053, "tauri_macros", false, 948100931064977814], [9010263965687315507, "http", false, 9326335740251934562], [9689903380558560274, "serde", false, 11664928829986004705], [10229185211513642314, "mime", false, 12029774490562403371], [10806645703491011684, "thiserror", false, 12814716657068513109], [11599800339996261026, "tauri_runtime_wry", false, 4741275794850961917], [11989259058781683633, "dunce", false, 11858794786811574169], [12393800526703971956, "tokio", false, 2142785373852924899], [12565293087094287914, "window_vibrancy", false, 13714837349278738148], [12986574360607194341, "serde_repr", false, 5304173902337742823], [13077543566650298139, "heck", false, 10358157586554867553], [13625485746686963219, "anyhow", false, 6546084173510242191], [14039947826026167952, "build_script_build", false, 3799535184955189743], [14585479307175734061, "windows", false, 3140733581549053838], [15367738274754116744, "serde_json", false, 2199351709971545488], [16928111194414003569, "dirs", false, 6391137576393731072], [17155886227862585100, "glob", false, 2178336415492610584]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-fb82e2df3b92b114\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}