function Ss(r,e){(e==null||e>r.length)&&(e=r.length);for(var t=0,a=Array(e);t<e;t++)a[t]=r[t];return a}function Uf(r){if(Array.isArray(r))return r}function $f(r){if(Array.isArray(r))return Ss(r)}function vt(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function Kf(r,e){for(var t=0;t<e.length;t++){var a=e[t];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(r,Kl(a.key),a)}}function ft(r,e,t){return e&&Kf(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}function Tr(r,e){var t=typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"];if(!t){if(Array.isArray(r)||(t=Us(r))||e){t&&(r=t);var a=0,n=function(){};return{s:n,n:function(){return a>=r.length?{done:!0}:{done:!1,value:r[a++]}},e:function(l){throw l},f:n}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var i,s=!0,o=!1;return{s:function(){t=t.call(r)},n:function(){var l=t.next();return s=l.done,l},e:function(l){o=!0,i=l},f:function(){try{s||t.return==null||t.return()}finally{if(o)throw i}}}}function $l(r,e,t){return(e=Kl(e))in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function Yf(r){if(typeof Symbol<"u"&&r[Symbol.iterator]!=null||r["@@iterator"]!=null)return Array.from(r)}function Xf(r,e){var t=r==null?null:typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"];if(t!=null){var a,n,i,s,o=[],l=!0,u=!1;try{if(i=(t=t.call(r)).next,e===0){if(Object(t)!==t)return;l=!1}else for(;!(l=(a=i.call(t)).done)&&(o.push(a.value),o.length!==e);l=!0);}catch(v){u=!0,n=v}finally{try{if(!l&&t.return!=null&&(s=t.return(),Object(s)!==s))return}finally{if(u)throw n}}return o}}function Zf(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Qf(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function je(r,e){return Uf(r)||Xf(r,e)||Us(r,e)||Zf()}function pn(r){return $f(r)||Yf(r)||Us(r)||Qf()}function Jf(r,e){if(typeof r!="object"||!r)return r;var t=r[Symbol.toPrimitive];if(t!==void 0){var a=t.call(r,e);if(typeof a!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(r)}function Kl(r){var e=Jf(r,"string");return typeof e=="symbol"?e:e+""}function rr(r){"@babel/helpers - typeof";return rr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},rr(r)}function Us(r,e){if(r){if(typeof r=="string")return Ss(r,e);var t={}.toString.call(r).slice(8,-1);return t==="Object"&&r.constructor&&(t=r.constructor.name),t==="Map"||t==="Set"?Array.from(r):t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Ss(r,e):void 0}}var Je=typeof window>"u"?null:window,xo=Je?Je.navigator:null;Je&&Je.document;var jf=rr(""),Yl=rr({}),ec=rr(function(){}),rc=typeof HTMLElement>"u"?"undefined":rr(HTMLElement),Aa=function(e){return e&&e.instanceString&&We(e.instanceString)?e.instanceString():null},fe=function(e){return e!=null&&rr(e)==jf},We=function(e){return e!=null&&rr(e)===ec},Fe=function(e){return!Dr(e)&&(Array.isArray?Array.isArray(e):e!=null&&e instanceof Array)},Pe=function(e){return e!=null&&rr(e)===Yl&&!Fe(e)&&e.constructor===Object},tc=function(e){return e!=null&&rr(e)===Yl},te=function(e){return e!=null&&rr(e)===rr(1)&&!isNaN(e)},ac=function(e){return te(e)&&Math.floor(e)===e},yn=function(e){if(rc!=="undefined")return e!=null&&e instanceof HTMLElement},Dr=function(e){return Ra(e)||Xl(e)},Ra=function(e){return Aa(e)==="collection"&&e._private.single},Xl=function(e){return Aa(e)==="collection"&&!e._private.single},$s=function(e){return Aa(e)==="core"},Zl=function(e){return Aa(e)==="stylesheet"},nc=function(e){return Aa(e)==="event"},nt=function(e){return e==null?!0:!!(e===""||e.match(/^\s+$/))},ic=function(e){return typeof HTMLElement>"u"?!1:e instanceof HTMLElement},sc=function(e){return Pe(e)&&te(e.x1)&&te(e.x2)&&te(e.y1)&&te(e.y2)},oc=function(e){return tc(e)&&We(e.then)},uc=function(){return xo&&xo.userAgent.match(/msie|trident|edge/i)},Yt=function(e,t){t||(t=function(){if(arguments.length===1)return arguments[0];if(arguments.length===0)return"undefined";for(var i=[],s=0;s<arguments.length;s++)i.push(arguments[s]);return i.join("$")});var a=function(){var i=this,s=arguments,o,l=t.apply(i,s),u=a.cache;return(o=u[l])||(o=u[l]=e.apply(i,s)),o};return a.cache={},a},Ks=Yt(function(r){return r.replace(/([A-Z])/g,function(e){return"-"+e.toLowerCase()})}),An=Yt(function(r){return r.replace(/(-\w)/g,function(e){return e[1].toUpperCase()})}),Ql=Yt(function(r,e){return r+e[0].toUpperCase()+e.substring(1)},function(r,e){return r+"$"+e}),Eo=function(e){return nt(e)?e:e.charAt(0).toUpperCase()+e.substring(1)},er="(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))",lc="rgb[a]?\\(("+er+"[%]?)\\s*,\\s*("+er+"[%]?)\\s*,\\s*("+er+"[%]?)(?:\\s*,\\s*("+er+"))?\\)",vc="rgb[a]?\\((?:"+er+"[%]?)\\s*,\\s*(?:"+er+"[%]?)\\s*,\\s*(?:"+er+"[%]?)(?:\\s*,\\s*(?:"+er+"))?\\)",fc="hsl[a]?\\(("+er+")\\s*,\\s*("+er+"[%])\\s*,\\s*("+er+"[%])(?:\\s*,\\s*("+er+"))?\\)",cc="hsl[a]?\\((?:"+er+")\\s*,\\s*(?:"+er+"[%])\\s*,\\s*(?:"+er+"[%])(?:\\s*,\\s*(?:"+er+"))?\\)",dc="\\#[0-9a-fA-F]{3}",hc="\\#[0-9a-fA-F]{6}",Jl=function(e,t){return e<t?-1:e>t?1:0},gc=function(e,t){return-1*Jl(e,t)},he=Object.assign!=null?Object.assign.bind(Object):function(r){for(var e=arguments,t=1;t<e.length;t++){var a=e[t];if(a!=null)for(var n=Object.keys(a),i=0;i<n.length;i++){var s=n[i];r[s]=a[s]}}return r},pc=function(e){if(!(!(e.length===4||e.length===7)||e[0]!=="#")){var t=e.length===4,a,n,i,s=16;return t?(a=parseInt(e[1]+e[1],s),n=parseInt(e[2]+e[2],s),i=parseInt(e[3]+e[3],s)):(a=parseInt(e[1]+e[2],s),n=parseInt(e[3]+e[4],s),i=parseInt(e[5]+e[6],s)),[a,n,i]}},yc=function(e){var t,a,n,i,s,o,l,u;function v(d,y,g){return g<0&&(g+=1),g>1&&(g-=1),g<1/6?d+(y-d)*6*g:g<1/2?y:g<2/3?d+(y-d)*(2/3-g)*6:d}var f=new RegExp("^"+fc+"$").exec(e);if(f){if(a=parseInt(f[1]),a<0?a=(360- -1*a%360)%360:a>360&&(a=a%360),a/=360,n=parseFloat(f[2]),n<0||n>100||(n=n/100,i=parseFloat(f[3]),i<0||i>100)||(i=i/100,s=f[4],s!==void 0&&(s=parseFloat(s),s<0||s>1)))return;if(n===0)o=l=u=Math.round(i*255);else{var c=i<.5?i*(1+n):i+n-i*n,h=2*i-c;o=Math.round(255*v(h,c,a+1/3)),l=Math.round(255*v(h,c,a)),u=Math.round(255*v(h,c,a-1/3))}t=[o,l,u,s]}return t},mc=function(e){var t,a=new RegExp("^"+lc+"$").exec(e);if(a){t=[];for(var n=[],i=1;i<=3;i++){var s=a[i];if(s[s.length-1]==="%"&&(n[i]=!0),s=parseFloat(s),n[i]&&(s=s/100*255),s<0||s>255)return;t.push(Math.floor(s))}var o=n[1]||n[2]||n[3],l=n[1]&&n[2]&&n[3];if(o&&!l)return;var u=a[4];if(u!==void 0){if(u=parseFloat(u),u<0||u>1)return;t.push(u)}}return t},bc=function(e){return wc[e.toLowerCase()]},jl=function(e){return(Fe(e)?e:null)||bc(e)||pc(e)||mc(e)||yc(e)},wc={transparent:[0,0,0,0],aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],grey:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},ev=function(e){for(var t=e.map,a=e.keys,n=a.length,i=0;i<n;i++){var s=a[i];if(Pe(s))throw Error("Tried to set map with object key");i<a.length-1?(t[s]==null&&(t[s]={}),t=t[s]):t[s]=e.value}},rv=function(e){for(var t=e.map,a=e.keys,n=a.length,i=0;i<n;i++){var s=a[i];if(Pe(s))throw Error("Tried to get map with object key");if(t=t[s],t==null)return t}return t},Ua=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ma(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var Zn,Co;function La(){if(Co)return Zn;Co=1;function r(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}return Zn=r,Zn}var Qn,To;function xc(){if(To)return Qn;To=1;var r=typeof Ua=="object"&&Ua&&Ua.Object===Object&&Ua;return Qn=r,Qn}var Jn,So;function Rn(){if(So)return Jn;So=1;var r=xc(),e=typeof self=="object"&&self&&self.Object===Object&&self,t=r||e||Function("return this")();return Jn=t,Jn}var jn,Do;function Ec(){if(Do)return jn;Do=1;var r=Rn(),e=function(){return r.Date.now()};return jn=e,jn}var ei,ko;function Cc(){if(ko)return ei;ko=1;var r=/\s/;function e(t){for(var a=t.length;a--&&r.test(t.charAt(a)););return a}return ei=e,ei}var ri,Bo;function Tc(){if(Bo)return ri;Bo=1;var r=Cc(),e=/^\s+/;function t(a){return a&&a.slice(0,r(a)+1).replace(e,"")}return ri=t,ri}var ti,Po;function Ys(){if(Po)return ti;Po=1;var r=Rn(),e=r.Symbol;return ti=e,ti}var ai,Ao;function Sc(){if(Ao)return ai;Ao=1;var r=Ys(),e=Object.prototype,t=e.hasOwnProperty,a=e.toString,n=r?r.toStringTag:void 0;function i(s){var o=t.call(s,n),l=s[n];try{s[n]=void 0;var u=!0}catch{}var v=a.call(s);return u&&(o?s[n]=l:delete s[n]),v}return ai=i,ai}var ni,Ro;function Dc(){if(Ro)return ni;Ro=1;var r=Object.prototype,e=r.toString;function t(a){return e.call(a)}return ni=t,ni}var ii,Mo;function tv(){if(Mo)return ii;Mo=1;var r=Ys(),e=Sc(),t=Dc(),a="[object Null]",n="[object Undefined]",i=r?r.toStringTag:void 0;function s(o){return o==null?o===void 0?n:a:i&&i in Object(o)?e(o):t(o)}return ii=s,ii}var si,Lo;function kc(){if(Lo)return si;Lo=1;function r(e){return e!=null&&typeof e=="object"}return si=r,si}var oi,Io;function Ia(){if(Io)return oi;Io=1;var r=tv(),e=kc(),t="[object Symbol]";function a(n){return typeof n=="symbol"||e(n)&&r(n)==t}return oi=a,oi}var ui,Oo;function Bc(){if(Oo)return ui;Oo=1;var r=Tc(),e=La(),t=Ia(),a=NaN,n=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,s=/^0o[0-7]+$/i,o=parseInt;function l(u){if(typeof u=="number")return u;if(t(u))return a;if(e(u)){var v=typeof u.valueOf=="function"?u.valueOf():u;u=e(v)?v+"":v}if(typeof u!="string")return u===0?u:+u;u=r(u);var f=i.test(u);return f||s.test(u)?o(u.slice(2),f?2:8):n.test(u)?a:+u}return ui=l,ui}var li,No;function Pc(){if(No)return li;No=1;var r=La(),e=Ec(),t=Bc(),a="Expected a function",n=Math.max,i=Math.min;function s(o,l,u){var v,f,c,h,d,y,g=0,p=!1,m=!1,b=!0;if(typeof o!="function")throw new TypeError(a);l=t(l)||0,r(u)&&(p=!!u.leading,m="maxWait"in u,c=m?n(t(u.maxWait)||0,l):c,b="trailing"in u?!!u.trailing:b);function w(B){var R=v,M=f;return v=f=void 0,g=B,h=o.apply(M,R),h}function E(B){return g=B,d=setTimeout(k,l),p?w(B):h}function C(B){var R=B-y,M=B-g,I=l-R;return m?i(I,c-M):I}function x(B){var R=B-y,M=B-g;return y===void 0||R>=l||R<0||m&&M>=c}function k(){var B=e();if(x(B))return S(B);d=setTimeout(k,C(B))}function S(B){return d=void 0,b&&v?w(B):(v=f=void 0,h)}function P(){d!==void 0&&clearTimeout(d),g=0,v=y=f=d=void 0}function D(){return d===void 0?h:S(e())}function A(){var B=e(),R=x(B);if(v=arguments,f=this,y=B,R){if(d===void 0)return E(y);if(m)return clearTimeout(d),d=setTimeout(k,l),w(y)}return d===void 0&&(d=setTimeout(k,l)),h}return A.cancel=P,A.flush=D,A}return li=s,li}var Ac=Pc(),Oa=Ma(Ac),vi=Je?Je.performance:null,av=vi&&vi.now?function(){return vi.now()}:function(){return Date.now()},Rc=function(){if(Je){if(Je.requestAnimationFrame)return function(r){Je.requestAnimationFrame(r)};if(Je.mozRequestAnimationFrame)return function(r){Je.mozRequestAnimationFrame(r)};if(Je.webkitRequestAnimationFrame)return function(r){Je.webkitRequestAnimationFrame(r)};if(Je.msRequestAnimationFrame)return function(r){Je.msRequestAnimationFrame(r)}}return function(r){r&&setTimeout(function(){r(av())},1e3/60)}}(),mn=function(e){return Rc(e)},Yr=av,xt=9261,nv=65599,qt=5381,iv=function(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:xt,a=t,n;n=e.next(),!n.done;)a=a*nv+n.value|0;return a},wa=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:xt;return t*nv+e|0},xa=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:qt;return(t<<5)+t+e|0},Mc=function(e,t){return e*2097152+t},jr=function(e){return e[0]*2097152+e[1]},$a=function(e,t){return[wa(e[0],t[0]),xa(e[1],t[1])]},zo=function(e,t){var a={value:0,done:!1},n=0,i=e.length,s={next:function(){return n<i?a.value=e[n++]:a.done=!0,a}};return iv(s,t)},Tt=function(e,t){var a={value:0,done:!1},n=0,i=e.length,s={next:function(){return n<i?a.value=e.charCodeAt(n++):a.done=!0,a}};return iv(s,t)},sv=function(){return Lc(arguments)},Lc=function(e){for(var t,a=0;a<e.length;a++){var n=e[a];a===0?t=Tt(n):t=Tt(n,t)}return t},Fo=!0,Ic=console.warn!=null,Oc=console.trace!=null,Xs=Number.MAX_SAFE_INTEGER||9007199254740991,ov=function(){return!0},bn=function(){return!1},Vo=function(){return 0},Zs=function(){},He=function(e){throw new Error(e)},uv=function(e){if(e!==void 0)Fo=!!e;else return Fo},Le=function(e){uv()&&(Ic?console.warn(e):(console.log(e),Oc&&console.trace()))},Nc=function(e){return he({},e)},qr=function(e){return e==null?e:Fe(e)?e.slice():Pe(e)?Nc(e):e},zc=function(e){return e.slice()},lv=function(e,t){for(t=e="";e++<36;t+=e*51&52?(e^15?8^Math.random()*(e^20?16:4):4).toString(16):"-");return t},Fc={},vv=function(){return Fc},fr=function(e){var t=Object.keys(e);return function(a){for(var n={},i=0;i<t.length;i++){var s=t[i],o=a==null?void 0:a[s];n[s]=o===void 0?e[s]:o}return n}},it=function(e,t,a){for(var n=e.length-1;n>=0;n--)e[n]===t&&e.splice(n,1)},Qs=function(e){e.splice(0,e.length)},Vc=function(e,t){for(var a=0;a<t.length;a++){var n=t[a];e.push(n)}},Er=function(e,t,a){return a&&(t=Ql(a,t)),e[t]},$r=function(e,t,a,n){a&&(t=Ql(a,t)),e[t]=n},qc=function(){function r(){vt(this,r),this._obj={}}return ft(r,[{key:"set",value:function(t,a){return this._obj[t]=a,this}},{key:"delete",value:function(t){return this._obj[t]=void 0,this}},{key:"clear",value:function(){this._obj={}}},{key:"has",value:function(t){return this._obj[t]!==void 0}},{key:"get",value:function(t){return this._obj[t]}}])}(),Kr=typeof Map<"u"?Map:qc,_c="undefined",Gc=function(){function r(e){if(vt(this,r),this._obj=Object.create(null),this.size=0,e!=null){var t;e.instanceString!=null&&e.instanceString()===this.instanceString()?t=e.toArray():t=e;for(var a=0;a<t.length;a++)this.add(t[a])}}return ft(r,[{key:"instanceString",value:function(){return"set"}},{key:"add",value:function(t){var a=this._obj;a[t]!==1&&(a[t]=1,this.size++)}},{key:"delete",value:function(t){var a=this._obj;a[t]===1&&(a[t]=0,this.size--)}},{key:"clear",value:function(){this._obj=Object.create(null)}},{key:"has",value:function(t){return this._obj[t]===1}},{key:"toArray",value:function(){var t=this;return Object.keys(this._obj).filter(function(a){return t.has(a)})}},{key:"forEach",value:function(t,a){return this.toArray().forEach(t,a)}}])}(),jt=(typeof Set>"u"?"undefined":rr(Set))!==_c?Set:Gc,Mn=function(e,t){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(e===void 0||t===void 0||!$s(e)){He("An element must have a core reference and parameters set");return}var n=t.group;if(n==null&&(t.data&&t.data.source!=null&&t.data.target!=null?n="edges":n="nodes"),n!=="nodes"&&n!=="edges"){He("An element must be of type `nodes` or `edges`; you specified `"+n+"`");return}this.length=1,this[0]=this;var i=this._private={cy:e,single:!0,data:t.data||{},position:t.position||{x:0,y:0},autoWidth:void 0,autoHeight:void 0,autoPadding:void 0,compoundBoundsClean:!1,listeners:[],group:n,style:{},rstyle:{},styleCxts:[],styleKeys:{},removed:!0,selected:!!t.selected,selectable:t.selectable===void 0?!0:!!t.selectable,locked:!!t.locked,grabbed:!1,grabbable:t.grabbable===void 0?!0:!!t.grabbable,pannable:t.pannable===void 0?n==="edges":!!t.pannable,active:!1,classes:new jt,animation:{current:[],queue:[]},rscratch:{},scratch:t.scratch||{},edges:[],children:[],parent:t.parent&&t.parent.isNode()?t.parent:null,traversalCache:{},backgrounding:!1,bbCache:null,bbCacheShift:{x:0,y:0},bodyBounds:null,overlayBounds:null,labelBounds:{all:null,source:null,target:null,main:null},arrowBounds:{source:null,target:null,"mid-source":null,"mid-target":null}};if(i.position.x==null&&(i.position.x=0),i.position.y==null&&(i.position.y=0),t.renderedPosition){var s=t.renderedPosition,o=e.pan(),l=e.zoom();i.position={x:(s.x-o.x)/l,y:(s.y-o.y)/l}}var u=[];Fe(t.classes)?u=t.classes:fe(t.classes)&&(u=t.classes.split(/\s+/));for(var v=0,f=u.length;v<f;v++){var c=u[v];!c||c===""||i.classes.add(c)}this.createEmitter(),(a===void 0||a)&&this.restore();var h=t.style||t.css;h&&(Le("Setting a `style` bypass at element creation should be done only when absolutely necessary.  Try to use the stylesheet instead."),this.style(h))},qo=function(e){return e={bfs:e.bfs||!e.dfs,dfs:e.dfs||!e.bfs},function(a,n,i){var s;Pe(a)&&!Dr(a)&&(s=a,a=s.roots||s.root,n=s.visit,i=s.directed),i=arguments.length===2&&!We(n)?n:i,n=We(n)?n:function(){};for(var o=this._private.cy,l=a=fe(a)?this.filter(a):a,u=[],v=[],f={},c={},h={},d=0,y,g=this.byGroup(),p=g.nodes,m=g.edges,b=0;b<l.length;b++){var w=l[b],E=w.id();w.isNode()&&(u.unshift(w),e.bfs&&(h[E]=!0,v.push(w)),c[E]=0)}for(var C=function(){var B=e.bfs?u.shift():u.pop(),R=B.id();if(e.dfs){if(h[R])return 0;h[R]=!0,v.push(B)}var M=c[R],I=f[R],L=I!=null?I.source():null,O=I!=null?I.target():null,V=I==null?void 0:B.same(L)?O[0]:L[0],G;if(G=n(B,I,V,d++,M),G===!0)return y=B,1;if(G===!1)return 1;for(var N=B.connectedEdges().filter(function(Z){return(!i||Z.source().same(B))&&m.has(Z)}),F=0;F<N.length;F++){var K=N[F],X=K.connectedNodes().filter(function(Z){return!Z.same(B)&&p.has(Z)}),Q=X.id();X.length!==0&&!h[Q]&&(X=X[0],u.push(X),e.bfs&&(h[Q]=!0,v.push(X)),f[Q]=K,c[Q]=c[R]+1)}},x;u.length!==0&&(x=C(),!(x!==0&&x===1)););for(var k=o.collection(),S=0;S<v.length;S++){var P=v[S],D=f[P.id()];D!=null&&k.push(D),k.push(P)}return{path:o.collection(k),found:o.collection(y)}}},Ea={breadthFirstSearch:qo({bfs:!0}),depthFirstSearch:qo({dfs:!0})};Ea.bfs=Ea.breadthFirstSearch;Ea.dfs=Ea.depthFirstSearch;var nn={exports:{}},Hc=nn.exports,_o;function Wc(){return _o||(_o=1,function(r,e){(function(){var t,a,n,i,s,o,l,u,v,f,c,h,d,y,g;n=Math.floor,f=Math.min,a=function(p,m){return p<m?-1:p>m?1:0},v=function(p,m,b,w,E){var C;if(b==null&&(b=0),E==null&&(E=a),b<0)throw new Error("lo must be non-negative");for(w==null&&(w=p.length);b<w;)C=n((b+w)/2),E(m,p[C])<0?w=C:b=C+1;return[].splice.apply(p,[b,b-b].concat(m)),m},o=function(p,m,b){return b==null&&(b=a),p.push(m),y(p,0,p.length-1,b)},s=function(p,m){var b,w;return m==null&&(m=a),b=p.pop(),p.length?(w=p[0],p[0]=b,g(p,0,m)):w=b,w},u=function(p,m,b){var w;return b==null&&(b=a),w=p[0],p[0]=m,g(p,0,b),w},l=function(p,m,b){var w;return b==null&&(b=a),p.length&&b(p[0],m)<0&&(w=[p[0],m],m=w[0],p[0]=w[1],g(p,0,b)),m},i=function(p,m){var b,w,E,C,x,k;for(m==null&&(m=a),C=(function(){k=[];for(var S=0,P=n(p.length/2);0<=P?S<P:S>P;0<=P?S++:S--)k.push(S);return k}).apply(this).reverse(),x=[],w=0,E=C.length;w<E;w++)b=C[w],x.push(g(p,b,m));return x},d=function(p,m,b){var w;if(b==null&&(b=a),w=p.indexOf(m),w!==-1)return y(p,0,w,b),g(p,w,b)},c=function(p,m,b){var w,E,C,x,k;if(b==null&&(b=a),E=p.slice(0,m),!E.length)return E;for(i(E,b),k=p.slice(m),C=0,x=k.length;C<x;C++)w=k[C],l(E,w,b);return E.sort(b).reverse()},h=function(p,m,b){var w,E,C,x,k,S,P,D,A;if(b==null&&(b=a),m*10<=p.length){if(C=p.slice(0,m).sort(b),!C.length)return C;for(E=C[C.length-1],P=p.slice(m),x=0,S=P.length;x<S;x++)w=P[x],b(w,E)<0&&(v(C,w,0,null,b),C.pop(),E=C[C.length-1]);return C}for(i(p,b),A=[],k=0,D=f(m,p.length);0<=D?k<D:k>D;0<=D?++k:--k)A.push(s(p,b));return A},y=function(p,m,b,w){var E,C,x;for(w==null&&(w=a),E=p[b];b>m;){if(x=b-1>>1,C=p[x],w(E,C)<0){p[b]=C,b=x;continue}break}return p[b]=E},g=function(p,m,b){var w,E,C,x,k;for(b==null&&(b=a),E=p.length,k=m,C=p[m],w=2*m+1;w<E;)x=w+1,x<E&&!(b(p[w],p[x])<0)&&(w=x),p[m]=p[w],m=w,w=2*m+1;return p[m]=C,y(p,k,m,b)},t=function(){p.push=o,p.pop=s,p.replace=u,p.pushpop=l,p.heapify=i,p.updateItem=d,p.nlargest=c,p.nsmallest=h;function p(m){this.cmp=m??a,this.nodes=[]}return p.prototype.push=function(m){return o(this.nodes,m,this.cmp)},p.prototype.pop=function(){return s(this.nodes,this.cmp)},p.prototype.peek=function(){return this.nodes[0]},p.prototype.contains=function(m){return this.nodes.indexOf(m)!==-1},p.prototype.replace=function(m){return u(this.nodes,m,this.cmp)},p.prototype.pushpop=function(m){return l(this.nodes,m,this.cmp)},p.prototype.heapify=function(){return i(this.nodes,this.cmp)},p.prototype.updateItem=function(m){return d(this.nodes,m,this.cmp)},p.prototype.clear=function(){return this.nodes=[]},p.prototype.empty=function(){return this.nodes.length===0},p.prototype.size=function(){return this.nodes.length},p.prototype.clone=function(){var m;return m=new p,m.nodes=this.nodes.slice(0),m},p.prototype.toArray=function(){return this.nodes.slice(0)},p.prototype.insert=p.prototype.push,p.prototype.top=p.prototype.peek,p.prototype.front=p.prototype.peek,p.prototype.has=p.prototype.contains,p.prototype.copy=p.prototype.clone,p}(),function(p,m){return r.exports=m()}(this,function(){return t})}).call(Hc)}(nn)),nn.exports}var fi,Go;function Uc(){return Go||(Go=1,fi=Wc()),fi}var $c=Uc(),Na=Ma($c),Kc=fr({root:null,weight:function(e){return 1},directed:!1}),Yc={dijkstra:function(e){if(!Pe(e)){var t=arguments;e={root:t[0],weight:t[1],directed:t[2]}}var a=Kc(e),n=a.root,i=a.weight,s=a.directed,o=this,l=i,u=fe(n)?this.filter(n)[0]:n[0],v={},f={},c={},h=this.byGroup(),d=h.nodes,y=h.edges;y.unmergeBy(function(M){return M.isLoop()});for(var g=function(I){return v[I.id()]},p=function(I,L){v[I.id()]=L,m.updateItem(I)},m=new Na(function(M,I){return g(M)-g(I)}),b=0;b<d.length;b++){var w=d[b];v[w.id()]=w.same(u)?0:1/0,m.push(w)}for(var E=function(I,L){for(var O=(s?I.edgesTo(L):I.edgesWith(L)).intersect(y),V=1/0,G,N=0;N<O.length;N++){var F=O[N],K=l(F);(K<V||!G)&&(V=K,G=F)}return{edge:G,dist:V}};m.size()>0;){var C=m.pop(),x=g(C),k=C.id();if(c[k]=x,x!==1/0)for(var S=C.neighborhood().intersect(d),P=0;P<S.length;P++){var D=S[P],A=D.id(),B=E(C,D),R=x+B.dist;R<g(D)&&(p(D,R),f[A]={node:C,edge:B.edge})}}return{distanceTo:function(I){var L=fe(I)?d.filter(I)[0]:I[0];return c[L.id()]},pathTo:function(I){var L=fe(I)?d.filter(I)[0]:I[0],O=[],V=L,G=V.id();if(L.length>0)for(O.unshift(L);f[G];){var N=f[G];O.unshift(N.edge),O.unshift(N.node),V=N.node,G=V.id()}return o.spawn(O)}}}},Xc={kruskal:function(e){e=e||function(b){return 1};for(var t=this.byGroup(),a=t.nodes,n=t.edges,i=a.length,s=new Array(i),o=a,l=function(w){for(var E=0;E<s.length;E++){var C=s[E];if(C.has(w))return E}},u=0;u<i;u++)s[u]=this.spawn(a[u]);for(var v=n.sort(function(b,w){return e(b)-e(w)}),f=0;f<v.length;f++){var c=v[f],h=c.source()[0],d=c.target()[0],y=l(h),g=l(d),p=s[y],m=s[g];y!==g&&(o.merge(c),p.merge(m),s.splice(g,1))}return o}},Zc=fr({root:null,goal:null,weight:function(e){return 1},heuristic:function(e){return 0},directed:!1}),Qc={aStar:function(e){var t=this.cy(),a=Zc(e),n=a.root,i=a.goal,s=a.heuristic,o=a.directed,l=a.weight;n=t.collection(n)[0],i=t.collection(i)[0];var u=n.id(),v=i.id(),f={},c={},h={},d=new Na(function(G,N){return c[G.id()]-c[N.id()]}),y=new jt,g={},p={},m=function(N,F){d.push(N),y.add(F)},b,w,E=function(){b=d.pop(),w=b.id(),y.delete(w)},C=function(N){return y.has(N)};m(n,u),f[u]=0,c[u]=s(n);for(var x=0;d.size()>0;){if(E(),x++,w===v){for(var k=[],S=i,P=v,D=p[P];k.unshift(S),D!=null&&k.unshift(D),S=g[P],S!=null;)P=S.id(),D=p[P];return{found:!0,distance:f[w],path:this.spawn(k),steps:x}}h[w]=!0;for(var A=b._private.edges,B=0;B<A.length;B++){var R=A[B];if(this.hasElementWithId(R.id())&&!(o&&R.data("source")!==w)){var M=R.source(),I=R.target(),L=M.id()!==w?M:I,O=L.id();if(this.hasElementWithId(O)&&!h[O]){var V=f[w]+l(R);if(!C(O)){f[O]=V,c[O]=V+s(L),m(L,O),g[O]=b,p[O]=R;continue}V<f[O]&&(f[O]=V,c[O]=V+s(L),g[O]=b,p[O]=R)}}}}return{found:!1,distance:void 0,path:void 0,steps:x}}},Jc=fr({weight:function(e){return 1},directed:!1}),jc={floydWarshall:function(e){for(var t=this.cy(),a=Jc(e),n=a.weight,i=a.directed,s=n,o=this.byGroup(),l=o.nodes,u=o.edges,v=l.length,f=v*v,c=function(K){return l.indexOf(K)},h=function(K){return l[K]},d=new Array(f),y=0;y<f;y++){var g=y%v,p=(y-g)/v;p===g?d[y]=0:d[y]=1/0}for(var m=new Array(f),b=new Array(f),w=0;w<u.length;w++){var E=u[w],C=E.source()[0],x=E.target()[0];if(C!==x){var k=c(C),S=c(x),P=k*v+S,D=s(E);if(d[P]>D&&(d[P]=D,m[P]=S,b[P]=E),!i){var A=S*v+k;!i&&d[A]>D&&(d[A]=D,m[A]=k,b[A]=E)}}}for(var B=0;B<v;B++)for(var R=0;R<v;R++)for(var M=R*v+B,I=0;I<v;I++){var L=R*v+I,O=B*v+I;d[M]+d[O]<d[L]&&(d[L]=d[M]+d[O],m[L]=m[M])}var V=function(K){return(fe(K)?t.filter(K):K)[0]},G=function(K){return c(V(K))},N={distance:function(K,X){var Q=G(K),Z=G(X);return d[Q*v+Z]},path:function(K,X){var Q=G(K),Z=G(X),re=h(Q);if(Q===Z)return re.collection();if(m[Q*v+Z]==null)return t.collection();var ae=t.collection(),J=Q,z;for(ae.merge(re);Q!==Z;)J=Q,Q=m[Q*v+Z],z=b[J*v+Q],ae.merge(z),ae.merge(h(Q));return ae}};return N}},ed=fr({weight:function(e){return 1},directed:!1,root:null}),rd={bellmanFord:function(e){var t=this,a=ed(e),n=a.weight,i=a.directed,s=a.root,o=n,l=this,u=this.cy(),v=this.byGroup(),f=v.edges,c=v.nodes,h=c.length,d=new Kr,y=!1,g=[];s=u.collection(s)[0],f.unmergeBy(function(Ie){return Ie.isLoop()});for(var p=f.length,m=function(se){var oe=d.get(se.id());return oe||(oe={},d.set(se.id(),oe)),oe},b=function(se){return(fe(se)?u.$(se):se)[0]},w=function(se){return m(b(se)).dist},E=function(se){for(var oe=arguments.length>1&&arguments[1]!==void 0?arguments[1]:s,ce=b(se),ge=[],de=ce;;){if(de==null)return t.spawn();var ye=m(de),we=ye.edge,De=ye.pred;if(ge.unshift(de[0]),de.same(oe)&&ge.length>0)break;we!=null&&ge.unshift(we),de=De}return l.spawn(ge)},C=0;C<h;C++){var x=c[C],k=m(x);x.same(s)?k.dist=0:k.dist=1/0,k.pred=null,k.edge=null}for(var S=!1,P=function(se,oe,ce,ge,de,ye){var we=ge.dist+ye;we<de.dist&&!ce.same(ge.edge)&&(de.dist=we,de.pred=se,de.edge=ce,S=!0)},D=1;D<h;D++){S=!1;for(var A=0;A<p;A++){var B=f[A],R=B.source(),M=B.target(),I=o(B),L=m(R),O=m(M);P(R,M,B,L,O,I),i||P(M,R,B,O,L,I)}if(!S)break}if(S)for(var V=[],G=0;G<p;G++){var N=f[G],F=N.source(),K=N.target(),X=o(N),Q=m(F).dist,Z=m(K).dist;if(Q+X<Z||!i&&Z+X<Q)if(y||(Le("Graph contains a negative weight cycle for Bellman-Ford"),y=!0),e.findNegativeWeightCycles!==!1){var re=[];Q+X<Z&&re.push(F),!i&&Z+X<Q&&re.push(K);for(var ae=re.length,J=0;J<ae;J++){var z=re[J],q=[z];q.push(m(z).edge);for(var H=m(z).pred;q.indexOf(H)===-1;)q.push(H),q.push(m(H).edge),H=m(H).pred;q=q.slice(q.indexOf(H));for(var ee=q[0].id(),ne=0,be=2;be<q.length;be+=2)q[be].id()<ee&&(ee=q[be].id(),ne=be);q=q.slice(ne).concat(q.slice(0,ne)),q.push(q[0]);var _e=q.map(function(Ie){return Ie.id()}).join(",");V.indexOf(_e)===-1&&(g.push(l.spawn(q)),V.push(_e))}}else break}return{distanceTo:w,pathTo:E,hasNegativeWeightCycle:y,negativeWeightCycles:g}}},td=Math.sqrt(2),ad=function(e,t,a){a.length===0&&He("Karger-Stein must be run on a connected (sub)graph");for(var n=a[e],i=n[1],s=n[2],o=t[i],l=t[s],u=a,v=u.length-1;v>=0;v--){var f=u[v],c=f[1],h=f[2];(t[c]===o&&t[h]===l||t[c]===l&&t[h]===o)&&u.splice(v,1)}for(var d=0;d<u.length;d++){var y=u[d];y[1]===l?(u[d]=y.slice(),u[d][1]=o):y[2]===l&&(u[d]=y.slice(),u[d][2]=o)}for(var g=0;g<t.length;g++)t[g]===l&&(t[g]=o);return u},ci=function(e,t,a,n){for(;a>n;){var i=Math.floor(Math.random()*t.length);t=ad(i,e,t),a--}return t},nd={kargerStein:function(){var e=this,t=this.byGroup(),a=t.nodes,n=t.edges;n.unmergeBy(function(O){return O.isLoop()});var i=a.length,s=n.length,o=Math.ceil(Math.pow(Math.log(i)/Math.LN2,2)),l=Math.floor(i/td);if(i<2){He("At least 2 nodes are required for Karger-Stein algorithm");return}for(var u=[],v=0;v<s;v++){var f=n[v];u.push([v,a.indexOf(f.source()),a.indexOf(f.target())])}for(var c=1/0,h=[],d=new Array(i),y=new Array(i),g=new Array(i),p=function(V,G){for(var N=0;N<i;N++)G[N]=V[N]},m=0;m<=o;m++){for(var b=0;b<i;b++)y[b]=b;var w=ci(y,u.slice(),i,l),E=w.slice();p(y,g);var C=ci(y,w,l,2),x=ci(g,E,l,2);C.length<=x.length&&C.length<c?(c=C.length,h=C,p(y,d)):x.length<=C.length&&x.length<c&&(c=x.length,h=x,p(g,d))}for(var k=this.spawn(h.map(function(O){return n[O[0]]})),S=this.spawn(),P=this.spawn(),D=d[0],A=0;A<d.length;A++){var B=d[A],R=a[A];B===D?S.merge(R):P.merge(R)}var M=function(V){var G=e.spawn();return V.forEach(function(N){G.merge(N),N.connectedEdges().forEach(function(F){e.contains(F)&&!k.contains(F)&&G.merge(F)})}),G},I=[M(S),M(P)],L={cut:k,components:I,partition1:S,partition2:P};return L}},id=function(e){return{x:e.x,y:e.y}},Ln=function(e,t,a){return{x:e.x*t+a.x,y:e.y*t+a.y}},fv=function(e,t,a){return{x:(e.x-a.x)/t,y:(e.y-a.y)/t}},_t=function(e){return{x:e[0],y:e[1]}},sd=function(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=1/0,i=t;i<a;i++){var s=e[i];isFinite(s)&&(n=Math.min(s,n))}return n},od=function(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=-1/0,i=t;i<a;i++){var s=e[i];isFinite(s)&&(n=Math.max(s,n))}return n},ud=function(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=0,i=0,s=t;s<a;s++){var o=e[s];isFinite(o)&&(n+=o,i++)}return n/i},ld=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0;n?e=e.slice(t,a):(a<e.length&&e.splice(a,e.length-a),t>0&&e.splice(0,t));for(var o=0,l=e.length-1;l>=0;l--){var u=e[l];s?isFinite(u)||(e[l]=-1/0,o++):e.splice(l,1)}i&&e.sort(function(c,h){return c-h});var v=e.length,f=Math.floor(v/2);return v%2!==0?e[f+1+o]:(e[f-1+o]+e[f+o])/2},vd=function(e){return Math.PI*e/180},Ka=function(e,t){return Math.atan2(t,e)-Math.PI/2},Js=Math.log2||function(r){return Math.log(r)/Math.log(2)},js=function(e){return e>0?1:e<0?-1:0},St=function(e,t){return Math.sqrt(mt(e,t))},mt=function(e,t){var a=t.x-e.x,n=t.y-e.y;return a*a+n*n},fd=function(e){for(var t=e.length,a=0,n=0;n<t;n++)a+=e[n];for(var i=0;i<t;i++)e[i]=e[i]/a;return e},nr=function(e,t,a,n){return(1-n)*(1-n)*e+2*(1-n)*n*t+n*n*a},Wt=function(e,t,a,n){return{x:nr(e.x,t.x,a.x,n),y:nr(e.y,t.y,a.y,n)}},cd=function(e,t,a,n){var i={x:t.x-e.x,y:t.y-e.y},s=St(e,t),o={x:i.x/s,y:i.y/s};return a=a??0,n=n??a*s,{x:e.x+o.x*n,y:e.y+o.y*n}},Ca=function(e,t,a){return Math.max(e,Math.min(a,t))},Sr=function(e){if(e==null)return{x1:1/0,y1:1/0,x2:-1/0,y2:-1/0,w:0,h:0};if(e.x1!=null&&e.y1!=null){if(e.x2!=null&&e.y2!=null&&e.x2>=e.x1&&e.y2>=e.y1)return{x1:e.x1,y1:e.y1,x2:e.x2,y2:e.y2,w:e.x2-e.x1,h:e.y2-e.y1};if(e.w!=null&&e.h!=null&&e.w>=0&&e.h>=0)return{x1:e.x1,y1:e.y1,x2:e.x1+e.w,y2:e.y1+e.h,w:e.w,h:e.h}}},dd=function(e){return{x1:e.x1,x2:e.x2,w:e.w,y1:e.y1,y2:e.y2,h:e.h}},hd=function(e){e.x1=1/0,e.y1=1/0,e.x2=-1/0,e.y2=-1/0,e.w=0,e.h=0},gd=function(e,t,a){return{x1:e.x1+t,x2:e.x2+t,y1:e.y1+a,y2:e.y2+a,w:e.w,h:e.h}},cv=function(e,t){e.x1=Math.min(e.x1,t.x1),e.x2=Math.max(e.x2,t.x2),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,t.y1),e.y2=Math.max(e.y2,t.y2),e.h=e.y2-e.y1},pd=function(e,t,a){e.x1=Math.min(e.x1,t),e.x2=Math.max(e.x2,t),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,a),e.y2=Math.max(e.y2,a),e.h=e.y2-e.y1},sn=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return e.x1-=t,e.x2+=t,e.y1-=t,e.y2+=t,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},on=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[0],a,n,i,s;if(t.length===1)a=n=i=s=t[0];else if(t.length===2)a=i=t[0],s=n=t[1];else if(t.length===4){var o=je(t,4);a=o[0],n=o[1],i=o[2],s=o[3]}return e.x1-=s,e.x2+=n,e.y1-=a,e.y2+=i,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},Ho=function(e,t){e.x1=t.x1,e.y1=t.y1,e.x2=t.x2,e.y2=t.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1},eo=function(e,t){return!(e.x1>t.x2||t.x1>e.x2||e.x2<t.x1||t.x2<e.x1||e.y2<t.y1||t.y2<e.y1||e.y1>t.y2||t.y1>e.y2)},Xt=function(e,t,a){return e.x1<=t&&t<=e.x2&&e.y1<=a&&a<=e.y2},yd=function(e,t){return Xt(e,t.x,t.y)},md=function(e,t){return Xt(e,t.x1,t.y1)&&Xt(e,t.x2,t.y2)},dv=function(e,t,a,n,i,s,o){var l=arguments.length>7&&arguments[7]!==void 0?arguments[7]:"auto",u=l==="auto"?st(i,s):l,v=i/2,f=s/2;u=Math.min(u,v,f);var c=u!==v,h=u!==f,d;if(c){var y=a-v+u-o,g=n-f-o,p=a+v-u+o,m=g;if(d=rt(e,t,a,n,y,g,p,m,!1),d.length>0)return d}if(h){var b=a+v+o,w=n-f+u-o,E=b,C=n+f-u+o;if(d=rt(e,t,a,n,b,w,E,C,!1),d.length>0)return d}if(c){var x=a-v+u-o,k=n+f+o,S=a+v-u+o,P=k;if(d=rt(e,t,a,n,x,k,S,P,!1),d.length>0)return d}if(h){var D=a-v-o,A=n-f+u-o,B=D,R=n+f-u+o;if(d=rt(e,t,a,n,D,A,B,R,!1),d.length>0)return d}var M;{var I=a-v+u,L=n-f+u;if(M=ha(e,t,a,n,I,L,u+o),M.length>0&&M[0]<=I&&M[1]<=L)return[M[0],M[1]]}{var O=a+v-u,V=n-f+u;if(M=ha(e,t,a,n,O,V,u+o),M.length>0&&M[0]>=O&&M[1]<=V)return[M[0],M[1]]}{var G=a+v-u,N=n+f-u;if(M=ha(e,t,a,n,G,N,u+o),M.length>0&&M[0]>=G&&M[1]>=N)return[M[0],M[1]]}{var F=a-v+u,K=n+f-u;if(M=ha(e,t,a,n,F,K,u+o),M.length>0&&M[0]<=F&&M[1]>=K)return[M[0],M[1]]}return[]},bd=function(e,t,a,n,i,s,o){var l=o,u=Math.min(a,i),v=Math.max(a,i),f=Math.min(n,s),c=Math.max(n,s);return u-l<=e&&e<=v+l&&f-l<=t&&t<=c+l},wd=function(e,t,a,n,i,s,o,l,u){var v={x1:Math.min(a,o,i)-u,x2:Math.max(a,o,i)+u,y1:Math.min(n,l,s)-u,y2:Math.max(n,l,s)+u};return!(e<v.x1||e>v.x2||t<v.y1||t>v.y2)},xd=function(e,t,a,n){a-=n;var i=t*t-4*e*a;if(i<0)return[];var s=Math.sqrt(i),o=2*e,l=(-t+s)/o,u=(-t-s)/o;return[l,u]},Ed=function(e,t,a,n,i){var s=1e-5;e===0&&(e=s),t/=e,a/=e,n/=e;var o,l,u,v,f,c,h,d;if(l=(3*a-t*t)/9,u=-(27*n)+t*(9*a-2*(t*t)),u/=54,o=l*l*l+u*u,i[1]=0,h=t/3,o>0){f=u+Math.sqrt(o),f=f<0?-Math.pow(-f,1/3):Math.pow(f,1/3),c=u-Math.sqrt(o),c=c<0?-Math.pow(-c,1/3):Math.pow(c,1/3),i[0]=-h+f+c,h+=(f+c)/2,i[4]=i[2]=-h,h=Math.sqrt(3)*(-c+f)/2,i[3]=h,i[5]=-h;return}if(i[5]=i[3]=0,o===0){d=u<0?-Math.pow(-u,1/3):Math.pow(u,1/3),i[0]=-h+2*d,i[4]=i[2]=-(d+h);return}l=-l,v=l*l*l,v=Math.acos(u/Math.sqrt(v)),d=2*Math.sqrt(l),i[0]=-h+d*Math.cos(v/3),i[2]=-h+d*Math.cos((v+2*Math.PI)/3),i[4]=-h+d*Math.cos((v+4*Math.PI)/3)},Cd=function(e,t,a,n,i,s,o,l){var u=1*a*a-4*a*i+2*a*o+4*i*i-4*i*o+o*o+n*n-4*n*s+2*n*l+4*s*s-4*s*l+l*l,v=1*9*a*i-3*a*a-3*a*o-6*i*i+3*i*o+9*n*s-3*n*n-3*n*l-6*s*s+3*s*l,f=1*3*a*a-6*a*i+a*o-a*e+2*i*i+2*i*e-o*e+3*n*n-6*n*s+n*l-n*t+2*s*s+2*s*t-l*t,c=1*a*i-a*a+a*e-i*e+n*s-n*n+n*t-s*t,h=[];Ed(u,v,f,c,h);for(var d=1e-7,y=[],g=0;g<6;g+=2)Math.abs(h[g+1])<d&&h[g]>=0&&h[g]<=1&&y.push(h[g]);y.push(1),y.push(0);for(var p=-1,m,b,w,E=0;E<y.length;E++)m=Math.pow(1-y[E],2)*a+2*(1-y[E])*y[E]*i+y[E]*y[E]*o,b=Math.pow(1-y[E],2)*n+2*(1-y[E])*y[E]*s+y[E]*y[E]*l,w=Math.pow(m-e,2)+Math.pow(b-t,2),p>=0?w<p&&(p=w):p=w;return p},Td=function(e,t,a,n,i,s){var o=[e-a,t-n],l=[i-a,s-n],u=l[0]*l[0]+l[1]*l[1],v=o[0]*o[0]+o[1]*o[1],f=o[0]*l[0]+o[1]*l[1],c=f*f/u;return f<0?v:c>u?(e-i)*(e-i)+(t-s)*(t-s):v-c},Cr=function(e,t,a){for(var n,i,s,o,l,u=0,v=0;v<a.length/2;v++)if(n=a[v*2],i=a[v*2+1],v+1<a.length/2?(s=a[(v+1)*2],o=a[(v+1)*2+1]):(s=a[(v+1-a.length/2)*2],o=a[(v+1-a.length/2)*2+1]),!(n==e&&s==e))if(n>=e&&e>=s||n<=e&&e<=s)l=(e-n)/(s-n)*(o-i)+i,l>t&&u++;else continue;return u%2!==0},Xr=function(e,t,a,n,i,s,o,l,u){var v=new Array(a.length),f;l[0]!=null?(f=Math.atan(l[1]/l[0]),l[0]<0?f=f+Math.PI/2:f=-f-Math.PI/2):f=l;for(var c=Math.cos(-f),h=Math.sin(-f),d=0;d<v.length/2;d++)v[d*2]=s/2*(a[d*2]*c-a[d*2+1]*h),v[d*2+1]=o/2*(a[d*2+1]*c+a[d*2]*h),v[d*2]+=n,v[d*2+1]+=i;var y;if(u>0){var g=xn(v,-u);y=wn(g)}else y=v;return Cr(e,t,y)},Sd=function(e,t,a,n,i,s,o,l){for(var u=new Array(a.length*2),v=0;v<l.length;v++){var f=l[v];u[v*4+0]=f.startX,u[v*4+1]=f.startY,u[v*4+2]=f.stopX,u[v*4+3]=f.stopY;var c=Math.pow(f.cx-e,2)+Math.pow(f.cy-t,2);if(c<=Math.pow(f.radius,2))return!0}return Cr(e,t,u)},wn=function(e){for(var t=new Array(e.length/2),a,n,i,s,o,l,u,v,f=0;f<e.length/4;f++){a=e[f*4],n=e[f*4+1],i=e[f*4+2],s=e[f*4+3],f<e.length/4-1?(o=e[(f+1)*4],l=e[(f+1)*4+1],u=e[(f+1)*4+2],v=e[(f+1)*4+3]):(o=e[0],l=e[1],u=e[2],v=e[3]);var c=rt(a,n,i,s,o,l,u,v,!0);t[f*2]=c[0],t[f*2+1]=c[1]}return t},xn=function(e,t){for(var a=new Array(e.length*2),n,i,s,o,l=0;l<e.length/2;l++){n=e[l*2],i=e[l*2+1],l<e.length/2-1?(s=e[(l+1)*2],o=e[(l+1)*2+1]):(s=e[0],o=e[1]);var u=o-i,v=-(s-n),f=Math.sqrt(u*u+v*v),c=u/f,h=v/f;a[l*4]=n+c*t,a[l*4+1]=i+h*t,a[l*4+2]=s+c*t,a[l*4+3]=o+h*t}return a},Dd=function(e,t,a,n,i,s){var o=a-e,l=n-t;o/=i,l/=s;var u=Math.sqrt(o*o+l*l),v=u-1;if(v<0)return[];var f=v/u;return[(a-e)*f+e,(n-t)*f+t]},Ct=function(e,t,a,n,i,s,o){return e-=i,t-=s,e/=a/2+o,t/=n/2+o,e*e+t*t<=1},ha=function(e,t,a,n,i,s,o){var l=[a-e,n-t],u=[e-i,t-s],v=l[0]*l[0]+l[1]*l[1],f=2*(u[0]*l[0]+u[1]*l[1]),c=u[0]*u[0]+u[1]*u[1]-o*o,h=f*f-4*v*c;if(h<0)return[];var d=(-f+Math.sqrt(h))/(2*v),y=(-f-Math.sqrt(h))/(2*v),g=Math.min(d,y),p=Math.max(d,y),m=[];if(g>=0&&g<=1&&m.push(g),p>=0&&p<=1&&m.push(p),m.length===0)return[];var b=m[0]*l[0]+e,w=m[0]*l[1]+t;if(m.length>1){if(m[0]==m[1])return[b,w];var E=m[1]*l[0]+e,C=m[1]*l[1]+t;return[b,w,E,C]}else return[b,w]},di=function(e,t,a){return t<=e&&e<=a||a<=e&&e<=t?e:e<=t&&t<=a||a<=t&&t<=e?t:a},rt=function(e,t,a,n,i,s,o,l,u){var v=e-i,f=a-e,c=o-i,h=t-s,d=n-t,y=l-s,g=c*h-y*v,p=f*h-d*v,m=y*f-c*d;if(m!==0){var b=g/m,w=p/m,E=.001,C=0-E,x=1+E;return C<=b&&b<=x&&C<=w&&w<=x?[e+b*f,t+b*d]:u?[e+b*f,t+b*d]:[]}else return g===0||p===0?di(e,a,o)===o?[o,l]:di(e,a,i)===i?[i,s]:di(i,o,a)===a?[a,n]:[]:[]},Ta=function(e,t,a,n,i,s,o,l){var u=[],v,f=new Array(a.length),c=!0;s==null&&(c=!1);var h;if(c){for(var d=0;d<f.length/2;d++)f[d*2]=a[d*2]*s+n,f[d*2+1]=a[d*2+1]*o+i;if(l>0){var y=xn(f,-l);h=wn(y)}else h=f}else h=a;for(var g,p,m,b,w=0;w<h.length/2;w++)g=h[w*2],p=h[w*2+1],w<h.length/2-1?(m=h[(w+1)*2],b=h[(w+1)*2+1]):(m=h[0],b=h[1]),v=rt(e,t,n,i,g,p,m,b),v.length!==0&&u.push(v[0],v[1]);return u},kd=function(e,t,a,n,i,s,o,l,u){var v=[],f,c=new Array(a.length*2);u.forEach(function(m,b){b===0?(c[c.length-2]=m.startX,c[c.length-1]=m.startY):(c[b*4-2]=m.startX,c[b*4-1]=m.startY),c[b*4]=m.stopX,c[b*4+1]=m.stopY,f=ha(e,t,n,i,m.cx,m.cy,m.radius),f.length!==0&&v.push(f[0],f[1])});for(var h=0;h<c.length/4;h++)f=rt(e,t,n,i,c[h*4],c[h*4+1],c[h*4+2],c[h*4+3],!1),f.length!==0&&v.push(f[0],f[1]);if(v.length>2){for(var d=[v[0],v[1]],y=Math.pow(d[0]-e,2)+Math.pow(d[1]-t,2),g=1;g<v.length/2;g++){var p=Math.pow(v[g*2]-e,2)+Math.pow(v[g*2+1]-t,2);p<=y&&(d[0]=v[g*2],d[1]=v[g*2+1],y=p)}return d}return v},Ya=function(e,t,a){var n=[e[0]-t[0],e[1]-t[1]],i=Math.sqrt(n[0]*n[0]+n[1]*n[1]),s=(i-a)/i;return s<0&&(s=1e-5),[t[0]+s*n[0],t[1]+s*n[1]]},yr=function(e,t){var a=Ds(e,t);return a=hv(a),a},hv=function(e){for(var t,a,n=e.length/2,i=1/0,s=1/0,o=-1/0,l=-1/0,u=0;u<n;u++)t=e[2*u],a=e[2*u+1],i=Math.min(i,t),o=Math.max(o,t),s=Math.min(s,a),l=Math.max(l,a);for(var v=2/(o-i),f=2/(l-s),c=0;c<n;c++)t=e[2*c]=e[2*c]*v,a=e[2*c+1]=e[2*c+1]*f,i=Math.min(i,t),o=Math.max(o,t),s=Math.min(s,a),l=Math.max(l,a);if(s<-1)for(var h=0;h<n;h++)a=e[2*h+1]=e[2*h+1]+(-1-s);return e},Ds=function(e,t){var a=1/e*2*Math.PI,n=e%2===0?Math.PI/2+a/2:Math.PI/2;n+=t;for(var i=new Array(e*2),s,o=0;o<e;o++)s=o*a+n,i[2*o]=Math.cos(s),i[2*o+1]=Math.sin(-s);return i},st=function(e,t){return Math.min(e/4,t/4,8)},gv=function(e,t){return Math.min(e/10,t/10,8)},ro=function(){return 8},Bd=function(e,t,a){return[e-2*t+a,2*(t-e),e]},ks=function(e,t){return{heightOffset:Math.min(15,.05*t),widthOffset:Math.min(100,.25*e),ctrlPtOffsetPct:.05}};function Pd(r,e){function t(f){for(var c=[],h=0;h<f.length;h++){var d=f[h],y=f[(h+1)%f.length],g={x:y.x-d.x,y:y.y-d.y},p={x:-g.y,y:g.x},m=Math.sqrt(p.x*p.x+p.y*p.y);c.push({x:p.x/m,y:p.y/m})}return c}function a(f,c){var h=1/0,d=-1/0,y=Tr(f),g;try{for(y.s();!(g=y.n()).done;){var p=g.value,m=p.x*c.x+p.y*c.y;h=Math.min(h,m),d=Math.max(d,m)}}catch(b){y.e(b)}finally{y.f()}return{min:h,max:d}}function n(f,c){return!(f.max<c.min||c.max<f.min)}var i=[].concat(pn(t(r)),pn(t(e))),s=Tr(i),o;try{for(s.s();!(o=s.n()).done;){var l=o.value,u=a(r,l),v=a(e,l);if(!n(u,v))return!1}}catch(f){s.e(f)}finally{s.f()}return!0}var Ad=fr({dampingFactor:.8,precision:1e-6,iterations:200,weight:function(e){return 1}}),Rd={pageRank:function(e){for(var t=Ad(e),a=t.dampingFactor,n=t.precision,i=t.iterations,s=t.weight,o=this._private.cy,l=this.byGroup(),u=l.nodes,v=l.edges,f=u.length,c=f*f,h=v.length,d=new Array(c),y=new Array(f),g=(1-a)/f,p=0;p<f;p++){for(var m=0;m<f;m++){var b=p*f+m;d[b]=0}y[p]=0}for(var w=0;w<h;w++){var E=v[w],C=E.data("source"),x=E.data("target");if(C!==x){var k=u.indexOfId(C),S=u.indexOfId(x),P=s(E),D=S*f+k;d[D]+=P,y[k]+=P}}for(var A=1/f+g,B=0;B<f;B++)if(y[B]===0)for(var R=0;R<f;R++){var M=R*f+B;d[M]=A}else for(var I=0;I<f;I++){var L=I*f+B;d[L]=d[L]/y[B]+g}for(var O=new Array(f),V=new Array(f),G,N=0;N<f;N++)O[N]=1;for(var F=0;F<i;F++){for(var K=0;K<f;K++)V[K]=0;for(var X=0;X<f;X++)for(var Q=0;Q<f;Q++){var Z=X*f+Q;V[X]+=d[Z]*O[Q]}fd(V),G=O,O=V,V=G;for(var re=0,ae=0;ae<f;ae++){var J=G[ae]-O[ae];re+=J*J}if(re<n)break}var z={rank:function(H){return H=o.collection(H)[0],O[u.indexOf(H)]}};return z}},Wo=fr({root:null,weight:function(e){return 1},directed:!1,alpha:0}),Ut={degreeCentralityNormalized:function(e){e=Wo(e);var t=this.cy(),a=this.nodes(),n=a.length;if(e.directed){for(var v={},f={},c=0,h=0,d=0;d<n;d++){var y=a[d],g=y.id();e.root=y;var p=this.degreeCentrality(e);c<p.indegree&&(c=p.indegree),h<p.outdegree&&(h=p.outdegree),v[g]=p.indegree,f[g]=p.outdegree}return{indegree:function(b){return c==0?0:(fe(b)&&(b=t.filter(b)),v[b.id()]/c)},outdegree:function(b){return h===0?0:(fe(b)&&(b=t.filter(b)),f[b.id()]/h)}}}else{for(var i={},s=0,o=0;o<n;o++){var l=a[o];e.root=l;var u=this.degreeCentrality(e);s<u.degree&&(s=u.degree),i[l.id()]=u.degree}return{degree:function(b){return s===0?0:(fe(b)&&(b=t.filter(b)),i[b.id()]/s)}}}},degreeCentrality:function(e){e=Wo(e);var t=this.cy(),a=this,n=e,i=n.root,s=n.weight,o=n.directed,l=n.alpha;if(i=t.collection(i)[0],o){for(var h=i.connectedEdges(),d=h.filter(function(C){return C.target().same(i)&&a.has(C)}),y=h.filter(function(C){return C.source().same(i)&&a.has(C)}),g=d.length,p=y.length,m=0,b=0,w=0;w<d.length;w++)m+=s(d[w]);for(var E=0;E<y.length;E++)b+=s(y[E]);return{indegree:Math.pow(g,1-l)*Math.pow(m,l),outdegree:Math.pow(p,1-l)*Math.pow(b,l)}}else{for(var u=i.connectedEdges().intersection(a),v=u.length,f=0,c=0;c<u.length;c++)f+=s(u[c]);return{degree:Math.pow(v,1-l)*Math.pow(f,l)}}}};Ut.dc=Ut.degreeCentrality;Ut.dcn=Ut.degreeCentralityNormalised=Ut.degreeCentralityNormalized;var Uo=fr({harmonic:!0,weight:function(){return 1},directed:!1,root:null}),$t={closenessCentralityNormalized:function(e){for(var t=Uo(e),a=t.harmonic,n=t.weight,i=t.directed,s=this.cy(),o={},l=0,u=this.nodes(),v=this.floydWarshall({weight:n,directed:i}),f=0;f<u.length;f++){for(var c=0,h=u[f],d=0;d<u.length;d++)if(f!==d){var y=v.distance(h,u[d]);a?c+=1/y:c+=y}a||(c=1/c),l<c&&(l=c),o[h.id()]=c}return{closeness:function(p){return l==0?0:(fe(p)?p=s.filter(p)[0].id():p=p.id(),o[p]/l)}}},closenessCentrality:function(e){var t=Uo(e),a=t.root,n=t.weight,i=t.directed,s=t.harmonic;a=this.filter(a)[0];for(var o=this.dijkstra({root:a,weight:n,directed:i}),l=0,u=this.nodes(),v=0;v<u.length;v++){var f=u[v];if(!f.same(a)){var c=o.distanceTo(f);s?l+=1/c:l+=c}}return s?l:1/l}};$t.cc=$t.closenessCentrality;$t.ccn=$t.closenessCentralityNormalised=$t.closenessCentralityNormalized;var Md=fr({weight:null,directed:!1}),Bs={betweennessCentrality:function(e){for(var t=Md(e),a=t.directed,n=t.weight,i=n!=null,s=this.cy(),o=this.nodes(),l={},u={},v=0,f={set:function(b,w){u[b]=w,w>v&&(v=w)},get:function(b){return u[b]}},c=0;c<o.length;c++){var h=o[c],d=h.id();a?l[d]=h.outgoers().nodes():l[d]=h.openNeighborhood().nodes(),f.set(d,0)}for(var y=function(){for(var b=o[g].id(),w=[],E={},C={},x={},k=new Na(function(X,Q){return x[X]-x[Q]}),S=0;S<o.length;S++){var P=o[S].id();E[P]=[],C[P]=0,x[P]=1/0}for(C[b]=1,x[b]=0,k.push(b);!k.empty();){var D=k.pop();if(w.push(D),i)for(var A=0;A<l[D].length;A++){var B=l[D][A],R=s.getElementById(D),M=void 0;R.edgesTo(B).length>0?M=R.edgesTo(B)[0]:M=B.edgesTo(R)[0];var I=n(M);B=B.id(),x[B]>x[D]+I&&(x[B]=x[D]+I,k.nodes.indexOf(B)<0?k.push(B):k.updateItem(B),C[B]=0,E[B]=[]),x[B]==x[D]+I&&(C[B]=C[B]+C[D],E[B].push(D))}else for(var L=0;L<l[D].length;L++){var O=l[D][L].id();x[O]==1/0&&(k.push(O),x[O]=x[D]+1),x[O]==x[D]+1&&(C[O]=C[O]+C[D],E[O].push(D))}}for(var V={},G=0;G<o.length;G++)V[o[G].id()]=0;for(;w.length>0;){for(var N=w.pop(),F=0;F<E[N].length;F++){var K=E[N][F];V[K]=V[K]+C[K]/C[N]*(1+V[N])}N!=o[g].id()&&f.set(N,f.get(N)+V[N])}},g=0;g<o.length;g++)y();var p={betweenness:function(b){var w=s.collection(b).id();return f.get(w)},betweennessNormalized:function(b){if(v==0)return 0;var w=s.collection(b).id();return f.get(w)/v}};return p.betweennessNormalised=p.betweennessNormalized,p}};Bs.bc=Bs.betweennessCentrality;var Ld=fr({expandFactor:2,inflateFactor:2,multFactor:1,maxIterations:20,attributes:[function(r){return 1}]}),Id=function(e){return Ld(e)},Od=function(e,t){for(var a=0,n=0;n<t.length;n++)a+=t[n](e);return a},Nd=function(e,t,a){for(var n=0;n<t;n++)e[n*t+n]=a},pv=function(e,t){for(var a,n=0;n<t;n++){a=0;for(var i=0;i<t;i++)a+=e[i*t+n];for(var s=0;s<t;s++)e[s*t+n]=e[s*t+n]/a}},zd=function(e,t,a){for(var n=new Array(a*a),i=0;i<a;i++){for(var s=0;s<a;s++)n[i*a+s]=0;for(var o=0;o<a;o++)for(var l=0;l<a;l++)n[i*a+l]+=e[i*a+o]*t[o*a+l]}return n},Fd=function(e,t,a){for(var n=e.slice(0),i=1;i<a;i++)e=zd(e,n,t);return e},Vd=function(e,t,a){for(var n=new Array(t*t),i=0;i<t*t;i++)n[i]=Math.pow(e[i],a);return pv(n,t),n},qd=function(e,t,a,n){for(var i=0;i<a;i++){var s=Math.round(e[i]*Math.pow(10,n))/Math.pow(10,n),o=Math.round(t[i]*Math.pow(10,n))/Math.pow(10,n);if(s!==o)return!1}return!0},_d=function(e,t,a,n){for(var i=[],s=0;s<t;s++){for(var o=[],l=0;l<t;l++)Math.round(e[s*t+l]*1e3)/1e3>0&&o.push(a[l]);o.length!==0&&i.push(n.collection(o))}return i},Gd=function(e,t){for(var a=0;a<e.length;a++)if(!t[a]||e[a].id()!==t[a].id())return!1;return!0},Hd=function(e){for(var t=0;t<e.length;t++)for(var a=0;a<e.length;a++)t!=a&&Gd(e[t],e[a])&&e.splice(a,1);return e},$o=function(e){for(var t=this.nodes(),a=this.edges(),n=this.cy(),i=Id(e),s={},o=0;o<t.length;o++)s[t[o].id()]=o;for(var l=t.length,u=l*l,v=new Array(u),f,c=0;c<u;c++)v[c]=0;for(var h=0;h<a.length;h++){var d=a[h],y=s[d.source().id()],g=s[d.target().id()],p=Od(d,i.attributes);v[y*l+g]+=p,v[g*l+y]+=p}Nd(v,l,i.multFactor),pv(v,l);for(var m=!0,b=0;m&&b<i.maxIterations;)m=!1,f=Fd(v,l,i.expandFactor),v=Vd(f,l,i.inflateFactor),qd(v,f,u,4)||(m=!0),b++;var w=_d(v,l,t,n);return w=Hd(w),w},Wd={markovClustering:$o,mcl:$o},Ud=function(e){return e},yv=function(e,t){return Math.abs(t-e)},Ko=function(e,t,a){return e+yv(t,a)},Yo=function(e,t,a){return e+Math.pow(a-t,2)},$d=function(e){return Math.sqrt(e)},Kd=function(e,t,a){return Math.max(e,yv(t,a))},oa=function(e,t,a,n,i){for(var s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:Ud,o=n,l,u,v=0;v<e;v++)l=t(v),u=a(v),o=i(o,l,u);return s(o)},Zt={euclidean:function(e,t,a){return e>=2?oa(e,t,a,0,Yo,$d):oa(e,t,a,0,Ko)},squaredEuclidean:function(e,t,a){return oa(e,t,a,0,Yo)},manhattan:function(e,t,a){return oa(e,t,a,0,Ko)},max:function(e,t,a){return oa(e,t,a,-1/0,Kd)}};Zt["squared-euclidean"]=Zt.squaredEuclidean;Zt.squaredeuclidean=Zt.squaredEuclidean;function In(r,e,t,a,n,i){var s;return We(r)?s=r:s=Zt[r]||Zt.euclidean,e===0&&We(r)?s(n,i):s(e,t,a,n,i)}var Yd=fr({k:2,m:2,sensitivityThreshold:1e-4,distance:"euclidean",maxIterations:10,attributes:[],testMode:!1,testCentroids:null}),to=function(e){return Yd(e)},En=function(e,t,a,n,i){var s=i!=="kMedoids",o=s?function(f){return a[f]}:function(f){return n[f](a)},l=function(c){return n[c](t)},u=a,v=t;return In(e,n.length,o,l,u,v)},hi=function(e,t,a){for(var n=a.length,i=new Array(n),s=new Array(n),o=new Array(t),l=null,u=0;u<n;u++)i[u]=e.min(a[u]).value,s[u]=e.max(a[u]).value;for(var v=0;v<t;v++){l=[];for(var f=0;f<n;f++)l[f]=Math.random()*(s[f]-i[f])+i[f];o[v]=l}return o},mv=function(e,t,a,n,i){for(var s=1/0,o=0,l=0;l<t.length;l++){var u=En(a,e,t[l],n,i);u<s&&(s=u,o=l)}return o},bv=function(e,t,a){for(var n=[],i=null,s=0;s<t.length;s++)i=t[s],a[i.id()]===e&&n.push(i);return n},Xd=function(e,t,a){return Math.abs(t-e)<=a},Zd=function(e,t,a){for(var n=0;n<e.length;n++)for(var i=0;i<e[n].length;i++){var s=Math.abs(e[n][i]-t[n][i]);if(s>a)return!1}return!0},Qd=function(e,t,a){for(var n=0;n<a;n++)if(e===t[n])return!0;return!1},Xo=function(e,t){var a=new Array(t);if(e.length<50)for(var n=0;n<t;n++){for(var i=e[Math.floor(Math.random()*e.length)];Qd(i,a,n);)i=e[Math.floor(Math.random()*e.length)];a[n]=i}else for(var s=0;s<t;s++)a[s]=e[Math.floor(Math.random()*e.length)];return a},Zo=function(e,t,a){for(var n=0,i=0;i<t.length;i++)n+=En("manhattan",t[i],e,a,"kMedoids");return n},Jd=function(e){var t=this.cy(),a=this.nodes(),n=null,i=to(e),s=new Array(i.k),o={},l;i.testMode?typeof i.testCentroids=="number"?(i.testCentroids,l=hi(a,i.k,i.attributes)):rr(i.testCentroids)==="object"?l=i.testCentroids:l=hi(a,i.k,i.attributes):l=hi(a,i.k,i.attributes);for(var u=!0,v=0;u&&v<i.maxIterations;){for(var f=0;f<a.length;f++)n=a[f],o[n.id()]=mv(n,l,i.distance,i.attributes,"kMeans");u=!1;for(var c=0;c<i.k;c++){var h=bv(c,a,o);if(h.length!==0){for(var d=i.attributes.length,y=l[c],g=new Array(d),p=new Array(d),m=0;m<d;m++){p[m]=0;for(var b=0;b<h.length;b++)n=h[b],p[m]+=i.attributes[m](n);g[m]=p[m]/h.length,Xd(g[m],y[m],i.sensitivityThreshold)||(u=!0)}l[c]=g,s[c]=t.collection(h)}}v++}return s},jd=function(e){var t=this.cy(),a=this.nodes(),n=null,i=to(e),s=new Array(i.k),o,l={},u,v=new Array(i.k);i.testMode?typeof i.testCentroids=="number"||(rr(i.testCentroids)==="object"?o=i.testCentroids:o=Xo(a,i.k)):o=Xo(a,i.k);for(var f=!0,c=0;f&&c<i.maxIterations;){for(var h=0;h<a.length;h++)n=a[h],l[n.id()]=mv(n,o,i.distance,i.attributes,"kMedoids");f=!1;for(var d=0;d<o.length;d++){var y=bv(d,a,l);if(y.length!==0){v[d]=Zo(o[d],y,i.attributes);for(var g=0;g<y.length;g++)u=Zo(y[g],y,i.attributes),u<v[d]&&(v[d]=u,o[d]=y[g],f=!0);s[d]=t.collection(y)}}c++}return s},eh=function(e,t,a,n,i){for(var s,o,l=0;l<t.length;l++)for(var u=0;u<e.length;u++)n[l][u]=Math.pow(a[l][u],i.m);for(var v=0;v<e.length;v++)for(var f=0;f<i.attributes.length;f++){s=0,o=0;for(var c=0;c<t.length;c++)s+=n[c][v]*i.attributes[f](t[c]),o+=n[c][v];e[v][f]=s/o}},rh=function(e,t,a,n,i){for(var s=0;s<e.length;s++)t[s]=e[s].slice();for(var o,l,u,v=2/(i.m-1),f=0;f<a.length;f++)for(var c=0;c<n.length;c++){o=0;for(var h=0;h<a.length;h++)l=En(i.distance,n[c],a[f],i.attributes,"cmeans"),u=En(i.distance,n[c],a[h],i.attributes,"cmeans"),o+=Math.pow(l/u,v);e[c][f]=1/o}},th=function(e,t,a,n){for(var i=new Array(a.k),s=0;s<i.length;s++)i[s]=[];for(var o,l,u=0;u<t.length;u++){o=-1/0,l=-1;for(var v=0;v<t[0].length;v++)t[u][v]>o&&(o=t[u][v],l=v);i[l].push(e[u])}for(var f=0;f<i.length;f++)i[f]=n.collection(i[f]);return i},Qo=function(e){var t=this.cy(),a=this.nodes(),n=to(e),i,s,o,l,u;l=new Array(a.length);for(var v=0;v<a.length;v++)l[v]=new Array(n.k);o=new Array(a.length);for(var f=0;f<a.length;f++)o[f]=new Array(n.k);for(var c=0;c<a.length;c++){for(var h=0,d=0;d<n.k;d++)o[c][d]=Math.random(),h+=o[c][d];for(var y=0;y<n.k;y++)o[c][y]=o[c][y]/h}s=new Array(n.k);for(var g=0;g<n.k;g++)s[g]=new Array(n.attributes.length);u=new Array(a.length);for(var p=0;p<a.length;p++)u[p]=new Array(n.k);for(var m=!0,b=0;m&&b<n.maxIterations;)m=!1,eh(s,a,o,u,n),rh(o,l,s,a,n),Zd(o,l,n.sensitivityThreshold)||(m=!0),b++;return i=th(a,o,n,t),{clusters:i,degreeOfMembership:o}},ah={kMeans:Jd,kMedoids:jd,fuzzyCMeans:Qo,fcm:Qo},nh=fr({distance:"euclidean",linkage:"min",mode:"threshold",threshold:1/0,addDendrogram:!1,dendrogramDepth:0,attributes:[]}),ih={single:"min",complete:"max"},sh=function(e){var t=nh(e),a=ih[t.linkage];return a!=null&&(t.linkage=a),t},Jo=function(e,t,a,n,i){for(var s=0,o=1/0,l,u=i.attributes,v=function(S,P){return In(i.distance,u.length,function(D){return u[D](S)},function(D){return u[D](P)},S,P)},f=0;f<e.length;f++){var c=e[f].key,h=a[c][n[c]];h<o&&(s=c,o=h)}if(i.mode==="threshold"&&o>=i.threshold||i.mode==="dendrogram"&&e.length===1)return!1;var d=t[s],y=t[n[s]],g;i.mode==="dendrogram"?g={left:d,right:y,key:d.key}:g={value:d.value.concat(y.value),key:d.key},e[d.index]=g,e.splice(y.index,1),t[d.key]=g;for(var p=0;p<e.length;p++){var m=e[p];d.key===m.key?l=1/0:i.linkage==="min"?(l=a[d.key][m.key],a[d.key][m.key]>a[y.key][m.key]&&(l=a[y.key][m.key])):i.linkage==="max"?(l=a[d.key][m.key],a[d.key][m.key]<a[y.key][m.key]&&(l=a[y.key][m.key])):i.linkage==="mean"?l=(a[d.key][m.key]*d.size+a[y.key][m.key]*y.size)/(d.size+y.size):i.mode==="dendrogram"?l=v(m.value,d.value):l=v(m.value[0],d.value[0]),a[d.key][m.key]=a[m.key][d.key]=l}for(var b=0;b<e.length;b++){var w=e[b].key;if(n[w]===d.key||n[w]===y.key){for(var E=w,C=0;C<e.length;C++){var x=e[C].key;a[w][x]<a[w][E]&&(E=x)}n[w]=E}e[b].index=b}return d.key=y.key=d.index=y.index=null,!0},Gt=function(e,t,a){e&&(e.value?t.push(e.value):(e.left&&Gt(e.left,t),e.right&&Gt(e.right,t)))},Ps=function(e,t){if(!e)return"";if(e.left&&e.right){var a=Ps(e.left,t),n=Ps(e.right,t),i=t.add({group:"nodes",data:{id:a+","+n}});return t.add({group:"edges",data:{source:a,target:i.id()}}),t.add({group:"edges",data:{source:n,target:i.id()}}),i.id()}else if(e.value)return e.value.id()},As=function(e,t,a){if(!e)return[];var n=[],i=[],s=[];return t===0?(e.left&&Gt(e.left,n),e.right&&Gt(e.right,i),s=n.concat(i),[a.collection(s)]):t===1?e.value?[a.collection(e.value)]:(e.left&&Gt(e.left,n),e.right&&Gt(e.right,i),[a.collection(n),a.collection(i)]):e.value?[a.collection(e.value)]:(e.left&&(n=As(e.left,t-1,a)),e.right&&(i=As(e.right,t-1,a)),n.concat(i))},jo=function(e){for(var t=this.cy(),a=this.nodes(),n=sh(e),i=n.attributes,s=function(b,w){return In(n.distance,i.length,function(E){return i[E](b)},function(E){return i[E](w)},b,w)},o=[],l=[],u=[],v=[],f=0;f<a.length;f++){var c={value:n.mode==="dendrogram"?a[f]:[a[f]],key:f,index:f};o[f]=c,v[f]=c,l[f]=[],u[f]=0}for(var h=0;h<o.length;h++)for(var d=0;d<=h;d++){var y=void 0;n.mode==="dendrogram"?y=h===d?1/0:s(o[h].value,o[d].value):y=h===d?1/0:s(o[h].value[0],o[d].value[0]),l[h][d]=y,l[d][h]=y,y<l[h][u[h]]&&(u[h]=d)}for(var g=Jo(o,v,l,u,n);g;)g=Jo(o,v,l,u,n);var p;return n.mode==="dendrogram"?(p=As(o[0],n.dendrogramDepth,t),n.addDendrogram&&Ps(o[0],t)):(p=new Array(o.length),o.forEach(function(m,b){m.key=m.index=null,p[b]=t.collection(m.value)})),p},oh={hierarchicalClustering:jo,hca:jo},uh=fr({distance:"euclidean",preference:"median",damping:.8,maxIterations:1e3,minIterations:100,attributes:[]}),lh=function(e){var t=e.damping,a=e.preference;.5<=t&&t<1||He("Damping must range on [0.5, 1).  Got: ".concat(t));var n=["median","mean","min","max"];return n.some(function(i){return i===a})||te(a)||He("Preference must be one of [".concat(n.map(function(i){return"'".concat(i,"'")}).join(", "),"] or a number.  Got: ").concat(a)),uh(e)},vh=function(e,t,a,n){var i=function(o,l){return n[l](o)};return-In(e,n.length,function(s){return i(t,s)},function(s){return i(a,s)},t,a)},fh=function(e,t){var a=null;return t==="median"?a=ld(e):t==="mean"?a=ud(e):t==="min"?a=sd(e):t==="max"?a=od(e):a=t,a},ch=function(e,t,a){for(var n=[],i=0;i<e;i++)t[i*e+i]+a[i*e+i]>0&&n.push(i);return n},eu=function(e,t,a){for(var n=[],i=0;i<e;i++){for(var s=-1,o=-1/0,l=0;l<a.length;l++){var u=a[l];t[i*e+u]>o&&(s=u,o=t[i*e+u])}s>0&&n.push(s)}for(var v=0;v<a.length;v++)n[a[v]]=a[v];return n},dh=function(e,t,a){for(var n=eu(e,t,a),i=0;i<a.length;i++){for(var s=[],o=0;o<n.length;o++)n[o]===a[i]&&s.push(o);for(var l=-1,u=-1/0,v=0;v<s.length;v++){for(var f=0,c=0;c<s.length;c++)f+=t[s[c]*e+s[v]];f>u&&(l=v,u=f)}a[i]=s[l]}return n=eu(e,t,a),n},ru=function(e){for(var t=this.cy(),a=this.nodes(),n=lh(e),i={},s=0;s<a.length;s++)i[a[s].id()]=s;var o,l,u,v,f,c;o=a.length,l=o*o,u=new Array(l);for(var h=0;h<l;h++)u[h]=-1/0;for(var d=0;d<o;d++)for(var y=0;y<o;y++)d!==y&&(u[d*o+y]=vh(n.distance,a[d],a[y],n.attributes));v=fh(u,n.preference);for(var g=0;g<o;g++)u[g*o+g]=v;f=new Array(l);for(var p=0;p<l;p++)f[p]=0;c=new Array(l);for(var m=0;m<l;m++)c[m]=0;for(var b=new Array(o),w=new Array(o),E=new Array(o),C=0;C<o;C++)b[C]=0,w[C]=0,E[C]=0;for(var x=new Array(o*n.minIterations),k=0;k<x.length;k++)x[k]=0;var S;for(S=0;S<n.maxIterations;S++){for(var P=0;P<o;P++){for(var D=-1/0,A=-1/0,B=-1,R=0,M=0;M<o;M++)b[M]=f[P*o+M],R=c[P*o+M]+u[P*o+M],R>=D?(A=D,D=R,B=M):R>A&&(A=R);for(var I=0;I<o;I++)f[P*o+I]=(1-n.damping)*(u[P*o+I]-D)+n.damping*b[I];f[P*o+B]=(1-n.damping)*(u[P*o+B]-A)+n.damping*b[B]}for(var L=0;L<o;L++){for(var O=0,V=0;V<o;V++)b[V]=c[V*o+L],w[V]=Math.max(0,f[V*o+L]),O+=w[V];O-=w[L],w[L]=f[L*o+L],O+=w[L];for(var G=0;G<o;G++)c[G*o+L]=(1-n.damping)*Math.min(0,O-w[G])+n.damping*b[G];c[L*o+L]=(1-n.damping)*(O-w[L])+n.damping*b[L]}for(var N=0,F=0;F<o;F++){var K=c[F*o+F]+f[F*o+F]>0?1:0;x[S%n.minIterations*o+F]=K,N+=K}if(N>0&&(S>=n.minIterations-1||S==n.maxIterations-1)){for(var X=0,Q=0;Q<o;Q++){E[Q]=0;for(var Z=0;Z<n.minIterations;Z++)E[Q]+=x[Z*o+Q];(E[Q]===0||E[Q]===n.minIterations)&&X++}if(X===o)break}}for(var re=ch(o,f,c),ae=dh(o,u,re),J={},z=0;z<re.length;z++)J[re[z]]=[];for(var q=0;q<a.length;q++){var H=i[a[q].id()],ee=ae[H];ee!=null&&J[ee].push(a[q])}for(var ne=new Array(re.length),be=0;be<re.length;be++)ne[be]=t.collection(J[re[be]]);return ne},hh={affinityPropagation:ru,ap:ru},gh=fr({root:void 0,directed:!1}),ph={hierholzer:function(e){if(!Pe(e)){var t=arguments;e={root:t[0],directed:t[1]}}var a=gh(e),n=a.root,i=a.directed,s=this,o=!1,l,u,v;n&&(v=fe(n)?this.filter(n)[0].id():n[0].id());var f={},c={};i?s.forEach(function(m){var b=m.id();if(m.isNode()){var w=m.indegree(!0),E=m.outdegree(!0),C=w-E,x=E-w;C==1?l?o=!0:l=b:x==1?u?o=!0:u=b:(x>1||C>1)&&(o=!0),f[b]=[],m.outgoers().forEach(function(k){k.isEdge()&&f[b].push(k.id())})}else c[b]=[void 0,m.target().id()]}):s.forEach(function(m){var b=m.id();if(m.isNode()){var w=m.degree(!0);w%2&&(l?u?o=!0:u=b:l=b),f[b]=[],m.connectedEdges().forEach(function(E){return f[b].push(E.id())})}else c[b]=[m.source().id(),m.target().id()]});var h={found:!1,trail:void 0};if(o)return h;if(u&&l)if(i){if(v&&u!=v)return h;v=u}else{if(v&&u!=v&&l!=v)return h;v||(v=u)}else v||(v=s[0].id());var d=function(b){for(var w=b,E=[b],C,x,k;f[w].length;)C=f[w].shift(),x=c[C][0],k=c[C][1],w!=k?(f[k]=f[k].filter(function(S){return S!=C}),w=k):!i&&w!=x&&(f[x]=f[x].filter(function(S){return S!=C}),w=x),E.unshift(C),E.unshift(w);return E},y=[],g=[];for(g=d(v);g.length!=1;)f[g[0]].length==0?(y.unshift(s.getElementById(g.shift())),y.unshift(s.getElementById(g.shift()))):g=d(g.shift()).concat(g);y.unshift(s.getElementById(g.shift()));for(var p in f)if(f[p].length)return h;return h.found=!0,h.trail=this.spawn(y,!0),h}},Xa=function(){var e=this,t={},a=0,n=0,i=[],s=[],o={},l=function(c,h){for(var d=s.length-1,y=[],g=e.spawn();s[d].x!=c||s[d].y!=h;)y.push(s.pop().edge),d--;y.push(s.pop().edge),y.forEach(function(p){var m=p.connectedNodes().intersection(e);g.merge(p),m.forEach(function(b){var w=b.id(),E=b.connectedEdges().intersection(e);g.merge(b),t[w].cutVertex?g.merge(E.filter(function(C){return C.isLoop()})):g.merge(E)})}),i.push(g)},u=function(c,h,d){c===d&&(n+=1),t[h]={id:a,low:a++,cutVertex:!1};var y=e.getElementById(h).connectedEdges().intersection(e);if(y.size()===0)i.push(e.spawn(e.getElementById(h)));else{var g,p,m,b;y.forEach(function(w){g=w.source().id(),p=w.target().id(),m=g===h?p:g,m!==d&&(b=w.id(),o[b]||(o[b]=!0,s.push({x:h,y:m,edge:w})),m in t?t[h].low=Math.min(t[h].low,t[m].id):(u(c,m,h),t[h].low=Math.min(t[h].low,t[m].low),t[h].id<=t[m].low&&(t[h].cutVertex=!0,l(h,m))))})}};e.forEach(function(f){if(f.isNode()){var c=f.id();c in t||(n=0,u(c,c),t[c].cutVertex=n>1)}});var v=Object.keys(t).filter(function(f){return t[f].cutVertex}).map(function(f){return e.getElementById(f)});return{cut:e.spawn(v),components:i}},yh={hopcroftTarjanBiconnected:Xa,htbc:Xa,htb:Xa,hopcroftTarjanBiconnectedComponents:Xa},Za=function(){var e=this,t={},a=0,n=[],i=[],s=e.spawn(e),o=function(u){i.push(u),t[u]={index:a,low:a++,explored:!1};var v=e.getElementById(u).connectedEdges().intersection(e);if(v.forEach(function(y){var g=y.target().id();g!==u&&(g in t||o(g),t[g].explored||(t[u].low=Math.min(t[u].low,t[g].low)))}),t[u].index===t[u].low){for(var f=e.spawn();;){var c=i.pop();if(f.merge(e.getElementById(c)),t[c].low=t[u].index,t[c].explored=!0,c===u)break}var h=f.edgesWith(f),d=f.merge(h);n.push(d),s=s.difference(d)}};return e.forEach(function(l){if(l.isNode()){var u=l.id();u in t||o(u)}}),{cut:s,components:n}},mh={tarjanStronglyConnected:Za,tsc:Za,tscc:Za,tarjanStronglyConnectedComponents:Za},wv={};[Ea,Yc,Xc,Qc,jc,rd,nd,Rd,Ut,$t,Bs,Wd,ah,oh,hh,ph,yh,mh].forEach(function(r){he(wv,r)});/*!
Embeddable Minimum Strictly-Compliant Promises/A+ 1.1.1 Thenable
Copyright (c) 2013-2014 Ralf S. Engelschall (http://engelschall.com)
Licensed under The MIT License (http://opensource.org/licenses/MIT)
*/var xv=0,Ev=1,Cv=2,Ir=function(e){if(!(this instanceof Ir))return new Ir(e);this.id="Thenable/1.0.7",this.state=xv,this.fulfillValue=void 0,this.rejectReason=void 0,this.onFulfilled=[],this.onRejected=[],this.proxy={then:this.then.bind(this)},typeof e=="function"&&e.call(this,this.fulfill.bind(this),this.reject.bind(this))};Ir.prototype={fulfill:function(e){return tu(this,Ev,"fulfillValue",e)},reject:function(e){return tu(this,Cv,"rejectReason",e)},then:function(e,t){var a=this,n=new Ir;return a.onFulfilled.push(nu(e,n,"fulfill")),a.onRejected.push(nu(t,n,"reject")),Tv(a),n.proxy}};var tu=function(e,t,a,n){return e.state===xv&&(e.state=t,e[a]=n,Tv(e)),e},Tv=function(e){e.state===Ev?au(e,"onFulfilled",e.fulfillValue):e.state===Cv&&au(e,"onRejected",e.rejectReason)},au=function(e,t,a){if(e[t].length!==0){var n=e[t];e[t]=[];var i=function(){for(var o=0;o<n.length;o++)n[o](a)};typeof setImmediate=="function"?setImmediate(i):setTimeout(i,0)}},nu=function(e,t,a){return function(n){if(typeof e!="function")t[a].call(t,n);else{var i;try{i=e(n)}catch(s){t.reject(s);return}Sv(t,i)}}},Sv=function(e,t){if(e===t||e.proxy===t){e.reject(new TypeError("cannot resolve promise with itself"));return}var a;if(rr(t)==="object"&&t!==null||typeof t=="function")try{a=t.then}catch(i){e.reject(i);return}if(typeof a=="function"){var n=!1;try{a.call(t,function(i){n||(n=!0,i===t?e.reject(new TypeError("circular thenable chain")):Sv(e,i))},function(i){n||(n=!0,e.reject(i))})}catch(i){n||e.reject(i)}return}e.fulfill(t)};Ir.all=function(r){return new Ir(function(e,t){for(var a=new Array(r.length),n=0,i=function(l,u){a[l]=u,n++,n===r.length&&e(a)},s=0;s<r.length;s++)(function(o){var l=r[o],u=l!=null&&l.then!=null;if(u)l.then(function(f){i(o,f)},function(f){t(f)});else{var v=l;i(o,v)}})(s)})};Ir.resolve=function(r){return new Ir(function(e,t){e(r)})};Ir.reject=function(r){return new Ir(function(e,t){t(r)})};var ea=typeof Promise<"u"?Promise:Ir,Rs=function(e,t,a){var n=$s(e),i=!n,s=this._private=he({duration:1e3},t,a);if(s.target=e,s.style=s.style||s.css,s.started=!1,s.playing=!1,s.hooked=!1,s.applying=!1,s.progress=0,s.completes=[],s.frames=[],s.complete&&We(s.complete)&&s.completes.push(s.complete),i){var o=e.position();s.startPosition=s.startPosition||{x:o.x,y:o.y},s.startStyle=s.startStyle||e.cy().style().getAnimationStartStyle(e,s.style)}if(n){var l=e.pan();s.startPan={x:l.x,y:l.y},s.startZoom=e.zoom()}this.length=1,this[0]=this},Dt=Rs.prototype;he(Dt,{instanceString:function(){return"animation"},hook:function(){var e=this._private;if(!e.hooked){var t,a=e.target._private.animation;e.queue?t=a.queue:t=a.current,t.push(this),Dr(e.target)&&e.target.cy().addToAnimationPool(e.target),e.hooked=!0}return this},play:function(){var e=this._private;return e.progress===1&&(e.progress=0),e.playing=!0,e.started=!1,e.stopped=!1,this.hook(),this},playing:function(){return this._private.playing},apply:function(){var e=this._private;return e.applying=!0,e.started=!1,e.stopped=!1,this.hook(),this},applying:function(){return this._private.applying},pause:function(){var e=this._private;return e.playing=!1,e.started=!1,this},stop:function(){var e=this._private;return e.playing=!1,e.started=!1,e.stopped=!0,this},rewind:function(){return this.progress(0)},fastforward:function(){return this.progress(1)},time:function(e){var t=this._private;return e===void 0?t.progress*t.duration:this.progress(e/t.duration)},progress:function(e){var t=this._private,a=t.playing;return e===void 0?t.progress:(a&&this.pause(),t.progress=e,t.started=!1,a&&this.play(),this)},completed:function(){return this._private.progress===1},reverse:function(){var e=this._private,t=e.playing;t&&this.pause(),e.progress=1-e.progress,e.started=!1;var a=function(u,v){var f=e[u];f!=null&&(e[u]=e[v],e[v]=f)};if(a("zoom","startZoom"),a("pan","startPan"),a("position","startPosition"),e.style)for(var n=0;n<e.style.length;n++){var i=e.style[n],s=i.name,o=e.startStyle[s];e.startStyle[s]=i,e.style[n]=o}return t&&this.play(),this},promise:function(e){var t=this._private,a;switch(e){case"frame":a=t.frames;break;default:case"complete":case"completed":a=t.completes}return new ea(function(n,i){a.push(function(){n()})})}});Dt.complete=Dt.completed;Dt.run=Dt.play;Dt.running=Dt.playing;var bh={animated:function(){return function(){var t=this,a=t.length!==void 0,n=a?t:[t],i=this._private.cy||this;if(!i.styleEnabled())return!1;var s=n[0];if(s)return s._private.animation.current.length>0}},clearQueue:function(){return function(){var t=this,a=t.length!==void 0,n=a?t:[t],i=this._private.cy||this;if(!i.styleEnabled())return this;for(var s=0;s<n.length;s++){var o=n[s];o._private.animation.queue=[]}return this}},delay:function(){return function(t,a){var n=this._private.cy||this;return n.styleEnabled()?this.animate({delay:t,duration:t,complete:a}):this}},delayAnimation:function(){return function(t,a){var n=this._private.cy||this;return n.styleEnabled()?this.animation({delay:t,duration:t,complete:a}):this}},animation:function(){return function(t,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this,l=!i,u=!l;if(!o.styleEnabled())return this;var v=o.style();t=he({},t,a);var f=Object.keys(t).length===0;if(f)return new Rs(s[0],t);switch(t.duration===void 0&&(t.duration=400),t.duration){case"slow":t.duration=600;break;case"fast":t.duration=200;break}if(u&&(t.style=v.getPropsList(t.style||t.css),t.css=void 0),u&&t.renderedPosition!=null){var c=t.renderedPosition,h=o.pan(),d=o.zoom();t.position=fv(c,d,h)}if(l&&t.panBy!=null){var y=t.panBy,g=o.pan();t.pan={x:g.x+y.x,y:g.y+y.y}}var p=t.center||t.centre;if(l&&p!=null){var m=o.getCenterPan(p.eles,t.zoom);m!=null&&(t.pan=m)}if(l&&t.fit!=null){var b=t.fit,w=o.getFitViewport(b.eles||b.boundingBox,b.padding);w!=null&&(t.pan=w.pan,t.zoom=w.zoom)}if(l&&Pe(t.zoom)){var E=o.getZoomedViewport(t.zoom);E!=null?(E.zoomed&&(t.zoom=E.zoom),E.panned&&(t.pan=E.pan)):t.zoom=null}return new Rs(s[0],t)}},animate:function(){return function(t,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this;if(!o.styleEnabled())return this;a&&(t=he({},t,a));for(var l=0;l<s.length;l++){var u=s[l],v=u.animated()&&(t.queue===void 0||t.queue),f=u.animation(t,v?{queue:!0}:void 0);f.play()}return this}},stop:function(){return function(t,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this;if(!o.styleEnabled())return this;for(var l=0;l<s.length;l++){for(var u=s[l],v=u._private,f=v.animation.current,c=0;c<f.length;c++){var h=f[c],d=h._private;a&&(d.duration=0)}t&&(v.animation.queue=[]),a||(v.animation.current=[])}return o.notify("draw"),this}}},gi,iu;function On(){if(iu)return gi;iu=1;var r=Array.isArray;return gi=r,gi}var pi,su;function wh(){if(su)return pi;su=1;var r=On(),e=Ia(),t=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;function n(i,s){if(r(i))return!1;var o=typeof i;return o=="number"||o=="symbol"||o=="boolean"||i==null||e(i)?!0:a.test(i)||!t.test(i)||s!=null&&i in Object(s)}return pi=n,pi}var yi,ou;function xh(){if(ou)return yi;ou=1;var r=tv(),e=La(),t="[object AsyncFunction]",a="[object Function]",n="[object GeneratorFunction]",i="[object Proxy]";function s(o){if(!e(o))return!1;var l=r(o);return l==a||l==n||l==t||l==i}return yi=s,yi}var mi,uu;function Eh(){if(uu)return mi;uu=1;var r=Rn(),e=r["__core-js_shared__"];return mi=e,mi}var bi,lu;function Ch(){if(lu)return bi;lu=1;var r=Eh(),e=function(){var a=/[^.]+$/.exec(r&&r.keys&&r.keys.IE_PROTO||"");return a?"Symbol(src)_1."+a:""}();function t(a){return!!e&&e in a}return bi=t,bi}var wi,vu;function Th(){if(vu)return wi;vu=1;var r=Function.prototype,e=r.toString;function t(a){if(a!=null){try{return e.call(a)}catch{}try{return a+""}catch{}}return""}return wi=t,wi}var xi,fu;function Sh(){if(fu)return xi;fu=1;var r=xh(),e=Ch(),t=La(),a=Th(),n=/[\\^$.*+?()[\]{}|]/g,i=/^\[object .+?Constructor\]$/,s=Function.prototype,o=Object.prototype,l=s.toString,u=o.hasOwnProperty,v=RegExp("^"+l.call(u).replace(n,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function f(c){if(!t(c)||e(c))return!1;var h=r(c)?v:i;return h.test(a(c))}return xi=f,xi}var Ei,cu;function Dh(){if(cu)return Ei;cu=1;function r(e,t){return e==null?void 0:e[t]}return Ei=r,Ei}var Ci,du;function ao(){if(du)return Ci;du=1;var r=Sh(),e=Dh();function t(a,n){var i=e(a,n);return r(i)?i:void 0}return Ci=t,Ci}var Ti,hu;function Nn(){if(hu)return Ti;hu=1;var r=ao(),e=r(Object,"create");return Ti=e,Ti}var Si,gu;function kh(){if(gu)return Si;gu=1;var r=Nn();function e(){this.__data__=r?r(null):{},this.size=0}return Si=e,Si}var Di,pu;function Bh(){if(pu)return Di;pu=1;function r(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}return Di=r,Di}var ki,yu;function Ph(){if(yu)return ki;yu=1;var r=Nn(),e="__lodash_hash_undefined__",t=Object.prototype,a=t.hasOwnProperty;function n(i){var s=this.__data__;if(r){var o=s[i];return o===e?void 0:o}return a.call(s,i)?s[i]:void 0}return ki=n,ki}var Bi,mu;function Ah(){if(mu)return Bi;mu=1;var r=Nn(),e=Object.prototype,t=e.hasOwnProperty;function a(n){var i=this.__data__;return r?i[n]!==void 0:t.call(i,n)}return Bi=a,Bi}var Pi,bu;function Rh(){if(bu)return Pi;bu=1;var r=Nn(),e="__lodash_hash_undefined__";function t(a,n){var i=this.__data__;return this.size+=this.has(a)?0:1,i[a]=r&&n===void 0?e:n,this}return Pi=t,Pi}var Ai,wu;function Mh(){if(wu)return Ai;wu=1;var r=kh(),e=Bh(),t=Ph(),a=Ah(),n=Rh();function i(s){var o=-1,l=s==null?0:s.length;for(this.clear();++o<l;){var u=s[o];this.set(u[0],u[1])}}return i.prototype.clear=r,i.prototype.delete=e,i.prototype.get=t,i.prototype.has=a,i.prototype.set=n,Ai=i,Ai}var Ri,xu;function Lh(){if(xu)return Ri;xu=1;function r(){this.__data__=[],this.size=0}return Ri=r,Ri}var Mi,Eu;function Dv(){if(Eu)return Mi;Eu=1;function r(e,t){return e===t||e!==e&&t!==t}return Mi=r,Mi}var Li,Cu;function zn(){if(Cu)return Li;Cu=1;var r=Dv();function e(t,a){for(var n=t.length;n--;)if(r(t[n][0],a))return n;return-1}return Li=e,Li}var Ii,Tu;function Ih(){if(Tu)return Ii;Tu=1;var r=zn(),e=Array.prototype,t=e.splice;function a(n){var i=this.__data__,s=r(i,n);if(s<0)return!1;var o=i.length-1;return s==o?i.pop():t.call(i,s,1),--this.size,!0}return Ii=a,Ii}var Oi,Su;function Oh(){if(Su)return Oi;Su=1;var r=zn();function e(t){var a=this.__data__,n=r(a,t);return n<0?void 0:a[n][1]}return Oi=e,Oi}var Ni,Du;function Nh(){if(Du)return Ni;Du=1;var r=zn();function e(t){return r(this.__data__,t)>-1}return Ni=e,Ni}var zi,ku;function zh(){if(ku)return zi;ku=1;var r=zn();function e(t,a){var n=this.__data__,i=r(n,t);return i<0?(++this.size,n.push([t,a])):n[i][1]=a,this}return zi=e,zi}var Fi,Bu;function Fh(){if(Bu)return Fi;Bu=1;var r=Lh(),e=Ih(),t=Oh(),a=Nh(),n=zh();function i(s){var o=-1,l=s==null?0:s.length;for(this.clear();++o<l;){var u=s[o];this.set(u[0],u[1])}}return i.prototype.clear=r,i.prototype.delete=e,i.prototype.get=t,i.prototype.has=a,i.prototype.set=n,Fi=i,Fi}var Vi,Pu;function Vh(){if(Pu)return Vi;Pu=1;var r=ao(),e=Rn(),t=r(e,"Map");return Vi=t,Vi}var qi,Au;function qh(){if(Au)return qi;Au=1;var r=Mh(),e=Fh(),t=Vh();function a(){this.size=0,this.__data__={hash:new r,map:new(t||e),string:new r}}return qi=a,qi}var _i,Ru;function _h(){if(Ru)return _i;Ru=1;function r(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}return _i=r,_i}var Gi,Mu;function Fn(){if(Mu)return Gi;Mu=1;var r=_h();function e(t,a){var n=t.__data__;return r(a)?n[typeof a=="string"?"string":"hash"]:n.map}return Gi=e,Gi}var Hi,Lu;function Gh(){if(Lu)return Hi;Lu=1;var r=Fn();function e(t){var a=r(this,t).delete(t);return this.size-=a?1:0,a}return Hi=e,Hi}var Wi,Iu;function Hh(){if(Iu)return Wi;Iu=1;var r=Fn();function e(t){return r(this,t).get(t)}return Wi=e,Wi}var Ui,Ou;function Wh(){if(Ou)return Ui;Ou=1;var r=Fn();function e(t){return r(this,t).has(t)}return Ui=e,Ui}var $i,Nu;function Uh(){if(Nu)return $i;Nu=1;var r=Fn();function e(t,a){var n=r(this,t),i=n.size;return n.set(t,a),this.size+=n.size==i?0:1,this}return $i=e,$i}var Ki,zu;function $h(){if(zu)return Ki;zu=1;var r=qh(),e=Gh(),t=Hh(),a=Wh(),n=Uh();function i(s){var o=-1,l=s==null?0:s.length;for(this.clear();++o<l;){var u=s[o];this.set(u[0],u[1])}}return i.prototype.clear=r,i.prototype.delete=e,i.prototype.get=t,i.prototype.has=a,i.prototype.set=n,Ki=i,Ki}var Yi,Fu;function Kh(){if(Fu)return Yi;Fu=1;var r=$h(),e="Expected a function";function t(a,n){if(typeof a!="function"||n!=null&&typeof n!="function")throw new TypeError(e);var i=function(){var s=arguments,o=n?n.apply(this,s):s[0],l=i.cache;if(l.has(o))return l.get(o);var u=a.apply(this,s);return i.cache=l.set(o,u)||l,u};return i.cache=new(t.Cache||r),i}return t.Cache=r,Yi=t,Yi}var Xi,Vu;function Yh(){if(Vu)return Xi;Vu=1;var r=Kh(),e=500;function t(a){var n=r(a,function(s){return i.size===e&&i.clear(),s}),i=n.cache;return n}return Xi=t,Xi}var Zi,qu;function kv(){if(qu)return Zi;qu=1;var r=Yh(),e=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,t=/\\(\\)?/g,a=r(function(n){var i=[];return n.charCodeAt(0)===46&&i.push(""),n.replace(e,function(s,o,l,u){i.push(l?u.replace(t,"$1"):o||s)}),i});return Zi=a,Zi}var Qi,_u;function Bv(){if(_u)return Qi;_u=1;function r(e,t){for(var a=-1,n=e==null?0:e.length,i=Array(n);++a<n;)i[a]=t(e[a],a,e);return i}return Qi=r,Qi}var Ji,Gu;function Xh(){if(Gu)return Ji;Gu=1;var r=Ys(),e=Bv(),t=On(),a=Ia(),n=r?r.prototype:void 0,i=n?n.toString:void 0;function s(o){if(typeof o=="string")return o;if(t(o))return e(o,s)+"";if(a(o))return i?i.call(o):"";var l=o+"";return l=="0"&&1/o==-1/0?"-0":l}return Ji=s,Ji}var ji,Hu;function Pv(){if(Hu)return ji;Hu=1;var r=Xh();function e(t){return t==null?"":r(t)}return ji=e,ji}var es,Wu;function Av(){if(Wu)return es;Wu=1;var r=On(),e=wh(),t=kv(),a=Pv();function n(i,s){return r(i)?i:e(i,s)?[i]:t(a(i))}return es=n,es}var rs,Uu;function no(){if(Uu)return rs;Uu=1;var r=Ia();function e(t){if(typeof t=="string"||r(t))return t;var a=t+"";return a=="0"&&1/t==-1/0?"-0":a}return rs=e,rs}var ts,$u;function Zh(){if($u)return ts;$u=1;var r=Av(),e=no();function t(a,n){n=r(n,a);for(var i=0,s=n.length;a!=null&&i<s;)a=a[e(n[i++])];return i&&i==s?a:void 0}return ts=t,ts}var as,Ku;function Qh(){if(Ku)return as;Ku=1;var r=Zh();function e(t,a,n){var i=t==null?void 0:r(t,a);return i===void 0?n:i}return as=e,as}var Jh=Qh(),jh=Ma(Jh),ns,Yu;function eg(){if(Yu)return ns;Yu=1;var r=ao(),e=function(){try{var t=r(Object,"defineProperty");return t({},"",{}),t}catch{}}();return ns=e,ns}var is,Xu;function rg(){if(Xu)return is;Xu=1;var r=eg();function e(t,a,n){a=="__proto__"&&r?r(t,a,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[a]=n}return is=e,is}var ss,Zu;function tg(){if(Zu)return ss;Zu=1;var r=rg(),e=Dv(),t=Object.prototype,a=t.hasOwnProperty;function n(i,s,o){var l=i[s];(!(a.call(i,s)&&e(l,o))||o===void 0&&!(s in i))&&r(i,s,o)}return ss=n,ss}var os,Qu;function ag(){if(Qu)return os;Qu=1;var r=9007199254740991,e=/^(?:0|[1-9]\d*)$/;function t(a,n){var i=typeof a;return n=n??r,!!n&&(i=="number"||i!="symbol"&&e.test(a))&&a>-1&&a%1==0&&a<n}return os=t,os}var us,Ju;function ng(){if(Ju)return us;Ju=1;var r=tg(),e=Av(),t=ag(),a=La(),n=no();function i(s,o,l,u){if(!a(s))return s;o=e(o,s);for(var v=-1,f=o.length,c=f-1,h=s;h!=null&&++v<f;){var d=n(o[v]),y=l;if(d==="__proto__"||d==="constructor"||d==="prototype")return s;if(v!=c){var g=h[d];y=u?u(g,d,h):void 0,y===void 0&&(y=a(g)?g:t(o[v+1])?[]:{})}r(h,d,y),h=h[d]}return s}return us=i,us}var ls,ju;function ig(){if(ju)return ls;ju=1;var r=ng();function e(t,a,n){return t==null?t:r(t,a,n)}return ls=e,ls}var sg=ig(),og=Ma(sg),vs,el;function ug(){if(el)return vs;el=1;function r(e,t){var a=-1,n=e.length;for(t||(t=Array(n));++a<n;)t[a]=e[a];return t}return vs=r,vs}var fs,rl;function lg(){if(rl)return fs;rl=1;var r=Bv(),e=ug(),t=On(),a=Ia(),n=kv(),i=no(),s=Pv();function o(l){return t(l)?r(l,i):a(l)?[l]:e(n(s(l)))}return fs=o,fs}var vg=lg(),fg=Ma(vg),cg={data:function(e){var t={field:"data",bindingEvent:"data",allowBinding:!1,allowSetting:!1,allowGetting:!1,settingEvent:"data",settingTriggersEvent:!1,triggerFnName:"trigger",immutableKeys:{},updateStyle:!1,beforeGet:function(n){},beforeSet:function(n,i){},onSet:function(n){},canSet:function(n){return!0}};return e=he({},t,e),function(n,i){var s=e,o=this,l=o.length!==void 0,u=l?o:[o],v=l?o[0]:o;if(fe(n)){var f=n.indexOf(".")!==-1,c=f&&fg(n);if(s.allowGetting&&i===void 0){var h;return v&&(s.beforeGet(v),c&&v._private[s.field][n]===void 0?h=jh(v._private[s.field],c):h=v._private[s.field][n]),h}else if(s.allowSetting&&i!==void 0){var d=!s.immutableKeys[n];if(d){var y=$l({},n,i);s.beforeSet(o,y);for(var g=0,p=u.length;g<p;g++){var m=u[g];s.canSet(m)&&(c&&v._private[s.field][n]===void 0?og(m._private[s.field],c,i):m._private[s.field][n]=i)}s.updateStyle&&o.updateStyle(),s.onSet(o),s.settingTriggersEvent&&o[s.triggerFnName](s.settingEvent)}}}else if(s.allowSetting&&Pe(n)){var b=n,w,E,C=Object.keys(b);s.beforeSet(o,b);for(var x=0;x<C.length;x++){w=C[x],E=b[w];var k=!s.immutableKeys[w];if(k)for(var S=0;S<u.length;S++){var P=u[S];s.canSet(P)&&(P._private[s.field][w]=E)}}s.updateStyle&&o.updateStyle(),s.onSet(o),s.settingTriggersEvent&&o[s.triggerFnName](s.settingEvent)}else if(s.allowBinding&&We(n)){var D=n;o.on(s.bindingEvent,D)}else if(s.allowGetting&&n===void 0){var A;return v&&(s.beforeGet(v),A=v._private[s.field]),A}return o}},removeData:function(e){var t={field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!1,immutableKeys:{}};return e=he({},t,e),function(n){var i=e,s=this,o=s.length!==void 0,l=o?s:[s];if(fe(n)){for(var u=n.split(/\s+/),v=u.length,f=0;f<v;f++){var c=u[f];if(!nt(c)){var h=!i.immutableKeys[c];if(h)for(var d=0,y=l.length;d<y;d++)l[d]._private[i.field][c]=void 0}}i.triggerEvent&&s[i.triggerFnName](i.event)}else if(n===void 0){for(var g=0,p=l.length;g<p;g++)for(var m=l[g]._private[i.field],b=Object.keys(m),w=0;w<b.length;w++){var E=b[w],C=!i.immutableKeys[E];C&&(m[E]=void 0)}i.triggerEvent&&s[i.triggerFnName](i.event)}return s}}},dg={eventAliasesOn:function(e){var t=e;t.addListener=t.listen=t.bind=t.on,t.unlisten=t.unbind=t.off=t.removeListener,t.trigger=t.emit,t.pon=t.promiseOn=function(a,n){var i=this,s=Array.prototype.slice.call(arguments,0);return new ea(function(o,l){var u=function(h){i.off.apply(i,f),o(h)},v=s.concat([u]),f=v.concat([]);i.on.apply(i,v)})}}},Me={};[bh,cg,dg].forEach(function(r){he(Me,r)});var hg={animate:Me.animate(),animation:Me.animation(),animated:Me.animated(),clearQueue:Me.clearQueue(),delay:Me.delay(),delayAnimation:Me.delayAnimation(),stop:Me.stop()},un={classes:function(e){var t=this;if(e===void 0){var a=[];return t[0]._private.classes.forEach(function(d){return a.push(d)}),a}else Fe(e)||(e=(e||"").match(/\S+/g)||[]);for(var n=[],i=new jt(e),s=0;s<t.length;s++){for(var o=t[s],l=o._private,u=l.classes,v=!1,f=0;f<e.length;f++){var c=e[f],h=u.has(c);if(!h){v=!0;break}}v||(v=u.size!==e.length),v&&(l.classes=i,n.push(o))}return n.length>0&&this.spawn(n).updateStyle().emit("class"),t},addClass:function(e){return this.toggleClass(e,!0)},hasClass:function(e){var t=this[0];return t!=null&&t._private.classes.has(e)},toggleClass:function(e,t){Fe(e)||(e=e.match(/\S+/g)||[]);for(var a=this,n=t===void 0,i=[],s=0,o=a.length;s<o;s++)for(var l=a[s],u=l._private.classes,v=!1,f=0;f<e.length;f++){var c=e[f],h=u.has(c),d=!1;t||n&&!h?(u.add(c),d=!0):(!t||n&&h)&&(u.delete(c),d=!0),!v&&d&&(i.push(l),v=!0)}return i.length>0&&this.spawn(i).updateStyle().emit("class"),a},removeClass:function(e){return this.toggleClass(e,!1)},flashClass:function(e,t){var a=this;if(t==null)t=250;else if(t===0)return a;return a.addClass(e),setTimeout(function(){a.removeClass(e)},t),a}};un.className=un.classNames=un.classes;var Be={metaChar:"[\\!\\\"\\#\\$\\%\\&\\'\\(\\)\\*\\+\\,\\.\\/\\:\\;\\<\\=\\>\\?\\@\\[\\]\\^\\`\\{\\|\\}\\~]",comparatorOp:"=|\\!=|>|>=|<|<=|\\$=|\\^=|\\*=",boolOp:"\\?|\\!|\\^",string:`"(?:\\\\"|[^"])*"|'(?:\\\\'|[^'])*'`,number:er,meta:"degree|indegree|outdegree",separator:"\\s*,\\s*",descendant:"\\s+",child:"\\s+>\\s+",subject:"\\$",group:"node|edge|\\*",directedEdge:"\\s+->\\s+",undirectedEdge:"\\s+<->\\s+"};Be.variable="(?:[\\w-.]|(?:\\\\"+Be.metaChar+"))+";Be.className="(?:[\\w-]|(?:\\\\"+Be.metaChar+"))+";Be.value=Be.string+"|"+Be.number;Be.id=Be.variable;(function(){var r,e,t;for(r=Be.comparatorOp.split("|"),t=0;t<r.length;t++)e=r[t],Be.comparatorOp+="|@"+e;for(r=Be.comparatorOp.split("|"),t=0;t<r.length;t++)e=r[t],!(e.indexOf("!")>=0)&&e!=="="&&(Be.comparatorOp+="|\\!"+e)})();var Ne=function(){return{checks:[]}},ie={GROUP:0,COLLECTION:1,FILTER:2,DATA_COMPARE:3,DATA_EXIST:4,DATA_BOOL:5,META_COMPARE:6,STATE:7,ID:8,CLASS:9,UNDIRECTED_EDGE:10,DIRECTED_EDGE:11,NODE_SOURCE:12,NODE_TARGET:13,NODE_NEIGHBOR:14,CHILD:15,DESCENDANT:16,PARENT:17,ANCESTOR:18,COMPOUND_SPLIT:19,TRUE:20},Ms=[{selector:":selected",matches:function(e){return e.selected()}},{selector:":unselected",matches:function(e){return!e.selected()}},{selector:":selectable",matches:function(e){return e.selectable()}},{selector:":unselectable",matches:function(e){return!e.selectable()}},{selector:":locked",matches:function(e){return e.locked()}},{selector:":unlocked",matches:function(e){return!e.locked()}},{selector:":visible",matches:function(e){return e.visible()}},{selector:":hidden",matches:function(e){return!e.visible()}},{selector:":transparent",matches:function(e){return e.transparent()}},{selector:":grabbed",matches:function(e){return e.grabbed()}},{selector:":free",matches:function(e){return!e.grabbed()}},{selector:":removed",matches:function(e){return e.removed()}},{selector:":inside",matches:function(e){return!e.removed()}},{selector:":grabbable",matches:function(e){return e.grabbable()}},{selector:":ungrabbable",matches:function(e){return!e.grabbable()}},{selector:":animated",matches:function(e){return e.animated()}},{selector:":unanimated",matches:function(e){return!e.animated()}},{selector:":parent",matches:function(e){return e.isParent()}},{selector:":childless",matches:function(e){return e.isChildless()}},{selector:":child",matches:function(e){return e.isChild()}},{selector:":orphan",matches:function(e){return e.isOrphan()}},{selector:":nonorphan",matches:function(e){return e.isChild()}},{selector:":compound",matches:function(e){return e.isNode()?e.isParent():e.source().isParent()||e.target().isParent()}},{selector:":loop",matches:function(e){return e.isLoop()}},{selector:":simple",matches:function(e){return e.isSimple()}},{selector:":active",matches:function(e){return e.active()}},{selector:":inactive",matches:function(e){return!e.active()}},{selector:":backgrounding",matches:function(e){return e.backgrounding()}},{selector:":nonbackgrounding",matches:function(e){return!e.backgrounding()}}].sort(function(r,e){return gc(r.selector,e.selector)}),gg=function(){for(var r={},e,t=0;t<Ms.length;t++)e=Ms[t],r[e.selector]=e.matches;return r}(),pg=function(e,t){return gg[e](t)},yg="("+Ms.map(function(r){return r.selector}).join("|")+")",Mt=function(e){return e.replace(new RegExp("\\\\("+Be.metaChar+")","g"),function(t,a){return a})},et=function(e,t,a){e[e.length-1]=a},Ls=[{name:"group",query:!0,regex:"("+Be.group+")",populate:function(e,t,a){var n=je(a,1),i=n[0];t.checks.push({type:ie.GROUP,value:i==="*"?i:i+"s"})}},{name:"state",query:!0,regex:yg,populate:function(e,t,a){var n=je(a,1),i=n[0];t.checks.push({type:ie.STATE,value:i})}},{name:"id",query:!0,regex:"\\#("+Be.id+")",populate:function(e,t,a){var n=je(a,1),i=n[0];t.checks.push({type:ie.ID,value:Mt(i)})}},{name:"className",query:!0,regex:"\\.("+Be.className+")",populate:function(e,t,a){var n=je(a,1),i=n[0];t.checks.push({type:ie.CLASS,value:Mt(i)})}},{name:"dataExists",query:!0,regex:"\\[\\s*("+Be.variable+")\\s*\\]",populate:function(e,t,a){var n=je(a,1),i=n[0];t.checks.push({type:ie.DATA_EXIST,field:Mt(i)})}},{name:"dataCompare",query:!0,regex:"\\[\\s*("+Be.variable+")\\s*("+Be.comparatorOp+")\\s*("+Be.value+")\\s*\\]",populate:function(e,t,a){var n=je(a,3),i=n[0],s=n[1],o=n[2],l=new RegExp("^"+Be.string+"$").exec(o)!=null;l?o=o.substring(1,o.length-1):o=parseFloat(o),t.checks.push({type:ie.DATA_COMPARE,field:Mt(i),operator:s,value:o})}},{name:"dataBool",query:!0,regex:"\\[\\s*("+Be.boolOp+")\\s*("+Be.variable+")\\s*\\]",populate:function(e,t,a){var n=je(a,2),i=n[0],s=n[1];t.checks.push({type:ie.DATA_BOOL,field:Mt(s),operator:i})}},{name:"metaCompare",query:!0,regex:"\\[\\[\\s*("+Be.meta+")\\s*("+Be.comparatorOp+")\\s*("+Be.number+")\\s*\\]\\]",populate:function(e,t,a){var n=je(a,3),i=n[0],s=n[1],o=n[2];t.checks.push({type:ie.META_COMPARE,field:Mt(i),operator:s,value:parseFloat(o)})}},{name:"nextQuery",separator:!0,regex:Be.separator,populate:function(e,t){var a=e.currentSubject,n=e.edgeCount,i=e.compoundCount,s=e[e.length-1];a!=null&&(s.subject=a,e.currentSubject=null),s.edgeCount=n,s.compoundCount=i,e.edgeCount=0,e.compoundCount=0;var o=e[e.length++]=Ne();return o}},{name:"directedEdge",separator:!0,regex:Be.directedEdge,populate:function(e,t){if(e.currentSubject==null){var a=Ne(),n=t,i=Ne();return a.checks.push({type:ie.DIRECTED_EDGE,source:n,target:i}),et(e,t,a),e.edgeCount++,i}else{var s=Ne(),o=t,l=Ne();return s.checks.push({type:ie.NODE_SOURCE,source:o,target:l}),et(e,t,s),e.edgeCount++,l}}},{name:"undirectedEdge",separator:!0,regex:Be.undirectedEdge,populate:function(e,t){if(e.currentSubject==null){var a=Ne(),n=t,i=Ne();return a.checks.push({type:ie.UNDIRECTED_EDGE,nodes:[n,i]}),et(e,t,a),e.edgeCount++,i}else{var s=Ne(),o=t,l=Ne();return s.checks.push({type:ie.NODE_NEIGHBOR,node:o,neighbor:l}),et(e,t,s),l}}},{name:"child",separator:!0,regex:Be.child,populate:function(e,t){if(e.currentSubject==null){var a=Ne(),n=Ne(),i=e[e.length-1];return a.checks.push({type:ie.CHILD,parent:i,child:n}),et(e,t,a),e.compoundCount++,n}else if(e.currentSubject===t){var s=Ne(),o=e[e.length-1],l=Ne(),u=Ne(),v=Ne(),f=Ne();return s.checks.push({type:ie.COMPOUND_SPLIT,left:o,right:l,subject:u}),u.checks=t.checks,t.checks=[{type:ie.TRUE}],f.checks.push({type:ie.TRUE}),l.checks.push({type:ie.PARENT,parent:f,child:v}),et(e,o,s),e.currentSubject=u,e.compoundCount++,v}else{var c=Ne(),h=Ne(),d=[{type:ie.PARENT,parent:c,child:h}];return c.checks=t.checks,t.checks=d,e.compoundCount++,h}}},{name:"descendant",separator:!0,regex:Be.descendant,populate:function(e,t){if(e.currentSubject==null){var a=Ne(),n=Ne(),i=e[e.length-1];return a.checks.push({type:ie.DESCENDANT,ancestor:i,descendant:n}),et(e,t,a),e.compoundCount++,n}else if(e.currentSubject===t){var s=Ne(),o=e[e.length-1],l=Ne(),u=Ne(),v=Ne(),f=Ne();return s.checks.push({type:ie.COMPOUND_SPLIT,left:o,right:l,subject:u}),u.checks=t.checks,t.checks=[{type:ie.TRUE}],f.checks.push({type:ie.TRUE}),l.checks.push({type:ie.ANCESTOR,ancestor:f,descendant:v}),et(e,o,s),e.currentSubject=u,e.compoundCount++,v}else{var c=Ne(),h=Ne(),d=[{type:ie.ANCESTOR,ancestor:c,descendant:h}];return c.checks=t.checks,t.checks=d,e.compoundCount++,h}}},{name:"subject",modifier:!0,regex:Be.subject,populate:function(e,t){if(e.currentSubject!=null&&e.currentSubject!==t)return Le("Redefinition of subject in selector `"+e.toString()+"`"),!1;e.currentSubject=t;var a=e[e.length-1],n=a.checks[0],i=n==null?null:n.type;i===ie.DIRECTED_EDGE?n.type=ie.NODE_TARGET:i===ie.UNDIRECTED_EDGE&&(n.type=ie.NODE_NEIGHBOR,n.node=n.nodes[1],n.neighbor=n.nodes[0],n.nodes=null)}}];Ls.forEach(function(r){return r.regexObj=new RegExp("^"+r.regex)});var mg=function(e){for(var t,a,n,i=0;i<Ls.length;i++){var s=Ls[i],o=s.name,l=e.match(s.regexObj);if(l!=null){a=l,t=s,n=o;var u=l[0];e=e.substring(u.length);break}}return{expr:t,match:a,name:n,remaining:e}},bg=function(e){var t=e.match(/^\s+/);if(t){var a=t[0];e=e.substring(a.length)}return e},wg=function(e){var t=this,a=t.inputText=e,n=t[0]=Ne();for(t.length=1,a=bg(a);;){var i=mg(a);if(i.expr==null)return Le("The selector `"+e+"`is invalid"),!1;var s=i.match.slice(1),o=i.expr.populate(t,n,s);if(o===!1)return!1;if(o!=null&&(n=o),a=i.remaining,a.match(/^\s*$/))break}var l=t[t.length-1];t.currentSubject!=null&&(l.subject=t.currentSubject),l.edgeCount=t.edgeCount,l.compoundCount=t.compoundCount;for(var u=0;u<t.length;u++){var v=t[u];if(v.compoundCount>0&&v.edgeCount>0)return Le("The selector `"+e+"` is invalid because it uses both a compound selector and an edge selector"),!1;if(v.edgeCount>1)return Le("The selector `"+e+"` is invalid because it uses multiple edge selectors"),!1;v.edgeCount===1&&Le("The selector `"+e+"` is deprecated.  Edge selectors do not take effect on changes to source and target nodes after an edge is added, for performance reasons.  Use a class or data selector on edges instead, updating the class or data of an edge when your app detects a change in source or target nodes.")}return!0},xg=function(){if(this.toStringCache!=null)return this.toStringCache;for(var e=function(v){return v??""},t=function(v){return fe(v)?'"'+v+'"':e(v)},a=function(v){return" "+v+" "},n=function(v,f){var c=v.type,h=v.value;switch(c){case ie.GROUP:{var d=e(h);return d.substring(0,d.length-1)}case ie.DATA_COMPARE:{var y=v.field,g=v.operator;return"["+y+a(e(g))+t(h)+"]"}case ie.DATA_BOOL:{var p=v.operator,m=v.field;return"["+e(p)+m+"]"}case ie.DATA_EXIST:{var b=v.field;return"["+b+"]"}case ie.META_COMPARE:{var w=v.operator,E=v.field;return"[["+E+a(e(w))+t(h)+"]]"}case ie.STATE:return h;case ie.ID:return"#"+h;case ie.CLASS:return"."+h;case ie.PARENT:case ie.CHILD:return i(v.parent,f)+a(">")+i(v.child,f);case ie.ANCESTOR:case ie.DESCENDANT:return i(v.ancestor,f)+" "+i(v.descendant,f);case ie.COMPOUND_SPLIT:{var C=i(v.left,f),x=i(v.subject,f),k=i(v.right,f);return C+(C.length>0?" ":"")+x+k}case ie.TRUE:return""}},i=function(v,f){return v.checks.reduce(function(c,h,d){return c+(f===v&&d===0?"$":"")+n(h,f)},"")},s="",o=0;o<this.length;o++){var l=this[o];s+=i(l,l.subject),this.length>1&&o<this.length-1&&(s+=", ")}return this.toStringCache=s,s},Eg={parse:wg,toString:xg},Rv=function(e,t,a){var n,i=fe(e),s=te(e),o=fe(a),l,u,v=!1,f=!1,c=!1;switch(t.indexOf("!")>=0&&(t=t.replace("!",""),f=!0),t.indexOf("@")>=0&&(t=t.replace("@",""),v=!0),(i||o||v)&&(l=!i&&!s?"":""+e,u=""+a),v&&(e=l=l.toLowerCase(),a=u=u.toLowerCase()),t){case"*=":n=l.indexOf(u)>=0;break;case"$=":n=l.indexOf(u,l.length-u.length)>=0;break;case"^=":n=l.indexOf(u)===0;break;case"=":n=e===a;break;case">":c=!0,n=e>a;break;case">=":c=!0,n=e>=a;break;case"<":c=!0,n=e<a;break;case"<=":c=!0,n=e<=a;break;default:n=!1;break}return f&&(e!=null||!c)&&(n=!n),n},Cg=function(e,t){switch(t){case"?":return!!e;case"!":return!e;case"^":return e===void 0}},Tg=function(e){return e!==void 0},io=function(e,t){return e.data(t)},Sg=function(e,t){return e[t]()},$e=[],Ge=function(e,t){return e.checks.every(function(a){return $e[a.type](a,t)})};$e[ie.GROUP]=function(r,e){var t=r.value;return t==="*"||t===e.group()};$e[ie.STATE]=function(r,e){var t=r.value;return pg(t,e)};$e[ie.ID]=function(r,e){var t=r.value;return e.id()===t};$e[ie.CLASS]=function(r,e){var t=r.value;return e.hasClass(t)};$e[ie.META_COMPARE]=function(r,e){var t=r.field,a=r.operator,n=r.value;return Rv(Sg(e,t),a,n)};$e[ie.DATA_COMPARE]=function(r,e){var t=r.field,a=r.operator,n=r.value;return Rv(io(e,t),a,n)};$e[ie.DATA_BOOL]=function(r,e){var t=r.field,a=r.operator;return Cg(io(e,t),a)};$e[ie.DATA_EXIST]=function(r,e){var t=r.field;return r.operator,Tg(io(e,t))};$e[ie.UNDIRECTED_EDGE]=function(r,e){var t=r.nodes[0],a=r.nodes[1],n=e.source(),i=e.target();return Ge(t,n)&&Ge(a,i)||Ge(a,n)&&Ge(t,i)};$e[ie.NODE_NEIGHBOR]=function(r,e){return Ge(r.node,e)&&e.neighborhood().some(function(t){return t.isNode()&&Ge(r.neighbor,t)})};$e[ie.DIRECTED_EDGE]=function(r,e){return Ge(r.source,e.source())&&Ge(r.target,e.target())};$e[ie.NODE_SOURCE]=function(r,e){return Ge(r.source,e)&&e.outgoers().some(function(t){return t.isNode()&&Ge(r.target,t)})};$e[ie.NODE_TARGET]=function(r,e){return Ge(r.target,e)&&e.incomers().some(function(t){return t.isNode()&&Ge(r.source,t)})};$e[ie.CHILD]=function(r,e){return Ge(r.child,e)&&Ge(r.parent,e.parent())};$e[ie.PARENT]=function(r,e){return Ge(r.parent,e)&&e.children().some(function(t){return Ge(r.child,t)})};$e[ie.DESCENDANT]=function(r,e){return Ge(r.descendant,e)&&e.ancestors().some(function(t){return Ge(r.ancestor,t)})};$e[ie.ANCESTOR]=function(r,e){return Ge(r.ancestor,e)&&e.descendants().some(function(t){return Ge(r.descendant,t)})};$e[ie.COMPOUND_SPLIT]=function(r,e){return Ge(r.subject,e)&&Ge(r.left,e)&&Ge(r.right,e)};$e[ie.TRUE]=function(){return!0};$e[ie.COLLECTION]=function(r,e){var t=r.value;return t.has(e)};$e[ie.FILTER]=function(r,e){var t=r.value;return t(e)};var Dg=function(e){var t=this;if(t.length===1&&t[0].checks.length===1&&t[0].checks[0].type===ie.ID)return e.getElementById(t[0].checks[0].value).collection();var a=function(i){for(var s=0;s<t.length;s++){var o=t[s];if(Ge(o,i))return!0}return!1};return t.text()==null&&(a=function(){return!0}),e.filter(a)},kg=function(e){for(var t=this,a=0;a<t.length;a++){var n=t[a];if(Ge(n,e))return!0}return!1},Bg={matches:kg,filter:Dg},ot=function(e){this.inputText=e,this.currentSubject=null,this.compoundCount=0,this.edgeCount=0,this.length=0,e==null||fe(e)&&e.match(/^\s*$/)||(Dr(e)?this.addQuery({checks:[{type:ie.COLLECTION,value:e.collection()}]}):We(e)?this.addQuery({checks:[{type:ie.FILTER,value:e}]}):fe(e)?this.parse(e)||(this.invalid=!0):He("A selector must be created from a string; found "))},ut=ot.prototype;[Eg,Bg].forEach(function(r){return he(ut,r)});ut.text=function(){return this.inputText};ut.size=function(){return this.length};ut.eq=function(r){return this[r]};ut.sameText=function(r){return!this.invalid&&!r.invalid&&this.text()===r.text()};ut.addQuery=function(r){this[this.length++]=r};ut.selector=ut.toString;var tt={allAre:function(e){var t=new ot(e);return this.every(function(a){return t.matches(a)})},is:function(e){var t=new ot(e);return this.some(function(a){return t.matches(a)})},some:function(e,t){for(var a=0;a<this.length;a++){var n=t?e.apply(t,[this[a],a,this]):e(this[a],a,this);if(n)return!0}return!1},every:function(e,t){for(var a=0;a<this.length;a++){var n=t?e.apply(t,[this[a],a,this]):e(this[a],a,this);if(!n)return!1}return!0},same:function(e){if(this===e)return!0;e=this.cy().collection(e);var t=this.length,a=e.length;return t!==a?!1:t===1?this[0]===e[0]:this.every(function(n){return e.hasElementWithId(n.id())})},anySame:function(e){return e=this.cy().collection(e),this.some(function(t){return e.hasElementWithId(t.id())})},allAreNeighbors:function(e){e=this.cy().collection(e);var t=this.neighborhood();return e.every(function(a){return t.hasElementWithId(a.id())})},contains:function(e){e=this.cy().collection(e);var t=this;return e.every(function(a){return t.hasElementWithId(a.id())})}};tt.allAreNeighbours=tt.allAreNeighbors;tt.has=tt.contains;tt.equal=tt.equals=tt.same;var Pr=function(e,t){return function(n,i,s,o){var l=n,u=this,v;if(l==null?v="":Dr(l)&&l.length===1&&(v=l.id()),u.length===1&&v){var f=u[0]._private,c=f.traversalCache=f.traversalCache||{},h=c[t]=c[t]||[],d=Tt(v),y=h[d];return y||(h[d]=e.call(u,n,i,s,o))}else return e.call(u,n,i,s,o)}},Qt={parent:function(e){var t=[];if(this.length===1){var a=this[0]._private.parent;if(a)return a}for(var n=0;n<this.length;n++){var i=this[n],s=i._private.parent;s&&t.push(s)}return this.spawn(t,!0).filter(e)},parents:function(e){for(var t=[],a=this.parent();a.nonempty();){for(var n=0;n<a.length;n++){var i=a[n];t.push(i)}a=a.parent()}return this.spawn(t,!0).filter(e)},commonAncestors:function(e){for(var t,a=0;a<this.length;a++){var n=this[a],i=n.parents();t=t||i,t=t.intersect(i)}return t.filter(e)},orphans:function(e){return this.stdFilter(function(t){return t.isOrphan()}).filter(e)},nonorphans:function(e){return this.stdFilter(function(t){return t.isChild()}).filter(e)},children:Pr(function(r){for(var e=[],t=0;t<this.length;t++)for(var a=this[t],n=a._private.children,i=0;i<n.length;i++)e.push(n[i]);return this.spawn(e,!0).filter(r)},"children"),siblings:function(e){return this.parent().children().not(this).filter(e)},isParent:function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length!==0},isChildless:function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length===0},isChild:function(){var e=this[0];if(e)return e.isNode()&&e._private.parent!=null},isOrphan:function(){var e=this[0];if(e)return e.isNode()&&e._private.parent==null},descendants:function(e){var t=[];function a(n){for(var i=0;i<n.length;i++){var s=n[i];t.push(s),s.children().nonempty()&&a(s.children())}}return a(this.children()),this.spawn(t,!0).filter(e)}};function so(r,e,t,a){for(var n=[],i=new jt,s=r.cy(),o=s.hasCompoundNodes(),l=0;l<r.length;l++){var u=r[l];t?n.push(u):o&&a(n,i,u)}for(;n.length>0;){var v=n.shift();e(v),i.add(v.id()),o&&a(n,i,v)}return r}function Mv(r,e,t){if(t.isParent())for(var a=t._private.children,n=0;n<a.length;n++){var i=a[n];e.has(i.id())||r.push(i)}}Qt.forEachDown=function(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return so(this,r,e,Mv)};function Lv(r,e,t){if(t.isChild()){var a=t._private.parent;e.has(a.id())||r.push(a)}}Qt.forEachUp=function(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return so(this,r,e,Lv)};function Pg(r,e,t){Lv(r,e,t),Mv(r,e,t)}Qt.forEachUpAndDown=function(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return so(this,r,e,Pg)};Qt.ancestors=Qt.parents;var Sa,Iv;Sa=Iv={data:Me.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),removeData:Me.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),scratch:Me.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:Me.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),rscratch:Me.data({field:"rscratch",allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!0}),removeRscratch:Me.removeData({field:"rscratch",triggerEvent:!1}),id:function(){var e=this[0];if(e)return e._private.data.id}};Sa.attr=Sa.data;Sa.removeAttr=Sa.removeData;var Ag=Iv,Vn={};function cs(r){return function(e){var t=this;if(e===void 0&&(e=!0),t.length!==0)if(t.isNode()&&!t.removed()){for(var a=0,n=t[0],i=n._private.edges,s=0;s<i.length;s++){var o=i[s];!e&&o.isLoop()||(a+=r(n,o))}return a}else return}}he(Vn,{degree:cs(function(r,e){return e.source().same(e.target())?2:1}),indegree:cs(function(r,e){return e.target().same(r)?1:0}),outdegree:cs(function(r,e){return e.source().same(r)?1:0})});function Lt(r,e){return function(t){for(var a,n=this.nodes(),i=0;i<n.length;i++){var s=n[i],o=s[r](t);o!==void 0&&(a===void 0||e(o,a))&&(a=o)}return a}}he(Vn,{minDegree:Lt("degree",function(r,e){return r<e}),maxDegree:Lt("degree",function(r,e){return r>e}),minIndegree:Lt("indegree",function(r,e){return r<e}),maxIndegree:Lt("indegree",function(r,e){return r>e}),minOutdegree:Lt("outdegree",function(r,e){return r<e}),maxOutdegree:Lt("outdegree",function(r,e){return r>e})});he(Vn,{totalDegree:function(e){for(var t=0,a=this.nodes(),n=0;n<a.length;n++)t+=a[n].degree(e);return t}});var Lr,Ov,Nv=function(e,t,a){for(var n=0;n<e.length;n++){var i=e[n];if(!i.locked()){var s=i._private.position,o={x:t.x!=null?t.x-s.x:0,y:t.y!=null?t.y-s.y:0};i.isParent()&&!(o.x===0&&o.y===0)&&i.children().shift(o,a),i.dirtyBoundingBoxCache()}}},tl={field:"position",bindingEvent:"position",allowBinding:!0,allowSetting:!0,settingEvent:"position",settingTriggersEvent:!0,triggerFnName:"emitAndNotify",allowGetting:!0,validKeys:["x","y"],beforeGet:function(e){e.updateCompoundBounds()},beforeSet:function(e,t){Nv(e,t,!1)},onSet:function(e){e.dirtyCompoundBoundsCache()},canSet:function(e){return!e.locked()}};Lr=Ov={position:Me.data(tl),silentPosition:Me.data(he({},tl,{allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!1,beforeSet:function(e,t){Nv(e,t,!0)},onSet:function(e){e.dirtyCompoundBoundsCache()}})),positions:function(e,t){if(Pe(e))t?this.silentPosition(e):this.position(e);else if(We(e)){var a=e,n=this.cy();n.startBatch();for(var i=0;i<this.length;i++){var s=this[i],o=void 0;(o=a(s,i))&&(t?s.silentPosition(o):s.position(o))}n.endBatch()}return this},silentPositions:function(e){return this.positions(e,!0)},shift:function(e,t,a){var n;if(Pe(e)?(n={x:te(e.x)?e.x:0,y:te(e.y)?e.y:0},a=t):fe(e)&&te(t)&&(n={x:0,y:0},n[e]=t),n!=null){var i=this.cy();i.startBatch();for(var s=0;s<this.length;s++){var o=this[s];if(!(i.hasCompoundNodes()&&o.isChild()&&o.ancestors().anySame(this))){var l=o.position(),u={x:l.x+n.x,y:l.y+n.y};a?o.silentPosition(u):o.position(u)}}i.endBatch()}return this},silentShift:function(e,t){return Pe(e)?this.shift(e,!0):fe(e)&&te(t)&&this.shift(e,t,!0),this},renderedPosition:function(e,t){var a=this[0],n=this.cy(),i=n.zoom(),s=n.pan(),o=Pe(e)?e:void 0,l=o!==void 0||t!==void 0&&fe(e);if(a&&a.isNode())if(l)for(var u=0;u<this.length;u++){var v=this[u];t!==void 0?v.position(e,(t-s[e])/i):o!==void 0&&v.position(fv(o,i,s))}else{var f=a.position();return o=Ln(f,i,s),e===void 0?o:o[e]}else if(!l)return;return this},relativePosition:function(e,t){var a=this[0],n=this.cy(),i=Pe(e)?e:void 0,s=i!==void 0||t!==void 0&&fe(e),o=n.hasCompoundNodes();if(a&&a.isNode())if(s)for(var l=0;l<this.length;l++){var u=this[l],v=o?u.parent():null,f=v&&v.length>0,c=f;f&&(v=v[0]);var h=c?v.position():{x:0,y:0};t!==void 0?u.position(e,t+h[e]):i!==void 0&&u.position({x:i.x+h.x,y:i.y+h.y})}else{var d=a.position(),y=o?a.parent():null,g=y&&y.length>0,p=g;g&&(y=y[0]);var m=p?y.position():{x:0,y:0};return i={x:d.x-m.x,y:d.y-m.y},e===void 0?i:i[e]}else if(!s)return;return this}};Lr.modelPosition=Lr.point=Lr.position;Lr.modelPositions=Lr.points=Lr.positions;Lr.renderedPoint=Lr.renderedPosition;Lr.relativePoint=Lr.relativePosition;var Rg=Ov,Kt,ct;Kt=ct={};ct.renderedBoundingBox=function(r){var e=this.boundingBox(r),t=this.cy(),a=t.zoom(),n=t.pan(),i=e.x1*a+n.x,s=e.x2*a+n.x,o=e.y1*a+n.y,l=e.y2*a+n.y;return{x1:i,x2:s,y1:o,y2:l,w:s-i,h:l-o}};ct.dirtyCompoundBoundsCache=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.cy();return!e.styleEnabled()||!e.hasCompoundNodes()?this:(this.forEachUp(function(t){if(t.isParent()){var a=t._private;a.compoundBoundsClean=!1,a.bbCache=null,r||t.emitAndNotify("bounds")}}),this)};ct.updateCompoundBounds=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.cy();if(!e.styleEnabled()||!e.hasCompoundNodes())return this;if(!r&&e.batching())return this;function t(s){if(!s.isParent())return;var o=s._private,l=s.children(),u=s.pstyle("compound-sizing-wrt-labels").value==="include",v={width:{val:s.pstyle("min-width").pfValue,left:s.pstyle("min-width-bias-left"),right:s.pstyle("min-width-bias-right")},height:{val:s.pstyle("min-height").pfValue,top:s.pstyle("min-height-bias-top"),bottom:s.pstyle("min-height-bias-bottom")}},f=l.boundingBox({includeLabels:u,includeOverlays:!1,useCache:!1}),c=o.position;(f.w===0||f.h===0)&&(f={w:s.pstyle("width").pfValue,h:s.pstyle("height").pfValue},f.x1=c.x-f.w/2,f.x2=c.x+f.w/2,f.y1=c.y-f.h/2,f.y2=c.y+f.h/2);function h(S,P,D){var A=0,B=0,R=P+D;return S>0&&R>0&&(A=P/R*S,B=D/R*S),{biasDiff:A,biasComplementDiff:B}}function d(S,P,D,A){if(D.units==="%")switch(A){case"width":return S>0?D.pfValue*S:0;case"height":return P>0?D.pfValue*P:0;case"average":return S>0&&P>0?D.pfValue*(S+P)/2:0;case"min":return S>0&&P>0?S>P?D.pfValue*P:D.pfValue*S:0;case"max":return S>0&&P>0?S>P?D.pfValue*S:D.pfValue*P:0;default:return 0}else return D.units==="px"?D.pfValue:0}var y=v.width.left.value;v.width.left.units==="px"&&v.width.val>0&&(y=y*100/v.width.val);var g=v.width.right.value;v.width.right.units==="px"&&v.width.val>0&&(g=g*100/v.width.val);var p=v.height.top.value;v.height.top.units==="px"&&v.height.val>0&&(p=p*100/v.height.val);var m=v.height.bottom.value;v.height.bottom.units==="px"&&v.height.val>0&&(m=m*100/v.height.val);var b=h(v.width.val-f.w,y,g),w=b.biasDiff,E=b.biasComplementDiff,C=h(v.height.val-f.h,p,m),x=C.biasDiff,k=C.biasComplementDiff;o.autoPadding=d(f.w,f.h,s.pstyle("padding"),s.pstyle("padding-relative-to").value),o.autoWidth=Math.max(f.w,v.width.val),c.x=(-w+f.x1+f.x2+E)/2,o.autoHeight=Math.max(f.h,v.height.val),c.y=(-x+f.y1+f.y2+k)/2}for(var a=0;a<this.length;a++){var n=this[a],i=n._private;(!i.compoundBoundsClean||r)&&(t(n),e.batching()||(i.compoundBoundsClean=!0))}return this};var Br=function(e){return e===1/0||e===-1/0?0:e},Mr=function(e,t,a,n,i){n-t===0||i-a===0||t==null||a==null||n==null||i==null||(e.x1=t<e.x1?t:e.x1,e.x2=n>e.x2?n:e.x2,e.y1=a<e.y1?a:e.y1,e.y2=i>e.y2?i:e.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1)},bt=function(e,t){return t==null?e:Mr(e,t.x1,t.y1,t.x2,t.y2)},ua=function(e,t,a){return Er(e,t,a)},Qa=function(e,t,a){if(!t.cy().headless()){var n=t._private,i=n.rstyle,s=i.arrowWidth/2,o=t.pstyle(a+"-arrow-shape").value,l,u;if(o!=="none"){a==="source"?(l=i.srcX,u=i.srcY):a==="target"?(l=i.tgtX,u=i.tgtY):(l=i.midX,u=i.midY);var v=n.arrowBounds=n.arrowBounds||{},f=v[a]=v[a]||{};f.x1=l-s,f.y1=u-s,f.x2=l+s,f.y2=u+s,f.w=f.x2-f.x1,f.h=f.y2-f.y1,sn(f,1),Mr(e,f.x1,f.y1,f.x2,f.y2)}}},ds=function(e,t,a){if(!t.cy().headless()){var n;a?n=a+"-":n="";var i=t._private,s=i.rstyle,o=t.pstyle(n+"label").strValue;if(o){var l=t.pstyle("text-halign"),u=t.pstyle("text-valign"),v=ua(s,"labelWidth",a),f=ua(s,"labelHeight",a),c=ua(s,"labelX",a),h=ua(s,"labelY",a),d=t.pstyle(n+"text-margin-x").pfValue,y=t.pstyle(n+"text-margin-y").pfValue,g=t.isEdge(),p=t.pstyle(n+"text-rotation"),m=t.pstyle("text-outline-width").pfValue,b=t.pstyle("text-border-width").pfValue,w=b/2,E=t.pstyle("text-background-padding").pfValue,C=2,x=f,k=v,S=k/2,P=x/2,D,A,B,R;if(g)D=c-S,A=c+S,B=h-P,R=h+P;else{switch(l.value){case"left":D=c-k,A=c;break;case"center":D=c-S,A=c+S;break;case"right":D=c,A=c+k;break}switch(u.value){case"top":B=h-x,R=h;break;case"center":B=h-P,R=h+P;break;case"bottom":B=h,R=h+x;break}}var M=d-Math.max(m,w)-E-C,I=d+Math.max(m,w)+E+C,L=y-Math.max(m,w)-E-C,O=y+Math.max(m,w)+E+C;D+=M,A+=I,B+=L,R+=O;var V=a||"main",G=i.labelBounds,N=G[V]=G[V]||{};N.x1=D,N.y1=B,N.x2=A,N.y2=R,N.w=A-D,N.h=R-B,N.leftPad=M,N.rightPad=I,N.topPad=L,N.botPad=O;var F=g&&p.strValue==="autorotate",K=p.pfValue!=null&&p.pfValue!==0;if(F||K){var X=F?ua(i.rstyle,"labelAngle",a):p.pfValue,Q=Math.cos(X),Z=Math.sin(X),re=(D+A)/2,ae=(B+R)/2;if(!g){switch(l.value){case"left":re=A;break;case"right":re=D;break}switch(u.value){case"top":ae=R;break;case"bottom":ae=B;break}}var J=function(Ie,se){return Ie=Ie-re,se=se-ae,{x:Ie*Q-se*Z+re,y:Ie*Z+se*Q+ae}},z=J(D,B),q=J(D,R),H=J(A,B),ee=J(A,R);D=Math.min(z.x,q.x,H.x,ee.x),A=Math.max(z.x,q.x,H.x,ee.x),B=Math.min(z.y,q.y,H.y,ee.y),R=Math.max(z.y,q.y,H.y,ee.y)}var ne=V+"Rot",be=G[ne]=G[ne]||{};be.x1=D,be.y1=B,be.x2=A,be.y2=R,be.w=A-D,be.h=R-B,Mr(e,D,B,A,R),Mr(i.labelBounds.all,D,B,A,R)}return e}},Mg=function(e,t){if(!t.cy().headless()){var a=t.pstyle("outline-opacity").value,n=t.pstyle("outline-width").value;if(a>0&&n>0){var i=t.pstyle("outline-offset").value,s=t.pstyle("shape").value,o=n+i,l=(e.w+o*2)/e.w,u=(e.h+o*2)/e.h,v=0,f=0;["diamond","pentagon","round-triangle"].includes(s)?(l=(e.w+o*2.4)/e.w,f=-o/3.6):["concave-hexagon","rhomboid","right-rhomboid"].includes(s)?l=(e.w+o*2.4)/e.w:s==="star"?(l=(e.w+o*2.8)/e.w,u=(e.h+o*2.6)/e.h,f=-o/3.8):s==="triangle"?(l=(e.w+o*2.8)/e.w,u=(e.h+o*2.4)/e.h,f=-o/1.4):s==="vee"&&(l=(e.w+o*4.4)/e.w,u=(e.h+o*3.8)/e.h,f=-o*.5);var c=e.h*u-e.h,h=e.w*l-e.w;if(on(e,[Math.ceil(c/2),Math.ceil(h/2)]),v!=0||f!==0){var d=gd(e,v,f);cv(e,d)}}}},Lg=function(e,t){var a=e._private.cy,n=a.styleEnabled(),i=a.headless(),s=Sr(),o=e._private,l=e.isNode(),u=e.isEdge(),v,f,c,h,d,y,g=o.rstyle,p=l&&n?e.pstyle("bounds-expansion").pfValue:[0],m=function(_e){return _e.pstyle("display").value!=="none"},b=!n||m(e)&&(!u||m(e.source())&&m(e.target()));if(b){var w=0,E=0;n&&t.includeOverlays&&(w=e.pstyle("overlay-opacity").value,w!==0&&(E=e.pstyle("overlay-padding").value));var C=0,x=0;n&&t.includeUnderlays&&(C=e.pstyle("underlay-opacity").value,C!==0&&(x=e.pstyle("underlay-padding").value));var k=Math.max(E,x),S=0,P=0;if(n&&(S=e.pstyle("width").pfValue,P=S/2),l&&t.includeNodes){var D=e.position();d=D.x,y=D.y;var A=e.outerWidth(),B=A/2,R=e.outerHeight(),M=R/2;v=d-B,f=d+B,c=y-M,h=y+M,Mr(s,v,c,f,h),n&&t.includeOutlines&&Mg(s,e)}else if(u&&t.includeEdges)if(n&&!i){var I=e.pstyle("curve-style").strValue;if(v=Math.min(g.srcX,g.midX,g.tgtX),f=Math.max(g.srcX,g.midX,g.tgtX),c=Math.min(g.srcY,g.midY,g.tgtY),h=Math.max(g.srcY,g.midY,g.tgtY),v-=P,f+=P,c-=P,h+=P,Mr(s,v,c,f,h),I==="haystack"){var L=g.haystackPts;if(L&&L.length===2){if(v=L[0].x,c=L[0].y,f=L[1].x,h=L[1].y,v>f){var O=v;v=f,f=O}if(c>h){var V=c;c=h,h=V}Mr(s,v-P,c-P,f+P,h+P)}}else if(I==="bezier"||I==="unbundled-bezier"||I.endsWith("segments")||I.endsWith("taxi")){var G;switch(I){case"bezier":case"unbundled-bezier":G=g.bezierPts;break;case"segments":case"taxi":case"round-segments":case"round-taxi":G=g.linePts;break}if(G!=null)for(var N=0;N<G.length;N++){var F=G[N];v=F.x-P,f=F.x+P,c=F.y-P,h=F.y+P,Mr(s,v,c,f,h)}}}else{var K=e.source(),X=K.position(),Q=e.target(),Z=Q.position();if(v=X.x,f=Z.x,c=X.y,h=Z.y,v>f){var re=v;v=f,f=re}if(c>h){var ae=c;c=h,h=ae}v-=P,f+=P,c-=P,h+=P,Mr(s,v,c,f,h)}if(n&&t.includeEdges&&u&&(Qa(s,e,"mid-source"),Qa(s,e,"mid-target"),Qa(s,e,"source"),Qa(s,e,"target")),n){var J=e.pstyle("ghost").value==="yes";if(J){var z=e.pstyle("ghost-offset-x").pfValue,q=e.pstyle("ghost-offset-y").pfValue;Mr(s,s.x1+z,s.y1+q,s.x2+z,s.y2+q)}}var H=o.bodyBounds=o.bodyBounds||{};Ho(H,s),on(H,p),sn(H,1),n&&(v=s.x1,f=s.x2,c=s.y1,h=s.y2,Mr(s,v-k,c-k,f+k,h+k));var ee=o.overlayBounds=o.overlayBounds||{};Ho(ee,s),on(ee,p),sn(ee,1);var ne=o.labelBounds=o.labelBounds||{};ne.all!=null?hd(ne.all):ne.all=Sr(),n&&t.includeLabels&&(t.includeMainLabels&&ds(s,e,null),u&&(t.includeSourceLabels&&ds(s,e,"source"),t.includeTargetLabels&&ds(s,e,"target")))}return s.x1=Br(s.x1),s.y1=Br(s.y1),s.x2=Br(s.x2),s.y2=Br(s.y2),s.w=Br(s.x2-s.x1),s.h=Br(s.y2-s.y1),s.w>0&&s.h>0&&b&&(on(s,p),sn(s,1)),s},zv=function(e){var t=0,a=function(s){return(s?1:0)<<t++},n=0;return n+=a(e.incudeNodes),n+=a(e.includeEdges),n+=a(e.includeLabels),n+=a(e.includeMainLabels),n+=a(e.includeSourceLabels),n+=a(e.includeTargetLabels),n+=a(e.includeOverlays),n+=a(e.includeOutlines),n},Fv=function(e){var t=function(o){return Math.round(o)};if(e.isEdge()){var a=e.source().position(),n=e.target().position();return zo([t(a.x),t(a.y),t(n.x),t(n.y)])}else{var i=e.position();return zo([t(i.x),t(i.y)])}},al=function(e,t){var a=e._private,n,i=e.isEdge(),s=t==null?nl:zv(t),o=s===nl;if(a.bbCache==null?(n=Lg(e,Da),a.bbCache=n,a.bbCachePosKey=Fv(e)):n=a.bbCache,!o){var l=e.isNode();n=Sr(),(t.includeNodes&&l||t.includeEdges&&!l)&&(t.includeOverlays?bt(n,a.overlayBounds):bt(n,a.bodyBounds)),t.includeLabels&&(t.includeMainLabels&&(!i||t.includeSourceLabels&&t.includeTargetLabels)?bt(n,a.labelBounds.all):(t.includeMainLabels&&bt(n,a.labelBounds.mainRot),t.includeSourceLabels&&bt(n,a.labelBounds.sourceRot),t.includeTargetLabels&&bt(n,a.labelBounds.targetRot))),n.w=n.x2-n.x1,n.h=n.y2-n.y1}return n},Da={includeNodes:!0,includeEdges:!0,includeLabels:!0,includeMainLabels:!0,includeSourceLabels:!0,includeTargetLabels:!0,includeOverlays:!0,includeUnderlays:!0,includeOutlines:!0,useCache:!0},nl=zv(Da),il=fr(Da);ct.boundingBox=function(r){var e,t=r===void 0||r.useCache===void 0||r.useCache===!0,a=Yt(function(v){var f=v._private;return f.bbCache==null||f.styleDirty||f.bbCachePosKey!==Fv(v)},function(v){return v.id()});if(t&&this.length===1&&!a(this[0]))r===void 0?r=Da:r=il(r),e=al(this[0],r);else{e=Sr(),r=r||Da;var n=il(r),i=this,s=i.cy(),o=s.styleEnabled();this.edges().forEach(a),this.nodes().forEach(a),o&&this.recalculateRenderedStyle(t),this.updateCompoundBounds(!t);for(var l=0;l<i.length;l++){var u=i[l];a(u)&&u.dirtyBoundingBoxCache(),bt(e,al(u,n))}}return e.x1=Br(e.x1),e.y1=Br(e.y1),e.x2=Br(e.x2),e.y2=Br(e.y2),e.w=Br(e.x2-e.x1),e.h=Br(e.y2-e.y1),e};ct.dirtyBoundingBoxCache=function(){for(var r=0;r<this.length;r++){var e=this[r]._private;e.bbCache=null,e.bbCachePosKey=null,e.bodyBounds=null,e.overlayBounds=null,e.labelBounds.all=null,e.labelBounds.source=null,e.labelBounds.target=null,e.labelBounds.main=null,e.labelBounds.sourceRot=null,e.labelBounds.targetRot=null,e.labelBounds.mainRot=null,e.arrowBounds.source=null,e.arrowBounds.target=null,e.arrowBounds["mid-source"]=null,e.arrowBounds["mid-target"]=null}return this.emitAndNotify("bounds"),this};ct.boundingBoxAt=function(r){var e=this.nodes(),t=this.cy(),a=t.hasCompoundNodes(),n=t.collection();if(a&&(n=e.filter(function(u){return u.isParent()}),e=e.not(n)),Pe(r)){var i=r;r=function(){return i}}var s=function(v,f){return v._private.bbAtOldPos=r(v,f)},o=function(v){return v._private.bbAtOldPos};t.startBatch(),e.forEach(s).silentPositions(r),a&&(n.dirtyCompoundBoundsCache(),n.dirtyBoundingBoxCache(),n.updateCompoundBounds(!0));var l=dd(this.boundingBox({useCache:!1}));return e.silentPositions(o),a&&(n.dirtyCompoundBoundsCache(),n.dirtyBoundingBoxCache(),n.updateCompoundBounds(!0)),t.endBatch(),l};Kt.boundingbox=Kt.bb=Kt.boundingBox;Kt.renderedBoundingbox=Kt.renderedBoundingBox;var Ig=ct,ga,za;ga=za={};var Vv=function(e){e.uppercaseName=Eo(e.name),e.autoName="auto"+e.uppercaseName,e.labelName="label"+e.uppercaseName,e.outerName="outer"+e.uppercaseName,e.uppercaseOuterName=Eo(e.outerName),ga[e.name]=function(){var a=this[0],n=a._private,i=n.cy,s=i._private.styleEnabled;if(a)if(s){if(a.isParent())return a.updateCompoundBounds(),n[e.autoName]||0;var o=a.pstyle(e.name);switch(o.strValue){case"label":return a.recalculateRenderedStyle(),n.rstyle[e.labelName]||0;default:return o.pfValue}}else return 1},ga["outer"+e.uppercaseName]=function(){var a=this[0],n=a._private,i=n.cy,s=i._private.styleEnabled;if(a)if(s){var o=a[e.name](),l=a.pstyle("border-position").value,u;l==="center"?u=a.pstyle("border-width").pfValue:l==="outside"?u=2*a.pstyle("border-width").pfValue:u=0;var v=2*a.padding();return o+u+v}else return 1},ga["rendered"+e.uppercaseName]=function(){var a=this[0];if(a){var n=a[e.name]();return n*this.cy().zoom()}},ga["rendered"+e.uppercaseOuterName]=function(){var a=this[0];if(a){var n=a[e.outerName]();return n*this.cy().zoom()}}};Vv({name:"width"});Vv({name:"height"});za.padding=function(){var r=this[0],e=r._private;return r.isParent()?(r.updateCompoundBounds(),e.autoPadding!==void 0?e.autoPadding:r.pstyle("padding").pfValue):r.pstyle("padding").pfValue};za.paddedHeight=function(){var r=this[0];return r.height()+2*r.padding()};za.paddedWidth=function(){var r=this[0];return r.width()+2*r.padding()};var Og=za,Ng=function(e,t){if(e.isEdge()&&e.takesUpSpace())return t(e)},zg=function(e,t){if(e.isEdge()&&e.takesUpSpace()){var a=e.cy();return Ln(t(e),a.zoom(),a.pan())}},Fg=function(e,t){if(e.isEdge()&&e.takesUpSpace()){var a=e.cy(),n=a.pan(),i=a.zoom();return t(e).map(function(s){return Ln(s,i,n)})}},Vg=function(e){return e.renderer().getControlPoints(e)},qg=function(e){return e.renderer().getSegmentPoints(e)},_g=function(e){return e.renderer().getSourceEndpoint(e)},Gg=function(e){return e.renderer().getTargetEndpoint(e)},Hg=function(e){return e.renderer().getEdgeMidpoint(e)},sl={controlPoints:{get:Vg,mult:!0},segmentPoints:{get:qg,mult:!0},sourceEndpoint:{get:_g},targetEndpoint:{get:Gg},midpoint:{get:Hg}},Wg=function(e){return"rendered"+e[0].toUpperCase()+e.substr(1)},Ug=Object.keys(sl).reduce(function(r,e){var t=sl[e],a=Wg(e);return r[e]=function(){return Ng(this,t.get)},t.mult?r[a]=function(){return Fg(this,t.get)}:r[a]=function(){return zg(this,t.get)},r},{}),$g=he({},Rg,Ig,Og,Ug);/*!
Event object based on jQuery events, MIT license

https://jquery.org/license/
https://tldrlegal.com/license/mit-license
https://github.com/jquery/jquery/blob/master/src/event.js
*/var qv=function(e,t){this.recycle(e,t)};function la(){return!1}function Ja(){return!0}qv.prototype={instanceString:function(){return"event"},recycle:function(e,t){if(this.isImmediatePropagationStopped=this.isPropagationStopped=this.isDefaultPrevented=la,e!=null&&e.preventDefault?(this.type=e.type,this.isDefaultPrevented=e.defaultPrevented?Ja:la):e!=null&&e.type?t=e:this.type=e,t!=null&&(this.originalEvent=t.originalEvent,this.type=t.type!=null?t.type:this.type,this.cy=t.cy,this.target=t.target,this.position=t.position,this.renderedPosition=t.renderedPosition,this.namespace=t.namespace,this.layout=t.layout),this.cy!=null&&this.position!=null&&this.renderedPosition==null){var a=this.position,n=this.cy.zoom(),i=this.cy.pan();this.renderedPosition={x:a.x*n+i.x,y:a.y*n+i.y}}this.timeStamp=e&&e.timeStamp||Date.now()},preventDefault:function(){this.isDefaultPrevented=Ja;var e=this.originalEvent;e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){this.isPropagationStopped=Ja;var e=this.originalEvent;e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=Ja,this.stopPropagation()},isDefaultPrevented:la,isPropagationStopped:la,isImmediatePropagationStopped:la};var _v=/^([^.]+)(\.(?:[^.]+))?$/,Kg=".*",Gv={qualifierCompare:function(e,t){return e===t},eventMatches:function(){return!0},addEventFields:function(){},callbackContext:function(e){return e},beforeEmit:function(){},afterEmit:function(){},bubble:function(){return!1},parent:function(){return null},context:null},ol=Object.keys(Gv),Yg={};function qn(){for(var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Yg,e=arguments.length>1?arguments[1]:void 0,t=0;t<ol.length;t++){var a=ol[t];this[a]=r[a]||Gv[a]}this.context=e||this.context,this.listeners=[],this.emitting=0}var lt=qn.prototype,Hv=function(e,t,a,n,i,s,o){We(n)&&(i=n,n=null),o&&(s==null?s=o:s=he({},s,o));for(var l=Fe(a)?a:a.split(/\s+/),u=0;u<l.length;u++){var v=l[u];if(!nt(v)){var f=v.match(_v);if(f){var c=f[1],h=f[2]?f[2]:null,d=t(e,v,c,h,n,i,s);if(d===!1)break}}}},ul=function(e,t){return e.addEventFields(e.context,t),new qv(t.type,t)},Xg=function(e,t,a){if(nc(a)){t(e,a);return}else if(Pe(a)){t(e,ul(e,a));return}for(var n=Fe(a)?a:a.split(/\s+/),i=0;i<n.length;i++){var s=n[i];if(!nt(s)){var o=s.match(_v);if(o){var l=o[1],u=o[2]?o[2]:null,v=ul(e,{type:l,namespace:u,target:e.context});t(e,v)}}}};lt.on=lt.addListener=function(r,e,t,a,n){return Hv(this,function(i,s,o,l,u,v,f){We(v)&&i.listeners.push({event:s,callback:v,type:o,namespace:l,qualifier:u,conf:f})},r,e,t,a,n),this};lt.one=function(r,e,t,a){return this.on(r,e,t,a,{one:!0})};lt.removeListener=lt.off=function(r,e,t,a){var n=this;this.emitting!==0&&(this.listeners=zc(this.listeners));for(var i=this.listeners,s=function(u){var v=i[u];Hv(n,function(f,c,h,d,y,g){if((v.type===h||r==="*")&&(!d&&v.namespace!==".*"||v.namespace===d)&&(!y||f.qualifierCompare(v.qualifier,y))&&(!g||v.callback===g))return i.splice(u,1),!1},r,e,t,a)},o=i.length-1;o>=0;o--)s(o);return this};lt.removeAllListeners=function(){return this.removeListener("*")};lt.emit=lt.trigger=function(r,e,t){var a=this.listeners,n=a.length;return this.emitting++,Fe(e)||(e=[e]),Xg(this,function(i,s){t!=null&&(a=[{event:s.event,type:s.type,namespace:s.namespace,callback:t}],n=a.length);for(var o=function(){var v=a[l];if(v.type===s.type&&(!v.namespace||v.namespace===s.namespace||v.namespace===Kg)&&i.eventMatches(i.context,v,s)){var f=[s];e!=null&&Vc(f,e),i.beforeEmit(i.context,v,s),v.conf&&v.conf.one&&(i.listeners=i.listeners.filter(function(d){return d!==v}));var c=i.callbackContext(i.context,v,s),h=v.callback.apply(c,f);i.afterEmit(i.context,v,s),h===!1&&(s.stopPropagation(),s.preventDefault())}},l=0;l<n;l++)o();i.bubble(i.context)&&!s.isPropagationStopped()&&i.parent(i.context).emit(s,e)},r),this.emitting--,this};var Zg={qualifierCompare:function(e,t){return e==null||t==null?e==null&&t==null:e.sameText(t)},eventMatches:function(e,t,a){var n=t.qualifier;return n!=null?e!==a.target&&Ra(a.target)&&n.matches(a.target):!0},addEventFields:function(e,t){t.cy=e.cy(),t.target=e},callbackContext:function(e,t,a){return t.qualifier!=null?a.target:e},beforeEmit:function(e,t){t.conf&&t.conf.once&&t.conf.onceCollection.removeListener(t.event,t.qualifier,t.callback)},bubble:function(){return!0},parent:function(e){return e.isChild()?e.parent():e.cy()}},ja=function(e){return fe(e)?new ot(e):e},Wv={createEmitter:function(){for(var e=0;e<this.length;e++){var t=this[e],a=t._private;a.emitter||(a.emitter=new qn(Zg,t))}return this},emitter:function(){return this._private.emitter},on:function(e,t,a){for(var n=ja(t),i=0;i<this.length;i++){var s=this[i];s.emitter().on(e,n,a)}return this},removeListener:function(e,t,a){for(var n=ja(t),i=0;i<this.length;i++){var s=this[i];s.emitter().removeListener(e,n,a)}return this},removeAllListeners:function(){for(var e=0;e<this.length;e++){var t=this[e];t.emitter().removeAllListeners()}return this},one:function(e,t,a){for(var n=ja(t),i=0;i<this.length;i++){var s=this[i];s.emitter().one(e,n,a)}return this},once:function(e,t,a){for(var n=ja(t),i=0;i<this.length;i++){var s=this[i];s.emitter().on(e,n,a,{once:!0,onceCollection:this})}},emit:function(e,t){for(var a=0;a<this.length;a++){var n=this[a];n.emitter().emit(e,t)}return this},emitAndNotify:function(e,t){if(this.length!==0)return this.cy().notify(e,this),this.emit(e,t),this}};Me.eventAliasesOn(Wv);var Uv={nodes:function(e){return this.filter(function(t){return t.isNode()}).filter(e)},edges:function(e){return this.filter(function(t){return t.isEdge()}).filter(e)},byGroup:function(){for(var e=this.spawn(),t=this.spawn(),a=0;a<this.length;a++){var n=this[a];n.isNode()?e.push(n):t.push(n)}return{nodes:e,edges:t}},filter:function(e,t){if(e===void 0)return this;if(fe(e)||Dr(e))return new ot(e).filter(this);if(We(e)){for(var a=this.spawn(),n=this,i=0;i<n.length;i++){var s=n[i],o=t?e.apply(t,[s,i,n]):e(s,i,n);o&&a.push(s)}return a}return this.spawn()},not:function(e){if(e){fe(e)&&(e=this.filter(e));for(var t=this.spawn(),a=0;a<this.length;a++){var n=this[a],i=e.has(n);i||t.push(n)}return t}else return this},absoluteComplement:function(){var e=this.cy();return e.mutableElements().not(this)},intersect:function(e){if(fe(e)){var t=e;return this.filter(t)}for(var a=this.spawn(),n=this,i=e,s=this.length<e.length,o=s?n:i,l=s?i:n,u=0;u<o.length;u++){var v=o[u];l.has(v)&&a.push(v)}return a},xor:function(e){var t=this._private.cy;fe(e)&&(e=t.$(e));var a=this.spawn(),n=this,i=e,s=function(l,u){for(var v=0;v<l.length;v++){var f=l[v],c=f._private.data.id,h=u.hasElementWithId(c);h||a.push(f)}};return s(n,i),s(i,n),a},diff:function(e){var t=this._private.cy;fe(e)&&(e=t.$(e));var a=this.spawn(),n=this.spawn(),i=this.spawn(),s=this,o=e,l=function(v,f,c){for(var h=0;h<v.length;h++){var d=v[h],y=d._private.data.id,g=f.hasElementWithId(y);g?i.merge(d):c.push(d)}};return l(s,o,a),l(o,s,n),{left:a,right:n,both:i}},add:function(e){var t=this._private.cy;if(!e)return this;if(fe(e)){var a=e;e=t.mutableElements().filter(a)}for(var n=this.spawnSelf(),i=0;i<e.length;i++){var s=e[i],o=!this.has(s);o&&n.push(s)}return n},merge:function(e){var t=this._private,a=t.cy;if(!e)return this;if(e&&fe(e)){var n=e;e=a.mutableElements().filter(n)}for(var i=t.map,s=0;s<e.length;s++){var o=e[s],l=o._private.data.id,u=!i.has(l);if(u){var v=this.length++;this[v]=o,i.set(l,{ele:o,index:v})}}return this},unmergeAt:function(e){var t=this[e],a=t.id(),n=this._private,i=n.map;this[e]=void 0,i.delete(a);var s=e===this.length-1;if(this.length>1&&!s){var o=this.length-1,l=this[o],u=l._private.data.id;this[o]=void 0,this[e]=l,i.set(u,{ele:l,index:e})}return this.length--,this},unmergeOne:function(e){e=e[0];var t=this._private,a=e._private.data.id,n=t.map,i=n.get(a);if(!i)return this;var s=i.index;return this.unmergeAt(s),this},unmerge:function(e){var t=this._private.cy;if(!e)return this;if(e&&fe(e)){var a=e;e=t.mutableElements().filter(a)}for(var n=0;n<e.length;n++)this.unmergeOne(e[n]);return this},unmergeBy:function(e){for(var t=this.length-1;t>=0;t--){var a=this[t];e(a)&&this.unmergeAt(t)}return this},map:function(e,t){for(var a=[],n=this,i=0;i<n.length;i++){var s=n[i],o=t?e.apply(t,[s,i,n]):e(s,i,n);a.push(o)}return a},reduce:function(e,t){for(var a=t,n=this,i=0;i<n.length;i++)a=e(a,n[i],i,n);return a},max:function(e,t){for(var a=-1/0,n,i=this,s=0;s<i.length;s++){var o=i[s],l=t?e.apply(t,[o,s,i]):e(o,s,i);l>a&&(a=l,n=o)}return{value:a,ele:n}},min:function(e,t){for(var a=1/0,n,i=this,s=0;s<i.length;s++){var o=i[s],l=t?e.apply(t,[o,s,i]):e(o,s,i);l<a&&(a=l,n=o)}return{value:a,ele:n}}},Re=Uv;Re.u=Re["|"]=Re["+"]=Re.union=Re.or=Re.add;Re["\\"]=Re["!"]=Re["-"]=Re.difference=Re.relativeComplement=Re.subtract=Re.not;Re.n=Re["&"]=Re["."]=Re.and=Re.intersection=Re.intersect;Re["^"]=Re["(+)"]=Re["(-)"]=Re.symmetricDifference=Re.symdiff=Re.xor;Re.fnFilter=Re.filterFn=Re.stdFilter=Re.filter;Re.complement=Re.abscomp=Re.absoluteComplement;var Qg={isNode:function(){return this.group()==="nodes"},isEdge:function(){return this.group()==="edges"},isLoop:function(){return this.isEdge()&&this.source()[0]===this.target()[0]},isSimple:function(){return this.isEdge()&&this.source()[0]!==this.target()[0]},group:function(){var e=this[0];if(e)return e._private.group}},$v=function(e,t){var a=e.cy(),n=a.hasCompoundNodes();function i(v){var f=v.pstyle("z-compound-depth");return f.value==="auto"?n?v.zDepth():0:f.value==="bottom"?-1:f.value==="top"?Xs:0}var s=i(e)-i(t);if(s!==0)return s;function o(v){var f=v.pstyle("z-index-compare");return f.value==="auto"&&v.isNode()?1:0}var l=o(e)-o(t);if(l!==0)return l;var u=e.pstyle("z-index").value-t.pstyle("z-index").value;return u!==0?u:e.poolIndex()-t.poolIndex()},Cn={forEach:function(e,t){if(We(e))for(var a=this.length,n=0;n<a;n++){var i=this[n],s=t?e.apply(t,[i,n,this]):e(i,n,this);if(s===!1)break}return this},toArray:function(){for(var e=[],t=0;t<this.length;t++)e.push(this[t]);return e},slice:function(e,t){var a=[],n=this.length;t==null&&(t=n),e==null&&(e=0),e<0&&(e=n+e),t<0&&(t=n+t);for(var i=e;i>=0&&i<t&&i<n;i++)a.push(this[i]);return this.spawn(a)},size:function(){return this.length},eq:function(e){return this[e]||this.spawn()},first:function(){return this[0]||this.spawn()},last:function(){return this[this.length-1]||this.spawn()},empty:function(){return this.length===0},nonempty:function(){return!this.empty()},sort:function(e){if(!We(e))return this;var t=this.toArray().sort(e);return this.spawn(t)},sortByZIndex:function(){return this.sort($v)},zDepth:function(){var e=this[0];if(e){var t=e._private,a=t.group;if(a==="nodes"){var n=t.data.parent?e.parents().size():0;return e.isParent()?n:Xs-1}else{var i=t.source,s=t.target,o=i.zDepth(),l=s.zDepth();return Math.max(o,l,0)}}}};Cn.each=Cn.forEach;var Jg=function(){var e="undefined",t=(typeof Symbol>"u"?"undefined":rr(Symbol))!=e&&rr(Symbol.iterator)!=e;t&&(Cn[Symbol.iterator]=function(){var a=this,n={value:void 0,done:!1},i=0,s=this.length;return $l({next:function(){return i<s?n.value=a[i++]:(n.value=void 0,n.done=!0),n}},Symbol.iterator,function(){return this})})};Jg();var jg=fr({nodeDimensionsIncludeLabels:!1}),ln={layoutDimensions:function(e){e=jg(e);var t;if(!this.takesUpSpace())t={w:0,h:0};else if(e.nodeDimensionsIncludeLabels){var a=this.boundingBox();t={w:a.w,h:a.h}}else t={w:this.outerWidth(),h:this.outerHeight()};return(t.w===0||t.h===0)&&(t.w=t.h=1),t},layoutPositions:function(e,t,a){var n=this.nodes().filter(function(E){return!E.isParent()}),i=this.cy(),s=t.eles,o=function(C){return C.id()},l=Yt(a,o);e.emit({type:"layoutstart",layout:e}),e.animations=[];var u=function(C,x,k){var S={x:x.x1+x.w/2,y:x.y1+x.h/2},P={x:(k.x-S.x)*C,y:(k.y-S.y)*C};return{x:S.x+P.x,y:S.y+P.y}},v=t.spacingFactor&&t.spacingFactor!==1,f=function(){if(!v)return null;for(var C=Sr(),x=0;x<n.length;x++){var k=n[x],S=l(k,x);pd(C,S.x,S.y)}return C},c=f(),h=Yt(function(E,C){var x=l(E,C);if(v){var k=Math.abs(t.spacingFactor);x=u(k,c,x)}return t.transform!=null&&(x=t.transform(E,x)),x},o);if(t.animate){for(var d=0;d<n.length;d++){var y=n[d],g=h(y,d),p=t.animateFilter==null||t.animateFilter(y,d);if(p){var m=y.animation({position:g,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(m)}else y.position(g)}if(t.fit){var b=i.animation({fit:{boundingBox:s.boundingBoxAt(h),padding:t.padding},duration:t.animationDuration,easing:t.animationEasing});e.animations.push(b)}else if(t.zoom!==void 0&&t.pan!==void 0){var w=i.animation({zoom:t.zoom,pan:t.pan,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(w)}e.animations.forEach(function(E){return E.play()}),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),ea.all(e.animations.map(function(E){return E.promise()})).then(function(){e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e})})}else n.positions(h),t.fit&&i.fit(t.eles,t.padding),t.zoom!=null&&i.zoom(t.zoom),t.pan&&i.pan(t.pan),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e});return this},layout:function(e){var t=this.cy();return t.makeLayout(he({},e,{eles:this}))}};ln.createLayout=ln.makeLayout=ln.layout;function Kv(r,e,t){var a=t._private,n=a.styleCache=a.styleCache||[],i;return(i=n[r])!=null||(i=n[r]=e(t)),i}function _n(r,e){return r=Tt(r),function(a){return Kv(r,e,a)}}function Gn(r,e){r=Tt(r);var t=function(n){return e.call(n)};return function(){var n=this[0];if(n)return Kv(r,t,n)}}var lr={recalculateRenderedStyle:function(e){var t=this.cy(),a=t.renderer(),n=t.styleEnabled();return a&&n&&a.recalculateRenderedStyle(this,e),this},dirtyStyleCache:function(){var e=this.cy(),t=function(i){return i._private.styleCache=null};if(e.hasCompoundNodes()){var a;a=this.spawnSelf().merge(this.descendants()).merge(this.parents()),a.merge(a.connectedEdges()),a.forEach(t)}else this.forEach(function(n){t(n),n.connectedEdges().forEach(t)});return this},updateStyle:function(e){var t=this._private.cy;if(!t.styleEnabled())return this;if(t.batching()){var a=t._private.batchStyleEles;return a.merge(this),this}var n=t.hasCompoundNodes(),i=this;e=!!(e||e===void 0),n&&(i=this.spawnSelf().merge(this.descendants()).merge(this.parents()));var s=i;return e?s.emitAndNotify("style"):s.emit("style"),i.forEach(function(o){return o._private.styleDirty=!0}),this},cleanStyle:function(){var e=this.cy();if(e.styleEnabled())for(var t=0;t<this.length;t++){var a=this[t];a._private.styleDirty&&(a._private.styleDirty=!1,e.style().apply(a))}},parsedStyle:function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,a=this[0],n=a.cy();if(n.styleEnabled()&&a){a._private.styleDirty&&(a._private.styleDirty=!1,n.style().apply(a));var i=a._private.style[e];return i??(t?n.style().getDefaultProperty(e):null)}},numericStyle:function(e){var t=this[0];if(t.cy().styleEnabled()&&t){var a=t.pstyle(e);return a.pfValue!==void 0?a.pfValue:a.value}},numericStyleUnits:function(e){var t=this[0];if(t.cy().styleEnabled()&&t)return t.pstyle(e).units},renderedStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var a=this[0];if(a)return t.style().getRenderedStyle(a,e)},style:function(e,t){var a=this.cy();if(!a.styleEnabled())return this;var n=!1,i=a.style();if(Pe(e)){var s=e;i.applyBypass(this,s,n),this.emitAndNotify("style")}else if(fe(e))if(t===void 0){var o=this[0];return o?i.getStylePropertyValue(o,e):void 0}else i.applyBypass(this,e,t,n),this.emitAndNotify("style");else if(e===void 0){var l=this[0];return l?i.getRawStyle(l):void 0}return this},removeStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var a=!1,n=t.style(),i=this;if(e===void 0)for(var s=0;s<i.length;s++){var o=i[s];n.removeAllBypasses(o,a)}else{e=e.split(/\s+/);for(var l=0;l<i.length;l++){var u=i[l];n.removeBypasses(u,e,a)}}return this.emitAndNotify("style"),this},show:function(){return this.css("display","element"),this},hide:function(){return this.css("display","none"),this},effectiveOpacity:function(){var e=this.cy();if(!e.styleEnabled())return 1;var t=e.hasCompoundNodes(),a=this[0];if(a){var n=a._private,i=a.pstyle("opacity").value;if(!t)return i;var s=n.data.parent?a.parents():null;if(s)for(var o=0;o<s.length;o++){var l=s[o],u=l.pstyle("opacity").value;i=u*i}return i}},transparent:function(){var e=this.cy();if(!e.styleEnabled())return!1;var t=this[0],a=t.cy().hasCompoundNodes();if(t)return a?t.effectiveOpacity()===0:t.pstyle("opacity").value===0},backgrounding:function(){var e=this.cy();if(!e.styleEnabled())return!1;var t=this[0];return!!t._private.backgrounding}};function hs(r,e){var t=r._private,a=t.data.parent?r.parents():null;if(a)for(var n=0;n<a.length;n++){var i=a[n];if(!e(i))return!1}return!0}function oo(r){var e=r.ok,t=r.edgeOkViaNode||r.ok,a=r.parentOk||r.ok;return function(){var n=this.cy();if(!n.styleEnabled())return!0;var i=this[0],s=n.hasCompoundNodes();if(i){var o=i._private;if(!e(i))return!1;if(i.isNode())return!s||hs(i,a);var l=o.source,u=o.target;return t(l)&&(!s||hs(l,t))&&(l===u||t(u)&&(!s||hs(u,t)))}}}var ra=_n("eleTakesUpSpace",function(r){return r.pstyle("display").value==="element"&&r.width()!==0&&(r.isNode()?r.height()!==0:!0)});lr.takesUpSpace=Gn("takesUpSpace",oo({ok:ra}));var ep=_n("eleInteractive",function(r){return r.pstyle("events").value==="yes"&&r.pstyle("visibility").value==="visible"&&ra(r)}),rp=_n("parentInteractive",function(r){return r.pstyle("visibility").value==="visible"&&ra(r)});lr.interactive=Gn("interactive",oo({ok:ep,parentOk:rp,edgeOkViaNode:ra}));lr.noninteractive=function(){var r=this[0];if(r)return!r.interactive()};var tp=_n("eleVisible",function(r){return r.pstyle("visibility").value==="visible"&&r.pstyle("opacity").pfValue!==0&&ra(r)}),ap=ra;lr.visible=Gn("visible",oo({ok:tp,edgeOkViaNode:ap}));lr.hidden=function(){var r=this[0];if(r)return!r.visible()};lr.isBundledBezier=Gn("isBundledBezier",function(){return this.cy().styleEnabled()?!this.removed()&&this.pstyle("curve-style").value==="bezier"&&this.takesUpSpace():!1});lr.bypass=lr.css=lr.style;lr.renderedCss=lr.renderedStyle;lr.removeBypass=lr.removeCss=lr.removeStyle;lr.pstyle=lr.parsedStyle;var at={};function ll(r){return function(){var e=arguments,t=[];if(e.length===2){var a=e[0],n=e[1];this.on(r.event,a,n)}else if(e.length===1&&We(e[0])){var i=e[0];this.on(r.event,i)}else if(e.length===0||e.length===1&&Fe(e[0])){for(var s=e.length===1?e[0]:null,o=0;o<this.length;o++){var l=this[o],u=!r.ableField||l._private[r.ableField],v=l._private[r.field]!=r.value;if(r.overrideAble){var f=r.overrideAble(l);if(f!==void 0&&(u=f,!f))return this}u&&(l._private[r.field]=r.value,v&&t.push(l))}var c=this.spawn(t);c.updateStyle(),c.emit(r.event),s&&c.emit(s)}return this}}function ta(r){at[r.field]=function(){var e=this[0];if(e){if(r.overrideField){var t=r.overrideField(e);if(t!==void 0)return t}return e._private[r.field]}},at[r.on]=ll({event:r.on,field:r.field,ableField:r.ableField,overrideAble:r.overrideAble,value:!0}),at[r.off]=ll({event:r.off,field:r.field,ableField:r.ableField,overrideAble:r.overrideAble,value:!1})}ta({field:"locked",overrideField:function(e){return e.cy().autolock()?!0:void 0},on:"lock",off:"unlock"});ta({field:"grabbable",overrideField:function(e){return e.cy().autoungrabify()||e.pannable()?!1:void 0},on:"grabify",off:"ungrabify"});ta({field:"selected",ableField:"selectable",overrideAble:function(e){return e.cy().autounselectify()?!1:void 0},on:"select",off:"unselect"});ta({field:"selectable",overrideField:function(e){return e.cy().autounselectify()?!1:void 0},on:"selectify",off:"unselectify"});at.deselect=at.unselect;at.grabbed=function(){var r=this[0];if(r)return r._private.grabbed};ta({field:"active",on:"activate",off:"unactivate"});ta({field:"pannable",on:"panify",off:"unpanify"});at.inactive=function(){var r=this[0];if(r)return!r._private.active};var hr={},vl=function(e){return function(a){for(var n=this,i=[],s=0;s<n.length;s++){var o=n[s];if(o.isNode()){for(var l=!1,u=o.connectedEdges(),v=0;v<u.length;v++){var f=u[v],c=f.source(),h=f.target();if(e.noIncomingEdges&&h===o&&c!==o||e.noOutgoingEdges&&c===o&&h!==o){l=!0;break}}l||i.push(o)}}return this.spawn(i,!0).filter(a)}},fl=function(e){return function(t){for(var a=this,n=[],i=0;i<a.length;i++){var s=a[i];if(s.isNode())for(var o=s.connectedEdges(),l=0;l<o.length;l++){var u=o[l],v=u.source(),f=u.target();e.outgoing&&v===s?(n.push(u),n.push(f)):e.incoming&&f===s&&(n.push(u),n.push(v))}}return this.spawn(n,!0).filter(t)}},cl=function(e){return function(t){for(var a=this,n=[],i={};;){var s=e.outgoing?a.outgoers():a.incomers();if(s.length===0)break;for(var o=!1,l=0;l<s.length;l++){var u=s[l],v=u.id();i[v]||(i[v]=!0,n.push(u),o=!0)}if(!o)break;a=s}return this.spawn(n,!0).filter(t)}};hr.clearTraversalCache=function(){for(var r=0;r<this.length;r++)this[r]._private.traversalCache=null};he(hr,{roots:vl({noIncomingEdges:!0}),leaves:vl({noOutgoingEdges:!0}),outgoers:Pr(fl({outgoing:!0}),"outgoers"),successors:cl({outgoing:!0}),incomers:Pr(fl({incoming:!0}),"incomers"),predecessors:cl({})});he(hr,{neighborhood:Pr(function(r){for(var e=[],t=this.nodes(),a=0;a<t.length;a++)for(var n=t[a],i=n.connectedEdges(),s=0;s<i.length;s++){var o=i[s],l=o.source(),u=o.target(),v=n===l?u:l;v.length>0&&e.push(v[0]),e.push(o[0])}return this.spawn(e,!0).filter(r)},"neighborhood"),closedNeighborhood:function(e){return this.neighborhood().add(this).filter(e)},openNeighborhood:function(e){return this.neighborhood(e)}});hr.neighbourhood=hr.neighborhood;hr.closedNeighbourhood=hr.closedNeighborhood;hr.openNeighbourhood=hr.openNeighborhood;he(hr,{source:Pr(function(e){var t=this[0],a;return t&&(a=t._private.source||t.cy().collection()),a&&e?a.filter(e):a},"source"),target:Pr(function(e){var t=this[0],a;return t&&(a=t._private.target||t.cy().collection()),a&&e?a.filter(e):a},"target"),sources:dl({attr:"source"}),targets:dl({attr:"target"})});function dl(r){return function(t){for(var a=[],n=0;n<this.length;n++){var i=this[n],s=i._private[r.attr];s&&a.push(s)}return this.spawn(a,!0).filter(t)}}he(hr,{edgesWith:Pr(hl(),"edgesWith"),edgesTo:Pr(hl({thisIsSrc:!0}),"edgesTo")});function hl(r){return function(t){var a=[],n=this._private.cy,i=r||{};fe(t)&&(t=n.$(t));for(var s=0;s<t.length;s++)for(var o=t[s]._private.edges,l=0;l<o.length;l++){var u=o[l],v=u._private.data,f=this.hasElementWithId(v.source)&&t.hasElementWithId(v.target),c=t.hasElementWithId(v.source)&&this.hasElementWithId(v.target),h=f||c;h&&((i.thisIsSrc||i.thisIsTgt)&&(i.thisIsSrc&&!f||i.thisIsTgt&&!c)||a.push(u))}return this.spawn(a,!0)}}he(hr,{connectedEdges:Pr(function(r){for(var e=[],t=this,a=0;a<t.length;a++){var n=t[a];if(n.isNode())for(var i=n._private.edges,s=0;s<i.length;s++){var o=i[s];e.push(o)}}return this.spawn(e,!0).filter(r)},"connectedEdges"),connectedNodes:Pr(function(r){for(var e=[],t=this,a=0;a<t.length;a++){var n=t[a];n.isEdge()&&(e.push(n.source()[0]),e.push(n.target()[0]))}return this.spawn(e,!0).filter(r)},"connectedNodes"),parallelEdges:Pr(gl(),"parallelEdges"),codirectedEdges:Pr(gl({codirected:!0}),"codirectedEdges")});function gl(r){var e={codirected:!1};return r=he({},e,r),function(a){for(var n=[],i=this.edges(),s=r,o=0;o<i.length;o++)for(var l=i[o],u=l._private,v=u.source,f=v._private.data.id,c=u.data.target,h=v._private.edges,d=0;d<h.length;d++){var y=h[d],g=y._private.data,p=g.target,m=g.source,b=p===c&&m===f,w=f===p&&c===m;(s.codirected&&b||!s.codirected&&(b||w))&&n.push(y)}return this.spawn(n,!0).filter(a)}}he(hr,{components:function(e){var t=this,a=t.cy(),n=a.collection(),i=e==null?t.nodes():e.nodes(),s=[];e!=null&&i.empty()&&(i=e.sources());var o=function(v,f){n.merge(v),i.unmerge(v),f.merge(v)};if(i.empty())return t.spawn();var l=function(){var v=a.collection();s.push(v);var f=i[0];o(f,v),t.bfs({directed:!1,roots:f,visit:function(h){return o(h,v)}}),v.forEach(function(c){c.connectedEdges().forEach(function(h){t.has(h)&&v.has(h.source())&&v.has(h.target())&&v.merge(h)})})};do l();while(i.length>0);return s},component:function(){var e=this[0];return e.cy().mutableElements().components(e)[0]}});hr.componentsOf=hr.components;var vr=function(e,t){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(e===void 0){He("A collection must have a reference to the core");return}var i=new Kr,s=!1;if(!t)t=[];else if(t.length>0&&Pe(t[0])&&!Ra(t[0])){s=!0;for(var o=[],l=new jt,u=0,v=t.length;u<v;u++){var f=t[u];f.data==null&&(f.data={});var c=f.data;if(c.id==null)c.id=lv();else if(e.hasElementWithId(c.id)||l.has(c.id))continue;var h=new Mn(e,f,!1);o.push(h),l.add(c.id)}t=o}this.length=0;for(var d=0,y=t.length;d<y;d++){var g=t[d][0];if(g!=null){var p=g._private.data.id;(!a||!i.has(p))&&(a&&i.set(p,{index:this.length,ele:g}),this[this.length]=g,this.length++)}}this._private={eles:this,cy:e,get map(){return this.lazyMap==null&&this.rebuildMap(),this.lazyMap},set map(m){this.lazyMap=m},rebuildMap:function(){for(var b=this.lazyMap=new Kr,w=this.eles,E=0;E<w.length;E++){var C=w[E];b.set(C.id(),{index:E,ele:C})}}},a&&(this._private.map=i),s&&!n&&this.restore()},qe=Mn.prototype=vr.prototype=Object.create(Array.prototype);qe.instanceString=function(){return"collection"};qe.spawn=function(r,e){return new vr(this.cy(),r,e)};qe.spawnSelf=function(){return this.spawn(this)};qe.cy=function(){return this._private.cy};qe.renderer=function(){return this._private.cy.renderer()};qe.element=function(){return this[0]};qe.collection=function(){return Xl(this)?this:new vr(this._private.cy,[this])};qe.unique=function(){return new vr(this._private.cy,this,!0)};qe.hasElementWithId=function(r){return r=""+r,this._private.map.has(r)};qe.getElementById=function(r){r=""+r;var e=this._private.cy,t=this._private.map.get(r);return t?t.ele:new vr(e)};qe.$id=qe.getElementById;qe.poolIndex=function(){var r=this._private.cy,e=r._private.elements,t=this[0]._private.data.id;return e._private.map.get(t).index};qe.indexOf=function(r){var e=r[0]._private.data.id;return this._private.map.get(e).index};qe.indexOfId=function(r){return r=""+r,this._private.map.get(r).index};qe.json=function(r){var e=this.element(),t=this.cy();if(e==null&&r)return this;if(e!=null){var a=e._private;if(Pe(r)){if(t.startBatch(),r.data){e.data(r.data);var n=a.data;if(e.isEdge()){var i=!1,s={},o=r.data.source,l=r.data.target;o!=null&&o!=n.source&&(s.source=""+o,i=!0),l!=null&&l!=n.target&&(s.target=""+l,i=!0),i&&(e=e.move(s))}else{var u="parent"in r.data,v=r.data.parent;u&&(v!=null||n.parent!=null)&&v!=n.parent&&(v===void 0&&(v=null),v!=null&&(v=""+v),e=e.move({parent:v}))}}r.position&&e.position(r.position);var f=function(y,g,p){var m=r[y];m!=null&&m!==a[y]&&(m?e[g]():e[p]())};return f("removed","remove","restore"),f("selected","select","unselect"),f("selectable","selectify","unselectify"),f("locked","lock","unlock"),f("grabbable","grabify","ungrabify"),f("pannable","panify","unpanify"),r.classes!=null&&e.classes(r.classes),t.endBatch(),this}else if(r===void 0){var c={data:qr(a.data),position:qr(a.position),group:a.group,removed:a.removed,selected:a.selected,selectable:a.selectable,locked:a.locked,grabbable:a.grabbable,pannable:a.pannable,classes:null};c.classes="";var h=0;return a.classes.forEach(function(d){return c.classes+=h++===0?d:" "+d}),c}}};qe.jsons=function(){for(var r=[],e=0;e<this.length;e++){var t=this[e],a=t.json();r.push(a)}return r};qe.clone=function(){for(var r=this.cy(),e=[],t=0;t<this.length;t++){var a=this[t],n=a.json(),i=new Mn(r,n,!1);e.push(i)}return new vr(r,e)};qe.copy=qe.clone;qe.restore=function(){for(var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,t=this,a=t.cy(),n=a._private,i=[],s=[],o,l=0,u=t.length;l<u;l++){var v=t[l];e&&!v.removed()||(v.isNode()?i.push(v):s.push(v))}o=i.concat(s);var f,c=function(){o.splice(f,1),f--};for(f=0;f<o.length;f++){var h=o[f],d=h._private,y=d.data;if(h.clearTraversalCache(),!(!e&&!d.removed)){if(y.id===void 0)y.id=lv();else if(te(y.id))y.id=""+y.id;else if(nt(y.id)||!fe(y.id)){He("Can not create element with invalid string ID `"+y.id+"`"),c();continue}else if(a.hasElementWithId(y.id)){He("Can not create second element with ID `"+y.id+"`"),c();continue}}var g=y.id;if(h.isNode()){var p=d.position;p.x==null&&(p.x=0),p.y==null&&(p.y=0)}if(h.isEdge()){for(var m=h,b=["source","target"],w=b.length,E=!1,C=0;C<w;C++){var x=b[C],k=y[x];te(k)&&(k=y[x]=""+y[x]),k==null||k===""?(He("Can not create edge `"+g+"` with unspecified "+x),E=!0):a.hasElementWithId(k)||(He("Can not create edge `"+g+"` with nonexistant "+x+" `"+k+"`"),E=!0)}if(E){c();continue}var S=a.getElementById(y.source),P=a.getElementById(y.target);S.same(P)?S._private.edges.push(m):(S._private.edges.push(m),P._private.edges.push(m)),m._private.source=S,m._private.target=P}d.map=new Kr,d.map.set(g,{ele:h,index:0}),d.removed=!1,e&&a.addToPool(h)}for(var D=0;D<i.length;D++){var A=i[D],B=A._private.data;te(B.parent)&&(B.parent=""+B.parent);var R=B.parent,M=R!=null;if(M||A._private.parent){var I=A._private.parent?a.collection().merge(A._private.parent):a.getElementById(R);if(I.empty())B.parent=void 0;else if(I[0].removed())Le("Node added with missing parent, reference to parent removed"),B.parent=void 0,A._private.parent=null;else{for(var L=!1,O=I;!O.empty();){if(A.same(O)){L=!0,B.parent=void 0;break}O=O.parent()}L||(I[0]._private.children.push(A),A._private.parent=I[0],n.hasCompoundNodes=!0)}}}if(o.length>0){for(var V=o.length===t.length?t:new vr(a,o),G=0;G<V.length;G++){var N=V[G];N.isNode()||(N.parallelEdges().clearTraversalCache(),N.source().clearTraversalCache(),N.target().clearTraversalCache())}var F;n.hasCompoundNodes?F=a.collection().merge(V).merge(V.connectedNodes()).merge(V.parent()):F=V,F.dirtyCompoundBoundsCache().dirtyBoundingBoxCache().updateStyle(r),r?V.emitAndNotify("add"):e&&V.emit("add")}return t};qe.removed=function(){var r=this[0];return r&&r._private.removed};qe.inside=function(){var r=this[0];return r&&!r._private.removed};qe.remove=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,t=this,a=[],n={},i=t._private.cy;function s(R){for(var M=R._private.edges,I=0;I<M.length;I++)l(M[I])}function o(R){for(var M=R._private.children,I=0;I<M.length;I++)l(M[I])}function l(R){var M=n[R.id()];e&&R.removed()||M||(n[R.id()]=!0,R.isNode()?(a.push(R),s(R),o(R)):a.unshift(R))}for(var u=0,v=t.length;u<v;u++){var f=t[u];l(f)}function c(R,M){var I=R._private.edges;it(I,M),R.clearTraversalCache()}function h(R){R.clearTraversalCache()}var d=[];d.ids={};function y(R,M){M=M[0],R=R[0];var I=R._private.children,L=R.id();it(I,M),M._private.parent=null,d.ids[L]||(d.ids[L]=!0,d.push(R))}t.dirtyCompoundBoundsCache(),e&&i.removeFromPool(a);for(var g=0;g<a.length;g++){var p=a[g];if(p.isEdge()){var m=p.source()[0],b=p.target()[0];c(m,p),c(b,p);for(var w=p.parallelEdges(),E=0;E<w.length;E++){var C=w[E];h(C),C.isBundledBezier()&&C.dirtyBoundingBoxCache()}}else{var x=p.parent();x.length!==0&&y(x,p)}e&&(p._private.removed=!0)}var k=i._private.elements;i._private.hasCompoundNodes=!1;for(var S=0;S<k.length;S++){var P=k[S];if(P.isParent()){i._private.hasCompoundNodes=!0;break}}var D=new vr(this.cy(),a);D.size()>0&&(r?D.emitAndNotify("remove"):e&&D.emit("remove"));for(var A=0;A<d.length;A++){var B=d[A];(!e||!B.removed())&&B.updateStyle()}return D};qe.move=function(r){var e=this._private.cy,t=this,a=!1,n=!1,i=function(d){return d==null?d:""+d};if(r.source!==void 0||r.target!==void 0){var s=i(r.source),o=i(r.target),l=s!=null&&e.hasElementWithId(s),u=o!=null&&e.hasElementWithId(o);(l||u)&&(e.batch(function(){t.remove(a,n),t.emitAndNotify("moveout");for(var h=0;h<t.length;h++){var d=t[h],y=d._private.data;d.isEdge()&&(l&&(y.source=s),u&&(y.target=o))}t.restore(a,n)}),t.emitAndNotify("move"))}else if(r.parent!==void 0){var v=i(r.parent),f=v===null||e.hasElementWithId(v);if(f){var c=v===null?void 0:v;e.batch(function(){var h=t.remove(a,n);h.emitAndNotify("moveout");for(var d=0;d<t.length;d++){var y=t[d],g=y._private.data;y.isNode()&&(g.parent=c)}h.restore(a,n)}),t.emitAndNotify("move")}}return this};[wv,hg,un,tt,Qt,Ag,Vn,$g,Wv,Uv,Qg,Cn,ln,lr,at,hr].forEach(function(r){he(qe,r)});var np={add:function(e){var t,a=this;if(Dr(e)){var n=e;if(n._private.cy===a)t=n.restore();else{for(var i=[],s=0;s<n.length;s++){var o=n[s];i.push(o.json())}t=new vr(a,i)}}else if(Fe(e)){var l=e;t=new vr(a,l)}else if(Pe(e)&&(Fe(e.nodes)||Fe(e.edges))){for(var u=e,v=[],f=["nodes","edges"],c=0,h=f.length;c<h;c++){var d=f[c],y=u[d];if(Fe(y))for(var g=0,p=y.length;g<p;g++){var m=he({group:d},y[g]);v.push(m)}}t=new vr(a,v)}else{var b=e;t=new Mn(a,b).collection()}return t},remove:function(e){if(!Dr(e)){if(fe(e)){var t=e;e=this.$(t)}}return e.remove()}};/*! Bezier curve function generator. Copyright Gaetan Renaudeau. MIT License: http://en.wikipedia.org/wiki/MIT_License */function ip(r,e,t,a){var n=4,i=.001,s=1e-7,o=10,l=11,u=1/(l-1),v=typeof Float32Array<"u";if(arguments.length!==4)return!1;for(var f=0;f<4;++f)if(typeof arguments[f]!="number"||isNaN(arguments[f])||!isFinite(arguments[f]))return!1;r=Math.min(r,1),t=Math.min(t,1),r=Math.max(r,0),t=Math.max(t,0);var c=v?new Float32Array(l):new Array(l);function h(P,D){return 1-3*D+3*P}function d(P,D){return 3*D-6*P}function y(P){return 3*P}function g(P,D,A){return((h(D,A)*P+d(D,A))*P+y(D))*P}function p(P,D,A){return 3*h(D,A)*P*P+2*d(D,A)*P+y(D)}function m(P,D){for(var A=0;A<n;++A){var B=p(D,r,t);if(B===0)return D;var R=g(D,r,t)-P;D-=R/B}return D}function b(){for(var P=0;P<l;++P)c[P]=g(P*u,r,t)}function w(P,D,A){var B,R,M=0;do R=D+(A-D)/2,B=g(R,r,t)-P,B>0?A=R:D=R;while(Math.abs(B)>s&&++M<o);return R}function E(P){for(var D=0,A=1,B=l-1;A!==B&&c[A]<=P;++A)D+=u;--A;var R=(P-c[A])/(c[A+1]-c[A]),M=D+R*u,I=p(M,r,t);return I>=i?m(P,M):I===0?M:w(P,D,D+u)}var C=!1;function x(){C=!0,(r!==e||t!==a)&&b()}var k=function(D){return C||x(),r===e&&t===a?D:D===0?0:D===1?1:g(E(D),e,a)};k.getControlPoints=function(){return[{x:r,y:e},{x:t,y:a}]};var S="generateBezier("+[r,e,t,a]+")";return k.toString=function(){return S},k}/*! Runge-Kutta spring physics function generator. Adapted from Framer.js, copyright Koen Bok. MIT License: http://en.wikipedia.org/wiki/MIT_License */var sp=function(){function r(a){return-a.tension*a.x-a.friction*a.v}function e(a,n,i){var s={x:a.x+i.dx*n,v:a.v+i.dv*n,tension:a.tension,friction:a.friction};return{dx:s.v,dv:r(s)}}function t(a,n){var i={dx:a.v,dv:r(a)},s=e(a,n*.5,i),o=e(a,n*.5,s),l=e(a,n,o),u=1/6*(i.dx+2*(s.dx+o.dx)+l.dx),v=1/6*(i.dv+2*(s.dv+o.dv)+l.dv);return a.x=a.x+u*n,a.v=a.v+v*n,a}return function a(n,i,s){var o={x:-1,v:0,tension:null,friction:null},l=[0],u=0,v=1/1e4,f=16/1e3,c,h,d;for(n=parseFloat(n)||500,i=parseFloat(i)||20,s=s||null,o.tension=n,o.friction=i,c=s!==null,c?(u=a(n,i),h=u/s*f):h=f;d=t(d||o,h),l.push(1+d.x),u+=16,Math.abs(d.x)>v&&Math.abs(d.v)>v;);return c?function(y){return l[y*(l.length-1)|0]}:u}}(),Ve=function(e,t,a,n){var i=ip(e,t,a,n);return function(s,o,l){return s+(o-s)*i(l)}},vn={linear:function(e,t,a){return e+(t-e)*a},ease:Ve(.25,.1,.25,1),"ease-in":Ve(.42,0,1,1),"ease-out":Ve(0,0,.58,1),"ease-in-out":Ve(.42,0,.58,1),"ease-in-sine":Ve(.47,0,.745,.715),"ease-out-sine":Ve(.39,.575,.565,1),"ease-in-out-sine":Ve(.445,.05,.55,.95),"ease-in-quad":Ve(.55,.085,.68,.53),"ease-out-quad":Ve(.25,.46,.45,.94),"ease-in-out-quad":Ve(.455,.03,.515,.955),"ease-in-cubic":Ve(.55,.055,.675,.19),"ease-out-cubic":Ve(.215,.61,.355,1),"ease-in-out-cubic":Ve(.645,.045,.355,1),"ease-in-quart":Ve(.895,.03,.685,.22),"ease-out-quart":Ve(.165,.84,.44,1),"ease-in-out-quart":Ve(.77,0,.175,1),"ease-in-quint":Ve(.755,.05,.855,.06),"ease-out-quint":Ve(.23,1,.32,1),"ease-in-out-quint":Ve(.86,0,.07,1),"ease-in-expo":Ve(.95,.05,.795,.035),"ease-out-expo":Ve(.19,1,.22,1),"ease-in-out-expo":Ve(1,0,0,1),"ease-in-circ":Ve(.6,.04,.98,.335),"ease-out-circ":Ve(.075,.82,.165,1),"ease-in-out-circ":Ve(.785,.135,.15,.86),spring:function(e,t,a){if(a===0)return vn.linear;var n=sp(e,t,a);return function(i,s,o){return i+(s-i)*n(o)}},"cubic-bezier":Ve};function pl(r,e,t,a,n){if(a===1||e===t)return t;var i=n(e,t,a);return r==null||((r.roundValue||r.color)&&(i=Math.round(i)),r.min!==void 0&&(i=Math.max(i,r.min)),r.max!==void 0&&(i=Math.min(i,r.max))),i}function yl(r,e){return r.pfValue!=null||r.value!=null?r.pfValue!=null&&(e==null||e.type.units!=="%")?r.pfValue:r.value:r}function It(r,e,t,a,n){var i=n!=null?n.type:null;t<0?t=0:t>1&&(t=1);var s=yl(r,n),o=yl(e,n);if(te(s)&&te(o))return pl(i,s,o,t,a);if(Fe(s)&&Fe(o)){for(var l=[],u=0;u<o.length;u++){var v=s[u],f=o[u];if(v!=null&&f!=null){var c=pl(i,v,f,t,a);l.push(c)}else l.push(f)}return l}}function op(r,e,t,a){var n=!a,i=r._private,s=e._private,o=s.easing,l=s.startTime,u=a?r:r.cy(),v=u.style();if(!s.easingImpl)if(o==null)s.easingImpl=vn.linear;else{var f;if(fe(o)){var c=v.parse("transition-timing-function",o);f=c.value}else f=o;var h,d;fe(f)?(h=f,d=[]):(h=f[1],d=f.slice(2).map(function(V){return+V})),d.length>0?(h==="spring"&&d.push(s.duration),s.easingImpl=vn[h].apply(null,d)):s.easingImpl=vn[h]}var y=s.easingImpl,g;if(s.duration===0?g=1:g=(t-l)/s.duration,s.applying&&(g=s.progress),g<0?g=0:g>1&&(g=1),s.delay==null){var p=s.startPosition,m=s.position;if(m&&n&&!r.locked()){var b={};va(p.x,m.x)&&(b.x=It(p.x,m.x,g,y)),va(p.y,m.y)&&(b.y=It(p.y,m.y,g,y)),r.position(b)}var w=s.startPan,E=s.pan,C=i.pan,x=E!=null&&a;x&&(va(w.x,E.x)&&(C.x=It(w.x,E.x,g,y)),va(w.y,E.y)&&(C.y=It(w.y,E.y,g,y)),r.emit("pan"));var k=s.startZoom,S=s.zoom,P=S!=null&&a;P&&(va(k,S)&&(i.zoom=Ca(i.minZoom,It(k,S,g,y),i.maxZoom)),r.emit("zoom")),(x||P)&&r.emit("viewport");var D=s.style;if(D&&D.length>0&&n){for(var A=0;A<D.length;A++){var B=D[A],R=B.name,M=B,I=s.startStyle[R],L=v.properties[I.name],O=It(I,M,g,y,L);v.overrideBypass(r,R,O)}r.emit("style")}}return s.progress=g,g}function va(r,e){return r==null||e==null?!1:te(r)&&te(e)?!0:!!(r&&e)}function up(r,e,t,a){var n=e._private;n.started=!0,n.startTime=t-n.progress*n.duration}function ml(r,e){var t=e._private.aniEles,a=[];function n(v,f){var c=v._private,h=c.animation.current,d=c.animation.queue,y=!1;if(h.length===0){var g=d.shift();g&&h.push(g)}for(var p=function(C){for(var x=C.length-1;x>=0;x--){var k=C[x];k()}C.splice(0,C.length)},m=h.length-1;m>=0;m--){var b=h[m],w=b._private;if(w.stopped){h.splice(m,1),w.hooked=!1,w.playing=!1,w.started=!1,p(w.frames);continue}!w.playing&&!w.applying||(w.playing&&w.applying&&(w.applying=!1),w.started||up(v,b,r),op(v,b,r,f),w.applying&&(w.applying=!1),p(w.frames),w.step!=null&&w.step(r),b.completed()&&(h.splice(m,1),w.hooked=!1,w.playing=!1,w.started=!1,p(w.completes)),y=!0)}return!f&&h.length===0&&d.length===0&&a.push(v),y}for(var i=!1,s=0;s<t.length;s++){var o=t[s],l=n(o);i=i||l}var u=n(e,!0);(i||u)&&(t.length>0?e.notify("draw",t):e.notify("draw")),t.unmerge(a),e.emit("step")}var lp={animate:Me.animate(),animation:Me.animation(),animated:Me.animated(),clearQueue:Me.clearQueue(),delay:Me.delay(),delayAnimation:Me.delayAnimation(),stop:Me.stop(),addToAnimationPool:function(e){var t=this;t.styleEnabled()&&t._private.aniEles.merge(e)},stopAnimationLoop:function(){this._private.animationsRunning=!1},startAnimationLoop:function(){var e=this;if(e._private.animationsRunning=!0,!e.styleEnabled())return;function t(){e._private.animationsRunning&&mn(function(i){ml(i,e),t()})}var a=e.renderer();a&&a.beforeRender?a.beforeRender(function(i,s){ml(s,e)},a.beforeRenderPriorities.animations):t()}},vp={qualifierCompare:function(e,t){return e==null||t==null?e==null&&t==null:e.sameText(t)},eventMatches:function(e,t,a){var n=t.qualifier;return n!=null?e!==a.target&&Ra(a.target)&&n.matches(a.target):!0},addEventFields:function(e,t){t.cy=e,t.target=e},callbackContext:function(e,t,a){return t.qualifier!=null?a.target:e}},en=function(e){return fe(e)?new ot(e):e},Yv={createEmitter:function(){var e=this._private;return e.emitter||(e.emitter=new qn(vp,this)),this},emitter:function(){return this._private.emitter},on:function(e,t,a){return this.emitter().on(e,en(t),a),this},removeListener:function(e,t,a){return this.emitter().removeListener(e,en(t),a),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},one:function(e,t,a){return this.emitter().one(e,en(t),a),this},once:function(e,t,a){return this.emitter().one(e,en(t),a),this},emit:function(e,t){return this.emitter().emit(e,t),this},emitAndNotify:function(e,t){return this.emit(e),this.notify(e,t),this}};Me.eventAliasesOn(Yv);var Is={png:function(e){var t=this._private.renderer;return e=e||{},t.png(e)},jpg:function(e){var t=this._private.renderer;return e=e||{},e.bg=e.bg||"#fff",t.jpg(e)}};Is.jpeg=Is.jpg;var fn={layout:function(e){var t=this;if(e==null){He("Layout options must be specified to make a layout");return}if(e.name==null){He("A `name` must be specified to make a layout");return}var a=e.name,n=t.extension("layout",a);if(n==null){He("No such layout `"+a+"` found.  Did you forget to import it and `cytoscape.use()` it?");return}var i;fe(e.eles)?i=t.$(e.eles):i=e.eles!=null?e.eles:t.$();var s=new n(he({},e,{cy:t,eles:i}));return s}};fn.createLayout=fn.makeLayout=fn.layout;var fp={notify:function(e,t){var a=this._private;if(this.batching()){a.batchNotifications=a.batchNotifications||{};var n=a.batchNotifications[e]=a.batchNotifications[e]||this.collection();t!=null&&n.merge(t);return}if(a.notificationsEnabled){var i=this.renderer();this.destroyed()||!i||i.notify(e,t)}},notifications:function(e){var t=this._private;return e===void 0?t.notificationsEnabled:(t.notificationsEnabled=!!e,this)},noNotifications:function(e){this.notifications(!1),e(),this.notifications(!0)},batching:function(){return this._private.batchCount>0},startBatch:function(){var e=this._private;return e.batchCount==null&&(e.batchCount=0),e.batchCount===0&&(e.batchStyleEles=this.collection(),e.batchNotifications={}),e.batchCount++,this},endBatch:function(){var e=this._private;if(e.batchCount===0)return this;if(e.batchCount--,e.batchCount===0){e.batchStyleEles.updateStyle();var t=this.renderer();Object.keys(e.batchNotifications).forEach(function(a){var n=e.batchNotifications[a];n.empty()?t.notify(a):t.notify(a,n)})}return this},batch:function(e){return this.startBatch(),e(),this.endBatch(),this},batchData:function(e){var t=this;return this.batch(function(){for(var a=Object.keys(e),n=0;n<a.length;n++){var i=a[n],s=e[i],o=t.getElementById(i);o.data(s)}})}},cp=fr({hideEdgesOnViewport:!1,textureOnViewport:!1,motionBlur:!1,motionBlurOpacity:.05,pixelRatio:void 0,desktopTapThreshold:4,touchTapThreshold:8,wheelSensitivity:1,debug:!1,showFps:!1,webgl:!1,webglDebug:!1,webglDebugShowAtlases:!1,webglTexSize:2048,webglTexRows:36,webglTexRowsNodes:18,webglBatchSize:2048,webglTexPerBatch:14,webglBgColor:[255,255,255]}),Os={renderTo:function(e,t,a,n){var i=this._private.renderer;return i.renderTo(e,t,a,n),this},renderer:function(){return this._private.renderer},forceRender:function(){return this.notify("draw"),this},resize:function(){return this.invalidateSize(),this.emitAndNotify("resize"),this},initRenderer:function(e){var t=this,a=t.extension("renderer",e.name);if(a==null){He("Can not initialise: No such renderer `".concat(e.name,"` found. Did you forget to import it and `cytoscape.use()` it?"));return}e.wheelSensitivity!==void 0&&Le("You have set a custom wheel sensitivity.  This will make your app zoom unnaturally when using mainstream mice.  You should change this value from the default only if you can guarantee that all your users will use the same hardware and OS configuration as your current machine.");var n=cp(e);n.cy=t,t._private.renderer=new a(n),this.notify("init")},destroyRenderer:function(){var e=this;e.notify("destroy");var t=e.container();if(t)for(t._cyreg=null;t.childNodes.length>0;)t.removeChild(t.childNodes[0]);e._private.renderer=null,e.mutableElements().forEach(function(a){var n=a._private;n.rscratch={},n.rstyle={},n.animation.current=[],n.animation.queue=[]})},onRender:function(e){return this.on("render",e)},offRender:function(e){return this.off("render",e)}};Os.invalidateDimensions=Os.resize;var cn={collection:function(e,t){return fe(e)?this.$(e):Dr(e)?e.collection():Fe(e)?(t||(t={}),new vr(this,e,t.unique,t.removed)):new vr(this)},nodes:function(e){var t=this.$(function(a){return a.isNode()});return e?t.filter(e):t},edges:function(e){var t=this.$(function(a){return a.isEdge()});return e?t.filter(e):t},$:function(e){var t=this._private.elements;return e?t.filter(e):t.spawnSelf()},mutableElements:function(){return this._private.elements}};cn.elements=cn.filter=cn.$;var sr={},ya="t",dp="f";sr.apply=function(r){for(var e=this,t=e._private,a=t.cy,n=a.collection(),i=0;i<r.length;i++){var s=r[i],o=e.getContextMeta(s);if(!o.empty){var l=e.getContextStyle(o),u=e.applyContextStyle(o,l,s);s._private.appliedInitStyle?e.updateTransitions(s,u.diffProps):s._private.appliedInitStyle=!0;var v=e.updateStyleHints(s);v&&n.push(s)}}return n};sr.getPropertiesDiff=function(r,e){var t=this,a=t._private.propDiffs=t._private.propDiffs||{},n=r+"-"+e,i=a[n];if(i)return i;for(var s=[],o={},l=0;l<t.length;l++){var u=t[l],v=r[l]===ya,f=e[l]===ya,c=v!==f,h=u.mappedProperties.length>0;if(c||f&&h){var d=void 0;c&&h||c?d=u.properties:h&&(d=u.mappedProperties);for(var y=0;y<d.length;y++){for(var g=d[y],p=g.name,m=!1,b=l+1;b<t.length;b++){var w=t[b],E=e[b]===ya;if(E&&(m=w.properties[g.name]!=null,m))break}!o[p]&&!m&&(o[p]=!0,s.push(p))}}}return a[n]=s,s};sr.getContextMeta=function(r){for(var e=this,t="",a,n=r._private.styleCxtKey||"",i=0;i<e.length;i++){var s=e[i],o=s.selector&&s.selector.matches(r);o?t+=ya:t+=dp}return a=e.getPropertiesDiff(n,t),r._private.styleCxtKey=t,{key:t,diffPropNames:a,empty:a.length===0}};sr.getContextStyle=function(r){var e=r.key,t=this,a=this._private.contextStyles=this._private.contextStyles||{};if(a[e])return a[e];for(var n={_private:{key:e}},i=0;i<t.length;i++){var s=t[i],o=e[i]===ya;if(o)for(var l=0;l<s.properties.length;l++){var u=s.properties[l];n[u.name]=u}}return a[e]=n,n};sr.applyContextStyle=function(r,e,t){for(var a=this,n=r.diffPropNames,i={},s=a.types,o=0;o<n.length;o++){var l=n[o],u=e[l],v=t.pstyle(l);if(!u)if(v)v.bypass?u={name:l,deleteBypassed:!0}:u={name:l,delete:!0};else continue;if(v!==u){if(u.mapped===s.fn&&v!=null&&v.mapping!=null&&v.mapping.value===u.value){var f=v.mapping,c=f.fnValue=u.value(t);if(c===f.prevFnValue)continue}var h=i[l]={prev:v};a.applyParsedProperty(t,u),h.next=t.pstyle(l),h.next&&h.next.bypass&&(h.next=h.next.bypassed)}}return{diffProps:i}};sr.updateStyleHints=function(r){var e=r._private,t=this,a=t.propertyGroupNames,n=t.propertyGroupKeys,i=function(H,ee,ne){return t.getPropertiesHash(H,ee,ne)},s=e.styleKey;if(r.removed())return!1;var o=e.group==="nodes",l=r._private.style;a=Object.keys(l);for(var u=0;u<n.length;u++){var v=n[u];e.styleKeys[v]=[xt,qt]}for(var f=function(H,ee){return e.styleKeys[ee][0]=wa(H,e.styleKeys[ee][0])},c=function(H,ee){return e.styleKeys[ee][1]=xa(H,e.styleKeys[ee][1])},h=function(H,ee){f(H,ee),c(H,ee)},d=function(H,ee){for(var ne=0;ne<H.length;ne++){var be=H.charCodeAt(ne);f(be,ee),c(be,ee)}},y=2e9,g=function(H){return-128<H&&H<128&&Math.floor(H)!==H?y-(H*1024|0):H},p=0;p<a.length;p++){var m=a[p],b=l[m];if(b!=null){var w=this.properties[m],E=w.type,C=w.groupKey,x=void 0;w.hashOverride!=null?x=w.hashOverride(r,b):b.pfValue!=null&&(x=b.pfValue);var k=w.enums==null?b.value:null,S=x!=null,P=k!=null,D=S||P,A=b.units;if(E.number&&D&&!E.multiple){var B=S?x:k;h(g(B),C),!S&&A!=null&&d(A,C)}else d(b.strValue,C)}}for(var R=[xt,qt],M=0;M<n.length;M++){var I=n[M],L=e.styleKeys[I];R[0]=wa(L[0],R[0]),R[1]=xa(L[1],R[1])}e.styleKey=Mc(R[0],R[1]);var O=e.styleKeys;e.labelDimsKey=jr(O.labelDimensions);var V=i(r,["label"],O.labelDimensions);if(e.labelKey=jr(V),e.labelStyleKey=jr($a(O.commonLabel,V)),!o){var G=i(r,["source-label"],O.labelDimensions);e.sourceLabelKey=jr(G),e.sourceLabelStyleKey=jr($a(O.commonLabel,G));var N=i(r,["target-label"],O.labelDimensions);e.targetLabelKey=jr(N),e.targetLabelStyleKey=jr($a(O.commonLabel,N))}if(o){var F=e.styleKeys,K=F.nodeBody,X=F.nodeBorder,Q=F.nodeOutline,Z=F.backgroundImage,re=F.compound,ae=F.pie,J=F.stripe,z=[K,X,Q,Z,re,ae,J].filter(function(q){return q!=null}).reduce($a,[xt,qt]);e.nodeKey=jr(z),e.hasPie=ae!=null&&ae[0]!==xt&&ae[1]!==qt,e.hasStripe=J!=null&&J[0]!==xt&&J[1]!==qt}return s!==e.styleKey};sr.clearStyleHints=function(r){var e=r._private;e.styleCxtKey="",e.styleKeys={},e.styleKey=null,e.labelKey=null,e.labelStyleKey=null,e.sourceLabelKey=null,e.sourceLabelStyleKey=null,e.targetLabelKey=null,e.targetLabelStyleKey=null,e.nodeKey=null,e.hasPie=null,e.hasStripe=null};sr.applyParsedProperty=function(r,e){var t=this,a=e,n=r._private.style,i,s=t.types,o=t.properties[a.name].type,l=a.bypass,u=n[a.name],v=u&&u.bypass,f=r._private,c="mapping",h=function(K){return K==null?null:K.pfValue!=null?K.pfValue:K.value},d=function(){var K=h(u),X=h(a);t.checkTriggers(r,a.name,K,X)};if(e.name==="curve-style"&&r.isEdge()&&(e.value!=="bezier"&&r.isLoop()||e.value==="haystack"&&(r.source().isParent()||r.target().isParent()))&&(a=e=this.parse(e.name,"bezier",l)),a.delete)return n[a.name]=void 0,d(),!0;if(a.deleteBypassed)return u?u.bypass?(u.bypassed=void 0,d(),!0):!1:(d(),!0);if(a.deleteBypass)return u?u.bypass?(n[a.name]=u.bypassed,d(),!0):!1:(d(),!0);var y=function(){Le("Do not assign mappings to elements without corresponding data (i.e. ele `"+r.id()+"` has no mapping for property `"+a.name+"` with data field `"+a.field+"`); try a `["+a.field+"]` selector to limit scope to elements with `"+a.field+"` defined")};switch(a.mapped){case s.mapData:{for(var g=a.field.split("."),p=f.data,m=0;m<g.length&&p;m++){var b=g[m];p=p[b]}if(p==null)return y(),!1;var w;if(te(p)){var E=a.fieldMax-a.fieldMin;E===0?w=0:w=(p-a.fieldMin)/E}else return Le("Do not use continuous mappers without specifying numeric data (i.e. `"+a.field+": "+p+"` for `"+r.id()+"` is non-numeric)"),!1;if(w<0?w=0:w>1&&(w=1),o.color){var C=a.valueMin[0],x=a.valueMax[0],k=a.valueMin[1],S=a.valueMax[1],P=a.valueMin[2],D=a.valueMax[2],A=a.valueMin[3]==null?1:a.valueMin[3],B=a.valueMax[3]==null?1:a.valueMax[3],R=[Math.round(C+(x-C)*w),Math.round(k+(S-k)*w),Math.round(P+(D-P)*w),Math.round(A+(B-A)*w)];i={bypass:a.bypass,name:a.name,value:R,strValue:"rgb("+R[0]+", "+R[1]+", "+R[2]+")"}}else if(o.number){var M=a.valueMin+(a.valueMax-a.valueMin)*w;i=this.parse(a.name,M,a.bypass,c)}else return!1;if(!i)return y(),!1;i.mapping=a,a=i;break}case s.data:{for(var I=a.field.split("."),L=f.data,O=0;O<I.length&&L;O++){var V=I[O];L=L[V]}if(L!=null&&(i=this.parse(a.name,L,a.bypass,c)),!i)return y(),!1;i.mapping=a,a=i;break}case s.fn:{var G=a.value,N=a.fnValue!=null?a.fnValue:G(r);if(a.prevFnValue=N,N==null)return Le("Custom function mappers may not return null (i.e. `"+a.name+"` for ele `"+r.id()+"` is null)"),!1;if(i=this.parse(a.name,N,a.bypass,c),!i)return Le("Custom function mappers may not return invalid values for the property type (i.e. `"+a.name+"` for ele `"+r.id()+"` is invalid)"),!1;i.mapping=qr(a),a=i;break}case void 0:break;default:return!1}return l?(v?a.bypassed=u.bypassed:a.bypassed=u,n[a.name]=a):v?u.bypassed=a:n[a.name]=a,d(),!0};sr.cleanElements=function(r,e){for(var t=0;t<r.length;t++){var a=r[t];if(this.clearStyleHints(a),a.dirtyCompoundBoundsCache(),a.dirtyBoundingBoxCache(),!e)a._private.style={};else for(var n=a._private.style,i=Object.keys(n),s=0;s<i.length;s++){var o=i[s],l=n[o];l!=null&&(l.bypass?l.bypassed=null:n[o]=null)}}};sr.update=function(){var r=this._private.cy,e=r.mutableElements();e.updateStyle()};sr.updateTransitions=function(r,e){var t=this,a=r._private,n=r.pstyle("transition-property").value,i=r.pstyle("transition-duration").pfValue,s=r.pstyle("transition-delay").pfValue;if(n.length>0&&i>0){for(var o={},l=!1,u=0;u<n.length;u++){var v=n[u],f=r.pstyle(v),c=e[v];if(c){var h=c.prev,d=h,y=c.next!=null?c.next:f,g=!1,p=void 0,m=1e-6;d&&(te(d.pfValue)&&te(y.pfValue)?(g=y.pfValue-d.pfValue,p=d.pfValue+m*g):te(d.value)&&te(y.value)?(g=y.value-d.value,p=d.value+m*g):Fe(d.value)&&Fe(y.value)&&(g=d.value[0]!==y.value[0]||d.value[1]!==y.value[1]||d.value[2]!==y.value[2],p=d.strValue),g&&(o[v]=y.strValue,this.applyBypass(r,v,p),l=!0))}}if(!l)return;a.transitioning=!0,new ea(function(b){s>0?r.delayAnimation(s).play().promise().then(b):b()}).then(function(){return r.animation({style:o,duration:i,easing:r.pstyle("transition-timing-function").value,queue:!1}).play().promise()}).then(function(){t.removeBypasses(r,n),r.emitAndNotify("style"),a.transitioning=!1})}else a.transitioning&&(this.removeBypasses(r,n),r.emitAndNotify("style"),a.transitioning=!1)};sr.checkTrigger=function(r,e,t,a,n,i){var s=this.properties[e],o=n(s);r.removed()||o!=null&&o(t,a,r)&&i(s)};sr.checkZOrderTrigger=function(r,e,t,a){var n=this;this.checkTrigger(r,e,t,a,function(i){return i.triggersZOrder},function(){n._private.cy.notify("zorder",r)})};sr.checkBoundsTrigger=function(r,e,t,a){this.checkTrigger(r,e,t,a,function(n){return n.triggersBounds},function(n){r.dirtyCompoundBoundsCache(),r.dirtyBoundingBoxCache()})};sr.checkConnectedEdgesBoundsTrigger=function(r,e,t,a){this.checkTrigger(r,e,t,a,function(n){return n.triggersBoundsOfConnectedEdges},function(n){r.connectedEdges().forEach(function(i){i.dirtyBoundingBoxCache()})})};sr.checkParallelEdgesBoundsTrigger=function(r,e,t,a){this.checkTrigger(r,e,t,a,function(n){return n.triggersBoundsOfParallelEdges},function(n){r.parallelEdges().forEach(function(i){i.dirtyBoundingBoxCache()})})};sr.checkTriggers=function(r,e,t,a){r.dirtyStyleCache(),this.checkZOrderTrigger(r,e,t,a),this.checkBoundsTrigger(r,e,t,a),this.checkConnectedEdgesBoundsTrigger(r,e,t,a),this.checkParallelEdgesBoundsTrigger(r,e,t,a)};var Fa={};Fa.applyBypass=function(r,e,t,a){var n=this,i=[],s=!0;if(e==="*"||e==="**"){if(t!==void 0)for(var o=0;o<n.properties.length;o++){var l=n.properties[o],u=l.name,v=this.parse(u,t,!0);v&&i.push(v)}}else if(fe(e)){var f=this.parse(e,t,!0);f&&i.push(f)}else if(Pe(e)){var c=e;a=t;for(var h=Object.keys(c),d=0;d<h.length;d++){var y=h[d],g=c[y];if(g===void 0&&(g=c[An(y)]),g!==void 0){var p=this.parse(y,g,!0);p&&i.push(p)}}}else return!1;if(i.length===0)return!1;for(var m=!1,b=0;b<r.length;b++){for(var w=r[b],E={},C=void 0,x=0;x<i.length;x++){var k=i[x];if(a){var S=w.pstyle(k.name);C=E[k.name]={prev:S}}m=this.applyParsedProperty(w,qr(k))||m,a&&(C.next=w.pstyle(k.name))}m&&this.updateStyleHints(w),a&&this.updateTransitions(w,E,s)}return m};Fa.overrideBypass=function(r,e,t){e=Ks(e);for(var a=0;a<r.length;a++){var n=r[a],i=n._private.style[e],s=this.properties[e].type,o=s.color,l=s.mutiple,u=i?i.pfValue!=null?i.pfValue:i.value:null;!i||!i.bypass?this.applyBypass(n,e,t):(i.value=t,i.pfValue!=null&&(i.pfValue=t),o?i.strValue="rgb("+t.join(",")+")":l?i.strValue=t.join(" "):i.strValue=""+t,this.updateStyleHints(n)),this.checkTriggers(n,e,u,t)}};Fa.removeAllBypasses=function(r,e){return this.removeBypasses(r,this.propertyNames,e)};Fa.removeBypasses=function(r,e,t){for(var a=!0,n=0;n<r.length;n++){for(var i=r[n],s={},o=0;o<e.length;o++){var l=e[o],u=this.properties[l],v=i.pstyle(u.name);if(!(!v||!v.bypass)){var f="",c=this.parse(l,f,!0),h=s[u.name]={prev:v};this.applyParsedProperty(i,c),h.next=i.pstyle(u.name)}}this.updateStyleHints(i),t&&this.updateTransitions(i,s,a)}};var uo={};uo.getEmSizeInPixels=function(){var r=this.containerCss("font-size");return r!=null?parseFloat(r):1};uo.containerCss=function(r){var e=this._private.cy,t=e.container(),a=e.window();if(a&&t&&a.getComputedStyle)return a.getComputedStyle(t).getPropertyValue(r)};var _r={};_r.getRenderedStyle=function(r,e){return e?this.getStylePropertyValue(r,e,!0):this.getRawStyle(r,!0)};_r.getRawStyle=function(r,e){var t=this;if(r=r[0],r){for(var a={},n=0;n<t.properties.length;n++){var i=t.properties[n],s=t.getStylePropertyValue(r,i.name,e);s!=null&&(a[i.name]=s,a[An(i.name)]=s)}return a}};_r.getIndexedStyle=function(r,e,t,a){var n=r.pstyle(e)[t][a];return n??r.cy().style().getDefaultProperty(e)[t][0]};_r.getStylePropertyValue=function(r,e,t){var a=this;if(r=r[0],r){var n=a.properties[e];n.alias&&(n=n.pointsTo);var i=n.type,s=r.pstyle(n.name);if(s){var o=s.value,l=s.units,u=s.strValue;if(t&&i.number&&o!=null&&te(o)){var v=r.cy().zoom(),f=function(g){return g*v},c=function(g,p){return f(g)+p},h=Fe(o),d=h?l.every(function(y){return y!=null}):l!=null;return d?h?o.map(function(y,g){return c(y,l[g])}).join(" "):c(o,l):h?o.map(function(y){return fe(y)?y:""+f(y)}).join(" "):""+f(o)}else if(u!=null)return u}return null}};_r.getAnimationStartStyle=function(r,e){for(var t={},a=0;a<e.length;a++){var n=e[a],i=n.name,s=r.pstyle(i);s!==void 0&&(Pe(s)?s=this.parse(i,s.strValue):s=this.parse(i,s)),s&&(t[i]=s)}return t};_r.getPropsList=function(r){var e=this,t=[],a=r,n=e.properties;if(a)for(var i=Object.keys(a),s=0;s<i.length;s++){var o=i[s],l=a[o],u=n[o]||n[Ks(o)],v=this.parse(u.name,l);v&&t.push(v)}return t};_r.getNonDefaultPropertiesHash=function(r,e,t){var a=t.slice(),n,i,s,o,l,u;for(l=0;l<e.length;l++)if(n=e[l],i=r.pstyle(n,!1),i!=null)if(i.pfValue!=null)a[0]=wa(o,a[0]),a[1]=xa(o,a[1]);else for(s=i.strValue,u=0;u<s.length;u++)o=s.charCodeAt(u),a[0]=wa(o,a[0]),a[1]=xa(o,a[1]);return a};_r.getPropertiesHash=_r.getNonDefaultPropertiesHash;var Hn={};Hn.appendFromJson=function(r){for(var e=this,t=0;t<r.length;t++){var a=r[t],n=a.selector,i=a.style||a.css,s=Object.keys(i);e.selector(n);for(var o=0;o<s.length;o++){var l=s[o],u=i[l];e.css(l,u)}}return e};Hn.fromJson=function(r){var e=this;return e.resetToDefault(),e.appendFromJson(r),e};Hn.json=function(){for(var r=[],e=this.defaultLength;e<this.length;e++){for(var t=this[e],a=t.selector,n=t.properties,i={},s=0;s<n.length;s++){var o=n[s];i[o.name]=o.strValue}r.push({selector:a?a.toString():"core",style:i})}return r};var lo={};lo.appendFromString=function(r){var e=this,t=this,a=""+r,n,i,s;a=a.replace(/[/][*](\s|.)+?[*][/]/g,"");function o(){a.length>n.length?a=a.substr(n.length):a=""}function l(){i.length>s.length?i=i.substr(s.length):i=""}for(;;){var u=a.match(/^\s*$/);if(u)break;var v=a.match(/^\s*((?:.|\s)+?)\s*\{((?:.|\s)+?)\}/);if(!v){Le("Halting stylesheet parsing: String stylesheet contains more to parse but no selector and block found in: "+a);break}n=v[0];var f=v[1];if(f!=="core"){var c=new ot(f);if(c.invalid){Le("Skipping parsing of block: Invalid selector found in string stylesheet: "+f),o();continue}}var h=v[2],d=!1;i=h;for(var y=[];;){var g=i.match(/^\s*$/);if(g)break;var p=i.match(/^\s*(.+?)\s*:\s*(.+?)(?:\s*;|\s*$)/);if(!p){Le("Skipping parsing of block: Invalid formatting of style property and value definitions found in:"+h),d=!0;break}s=p[0];var m=p[1],b=p[2],w=e.properties[m];if(!w){Le("Skipping property: Invalid property name in: "+s),l();continue}var E=t.parse(m,b);if(!E){Le("Skipping property: Invalid property definition in: "+s),l();continue}y.push({name:m,val:b}),l()}if(d){o();break}t.selector(f);for(var C=0;C<y.length;C++){var x=y[C];t.css(x.name,x.val)}o()}return t};lo.fromString=function(r){var e=this;return e.resetToDefault(),e.appendFromString(r),e};var Ze={};(function(){var r=er,e=vc,t=cc,a=dc,n=hc,i=function(q){return"^"+q+"\\s*\\(\\s*([\\w\\.]+)\\s*\\)$"},s=function(q){var H=r+"|\\w+|"+e+"|"+t+"|"+a+"|"+n;return"^"+q+"\\s*\\(([\\w\\.]+)\\s*\\,\\s*("+r+")\\s*\\,\\s*("+r+")\\s*,\\s*("+H+")\\s*\\,\\s*("+H+")\\)$"},o=[`^url\\s*\\(\\s*['"]?(.+?)['"]?\\s*\\)$`,"^(none)$","^(.+)$"];Ze.types={time:{number:!0,min:0,units:"s|ms",implicitUnits:"ms"},percent:{number:!0,min:0,max:100,units:"%",implicitUnits:"%"},percentages:{number:!0,min:0,max:100,units:"%",implicitUnits:"%",multiple:!0},zeroOneNumber:{number:!0,min:0,max:1,unitless:!0},zeroOneNumbers:{number:!0,min:0,max:1,unitless:!0,multiple:!0},nOneOneNumber:{number:!0,min:-1,max:1,unitless:!0},nonNegativeInt:{number:!0,min:0,integer:!0,unitless:!0},nonNegativeNumber:{number:!0,min:0,unitless:!0},position:{enums:["parent","origin"]},nodeSize:{number:!0,min:0,enums:["label"]},number:{number:!0,unitless:!0},numbers:{number:!0,unitless:!0,multiple:!0},positiveNumber:{number:!0,unitless:!0,min:0,strictMin:!0},size:{number:!0,min:0},bidirectionalSize:{number:!0},bidirectionalSizeMaybePercent:{number:!0,allowPercent:!0},bidirectionalSizes:{number:!0,multiple:!0},sizeMaybePercent:{number:!0,min:0,allowPercent:!0},axisDirection:{enums:["horizontal","leftward","rightward","vertical","upward","downward","auto"]},axisDirectionExplicit:{enums:["leftward","rightward","upward","downward"]},axisDirectionPrimary:{enums:["horizontal","vertical"]},paddingRelativeTo:{enums:["width","height","average","min","max"]},bgWH:{number:!0,min:0,allowPercent:!0,enums:["auto"],multiple:!0},bgPos:{number:!0,allowPercent:!0,multiple:!0},bgRelativeTo:{enums:["inner","include-padding"],multiple:!0},bgRepeat:{enums:["repeat","repeat-x","repeat-y","no-repeat"],multiple:!0},bgFit:{enums:["none","contain","cover"],multiple:!0},bgCrossOrigin:{enums:["anonymous","use-credentials","null"],multiple:!0},bgClip:{enums:["none","node"],multiple:!0},bgContainment:{enums:["inside","over"],multiple:!0},color:{color:!0},colors:{color:!0,multiple:!0},fill:{enums:["solid","linear-gradient","radial-gradient"]},bool:{enums:["yes","no"]},bools:{enums:["yes","no"],multiple:!0},lineStyle:{enums:["solid","dotted","dashed"]},lineCap:{enums:["butt","round","square"]},linePosition:{enums:["center","inside","outside"]},lineJoin:{enums:["round","bevel","miter"]},borderStyle:{enums:["solid","dotted","dashed","double"]},curveStyle:{enums:["bezier","unbundled-bezier","haystack","segments","straight","straight-triangle","taxi","round-segments","round-taxi"]},radiusType:{enums:["arc-radius","influence-radius"],multiple:!0},fontFamily:{regex:'^([\\w- \\"]+(?:\\s*,\\s*[\\w- \\"]+)*)$'},fontStyle:{enums:["italic","normal","oblique"]},fontWeight:{enums:["normal","bold","bolder","lighter","100","200","300","400","500","600","800","900",100,200,300,400,500,600,700,800,900]},textDecoration:{enums:["none","underline","overline","line-through"]},textTransform:{enums:["none","uppercase","lowercase"]},textWrap:{enums:["none","wrap","ellipsis"]},textOverflowWrap:{enums:["whitespace","anywhere"]},textBackgroundShape:{enums:["rectangle","roundrectangle","round-rectangle"]},nodeShape:{enums:["rectangle","roundrectangle","round-rectangle","cutrectangle","cut-rectangle","bottomroundrectangle","bottom-round-rectangle","barrel","ellipse","triangle","round-triangle","square","pentagon","round-pentagon","hexagon","round-hexagon","concavehexagon","concave-hexagon","heptagon","round-heptagon","octagon","round-octagon","tag","round-tag","star","diamond","round-diamond","vee","rhomboid","right-rhomboid","polygon"]},overlayShape:{enums:["roundrectangle","round-rectangle","ellipse"]},cornerRadius:{number:!0,min:0,units:"px|em",implicitUnits:"px",enums:["auto"]},compoundIncludeLabels:{enums:["include","exclude"]},arrowShape:{enums:["tee","triangle","triangle-tee","circle-triangle","triangle-cross","triangle-backcurve","vee","square","circle","diamond","chevron","none"]},arrowFill:{enums:["filled","hollow"]},arrowWidth:{number:!0,units:"%|px|em",implicitUnits:"px",enums:["match-line"]},display:{enums:["element","none"]},visibility:{enums:["hidden","visible"]},zCompoundDepth:{enums:["bottom","orphan","auto","top"]},zIndexCompare:{enums:["auto","manual"]},valign:{enums:["top","center","bottom"]},halign:{enums:["left","center","right"]},justification:{enums:["left","center","right","auto"]},text:{string:!0},data:{mapping:!0,regex:i("data")},layoutData:{mapping:!0,regex:i("layoutData")},scratch:{mapping:!0,regex:i("scratch")},mapData:{mapping:!0,regex:s("mapData")},mapLayoutData:{mapping:!0,regex:s("mapLayoutData")},mapScratch:{mapping:!0,regex:s("mapScratch")},fn:{mapping:!0,fn:!0},url:{regexes:o,singleRegexMatchValue:!0},urls:{regexes:o,singleRegexMatchValue:!0,multiple:!0},propList:{propList:!0},angle:{number:!0,units:"deg|rad",implicitUnits:"rad"},textRotation:{number:!0,units:"deg|rad",implicitUnits:"rad",enums:["none","autorotate"]},polygonPointList:{number:!0,multiple:!0,evenMultiple:!0,min:-1,max:1,unitless:!0},edgeDistances:{enums:["intersection","node-position","endpoints"]},edgeEndpoint:{number:!0,multiple:!0,units:"%|px|em|deg|rad",implicitUnits:"px",enums:["inside-to-node","outside-to-node","outside-to-node-or-label","outside-to-line","outside-to-line-or-label"],singleEnum:!0,validate:function(q,H){switch(q.length){case 2:return H[0]!=="deg"&&H[0]!=="rad"&&H[1]!=="deg"&&H[1]!=="rad";case 1:return fe(q[0])||H[0]==="deg"||H[0]==="rad";default:return!1}}},easing:{regexes:["^(spring)\\s*\\(\\s*("+r+")\\s*,\\s*("+r+")\\s*\\)$","^(cubic-bezier)\\s*\\(\\s*("+r+")\\s*,\\s*("+r+")\\s*,\\s*("+r+")\\s*,\\s*("+r+")\\s*\\)$"],enums:["linear","ease","ease-in","ease-out","ease-in-out","ease-in-sine","ease-out-sine","ease-in-out-sine","ease-in-quad","ease-out-quad","ease-in-out-quad","ease-in-cubic","ease-out-cubic","ease-in-out-cubic","ease-in-quart","ease-out-quart","ease-in-out-quart","ease-in-quint","ease-out-quint","ease-in-out-quint","ease-in-expo","ease-out-expo","ease-in-out-expo","ease-in-circ","ease-out-circ","ease-in-out-circ"]},gradientDirection:{enums:["to-bottom","to-top","to-left","to-right","to-bottom-right","to-bottom-left","to-top-right","to-top-left","to-right-bottom","to-left-bottom","to-right-top","to-left-top"]},boundsExpansion:{number:!0,multiple:!0,min:0,validate:function(q){var H=q.length;return H===1||H===2||H===4}}};var l={zeroNonZero:function(q,H){return(q==null||H==null)&&q!==H||q==0&&H!=0?!0:q!=0&&H==0},any:function(q,H){return q!=H},emptyNonEmpty:function(q,H){var ee=nt(q),ne=nt(H);return ee&&!ne||!ee&&ne}},u=Ze.types,v=[{name:"label",type:u.text,triggersBounds:l.any,triggersZOrder:l.emptyNonEmpty},{name:"text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any}],f=[{name:"source-label",type:u.text,triggersBounds:l.any},{name:"source-text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"source-text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"source-text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"source-text-offset",type:u.size,triggersBounds:l.any}],c=[{name:"target-label",type:u.text,triggersBounds:l.any},{name:"target-text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"target-text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"target-text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"target-text-offset",type:u.size,triggersBounds:l.any}],h=[{name:"font-family",type:u.fontFamily,triggersBounds:l.any},{name:"font-style",type:u.fontStyle,triggersBounds:l.any},{name:"font-weight",type:u.fontWeight,triggersBounds:l.any},{name:"font-size",type:u.size,triggersBounds:l.any},{name:"text-transform",type:u.textTransform,triggersBounds:l.any},{name:"text-wrap",type:u.textWrap,triggersBounds:l.any},{name:"text-overflow-wrap",type:u.textOverflowWrap,triggersBounds:l.any},{name:"text-max-width",type:u.size,triggersBounds:l.any},{name:"text-outline-width",type:u.size,triggersBounds:l.any},{name:"line-height",type:u.positiveNumber,triggersBounds:l.any}],d=[{name:"text-valign",type:u.valign,triggersBounds:l.any},{name:"text-halign",type:u.halign,triggersBounds:l.any},{name:"color",type:u.color},{name:"text-outline-color",type:u.color},{name:"text-outline-opacity",type:u.zeroOneNumber},{name:"text-background-color",type:u.color},{name:"text-background-opacity",type:u.zeroOneNumber},{name:"text-background-padding",type:u.size,triggersBounds:l.any},{name:"text-border-opacity",type:u.zeroOneNumber},{name:"text-border-color",type:u.color},{name:"text-border-width",type:u.size,triggersBounds:l.any},{name:"text-border-style",type:u.borderStyle,triggersBounds:l.any},{name:"text-background-shape",type:u.textBackgroundShape,triggersBounds:l.any},{name:"text-justification",type:u.justification},{name:"box-select-labels",type:u.bool,triggersBounds:l.any}],y=[{name:"events",type:u.bool,triggersZOrder:l.any},{name:"text-events",type:u.bool,triggersZOrder:l.any}],g=[{name:"display",type:u.display,triggersZOrder:l.any,triggersBounds:l.any,triggersBoundsOfConnectedEdges:l.any,triggersBoundsOfParallelEdges:function(q,H,ee){return q===H?!1:ee.pstyle("curve-style").value==="bezier"}},{name:"visibility",type:u.visibility,triggersZOrder:l.any},{name:"opacity",type:u.zeroOneNumber,triggersZOrder:l.zeroNonZero},{name:"text-opacity",type:u.zeroOneNumber},{name:"min-zoomed-font-size",type:u.size},{name:"z-compound-depth",type:u.zCompoundDepth,triggersZOrder:l.any},{name:"z-index-compare",type:u.zIndexCompare,triggersZOrder:l.any},{name:"z-index",type:u.number,triggersZOrder:l.any}],p=[{name:"overlay-padding",type:u.size,triggersBounds:l.any},{name:"overlay-color",type:u.color},{name:"overlay-opacity",type:u.zeroOneNumber,triggersBounds:l.zeroNonZero},{name:"overlay-shape",type:u.overlayShape,triggersBounds:l.any},{name:"overlay-corner-radius",type:u.cornerRadius}],m=[{name:"underlay-padding",type:u.size,triggersBounds:l.any},{name:"underlay-color",type:u.color},{name:"underlay-opacity",type:u.zeroOneNumber,triggersBounds:l.zeroNonZero},{name:"underlay-shape",type:u.overlayShape,triggersBounds:l.any},{name:"underlay-corner-radius",type:u.cornerRadius}],b=[{name:"transition-property",type:u.propList},{name:"transition-duration",type:u.time},{name:"transition-delay",type:u.time},{name:"transition-timing-function",type:u.easing}],w=function(q,H){return H.value==="label"?-q.poolIndex():H.pfValue},E=[{name:"height",type:u.nodeSize,triggersBounds:l.any,hashOverride:w},{name:"width",type:u.nodeSize,triggersBounds:l.any,hashOverride:w},{name:"shape",type:u.nodeShape,triggersBounds:l.any},{name:"shape-polygon-points",type:u.polygonPointList,triggersBounds:l.any},{name:"corner-radius",type:u.cornerRadius},{name:"background-color",type:u.color},{name:"background-fill",type:u.fill},{name:"background-opacity",type:u.zeroOneNumber},{name:"background-blacken",type:u.nOneOneNumber},{name:"background-gradient-stop-colors",type:u.colors},{name:"background-gradient-stop-positions",type:u.percentages},{name:"background-gradient-direction",type:u.gradientDirection},{name:"padding",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"padding-relative-to",type:u.paddingRelativeTo,triggersBounds:l.any},{name:"bounds-expansion",type:u.boundsExpansion,triggersBounds:l.any}],C=[{name:"border-color",type:u.color},{name:"border-opacity",type:u.zeroOneNumber},{name:"border-width",type:u.size,triggersBounds:l.any},{name:"border-style",type:u.borderStyle},{name:"border-cap",type:u.lineCap},{name:"border-join",type:u.lineJoin},{name:"border-dash-pattern",type:u.numbers},{name:"border-dash-offset",type:u.number},{name:"border-position",type:u.linePosition}],x=[{name:"outline-color",type:u.color},{name:"outline-opacity",type:u.zeroOneNumber},{name:"outline-width",type:u.size,triggersBounds:l.any},{name:"outline-style",type:u.borderStyle},{name:"outline-offset",type:u.size,triggersBounds:l.any}],k=[{name:"background-image",type:u.urls},{name:"background-image-crossorigin",type:u.bgCrossOrigin},{name:"background-image-opacity",type:u.zeroOneNumbers},{name:"background-image-containment",type:u.bgContainment},{name:"background-image-smoothing",type:u.bools},{name:"background-position-x",type:u.bgPos},{name:"background-position-y",type:u.bgPos},{name:"background-width-relative-to",type:u.bgRelativeTo},{name:"background-height-relative-to",type:u.bgRelativeTo},{name:"background-repeat",type:u.bgRepeat},{name:"background-fit",type:u.bgFit},{name:"background-clip",type:u.bgClip},{name:"background-width",type:u.bgWH},{name:"background-height",type:u.bgWH},{name:"background-offset-x",type:u.bgPos},{name:"background-offset-y",type:u.bgPos}],S=[{name:"position",type:u.position,triggersBounds:l.any},{name:"compound-sizing-wrt-labels",type:u.compoundIncludeLabels,triggersBounds:l.any},{name:"min-width",type:u.size,triggersBounds:l.any},{name:"min-width-bias-left",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-width-bias-right",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-height",type:u.size,triggersBounds:l.any},{name:"min-height-bias-top",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-height-bias-bottom",type:u.sizeMaybePercent,triggersBounds:l.any}],P=[{name:"line-style",type:u.lineStyle},{name:"line-color",type:u.color},{name:"line-fill",type:u.fill},{name:"line-cap",type:u.lineCap},{name:"line-opacity",type:u.zeroOneNumber},{name:"line-dash-pattern",type:u.numbers},{name:"line-dash-offset",type:u.number},{name:"line-outline-width",type:u.size},{name:"line-outline-color",type:u.color},{name:"line-gradient-stop-colors",type:u.colors},{name:"line-gradient-stop-positions",type:u.percentages},{name:"curve-style",type:u.curveStyle,triggersBounds:l.any,triggersBoundsOfParallelEdges:function(q,H){return q===H?!1:q==="bezier"||H==="bezier"}},{name:"haystack-radius",type:u.zeroOneNumber,triggersBounds:l.any},{name:"source-endpoint",type:u.edgeEndpoint,triggersBounds:l.any},{name:"target-endpoint",type:u.edgeEndpoint,triggersBounds:l.any},{name:"control-point-step-size",type:u.size,triggersBounds:l.any},{name:"control-point-distances",type:u.bidirectionalSizes,triggersBounds:l.any},{name:"control-point-weights",type:u.numbers,triggersBounds:l.any},{name:"segment-distances",type:u.bidirectionalSizes,triggersBounds:l.any},{name:"segment-weights",type:u.numbers,triggersBounds:l.any},{name:"segment-radii",type:u.numbers,triggersBounds:l.any},{name:"radius-type",type:u.radiusType,triggersBounds:l.any},{name:"taxi-turn",type:u.bidirectionalSizeMaybePercent,triggersBounds:l.any},{name:"taxi-turn-min-distance",type:u.size,triggersBounds:l.any},{name:"taxi-direction",type:u.axisDirection,triggersBounds:l.any},{name:"taxi-radius",type:u.number,triggersBounds:l.any},{name:"edge-distances",type:u.edgeDistances,triggersBounds:l.any},{name:"arrow-scale",type:u.positiveNumber,triggersBounds:l.any},{name:"loop-direction",type:u.angle,triggersBounds:l.any},{name:"loop-sweep",type:u.angle,triggersBounds:l.any},{name:"source-distance-from-node",type:u.size,triggersBounds:l.any},{name:"target-distance-from-node",type:u.size,triggersBounds:l.any}],D=[{name:"ghost",type:u.bool,triggersBounds:l.any},{name:"ghost-offset-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"ghost-offset-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"ghost-opacity",type:u.zeroOneNumber}],A=[{name:"selection-box-color",type:u.color},{name:"selection-box-opacity",type:u.zeroOneNumber},{name:"selection-box-border-color",type:u.color},{name:"selection-box-border-width",type:u.size},{name:"active-bg-color",type:u.color},{name:"active-bg-opacity",type:u.zeroOneNumber},{name:"active-bg-size",type:u.size},{name:"outside-texture-bg-color",type:u.color},{name:"outside-texture-bg-opacity",type:u.zeroOneNumber}],B=[];Ze.pieBackgroundN=16,B.push({name:"pie-size",type:u.sizeMaybePercent}),B.push({name:"pie-hole",type:u.sizeMaybePercent}),B.push({name:"pie-start-angle",type:u.angle});for(var R=1;R<=Ze.pieBackgroundN;R++)B.push({name:"pie-"+R+"-background-color",type:u.color}),B.push({name:"pie-"+R+"-background-size",type:u.percent}),B.push({name:"pie-"+R+"-background-opacity",type:u.zeroOneNumber});var M=[];Ze.stripeBackgroundN=16,M.push({name:"stripe-size",type:u.sizeMaybePercent}),M.push({name:"stripe-direction",type:u.axisDirectionPrimary});for(var I=1;I<=Ze.stripeBackgroundN;I++)M.push({name:"stripe-"+I+"-background-color",type:u.color}),M.push({name:"stripe-"+I+"-background-size",type:u.percent}),M.push({name:"stripe-"+I+"-background-opacity",type:u.zeroOneNumber});var L=[],O=Ze.arrowPrefixes=["source","mid-source","target","mid-target"];[{name:"arrow-shape",type:u.arrowShape,triggersBounds:l.any},{name:"arrow-color",type:u.color},{name:"arrow-fill",type:u.arrowFill},{name:"arrow-width",type:u.arrowWidth}].forEach(function(z){O.forEach(function(q){var H=q+"-"+z.name,ee=z.type,ne=z.triggersBounds;L.push({name:H,type:ee,triggersBounds:ne})})},{});var V=Ze.properties=[].concat(y,b,g,p,m,D,d,h,v,f,c,E,C,x,k,B,M,S,P,L,A),G=Ze.propertyGroups={behavior:y,transition:b,visibility:g,overlay:p,underlay:m,ghost:D,commonLabel:d,labelDimensions:h,mainLabel:v,sourceLabel:f,targetLabel:c,nodeBody:E,nodeBorder:C,nodeOutline:x,backgroundImage:k,pie:B,stripe:M,compound:S,edgeLine:P,edgeArrow:L,core:A},N=Ze.propertyGroupNames={},F=Ze.propertyGroupKeys=Object.keys(G);F.forEach(function(z){N[z]=G[z].map(function(q){return q.name}),G[z].forEach(function(q){return q.groupKey=z})});var K=Ze.aliases=[{name:"content",pointsTo:"label"},{name:"control-point-distance",pointsTo:"control-point-distances"},{name:"control-point-weight",pointsTo:"control-point-weights"},{name:"segment-distance",pointsTo:"segment-distances"},{name:"segment-weight",pointsTo:"segment-weights"},{name:"segment-radius",pointsTo:"segment-radii"},{name:"edge-text-rotation",pointsTo:"text-rotation"},{name:"padding-left",pointsTo:"padding"},{name:"padding-right",pointsTo:"padding"},{name:"padding-top",pointsTo:"padding"},{name:"padding-bottom",pointsTo:"padding"}];Ze.propertyNames=V.map(function(z){return z.name});for(var X=0;X<V.length;X++){var Q=V[X];V[Q.name]=Q}for(var Z=0;Z<K.length;Z++){var re=K[Z],ae=V[re.pointsTo],J={name:re.name,alias:!0,pointsTo:ae};V.push(J),V[re.name]=J}})();Ze.getDefaultProperty=function(r){return this.getDefaultProperties()[r]};Ze.getDefaultProperties=function(){var r=this._private;if(r.defaultProperties!=null)return r.defaultProperties;for(var e=he({"selection-box-color":"#ddd","selection-box-opacity":.65,"selection-box-border-color":"#aaa","selection-box-border-width":1,"active-bg-color":"black","active-bg-opacity":.15,"active-bg-size":30,"outside-texture-bg-color":"#000","outside-texture-bg-opacity":.125,events:"yes","text-events":"no","text-valign":"top","text-halign":"center","text-justification":"auto","line-height":1,color:"#000","text-outline-color":"#000","text-outline-width":0,"text-outline-opacity":1,"text-opacity":1,"text-decoration":"none","text-transform":"none","text-wrap":"none","text-overflow-wrap":"whitespace","text-max-width":9999,"text-background-color":"#000","text-background-opacity":0,"text-background-shape":"rectangle","text-background-padding":0,"text-border-opacity":0,"text-border-width":0,"text-border-style":"solid","text-border-color":"#000","font-family":"Helvetica Neue, Helvetica, sans-serif","font-style":"normal","font-weight":"normal","font-size":16,"min-zoomed-font-size":0,"text-rotation":"none","source-text-rotation":"none","target-text-rotation":"none",visibility:"visible",display:"element",opacity:1,"z-compound-depth":"auto","z-index-compare":"auto","z-index":0,label:"","text-margin-x":0,"text-margin-y":0,"source-label":"","source-text-offset":0,"source-text-margin-x":0,"source-text-margin-y":0,"target-label":"","target-text-offset":0,"target-text-margin-x":0,"target-text-margin-y":0,"overlay-opacity":0,"overlay-color":"#000","overlay-padding":10,"overlay-shape":"round-rectangle","overlay-corner-radius":"auto","underlay-opacity":0,"underlay-color":"#000","underlay-padding":10,"underlay-shape":"round-rectangle","underlay-corner-radius":"auto","transition-property":"none","transition-duration":0,"transition-delay":0,"transition-timing-function":"linear","box-select-labels":"no","background-blacken":0,"background-color":"#999","background-fill":"solid","background-opacity":1,"background-image":"none","background-image-crossorigin":"anonymous","background-image-opacity":1,"background-image-containment":"inside","background-image-smoothing":"yes","background-position-x":"50%","background-position-y":"50%","background-offset-x":0,"background-offset-y":0,"background-width-relative-to":"include-padding","background-height-relative-to":"include-padding","background-repeat":"no-repeat","background-fit":"none","background-clip":"node","background-width":"auto","background-height":"auto","border-color":"#000","border-opacity":1,"border-width":0,"border-style":"solid","border-dash-pattern":[4,2],"border-dash-offset":0,"border-cap":"butt","border-join":"miter","border-position":"center","outline-color":"#999","outline-opacity":1,"outline-width":0,"outline-offset":0,"outline-style":"solid",height:30,width:30,shape:"ellipse","shape-polygon-points":"-1, -1,   1, -1,   1, 1,   -1, 1","corner-radius":"auto","bounds-expansion":0,"background-gradient-direction":"to-bottom","background-gradient-stop-colors":"#999","background-gradient-stop-positions":"0%",ghost:"no","ghost-offset-y":0,"ghost-offset-x":0,"ghost-opacity":0,padding:0,"padding-relative-to":"width",position:"origin","compound-sizing-wrt-labels":"include","min-width":0,"min-width-bias-left":0,"min-width-bias-right":0,"min-height":0,"min-height-bias-top":0,"min-height-bias-bottom":0},{"pie-size":"100%","pie-hole":0,"pie-start-angle":"0deg"},[{name:"pie-{{i}}-background-color",value:"black"},{name:"pie-{{i}}-background-size",value:"0%"},{name:"pie-{{i}}-background-opacity",value:1}].reduce(function(l,u){for(var v=1;v<=Ze.pieBackgroundN;v++){var f=u.name.replace("{{i}}",v),c=u.value;l[f]=c}return l},{}),{"stripe-size":"100%","stripe-direction":"horizontal"},[{name:"stripe-{{i}}-background-color",value:"black"},{name:"stripe-{{i}}-background-size",value:"0%"},{name:"stripe-{{i}}-background-opacity",value:1}].reduce(function(l,u){for(var v=1;v<=Ze.stripeBackgroundN;v++){var f=u.name.replace("{{i}}",v),c=u.value;l[f]=c}return l},{}),{"line-style":"solid","line-color":"#999","line-fill":"solid","line-cap":"butt","line-opacity":1,"line-outline-width":0,"line-outline-color":"#000","line-gradient-stop-colors":"#999","line-gradient-stop-positions":"0%","control-point-step-size":40,"control-point-weights":.5,"segment-weights":.5,"segment-distances":20,"segment-radii":15,"radius-type":"arc-radius","taxi-turn":"50%","taxi-radius":15,"taxi-turn-min-distance":10,"taxi-direction":"auto","edge-distances":"intersection","curve-style":"haystack","haystack-radius":0,"arrow-scale":1,"loop-direction":"-45deg","loop-sweep":"-90deg","source-distance-from-node":0,"target-distance-from-node":0,"source-endpoint":"outside-to-node","target-endpoint":"outside-to-node","line-dash-pattern":[6,3],"line-dash-offset":0},[{name:"arrow-shape",value:"none"},{name:"arrow-color",value:"#999"},{name:"arrow-fill",value:"filled"},{name:"arrow-width",value:1}].reduce(function(l,u){return Ze.arrowPrefixes.forEach(function(v){var f=v+"-"+u.name,c=u.value;l[f]=c}),l},{})),t={},a=0;a<this.properties.length;a++){var n=this.properties[a];if(!n.pointsTo){var i=n.name,s=e[i],o=this.parse(i,s);t[i]=o}}return r.defaultProperties=t,r.defaultProperties};Ze.addDefaultStylesheet=function(){this.selector(":parent").css({shape:"rectangle",padding:10,"background-color":"#eee","border-color":"#ccc","border-width":1}).selector("edge").css({width:3}).selector(":loop").css({"curve-style":"bezier"}).selector("edge:compound").css({"curve-style":"bezier","source-endpoint":"outside-to-line","target-endpoint":"outside-to-line"}).selector(":selected").css({"background-color":"#0169D9","line-color":"#0169D9","source-arrow-color":"#0169D9","target-arrow-color":"#0169D9","mid-source-arrow-color":"#0169D9","mid-target-arrow-color":"#0169D9"}).selector(":parent:selected").css({"background-color":"#CCE1F9","border-color":"#aec8e5"}).selector(":active").css({"overlay-color":"black","overlay-padding":10,"overlay-opacity":.25}),this.defaultLength=this.length};var Wn={};Wn.parse=function(r,e,t,a){var n=this;if(We(e))return n.parseImplWarn(r,e,t,a);var i=a==="mapping"||a===!0||a===!1||a==null?"dontcare":a,s=t?"t":"f",o=""+e,l=sv(r,o,s,i),u=n.propCache=n.propCache||[],v;return(v=u[l])||(v=u[l]=n.parseImplWarn(r,e,t,a)),(t||a==="mapping")&&(v=qr(v),v&&(v.value=qr(v.value))),v};Wn.parseImplWarn=function(r,e,t,a){var n=this.parseImpl(r,e,t,a);return!n&&e!=null&&Le("The style property `".concat(r,": ").concat(e,"` is invalid")),n&&(n.name==="width"||n.name==="height")&&e==="label"&&Le("The style value of `label` is deprecated for `"+n.name+"`"),n};Wn.parseImpl=function(r,e,t,a){var n=this;r=Ks(r);var i=n.properties[r],s=e,o=n.types;if(!i||e===void 0)return null;i.alias&&(i=i.pointsTo,r=i.name);var l=fe(e);l&&(e=e.trim());var u=i.type;if(!u)return null;if(t&&(e===""||e===null))return{name:r,value:e,bypass:!0,deleteBypass:!0};if(We(e))return{name:r,value:e,strValue:"fn",mapped:o.fn,bypass:t};var v,f;if(!(!l||a||e.length<7||e[1]!=="a")){if(e.length>=7&&e[0]==="d"&&(v=new RegExp(o.data.regex).exec(e))){if(t)return!1;var c=o.data;return{name:r,value:v,strValue:""+e,mapped:c,field:v[1],bypass:t}}else if(e.length>=10&&e[0]==="m"&&(f=new RegExp(o.mapData.regex).exec(e))){if(t||u.multiple)return!1;var h=o.mapData;if(!(u.color||u.number))return!1;var d=this.parse(r,f[4]);if(!d||d.mapped)return!1;var y=this.parse(r,f[5]);if(!y||y.mapped)return!1;if(d.pfValue===y.pfValue||d.strValue===y.strValue)return Le("`"+r+": "+e+"` is not a valid mapper because the output range is zero; converting to `"+r+": "+d.strValue+"`"),this.parse(r,d.strValue);if(u.color){var g=d.value,p=y.value,m=g[0]===p[0]&&g[1]===p[1]&&g[2]===p[2]&&(g[3]===p[3]||(g[3]==null||g[3]===1)&&(p[3]==null||p[3]===1));if(m)return!1}return{name:r,value:f,strValue:""+e,mapped:h,field:f[1],fieldMin:parseFloat(f[2]),fieldMax:parseFloat(f[3]),valueMin:d.value,valueMax:y.value,bypass:t}}}if(u.multiple&&a!=="multiple"){var b;if(l?b=e.split(/\s+/):Fe(e)?b=e:b=[e],u.evenMultiple&&b.length%2!==0)return null;for(var w=[],E=[],C=[],x="",k=!1,S=0;S<b.length;S++){var P=n.parse(r,b[S],t,"multiple");k=k||fe(P.value),w.push(P.value),C.push(P.pfValue!=null?P.pfValue:P.value),E.push(P.units),x+=(S>0?" ":"")+P.strValue}return u.validate&&!u.validate(w,E)?null:u.singleEnum&&k?w.length===1&&fe(w[0])?{name:r,value:w[0],strValue:w[0],bypass:t}:null:{name:r,value:w,pfValue:C,strValue:x,bypass:t,units:E}}var D=function(){for(var J=0;J<u.enums.length;J++){var z=u.enums[J];if(z===e)return{name:r,value:e,strValue:""+e,bypass:t}}return null};if(u.number){var A,B="px";if(u.units&&(A=u.units),u.implicitUnits&&(B=u.implicitUnits),!u.unitless)if(l){var R="px|em"+(u.allowPercent?"|\\%":"");A&&(R=A);var M=e.match("^("+er+")("+R+")?$");M&&(e=M[1],A=M[2]||B)}else(!A||u.implicitUnits)&&(A=B);if(e=parseFloat(e),isNaN(e)&&u.enums===void 0)return null;if(isNaN(e)&&u.enums!==void 0)return e=s,D();if(u.integer&&!ac(e)||u.min!==void 0&&(e<u.min||u.strictMin&&e===u.min)||u.max!==void 0&&(e>u.max||u.strictMax&&e===u.max))return null;var I={name:r,value:e,strValue:""+e+(A||""),units:A,bypass:t};return u.unitless||A!=="px"&&A!=="em"?I.pfValue=e:I.pfValue=A==="px"||!A?e:this.getEmSizeInPixels()*e,(A==="ms"||A==="s")&&(I.pfValue=A==="ms"?e:1e3*e),(A==="deg"||A==="rad")&&(I.pfValue=A==="rad"?e:vd(e)),A==="%"&&(I.pfValue=e/100),I}else if(u.propList){var L=[],O=""+e;if(O!=="none"){for(var V=O.split(/\s*,\s*|\s+/),G=0;G<V.length;G++){var N=V[G].trim();n.properties[N]?L.push(N):Le("`"+N+"` is not a valid property name")}if(L.length===0)return null}return{name:r,value:L,strValue:L.length===0?"none":L.join(" "),bypass:t}}else if(u.color){var F=jl(e);return F?{name:r,value:F,pfValue:F,strValue:"rgb("+F[0]+","+F[1]+","+F[2]+")",bypass:t}:null}else if(u.regex||u.regexes){if(u.enums){var K=D();if(K)return K}for(var X=u.regexes?u.regexes:[u.regex],Q=0;Q<X.length;Q++){var Z=new RegExp(X[Q]),re=Z.exec(e);if(re)return{name:r,value:u.singleRegexMatchValue?re[1]:re,strValue:""+e,bypass:t}}return null}else return u.string?{name:r,value:""+e,strValue:""+e,bypass:t}:u.enums?D():null};var ir=function(e){if(!(this instanceof ir))return new ir(e);if(!$s(e)){He("A style must have a core reference");return}this._private={cy:e,coreStyle:{}},this.length=0,this.resetToDefault()},gr=ir.prototype;gr.instanceString=function(){return"style"};gr.clear=function(){for(var r=this._private,e=r.cy,t=e.elements(),a=0;a<this.length;a++)this[a]=void 0;return this.length=0,r.contextStyles={},r.propDiffs={},this.cleanElements(t,!0),t.forEach(function(n){var i=n[0]._private;i.styleDirty=!0,i.appliedInitStyle=!1}),this};gr.resetToDefault=function(){return this.clear(),this.addDefaultStylesheet(),this};gr.core=function(r){return this._private.coreStyle[r]||this.getDefaultProperty(r)};gr.selector=function(r){var e=r==="core"?null:new ot(r),t=this.length++;return this[t]={selector:e,properties:[],mappedProperties:[],index:t},this};gr.css=function(){var r=this,e=arguments;if(e.length===1)for(var t=e[0],a=0;a<r.properties.length;a++){var n=r.properties[a],i=t[n.name];i===void 0&&(i=t[An(n.name)]),i!==void 0&&this.cssRule(n.name,i)}else e.length===2&&this.cssRule(e[0],e[1]);return this};gr.style=gr.css;gr.cssRule=function(r,e){var t=this.parse(r,e);if(t){var a=this.length-1;this[a].properties.push(t),this[a].properties[t.name]=t,t.name.match(/pie-(\d+)-background-size/)&&t.value&&(this._private.hasPie=!0),t.name.match(/stripe-(\d+)-background-size/)&&t.value&&(this._private.hasStripe=!0),t.mapped&&this[a].mappedProperties.push(t);var n=!this[a].selector;n&&(this._private.coreStyle[t.name]=t)}return this};gr.append=function(r){return Zl(r)?r.appendToStyle(this):Fe(r)?this.appendFromJson(r):fe(r)&&this.appendFromString(r),this};ir.fromJson=function(r,e){var t=new ir(r);return t.fromJson(e),t};ir.fromString=function(r,e){return new ir(r).fromString(e)};[sr,Fa,uo,_r,Hn,lo,Ze,Wn].forEach(function(r){he(gr,r)});ir.types=gr.types;ir.properties=gr.properties;ir.propertyGroups=gr.propertyGroups;ir.propertyGroupNames=gr.propertyGroupNames;ir.propertyGroupKeys=gr.propertyGroupKeys;var hp={style:function(e){if(e){var t=this.setStyle(e);t.update()}return this._private.style},setStyle:function(e){var t=this._private;return Zl(e)?t.style=e.generateStyle(this):Fe(e)?t.style=ir.fromJson(this,e):fe(e)?t.style=ir.fromString(this,e):t.style=ir(this),t.style},updateStyle:function(){this.mutableElements().updateStyle()}},gp="single",kt={autolock:function(e){if(e!==void 0)this._private.autolock=!!e;else return this._private.autolock;return this},autoungrabify:function(e){if(e!==void 0)this._private.autoungrabify=!!e;else return this._private.autoungrabify;return this},autounselectify:function(e){if(e!==void 0)this._private.autounselectify=!!e;else return this._private.autounselectify;return this},selectionType:function(e){var t=this._private;if(t.selectionType==null&&(t.selectionType=gp),e!==void 0)(e==="additive"||e==="single")&&(t.selectionType=e);else return t.selectionType;return this},panningEnabled:function(e){if(e!==void 0)this._private.panningEnabled=!!e;else return this._private.panningEnabled;return this},userPanningEnabled:function(e){if(e!==void 0)this._private.userPanningEnabled=!!e;else return this._private.userPanningEnabled;return this},zoomingEnabled:function(e){if(e!==void 0)this._private.zoomingEnabled=!!e;else return this._private.zoomingEnabled;return this},userZoomingEnabled:function(e){if(e!==void 0)this._private.userZoomingEnabled=!!e;else return this._private.userZoomingEnabled;return this},boxSelectionEnabled:function(e){if(e!==void 0)this._private.boxSelectionEnabled=!!e;else return this._private.boxSelectionEnabled;return this},pan:function(){var e=arguments,t=this._private.pan,a,n,i,s,o;switch(e.length){case 0:return t;case 1:if(fe(e[0]))return a=e[0],t[a];if(Pe(e[0])){if(!this._private.panningEnabled)return this;i=e[0],s=i.x,o=i.y,te(s)&&(t.x=s),te(o)&&(t.y=o),this.emit("pan viewport")}break;case 2:if(!this._private.panningEnabled)return this;a=e[0],n=e[1],(a==="x"||a==="y")&&te(n)&&(t[a]=n),this.emit("pan viewport");break}return this.notify("viewport"),this},panBy:function(e,t){var a=arguments,n=this._private.pan,i,s,o,l,u;if(!this._private.panningEnabled)return this;switch(a.length){case 1:Pe(e)&&(o=a[0],l=o.x,u=o.y,te(l)&&(n.x+=l),te(u)&&(n.y+=u),this.emit("pan viewport"));break;case 2:i=e,s=t,(i==="x"||i==="y")&&te(s)&&(n[i]+=s),this.emit("pan viewport");break}return this.notify("viewport"),this},gc:function(){this.notify("gc")},fit:function(e,t){var a=this.getFitViewport(e,t);if(a){var n=this._private;n.zoom=a.zoom,n.pan=a.pan,this.emit("pan zoom viewport"),this.notify("viewport")}return this},getFitViewport:function(e,t){if(te(e)&&t===void 0&&(t=e,e=void 0),!(!this._private.panningEnabled||!this._private.zoomingEnabled)){var a;if(fe(e)){var n=e;e=this.$(n)}else if(sc(e)){var i=e;a={x1:i.x1,y1:i.y1,x2:i.x2,y2:i.y2},a.w=a.x2-a.x1,a.h=a.y2-a.y1}else Dr(e)||(e=this.mutableElements());if(!(Dr(e)&&e.empty())){a=a||e.boundingBox();var s=this.width(),o=this.height(),l;if(t=te(t)?t:0,!isNaN(s)&&!isNaN(o)&&s>0&&o>0&&!isNaN(a.w)&&!isNaN(a.h)&&a.w>0&&a.h>0){l=Math.min((s-2*t)/a.w,(o-2*t)/a.h),l=l>this._private.maxZoom?this._private.maxZoom:l,l=l<this._private.minZoom?this._private.minZoom:l;var u={x:(s-l*(a.x1+a.x2))/2,y:(o-l*(a.y1+a.y2))/2};return{zoom:l,pan:u}}}}},zoomRange:function(e,t){var a=this._private;if(t==null){var n=e;e=n.min,t=n.max}return te(e)&&te(t)&&e<=t?(a.minZoom=e,a.maxZoom=t):te(e)&&t===void 0&&e<=a.maxZoom?a.minZoom=e:te(t)&&e===void 0&&t>=a.minZoom&&(a.maxZoom=t),this},minZoom:function(e){return e===void 0?this._private.minZoom:this.zoomRange({min:e})},maxZoom:function(e){return e===void 0?this._private.maxZoom:this.zoomRange({max:e})},getZoomedViewport:function(e){var t=this._private,a=t.pan,n=t.zoom,i,s,o=!1;if(t.zoomingEnabled||(o=!0),te(e)?s=e:Pe(e)&&(s=e.level,e.position!=null?i=Ln(e.position,n,a):e.renderedPosition!=null&&(i=e.renderedPosition),i!=null&&!t.panningEnabled&&(o=!0)),s=s>t.maxZoom?t.maxZoom:s,s=s<t.minZoom?t.minZoom:s,o||!te(s)||s===n||i!=null&&(!te(i.x)||!te(i.y)))return null;if(i!=null){var l=a,u=n,v=s,f={x:-v/u*(i.x-l.x)+i.x,y:-v/u*(i.y-l.y)+i.y};return{zoomed:!0,panned:!0,zoom:v,pan:f}}else return{zoomed:!0,panned:!1,zoom:s,pan:a}},zoom:function(e){if(e===void 0)return this._private.zoom;var t=this.getZoomedViewport(e),a=this._private;return t==null||!t.zoomed?this:(a.zoom=t.zoom,t.panned&&(a.pan.x=t.pan.x,a.pan.y=t.pan.y),this.emit("zoom"+(t.panned?" pan":"")+" viewport"),this.notify("viewport"),this)},viewport:function(e){var t=this._private,a=!0,n=!0,i=[],s=!1,o=!1;if(!e)return this;if(te(e.zoom)||(a=!1),Pe(e.pan)||(n=!1),!a&&!n)return this;if(a){var l=e.zoom;l<t.minZoom||l>t.maxZoom||!t.zoomingEnabled?s=!0:(t.zoom=l,i.push("zoom"))}if(n&&(!s||!e.cancelOnFailedZoom)&&t.panningEnabled){var u=e.pan;te(u.x)&&(t.pan.x=u.x,o=!1),te(u.y)&&(t.pan.y=u.y,o=!1),o||i.push("pan")}return i.length>0&&(i.push("viewport"),this.emit(i.join(" ")),this.notify("viewport")),this},center:function(e){var t=this.getCenterPan(e);return t&&(this._private.pan=t,this.emit("pan viewport"),this.notify("viewport")),this},getCenterPan:function(e,t){if(this._private.panningEnabled){if(fe(e)){var a=e;e=this.mutableElements().filter(a)}else Dr(e)||(e=this.mutableElements());if(e.length!==0){var n=e.boundingBox(),i=this.width(),s=this.height();t=t===void 0?this._private.zoom:t;var o={x:(i-t*(n.x1+n.x2))/2,y:(s-t*(n.y1+n.y2))/2};return o}}},reset:function(){return!this._private.panningEnabled||!this._private.zoomingEnabled?this:(this.viewport({pan:{x:0,y:0},zoom:1}),this)},invalidateSize:function(){this._private.sizeCache=null},size:function(){var e=this._private,t=e.container,a=this;return e.sizeCache=e.sizeCache||(t?function(){var n=a.window().getComputedStyle(t),i=function(o){return parseFloat(n.getPropertyValue(o))};return{width:t.clientWidth-i("padding-left")-i("padding-right"),height:t.clientHeight-i("padding-top")-i("padding-bottom")}}():{width:1,height:1})},width:function(){return this.size().width},height:function(){return this.size().height},extent:function(){var e=this._private.pan,t=this._private.zoom,a=this.renderedExtent(),n={x1:(a.x1-e.x)/t,x2:(a.x2-e.x)/t,y1:(a.y1-e.y)/t,y2:(a.y2-e.y)/t};return n.w=n.x2-n.x1,n.h=n.y2-n.y1,n},renderedExtent:function(){var e=this.width(),t=this.height();return{x1:0,y1:0,x2:e,y2:t,w:e,h:t}},multiClickDebounceTime:function(e){if(e)this._private.multiClickDebounceTime=e;else return this._private.multiClickDebounceTime;return this}};kt.centre=kt.center;kt.autolockNodes=kt.autolock;kt.autoungrabifyNodes=kt.autoungrabify;var ka={data:Me.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeData:Me.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),scratch:Me.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:Me.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0})};ka.attr=ka.data;ka.removeAttr=ka.removeData;var Ba=function(e){var t=this;e=he({},e);var a=e.container;a&&!yn(a)&&yn(a[0])&&(a=a[0]);var n=a?a._cyreg:null;n=n||{},n&&n.cy&&(n.cy.destroy(),n={});var i=n.readies=n.readies||[];a&&(a._cyreg=n),n.cy=t;var s=Je!==void 0&&a!==void 0&&!e.headless,o=e;o.layout=he({name:s?"grid":"null"},o.layout),o.renderer=he({name:s?"canvas":"null"},o.renderer);var l=function(d,y,g){return y!==void 0?y:g!==void 0?g:d},u=this._private={container:a,ready:!1,options:o,elements:new vr(this),listeners:[],aniEles:new vr(this),data:o.data||{},scratch:{},layout:null,renderer:null,destroyed:!1,notificationsEnabled:!0,minZoom:1e-50,maxZoom:1e50,zoomingEnabled:l(!0,o.zoomingEnabled),userZoomingEnabled:l(!0,o.userZoomingEnabled),panningEnabled:l(!0,o.panningEnabled),userPanningEnabled:l(!0,o.userPanningEnabled),boxSelectionEnabled:l(!0,o.boxSelectionEnabled),autolock:l(!1,o.autolock,o.autolockNodes),autoungrabify:l(!1,o.autoungrabify,o.autoungrabifyNodes),autounselectify:l(!1,o.autounselectify),styleEnabled:o.styleEnabled===void 0?s:o.styleEnabled,zoom:te(o.zoom)?o.zoom:1,pan:{x:Pe(o.pan)&&te(o.pan.x)?o.pan.x:0,y:Pe(o.pan)&&te(o.pan.y)?o.pan.y:0},animation:{current:[],queue:[]},hasCompoundNodes:!1,multiClickDebounceTime:l(250,o.multiClickDebounceTime)};this.createEmitter(),this.selectionType(o.selectionType),this.zoomRange({min:o.minZoom,max:o.maxZoom});var v=function(d,y){var g=d.some(oc);if(g)return ea.all(d).then(y);y(d)};u.styleEnabled&&t.setStyle([]);var f=he({},o,o.renderer);t.initRenderer(f);var c=function(d,y,g){t.notifications(!1);var p=t.mutableElements();p.length>0&&p.remove(),d!=null&&(Pe(d)||Fe(d))&&t.add(d),t.one("layoutready",function(b){t.notifications(!0),t.emit(b),t.one("load",y),t.emitAndNotify("load")}).one("layoutstop",function(){t.one("done",g),t.emit("done")});var m=he({},t._private.options.layout);m.eles=t.elements(),t.layout(m).run()};v([o.style,o.elements],function(h){var d=h[0],y=h[1];u.styleEnabled&&t.style().append(d),c(y,function(){t.startAnimationLoop(),u.ready=!0,We(o.ready)&&t.on("ready",o.ready);for(var g=0;g<i.length;g++){var p=i[g];t.on("ready",p)}n&&(n.readies=[]),t.emit("ready")},o.done)})},Tn=Ba.prototype;he(Tn,{instanceString:function(){return"core"},isReady:function(){return this._private.ready},destroyed:function(){return this._private.destroyed},ready:function(e){return this.isReady()?this.emitter().emit("ready",[],e):this.on("ready",e),this},destroy:function(){var e=this;if(!e.destroyed())return e.stopAnimationLoop(),e.destroyRenderer(),this.emit("destroy"),e._private.destroyed=!0,e},hasElementWithId:function(e){return this._private.elements.hasElementWithId(e)},getElementById:function(e){return this._private.elements.getElementById(e)},hasCompoundNodes:function(){return this._private.hasCompoundNodes},headless:function(){return this._private.renderer.isHeadless()},styleEnabled:function(){return this._private.styleEnabled},addToPool:function(e){return this._private.elements.merge(e),this},removeFromPool:function(e){return this._private.elements.unmerge(e),this},container:function(){return this._private.container||null},window:function(){var e=this._private.container;if(e==null)return Je;var t=this._private.container.ownerDocument;return t===void 0||t==null?Je:t.defaultView||Je},mount:function(e){if(e!=null){var t=this,a=t._private,n=a.options;return!yn(e)&&yn(e[0])&&(e=e[0]),t.stopAnimationLoop(),t.destroyRenderer(),a.container=e,a.styleEnabled=!0,t.invalidateSize(),t.initRenderer(he({},n,n.renderer,{name:n.renderer.name==="null"?"canvas":n.renderer.name})),t.startAnimationLoop(),t.style(n.style),t.emit("mount"),t}},unmount:function(){var e=this;return e.stopAnimationLoop(),e.destroyRenderer(),e.initRenderer({name:"null"}),e.emit("unmount"),e},options:function(){return qr(this._private.options)},json:function(e){var t=this,a=t._private,n=t.mutableElements(),i=function(w){return t.getElementById(w.id())};if(Pe(e)){if(t.startBatch(),e.elements){var s={},o=function(w,E){for(var C=[],x=[],k=0;k<w.length;k++){var S=w[k];if(!S.data.id){Le("cy.json() cannot handle elements without an ID attribute");continue}var P=""+S.data.id,D=t.getElementById(P);s[P]=!0,D.length!==0?x.push({ele:D,json:S}):(E&&(S.group=E),C.push(S))}t.add(C);for(var A=0;A<x.length;A++){var B=x[A],R=B.ele,M=B.json;R.json(M)}};if(Fe(e.elements))o(e.elements);else for(var l=["nodes","edges"],u=0;u<l.length;u++){var v=l[u],f=e.elements[v];Fe(f)&&o(f,v)}var c=t.collection();n.filter(function(b){return!s[b.id()]}).forEach(function(b){b.isParent()?c.merge(b):b.remove()}),c.forEach(function(b){return b.children().move({parent:null})}),c.forEach(function(b){return i(b).remove()})}e.style&&t.style(e.style),e.zoom!=null&&e.zoom!==a.zoom&&t.zoom(e.zoom),e.pan&&(e.pan.x!==a.pan.x||e.pan.y!==a.pan.y)&&t.pan(e.pan),e.data&&t.data(e.data);for(var h=["minZoom","maxZoom","zoomingEnabled","userZoomingEnabled","panningEnabled","userPanningEnabled","boxSelectionEnabled","autolock","autoungrabify","autounselectify","multiClickDebounceTime"],d=0;d<h.length;d++){var y=h[d];e[y]!=null&&t[y](e[y])}return t.endBatch(),this}else{var g=!!e,p={};g?p.elements=this.elements().map(function(b){return b.json()}):(p.elements={},n.forEach(function(b){var w=b.group();p.elements[w]||(p.elements[w]=[]),p.elements[w].push(b.json())})),this._private.styleEnabled&&(p.style=t.style().json()),p.data=qr(t.data());var m=a.options;return p.zoomingEnabled=a.zoomingEnabled,p.userZoomingEnabled=a.userZoomingEnabled,p.zoom=a.zoom,p.minZoom=a.minZoom,p.maxZoom=a.maxZoom,p.panningEnabled=a.panningEnabled,p.userPanningEnabled=a.userPanningEnabled,p.pan=qr(a.pan),p.boxSelectionEnabled=a.boxSelectionEnabled,p.renderer=qr(m.renderer),p.hideEdgesOnViewport=m.hideEdgesOnViewport,p.textureOnViewport=m.textureOnViewport,p.wheelSensitivity=m.wheelSensitivity,p.motionBlur=m.motionBlur,p.multiClickDebounceTime=m.multiClickDebounceTime,p}}});Tn.$id=Tn.getElementById;[np,lp,Yv,Is,fn,fp,Os,cn,hp,kt,ka].forEach(function(r){he(Tn,r)});var pp={fit:!0,directed:!1,padding:30,circle:!1,grid:!1,spacingFactor:1.75,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,roots:void 0,depthSort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}},yp={maximal:!1,acyclic:!1},Ot=function(e){return e.scratch("breadthfirst")},bl=function(e,t){return e.scratch("breadthfirst",t)};function Xv(r){this.options=he({},pp,yp,r)}Xv.prototype.run=function(){var r=this.options,e=r.cy,t=r.eles,a=t.nodes().filter(function(se){return se.isChildless()}),n=t,i=r.directed,s=r.acyclic||r.maximal||r.maximalAdjustments>0,o=!!r.boundingBox,l=e.extent(),u=Sr(o?r.boundingBox:{x1:l.x1,y1:l.y1,w:l.w,h:l.h}),v;if(Dr(r.roots))v=r.roots;else if(Fe(r.roots)){for(var f=[],c=0;c<r.roots.length;c++){var h=r.roots[c],d=e.getElementById(h);f.push(d)}v=e.collection(f)}else if(fe(r.roots))v=e.$(r.roots);else if(i)v=a.roots();else{var y=t.components();v=e.collection();for(var g=function(){var oe=y[p],ce=oe.maxDegree(!1),ge=oe.filter(function(de){return de.degree(!1)===ce});v=v.add(ge)},p=0;p<y.length;p++)g()}var m=[],b={},w=function(oe,ce){m[ce]==null&&(m[ce]=[]);var ge=m[ce].length;m[ce].push(oe),bl(oe,{index:ge,depth:ce})},E=function(oe,ce){var ge=Ot(oe),de=ge.depth,ye=ge.index;m[de][ye]=null,oe.isChildless()&&w(oe,ce)};n.bfs({roots:v,directed:r.directed,visit:function(oe,ce,ge,de,ye){var we=oe[0],De=we.id();we.isChildless()&&w(we,ye),b[De]=!0}});for(var C=[],x=0;x<a.length;x++){var k=a[x];b[k.id()]||C.push(k)}var S=function(oe){for(var ce=m[oe],ge=0;ge<ce.length;ge++){var de=ce[ge];if(de==null){ce.splice(ge,1),ge--;continue}bl(de,{depth:oe,index:ge})}},P=function(oe,ce){for(var ge=Ot(oe),de=oe.incomers().filter(function(Ye){return Ye.isNode()&&t.has(Ye)}),ye=-1,we=oe.id(),De=0;De<de.length;De++){var ze=de[De],Ue=Ot(ze);ye=Math.max(ye,Ue.depth)}if(ge.depth<=ye){if(!r.acyclic&&ce[we])return null;var Ae=ye+1;return E(oe,Ae),ce[we]=Ae,!0}return!1};if(i&&s){var D=[],A={},B=function(oe){return D.push(oe)},R=function(){return D.shift()};for(a.forEach(function(se){return D.push(se)});D.length>0;){var M=R(),I=P(M,A);if(I)M.outgoers().filter(function(se){return se.isNode()&&t.has(se)}).forEach(B);else if(I===null){Le("Detected double maximal shift for node `"+M.id()+"`.  Bailing maximal adjustment due to cycle.  Use `options.maximal: true` only on DAGs.");break}}}var L=0;if(r.avoidOverlap)for(var O=0;O<a.length;O++){var V=a[O],G=V.layoutDimensions(r),N=G.w,F=G.h;L=Math.max(L,N,F)}var K={},X=function(oe){if(K[oe.id()])return K[oe.id()];for(var ce=Ot(oe).depth,ge=oe.neighborhood(),de=0,ye=0,we=0;we<ge.length;we++){var De=ge[we];if(!(De.isEdge()||De.isParent()||!a.has(De))){var ze=Ot(De);if(ze!=null){var Ue=ze.index,Ae=ze.depth;if(!(Ue==null||Ae==null)){var Ye=m[Ae].length;Ae<ce&&(de+=Ue/Ye,ye++)}}}}return ye=Math.max(1,ye),de=de/ye,ye===0&&(de=0),K[oe.id()]=de,de},Q=function(oe,ce){var ge=X(oe),de=X(ce),ye=ge-de;return ye===0?Jl(oe.id(),ce.id()):ye};r.depthSort!==void 0&&(Q=r.depthSort);for(var Z=m.length,re=0;re<Z;re++)m[re].sort(Q),S(re);for(var ae=[],J=0;J<C.length;J++)ae.push(C[J]);var z=function(){for(var oe=0;oe<Z;oe++)S(oe)};ae.length&&(m.unshift(ae),Z=m.length,z());for(var q=0,H=0;H<Z;H++)q=Math.max(m[H].length,q);var ee={x:u.x1+u.w/2,y:u.y1+u.h/2},ne=a.reduce(function(se,oe){return function(ce){return{w:se.w===-1?ce.w:(se.w+ce.w)/2,h:se.h===-1?ce.h:(se.h+ce.h)/2}}(oe.boundingBox({includeLabels:r.nodeDimensionsIncludeLabels}))},{w:-1,h:-1}),be=Math.max(Z===1?0:o?(u.h-r.padding*2-ne.h)/(Z-1):(u.h-r.padding*2-ne.h)/(Z+1),L),_e=m.reduce(function(se,oe){return Math.max(se,oe.length)},0),Ie=function(oe){var ce=Ot(oe),ge=ce.depth,de=ce.index;if(r.circle){var ye=Math.min(u.w/2/Z,u.h/2/Z);ye=Math.max(ye,L);var we=ye*ge+ye-(Z>0&&m[0].length<=3?ye/2:0),De=2*Math.PI/m[ge].length*de;return ge===0&&m[0].length===1&&(we=1),{x:ee.x+we*Math.cos(De),y:ee.y+we*Math.sin(De)}}else{var ze=m[ge].length,Ue=Math.max(ze===1?0:o?(u.w-r.padding*2-ne.w)/((r.grid?_e:ze)-1):(u.w-r.padding*2-ne.w)/((r.grid?_e:ze)+1),L),Ae={x:ee.x+(de+1-(ze+1)/2)*Ue,y:ee.y+(ge+1-(Z+1)/2)*be};return Ae}};return t.nodes().layoutPositions(this,r,Ie),this};var mp={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,radius:void 0,startAngle:3/2*Math.PI,sweep:void 0,clockwise:!0,sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function Zv(r){this.options=he({},mp,r)}Zv.prototype.run=function(){var r=this.options,e=r,t=r.cy,a=e.eles,n=e.counterclockwise!==void 0?!e.counterclockwise:e.clockwise,i=a.nodes().not(":parent");e.sort&&(i=i.sort(e.sort));for(var s=Sr(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()}),o={x:s.x1+s.w/2,y:s.y1+s.h/2},l=e.sweep===void 0?2*Math.PI-2*Math.PI/i.length:e.sweep,u=l/Math.max(1,i.length-1),v,f=0,c=0;c<i.length;c++){var h=i[c],d=h.layoutDimensions(e),y=d.w,g=d.h;f=Math.max(f,y,g)}if(te(e.radius)?v=e.radius:i.length<=1?v=0:v=Math.min(s.h,s.w)/2-f,i.length>1&&e.avoidOverlap){f*=1.75;var p=Math.cos(u)-Math.cos(0),m=Math.sin(u)-Math.sin(0),b=Math.sqrt(f*f/(p*p+m*m));v=Math.max(b,v)}var w=function(C,x){var k=e.startAngle+x*u*(n?1:-1),S=v*Math.cos(k),P=v*Math.sin(k),D={x:o.x+S,y:o.y+P};return D};return a.nodes().layoutPositions(this,e,w),this};var bp={fit:!0,padding:30,startAngle:3/2*Math.PI,sweep:void 0,clockwise:!0,equidistant:!1,minNodeSpacing:10,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,height:void 0,width:void 0,spacingFactor:void 0,concentric:function(e){return e.degree()},levelWidth:function(e){return e.maxDegree()/4},animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function Qv(r){this.options=he({},bp,r)}Qv.prototype.run=function(){for(var r=this.options,e=r,t=e.counterclockwise!==void 0?!e.counterclockwise:e.clockwise,a=r.cy,n=e.eles,i=n.nodes().not(":parent"),s=Sr(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:a.width(),h:a.height()}),o={x:s.x1+s.w/2,y:s.y1+s.h/2},l=[],u=0,v=0;v<i.length;v++){var f=i[v],c=void 0;c=e.concentric(f),l.push({value:c,node:f}),f._private.scratch.concentric=c}i.updateStyle();for(var h=0;h<i.length;h++){var d=i[h],y=d.layoutDimensions(e);u=Math.max(u,y.w,y.h)}l.sort(function(ne,be){return be.value-ne.value});for(var g=e.levelWidth(i),p=[[]],m=p[0],b=0;b<l.length;b++){var w=l[b];if(m.length>0){var E=Math.abs(m[0].value-w.value);E>=g&&(m=[],p.push(m))}m.push(w)}var C=u+e.minNodeSpacing;if(!e.avoidOverlap){var x=p.length>0&&p[0].length>1,k=Math.min(s.w,s.h)/2-C,S=k/(p.length+x?1:0);C=Math.min(C,S)}for(var P=0,D=0;D<p.length;D++){var A=p[D],B=e.sweep===void 0?2*Math.PI-2*Math.PI/A.length:e.sweep,R=A.dTheta=B/Math.max(1,A.length-1);if(A.length>1&&e.avoidOverlap){var M=Math.cos(R)-Math.cos(0),I=Math.sin(R)-Math.sin(0),L=Math.sqrt(C*C/(M*M+I*I));P=Math.max(L,P)}A.r=P,P+=C}if(e.equidistant){for(var O=0,V=0,G=0;G<p.length;G++){var N=p[G],F=N.r-V;O=Math.max(O,F)}V=0;for(var K=0;K<p.length;K++){var X=p[K];K===0&&(V=X.r),X.r=V,V+=O}}for(var Q={},Z=0;Z<p.length;Z++)for(var re=p[Z],ae=re.dTheta,J=re.r,z=0;z<re.length;z++){var q=re[z],H=e.startAngle+(t?1:-1)*ae*z,ee={x:o.x+J*Math.cos(H),y:o.y+J*Math.sin(H)};Q[q.node.id()]=ee}return n.nodes().layoutPositions(this,e,function(ne){var be=ne.id();return Q[be]}),this};var gs,wp={ready:function(){},stop:function(){},animate:!0,animationEasing:void 0,animationDuration:void 0,animateFilter:function(e,t){return!0},animationThreshold:250,refresh:20,fit:!0,padding:30,boundingBox:void 0,nodeDimensionsIncludeLabels:!1,randomize:!1,componentSpacing:40,nodeRepulsion:function(e){return 2048},nodeOverlap:4,idealEdgeLength:function(e){return 32},edgeElasticity:function(e){return 32},nestingFactor:1.2,gravity:1,numIter:1e3,initialTemp:1e3,coolingFactor:.99,minTemp:1};function Un(r){this.options=he({},wp,r),this.options.layout=this;var e=this.options.eles.nodes(),t=this.options.eles.edges(),a=t.filter(function(n){var i=n.source().data("id"),s=n.target().data("id"),o=e.some(function(u){return u.data("id")===i}),l=e.some(function(u){return u.data("id")===s});return!o||!l});this.options.eles=this.options.eles.not(a)}Un.prototype.run=function(){var r=this.options,e=r.cy,t=this;t.stopped=!1,(r.animate===!0||r.animate===!1)&&t.emit({type:"layoutstart",layout:t}),r.debug===!0?gs=!0:gs=!1;var a=xp(e,t,r);gs&&Cp(a),r.randomize&&Tp(a);var n=Yr(),i=function(){Sp(a,e,r),r.fit===!0&&e.fit(r.padding)},s=function(c){return!(t.stopped||c>=r.numIter||(Dp(a,r),a.temperature=a.temperature*r.coolingFactor,a.temperature<r.minTemp))},o=function(){if(r.animate===!0||r.animate===!1)i(),t.one("layoutstop",r.stop),t.emit({type:"layoutstop",layout:t});else{var c=r.eles.nodes(),h=jv(a,r,c);c.layoutPositions(t,r,h)}},l=0,u=!0;if(r.animate===!0){var v=function(){for(var c=0;u&&c<r.refresh;)u=s(l),l++,c++;if(!u)xl(a,r),o();else{var h=Yr();h-n>=r.animationThreshold&&i(),mn(v)}};v()}else{for(;u;)u=s(l),l++;xl(a,r),o()}return this};Un.prototype.stop=function(){return this.stopped=!0,this.thread&&this.thread.stop(),this.emit("layoutstop"),this};Un.prototype.destroy=function(){return this.thread&&this.thread.stop(),this};var xp=function(e,t,a){for(var n=a.eles.edges(),i=a.eles.nodes(),s=Sr(a.boundingBox?a.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),o={isCompound:e.hasCompoundNodes(),layoutNodes:[],idToIndex:{},nodeSize:i.size(),graphSet:[],indexToGraph:[],layoutEdges:[],edgeSize:n.size(),temperature:a.initialTemp,clientWidth:s.w,clientHeight:s.h,boundingBox:s},l=a.eles.components(),u={},v=0;v<l.length;v++)for(var f=l[v],c=0;c<f.length;c++){var h=f[c];u[h.id()]=v}for(var v=0;v<o.nodeSize;v++){var d=i[v],y=d.layoutDimensions(a),g={};g.isLocked=d.locked(),g.id=d.data("id"),g.parentId=d.data("parent"),g.cmptId=u[d.id()],g.children=[],g.positionX=d.position("x"),g.positionY=d.position("y"),g.offsetX=0,g.offsetY=0,g.height=y.w,g.width=y.h,g.maxX=g.positionX+g.width/2,g.minX=g.positionX-g.width/2,g.maxY=g.positionY+g.height/2,g.minY=g.positionY-g.height/2,g.padLeft=parseFloat(d.style("padding")),g.padRight=parseFloat(d.style("padding")),g.padTop=parseFloat(d.style("padding")),g.padBottom=parseFloat(d.style("padding")),g.nodeRepulsion=We(a.nodeRepulsion)?a.nodeRepulsion(d):a.nodeRepulsion,o.layoutNodes.push(g),o.idToIndex[g.id]=v}for(var p=[],m=0,b=-1,w=[],v=0;v<o.nodeSize;v++){var d=o.layoutNodes[v],E=d.parentId;E!=null?o.layoutNodes[o.idToIndex[E]].children.push(d.id):(p[++b]=d.id,w.push(d.id))}for(o.graphSet.push(w);m<=b;){var C=p[m++],x=o.idToIndex[C],h=o.layoutNodes[x],k=h.children;if(k.length>0){o.graphSet.push(k);for(var v=0;v<k.length;v++)p[++b]=k[v]}}for(var v=0;v<o.graphSet.length;v++)for(var S=o.graphSet[v],c=0;c<S.length;c++){var P=o.idToIndex[S[c]];o.indexToGraph[P]=v}for(var v=0;v<o.edgeSize;v++){var D=n[v],A={};A.id=D.data("id"),A.sourceId=D.data("source"),A.targetId=D.data("target");var B=We(a.idealEdgeLength)?a.idealEdgeLength(D):a.idealEdgeLength,R=We(a.edgeElasticity)?a.edgeElasticity(D):a.edgeElasticity,M=o.idToIndex[A.sourceId],I=o.idToIndex[A.targetId],L=o.indexToGraph[M],O=o.indexToGraph[I];if(L!=O){for(var V=Ep(A.sourceId,A.targetId,o),G=o.graphSet[V],N=0,g=o.layoutNodes[M];G.indexOf(g.id)===-1;)g=o.layoutNodes[o.idToIndex[g.parentId]],N++;for(g=o.layoutNodes[I];G.indexOf(g.id)===-1;)g=o.layoutNodes[o.idToIndex[g.parentId]],N++;B*=N*a.nestingFactor}A.idealLength=B,A.elasticity=R,o.layoutEdges.push(A)}return o},Ep=function(e,t,a){var n=Jv(e,t,0,a);return 2>n.count?0:n.graph},Jv=function(e,t,a,n){var i=n.graphSet[a];if(-1<i.indexOf(e)&&-1<i.indexOf(t))return{count:2,graph:a};for(var s=0,o=0;o<i.length;o++){var l=i[o],u=n.idToIndex[l],v=n.layoutNodes[u].children;if(v.length!==0){var f=n.indexToGraph[n.idToIndex[v[0]]],c=Jv(e,t,f,n);if(c.count!==0)if(c.count===1){if(s++,s===2)break}else return c}}return{count:s,graph:a}},Cp,Tp=function(e,t){for(var a=e.clientWidth,n=e.clientHeight,i=0;i<e.nodeSize;i++){var s=e.layoutNodes[i];s.children.length===0&&!s.isLocked&&(s.positionX=Math.random()*a,s.positionY=Math.random()*n)}},jv=function(e,t,a){var n=e.boundingBox,i={x1:1/0,x2:-1/0,y1:1/0,y2:-1/0};return t.boundingBox&&(a.forEach(function(s){var o=e.layoutNodes[e.idToIndex[s.data("id")]];i.x1=Math.min(i.x1,o.positionX),i.x2=Math.max(i.x2,o.positionX),i.y1=Math.min(i.y1,o.positionY),i.y2=Math.max(i.y2,o.positionY)}),i.w=i.x2-i.x1,i.h=i.y2-i.y1),function(s,o){var l=e.layoutNodes[e.idToIndex[s.data("id")]];if(t.boundingBox){var u=(l.positionX-i.x1)/i.w,v=(l.positionY-i.y1)/i.h;return{x:n.x1+u*n.w,y:n.y1+v*n.h}}else return{x:l.positionX,y:l.positionY}}},Sp=function(e,t,a){var n=a.layout,i=a.eles.nodes(),s=jv(e,a,i);i.positions(s),e.ready!==!0&&(e.ready=!0,n.one("layoutready",a.ready),n.emit({type:"layoutready",layout:this}))},Dp=function(e,t,a){kp(e,t),Ap(e),Rp(e,t),Mp(e),Lp(e)},kp=function(e,t){for(var a=0;a<e.graphSet.length;a++)for(var n=e.graphSet[a],i=n.length,s=0;s<i;s++)for(var o=e.layoutNodes[e.idToIndex[n[s]]],l=s+1;l<i;l++){var u=e.layoutNodes[e.idToIndex[n[l]]];Bp(o,u,e,t)}},wl=function(e){return-1+2*e*Math.random()},Bp=function(e,t,a,n){var i=e.cmptId,s=t.cmptId;if(!(i!==s&&!a.isCompound)){var o=t.positionX-e.positionX,l=t.positionY-e.positionY,u=1;o===0&&l===0&&(o=wl(u),l=wl(u));var v=Pp(e,t,o,l);if(v>0)var f=n.nodeOverlap*v,c=Math.sqrt(o*o+l*l),h=f*o/c,d=f*l/c;else var y=Sn(e,o,l),g=Sn(t,-1*o,-1*l),p=g.x-y.x,m=g.y-y.y,b=p*p+m*m,c=Math.sqrt(b),f=(e.nodeRepulsion+t.nodeRepulsion)/b,h=f*p/c,d=f*m/c;e.isLocked||(e.offsetX-=h,e.offsetY-=d),t.isLocked||(t.offsetX+=h,t.offsetY+=d)}},Pp=function(e,t,a,n){if(a>0)var i=e.maxX-t.minX;else var i=t.maxX-e.minX;if(n>0)var s=e.maxY-t.minY;else var s=t.maxY-e.minY;return i>=0&&s>=0?Math.sqrt(i*i+s*s):0},Sn=function(e,t,a){var n=e.positionX,i=e.positionY,s=e.height||1,o=e.width||1,l=a/t,u=s/o,v={};return t===0&&0<a||t===0&&0>a?(v.x=n,v.y=i+s/2,v):0<t&&-1*u<=l&&l<=u?(v.x=n+o/2,v.y=i+o*a/2/t,v):0>t&&-1*u<=l&&l<=u?(v.x=n-o/2,v.y=i-o*a/2/t,v):0<a&&(l<=-1*u||l>=u)?(v.x=n+s*t/2/a,v.y=i+s/2,v):(0>a&&(l<=-1*u||l>=u)&&(v.x=n-s*t/2/a,v.y=i-s/2),v)},Ap=function(e,t){for(var a=0;a<e.edgeSize;a++){var n=e.layoutEdges[a],i=e.idToIndex[n.sourceId],s=e.layoutNodes[i],o=e.idToIndex[n.targetId],l=e.layoutNodes[o],u=l.positionX-s.positionX,v=l.positionY-s.positionY;if(!(u===0&&v===0)){var f=Sn(s,u,v),c=Sn(l,-1*u,-1*v),h=c.x-f.x,d=c.y-f.y,y=Math.sqrt(h*h+d*d),g=Math.pow(n.idealLength-y,2)/n.elasticity;if(y!==0)var p=g*h/y,m=g*d/y;else var p=0,m=0;s.isLocked||(s.offsetX+=p,s.offsetY+=m),l.isLocked||(l.offsetX-=p,l.offsetY-=m)}}},Rp=function(e,t){if(t.gravity!==0)for(var a=1,n=0;n<e.graphSet.length;n++){var i=e.graphSet[n],s=i.length;if(n===0)var o=e.clientHeight/2,l=e.clientWidth/2;else var u=e.layoutNodes[e.idToIndex[i[0]]],v=e.layoutNodes[e.idToIndex[u.parentId]],o=v.positionX,l=v.positionY;for(var f=0;f<s;f++){var c=e.layoutNodes[e.idToIndex[i[f]]];if(!c.isLocked){var h=o-c.positionX,d=l-c.positionY,y=Math.sqrt(h*h+d*d);if(y>a){var g=t.gravity*h/y,p=t.gravity*d/y;c.offsetX+=g,c.offsetY+=p}}}}},Mp=function(e,t){var a=[],n=0,i=-1;for(a.push.apply(a,e.graphSet[0]),i+=e.graphSet[0].length;n<=i;){var s=a[n++],o=e.idToIndex[s],l=e.layoutNodes[o],u=l.children;if(0<u.length&&!l.isLocked){for(var v=l.offsetX,f=l.offsetY,c=0;c<u.length;c++){var h=e.layoutNodes[e.idToIndex[u[c]]];h.offsetX+=v,h.offsetY+=f,a[++i]=u[c]}l.offsetX=0,l.offsetY=0}}},Lp=function(e,t){for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];0<n.children.length&&(n.maxX=void 0,n.minX=void 0,n.maxY=void 0,n.minY=void 0)}for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];if(!(0<n.children.length||n.isLocked)){var i=Ip(n.offsetX,n.offsetY,e.temperature);n.positionX+=i.x,n.positionY+=i.y,n.offsetX=0,n.offsetY=0,n.minX=n.positionX-n.width,n.maxX=n.positionX+n.width,n.minY=n.positionY-n.height,n.maxY=n.positionY+n.height,ef(n,e)}}for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];0<n.children.length&&!n.isLocked&&(n.positionX=(n.maxX+n.minX)/2,n.positionY=(n.maxY+n.minY)/2,n.width=n.maxX-n.minX,n.height=n.maxY-n.minY)}},Ip=function(e,t,a){var n=Math.sqrt(e*e+t*t);if(n>a)var i={x:a*e/n,y:a*t/n};else var i={x:e,y:t};return i},ef=function(e,t){var a=e.parentId;if(a!=null){var n=t.layoutNodes[t.idToIndex[a]],i=!1;if((n.maxX==null||e.maxX+n.padRight>n.maxX)&&(n.maxX=e.maxX+n.padRight,i=!0),(n.minX==null||e.minX-n.padLeft<n.minX)&&(n.minX=e.minX-n.padLeft,i=!0),(n.maxY==null||e.maxY+n.padBottom>n.maxY)&&(n.maxY=e.maxY+n.padBottom,i=!0),(n.minY==null||e.minY-n.padTop<n.minY)&&(n.minY=e.minY-n.padTop,i=!0),i)return ef(n,t)}},xl=function(e,t){for(var a=e.layoutNodes,n=[],i=0;i<a.length;i++){var s=a[i],o=s.cmptId,l=n[o]=n[o]||[];l.push(s)}for(var u=0,i=0;i<n.length;i++){var v=n[i];if(v){v.x1=1/0,v.x2=-1/0,v.y1=1/0,v.y2=-1/0;for(var f=0;f<v.length;f++){var c=v[f];v.x1=Math.min(v.x1,c.positionX-c.width/2),v.x2=Math.max(v.x2,c.positionX+c.width/2),v.y1=Math.min(v.y1,c.positionY-c.height/2),v.y2=Math.max(v.y2,c.positionY+c.height/2)}v.w=v.x2-v.x1,v.h=v.y2-v.y1,u+=v.w*v.h}}n.sort(function(m,b){return b.w*b.h-m.w*m.h});for(var h=0,d=0,y=0,g=0,p=Math.sqrt(u)*e.clientWidth/e.clientHeight,i=0;i<n.length;i++){var v=n[i];if(v){for(var f=0;f<v.length;f++){var c=v[f];c.isLocked||(c.positionX+=h-v.x1,c.positionY+=d-v.y1)}h+=v.w+t.componentSpacing,y+=v.w+t.componentSpacing,g=Math.max(g,v.h),y>p&&(d+=g+t.componentSpacing,h=0,y=0,g=0)}}},Op={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,avoidOverlapPadding:10,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,condense:!1,rows:void 0,cols:void 0,position:function(e){},sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function rf(r){this.options=he({},Op,r)}rf.prototype.run=function(){var r=this.options,e=r,t=r.cy,a=e.eles,n=a.nodes().not(":parent");e.sort&&(n=n.sort(e.sort));var i=Sr(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()});if(i.h===0||i.w===0)a.nodes().layoutPositions(this,e,function(K){return{x:i.x1,y:i.y1}});else{var s=n.size(),o=Math.sqrt(s*i.h/i.w),l=Math.round(o),u=Math.round(i.w/i.h*o),v=function(X){if(X==null)return Math.min(l,u);var Q=Math.min(l,u);Q==l?l=X:u=X},f=function(X){if(X==null)return Math.max(l,u);var Q=Math.max(l,u);Q==l?l=X:u=X},c=e.rows,h=e.cols!=null?e.cols:e.columns;if(c!=null&&h!=null)l=c,u=h;else if(c!=null&&h==null)l=c,u=Math.ceil(s/l);else if(c==null&&h!=null)u=h,l=Math.ceil(s/u);else if(u*l>s){var d=v(),y=f();(d-1)*y>=s?v(d-1):(y-1)*d>=s&&f(y-1)}else for(;u*l<s;){var g=v(),p=f();(p+1)*g>=s?f(p+1):v(g+1)}var m=i.w/u,b=i.h/l;if(e.condense&&(m=0,b=0),e.avoidOverlap)for(var w=0;w<n.length;w++){var E=n[w],C=E._private.position;(C.x==null||C.y==null)&&(C.x=0,C.y=0);var x=E.layoutDimensions(e),k=e.avoidOverlapPadding,S=x.w+k,P=x.h+k;m=Math.max(m,S),b=Math.max(b,P)}for(var D={},A=function(X,Q){return!!D["c-"+X+"-"+Q]},B=function(X,Q){D["c-"+X+"-"+Q]=!0},R=0,M=0,I=function(){M++,M>=u&&(M=0,R++)},L={},O=0;O<n.length;O++){var V=n[O],G=e.position(V);if(G&&(G.row!==void 0||G.col!==void 0)){var N={row:G.row,col:G.col};if(N.col===void 0)for(N.col=0;A(N.row,N.col);)N.col++;else if(N.row===void 0)for(N.row=0;A(N.row,N.col);)N.row++;L[V.id()]=N,B(N.row,N.col)}}var F=function(X,Q){var Z,re;if(X.locked()||X.isParent())return!1;var ae=L[X.id()];if(ae)Z=ae.col*m+m/2+i.x1,re=ae.row*b+b/2+i.y1;else{for(;A(R,M);)I();Z=M*m+m/2+i.x1,re=R*b+b/2+i.y1,B(R,M),I()}return{x:Z,y:re}};n.layoutPositions(this,e,F)}return this};var Np={ready:function(){},stop:function(){}};function vo(r){this.options=he({},Np,r)}vo.prototype.run=function(){var r=this.options,e=r.eles,t=this;return r.cy,t.emit("layoutstart"),e.nodes().positions(function(){return{x:0,y:0}}),t.one("layoutready",r.ready),t.emit("layoutready"),t.one("layoutstop",r.stop),t.emit("layoutstop"),this};vo.prototype.stop=function(){return this};var zp={positions:void 0,zoom:void 0,pan:void 0,fit:!0,padding:30,spacingFactor:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function tf(r){this.options=he({},zp,r)}tf.prototype.run=function(){var r=this.options,e=r.eles,t=e.nodes(),a=We(r.positions);function n(i){if(r.positions==null)return id(i.position());if(a)return r.positions(i);var s=r.positions[i._private.data.id];return s??null}return t.layoutPositions(this,r,function(i,s){var o=n(i);return i.locked()||o==null?!1:o}),this};var Fp={fit:!0,padding:30,boundingBox:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function af(r){this.options=he({},Fp,r)}af.prototype.run=function(){var r=this.options,e=r.cy,t=r.eles,a=Sr(r.boundingBox?r.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),n=function(s,o){return{x:a.x1+Math.round(Math.random()*a.w),y:a.y1+Math.round(Math.random()*a.h)}};return t.nodes().layoutPositions(this,r,n),this};var Vp=[{name:"breadthfirst",impl:Xv},{name:"circle",impl:Zv},{name:"concentric",impl:Qv},{name:"cose",impl:Un},{name:"grid",impl:rf},{name:"null",impl:vo},{name:"preset",impl:tf},{name:"random",impl:af}];function nf(r){this.options=r,this.notifications=0}var El=function(){},Cl=function(){throw new Error("A headless instance can not render images")};nf.prototype={recalculateRenderedStyle:El,notify:function(){this.notifications++},init:El,isHeadless:function(){return!0},png:Cl,jpg:Cl};var fo={};fo.arrowShapeWidth=.3;fo.registerArrowShapes=function(){var r=this.arrowShapes={},e=this,t=function(u,v,f,c,h,d,y){var g=h.x-f/2-y,p=h.x+f/2+y,m=h.y-f/2-y,b=h.y+f/2+y,w=g<=u&&u<=p&&m<=v&&v<=b;return w},a=function(u,v,f,c,h){var d=u*Math.cos(c)-v*Math.sin(c),y=u*Math.sin(c)+v*Math.cos(c),g=d*f,p=y*f,m=g+h.x,b=p+h.y;return{x:m,y:b}},n=function(u,v,f,c){for(var h=[],d=0;d<u.length;d+=2){var y=u[d],g=u[d+1];h.push(a(y,g,v,f,c))}return h},i=function(u){for(var v=[],f=0;f<u.length;f++){var c=u[f];v.push(c.x,c.y)}return v},s=function(u){return u.pstyle("width").pfValue*u.pstyle("arrow-scale").pfValue*2},o=function(u,v){fe(v)&&(v=r[v]),r[u]=he({name:u,points:[-.15,-.3,.15,-.3,.15,.3,-.15,.3],collide:function(c,h,d,y,g,p){var m=i(n(this.points,d+2*p,y,g)),b=Cr(c,h,m);return b},roughCollide:t,draw:function(c,h,d,y){var g=n(this.points,h,d,y);e.arrowShapeImpl("polygon")(c,g)},spacing:function(c){return 0},gap:s},v)};o("none",{collide:bn,roughCollide:bn,draw:Zs,spacing:Vo,gap:Vo}),o("triangle",{points:[-.15,-.3,0,0,.15,-.3]}),o("arrow","triangle"),o("triangle-backcurve",{points:r.triangle.points,controlPoint:[0,-.15],roughCollide:t,draw:function(u,v,f,c,h){var d=n(this.points,v,f,c),y=this.controlPoint,g=a(y[0],y[1],v,f,c);e.arrowShapeImpl(this.name)(u,d,g)},gap:function(u){return s(u)*.8}}),o("triangle-tee",{points:[0,0,.15,-.3,-.15,-.3,0,0],pointsTee:[-.15,-.4,-.15,-.5,.15,-.5,.15,-.4],collide:function(u,v,f,c,h,d,y){var g=i(n(this.points,f+2*y,c,h)),p=i(n(this.pointsTee,f+2*y,c,h)),m=Cr(u,v,g)||Cr(u,v,p);return m},draw:function(u,v,f,c,h){var d=n(this.points,v,f,c),y=n(this.pointsTee,v,f,c);e.arrowShapeImpl(this.name)(u,d,y)}}),o("circle-triangle",{radius:.15,pointsTr:[0,-.15,.15,-.45,-.15,-.45,0,-.15],collide:function(u,v,f,c,h,d,y){var g=h,p=Math.pow(g.x-u,2)+Math.pow(g.y-v,2)<=Math.pow((f+2*y)*this.radius,2),m=i(n(this.points,f+2*y,c,h));return Cr(u,v,m)||p},draw:function(u,v,f,c,h){var d=n(this.pointsTr,v,f,c);e.arrowShapeImpl(this.name)(u,d,c.x,c.y,this.radius*v)},spacing:function(u){return e.getArrowWidth(u.pstyle("width").pfValue,u.pstyle("arrow-scale").value)*this.radius}}),o("triangle-cross",{points:[0,0,.15,-.3,-.15,-.3,0,0],baseCrossLinePts:[-.15,-.4,-.15,-.4,.15,-.4,.15,-.4],crossLinePts:function(u,v){var f=this.baseCrossLinePts.slice(),c=v/u,h=3,d=5;return f[h]=f[h]-c,f[d]=f[d]-c,f},collide:function(u,v,f,c,h,d,y){var g=i(n(this.points,f+2*y,c,h)),p=i(n(this.crossLinePts(f,d),f+2*y,c,h)),m=Cr(u,v,g)||Cr(u,v,p);return m},draw:function(u,v,f,c,h){var d=n(this.points,v,f,c),y=n(this.crossLinePts(v,h),v,f,c);e.arrowShapeImpl(this.name)(u,d,y)}}),o("vee",{points:[-.15,-.3,0,0,.15,-.3,0,-.15],gap:function(u){return s(u)*.525}}),o("circle",{radius:.15,collide:function(u,v,f,c,h,d,y){var g=h,p=Math.pow(g.x-u,2)+Math.pow(g.y-v,2)<=Math.pow((f+2*y)*this.radius,2);return p},draw:function(u,v,f,c,h){e.arrowShapeImpl(this.name)(u,c.x,c.y,this.radius*v)},spacing:function(u){return e.getArrowWidth(u.pstyle("width").pfValue,u.pstyle("arrow-scale").value)*this.radius}}),o("tee",{points:[-.15,0,-.15,-.1,.15,-.1,.15,0],spacing:function(u){return 1},gap:function(u){return 1}}),o("square",{points:[-.15,0,.15,0,.15,-.3,-.15,-.3]}),o("diamond",{points:[-.15,-.15,0,-.3,.15,-.15,0,0],gap:function(u){return u.pstyle("width").pfValue*u.pstyle("arrow-scale").value}}),o("chevron",{points:[0,0,-.15,-.15,-.1,-.2,0,-.1,.1,-.2,.15,-.15],gap:function(u){return .95*u.pstyle("width").pfValue*u.pstyle("arrow-scale").value}})};var Pt={};Pt.projectIntoViewport=function(r,e){var t=this.cy,a=this.findContainerClientCoords(),n=a[0],i=a[1],s=a[4],o=t.pan(),l=t.zoom(),u=((r-n)/s-o.x)/l,v=((e-i)/s-o.y)/l;return[u,v]};Pt.findContainerClientCoords=function(){if(this.containerBB)return this.containerBB;var r=this.container,e=r.getBoundingClientRect(),t=this.cy.window().getComputedStyle(r),a=function(p){return parseFloat(t.getPropertyValue(p))},n={left:a("padding-left"),right:a("padding-right"),top:a("padding-top"),bottom:a("padding-bottom")},i={left:a("border-left-width"),right:a("border-right-width"),top:a("border-top-width"),bottom:a("border-bottom-width")},s=r.clientWidth,o=r.clientHeight,l=n.left+n.right,u=n.top+n.bottom,v=i.left+i.right,f=e.width/(s+v),c=s-l,h=o-u,d=e.left+n.left+i.left,y=e.top+n.top+i.top;return this.containerBB=[d,y,c,h,f]};Pt.invalidateContainerClientCoordsCache=function(){this.containerBB=null};Pt.findNearestElement=function(r,e,t,a){return this.findNearestElements(r,e,t,a)[0]};Pt.findNearestElements=function(r,e,t,a){var n=this,i=this,s=i.getCachedZSortedEles(),o=[],l=i.cy.zoom(),u=i.cy.hasCompoundNodes(),v=(a?24:8)/l,f=(a?8:2)/l,c=(a?8:2)/l,h=1/0,d,y;t&&(s=s.interactive);function g(x,k){if(x.isNode()){if(y)return;y=x,o.push(x)}if(x.isEdge()&&(k==null||k<h))if(d){if(d.pstyle("z-compound-depth").value===x.pstyle("z-compound-depth").value&&d.pstyle("z-compound-depth").value===x.pstyle("z-compound-depth").value){for(var S=0;S<o.length;S++)if(o[S].isEdge()){o[S]=x,d=x,h=k??h;break}}}else o.push(x),d=x,h=k??h}function p(x){var k=x.outerWidth()+2*f,S=x.outerHeight()+2*f,P=k/2,D=S/2,A=x.position(),B=x.pstyle("corner-radius").value==="auto"?"auto":x.pstyle("corner-radius").pfValue,R=x._private.rscratch;if(A.x-P<=r&&r<=A.x+P&&A.y-D<=e&&e<=A.y+D){var M=i.nodeShapes[n.getNodeShape(x)];if(M.checkPoint(r,e,0,k,S,A.x,A.y,B,R))return g(x,0),!0}}function m(x){var k=x._private,S=k.rscratch,P=x.pstyle("width").pfValue,D=x.pstyle("arrow-scale").value,A=P/2+v,B=A*A,R=A*2,O=k.source,V=k.target,M;if(S.edgeType==="segments"||S.edgeType==="straight"||S.edgeType==="haystack"){for(var I=S.allpts,L=0;L+3<I.length;L+=2)if(bd(r,e,I[L],I[L+1],I[L+2],I[L+3],R)&&B>(M=Td(r,e,I[L],I[L+1],I[L+2],I[L+3])))return g(x,M),!0}else if(S.edgeType==="bezier"||S.edgeType==="multibezier"||S.edgeType==="self"||S.edgeType==="compound"){for(var I=S.allpts,L=0;L+5<S.allpts.length;L+=4)if(wd(r,e,I[L],I[L+1],I[L+2],I[L+3],I[L+4],I[L+5],R)&&B>(M=Cd(r,e,I[L],I[L+1],I[L+2],I[L+3],I[L+4],I[L+5])))return g(x,M),!0}for(var O=O||k.source,V=V||k.target,G=n.getArrowWidth(P,D),N=[{name:"source",x:S.arrowStartX,y:S.arrowStartY,angle:S.srcArrowAngle},{name:"target",x:S.arrowEndX,y:S.arrowEndY,angle:S.tgtArrowAngle},{name:"mid-source",x:S.midX,y:S.midY,angle:S.midsrcArrowAngle},{name:"mid-target",x:S.midX,y:S.midY,angle:S.midtgtArrowAngle}],L=0;L<N.length;L++){var F=N[L],K=i.arrowShapes[x.pstyle(F.name+"-arrow-shape").value],X=x.pstyle("width").pfValue;if(K.roughCollide(r,e,G,F.angle,{x:F.x,y:F.y},X,v)&&K.collide(r,e,G,F.angle,{x:F.x,y:F.y},X,v))return g(x),!0}u&&o.length>0&&(p(O),p(V))}function b(x,k,S){return Er(x,k,S)}function w(x,k){var S=x._private,P=c,D;k?D=k+"-":D="",x.boundingBox();var A=S.labelBounds[k||"main"],B=x.pstyle(D+"label").value,R=x.pstyle("text-events").strValue==="yes";if(!(!R||!B)){var M=b(S.rscratch,"labelX",k),I=b(S.rscratch,"labelY",k),L=b(S.rscratch,"labelAngle",k),O=x.pstyle(D+"text-margin-x").pfValue,V=x.pstyle(D+"text-margin-y").pfValue,G=A.x1-P-O,N=A.x2+P-O,F=A.y1-P-V,K=A.y2+P-V;if(L){var X=Math.cos(L),Q=Math.sin(L),Z=function(ee,ne){return ee=ee-M,ne=ne-I,{x:ee*X-ne*Q+M,y:ee*Q+ne*X+I}},re=Z(G,F),ae=Z(G,K),J=Z(N,F),z=Z(N,K),q=[re.x+O,re.y+V,J.x+O,J.y+V,z.x+O,z.y+V,ae.x+O,ae.y+V];if(Cr(r,e,q))return g(x),!0}else if(Xt(A,r,e))return g(x),!0}}for(var E=s.length-1;E>=0;E--){var C=s[E];C.isNode()?p(C)||w(C):m(C)||w(C)||w(C,"source")||w(C,"target")}return o};Pt.getAllInBox=function(r,e,t,a){var n=this.getCachedZSortedEles().interactive,i=this.cy.zoom(),s=2/i,o=[],l=Math.min(r,t),u=Math.max(r,t),v=Math.min(e,a),f=Math.max(e,a);r=l,t=u,e=v,a=f;var c=Sr({x1:r,y1:e,x2:t,y2:a});function h(B,R,M){return Er(B,R,M)}function d(B,R){var M=B._private,I=s,L="";B.boundingBox();var O=M.labelBounds.main,V=h(M.rscratch,"labelX",R),G=h(M.rscratch,"labelY",R),N=h(M.rscratch,"labelAngle",R),F=B.pstyle(L+"text-margin-x").pfValue,K=B.pstyle(L+"text-margin-y").pfValue,X=O.x1-I-F,Q=O.x2+I-F,Z=O.y1-I-K,re=O.y2+I-K;if(N){var ae=Math.cos(N),J=Math.sin(N),z=function(H,ee){return H=H-V,ee=ee-G,{x:H*ae-ee*J+V,y:H*J+ee*ae+G}};return[z(X,Z),z(Q,Z),z(Q,re),z(X,re)]}else return[{x:X,y:Z},{x:Q,y:Z},{x:Q,y:re},{x:X,y:re}]}for(var y=0;y<n.length;y++){var g=n[y];if(g.isNode()){var p=g,m=p.pstyle("text-events").strValue==="yes",b=p.pstyle("box-select-labels").strValue==="yes",w=p.boundingBox({includeNodes:!0,includeEdges:!1,includeLabels:b&&m});if(eo(c,w)){var E=d(p),C=[{x:c.x1,y:c.y1},{x:c.x2,y:c.y1},{x:c.x2,y:c.y2},{x:c.x1,y:c.y2}];Pd(E,C)&&o.push(p)}}else{var x=g,k=x._private,S=k.rscratch;if(S.startX!=null&&S.startY!=null&&!Xt(c,S.startX,S.startY)||S.endX!=null&&S.endY!=null&&!Xt(c,S.endX,S.endY))continue;if(S.edgeType==="bezier"||S.edgeType==="multibezier"||S.edgeType==="self"||S.edgeType==="compound"||S.edgeType==="segments"||S.edgeType==="haystack"){for(var P=k.rstyle.bezierPts||k.rstyle.linePts||k.rstyle.haystackPts,D=!0,A=0;A<P.length;A++)if(!yd(c,P[A])){D=!1;break}D&&o.push(x)}else(S.edgeType==="haystack"||S.edgeType==="straight")&&o.push(x)}}return o};var Dn={};Dn.calculateArrowAngles=function(r){var e=r._private.rscratch,t=e.edgeType==="haystack",a=e.edgeType==="bezier",n=e.edgeType==="multibezier",i=e.edgeType==="segments",s=e.edgeType==="compound",o=e.edgeType==="self",l,u,v,f,c,h,p,m;if(t?(v=e.haystackPts[0],f=e.haystackPts[1],c=e.haystackPts[2],h=e.haystackPts[3]):(v=e.arrowStartX,f=e.arrowStartY,c=e.arrowEndX,h=e.arrowEndY),p=e.midX,m=e.midY,i)l=v-e.segpts[0],u=f-e.segpts[1];else if(n||s||o||a){var d=e.allpts,y=nr(d[0],d[2],d[4],.1),g=nr(d[1],d[3],d[5],.1);l=v-y,u=f-g}else l=v-p,u=f-m;e.srcArrowAngle=Ka(l,u);var p=e.midX,m=e.midY;if(t&&(p=(v+c)/2,m=(f+h)/2),l=c-v,u=h-f,i){var d=e.allpts;if(d.length/2%2===0){var b=d.length/2,w=b-2;l=d[b]-d[w],u=d[b+1]-d[w+1]}else if(e.isRound)l=e.midVector[1],u=-e.midVector[0];else{var b=d.length/2-1,w=b-2;l=d[b]-d[w],u=d[b+1]-d[w+1]}}else if(n||s||o){var d=e.allpts,E=e.ctrlpts,C,x,k,S;if(E.length/2%2===0){var P=d.length/2-1,D=P+2,A=D+2;C=nr(d[P],d[D],d[A],0),x=nr(d[P+1],d[D+1],d[A+1],0),k=nr(d[P],d[D],d[A],1e-4),S=nr(d[P+1],d[D+1],d[A+1],1e-4)}else{var D=d.length/2-1,P=D-2,A=D+2;C=nr(d[P],d[D],d[A],.4999),x=nr(d[P+1],d[D+1],d[A+1],.4999),k=nr(d[P],d[D],d[A],.5),S=nr(d[P+1],d[D+1],d[A+1],.5)}l=k-C,u=S-x}if(e.midtgtArrowAngle=Ka(l,u),e.midDispX=l,e.midDispY=u,l*=-1,u*=-1,i){var d=e.allpts;if(d.length/2%2!==0){if(!e.isRound){var b=d.length/2-1,B=b+2;l=-(d[B]-d[b]),u=-(d[B+1]-d[b+1])}}}if(e.midsrcArrowAngle=Ka(l,u),i)l=c-e.segpts[e.segpts.length-2],u=h-e.segpts[e.segpts.length-1];else if(n||s||o||a){var d=e.allpts,R=d.length,y=nr(d[R-6],d[R-4],d[R-2],.9),g=nr(d[R-5],d[R-3],d[R-1],.9);l=c-y,u=h-g}else l=c-p,u=h-m;e.tgtArrowAngle=Ka(l,u)};Dn.getArrowWidth=Dn.getArrowHeight=function(r,e){var t=this.arrowWidthCache=this.arrowWidthCache||{},a=t[r+", "+e];return a||(a=Math.max(Math.pow(r*13.37,.9),29)*e,t[r+", "+e]=a,a)};var Ns,zs,Vr={},kr={},Tl,Sl,Et,dn,Ur,pt,wt,zr,Nt,rn,sf,of,Fs,Vs,Dl,kl=function(e,t,a){a.x=t.x-e.x,a.y=t.y-e.y,a.len=Math.sqrt(a.x*a.x+a.y*a.y),a.nx=a.x/a.len,a.ny=a.y/a.len,a.ang=Math.atan2(a.ny,a.nx)},qp=function(e,t){t.x=e.x*-1,t.y=e.y*-1,t.nx=e.nx*-1,t.ny=e.ny*-1,t.ang=e.ang>0?-(Math.PI-e.ang):Math.PI+e.ang},_p=function(e,t,a,n,i){if(e!==Dl?kl(t,e,Vr):qp(kr,Vr),kl(t,a,kr),Tl=Vr.nx*kr.ny-Vr.ny*kr.nx,Sl=Vr.nx*kr.nx-Vr.ny*-kr.ny,Ur=Math.asin(Math.max(-1,Math.min(1,Tl))),Math.abs(Ur)<1e-6){Ns=t.x,zs=t.y,wt=Nt=0;return}Et=1,dn=!1,Sl<0?Ur<0?Ur=Math.PI+Ur:(Ur=Math.PI-Ur,Et=-1,dn=!0):Ur>0&&(Et=-1,dn=!0),t.radius!==void 0?Nt=t.radius:Nt=n,pt=Ur/2,rn=Math.min(Vr.len/2,kr.len/2),i?(zr=Math.abs(Math.cos(pt)*Nt/Math.sin(pt)),zr>rn?(zr=rn,wt=Math.abs(zr*Math.sin(pt)/Math.cos(pt))):wt=Nt):(zr=Math.min(rn,Nt),wt=Math.abs(zr*Math.sin(pt)/Math.cos(pt))),Fs=t.x+kr.nx*zr,Vs=t.y+kr.ny*zr,Ns=Fs-kr.ny*wt*Et,zs=Vs+kr.nx*wt*Et,sf=t.x+Vr.nx*zr,of=t.y+Vr.ny*zr,Dl=t};function uf(r,e){e.radius===0?r.lineTo(e.cx,e.cy):r.arc(e.cx,e.cy,e.radius,e.startAngle,e.endAngle,e.counterClockwise)}function co(r,e,t,a){var n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0;return a===0||e.radius===0?{cx:e.x,cy:e.y,radius:0,startX:e.x,startY:e.y,stopX:e.x,stopY:e.y,startAngle:void 0,endAngle:void 0,counterClockwise:void 0}:(_p(r,e,t,a,n),{cx:Ns,cy:zs,radius:wt,startX:sf,startY:of,stopX:Fs,stopY:Vs,startAngle:Vr.ang+Math.PI/2*Et,endAngle:kr.ang-Math.PI/2*Et,counterClockwise:dn})}var Pa=.01,Gp=Math.sqrt(2*Pa),pr={};pr.findMidptPtsEtc=function(r,e){var t=e.posPts,a=e.intersectionPts,n=e.vectorNormInverse,i,s=r.pstyle("source-endpoint"),o=r.pstyle("target-endpoint"),l=s.units!=null&&o.units!=null,u=function(E,C,x,k){var S=k-C,P=x-E,D=Math.sqrt(P*P+S*S);return{x:-S/D,y:P/D}},v=r.pstyle("edge-distances").value;switch(v){case"node-position":i=t;break;case"intersection":i=a;break;case"endpoints":{if(l){var f=this.manualEndptToPx(r.source()[0],s),c=je(f,2),h=c[0],d=c[1],y=this.manualEndptToPx(r.target()[0],o),g=je(y,2),p=g[0],m=g[1],b={x1:h,y1:d,x2:p,y2:m};n=u(h,d,p,m),i=b}else Le("Edge ".concat(r.id()," has edge-distances:endpoints specified without manual endpoints specified via source-endpoint and target-endpoint.  Falling back on edge-distances:intersection (default).")),i=a;break}}return{midptPts:i,vectorNormInverse:n}};pr.findHaystackPoints=function(r){for(var e=0;e<r.length;e++){var t=r[e],a=t._private,n=a.rscratch;if(!n.haystack){var i=Math.random()*2*Math.PI;n.source={x:Math.cos(i),y:Math.sin(i)},i=Math.random()*2*Math.PI,n.target={x:Math.cos(i),y:Math.sin(i)}}var s=a.source,o=a.target,l=s.position(),u=o.position(),v=s.width(),f=o.width(),c=s.height(),h=o.height(),d=t.pstyle("haystack-radius").value,y=d/2;n.haystackPts=n.allpts=[n.source.x*v*y+l.x,n.source.y*c*y+l.y,n.target.x*f*y+u.x,n.target.y*h*y+u.y],n.midX=(n.allpts[0]+n.allpts[2])/2,n.midY=(n.allpts[1]+n.allpts[3])/2,n.edgeType="haystack",n.haystack=!0,this.storeEdgeProjections(t),this.calculateArrowAngles(t),this.recalculateEdgeLabelProjections(t),this.calculateLabelAngles(t)}};pr.findSegmentsPoints=function(r,e){var t=r._private.rscratch,a=r.pstyle("segment-weights"),n=r.pstyle("segment-distances"),i=r.pstyle("segment-radii"),s=r.pstyle("radius-type"),o=Math.min(a.pfValue.length,n.pfValue.length),l=i.pfValue[i.pfValue.length-1],u=s.pfValue[s.pfValue.length-1];t.edgeType="segments",t.segpts=[],t.radii=[],t.isArcRadius=[];for(var v=0;v<o;v++){var f=a.pfValue[v],c=n.pfValue[v],h=1-f,d=f,y=this.findMidptPtsEtc(r,e),g=y.midptPts,p=y.vectorNormInverse,m={x:g.x1*h+g.x2*d,y:g.y1*h+g.y2*d};t.segpts.push(m.x+p.x*c,m.y+p.y*c),t.radii.push(i.pfValue[v]!==void 0?i.pfValue[v]:l),t.isArcRadius.push((s.pfValue[v]!==void 0?s.pfValue[v]:u)==="arc-radius")}};pr.findLoopPoints=function(r,e,t,a){var n=r._private.rscratch,i=e.dirCounts,s=e.srcPos,o=r.pstyle("control-point-distances"),l=o?o.pfValue[0]:void 0,u=r.pstyle("loop-direction").pfValue,v=r.pstyle("loop-sweep").pfValue,f=r.pstyle("control-point-step-size").pfValue;n.edgeType="self";var c=t,h=f;a&&(c=0,h=l);var d=u-Math.PI/2,y=d-v/2,g=d+v/2,p=u+"_"+v;c=i[p]===void 0?i[p]=0:++i[p],n.ctrlpts=[s.x+Math.cos(y)*1.4*h*(c/3+1),s.y+Math.sin(y)*1.4*h*(c/3+1),s.x+Math.cos(g)*1.4*h*(c/3+1),s.y+Math.sin(g)*1.4*h*(c/3+1)]};pr.findCompoundLoopPoints=function(r,e,t,a){var n=r._private.rscratch;n.edgeType="compound";var i=e.srcPos,s=e.tgtPos,o=e.srcW,l=e.srcH,u=e.tgtW,v=e.tgtH,f=r.pstyle("control-point-step-size").pfValue,c=r.pstyle("control-point-distances"),h=c?c.pfValue[0]:void 0,d=t,y=f;a&&(d=0,y=h);var g=50,p={x:i.x-o/2,y:i.y-l/2},m={x:s.x-u/2,y:s.y-v/2},b={x:Math.min(p.x,m.x),y:Math.min(p.y,m.y)},w=.5,E=Math.max(w,Math.log(o*Pa)),C=Math.max(w,Math.log(u*Pa));n.ctrlpts=[b.x,b.y-(1+Math.pow(g,1.12)/100)*y*(d/3+1)*E,b.x-(1+Math.pow(g,1.12)/100)*y*(d/3+1)*C,b.y]};pr.findStraightEdgePoints=function(r){r._private.rscratch.edgeType="straight"};pr.findBezierPoints=function(r,e,t,a,n){var i=r._private.rscratch,s=r.pstyle("control-point-step-size").pfValue,o=r.pstyle("control-point-distances"),l=r.pstyle("control-point-weights"),u=o&&l?Math.min(o.value.length,l.value.length):1,v=o?o.pfValue[0]:void 0,f=l.value[0],c=a;i.edgeType=c?"multibezier":"bezier",i.ctrlpts=[];for(var h=0;h<u;h++){var d=(.5-e.eles.length/2+t)*s*(n?-1:1),y=void 0,g=js(d);c&&(v=o?o.pfValue[h]:s,f=l.value[h]),a?y=v:y=v!==void 0?g*v:void 0;var p=y!==void 0?y:d,m=1-f,b=f,w=this.findMidptPtsEtc(r,e),E=w.midptPts,C=w.vectorNormInverse,x={x:E.x1*m+E.x2*b,y:E.y1*m+E.y2*b};i.ctrlpts.push(x.x+C.x*p,x.y+C.y*p)}};pr.findTaxiPoints=function(r,e){var t=r._private.rscratch;t.edgeType="segments";var a="vertical",n="horizontal",i="leftward",s="rightward",o="downward",l="upward",u="auto",v=e.posPts,f=e.srcW,c=e.srcH,h=e.tgtW,d=e.tgtH,y=r.pstyle("edge-distances").value,g=y!=="node-position",p=r.pstyle("taxi-direction").value,m=p,b=r.pstyle("taxi-turn"),w=b.units==="%",E=b.pfValue,C=E<0,x=r.pstyle("taxi-turn-min-distance").pfValue,k=g?(f+h)/2:0,S=g?(c+d)/2:0,P=v.x2-v.x1,D=v.y2-v.y1,A=function(T,_){return T>0?Math.max(T-_,0):Math.min(T+_,0)},B=A(P,k),R=A(D,S),M=!1;m===u?p=Math.abs(B)>Math.abs(R)?n:a:m===l||m===o?(p=a,M=!0):(m===i||m===s)&&(p=n,M=!0);var I=p===a,L=I?R:B,O=I?D:P,V=js(O),G=!1;!(M&&(w||C))&&(m===o&&O<0||m===l&&O>0||m===i&&O>0||m===s&&O<0)&&(V*=-1,L=V*Math.abs(L),G=!0);var N;if(w){var F=E<0?1+E:E;N=F*L}else{var K=E<0?L:0;N=K+E*V}var X=function(T){return Math.abs(T)<x||Math.abs(T)>=Math.abs(L)},Q=X(N),Z=X(Math.abs(L)-Math.abs(N)),re=Q||Z;if(re&&!G)if(I){var ae=Math.abs(O)<=c/2,J=Math.abs(P)<=h/2;if(ae){var z=(v.x1+v.x2)/2,q=v.y1,H=v.y2;t.segpts=[z,q,z,H]}else if(J){var ee=(v.y1+v.y2)/2,ne=v.x1,be=v.x2;t.segpts=[ne,ee,be,ee]}else t.segpts=[v.x1,v.y2]}else{var _e=Math.abs(O)<=f/2,Ie=Math.abs(D)<=d/2;if(_e){var se=(v.y1+v.y2)/2,oe=v.x1,ce=v.x2;t.segpts=[oe,se,ce,se]}else if(Ie){var ge=(v.x1+v.x2)/2,de=v.y1,ye=v.y2;t.segpts=[ge,de,ge,ye]}else t.segpts=[v.x2,v.y1]}else if(I){var we=v.y1+N+(g?c/2*V:0),De=v.x1,ze=v.x2;t.segpts=[De,we,ze,we]}else{var Ue=v.x1+N+(g?f/2*V:0),Ae=v.y1,Ye=v.y2;t.segpts=[Ue,Ae,Ue,Ye]}if(t.isRound){var ke=r.pstyle("taxi-radius").value,le=r.pstyle("radius-type").value[0]==="arc-radius";t.radii=new Array(t.segpts.length/2).fill(ke),t.isArcRadius=new Array(t.segpts.length/2).fill(le)}};pr.tryToCorrectInvalidPoints=function(r,e){var t=r._private.rscratch;if(t.edgeType==="bezier"){var a=e.srcPos,n=e.tgtPos,i=e.srcW,s=e.srcH,o=e.tgtW,l=e.tgtH,u=e.srcShape,v=e.tgtShape,f=e.srcCornerRadius,c=e.tgtCornerRadius,h=e.srcRs,d=e.tgtRs,y=!te(t.startX)||!te(t.startY),g=!te(t.arrowStartX)||!te(t.arrowStartY),p=!te(t.endX)||!te(t.endY),m=!te(t.arrowEndX)||!te(t.arrowEndY),b=3,w=this.getArrowWidth(r.pstyle("width").pfValue,r.pstyle("arrow-scale").value)*this.arrowShapeWidth,E=b*w,C=St({x:t.ctrlpts[0],y:t.ctrlpts[1]},{x:t.startX,y:t.startY}),x=C<E,k=St({x:t.ctrlpts[0],y:t.ctrlpts[1]},{x:t.endX,y:t.endY}),S=k<E,P=!1;if(y||g||x){P=!0;var D={x:t.ctrlpts[0]-a.x,y:t.ctrlpts[1]-a.y},A=Math.sqrt(D.x*D.x+D.y*D.y),B={x:D.x/A,y:D.y/A},R=Math.max(i,s),M={x:t.ctrlpts[0]+B.x*2*R,y:t.ctrlpts[1]+B.y*2*R},I=u.intersectLine(a.x,a.y,i,s,M.x,M.y,0,f,h);x?(t.ctrlpts[0]=t.ctrlpts[0]+B.x*(E-C),t.ctrlpts[1]=t.ctrlpts[1]+B.y*(E-C)):(t.ctrlpts[0]=I[0]+B.x*E,t.ctrlpts[1]=I[1]+B.y*E)}if(p||m||S){P=!0;var L={x:t.ctrlpts[0]-n.x,y:t.ctrlpts[1]-n.y},O=Math.sqrt(L.x*L.x+L.y*L.y),V={x:L.x/O,y:L.y/O},G=Math.max(i,s),N={x:t.ctrlpts[0]+V.x*2*G,y:t.ctrlpts[1]+V.y*2*G},F=v.intersectLine(n.x,n.y,o,l,N.x,N.y,0,c,d);S?(t.ctrlpts[0]=t.ctrlpts[0]+V.x*(E-k),t.ctrlpts[1]=t.ctrlpts[1]+V.y*(E-k)):(t.ctrlpts[0]=F[0]+V.x*E,t.ctrlpts[1]=F[1]+V.y*E)}P&&this.findEndpoints(r)}};pr.storeAllpts=function(r){var e=r._private.rscratch;if(e.edgeType==="multibezier"||e.edgeType==="bezier"||e.edgeType==="self"||e.edgeType==="compound"){e.allpts=[],e.allpts.push(e.startX,e.startY);for(var t=0;t+1<e.ctrlpts.length;t+=2)e.allpts.push(e.ctrlpts[t],e.ctrlpts[t+1]),t+3<e.ctrlpts.length&&e.allpts.push((e.ctrlpts[t]+e.ctrlpts[t+2])/2,(e.ctrlpts[t+1]+e.ctrlpts[t+3])/2);e.allpts.push(e.endX,e.endY);var a,n;e.ctrlpts.length/2%2===0?(a=e.allpts.length/2-1,e.midX=e.allpts[a],e.midY=e.allpts[a+1]):(a=e.allpts.length/2-3,n=.5,e.midX=nr(e.allpts[a],e.allpts[a+2],e.allpts[a+4],n),e.midY=nr(e.allpts[a+1],e.allpts[a+3],e.allpts[a+5],n))}else if(e.edgeType==="straight")e.allpts=[e.startX,e.startY,e.endX,e.endY],e.midX=(e.startX+e.endX+e.arrowStartX+e.arrowEndX)/4,e.midY=(e.startY+e.endY+e.arrowStartY+e.arrowEndY)/4;else if(e.edgeType==="segments"){if(e.allpts=[],e.allpts.push(e.startX,e.startY),e.allpts.push.apply(e.allpts,e.segpts),e.allpts.push(e.endX,e.endY),e.isRound){e.roundCorners=[];for(var i=2;i+3<e.allpts.length;i+=2){var s=e.radii[i/2-1],o=e.isArcRadius[i/2-1];e.roundCorners.push(co({x:e.allpts[i-2],y:e.allpts[i-1]},{x:e.allpts[i],y:e.allpts[i+1],radius:s},{x:e.allpts[i+2],y:e.allpts[i+3]},s,o))}}if(e.segpts.length%4===0){var l=e.segpts.length/2,u=l-2;e.midX=(e.segpts[u]+e.segpts[l])/2,e.midY=(e.segpts[u+1]+e.segpts[l+1])/2}else{var v=e.segpts.length/2-1;if(!e.isRound)e.midX=e.segpts[v],e.midY=e.segpts[v+1];else{var f={x:e.segpts[v],y:e.segpts[v+1]},c=e.roundCorners[v/2];if(c.radius===0){var h={x:e.segpts[v+2],y:e.segpts[v+3]};e.midX=f.x,e.midY=f.y,e.midVector=[f.y-h.y,h.x-f.x]}else{var d=[f.x-c.cx,f.y-c.cy],y=c.radius/Math.sqrt(Math.pow(d[0],2)+Math.pow(d[1],2));d=d.map(function(g){return g*y}),e.midX=c.cx+d[0],e.midY=c.cy+d[1],e.midVector=d}}}}};pr.checkForInvalidEdgeWarning=function(r){var e=r[0]._private.rscratch;e.nodesOverlap||te(e.startX)&&te(e.startY)&&te(e.endX)&&te(e.endY)?e.loggedErr=!1:e.loggedErr||(e.loggedErr=!0,Le("Edge `"+r.id()+"` has invalid endpoints and so it is impossible to draw.  Adjust your edge style (e.g. control points) accordingly or use an alternative edge type.  This is expected behaviour when the source node and the target node overlap."))};pr.findEdgeControlPoints=function(r){var e=this;if(!(!r||r.length===0)){for(var t=this,a=t.cy,n=a.hasCompoundNodes(),i=new Kr,s=function(S,P){return[].concat(pn(S),[P?1:0]).join("-")},o=[],l=[],u=0;u<r.length;u++){var v=r[u],f=v._private,c=v.pstyle("curve-style").value;if(!(v.removed()||!v.takesUpSpace())){if(c==="haystack"){l.push(v);continue}var h=c==="unbundled-bezier"||c.endsWith("segments")||c==="straight"||c==="straight-triangle"||c.endsWith("taxi"),d=c==="unbundled-bezier"||c==="bezier",y=f.source,g=f.target,p=y.poolIndex(),m=g.poolIndex(),b=[p,m].sort(),w=s(b,h),E=i.get(w);E==null&&(E={eles:[]},o.push({pairId:b,edgeIsUnbundled:h}),i.set(w,E)),E.eles.push(v),h&&(E.hasUnbundled=!0),d&&(E.hasBezier=!0)}}for(var C=function(){var S=o[x],P=S.pairId,D=S.edgeIsUnbundled,A=s(P,D),B=i.get(A),R;if(!B.hasUnbundled){var M=B.eles[0].parallelEdges().filter(function(le){return le.isBundledBezier()});Qs(B.eles),M.forEach(function(le){return B.eles.push(le)}),B.eles.sort(function(le,Y){return le.poolIndex()-Y.poolIndex()})}var I=B.eles[0],L=I.source(),O=I.target();if(L.poolIndex()>O.poolIndex()){var V=L;L=O,O=V}var G=B.srcPos=L.position(),N=B.tgtPos=O.position(),F=B.srcW=L.outerWidth(),K=B.srcH=L.outerHeight(),X=B.tgtW=O.outerWidth(),Q=B.tgtH=O.outerHeight(),Z=B.srcShape=t.nodeShapes[e.getNodeShape(L)],re=B.tgtShape=t.nodeShapes[e.getNodeShape(O)],ae=B.srcCornerRadius=L.pstyle("corner-radius").value==="auto"?"auto":L.pstyle("corner-radius").pfValue,J=B.tgtCornerRadius=O.pstyle("corner-radius").value==="auto"?"auto":O.pstyle("corner-radius").pfValue,z=B.tgtRs=O._private.rscratch,q=B.srcRs=L._private.rscratch;B.dirCounts={north:0,west:0,south:0,east:0,northwest:0,southwest:0,northeast:0,southeast:0};for(var H=0;H<B.eles.length;H++){var ee=B.eles[H],ne=ee[0]._private.rscratch,be=ee.pstyle("curve-style").value,_e=be==="unbundled-bezier"||be.endsWith("segments")||be.endsWith("taxi"),Ie=!L.same(ee.source());if(!B.calculatedIntersection&&L!==O&&(B.hasBezier||B.hasUnbundled)){B.calculatedIntersection=!0;var se=Z.intersectLine(G.x,G.y,F,K,N.x,N.y,0,ae,q),oe=B.srcIntn=se,ce=re.intersectLine(N.x,N.y,X,Q,G.x,G.y,0,J,z),ge=B.tgtIntn=ce,de=B.intersectionPts={x1:se[0],x2:ce[0],y1:se[1],y2:ce[1]},ye=B.posPts={x1:G.x,x2:N.x,y1:G.y,y2:N.y},we=ce[1]-se[1],De=ce[0]-se[0],ze=Math.sqrt(De*De+we*we);te(ze)&&ze>=Gp||(ze=Math.sqrt(Math.max(De*De,Pa)+Math.max(we*we,Pa)));var Ue=B.vector={x:De,y:we},Ae=B.vectorNorm={x:Ue.x/ze,y:Ue.y/ze},Ye={x:-Ae.y,y:Ae.x};B.nodesOverlap=!te(ze)||re.checkPoint(se[0],se[1],0,X,Q,N.x,N.y,J,z)||Z.checkPoint(ce[0],ce[1],0,F,K,G.x,G.y,ae,q),B.vectorNormInverse=Ye,R={nodesOverlap:B.nodesOverlap,dirCounts:B.dirCounts,calculatedIntersection:!0,hasBezier:B.hasBezier,hasUnbundled:B.hasUnbundled,eles:B.eles,srcPos:N,srcRs:z,tgtPos:G,tgtRs:q,srcW:X,srcH:Q,tgtW:F,tgtH:K,srcIntn:ge,tgtIntn:oe,srcShape:re,tgtShape:Z,posPts:{x1:ye.x2,y1:ye.y2,x2:ye.x1,y2:ye.y1},intersectionPts:{x1:de.x2,y1:de.y2,x2:de.x1,y2:de.y1},vector:{x:-Ue.x,y:-Ue.y},vectorNorm:{x:-Ae.x,y:-Ae.y},vectorNormInverse:{x:-Ye.x,y:-Ye.y}}}var ke=Ie?R:B;ne.nodesOverlap=ke.nodesOverlap,ne.srcIntn=ke.srcIntn,ne.tgtIntn=ke.tgtIntn,ne.isRound=be.startsWith("round"),n&&(L.isParent()||L.isChild()||O.isParent()||O.isChild())&&(L.parents().anySame(O)||O.parents().anySame(L)||L.same(O)&&L.isParent())?e.findCompoundLoopPoints(ee,ke,H,_e):L===O?e.findLoopPoints(ee,ke,H,_e):be.endsWith("segments")?e.findSegmentsPoints(ee,ke):be.endsWith("taxi")?e.findTaxiPoints(ee,ke):be==="straight"||!_e&&B.eles.length%2===1&&H===Math.floor(B.eles.length/2)?e.findStraightEdgePoints(ee):e.findBezierPoints(ee,ke,H,_e,Ie),e.findEndpoints(ee),e.tryToCorrectInvalidPoints(ee,ke),e.checkForInvalidEdgeWarning(ee),e.storeAllpts(ee),e.storeEdgeProjections(ee),e.calculateArrowAngles(ee),e.recalculateEdgeLabelProjections(ee),e.calculateLabelAngles(ee)}},x=0;x<o.length;x++)C();this.findHaystackPoints(l)}};function lf(r){var e=[];if(r!=null){for(var t=0;t<r.length;t+=2){var a=r[t],n=r[t+1];e.push({x:a,y:n})}return e}}pr.getSegmentPoints=function(r){var e=r[0]._private.rscratch;this.recalculateRenderedStyle(r);var t=e.edgeType;if(t==="segments")return lf(e.segpts)};pr.getControlPoints=function(r){var e=r[0]._private.rscratch;this.recalculateRenderedStyle(r);var t=e.edgeType;if(t==="bezier"||t==="multibezier"||t==="self"||t==="compound")return lf(e.ctrlpts)};pr.getEdgeMidpoint=function(r){var e=r[0]._private.rscratch;return this.recalculateRenderedStyle(r),{x:e.midX,y:e.midY}};var Va={};Va.manualEndptToPx=function(r,e){var t=this,a=r.position(),n=r.outerWidth(),i=r.outerHeight(),s=r._private.rscratch;if(e.value.length===2){var o=[e.pfValue[0],e.pfValue[1]];return e.units[0]==="%"&&(o[0]=o[0]*n),e.units[1]==="%"&&(o[1]=o[1]*i),o[0]+=a.x,o[1]+=a.y,o}else{var l=e.pfValue[0];l=-Math.PI/2+l;var u=2*Math.max(n,i),v=[a.x+Math.cos(l)*u,a.y+Math.sin(l)*u];return t.nodeShapes[this.getNodeShape(r)].intersectLine(a.x,a.y,n,i,v[0],v[1],0,r.pstyle("corner-radius").value==="auto"?"auto":r.pstyle("corner-radius").pfValue,s)}};Va.findEndpoints=function(r){var e=this,t,a=r.source()[0],n=r.target()[0],i=a.position(),s=n.position(),o=r.pstyle("target-arrow-shape").value,l=r.pstyle("source-arrow-shape").value,u=r.pstyle("target-distance-from-node").pfValue,v=r.pstyle("source-distance-from-node").pfValue,f=a._private.rscratch,c=n._private.rscratch,h=r.pstyle("curve-style").value,d=r._private.rscratch,y=d.edgeType,g=h==="taxi",p=y==="self"||y==="compound",m=y==="bezier"||y==="multibezier"||p,b=y!=="bezier",w=y==="straight"||y==="segments",E=y==="segments",C=m||b||w,x=p||g,k=r.pstyle("source-endpoint"),S=x?"outside-to-node":k.value,P=a.pstyle("corner-radius").value==="auto"?"auto":a.pstyle("corner-radius").pfValue,D=r.pstyle("target-endpoint"),A=x?"outside-to-node":D.value,B=n.pstyle("corner-radius").value==="auto"?"auto":n.pstyle("corner-radius").pfValue;d.srcManEndpt=k,d.tgtManEndpt=D;var R,M,I,L;if(m){var O=[d.ctrlpts[0],d.ctrlpts[1]],V=b?[d.ctrlpts[d.ctrlpts.length-2],d.ctrlpts[d.ctrlpts.length-1]]:O;R=V,M=O}else if(w){var G=E?d.segpts.slice(0,2):[s.x,s.y],N=E?d.segpts.slice(d.segpts.length-2):[i.x,i.y];R=N,M=G}if(A==="inside-to-node")t=[s.x,s.y];else if(D.units)t=this.manualEndptToPx(n,D);else if(A==="outside-to-line")t=d.tgtIntn;else if(A==="outside-to-node"||A==="outside-to-node-or-label"?I=R:(A==="outside-to-line"||A==="outside-to-line-or-label")&&(I=[i.x,i.y]),t=e.nodeShapes[this.getNodeShape(n)].intersectLine(s.x,s.y,n.outerWidth(),n.outerHeight(),I[0],I[1],0,B,c),A==="outside-to-node-or-label"||A==="outside-to-line-or-label"){var F=n._private.rscratch,K=F.labelWidth,X=F.labelHeight,Q=F.labelX,Z=F.labelY,re=K/2,ae=X/2,J=n.pstyle("text-valign").value;J==="top"?Z-=ae:J==="bottom"&&(Z+=ae);var z=n.pstyle("text-halign").value;z==="left"?Q-=re:z==="right"&&(Q+=re);var q=Ta(I[0],I[1],[Q-re,Z-ae,Q+re,Z-ae,Q+re,Z+ae,Q-re,Z+ae],s.x,s.y);if(q.length>0){var H=i,ee=mt(H,_t(t)),ne=mt(H,_t(q)),be=ee;if(ne<ee&&(t=q,be=ne),q.length>2){var _e=mt(H,{x:q[2],y:q[3]});_e<be&&(t=[q[2],q[3]])}}}var Ie=Ya(t,R,e.arrowShapes[o].spacing(r)+u),se=Ya(t,R,e.arrowShapes[o].gap(r)+u);if(d.endX=se[0],d.endY=se[1],d.arrowEndX=Ie[0],d.arrowEndY=Ie[1],S==="inside-to-node")t=[i.x,i.y];else if(k.units)t=this.manualEndptToPx(a,k);else if(S==="outside-to-line")t=d.srcIntn;else if(S==="outside-to-node"||S==="outside-to-node-or-label"?L=M:(S==="outside-to-line"||S==="outside-to-line-or-label")&&(L=[s.x,s.y]),t=e.nodeShapes[this.getNodeShape(a)].intersectLine(i.x,i.y,a.outerWidth(),a.outerHeight(),L[0],L[1],0,P,f),S==="outside-to-node-or-label"||S==="outside-to-line-or-label"){var oe=a._private.rscratch,ce=oe.labelWidth,ge=oe.labelHeight,de=oe.labelX,ye=oe.labelY,we=ce/2,De=ge/2,ze=a.pstyle("text-valign").value;ze==="top"?ye-=De:ze==="bottom"&&(ye+=De);var Ue=a.pstyle("text-halign").value;Ue==="left"?de-=we:Ue==="right"&&(de+=we);var Ae=Ta(L[0],L[1],[de-we,ye-De,de+we,ye-De,de+we,ye+De,de-we,ye+De],i.x,i.y);if(Ae.length>0){var Ye=s,ke=mt(Ye,_t(t)),le=mt(Ye,_t(Ae)),Y=ke;if(le<ke&&(t=[Ae[0],Ae[1]],Y=le),Ae.length>2){var T=mt(Ye,{x:Ae[2],y:Ae[3]});T<Y&&(t=[Ae[2],Ae[3]])}}}var _=Ya(t,M,e.arrowShapes[l].spacing(r)+v),W=Ya(t,M,e.arrowShapes[l].gap(r)+v);d.startX=W[0],d.startY=W[1],d.arrowStartX=_[0],d.arrowStartY=_[1],C&&(!te(d.startX)||!te(d.startY)||!te(d.endX)||!te(d.endY)?d.badLine=!0:d.badLine=!1)};Va.getSourceEndpoint=function(r){var e=r[0]._private.rscratch;switch(this.recalculateRenderedStyle(r),e.edgeType){case"haystack":return{x:e.haystackPts[0],y:e.haystackPts[1]};default:return{x:e.arrowStartX,y:e.arrowStartY}}};Va.getTargetEndpoint=function(r){var e=r[0]._private.rscratch;switch(this.recalculateRenderedStyle(r),e.edgeType){case"haystack":return{x:e.haystackPts[2],y:e.haystackPts[3]};default:return{x:e.arrowEndX,y:e.arrowEndY}}};var ho={};function Hp(r,e,t){for(var a=function(u,v,f,c){return nr(u,v,f,c)},n=e._private,i=n.rstyle.bezierPts,s=0;s<r.bezierProjPcts.length;s++){var o=r.bezierProjPcts[s];i.push({x:a(t[0],t[2],t[4],o),y:a(t[1],t[3],t[5],o)})}}ho.storeEdgeProjections=function(r){var e=r._private,t=e.rscratch,a=t.edgeType;if(e.rstyle.bezierPts=null,e.rstyle.linePts=null,e.rstyle.haystackPts=null,a==="multibezier"||a==="bezier"||a==="self"||a==="compound"){e.rstyle.bezierPts=[];for(var n=0;n+5<t.allpts.length;n+=4)Hp(this,r,t.allpts.slice(n,n+6))}else if(a==="segments")for(var i=e.rstyle.linePts=[],n=0;n+1<t.allpts.length;n+=2)i.push({x:t.allpts[n],y:t.allpts[n+1]});else if(a==="haystack"){var s=t.haystackPts;e.rstyle.haystackPts=[{x:s[0],y:s[1]},{x:s[2],y:s[3]}]}e.rstyle.arrowWidth=this.getArrowWidth(r.pstyle("width").pfValue,r.pstyle("arrow-scale").value)*this.arrowShapeWidth};ho.recalculateEdgeProjections=function(r){this.findEdgeControlPoints(r)};var Gr={};Gr.recalculateNodeLabelProjection=function(r){var e=r.pstyle("label").strValue;if(!nt(e)){var t,a,n=r._private,i=r.width(),s=r.height(),o=r.padding(),l=r.position(),u=r.pstyle("text-halign").strValue,v=r.pstyle("text-valign").strValue,f=n.rscratch,c=n.rstyle;switch(u){case"left":t=l.x-i/2-o;break;case"right":t=l.x+i/2+o;break;default:t=l.x}switch(v){case"top":a=l.y-s/2-o;break;case"bottom":a=l.y+s/2+o;break;default:a=l.y}f.labelX=t,f.labelY=a,c.labelX=t,c.labelY=a,this.calculateLabelAngles(r),this.applyLabelDimensions(r)}};var vf=function(e,t){var a=Math.atan(t/e);return e===0&&a<0&&(a=a*-1),a},ff=function(e,t){var a=t.x-e.x,n=t.y-e.y;return vf(a,n)},Wp=function(e,t,a,n){var i=Ca(0,n-.001,1),s=Ca(0,n+.001,1),o=Wt(e,t,a,i),l=Wt(e,t,a,s);return ff(o,l)};Gr.recalculateEdgeLabelProjections=function(r){var e,t=r._private,a=t.rscratch,n=this,i={mid:r.pstyle("label").strValue,source:r.pstyle("source-label").strValue,target:r.pstyle("target-label").strValue};if(i.mid||i.source||i.target){e={x:a.midX,y:a.midY};var s=function(f,c,h){$r(t.rscratch,f,c,h),$r(t.rstyle,f,c,h)};s("labelX",null,e.x),s("labelY",null,e.y);var o=vf(a.midDispX,a.midDispY);s("labelAutoAngle",null,o);var l=function(){if(l.cache)return l.cache;for(var f=[],c=0;c+5<a.allpts.length;c+=4){var h={x:a.allpts[c],y:a.allpts[c+1]},d={x:a.allpts[c+2],y:a.allpts[c+3]},y={x:a.allpts[c+4],y:a.allpts[c+5]};f.push({p0:h,p1:d,p2:y,startDist:0,length:0,segments:[]})}var g=t.rstyle.bezierPts,p=n.bezierProjPcts.length;function m(x,k,S,P,D){var A=St(k,S),B=x.segments[x.segments.length-1],R={p0:k,p1:S,t0:P,t1:D,startDist:B?B.startDist+B.length:0,length:A};x.segments.push(R),x.length+=A}for(var b=0;b<f.length;b++){var w=f[b],E=f[b-1];E&&(w.startDist=E.startDist+E.length),m(w,w.p0,g[b*p],0,n.bezierProjPcts[0]);for(var C=0;C<p-1;C++)m(w,g[b*p+C],g[b*p+C+1],n.bezierProjPcts[C],n.bezierProjPcts[C+1]);m(w,g[b*p+p-1],w.p2,n.bezierProjPcts[p-1],1)}return l.cache=f},u=function(f){var c,h=f==="source";if(i[f]){var d=r.pstyle(f+"-text-offset").pfValue;switch(a.edgeType){case"self":case"compound":case"bezier":case"multibezier":{for(var y=l(),g,p=0,m=0,b=0;b<y.length;b++){for(var w=y[h?b:y.length-1-b],E=0;E<w.segments.length;E++){var C=w.segments[h?E:w.segments.length-1-E],x=b===y.length-1&&E===w.segments.length-1;if(p=m,m+=C.length,m>=d||x){g={cp:w,segment:C};break}}if(g)break}var k=g.cp,S=g.segment,P=(d-p)/S.length,D=S.t1-S.t0,A=h?S.t0+D*P:S.t1-D*P;A=Ca(0,A,1),e=Wt(k.p0,k.p1,k.p2,A),c=Wp(k.p0,k.p1,k.p2,A);break}case"straight":case"segments":case"haystack":{for(var B=0,R,M,I,L,O=a.allpts.length,V=0;V+3<O&&(h?(I={x:a.allpts[V],y:a.allpts[V+1]},L={x:a.allpts[V+2],y:a.allpts[V+3]}):(I={x:a.allpts[O-2-V],y:a.allpts[O-1-V]},L={x:a.allpts[O-4-V],y:a.allpts[O-3-V]}),R=St(I,L),M=B,B+=R,!(B>=d));V+=2);var G=d-M,N=G/R;N=Ca(0,N,1),e=cd(I,L,N),c=ff(I,L);break}}s("labelX",f,e.x),s("labelY",f,e.y),s("labelAutoAngle",f,c)}};u("source"),u("target"),this.applyLabelDimensions(r)}};Gr.applyLabelDimensions=function(r){this.applyPrefixedLabelDimensions(r),r.isEdge()&&(this.applyPrefixedLabelDimensions(r,"source"),this.applyPrefixedLabelDimensions(r,"target"))};Gr.applyPrefixedLabelDimensions=function(r,e){var t=r._private,a=this.getLabelText(r,e),n=Tt(a,r._private.labelDimsKey);if(Er(t.rscratch,"prefixedLabelDimsKey",e)!==n){$r(t.rscratch,"prefixedLabelDimsKey",e,n);var i=this.calculateLabelDimensions(r,a),s=r.pstyle("line-height").pfValue,o=r.pstyle("text-wrap").strValue,l=Er(t.rscratch,"labelWrapCachedLines",e)||[],u=o!=="wrap"?1:Math.max(l.length,1),v=i.height/u,f=v*s,c=i.width,h=i.height+(u-1)*(s-1)*v;$r(t.rstyle,"labelWidth",e,c),$r(t.rscratch,"labelWidth",e,c),$r(t.rstyle,"labelHeight",e,h),$r(t.rscratch,"labelHeight",e,h),$r(t.rscratch,"labelLineHeight",e,f)}};Gr.getLabelText=function(r,e){var t=r._private,a=e?e+"-":"",n=r.pstyle(a+"label").strValue,i=r.pstyle("text-transform").value,s=function(K,X){return X?($r(t.rscratch,K,e,X),X):Er(t.rscratch,K,e)};if(!n)return"";i=="none"||(i=="uppercase"?n=n.toUpperCase():i=="lowercase"&&(n=n.toLowerCase()));var o=r.pstyle("text-wrap").value;if(o==="wrap"){var l=s("labelKey");if(l!=null&&s("labelWrapKey")===l)return s("labelWrapCachedText");for(var u="​",v=n.split(`
`),f=r.pstyle("text-max-width").pfValue,c=r.pstyle("text-overflow-wrap").value,h=c==="anywhere",d=[],y=/[\s\u200b]+|$/g,g=0;g<v.length;g++){var p=v[g],m=this.calculateLabelDimensions(r,p),b=m.width;if(h){var w=p.split("").join(u);p=w}if(b>f){var E=p.matchAll(y),C="",x=0,k=Tr(E),S;try{for(k.s();!(S=k.n()).done;){var P=S.value,D=P[0],A=p.substring(x,P.index);x=P.index+D.length;var B=C.length===0?A:C+A+D,R=this.calculateLabelDimensions(r,B),M=R.width;M<=f?C+=A+D:(C&&d.push(C),C=A+D)}}catch(F){k.e(F)}finally{k.f()}C.match(/^[\s\u200b]+$/)||d.push(C)}else d.push(p)}s("labelWrapCachedLines",d),n=s("labelWrapCachedText",d.join(`
`)),s("labelWrapKey",l)}else if(o==="ellipsis"){var I=r.pstyle("text-max-width").pfValue,L="",O="…",V=!1;if(this.calculateLabelDimensions(r,n).width<I)return n;for(var G=0;G<n.length;G++){var N=this.calculateLabelDimensions(r,L+n[G]+O).width;if(N>I)break;L+=n[G],G===n.length-1&&(V=!0)}return V||(L+=O),L}return n};Gr.getLabelJustification=function(r){var e=r.pstyle("text-justification").strValue,t=r.pstyle("text-halign").strValue;if(e==="auto")if(r.isNode())switch(t){case"left":return"right";case"right":return"left";default:return"center"}else return"center";else return e};Gr.calculateLabelDimensions=function(r,e){var t=this,a=t.cy.window(),n=a.document,i=0,s=r.pstyle("font-style").strValue,o=r.pstyle("font-size").pfValue,l=r.pstyle("font-family").strValue,u=r.pstyle("font-weight").strValue,v=this.labelCalcCanvas,f=this.labelCalcCanvasContext;if(!v){v=this.labelCalcCanvas=n.createElement("canvas"),f=this.labelCalcCanvasContext=v.getContext("2d");var c=v.style;c.position="absolute",c.left="-9999px",c.top="-9999px",c.zIndex="-1",c.visibility="hidden",c.pointerEvents="none"}f.font="".concat(s," ").concat(u," ").concat(o,"px ").concat(l);for(var h=0,d=0,y=e.split(`
`),g=0;g<y.length;g++){var p=y[g],m=f.measureText(p),b=Math.ceil(m.width),w=o;h=Math.max(b,h),d+=w}return h+=i,d+=i,{width:h,height:d}};Gr.calculateLabelAngle=function(r,e){var t=r._private,a=t.rscratch,n=r.isEdge(),i=e?e+"-":"",s=r.pstyle(i+"text-rotation"),o=s.strValue;return o==="none"?0:n&&o==="autorotate"?a.labelAutoAngle:o==="autorotate"?0:s.pfValue};Gr.calculateLabelAngles=function(r){var e=this,t=r.isEdge(),a=r._private,n=a.rscratch;n.labelAngle=e.calculateLabelAngle(r),t&&(n.sourceLabelAngle=e.calculateLabelAngle(r,"source"),n.targetLabelAngle=e.calculateLabelAngle(r,"target"))};var cf={},Bl=28,Pl=!1;cf.getNodeShape=function(r){var e=this,t=r.pstyle("shape").value;if(t==="cutrectangle"&&(r.width()<Bl||r.height()<Bl))return Pl||(Le("The `cutrectangle` node shape can not be used at small sizes so `rectangle` is used instead"),Pl=!0),"rectangle";if(r.isParent())return t==="rectangle"||t==="roundrectangle"||t==="round-rectangle"||t==="cutrectangle"||t==="cut-rectangle"||t==="barrel"?t:"rectangle";if(t==="polygon"){var a=r.pstyle("shape-polygon-points").value;return e.nodeShapes.makePolygon(a).name}return t};var $n={};$n.registerCalculationListeners=function(){var r=this.cy,e=r.collection(),t=this,a=function(s){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(e.merge(s),o)for(var l=0;l<s.length;l++){var u=s[l],v=u._private,f=v.rstyle;f.clean=!1,f.cleanConnected=!1}};t.binder(r).on("bounds.* dirty.*",function(s){var o=s.target;a(o)}).on("style.* background.*",function(s){var o=s.target;a(o,!1)});var n=function(s){if(s){var o=t.onUpdateEleCalcsFns;e.cleanStyle();for(var l=0;l<e.length;l++){var u=e[l],v=u._private.rstyle;u.isNode()&&!v.cleanConnected&&(a(u.connectedEdges()),v.cleanConnected=!0)}if(o)for(var f=0;f<o.length;f++){var c=o[f];c(s,e)}t.recalculateRenderedStyle(e),e=r.collection()}};t.flushRenderedStyleQueue=function(){n(!0)},t.beforeRender(n,t.beforeRenderPriorities.eleCalcs)};$n.onUpdateEleCalcs=function(r){var e=this.onUpdateEleCalcsFns=this.onUpdateEleCalcsFns||[];e.push(r)};$n.recalculateRenderedStyle=function(r,e){var t=function(w){return w._private.rstyle.cleanConnected};if(r.length!==0){var a=[],n=[];if(!this.destroyed){e===void 0&&(e=!0);for(var i=0;i<r.length;i++){var s=r[i],o=s._private,l=o.rstyle;s.isEdge()&&(!t(s.source())||!t(s.target()))&&(l.clean=!1),s.isEdge()&&s.isBundledBezier()&&s.parallelEdges().some(function(b){return!b._private.rstyle.clean&&b.isBundledBezier()})&&(l.clean=!1),!(e&&l.clean||s.removed())&&s.pstyle("display").value!=="none"&&(o.group==="nodes"?n.push(s):a.push(s),l.clean=!0)}for(var u=0;u<n.length;u++){var v=n[u],f=v._private,c=f.rstyle,h=v.position();this.recalculateNodeLabelProjection(v),c.nodeX=h.x,c.nodeY=h.y,c.nodeW=v.pstyle("width").pfValue,c.nodeH=v.pstyle("height").pfValue}this.recalculateEdgeProjections(a);for(var d=0;d<a.length;d++){var y=a[d],g=y._private,p=g.rstyle,m=g.rscratch;p.srcX=m.arrowStartX,p.srcY=m.arrowStartY,p.tgtX=m.arrowEndX,p.tgtY=m.arrowEndY,p.midX=m.midX,p.midY=m.midY,p.labelAngle=m.labelAngle,p.sourceLabelAngle=m.sourceLabelAngle,p.targetLabelAngle=m.targetLabelAngle}}}};var Kn={};Kn.updateCachedGrabbedEles=function(){var r=this.cachedZSortedEles;if(r){r.drag=[],r.nondrag=[];for(var e=[],t=0;t<r.length;t++){var a=r[t],n=a._private.rscratch;a.grabbed()&&!a.isParent()?e.push(a):n.inDragLayer?r.drag.push(a):r.nondrag.push(a)}for(var t=0;t<e.length;t++){var a=e[t];r.drag.push(a)}}};Kn.invalidateCachedZSortedEles=function(){this.cachedZSortedEles=null};Kn.getCachedZSortedEles=function(r){if(r||!this.cachedZSortedEles){var e=this.cy.mutableElements().toArray();e.sort($v),e.interactive=e.filter(function(t){return t.interactive()}),this.cachedZSortedEles=e,this.updateCachedGrabbedEles()}else e=this.cachedZSortedEles;return e};var df={};[Pt,Dn,pr,Va,ho,Gr,cf,$n,Kn].forEach(function(r){he(df,r)});var hf={};hf.getCachedImage=function(r,e,t){var a=this,n=a.imageCache=a.imageCache||{},i=n[r];if(i)return i.image.complete||i.image.addEventListener("load",t),i.image;i=n[r]=n[r]||{};var s=i.image=new Image;s.addEventListener("load",t),s.addEventListener("error",function(){s.error=!0});var o="data:",l=r.substring(0,o.length).toLowerCase()===o;return l||(e=e==="null"?null:e,s.crossOrigin=e),s.src=r,s};var aa={};aa.registerBinding=function(r,e,t,a){var n=Array.prototype.slice.apply(arguments,[1]);if(Array.isArray(r)){for(var i=[],s=0;s<r.length;s++){var o=r[s];if(o!==void 0){var l=this.binder(o);i.push(l.on.apply(l,n))}}return i}var l=this.binder(r);return l.on.apply(l,n)};aa.binder=function(r){var e=this,t=e.cy.window(),a=r===t||r===t.document||r===t.document.body||ic(r);if(e.supportsPassiveEvents==null){var n=!1;try{var i=Object.defineProperty({},"passive",{get:function(){return n=!0,!0}});t.addEventListener("test",null,i)}catch{}e.supportsPassiveEvents=n}var s=function(l,u,v){var f=Array.prototype.slice.call(arguments);return a&&e.supportsPassiveEvents&&(f[2]={capture:v??!1,passive:!1,once:!1}),e.bindings.push({target:r,args:f}),(r.addEventListener||r.on).apply(r,f),this};return{on:s,addEventListener:s,addListener:s,bind:s}};aa.nodeIsDraggable=function(r){return r&&r.isNode()&&!r.locked()&&r.grabbable()};aa.nodeIsGrabbable=function(r){return this.nodeIsDraggable(r)&&r.interactive()};aa.load=function(){var r=this,e=r.cy.window(),t=function(T){return T.selected()},a=function(T){var _=T.getRootNode();if(_&&_.nodeType===11&&_.host!==void 0)return _},n=function(T,_,W,U){T==null&&(T=r.cy);for(var $=0;$<_.length;$++){var ue=_[$];T.emit({originalEvent:W,type:ue,position:U})}},i=function(T){return T.shiftKey||T.metaKey||T.ctrlKey},s=function(T,_){var W=!0;if(r.cy.hasCompoundNodes()&&T&&T.pannable())for(var U=0;_&&U<_.length;U++){var T=_[U];if(T.isNode()&&T.isParent()&&!T.pannable()){W=!1;break}}else W=!0;return W},o=function(T){T[0]._private.grabbed=!0},l=function(T){T[0]._private.grabbed=!1},u=function(T){T[0]._private.rscratch.inDragLayer=!0},v=function(T){T[0]._private.rscratch.inDragLayer=!1},f=function(T){T[0]._private.rscratch.isGrabTarget=!0},c=function(T){T[0]._private.rscratch.isGrabTarget=!1},h=function(T,_){var W=_.addToList,U=W.has(T);!U&&T.grabbable()&&!T.locked()&&(W.merge(T),o(T))},d=function(T,_){if(T.cy().hasCompoundNodes()&&!(_.inDragLayer==null&&_.addToList==null)){var W=T.descendants();_.inDragLayer&&(W.forEach(u),W.connectedEdges().forEach(u)),_.addToList&&h(W,_)}},y=function(T,_){_=_||{};var W=T.cy().hasCompoundNodes();_.inDragLayer&&(T.forEach(u),T.neighborhood().stdFilter(function(U){return!W||U.isEdge()}).forEach(u)),_.addToList&&T.forEach(function(U){h(U,_)}),d(T,_),m(T,{inDragLayer:_.inDragLayer}),r.updateCachedGrabbedEles()},g=y,p=function(T){T&&(r.getCachedZSortedEles().forEach(function(_){l(_),v(_),c(_)}),r.updateCachedGrabbedEles())},m=function(T,_){if(!(_.inDragLayer==null&&_.addToList==null)&&T.cy().hasCompoundNodes()){var W=T.ancestors().orphans();if(!W.same(T)){var U=W.descendants().spawnSelf().merge(W).unmerge(T).unmerge(T.descendants()),$=U.connectedEdges();_.inDragLayer&&($.forEach(u),U.forEach(u)),_.addToList&&U.forEach(function(ue){h(ue,_)})}}},b=function(){document.activeElement!=null&&document.activeElement.blur!=null&&document.activeElement.blur()},w=typeof MutationObserver<"u",E=typeof ResizeObserver<"u";w?(r.removeObserver=new MutationObserver(function(Y){for(var T=0;T<Y.length;T++){var _=Y[T],W=_.removedNodes;if(W)for(var U=0;U<W.length;U++){var $=W[U];if($===r.container){r.destroy();break}}}}),r.container.parentNode&&r.removeObserver.observe(r.container.parentNode,{childList:!0})):r.registerBinding(r.container,"DOMNodeRemoved",function(Y){r.destroy()});var C=Oa(function(){r.cy.resize()},100);w&&(r.styleObserver=new MutationObserver(C),r.styleObserver.observe(r.container,{attributes:!0})),r.registerBinding(e,"resize",C),E&&(r.resizeObserver=new ResizeObserver(C),r.resizeObserver.observe(r.container));var x=function(T,_){for(;T!=null;)_(T),T=T.parentNode},k=function(){r.invalidateContainerClientCoordsCache()};x(r.container,function(Y){r.registerBinding(Y,"transitionend",k),r.registerBinding(Y,"animationend",k),r.registerBinding(Y,"scroll",k)}),r.registerBinding(r.container,"contextmenu",function(Y){Y.preventDefault()});var S=function(){return r.selection[4]!==0},P=function(T){for(var _=r.findContainerClientCoords(),W=_[0],U=_[1],$=_[2],ue=_[3],j=T.touches?T.touches:[T],ve=!1,Ee=0;Ee<j.length;Ee++){var Se=j[Ee];if(W<=Se.clientX&&Se.clientX<=W+$&&U<=Se.clientY&&Se.clientY<=U+ue){ve=!0;break}}if(!ve)return!1;for(var pe=r.container,Ce=T.target,me=Ce.parentNode,xe=!1;me;){if(me===pe){xe=!0;break}me=me.parentNode}return!!xe};r.registerBinding(r.container,"mousedown",function(T){if(P(T)&&!(r.hoverData.which===1&&T.which!==1)){T.preventDefault(),b(),r.hoverData.capture=!0,r.hoverData.which=T.which;var _=r.cy,W=[T.clientX,T.clientY],U=r.projectIntoViewport(W[0],W[1]),$=r.selection,ue=r.findNearestElements(U[0],U[1],!0,!1),j=ue[0],ve=r.dragData.possibleDragElements;r.hoverData.mdownPos=U,r.hoverData.mdownGPos=W;var Ee=function(){r.hoverData.tapholdCancelled=!1,clearTimeout(r.hoverData.tapholdTimeout),r.hoverData.tapholdTimeout=setTimeout(function(){if(!r.hoverData.tapholdCancelled){var Oe=r.hoverData.down;Oe?Oe.emit({originalEvent:T,type:"taphold",position:{x:U[0],y:U[1]}}):_.emit({originalEvent:T,type:"taphold",position:{x:U[0],y:U[1]}})}},r.tapholdDuration)};if(T.which==3){r.hoverData.cxtStarted=!0;var Se={originalEvent:T,type:"cxttapstart",position:{x:U[0],y:U[1]}};j?(j.activate(),j.emit(Se),r.hoverData.down=j):_.emit(Se),r.hoverData.downTime=new Date().getTime(),r.hoverData.cxtDragged=!1}else if(T.which==1){j&&j.activate();{if(j!=null&&r.nodeIsGrabbable(j)){var pe=function(Oe){return{originalEvent:T,type:Oe,position:{x:U[0],y:U[1]}}},Ce=function(Oe){Oe.emit(pe("grab"))};if(f(j),!j.selected())ve=r.dragData.possibleDragElements=_.collection(),g(j,{addToList:ve}),j.emit(pe("grabon")).emit(pe("grab"));else{ve=r.dragData.possibleDragElements=_.collection();var me=_.$(function(xe){return xe.isNode()&&xe.selected()&&r.nodeIsGrabbable(xe)});y(me,{addToList:ve}),j.emit(pe("grabon")),me.forEach(Ce)}r.redrawHint("eles",!0),r.redrawHint("drag",!0)}r.hoverData.down=j,r.hoverData.downs=ue,r.hoverData.downTime=new Date().getTime()}n(j,["mousedown","tapstart","vmousedown"],T,{x:U[0],y:U[1]}),j==null?($[4]=1,r.data.bgActivePosistion={x:U[0],y:U[1]},r.redrawHint("select",!0),r.redraw()):j.pannable()&&($[4]=1),Ee()}$[0]=$[2]=U[0],$[1]=$[3]=U[1]}},!1);var D=a(r.container);r.registerBinding([e,D],"mousemove",function(T){var _=r.hoverData.capture;if(!(!_&&!P(T))){var W=!1,U=r.cy,$=U.zoom(),ue=[T.clientX,T.clientY],j=r.projectIntoViewport(ue[0],ue[1]),ve=r.hoverData.mdownPos,Ee=r.hoverData.mdownGPos,Se=r.selection,pe=null;!r.hoverData.draggingEles&&!r.hoverData.dragging&&!r.hoverData.selecting&&(pe=r.findNearestElement(j[0],j[1],!0,!1));var Ce=r.hoverData.last,me=r.hoverData.down,xe=[j[0]-Se[2],j[1]-Se[3]],Oe=r.dragData.possibleDragElements,Xe;if(Ee){var or=ue[0]-Ee[0],ar=or*or,Ke=ue[1]-Ee[1],ur=Ke*Ke,Qe=ar+ur;r.hoverData.isOverThresholdDrag=Xe=Qe>=r.desktopTapThreshold2}var br=i(T);Xe&&(r.hoverData.tapholdCancelled=!0);var Or=function(){var Nr=r.hoverData.dragDelta=r.hoverData.dragDelta||[];Nr.length===0?(Nr.push(xe[0]),Nr.push(xe[1])):(Nr[0]+=xe[0],Nr[1]+=xe[1])};W=!0,n(pe,["mousemove","vmousemove","tapdrag"],T,{x:j[0],y:j[1]});var Jr=function(){r.data.bgActivePosistion=void 0,r.hoverData.selecting||U.emit({originalEvent:T,type:"boxstart",position:{x:j[0],y:j[1]}}),Se[4]=1,r.hoverData.selecting=!0,r.redrawHint("select",!0),r.redraw()};if(r.hoverData.which===3){if(Xe){var Wr={originalEvent:T,type:"cxtdrag",position:{x:j[0],y:j[1]}};me?me.emit(Wr):U.emit(Wr),r.hoverData.cxtDragged=!0,(!r.hoverData.cxtOver||pe!==r.hoverData.cxtOver)&&(r.hoverData.cxtOver&&r.hoverData.cxtOver.emit({originalEvent:T,type:"cxtdragout",position:{x:j[0],y:j[1]}}),r.hoverData.cxtOver=pe,pe&&pe.emit({originalEvent:T,type:"cxtdragover",position:{x:j[0],y:j[1]}}))}}else if(r.hoverData.dragging){if(W=!0,U.panningEnabled()&&U.userPanningEnabled()){var Rt;if(r.hoverData.justStartedPan){var Ga=r.hoverData.mdownPos;Rt={x:(j[0]-Ga[0])*$,y:(j[1]-Ga[1])*$},r.hoverData.justStartedPan=!1}else Rt={x:xe[0]*$,y:xe[1]*$};U.panBy(Rt),U.emit("dragpan"),r.hoverData.dragged=!0}j=r.projectIntoViewport(T.clientX,T.clientY)}else if(Se[4]==1&&(me==null||me.pannable())){if(Xe){if(!r.hoverData.dragging&&U.boxSelectionEnabled()&&(br||!U.panningEnabled()||!U.userPanningEnabled()))Jr();else if(!r.hoverData.selecting&&U.panningEnabled()&&U.userPanningEnabled()){var gt=s(me,r.hoverData.downs);gt&&(r.hoverData.dragging=!0,r.hoverData.justStartedPan=!0,Se[4]=0,r.data.bgActivePosistion=_t(ve),r.redrawHint("select",!0),r.redraw())}me&&me.pannable()&&me.active()&&me.unactivate()}}else{if(me&&me.pannable()&&me.active()&&me.unactivate(),(!me||!me.grabbed())&&pe!=Ce&&(Ce&&n(Ce,["mouseout","tapdragout"],T,{x:j[0],y:j[1]}),pe&&n(pe,["mouseover","tapdragover"],T,{x:j[0],y:j[1]}),r.hoverData.last=pe),me)if(Xe){if(U.boxSelectionEnabled()&&br)me&&me.grabbed()&&(p(Oe),me.emit("freeon"),Oe.emit("free"),r.dragData.didDrag&&(me.emit("dragfreeon"),Oe.emit("dragfree"))),Jr();else if(me&&me.grabbed()&&r.nodeIsDraggable(me)){var wr=!r.dragData.didDrag;wr&&r.redrawHint("eles",!0),r.dragData.didDrag=!0,r.hoverData.draggingEles||y(Oe,{inDragLayer:!0});var dr={x:0,y:0};if(te(xe[0])&&te(xe[1])&&(dr.x+=xe[0],dr.y+=xe[1],wr)){var xr=r.hoverData.dragDelta;xr&&te(xr[0])&&te(xr[1])&&(dr.x+=xr[0],dr.y+=xr[1])}r.hoverData.draggingEles=!0,Oe.silentShift(dr).emit("position drag"),r.redrawHint("drag",!0),r.redraw()}}else Or();W=!0}if(Se[2]=j[0],Se[3]=j[1],W)return T.stopPropagation&&T.stopPropagation(),T.preventDefault&&T.preventDefault(),!1}},!1);var A,B,R;r.registerBinding(e,"mouseup",function(T){if(!(r.hoverData.which===1&&T.which!==1&&r.hoverData.capture)){var _=r.hoverData.capture;if(_){r.hoverData.capture=!1;var W=r.cy,U=r.projectIntoViewport(T.clientX,T.clientY),$=r.selection,ue=r.findNearestElement(U[0],U[1],!0,!1),j=r.dragData.possibleDragElements,ve=r.hoverData.down,Ee=i(T);if(r.data.bgActivePosistion&&(r.redrawHint("select",!0),r.redraw()),r.hoverData.tapholdCancelled=!0,r.data.bgActivePosistion=void 0,ve&&ve.unactivate(),r.hoverData.which===3){var Se={originalEvent:T,type:"cxttapend",position:{x:U[0],y:U[1]}};if(ve?ve.emit(Se):W.emit(Se),!r.hoverData.cxtDragged){var pe={originalEvent:T,type:"cxttap",position:{x:U[0],y:U[1]}};ve?ve.emit(pe):W.emit(pe)}r.hoverData.cxtDragged=!1,r.hoverData.which=null}else if(r.hoverData.which===1){if(n(ue,["mouseup","tapend","vmouseup"],T,{x:U[0],y:U[1]}),!r.dragData.didDrag&&!r.hoverData.dragged&&!r.hoverData.selecting&&!r.hoverData.isOverThresholdDrag&&(n(ve,["click","tap","vclick"],T,{x:U[0],y:U[1]}),B=!1,T.timeStamp-R<=W.multiClickDebounceTime()?(A&&clearTimeout(A),B=!0,R=null,n(ve,["dblclick","dbltap","vdblclick"],T,{x:U[0],y:U[1]})):(A=setTimeout(function(){B||n(ve,["oneclick","onetap","voneclick"],T,{x:U[0],y:U[1]})},W.multiClickDebounceTime()),R=T.timeStamp)),ve==null&&!r.dragData.didDrag&&!r.hoverData.selecting&&!r.hoverData.dragged&&!i(T)&&(W.$(t).unselect(["tapunselect"]),j.length>0&&r.redrawHint("eles",!0),r.dragData.possibleDragElements=j=W.collection()),ue==ve&&!r.dragData.didDrag&&!r.hoverData.selecting&&ue!=null&&ue._private.selectable&&(r.hoverData.dragging||(W.selectionType()==="additive"||Ee?ue.selected()?ue.unselect(["tapunselect"]):ue.select(["tapselect"]):Ee||(W.$(t).unmerge(ue).unselect(["tapunselect"]),ue.select(["tapselect"]))),r.redrawHint("eles",!0)),r.hoverData.selecting){var Ce=W.collection(r.getAllInBox($[0],$[1],$[2],$[3]));r.redrawHint("select",!0),Ce.length>0&&r.redrawHint("eles",!0),W.emit({type:"boxend",originalEvent:T,position:{x:U[0],y:U[1]}});var me=function(Xe){return Xe.selectable()&&!Xe.selected()};W.selectionType()==="additive"||Ee||W.$(t).unmerge(Ce).unselect(),Ce.emit("box").stdFilter(me).select().emit("boxselect"),r.redraw()}if(r.hoverData.dragging&&(r.hoverData.dragging=!1,r.redrawHint("select",!0),r.redrawHint("eles",!0),r.redraw()),!$[4]){r.redrawHint("drag",!0),r.redrawHint("eles",!0);var xe=ve&&ve.grabbed();p(j),xe&&(ve.emit("freeon"),j.emit("free"),r.dragData.didDrag&&(ve.emit("dragfreeon"),j.emit("dragfree")))}}$[4]=0,r.hoverData.down=null,r.hoverData.cxtStarted=!1,r.hoverData.draggingEles=!1,r.hoverData.selecting=!1,r.hoverData.isOverThresholdDrag=!1,r.dragData.didDrag=!1,r.hoverData.dragged=!1,r.hoverData.dragDelta=[],r.hoverData.mdownPos=null,r.hoverData.mdownGPos=null,r.hoverData.which=null}}},!1);var M=[],I=4,L,O=1e5,V=function(T,_){for(var W=0;W<T.length;W++)if(T[W]%_!==0)return!1;return!0},G=function(T){for(var _=Math.abs(T[0]),W=1;W<T.length;W++)if(Math.abs(T[W])!==_)return!1;return!0},N=function(T){var _=!1,W=T.deltaY;if(W==null&&(T.wheelDeltaY!=null?W=T.wheelDeltaY/4:T.wheelDelta!=null&&(W=T.wheelDelta/4)),L==null)if(M.length>=I){var U=M;if(L=V(U,5),!L){var $=Math.abs(U[0]);L=G(U)&&$>5}if(L)for(var ue=0;ue<U.length;ue++)O=Math.min(Math.abs(U[ue]),O)}else M.push(W),_=!0;else L&&(O=Math.min(Math.abs(W),O));if(!r.scrollingPage){var j=r.cy,ve=j.zoom(),Ee=j.pan(),Se=r.projectIntoViewport(T.clientX,T.clientY),pe=[Se[0]*ve+Ee.x,Se[1]*ve+Ee.y];if(r.hoverData.draggingEles||r.hoverData.dragging||r.hoverData.cxtStarted||S()){T.preventDefault();return}if(j.panningEnabled()&&j.userPanningEnabled()&&j.zoomingEnabled()&&j.userZoomingEnabled()){T.preventDefault(),r.data.wheelZooming=!0,clearTimeout(r.data.wheelTimeout),r.data.wheelTimeout=setTimeout(function(){r.data.wheelZooming=!1,r.redrawHint("eles",!0),r.redraw()},150);var Ce;_&&Math.abs(W)>5&&(W=js(W)*5),Ce=W/-250,L&&(Ce/=O,Ce*=3),Ce=Ce*r.wheelSensitivity;var me=T.deltaMode===1;me&&(Ce*=33);var xe=j.zoom()*Math.pow(10,Ce);T.type==="gesturechange"&&(xe=r.gestureStartZoom*T.scale),j.zoom({level:xe,renderedPosition:{x:pe[0],y:pe[1]}}),j.emit(T.type==="gesturechange"?"pinchzoom":"scrollzoom")}}};r.registerBinding(r.container,"wheel",N,!0),r.registerBinding(e,"scroll",function(T){r.scrollingPage=!0,clearTimeout(r.scrollingPageTimeout),r.scrollingPageTimeout=setTimeout(function(){r.scrollingPage=!1},250)},!0),r.registerBinding(r.container,"gesturestart",function(T){r.gestureStartZoom=r.cy.zoom(),r.hasTouchStarted||T.preventDefault()},!0),r.registerBinding(r.container,"gesturechange",function(Y){r.hasTouchStarted||N(Y)},!0),r.registerBinding(r.container,"mouseout",function(T){var _=r.projectIntoViewport(T.clientX,T.clientY);r.cy.emit({originalEvent:T,type:"mouseout",position:{x:_[0],y:_[1]}})},!1),r.registerBinding(r.container,"mouseover",function(T){var _=r.projectIntoViewport(T.clientX,T.clientY);r.cy.emit({originalEvent:T,type:"mouseover",position:{x:_[0],y:_[1]}})},!1);var F,K,X,Q,Z,re,ae,J,z,q,H,ee,ne,be=function(T,_,W,U){return Math.sqrt((W-T)*(W-T)+(U-_)*(U-_))},_e=function(T,_,W,U){return(W-T)*(W-T)+(U-_)*(U-_)},Ie;r.registerBinding(r.container,"touchstart",Ie=function(T){if(r.hasTouchStarted=!0,!!P(T)){b(),r.touchData.capture=!0,r.data.bgActivePosistion=void 0;var _=r.cy,W=r.touchData.now,U=r.touchData.earlier;if(T.touches[0]){var $=r.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);W[0]=$[0],W[1]=$[1]}if(T.touches[1]){var $=r.projectIntoViewport(T.touches[1].clientX,T.touches[1].clientY);W[2]=$[0],W[3]=$[1]}if(T.touches[2]){var $=r.projectIntoViewport(T.touches[2].clientX,T.touches[2].clientY);W[4]=$[0],W[5]=$[1]}if(T.touches[1]){r.touchData.singleTouchMoved=!0,p(r.dragData.touchDragEles);var ue=r.findContainerClientCoords();z=ue[0],q=ue[1],H=ue[2],ee=ue[3],F=T.touches[0].clientX-z,K=T.touches[0].clientY-q,X=T.touches[1].clientX-z,Q=T.touches[1].clientY-q,ne=0<=F&&F<=H&&0<=X&&X<=H&&0<=K&&K<=ee&&0<=Q&&Q<=ee;var j=_.pan(),ve=_.zoom();Z=be(F,K,X,Q),re=_e(F,K,X,Q),ae=[(F+X)/2,(K+Q)/2],J=[(ae[0]-j.x)/ve,(ae[1]-j.y)/ve];var Ee=200,Se=Ee*Ee;if(re<Se&&!T.touches[2]){var pe=r.findNearestElement(W[0],W[1],!0,!0),Ce=r.findNearestElement(W[2],W[3],!0,!0);pe&&pe.isNode()?(pe.activate().emit({originalEvent:T,type:"cxttapstart",position:{x:W[0],y:W[1]}}),r.touchData.start=pe):Ce&&Ce.isNode()?(Ce.activate().emit({originalEvent:T,type:"cxttapstart",position:{x:W[0],y:W[1]}}),r.touchData.start=Ce):_.emit({originalEvent:T,type:"cxttapstart",position:{x:W[0],y:W[1]}}),r.touchData.start&&(r.touchData.start._private.grabbed=!1),r.touchData.cxt=!0,r.touchData.cxtDragged=!1,r.data.bgActivePosistion=void 0,r.redraw();return}}if(T.touches[2])_.boxSelectionEnabled()&&T.preventDefault();else if(!T.touches[1]){if(T.touches[0]){var me=r.findNearestElements(W[0],W[1],!0,!0),xe=me[0];if(xe!=null&&(xe.activate(),r.touchData.start=xe,r.touchData.starts=me,r.nodeIsGrabbable(xe))){var Oe=r.dragData.touchDragEles=_.collection(),Xe=null;r.redrawHint("eles",!0),r.redrawHint("drag",!0),xe.selected()?(Xe=_.$(function(Qe){return Qe.selected()&&r.nodeIsGrabbable(Qe)}),y(Xe,{addToList:Oe})):g(xe,{addToList:Oe}),f(xe);var or=function(br){return{originalEvent:T,type:br,position:{x:W[0],y:W[1]}}};xe.emit(or("grabon")),Xe?Xe.forEach(function(Qe){Qe.emit(or("grab"))}):xe.emit(or("grab"))}n(xe,["touchstart","tapstart","vmousedown"],T,{x:W[0],y:W[1]}),xe==null&&(r.data.bgActivePosistion={x:$[0],y:$[1]},r.redrawHint("select",!0),r.redraw()),r.touchData.singleTouchMoved=!1,r.touchData.singleTouchStartTime=+new Date,clearTimeout(r.touchData.tapholdTimeout),r.touchData.tapholdTimeout=setTimeout(function(){r.touchData.singleTouchMoved===!1&&!r.pinching&&!r.touchData.selecting&&n(r.touchData.start,["taphold"],T,{x:W[0],y:W[1]})},r.tapholdDuration)}}if(T.touches.length>=1){for(var ar=r.touchData.startPosition=[null,null,null,null,null,null],Ke=0;Ke<W.length;Ke++)ar[Ke]=U[Ke]=W[Ke];var ur=T.touches[0];r.touchData.startGPosition=[ur.clientX,ur.clientY]}}},!1);var se;r.registerBinding(e,"touchmove",se=function(T){var _=r.touchData.capture;if(!(!_&&!P(T))){var W=r.selection,U=r.cy,$=r.touchData.now,ue=r.touchData.earlier,j=U.zoom();if(T.touches[0]){var ve=r.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);$[0]=ve[0],$[1]=ve[1]}if(T.touches[1]){var ve=r.projectIntoViewport(T.touches[1].clientX,T.touches[1].clientY);$[2]=ve[0],$[3]=ve[1]}if(T.touches[2]){var ve=r.projectIntoViewport(T.touches[2].clientX,T.touches[2].clientY);$[4]=ve[0],$[5]=ve[1]}var Ee=r.touchData.startGPosition,Se;if(_&&T.touches[0]&&Ee){for(var pe=[],Ce=0;Ce<$.length;Ce++)pe[Ce]=$[Ce]-ue[Ce];var me=T.touches[0].clientX-Ee[0],xe=me*me,Oe=T.touches[0].clientY-Ee[1],Xe=Oe*Oe,or=xe+Xe;Se=or>=r.touchTapThreshold2}if(_&&r.touchData.cxt){T.preventDefault();var ar=T.touches[0].clientX-z,Ke=T.touches[0].clientY-q,ur=T.touches[1].clientX-z,Qe=T.touches[1].clientY-q,br=_e(ar,Ke,ur,Qe),Or=br/re,Jr=150,Wr=Jr*Jr,Rt=1.5,Ga=Rt*Rt;if(Or>=Ga||br>=Wr){r.touchData.cxt=!1,r.data.bgActivePosistion=void 0,r.redrawHint("select",!0);var gt={originalEvent:T,type:"cxttapend",position:{x:$[0],y:$[1]}};r.touchData.start?(r.touchData.start.unactivate().emit(gt),r.touchData.start=null):U.emit(gt)}}if(_&&r.touchData.cxt){var gt={originalEvent:T,type:"cxtdrag",position:{x:$[0],y:$[1]}};r.data.bgActivePosistion=void 0,r.redrawHint("select",!0),r.touchData.start?r.touchData.start.emit(gt):U.emit(gt),r.touchData.start&&(r.touchData.start._private.grabbed=!1),r.touchData.cxtDragged=!0;var wr=r.findNearestElement($[0],$[1],!0,!0);(!r.touchData.cxtOver||wr!==r.touchData.cxtOver)&&(r.touchData.cxtOver&&r.touchData.cxtOver.emit({originalEvent:T,type:"cxtdragout",position:{x:$[0],y:$[1]}}),r.touchData.cxtOver=wr,wr&&wr.emit({originalEvent:T,type:"cxtdragover",position:{x:$[0],y:$[1]}}))}else if(_&&T.touches[2]&&U.boxSelectionEnabled())T.preventDefault(),r.data.bgActivePosistion=void 0,this.lastThreeTouch=+new Date,r.touchData.selecting||U.emit({originalEvent:T,type:"boxstart",position:{x:$[0],y:$[1]}}),r.touchData.selecting=!0,r.touchData.didSelect=!0,W[4]=1,!W||W.length===0||W[0]===void 0?(W[0]=($[0]+$[2]+$[4])/3,W[1]=($[1]+$[3]+$[5])/3,W[2]=($[0]+$[2]+$[4])/3+1,W[3]=($[1]+$[3]+$[5])/3+1):(W[2]=($[0]+$[2]+$[4])/3,W[3]=($[1]+$[3]+$[5])/3),r.redrawHint("select",!0),r.redraw();else if(_&&T.touches[1]&&!r.touchData.didSelect&&U.zoomingEnabled()&&U.panningEnabled()&&U.userZoomingEnabled()&&U.userPanningEnabled()){T.preventDefault(),r.data.bgActivePosistion=void 0,r.redrawHint("select",!0);var dr=r.dragData.touchDragEles;if(dr){r.redrawHint("drag",!0);for(var xr=0;xr<dr.length;xr++){var Ha=dr[xr]._private;Ha.grabbed=!1,Ha.rscratch.inDragLayer=!1}}var Nr=r.touchData.start,ar=T.touches[0].clientX-z,Ke=T.touches[0].clientY-q,ur=T.touches[1].clientX-z,Qe=T.touches[1].clientY-q,yo=be(ar,Ke,ur,Qe),Nf=yo/Z;if(ne){var zf=ar-F,Ff=Ke-K,Vf=ur-X,qf=Qe-Q,_f=(zf+Vf)/2,Gf=(Ff+qf)/2,ia=U.zoom(),Yn=ia*Nf,Wa=U.pan(),mo=J[0]*ia+Wa.x,bo=J[1]*ia+Wa.y,Hf={x:-Yn/ia*(mo-Wa.x-_f)+mo,y:-Yn/ia*(bo-Wa.y-Gf)+bo};if(Nr&&Nr.active()){var dr=r.dragData.touchDragEles;p(dr),r.redrawHint("drag",!0),r.redrawHint("eles",!0),Nr.unactivate().emit("freeon"),dr.emit("free"),r.dragData.didDrag&&(Nr.emit("dragfreeon"),dr.emit("dragfree"))}U.viewport({zoom:Yn,pan:Hf,cancelOnFailedZoom:!0}),U.emit("pinchzoom"),Z=yo,F=ar,K=Ke,X=ur,Q=Qe,r.pinching=!0}if(T.touches[0]){var ve=r.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);$[0]=ve[0],$[1]=ve[1]}if(T.touches[1]){var ve=r.projectIntoViewport(T.touches[1].clientX,T.touches[1].clientY);$[2]=ve[0],$[3]=ve[1]}if(T.touches[2]){var ve=r.projectIntoViewport(T.touches[2].clientX,T.touches[2].clientY);$[4]=ve[0],$[5]=ve[1]}}else if(T.touches[0]&&!r.touchData.didSelect){var Ar=r.touchData.start,Xn=r.touchData.last,wr;if(!r.hoverData.draggingEles&&!r.swipePanning&&(wr=r.findNearestElement($[0],$[1],!0,!0)),_&&Ar!=null&&T.preventDefault(),_&&Ar!=null&&r.nodeIsDraggable(Ar))if(Se){var dr=r.dragData.touchDragEles,wo=!r.dragData.didDrag;wo&&y(dr,{inDragLayer:!0}),r.dragData.didDrag=!0;var sa={x:0,y:0};if(te(pe[0])&&te(pe[1])&&(sa.x+=pe[0],sa.y+=pe[1],wo)){r.redrawHint("eles",!0);var Rr=r.touchData.dragDelta;Rr&&te(Rr[0])&&te(Rr[1])&&(sa.x+=Rr[0],sa.y+=Rr[1])}r.hoverData.draggingEles=!0,dr.silentShift(sa).emit("position drag"),r.redrawHint("drag",!0),r.touchData.startPosition[0]==ue[0]&&r.touchData.startPosition[1]==ue[1]&&r.redrawHint("eles",!0),r.redraw()}else{var Rr=r.touchData.dragDelta=r.touchData.dragDelta||[];Rr.length===0?(Rr.push(pe[0]),Rr.push(pe[1])):(Rr[0]+=pe[0],Rr[1]+=pe[1])}if(n(Ar||wr,["touchmove","tapdrag","vmousemove"],T,{x:$[0],y:$[1]}),(!Ar||!Ar.grabbed())&&wr!=Xn&&(Xn&&Xn.emit({originalEvent:T,type:"tapdragout",position:{x:$[0],y:$[1]}}),wr&&wr.emit({originalEvent:T,type:"tapdragover",position:{x:$[0],y:$[1]}})),r.touchData.last=wr,_)for(var xr=0;xr<$.length;xr++)$[xr]&&r.touchData.startPosition[xr]&&Se&&(r.touchData.singleTouchMoved=!0);if(_&&(Ar==null||Ar.pannable())&&U.panningEnabled()&&U.userPanningEnabled()){var Wf=s(Ar,r.touchData.starts);Wf&&(T.preventDefault(),r.data.bgActivePosistion||(r.data.bgActivePosistion=_t(r.touchData.startPosition)),r.swipePanning?(U.panBy({x:pe[0]*j,y:pe[1]*j}),U.emit("dragpan")):Se&&(r.swipePanning=!0,U.panBy({x:me*j,y:Oe*j}),U.emit("dragpan"),Ar&&(Ar.unactivate(),r.redrawHint("select",!0),r.touchData.start=null)));var ve=r.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);$[0]=ve[0],$[1]=ve[1]}}for(var Ce=0;Ce<$.length;Ce++)ue[Ce]=$[Ce];_&&T.touches.length>0&&!r.hoverData.draggingEles&&!r.swipePanning&&r.data.bgActivePosistion!=null&&(r.data.bgActivePosistion=void 0,r.redrawHint("select",!0),r.redraw())}},!1);var oe;r.registerBinding(e,"touchcancel",oe=function(T){var _=r.touchData.start;r.touchData.capture=!1,_&&_.unactivate()});var ce,ge,de,ye;if(r.registerBinding(e,"touchend",ce=function(T){var _=r.touchData.start,W=r.touchData.capture;if(W)T.touches.length===0&&(r.touchData.capture=!1),T.preventDefault();else return;var U=r.selection;r.swipePanning=!1,r.hoverData.draggingEles=!1;var $=r.cy,ue=$.zoom(),j=r.touchData.now,ve=r.touchData.earlier;if(T.touches[0]){var Ee=r.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);j[0]=Ee[0],j[1]=Ee[1]}if(T.touches[1]){var Ee=r.projectIntoViewport(T.touches[1].clientX,T.touches[1].clientY);j[2]=Ee[0],j[3]=Ee[1]}if(T.touches[2]){var Ee=r.projectIntoViewport(T.touches[2].clientX,T.touches[2].clientY);j[4]=Ee[0],j[5]=Ee[1]}_&&_.unactivate();var Se;if(r.touchData.cxt){if(Se={originalEvent:T,type:"cxttapend",position:{x:j[0],y:j[1]}},_?_.emit(Se):$.emit(Se),!r.touchData.cxtDragged){var pe={originalEvent:T,type:"cxttap",position:{x:j[0],y:j[1]}};_?_.emit(pe):$.emit(pe)}r.touchData.start&&(r.touchData.start._private.grabbed=!1),r.touchData.cxt=!1,r.touchData.start=null,r.redraw();return}if(!T.touches[2]&&$.boxSelectionEnabled()&&r.touchData.selecting){r.touchData.selecting=!1;var Ce=$.collection(r.getAllInBox(U[0],U[1],U[2],U[3]));U[0]=void 0,U[1]=void 0,U[2]=void 0,U[3]=void 0,U[4]=0,r.redrawHint("select",!0),$.emit({type:"boxend",originalEvent:T,position:{x:j[0],y:j[1]}});var me=function(Wr){return Wr.selectable()&&!Wr.selected()};Ce.emit("box").stdFilter(me).select().emit("boxselect"),Ce.nonempty()&&r.redrawHint("eles",!0),r.redraw()}if(_!=null&&_.unactivate(),T.touches[2])r.data.bgActivePosistion=void 0,r.redrawHint("select",!0);else if(!T.touches[1]){if(!T.touches[0]){if(!T.touches[0]){r.data.bgActivePosistion=void 0,r.redrawHint("select",!0);var xe=r.dragData.touchDragEles;if(_!=null){var Oe=_._private.grabbed;p(xe),r.redrawHint("drag",!0),r.redrawHint("eles",!0),Oe&&(_.emit("freeon"),xe.emit("free"),r.dragData.didDrag&&(_.emit("dragfreeon"),xe.emit("dragfree"))),n(_,["touchend","tapend","vmouseup","tapdragout"],T,{x:j[0],y:j[1]}),_.unactivate(),r.touchData.start=null}else{var Xe=r.findNearestElement(j[0],j[1],!0,!0);n(Xe,["touchend","tapend","vmouseup","tapdragout"],T,{x:j[0],y:j[1]})}var or=r.touchData.startPosition[0]-j[0],ar=or*or,Ke=r.touchData.startPosition[1]-j[1],ur=Ke*Ke,Qe=ar+ur,br=Qe*ue*ue;r.touchData.singleTouchMoved||(_||$.$(":selected").unselect(["tapunselect"]),n(_,["tap","vclick"],T,{x:j[0],y:j[1]}),ge=!1,T.timeStamp-ye<=$.multiClickDebounceTime()?(de&&clearTimeout(de),ge=!0,ye=null,n(_,["dbltap","vdblclick"],T,{x:j[0],y:j[1]})):(de=setTimeout(function(){ge||n(_,["onetap","voneclick"],T,{x:j[0],y:j[1]})},$.multiClickDebounceTime()),ye=T.timeStamp)),_!=null&&!r.dragData.didDrag&&_._private.selectable&&br<r.touchTapThreshold2&&!r.pinching&&($.selectionType()==="single"?($.$(t).unmerge(_).unselect(["tapunselect"]),_.select(["tapselect"])):_.selected()?_.unselect(["tapunselect"]):_.select(["tapselect"]),r.redrawHint("eles",!0)),r.touchData.singleTouchMoved=!0}}}for(var Or=0;Or<j.length;Or++)ve[Or]=j[Or];r.dragData.didDrag=!1,T.touches.length===0&&(r.touchData.dragDelta=[],r.touchData.startPosition=[null,null,null,null,null,null],r.touchData.startGPosition=null,r.touchData.didSelect=!1),T.touches.length<2&&(T.touches.length===1&&(r.touchData.startGPosition=[T.touches[0].clientX,T.touches[0].clientY]),r.pinching=!1,r.redrawHint("eles",!0),r.redraw())},!1),typeof TouchEvent>"u"){var we=[],De=function(T){return{clientX:T.clientX,clientY:T.clientY,force:1,identifier:T.pointerId,pageX:T.pageX,pageY:T.pageY,radiusX:T.width/2,radiusY:T.height/2,screenX:T.screenX,screenY:T.screenY,target:T.target}},ze=function(T){return{event:T,touch:De(T)}},Ue=function(T){we.push(ze(T))},Ae=function(T){for(var _=0;_<we.length;_++){var W=we[_];if(W.event.pointerId===T.pointerId){we.splice(_,1);return}}},Ye=function(T){var _=we.filter(function(W){return W.event.pointerId===T.pointerId})[0];_.event=T,_.touch=De(T)},ke=function(T){T.touches=we.map(function(_){return _.touch})},le=function(T){return T.pointerType==="mouse"||T.pointerType===4};r.registerBinding(r.container,"pointerdown",function(Y){le(Y)||(Y.preventDefault(),Ue(Y),ke(Y),Ie(Y))}),r.registerBinding(r.container,"pointerup",function(Y){le(Y)||(Ae(Y),ke(Y),ce(Y))}),r.registerBinding(r.container,"pointercancel",function(Y){le(Y)||(Ae(Y),ke(Y),oe(Y))}),r.registerBinding(r.container,"pointermove",function(Y){le(Y)||(Y.preventDefault(),Ye(Y),ke(Y),se(Y))})}};var Zr={};Zr.generatePolygon=function(r,e){return this.nodeShapes[r]={renderer:this,name:r,points:e,draw:function(a,n,i,s,o,l){this.renderer.nodeShapeImpl("polygon",a,n,i,s,o,this.points)},intersectLine:function(a,n,i,s,o,l,u,v){return Ta(o,l,this.points,a,n,i/2,s/2,u)},checkPoint:function(a,n,i,s,o,l,u,v){return Xr(a,n,this.points,l,u,s,o,[0,-1],i)}}};Zr.generateEllipse=function(){return this.nodeShapes.ellipse={renderer:this,name:"ellipse",draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i)},intersectLine:function(e,t,a,n,i,s,o,l){return Dd(i,s,e,t,a/2+o,n/2+o)},checkPoint:function(e,t,a,n,i,s,o,l){return Ct(e,t,n,i,s,o,a)}}};Zr.generateRoundPolygon=function(r,e){return this.nodeShapes[r]={renderer:this,name:r,points:e,getOrCreateCorners:function(a,n,i,s,o,l,u){if(l[u]!==void 0&&l[u+"-cx"]===a&&l[u+"-cy"]===n)return l[u];l[u]=new Array(e.length/2),l[u+"-cx"]=a,l[u+"-cy"]=n;var v=i/2,f=s/2;o=o==="auto"?gv(i,s):o;for(var c=new Array(e.length/2),h=0;h<e.length/2;h++)c[h]={x:a+v*e[h*2],y:n+f*e[h*2+1]};var d,y,g,p,m=c.length;for(y=c[m-1],d=0;d<m;d++)g=c[d%m],p=c[(d+1)%m],l[u][d]=co(y,g,p,o),y=g,g=p;return l[u]},draw:function(a,n,i,s,o,l,u){this.renderer.nodeShapeImpl("round-polygon",a,n,i,s,o,this.points,this.getOrCreateCorners(n,i,s,o,l,u,"drawCorners"))},intersectLine:function(a,n,i,s,o,l,u,v,f){return kd(o,l,this.points,a,n,i,s,u,this.getOrCreateCorners(a,n,i,s,v,f,"corners"))},checkPoint:function(a,n,i,s,o,l,u,v,f){return Sd(a,n,this.points,l,u,s,o,this.getOrCreateCorners(l,u,s,o,v,f,"corners"))}}};Zr.generateRoundRectangle=function(){return this.nodeShapes["round-rectangle"]=this.nodeShapes.roundrectangle={renderer:this,name:"round-rectangle",points:yr(4,0),draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i,this.points,s)},intersectLine:function(e,t,a,n,i,s,o,l){return dv(i,s,e,t,a,n,o,l)},checkPoint:function(e,t,a,n,i,s,o,l){var u=n/2,v=i/2;l=l==="auto"?st(n,i):l,l=Math.min(u,v,l);var f=l*2;return!!(Xr(e,t,this.points,s,o,n,i-f,[0,-1],a)||Xr(e,t,this.points,s,o,n-f,i,[0,-1],a)||Ct(e,t,f,f,s-u+l,o-v+l,a)||Ct(e,t,f,f,s+u-l,o-v+l,a)||Ct(e,t,f,f,s+u-l,o+v-l,a)||Ct(e,t,f,f,s-u+l,o+v-l,a))}}};Zr.generateCutRectangle=function(){return this.nodeShapes["cut-rectangle"]=this.nodeShapes.cutrectangle={renderer:this,name:"cut-rectangle",cornerLength:ro(),points:yr(4,0),draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i,null,s)},generateCutTrianglePts:function(e,t,a,n,i){var s=i==="auto"?this.cornerLength:i,o=t/2,l=e/2,u=a-l,v=a+l,f=n-o,c=n+o;return{topLeft:[u,f+s,u+s,f,u+s,f+s],topRight:[v-s,f,v,f+s,v-s,f+s],bottomRight:[v,c-s,v-s,c,v-s,c-s],bottomLeft:[u+s,c,u,c-s,u+s,c-s]}},intersectLine:function(e,t,a,n,i,s,o,l){var u=this.generateCutTrianglePts(a+2*o,n+2*o,e,t,l),v=[].concat.apply([],[u.topLeft.splice(0,4),u.topRight.splice(0,4),u.bottomRight.splice(0,4),u.bottomLeft.splice(0,4)]);return Ta(i,s,v,e,t)},checkPoint:function(e,t,a,n,i,s,o,l){var u=l==="auto"?this.cornerLength:l;if(Xr(e,t,this.points,s,o,n,i-2*u,[0,-1],a)||Xr(e,t,this.points,s,o,n-2*u,i,[0,-1],a))return!0;var v=this.generateCutTrianglePts(n,i,s,o);return Cr(e,t,v.topLeft)||Cr(e,t,v.topRight)||Cr(e,t,v.bottomRight)||Cr(e,t,v.bottomLeft)}}};Zr.generateBarrel=function(){return this.nodeShapes.barrel={renderer:this,name:"barrel",points:yr(4,0),draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i)},intersectLine:function(e,t,a,n,i,s,o,l){var u=.15,v=.5,f=.85,c=this.generateBarrelBezierPts(a+2*o,n+2*o,e,t),h=function(g){var p=Wt({x:g[0],y:g[1]},{x:g[2],y:g[3]},{x:g[4],y:g[5]},u),m=Wt({x:g[0],y:g[1]},{x:g[2],y:g[3]},{x:g[4],y:g[5]},v),b=Wt({x:g[0],y:g[1]},{x:g[2],y:g[3]},{x:g[4],y:g[5]},f);return[g[0],g[1],p.x,p.y,m.x,m.y,b.x,b.y,g[4],g[5]]},d=[].concat(h(c.topLeft),h(c.topRight),h(c.bottomRight),h(c.bottomLeft));return Ta(i,s,d,e,t)},generateBarrelBezierPts:function(e,t,a,n){var i=t/2,s=e/2,o=a-s,l=a+s,u=n-i,v=n+i,f=ks(e,t),c=f.heightOffset,h=f.widthOffset,d=f.ctrlPtOffsetPct*e,y={topLeft:[o,u+c,o+d,u,o+h,u],topRight:[l-h,u,l-d,u,l,u+c],bottomRight:[l,v-c,l-d,v,l-h,v],bottomLeft:[o+h,v,o+d,v,o,v-c]};return y.topLeft.isTop=!0,y.topRight.isTop=!0,y.bottomLeft.isBottom=!0,y.bottomRight.isBottom=!0,y},checkPoint:function(e,t,a,n,i,s,o,l){var u=ks(n,i),v=u.heightOffset,f=u.widthOffset;if(Xr(e,t,this.points,s,o,n,i-2*v,[0,-1],a)||Xr(e,t,this.points,s,o,n-2*f,i,[0,-1],a))return!0;for(var c=this.generateBarrelBezierPts(n,i,s,o),h=function(k,S,P){var D=P[4],A=P[2],B=P[0],R=P[5],M=P[1],I=Math.min(D,B),L=Math.max(D,B),O=Math.min(R,M),V=Math.max(R,M);if(I<=k&&k<=L&&O<=S&&S<=V){var G=Bd(D,A,B),N=xd(G[0],G[1],G[2],k),F=N.filter(function(K){return 0<=K&&K<=1});if(F.length>0)return F[0]}return null},d=Object.keys(c),y=0;y<d.length;y++){var g=d[y],p=c[g],m=h(e,t,p);if(m!=null){var b=p[5],w=p[3],E=p[1],C=nr(b,w,E,m);if(p.isTop&&C<=t||p.isBottom&&t<=C)return!0}}return!1}}};Zr.generateBottomRoundrectangle=function(){return this.nodeShapes["bottom-round-rectangle"]=this.nodeShapes.bottomroundrectangle={renderer:this,name:"bottom-round-rectangle",points:yr(4,0),draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i,this.points,s)},intersectLine:function(e,t,a,n,i,s,o,l){var u=e-(a/2+o),v=t-(n/2+o),f=v,c=e+(a/2+o),h=rt(i,s,e,t,u,v,c,f,!1);return h.length>0?h:dv(i,s,e,t,a,n,o,l)},checkPoint:function(e,t,a,n,i,s,o,l){l=l==="auto"?st(n,i):l;var u=2*l;if(Xr(e,t,this.points,s,o,n,i-u,[0,-1],a)||Xr(e,t,this.points,s,o,n-u,i,[0,-1],a))return!0;var v=n/2+2*a,f=i/2+2*a,c=[s-v,o-f,s-v,o,s+v,o,s+v,o-f];return!!(Cr(e,t,c)||Ct(e,t,u,u,s+n/2-l,o+i/2-l,a)||Ct(e,t,u,u,s-n/2+l,o+i/2-l,a))}}};Zr.registerNodeShapes=function(){var r=this.nodeShapes={},e=this;this.generateEllipse(),this.generatePolygon("triangle",yr(3,0)),this.generateRoundPolygon("round-triangle",yr(3,0)),this.generatePolygon("rectangle",yr(4,0)),r.square=r.rectangle,this.generateRoundRectangle(),this.generateCutRectangle(),this.generateBarrel(),this.generateBottomRoundrectangle();{var t=[0,1,1,0,0,-1,-1,0];this.generatePolygon("diamond",t),this.generateRoundPolygon("round-diamond",t)}this.generatePolygon("pentagon",yr(5,0)),this.generateRoundPolygon("round-pentagon",yr(5,0)),this.generatePolygon("hexagon",yr(6,0)),this.generateRoundPolygon("round-hexagon",yr(6,0)),this.generatePolygon("heptagon",yr(7,0)),this.generateRoundPolygon("round-heptagon",yr(7,0)),this.generatePolygon("octagon",yr(8,0)),this.generateRoundPolygon("round-octagon",yr(8,0));var a=new Array(20);{var n=Ds(5,0),i=Ds(5,Math.PI/5),s=.5*(3-Math.sqrt(5));s*=1.57;for(var o=0;o<i.length/2;o++)i[o*2]*=s,i[o*2+1]*=s;for(var o=0;o<20/4;o++)a[o*4]=n[o*2],a[o*4+1]=n[o*2+1],a[o*4+2]=i[o*2],a[o*4+3]=i[o*2+1]}a=hv(a),this.generatePolygon("star",a),this.generatePolygon("vee",[-1,-1,0,-.333,1,-1,0,1]),this.generatePolygon("rhomboid",[-1,-1,.333,-1,1,1,-.333,1]),this.generatePolygon("right-rhomboid",[-.333,-1,1,-1,.333,1,-1,1]),this.nodeShapes.concavehexagon=this.generatePolygon("concave-hexagon",[-1,-.95,-.75,0,-1,.95,1,.95,.75,0,1,-.95]);{var l=[-1,-1,.25,-1,1,0,.25,1,-1,1];this.generatePolygon("tag",l),this.generateRoundPolygon("round-tag",l)}r.makePolygon=function(u){var v=u.join("$"),f="polygon-"+v,c;return(c=this[f])?c:e.generatePolygon(f,u)}};var qa={};qa.timeToRender=function(){return this.redrawTotalTime/this.redrawCount};qa.redraw=function(r){r=r||vv();var e=this;e.averageRedrawTime===void 0&&(e.averageRedrawTime=0),e.lastRedrawTime===void 0&&(e.lastRedrawTime=0),e.lastDrawTime===void 0&&(e.lastDrawTime=0),e.requestedFrame=!0,e.renderOptions=r};qa.beforeRender=function(r,e){if(!this.destroyed){e==null&&He("Priority is not optional for beforeRender");var t=this.beforeRenderCallbacks;t.push({fn:r,priority:e}),t.sort(function(a,n){return n.priority-a.priority})}};var Al=function(e,t,a){for(var n=e.beforeRenderCallbacks,i=0;i<n.length;i++)n[i].fn(t,a)};qa.startRenderLoop=function(){var r=this,e=r.cy;if(!r.renderLoopStarted){r.renderLoopStarted=!0;var t=function(n){if(!r.destroyed){if(!e.batching())if(r.requestedFrame&&!r.skipFrame){Al(r,!0,n);var i=Yr();r.render(r.renderOptions);var s=r.lastDrawTime=Yr();r.averageRedrawTime===void 0&&(r.averageRedrawTime=s-i),r.redrawCount===void 0&&(r.redrawCount=0),r.redrawCount++,r.redrawTotalTime===void 0&&(r.redrawTotalTime=0);var o=s-i;r.redrawTotalTime+=o,r.lastRedrawTime=o,r.averageRedrawTime=r.averageRedrawTime/2+o/2,r.requestedFrame=!1}else Al(r,!1,n);r.skipFrame=!1,mn(t)}};mn(t)}};var Up=function(e){this.init(e)},gf=Up,na=gf.prototype;na.clientFunctions=["redrawHint","render","renderTo","matchCanvasSize","nodeShapeImpl","arrowShapeImpl"];na.init=function(r){var e=this;e.options=r,e.cy=r.cy;var t=e.container=r.cy.container(),a=e.cy.window();if(a){var n=a.document,i=n.head,s="__________cytoscape_stylesheet",o="__________cytoscape_container",l=n.getElementById(s)!=null;if(t.className.indexOf(o)<0&&(t.className=(t.className||"")+" "+o),!l){var u=n.createElement("style");u.id=s,u.textContent="."+o+" { position: relative; }",i.insertBefore(u,i.children[0])}var v=a.getComputedStyle(t),f=v.getPropertyValue("position");f==="static"&&Le("A Cytoscape container has style position:static and so can not use UI extensions properly")}e.selection=[void 0,void 0,void 0,void 0,0],e.bezierProjPcts=[.05,.225,.4,.5,.6,.775,.95],e.hoverData={down:null,last:null,downTime:null,triggerMode:null,dragging:!1,initialPan:[null,null],capture:!1},e.dragData={possibleDragElements:[]},e.touchData={start:null,capture:!1,startPosition:[null,null,null,null,null,null],singleTouchStartTime:null,singleTouchMoved:!0,now:[null,null,null,null,null,null],earlier:[null,null,null,null,null,null]},e.redraws=0,e.showFps=r.showFps,e.debug=r.debug,e.webgl=r.webgl,e.hideEdgesOnViewport=r.hideEdgesOnViewport,e.textureOnViewport=r.textureOnViewport,e.wheelSensitivity=r.wheelSensitivity,e.motionBlurEnabled=r.motionBlur,e.forcedPixelRatio=te(r.pixelRatio)?r.pixelRatio:null,e.motionBlur=r.motionBlur,e.motionBlurOpacity=r.motionBlurOpacity,e.motionBlurTransparency=1-e.motionBlurOpacity,e.motionBlurPxRatio=1,e.mbPxRBlurry=1,e.minMbLowQualFrames=4,e.fullQualityMb=!1,e.clearedForMotionBlur=[],e.desktopTapThreshold=r.desktopTapThreshold,e.desktopTapThreshold2=r.desktopTapThreshold*r.desktopTapThreshold,e.touchTapThreshold=r.touchTapThreshold,e.touchTapThreshold2=r.touchTapThreshold*r.touchTapThreshold,e.tapholdDuration=500,e.bindings=[],e.beforeRenderCallbacks=[],e.beforeRenderPriorities={animations:400,eleCalcs:300,eleTxrDeq:200,lyrTxrDeq:150,lyrTxrSkip:100},e.registerNodeShapes(),e.registerArrowShapes(),e.registerCalculationListeners()};na.notify=function(r,e){var t=this,a=t.cy;if(!this.destroyed){if(r==="init"){t.load();return}if(r==="destroy"){t.destroy();return}(r==="add"||r==="remove"||r==="move"&&a.hasCompoundNodes()||r==="load"||r==="zorder"||r==="mount")&&t.invalidateCachedZSortedEles(),r==="viewport"&&t.redrawHint("select",!0),r==="gc"&&t.redrawHint("gc",!0),(r==="load"||r==="resize"||r==="mount")&&(t.invalidateContainerClientCoordsCache(),t.matchCanvasSize(t.container)),t.redrawHint("eles",!0),t.redrawHint("drag",!0),this.startRenderLoop(),this.redraw()}};na.destroy=function(){var r=this;r.destroyed=!0,r.cy.stopAnimationLoop();for(var e=0;e<r.bindings.length;e++){var t=r.bindings[e],a=t,n=a.target;(n.off||n.removeEventListener).apply(n,a.args)}if(r.bindings=[],r.beforeRenderCallbacks=[],r.onUpdateEleCalcsFns=[],r.removeObserver&&r.removeObserver.disconnect(),r.styleObserver&&r.styleObserver.disconnect(),r.resizeObserver&&r.resizeObserver.disconnect(),r.labelCalcDiv)try{document.body.removeChild(r.labelCalcDiv)}catch{}};na.isHeadless=function(){return!1};[fo,df,hf,aa,Zr,qa].forEach(function(r){he(na,r)});var ps=1e3/60,pf={setupDequeueing:function(e){return function(){var a=this,n=this.renderer;if(!a.dequeueingSetup){a.dequeueingSetup=!0;var i=Oa(function(){n.redrawHint("eles",!0),n.redrawHint("drag",!0),n.redraw()},e.deqRedrawThreshold),s=function(u,v){var f=Yr(),c=n.averageRedrawTime,h=n.lastRedrawTime,d=[],y=n.cy.extent(),g=n.getPixelRatio();for(u||n.flushRenderedStyleQueue();;){var p=Yr(),m=p-f,b=p-v;if(h<ps){var w=ps-(u?c:0);if(b>=e.deqFastCost*w)break}else if(u){if(m>=e.deqCost*h||m>=e.deqAvgCost*c)break}else if(b>=e.deqNoDrawCost*ps)break;var E=e.deq(a,g,y);if(E.length>0)for(var C=0;C<E.length;C++)d.push(E[C]);else break}d.length>0&&(e.onDeqd(a,d),!u&&e.shouldRedraw(a,d,g,y)&&i())},o=e.priority||Zs;n.beforeRender(s,o(a))}}}},$p=function(){function r(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:bn;vt(this,r),this.idsByKey=new Kr,this.keyForId=new Kr,this.cachesByLvl=new Kr,this.lvls=[],this.getKey=e,this.doesEleInvalidateKey=t}return ft(r,[{key:"getIdsFor",value:function(t){t==null&&He("Can not get id list for null key");var a=this.idsByKey,n=this.idsByKey.get(t);return n||(n=new jt,a.set(t,n)),n}},{key:"addIdForKey",value:function(t,a){t!=null&&this.getIdsFor(t).add(a)}},{key:"deleteIdForKey",value:function(t,a){t!=null&&this.getIdsFor(t).delete(a)}},{key:"getNumberOfIdsForKey",value:function(t){return t==null?0:this.getIdsFor(t).size}},{key:"updateKeyMappingFor",value:function(t){var a=t.id(),n=this.keyForId.get(a),i=this.getKey(t);this.deleteIdForKey(n,a),this.addIdForKey(i,a),this.keyForId.set(a,i)}},{key:"deleteKeyMappingFor",value:function(t){var a=t.id(),n=this.keyForId.get(a);this.deleteIdForKey(n,a),this.keyForId.delete(a)}},{key:"keyHasChangedFor",value:function(t){var a=t.id(),n=this.keyForId.get(a),i=this.getKey(t);return n!==i}},{key:"isInvalid",value:function(t){return this.keyHasChangedFor(t)||this.doesEleInvalidateKey(t)}},{key:"getCachesAt",value:function(t){var a=this.cachesByLvl,n=this.lvls,i=a.get(t);return i||(i=new Kr,a.set(t,i),n.push(t)),i}},{key:"getCache",value:function(t,a){return this.getCachesAt(a).get(t)}},{key:"get",value:function(t,a){var n=this.getKey(t),i=this.getCache(n,a);return i!=null&&this.updateKeyMappingFor(t),i}},{key:"getForCachedKey",value:function(t,a){var n=this.keyForId.get(t.id()),i=this.getCache(n,a);return i}},{key:"hasCache",value:function(t,a){return this.getCachesAt(a).has(t)}},{key:"has",value:function(t,a){var n=this.getKey(t);return this.hasCache(n,a)}},{key:"setCache",value:function(t,a,n){n.key=t,this.getCachesAt(a).set(t,n)}},{key:"set",value:function(t,a,n){var i=this.getKey(t);this.setCache(i,a,n),this.updateKeyMappingFor(t)}},{key:"deleteCache",value:function(t,a){this.getCachesAt(a).delete(t)}},{key:"delete",value:function(t,a){var n=this.getKey(t);this.deleteCache(n,a)}},{key:"invalidateKey",value:function(t){var a=this;this.lvls.forEach(function(n){return a.deleteCache(t,n)})}},{key:"invalidate",value:function(t){var a=t.id(),n=this.keyForId.get(a);this.deleteKeyMappingFor(t);var i=this.doesEleInvalidateKey(t);return i&&this.invalidateKey(n),i||this.getNumberOfIdsForKey(n)===0}}])}(),Rl=25,tn=50,hn=-4,qs=3,yf=7.99,Kp=8,Yp=1024,Xp=1024,Zp=1024,Qp=.2,Jp=.8,jp=10,ey=.15,ry=.1,ty=.9,ay=.9,ny=100,iy=1,Ht={dequeue:"dequeue",downscale:"downscale",highQuality:"highQuality"},sy=fr({getKey:null,doesEleInvalidateKey:bn,drawElement:null,getBoundingBox:null,getRotationPoint:null,getRotationOffset:null,isVisible:ov,allowEdgeTxrCaching:!0,allowParentTxrCaching:!0}),pa=function(e,t){var a=this;a.renderer=e,a.onDequeues=[];var n=sy(t);he(a,n),a.lookup=new $p(n.getKey,n.doesEleInvalidateKey),a.setupDequeueing()},tr=pa.prototype;tr.reasons=Ht;tr.getTextureQueue=function(r){var e=this;return e.eleImgCaches=e.eleImgCaches||{},e.eleImgCaches[r]=e.eleImgCaches[r]||[]};tr.getRetiredTextureQueue=function(r){var e=this,t=e.eleImgCaches.retired=e.eleImgCaches.retired||{},a=t[r]=t[r]||[];return a};tr.getElementQueue=function(){var r=this,e=r.eleCacheQueue=r.eleCacheQueue||new Na(function(t,a){return a.reqs-t.reqs});return e};tr.getElementKeyToQueue=function(){var r=this,e=r.eleKeyToCacheQueue=r.eleKeyToCacheQueue||{};return e};tr.getElement=function(r,e,t,a,n){var i=this,s=this.renderer,o=s.cy.zoom(),l=this.lookup;if(!e||e.w===0||e.h===0||isNaN(e.w)||isNaN(e.h)||!r.visible()||r.removed()||!i.allowEdgeTxrCaching&&r.isEdge()||!i.allowParentTxrCaching&&r.isParent())return null;if(a==null&&(a=Math.ceil(Js(o*t))),a<hn)a=hn;else if(o>=yf||a>qs)return null;var u=Math.pow(2,a),v=e.h*u,f=e.w*u,c=s.eleTextBiggerThanMin(r,u);if(!this.isVisible(r,c))return null;var h=l.get(r,a);if(h&&h.invalidated&&(h.invalidated=!1,h.texture.invalidatedWidth-=h.width),h)return h;var d;if(v<=Rl?d=Rl:v<=tn?d=tn:d=Math.ceil(v/tn)*tn,v>Zp||f>Xp)return null;var y=i.getTextureQueue(d),g=y[y.length-2],p=function(){return i.recycleTexture(d,f)||i.addTexture(d,f)};g||(g=y[y.length-1]),g||(g=p()),g.width-g.usedWidth<f&&(g=p());for(var m=function(I){return I&&I.scaledLabelShown===c},b=n&&n===Ht.dequeue,w=n&&n===Ht.highQuality,E=n&&n===Ht.downscale,C,x=a+1;x<=qs;x++){var k=l.get(r,x);if(k){C=k;break}}var S=C&&C.level===a+1?C:null,P=function(){g.context.drawImage(S.texture.canvas,S.x,0,S.width,S.height,g.usedWidth,0,f,v)};if(g.context.setTransform(1,0,0,1,0,0),g.context.clearRect(g.usedWidth,0,f,d),m(S))P();else if(m(C))if(w){for(var D=C.level;D>a;D--)S=i.getElement(r,e,t,D,Ht.downscale);P()}else return i.queueElement(r,C.level-1),C;else{var A;if(!b&&!w&&!E)for(var B=a-1;B>=hn;B--){var R=l.get(r,B);if(R){A=R;break}}if(m(A))return i.queueElement(r,a),A;g.context.translate(g.usedWidth,0),g.context.scale(u,u),this.drawElement(g.context,r,e,c,!1),g.context.scale(1/u,1/u),g.context.translate(-g.usedWidth,0)}return h={x:g.usedWidth,texture:g,level:a,scale:u,width:f,height:v,scaledLabelShown:c},g.usedWidth+=Math.ceil(f+Kp),g.eleCaches.push(h),l.set(r,a,h),i.checkTextureFullness(g),h};tr.invalidateElements=function(r){for(var e=0;e<r.length;e++)this.invalidateElement(r[e])};tr.invalidateElement=function(r){var e=this,t=e.lookup,a=[],n=t.isInvalid(r);if(n){for(var i=hn;i<=qs;i++){var s=t.getForCachedKey(r,i);s&&a.push(s)}var o=t.invalidate(r);if(o)for(var l=0;l<a.length;l++){var u=a[l],v=u.texture;v.invalidatedWidth+=u.width,u.invalidated=!0,e.checkTextureUtility(v)}e.removeFromQueue(r)}};tr.checkTextureUtility=function(r){r.invalidatedWidth>=Qp*r.width&&this.retireTexture(r)};tr.checkTextureFullness=function(r){var e=this,t=e.getTextureQueue(r.height);r.usedWidth/r.width>Jp&&r.fullnessChecks>=jp?it(t,r):r.fullnessChecks++};tr.retireTexture=function(r){var e=this,t=r.height,a=e.getTextureQueue(t),n=this.lookup;it(a,r),r.retired=!0;for(var i=r.eleCaches,s=0;s<i.length;s++){var o=i[s];n.deleteCache(o.key,o.level)}Qs(i);var l=e.getRetiredTextureQueue(t);l.push(r)};tr.addTexture=function(r,e){var t=this,a=t.getTextureQueue(r),n={};return a.push(n),n.eleCaches=[],n.height=r,n.width=Math.max(Yp,e),n.usedWidth=0,n.invalidatedWidth=0,n.fullnessChecks=0,n.canvas=t.renderer.makeOffscreenCanvas(n.width,n.height),n.context=n.canvas.getContext("2d"),n};tr.recycleTexture=function(r,e){for(var t=this,a=t.getTextureQueue(r),n=t.getRetiredTextureQueue(r),i=0;i<n.length;i++){var s=n[i];if(s.width>=e)return s.retired=!1,s.usedWidth=0,s.invalidatedWidth=0,s.fullnessChecks=0,Qs(s.eleCaches),s.context.setTransform(1,0,0,1,0,0),s.context.clearRect(0,0,s.width,s.height),it(n,s),a.push(s),s}};tr.queueElement=function(r,e){var t=this,a=t.getElementQueue(),n=t.getElementKeyToQueue(),i=this.getKey(r),s=n[i];if(s)s.level=Math.max(s.level,e),s.eles.merge(r),s.reqs++,a.updateItem(s);else{var o={eles:r.spawn().merge(r),level:e,reqs:1,key:i};a.push(o),n[i]=o}};tr.dequeue=function(r){for(var e=this,t=e.getElementQueue(),a=e.getElementKeyToQueue(),n=[],i=e.lookup,s=0;s<iy&&t.size()>0;s++){var o=t.pop(),l=o.key,u=o.eles[0],v=i.hasCache(u,o.level);if(a[l]=null,v)continue;n.push(o);var f=e.getBoundingBox(u);e.getElement(u,f,r,o.level,Ht.dequeue)}return n};tr.removeFromQueue=function(r){var e=this,t=e.getElementQueue(),a=e.getElementKeyToQueue(),n=this.getKey(r),i=a[n];i!=null&&(i.eles.length===1?(i.reqs=Xs,t.updateItem(i),t.pop(),a[n]=null):i.eles.unmerge(r))};tr.onDequeue=function(r){this.onDequeues.push(r)};tr.offDequeue=function(r){it(this.onDequeues,r)};tr.setupDequeueing=pf.setupDequeueing({deqRedrawThreshold:ny,deqCost:ey,deqAvgCost:ry,deqNoDrawCost:ty,deqFastCost:ay,deq:function(e,t,a){return e.dequeue(t,a)},onDeqd:function(e,t){for(var a=0;a<e.onDequeues.length;a++){var n=e.onDequeues[a];n(t)}},shouldRedraw:function(e,t,a,n){for(var i=0;i<t.length;i++)for(var s=t[i].eles,o=0;o<s.length;o++){var l=s[o].boundingBox();if(eo(l,n))return!0}return!1},priority:function(e){return e.renderer.beforeRenderPriorities.eleTxrDeq}});var oy=1,ma=-4,kn=2,uy=3.99,ly=50,vy=50,fy=.15,cy=.1,dy=.9,hy=.9,gy=1,Ml=250,py=4e3*4e3,Ll=32767,yy=!0,mf=function(e){var t=this,a=t.renderer=e,n=a.cy;t.layersByLevel={},t.firstGet=!0,t.lastInvalidationTime=Yr()-2*Ml,t.skipping=!1,t.eleTxrDeqs=n.collection(),t.scheduleElementRefinement=Oa(function(){t.refineElementTextures(t.eleTxrDeqs),t.eleTxrDeqs.unmerge(t.eleTxrDeqs)},vy),a.beforeRender(function(s,o){o-t.lastInvalidationTime<=Ml?t.skipping=!0:t.skipping=!1},a.beforeRenderPriorities.lyrTxrSkip);var i=function(o,l){return l.reqs-o.reqs};t.layersQueue=new Na(i),t.setupDequeueing()},cr=mf.prototype,Il=0,my=Math.pow(2,53)-1;cr.makeLayer=function(r,e){var t=Math.pow(2,e),a=Math.ceil(r.w*t),n=Math.ceil(r.h*t),i=this.renderer.makeOffscreenCanvas(a,n),s={id:Il=++Il%my,bb:r,level:e,width:a,height:n,canvas:i,context:i.getContext("2d"),eles:[],elesQueue:[],reqs:0},o=s.context,l=-s.bb.x1,u=-s.bb.y1;return o.scale(t,t),o.translate(l,u),s};cr.getLayers=function(r,e,t){var a=this,n=a.renderer,i=n.cy,s=i.zoom(),o=a.firstGet;if(a.firstGet=!1,t==null){if(t=Math.ceil(Js(s*e)),t<ma)t=ma;else if(s>=uy||t>kn)return null}a.validateLayersElesOrdering(t,r);var l=a.layersByLevel,u=Math.pow(2,t),v=l[t]=l[t]||[],f,c=a.levelIsComplete(t,r),h,d=function(){var P=function(M){if(a.validateLayersElesOrdering(M,r),a.levelIsComplete(M,r))return h=l[M],!0},D=function(M){if(!h)for(var I=t+M;ma<=I&&I<=kn&&!P(I);I+=M);};D(1),D(-1);for(var A=v.length-1;A>=0;A--){var B=v[A];B.invalid&&it(v,B)}};if(!c)d();else return v;var y=function(){if(!f){f=Sr();for(var P=0;P<r.length;P++)cv(f,r[P].boundingBox())}return f},g=function(P){P=P||{};var D=P.after;y();var A=Math.ceil(f.w*u),B=Math.ceil(f.h*u);if(A>Ll||B>Ll)return null;var R=A*B;if(R>py)return null;var M=a.makeLayer(f,t);if(D!=null){var I=v.indexOf(D)+1;v.splice(I,0,M)}else(P.insert===void 0||P.insert)&&v.unshift(M);return M};if(a.skipping&&!o)return null;for(var p=null,m=r.length/oy,b=!o,w=0;w<r.length;w++){var E=r[w],C=E._private.rscratch,x=C.imgLayerCaches=C.imgLayerCaches||{},k=x[t];if(k){p=k;continue}if((!p||p.eles.length>=m||!md(p.bb,E.boundingBox()))&&(p=g({insert:!0,after:p}),!p))return null;h||b?a.queueLayer(p,E):a.drawEleInLayer(p,E,t,e),p.eles.push(E),x[t]=p}return h||(b?null:v)};cr.getEleLevelForLayerLevel=function(r,e){return r};cr.drawEleInLayer=function(r,e,t,a){var n=this,i=this.renderer,s=r.context,o=e.boundingBox();o.w===0||o.h===0||!e.visible()||(t=n.getEleLevelForLayerLevel(t,a),i.setImgSmoothing(s,!1),i.drawCachedElement(s,e,null,null,t,yy),i.setImgSmoothing(s,!0))};cr.levelIsComplete=function(r,e){var t=this,a=t.layersByLevel[r];if(!a||a.length===0)return!1;for(var n=0,i=0;i<a.length;i++){var s=a[i];if(s.reqs>0||s.invalid)return!1;n+=s.eles.length}return n===e.length};cr.validateLayersElesOrdering=function(r,e){var t=this.layersByLevel[r];if(t)for(var a=0;a<t.length;a++){for(var n=t[a],i=-1,s=0;s<e.length;s++)if(n.eles[0]===e[s]){i=s;break}if(i<0){this.invalidateLayer(n);continue}for(var o=i,s=0;s<n.eles.length;s++)if(n.eles[s]!==e[o+s]){this.invalidateLayer(n);break}}};cr.updateElementsInLayers=function(r,e){for(var t=this,a=Ra(r[0]),n=0;n<r.length;n++)for(var i=a?null:r[n],s=a?r[n]:r[n].ele,o=s._private.rscratch,l=o.imgLayerCaches=o.imgLayerCaches||{},u=ma;u<=kn;u++){var v=l[u];v&&(i&&t.getEleLevelForLayerLevel(v.level)!==i.level||e(v,s,i))}};cr.haveLayers=function(){for(var r=this,e=!1,t=ma;t<=kn;t++){var a=r.layersByLevel[t];if(a&&a.length>0){e=!0;break}}return e};cr.invalidateElements=function(r){var e=this;r.length!==0&&(e.lastInvalidationTime=Yr(),!(r.length===0||!e.haveLayers())&&e.updateElementsInLayers(r,function(a,n,i){e.invalidateLayer(a)}))};cr.invalidateLayer=function(r){if(this.lastInvalidationTime=Yr(),!r.invalid){var e=r.level,t=r.eles,a=this.layersByLevel[e];it(a,r),r.elesQueue=[],r.invalid=!0,r.replacement&&(r.replacement.invalid=!0);for(var n=0;n<t.length;n++){var i=t[n]._private.rscratch.imgLayerCaches;i&&(i[e]=null)}}};cr.refineElementTextures=function(r){var e=this;e.updateElementsInLayers(r,function(a,n,i){var s=a.replacement;if(s||(s=a.replacement=e.makeLayer(a.bb,a.level),s.replaces=a,s.eles=a.eles),!s.reqs)for(var o=0;o<s.eles.length;o++)e.queueLayer(s,s.eles[o])})};cr.enqueueElementRefinement=function(r){this.eleTxrDeqs.merge(r),this.scheduleElementRefinement()};cr.queueLayer=function(r,e){var t=this,a=t.layersQueue,n=r.elesQueue,i=n.hasId=n.hasId||{};if(!r.replacement){if(e){if(i[e.id()])return;n.push(e),i[e.id()]=!0}r.reqs?(r.reqs++,a.updateItem(r)):(r.reqs=1,a.push(r))}};cr.dequeue=function(r){for(var e=this,t=e.layersQueue,a=[],n=0;n<gy&&t.size()!==0;){var i=t.peek();if(i.replacement){t.pop();continue}if(i.replaces&&i!==i.replaces.replacement){t.pop();continue}if(i.invalid){t.pop();continue}var s=i.elesQueue.shift();s&&(e.drawEleInLayer(i,s,i.level,r),n++),a.length===0&&a.push(!0),i.elesQueue.length===0&&(t.pop(),i.reqs=0,i.replaces&&e.applyLayerReplacement(i),e.requestRedraw())}return a};cr.applyLayerReplacement=function(r){var e=this,t=e.layersByLevel[r.level],a=r.replaces,n=t.indexOf(a);if(!(n<0||a.invalid)){t[n]=r;for(var i=0;i<r.eles.length;i++){var s=r.eles[i]._private,o=s.imgLayerCaches=s.imgLayerCaches||{};o&&(o[r.level]=r)}e.requestRedraw()}};cr.requestRedraw=Oa(function(){var r=this.renderer;r.redrawHint("eles",!0),r.redrawHint("drag",!0),r.redraw()},100);cr.setupDequeueing=pf.setupDequeueing({deqRedrawThreshold:ly,deqCost:fy,deqAvgCost:cy,deqNoDrawCost:dy,deqFastCost:hy,deq:function(e,t){return e.dequeue(t)},onDeqd:Zs,shouldRedraw:ov,priority:function(e){return e.renderer.beforeRenderPriorities.lyrTxrDeq}});var bf={},Ol;function by(r,e){for(var t=0;t<e.length;t++){var a=e[t];r.lineTo(a.x,a.y)}}function wy(r,e,t){for(var a,n=0;n<e.length;n++){var i=e[n];n===0&&(a=i),r.lineTo(i.x,i.y)}r.quadraticCurveTo(t.x,t.y,a.x,a.y)}function Nl(r,e,t){r.beginPath&&r.beginPath();for(var a=e,n=0;n<a.length;n++){var i=a[n];r.lineTo(i.x,i.y)}var s=t,o=t[0];r.moveTo(o.x,o.y);for(var n=1;n<s.length;n++){var i=s[n];r.lineTo(i.x,i.y)}r.closePath&&r.closePath()}function xy(r,e,t,a,n){r.beginPath&&r.beginPath(),r.arc(t,a,n,0,Math.PI*2,!1);var i=e,s=i[0];r.moveTo(s.x,s.y);for(var o=0;o<i.length;o++){var l=i[o];r.lineTo(l.x,l.y)}r.closePath&&r.closePath()}function Ey(r,e,t,a){r.arc(e,t,a,0,Math.PI*2,!1)}bf.arrowShapeImpl=function(r){return(Ol||(Ol={polygon:by,"triangle-backcurve":wy,"triangle-tee":Nl,"circle-triangle":xy,"triangle-cross":Nl,circle:Ey}))[r]};var Hr={};Hr.drawElement=function(r,e,t,a,n,i){var s=this;e.isNode()?s.drawNode(r,e,t,a,n,i):s.drawEdge(r,e,t,a,n,i)};Hr.drawElementOverlay=function(r,e){var t=this;e.isNode()?t.drawNodeOverlay(r,e):t.drawEdgeOverlay(r,e)};Hr.drawElementUnderlay=function(r,e){var t=this;e.isNode()?t.drawNodeUnderlay(r,e):t.drawEdgeUnderlay(r,e)};Hr.drawCachedElementPortion=function(r,e,t,a,n,i,s,o){var l=this,u=t.getBoundingBox(e);if(!(u.w===0||u.h===0)){var v=t.getElement(e,u,a,n,i);if(v!=null){var f=o(l,e);if(f===0)return;var c=s(l,e),h=u.x1,d=u.y1,y=u.w,g=u.h,p,m,b,w,E;if(c!==0){var C=t.getRotationPoint(e);b=C.x,w=C.y,r.translate(b,w),r.rotate(c),E=l.getImgSmoothing(r),E||l.setImgSmoothing(r,!0);var x=t.getRotationOffset(e);p=x.x,m=x.y}else p=h,m=d;var k;f!==1&&(k=r.globalAlpha,r.globalAlpha=k*f),r.drawImage(v.texture.canvas,v.x,0,v.width,v.height,p,m,y,g),f!==1&&(r.globalAlpha=k),c!==0&&(r.rotate(-c),r.translate(-b,-w),E||l.setImgSmoothing(r,!1))}else t.drawElement(r,e)}};var Cy=function(){return 0},Ty=function(e,t){return e.getTextAngle(t,null)},Sy=function(e,t){return e.getTextAngle(t,"source")},Dy=function(e,t){return e.getTextAngle(t,"target")},ky=function(e,t){return t.effectiveOpacity()},ys=function(e,t){return t.pstyle("text-opacity").pfValue*t.effectiveOpacity()};Hr.drawCachedElement=function(r,e,t,a,n,i){var s=this,o=s.data,l=o.eleTxrCache,u=o.lblTxrCache,v=o.slbTxrCache,f=o.tlbTxrCache,c=e.boundingBox(),h=i===!0?l.reasons.highQuality:null;if(!(c.w===0||c.h===0||!e.visible())&&(!a||eo(c,a))){var d=e.isEdge(),y=e.element()._private.rscratch.badLine;s.drawElementUnderlay(r,e),s.drawCachedElementPortion(r,e,l,t,n,h,Cy,ky),(!d||!y)&&s.drawCachedElementPortion(r,e,u,t,n,h,Ty,ys),d&&!y&&(s.drawCachedElementPortion(r,e,v,t,n,h,Sy,ys),s.drawCachedElementPortion(r,e,f,t,n,h,Dy,ys)),s.drawElementOverlay(r,e)}};Hr.drawElements=function(r,e){for(var t=this,a=0;a<e.length;a++){var n=e[a];t.drawElement(r,n)}};Hr.drawCachedElements=function(r,e,t,a){for(var n=this,i=0;i<e.length;i++){var s=e[i];n.drawCachedElement(r,s,t,a)}};Hr.drawCachedNodes=function(r,e,t,a){for(var n=this,i=0;i<e.length;i++){var s=e[i];s.isNode()&&n.drawCachedElement(r,s,t,a)}};Hr.drawLayeredElements=function(r,e,t,a){var n=this,i=n.data.lyrTxrCache.getLayers(e,t);if(i)for(var s=0;s<i.length;s++){var o=i[s],l=o.bb;l.w===0||l.h===0||r.drawImage(o.canvas,l.x1,l.y1,l.w,l.h)}else n.drawCachedElements(r,e,t,a)};var Qr={};Qr.drawEdge=function(r,e,t){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this,o=e._private.rscratch;if(!(i&&!e.visible())&&!(o.badLine||o.allpts==null||isNaN(o.allpts[0]))){var l;t&&(l=t,r.translate(-l.x1,-l.y1));var u=i?e.pstyle("opacity").value:1,v=i?e.pstyle("line-opacity").value:1,f=e.pstyle("curve-style").value,c=e.pstyle("line-style").value,h=e.pstyle("width").pfValue,d=e.pstyle("line-cap").value,y=e.pstyle("line-outline-width").value,g=e.pstyle("line-outline-color").value,p=u*v,m=u*v,b=function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:p;f==="straight-triangle"?(s.eleStrokeStyle(r,e,M),s.drawEdgeTrianglePath(e,r,o.allpts)):(r.lineWidth=h,r.lineCap=d,s.eleStrokeStyle(r,e,M),s.drawEdgePath(e,r,o.allpts,c),r.lineCap="butt")},w=function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:p;if(r.lineWidth=h+y,r.lineCap=d,y>0)s.colorStrokeStyle(r,g[0],g[1],g[2],M);else{r.lineCap="butt";return}f==="straight-triangle"?s.drawEdgeTrianglePath(e,r,o.allpts):(s.drawEdgePath(e,r,o.allpts,c),r.lineCap="butt")},E=function(){n&&s.drawEdgeOverlay(r,e)},C=function(){n&&s.drawEdgeUnderlay(r,e)},x=function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:m;s.drawArrowheads(r,e,M)},k=function(){s.drawElementText(r,e,null,a)};r.lineJoin="round";var S=e.pstyle("ghost").value==="yes";if(S){var P=e.pstyle("ghost-offset-x").pfValue,D=e.pstyle("ghost-offset-y").pfValue,A=e.pstyle("ghost-opacity").value,B=p*A;r.translate(P,D),b(B),x(B),r.translate(-P,-D)}else w();C(),b(),x(),E(),k(),t&&r.translate(l.x1,l.y1)}};var wf=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(t,a){if(a.visible()){var n=a.pstyle("".concat(e,"-opacity")).value;if(n!==0){var i=this,s=i.usePaths(),o=a._private.rscratch,l=a.pstyle("".concat(e,"-padding")).pfValue,u=2*l,v=a.pstyle("".concat(e,"-color")).value;t.lineWidth=u,o.edgeType==="self"&&!s?t.lineCap="butt":t.lineCap="round",i.colorStrokeStyle(t,v[0],v[1],v[2],n),i.drawEdgePath(a,t,o.allpts,"solid")}}}};Qr.drawEdgeOverlay=wf("overlay");Qr.drawEdgeUnderlay=wf("underlay");Qr.drawEdgePath=function(r,e,t,a){var n=r._private.rscratch,i=e,s,o=!1,l=this.usePaths(),u=r.pstyle("line-dash-pattern").pfValue,v=r.pstyle("line-dash-offset").pfValue;if(l){var f=t.join("$"),c=n.pathCacheKey&&n.pathCacheKey===f;c?(s=e=n.pathCache,o=!0):(s=e=new Path2D,n.pathCacheKey=f,n.pathCache=s)}if(i.setLineDash)switch(a){case"dotted":i.setLineDash([1,1]);break;case"dashed":i.setLineDash(u),i.lineDashOffset=v;break;case"solid":i.setLineDash([]);break}if(!o&&!n.badLine)switch(e.beginPath&&e.beginPath(),e.moveTo(t[0],t[1]),n.edgeType){case"bezier":case"self":case"compound":case"multibezier":for(var h=2;h+3<t.length;h+=4)e.quadraticCurveTo(t[h],t[h+1],t[h+2],t[h+3]);break;case"straight":case"haystack":for(var d=2;d+1<t.length;d+=2)e.lineTo(t[d],t[d+1]);break;case"segments":if(n.isRound){var y=Tr(n.roundCorners),g;try{for(y.s();!(g=y.n()).done;){var p=g.value;uf(e,p)}}catch(b){y.e(b)}finally{y.f()}e.lineTo(t[t.length-2],t[t.length-1])}else for(var m=2;m+1<t.length;m+=2)e.lineTo(t[m],t[m+1]);break}e=i,l?e.stroke(s):e.stroke(),e.setLineDash&&e.setLineDash([])};Qr.drawEdgeTrianglePath=function(r,e,t){e.fillStyle=e.strokeStyle;for(var a=r.pstyle("width").pfValue,n=0;n+1<t.length;n+=2){var i=[t[n+2]-t[n],t[n+3]-t[n+1]],s=Math.sqrt(i[0]*i[0]+i[1]*i[1]),o=[i[1]/s,-i[0]/s],l=[o[0]*a/2,o[1]*a/2];e.beginPath(),e.moveTo(t[n]-l[0],t[n+1]-l[1]),e.lineTo(t[n]+l[0],t[n+1]+l[1]),e.lineTo(t[n+2],t[n+3]),e.closePath(),e.fill()}};Qr.drawArrowheads=function(r,e,t){var a=e._private.rscratch,n=a.edgeType==="haystack";n||this.drawArrowhead(r,e,"source",a.arrowStartX,a.arrowStartY,a.srcArrowAngle,t),this.drawArrowhead(r,e,"mid-target",a.midX,a.midY,a.midtgtArrowAngle,t),this.drawArrowhead(r,e,"mid-source",a.midX,a.midY,a.midsrcArrowAngle,t),n||this.drawArrowhead(r,e,"target",a.arrowEndX,a.arrowEndY,a.tgtArrowAngle,t)};Qr.drawArrowhead=function(r,e,t,a,n,i,s){if(!(isNaN(a)||a==null||isNaN(n)||n==null||isNaN(i)||i==null)){var o=this,l=e.pstyle(t+"-arrow-shape").value;if(l!=="none"){var u=e.pstyle(t+"-arrow-fill").value==="hollow"?"both":"filled",v=e.pstyle(t+"-arrow-fill").value,f=e.pstyle("width").pfValue,c=e.pstyle(t+"-arrow-width"),h=c.value==="match-line"?f:c.pfValue;c.units==="%"&&(h*=f);var d=e.pstyle("opacity").value;s===void 0&&(s=d);var y=r.globalCompositeOperation;(s!==1||v==="hollow")&&(r.globalCompositeOperation="destination-out",o.colorFillStyle(r,255,255,255,1),o.colorStrokeStyle(r,255,255,255,1),o.drawArrowShape(e,r,u,f,l,h,a,n,i),r.globalCompositeOperation=y);var g=e.pstyle(t+"-arrow-color").value;o.colorFillStyle(r,g[0],g[1],g[2],s),o.colorStrokeStyle(r,g[0],g[1],g[2],s),o.drawArrowShape(e,r,v,f,l,h,a,n,i)}}};Qr.drawArrowShape=function(r,e,t,a,n,i,s,o,l){var u=this,v=this.usePaths()&&n!=="triangle-cross",f=!1,c,h=e,d={x:s,y:o},y=r.pstyle("arrow-scale").value,g=this.getArrowWidth(a,y),p=u.arrowShapes[n];if(v){var m=u.arrowPathCache=u.arrowPathCache||[],b=Tt(n),w=m[b];w!=null?(c=e=w,f=!0):(c=e=new Path2D,m[b]=c)}f||(e.beginPath&&e.beginPath(),v?p.draw(e,1,0,{x:0,y:0},1):p.draw(e,g,l,d,a),e.closePath&&e.closePath()),e=h,v&&(e.translate(s,o),e.rotate(l),e.scale(g,g)),(t==="filled"||t==="both")&&(v?e.fill(c):e.fill()),(t==="hollow"||t==="both")&&(e.lineWidth=i/(v?g:1),e.lineJoin="miter",v?e.stroke(c):e.stroke()),v&&(e.scale(1/g,1/g),e.rotate(-l),e.translate(-s,-o))};var go={};go.safeDrawImage=function(r,e,t,a,n,i,s,o,l,u){if(!(n<=0||i<=0||l<=0||u<=0))try{r.drawImage(e,t,a,n,i,s,o,l,u)}catch(v){Le(v)}};go.drawInscribedImage=function(r,e,t,a,n){var i=this,s=t.position(),o=s.x,l=s.y,u=t.cy().style(),v=u.getIndexedStyle.bind(u),f=v(t,"background-fit","value",a),c=v(t,"background-repeat","value",a),h=t.width(),d=t.height(),y=t.padding()*2,g=h+(v(t,"background-width-relative-to","value",a)==="inner"?0:y),p=d+(v(t,"background-height-relative-to","value",a)==="inner"?0:y),m=t._private.rscratch,b=v(t,"background-clip","value",a),w=b==="node",E=v(t,"background-image-opacity","value",a)*n,C=v(t,"background-image-smoothing","value",a),x=t.pstyle("corner-radius").value;x!=="auto"&&(x=t.pstyle("corner-radius").pfValue);var k=e.width||e.cachedW,S=e.height||e.cachedH;(k==null||S==null)&&(document.body.appendChild(e),k=e.cachedW=e.width||e.offsetWidth,S=e.cachedH=e.height||e.offsetHeight,document.body.removeChild(e));var P=k,D=S;if(v(t,"background-width","value",a)!=="auto"&&(v(t,"background-width","units",a)==="%"?P=v(t,"background-width","pfValue",a)*g:P=v(t,"background-width","pfValue",a)),v(t,"background-height","value",a)!=="auto"&&(v(t,"background-height","units",a)==="%"?D=v(t,"background-height","pfValue",a)*p:D=v(t,"background-height","pfValue",a)),!(P===0||D===0)){if(f==="contain"){var A=Math.min(g/P,p/D);P*=A,D*=A}else if(f==="cover"){var A=Math.max(g/P,p/D);P*=A,D*=A}var B=o-g/2,R=v(t,"background-position-x","units",a),M=v(t,"background-position-x","pfValue",a);R==="%"?B+=(g-P)*M:B+=M;var I=v(t,"background-offset-x","units",a),L=v(t,"background-offset-x","pfValue",a);I==="%"?B+=(g-P)*L:B+=L;var O=l-p/2,V=v(t,"background-position-y","units",a),G=v(t,"background-position-y","pfValue",a);V==="%"?O+=(p-D)*G:O+=G;var N=v(t,"background-offset-y","units",a),F=v(t,"background-offset-y","pfValue",a);N==="%"?O+=(p-D)*F:O+=F,m.pathCache&&(B-=o,O-=l,o=0,l=0);var K=r.globalAlpha;r.globalAlpha=E;var X=i.getImgSmoothing(r),Q=!1;if(C==="no"&&X?(i.setImgSmoothing(r,!1),Q=!0):C==="yes"&&!X&&(i.setImgSmoothing(r,!0),Q=!0),c==="no-repeat")w&&(r.save(),m.pathCache?r.clip(m.pathCache):(i.nodeShapes[i.getNodeShape(t)].draw(r,o,l,g,p,x,m),r.clip())),i.safeDrawImage(r,e,0,0,k,S,B,O,P,D),w&&r.restore();else{var Z=r.createPattern(e,c);r.fillStyle=Z,i.nodeShapes[i.getNodeShape(t)].draw(r,o,l,g,p,x,m),r.translate(B,O),r.fill(),r.translate(-B,-O)}r.globalAlpha=K,Q&&i.setImgSmoothing(r,X)}};var At={};At.eleTextBiggerThanMin=function(r,e){if(!e){var t=r.cy().zoom(),a=this.getPixelRatio(),n=Math.ceil(Js(t*a));e=Math.pow(2,n)}var i=r.pstyle("font-size").pfValue*e,s=r.pstyle("min-zoomed-font-size").pfValue;return!(i<s)};At.drawElementText=function(r,e,t,a,n){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this;if(a==null){if(i&&!s.eleTextBiggerThanMin(e))return}else if(a===!1)return;if(e.isNode()){var o=e.pstyle("label");if(!o||!o.value)return;var l=s.getLabelJustification(e);r.textAlign=l,r.textBaseline="bottom"}else{var u=e.element()._private.rscratch.badLine,v=e.pstyle("label"),f=e.pstyle("source-label"),c=e.pstyle("target-label");if(u||(!v||!v.value)&&(!f||!f.value)&&(!c||!c.value))return;r.textAlign="center",r.textBaseline="bottom"}var h=!t,d;t&&(d=t,r.translate(-d.x1,-d.y1)),n==null?(s.drawText(r,e,null,h,i),e.isEdge()&&(s.drawText(r,e,"source",h,i),s.drawText(r,e,"target",h,i))):s.drawText(r,e,n,h,i),t&&r.translate(d.x1,d.y1)};At.getFontCache=function(r){var e;this.fontCaches=this.fontCaches||[];for(var t=0;t<this.fontCaches.length;t++)if(e=this.fontCaches[t],e.context===r)return e;return e={context:r},this.fontCaches.push(e),e};At.setupTextStyle=function(r,e){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,a=e.pstyle("font-style").strValue,n=e.pstyle("font-size").pfValue+"px",i=e.pstyle("font-family").strValue,s=e.pstyle("font-weight").strValue,o=t?e.effectiveOpacity()*e.pstyle("text-opacity").value:1,l=e.pstyle("text-outline-opacity").value*o,u=e.pstyle("color").value,v=e.pstyle("text-outline-color").value;r.font=a+" "+s+" "+n+" "+i,r.lineJoin="round",this.colorFillStyle(r,u[0],u[1],u[2],o),this.colorStrokeStyle(r,v[0],v[1],v[2],l)};function ms(r,e,t,a,n){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:5,s=arguments.length>6?arguments[6]:void 0;r.beginPath(),r.moveTo(e+i,t),r.lineTo(e+a-i,t),r.quadraticCurveTo(e+a,t,e+a,t+i),r.lineTo(e+a,t+n-i),r.quadraticCurveTo(e+a,t+n,e+a-i,t+n),r.lineTo(e+i,t+n),r.quadraticCurveTo(e,t+n,e,t+n-i),r.lineTo(e,t+i),r.quadraticCurveTo(e,t,e+i,t),r.closePath(),s?r.stroke():r.fill()}At.getTextAngle=function(r,e){var t,a=r._private,n=a.rscratch,i=e?e+"-":"",s=r.pstyle(i+"text-rotation");if(s.strValue==="autorotate"){var o=Er(n,"labelAngle",e);t=r.isEdge()?o:0}else s.strValue==="none"?t=0:t=s.pfValue;return t};At.drawText=function(r,e,t){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=e._private,s=i.rscratch,o=n?e.effectiveOpacity():1;if(!(n&&(o===0||e.pstyle("text-opacity").value===0))){t==="main"&&(t=null);var l=Er(s,"labelX",t),u=Er(s,"labelY",t),v,f,c=this.getLabelText(e,t);if(c!=null&&c!==""&&!isNaN(l)&&!isNaN(u)){this.setupTextStyle(r,e,n);var h=t?t+"-":"",d=Er(s,"labelWidth",t),y=Er(s,"labelHeight",t),g=e.pstyle(h+"text-margin-x").pfValue,p=e.pstyle(h+"text-margin-y").pfValue,m=e.isEdge(),b=e.pstyle("text-halign").value,w=e.pstyle("text-valign").value;m&&(b="center",w="center"),l+=g,u+=p;var E;switch(a?E=this.getTextAngle(e,t):E=0,E!==0&&(v=l,f=u,r.translate(v,f),r.rotate(E),l=0,u=0),w){case"top":break;case"center":u+=y/2;break;case"bottom":u+=y;break}var C=e.pstyle("text-background-opacity").value,x=e.pstyle("text-border-opacity").value,k=e.pstyle("text-border-width").pfValue,S=e.pstyle("text-background-padding").pfValue,P=e.pstyle("text-background-shape").strValue,D=P.indexOf("round")===0,A=2;if(C>0||k>0&&x>0){var B=l-S;switch(b){case"left":B-=d;break;case"center":B-=d/2;break}var R=u-y-S,M=d+2*S,I=y+2*S;if(C>0){var L=r.fillStyle,O=e.pstyle("text-background-color").value;r.fillStyle="rgba("+O[0]+","+O[1]+","+O[2]+","+C*o+")",D?ms(r,B,R,M,I,A):r.fillRect(B,R,M,I),r.fillStyle=L}if(k>0&&x>0){var V=r.strokeStyle,G=r.lineWidth,N=e.pstyle("text-border-color").value,F=e.pstyle("text-border-style").value;if(r.strokeStyle="rgba("+N[0]+","+N[1]+","+N[2]+","+x*o+")",r.lineWidth=k,r.setLineDash)switch(F){case"dotted":r.setLineDash([1,1]);break;case"dashed":r.setLineDash([4,2]);break;case"double":r.lineWidth=k/4,r.setLineDash([]);break;case"solid":r.setLineDash([]);break}if(D?ms(r,B,R,M,I,A,"stroke"):r.strokeRect(B,R,M,I),F==="double"){var K=k/2;D?ms(r,B+K,R+K,M-K*2,I-K*2,A,"stroke"):r.strokeRect(B+K,R+K,M-K*2,I-K*2)}r.setLineDash&&r.setLineDash([]),r.lineWidth=G,r.strokeStyle=V}}var X=2*e.pstyle("text-outline-width").pfValue;if(X>0&&(r.lineWidth=X),e.pstyle("text-wrap").value==="wrap"){var Q=Er(s,"labelWrapCachedLines",t),Z=Er(s,"labelLineHeight",t),re=d/2,ae=this.getLabelJustification(e);switch(ae==="auto"||(b==="left"?ae==="left"?l+=-d:ae==="center"&&(l+=-re):b==="center"?ae==="left"?l+=-re:ae==="right"&&(l+=re):b==="right"&&(ae==="center"?l+=re:ae==="right"&&(l+=d))),w){case"top":u-=(Q.length-1)*Z;break;case"center":case"bottom":u-=(Q.length-1)*Z;break}for(var J=0;J<Q.length;J++)X>0&&r.strokeText(Q[J],l,u),r.fillText(Q[J],l,u),u+=Z}else X>0&&r.strokeText(c,l,u),r.fillText(c,l,u);E!==0&&(r.rotate(-E),r.translate(-v,-f))}}};var dt={};dt.drawNode=function(r,e,t){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this,o,l,u=e._private,v=u.rscratch,f=e.position();if(!(!te(f.x)||!te(f.y))&&!(i&&!e.visible())){var c=i?e.effectiveOpacity():1,h=s.usePaths(),d,y=!1,g=e.padding();o=e.width()+2*g,l=e.height()+2*g;var p;t&&(p=t,r.translate(-p.x1,-p.y1));for(var m=e.pstyle("background-image"),b=m.value,w=new Array(b.length),E=new Array(b.length),C=0,x=0;x<b.length;x++){var k=b[x],S=w[x]=k!=null&&k!=="none";if(S){var P=e.cy().style().getIndexedStyle(e,"background-image-crossorigin","value",x);C++,E[x]=s.getCachedImage(k,P,function(){u.backgroundTimestamp=Date.now(),e.emitAndNotify("background")})}}var D=e.pstyle("background-blacken").value,A=e.pstyle("border-width").pfValue,B=e.pstyle("background-opacity").value*c,R=e.pstyle("border-color").value,M=e.pstyle("border-style").value,I=e.pstyle("border-join").value,L=e.pstyle("border-cap").value,O=e.pstyle("border-position").value,V=e.pstyle("border-dash-pattern").pfValue,G=e.pstyle("border-dash-offset").pfValue,N=e.pstyle("border-opacity").value*c,F=e.pstyle("outline-width").pfValue,K=e.pstyle("outline-color").value,X=e.pstyle("outline-style").value,Q=e.pstyle("outline-opacity").value*c,Z=e.pstyle("outline-offset").value,re=e.pstyle("corner-radius").value;re!=="auto"&&(re=e.pstyle("corner-radius").pfValue);var ae=function(){var le=arguments.length>0&&arguments[0]!==void 0?arguments[0]:B;s.eleFillStyle(r,e,le)},J=function(){var le=arguments.length>0&&arguments[0]!==void 0?arguments[0]:N;s.colorStrokeStyle(r,R[0],R[1],R[2],le)},z=function(){var le=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Q;s.colorStrokeStyle(r,K[0],K[1],K[2],le)},q=function(le,Y,T,_){var W=s.nodePathCache=s.nodePathCache||[],U=sv(T==="polygon"?T+","+_.join(","):T,""+Y,""+le,""+re),$=W[U],ue,j=!1;return $!=null?(ue=$,j=!0,v.pathCache=ue):(ue=new Path2D,W[U]=v.pathCache=ue),{path:ue,cacheHit:j}},H=e.pstyle("shape").strValue,ee=e.pstyle("shape-polygon-points").pfValue;if(h){r.translate(f.x,f.y);var ne=q(o,l,H,ee);d=ne.path,y=ne.cacheHit}var be=function(){if(!y){var le=f;h&&(le={x:0,y:0}),s.nodeShapes[s.getNodeShape(e)].draw(d||r,le.x,le.y,o,l,re,v)}h?r.fill(d):r.fill()},_e=function(){for(var le=arguments.length>0&&arguments[0]!==void 0?arguments[0]:c,Y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,T=u.backgrounding,_=0,W=0;W<E.length;W++){var U=e.cy().style().getIndexedStyle(e,"background-image-containment","value",W);if(Y&&U==="over"||!Y&&U==="inside"){_++;continue}w[W]&&E[W].complete&&!E[W].error&&(_++,s.drawInscribedImage(r,E[W],e,W,le))}u.backgrounding=_!==C,T!==u.backgrounding&&e.updateStyle(!1)},Ie=function(){var le=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,Y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:c;s.hasPie(e)&&(s.drawPie(r,e,Y),le&&(h||s.nodeShapes[s.getNodeShape(e)].draw(r,f.x,f.y,o,l,re,v)))},se=function(){var le=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,Y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:c;s.hasStripe(e)&&(r.save(),h?r.clip(v.pathCache):(s.nodeShapes[s.getNodeShape(e)].draw(r,f.x,f.y,o,l,re,v),r.clip()),s.drawStripe(r,e,Y),r.restore(),le&&(h||s.nodeShapes[s.getNodeShape(e)].draw(r,f.x,f.y,o,l,re,v)))},oe=function(){var le=arguments.length>0&&arguments[0]!==void 0?arguments[0]:c,Y=(D>0?D:-D)*le,T=D>0?0:255;D!==0&&(s.colorFillStyle(r,T,T,T,Y),h?r.fill(d):r.fill())},ce=function(){if(A>0){if(r.lineWidth=A,r.lineCap=L,r.lineJoin=I,r.setLineDash)switch(M){case"dotted":r.setLineDash([1,1]);break;case"dashed":r.setLineDash(V),r.lineDashOffset=G;break;case"solid":case"double":r.setLineDash([]);break}if(O!=="center"){if(r.save(),r.lineWidth*=2,O==="inside")h?r.clip(d):r.clip();else{var le=new Path2D;le.rect(-o/2-A,-l/2-A,o+2*A,l+2*A),le.addPath(d),r.clip(le,"evenodd")}h?r.stroke(d):r.stroke(),r.restore()}else h?r.stroke(d):r.stroke();if(M==="double"){r.lineWidth=A/3;var Y=r.globalCompositeOperation;r.globalCompositeOperation="destination-out",h?r.stroke(d):r.stroke(),r.globalCompositeOperation=Y}r.setLineDash&&r.setLineDash([])}},ge=function(){if(F>0){if(r.lineWidth=F,r.lineCap="butt",r.setLineDash)switch(X){case"dotted":r.setLineDash([1,1]);break;case"dashed":r.setLineDash([4,2]);break;case"solid":case"double":r.setLineDash([]);break}var le=f;h&&(le={x:0,y:0});var Y=s.getNodeShape(e),T=A;O==="inside"&&(T=0),O==="outside"&&(T*=2);var _=(o+T+(F+Z))/o,W=(l+T+(F+Z))/l,U=o*_,$=l*W,ue=s.nodeShapes[Y].points,j;if(h){var ve=q(U,$,Y,ue);j=ve.path}if(Y==="ellipse")s.drawEllipsePath(j||r,le.x,le.y,U,$);else if(["round-diamond","round-heptagon","round-hexagon","round-octagon","round-pentagon","round-polygon","round-triangle","round-tag"].includes(Y)){var Ee=0,Se=0,pe=0;Y==="round-diamond"?Ee=(T+Z+F)*1.4:Y==="round-heptagon"?(Ee=(T+Z+F)*1.075,pe=-(T/2+Z+F)/35):Y==="round-hexagon"?Ee=(T+Z+F)*1.12:Y==="round-pentagon"?(Ee=(T+Z+F)*1.13,pe=-(T/2+Z+F)/15):Y==="round-tag"?(Ee=(T+Z+F)*1.12,Se=(T/2+F+Z)*.07):Y==="round-triangle"&&(Ee=(T+Z+F)*(Math.PI/2),pe=-(T+Z/2+F)/Math.PI),Ee!==0&&(_=(o+Ee)/o,U=o*_,["round-hexagon","round-tag"].includes(Y)||(W=(l+Ee)/l,$=l*W)),re=re==="auto"?gv(U,$):re;for(var Ce=U/2,me=$/2,xe=re+(T+F+Z)/2,Oe=new Array(ue.length/2),Xe=new Array(ue.length/2),or=0;or<ue.length/2;or++)Oe[or]={x:le.x+Se+Ce*ue[or*2],y:le.y+pe+me*ue[or*2+1]};var ar,Ke,ur,Qe,br=Oe.length;for(Ke=Oe[br-1],ar=0;ar<br;ar++)ur=Oe[ar%br],Qe=Oe[(ar+1)%br],Xe[ar]=co(Ke,ur,Qe,xe),Ke=ur,ur=Qe;s.drawRoundPolygonPath(j||r,le.x+Se,le.y+pe,o*_,l*W,ue,Xe)}else if(["roundrectangle","round-rectangle"].includes(Y))re=re==="auto"?st(U,$):re,s.drawRoundRectanglePath(j||r,le.x,le.y,U,$,re+(T+F+Z)/2);else if(["cutrectangle","cut-rectangle"].includes(Y))re=re==="auto"?ro():re,s.drawCutRectanglePath(j||r,le.x,le.y,U,$,null,re+(T+F+Z)/4);else if(["bottomroundrectangle","bottom-round-rectangle"].includes(Y))re=re==="auto"?st(U,$):re,s.drawBottomRoundRectanglePath(j||r,le.x,le.y,U,$,re+(T+F+Z)/2);else if(Y==="barrel")s.drawBarrelPath(j||r,le.x,le.y,U,$);else if(Y.startsWith("polygon")||["rhomboid","right-rhomboid","round-tag","tag","vee"].includes(Y)){var Or=(T+F+Z)/o;ue=wn(xn(ue,Or)),s.drawPolygonPath(j||r,le.x,le.y,o,l,ue)}else{var Jr=(T+F+Z)/o;ue=wn(xn(ue,-Jr)),s.drawPolygonPath(j||r,le.x,le.y,o,l,ue)}if(h?r.stroke(j):r.stroke(),X==="double"){r.lineWidth=T/3;var Wr=r.globalCompositeOperation;r.globalCompositeOperation="destination-out",h?r.stroke(j):r.stroke(),r.globalCompositeOperation=Wr}r.setLineDash&&r.setLineDash([])}},de=function(){n&&s.drawNodeOverlay(r,e,f,o,l)},ye=function(){n&&s.drawNodeUnderlay(r,e,f,o,l)},we=function(){s.drawElementText(r,e,null,a)},De=e.pstyle("ghost").value==="yes";if(De){var ze=e.pstyle("ghost-offset-x").pfValue,Ue=e.pstyle("ghost-offset-y").pfValue,Ae=e.pstyle("ghost-opacity").value,Ye=Ae*c;r.translate(ze,Ue),z(),ge(),ae(Ae*B),be(),_e(Ye,!0),J(Ae*N),ce(),Ie(D!==0||A!==0),se(D!==0||A!==0),_e(Ye,!1),oe(Ye),r.translate(-ze,-Ue)}h&&r.translate(-f.x,-f.y),ye(),h&&r.translate(f.x,f.y),z(),ge(),ae(),be(),_e(c,!0),J(),ce(),Ie(D!==0||A!==0),se(D!==0||A!==0),_e(c,!1),oe(),h&&r.translate(-f.x,-f.y),we(),de(),t&&r.translate(p.x1,p.y1)}};var xf=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(t,a,n,i,s){var o=this;if(a.visible()){var l=a.pstyle("".concat(e,"-padding")).pfValue,u=a.pstyle("".concat(e,"-opacity")).value,v=a.pstyle("".concat(e,"-color")).value,f=a.pstyle("".concat(e,"-shape")).value,c=a.pstyle("".concat(e,"-corner-radius")).value;if(u>0){if(n=n||a.position(),i==null||s==null){var h=a.padding();i=a.width()+2*h,s=a.height()+2*h}o.colorFillStyle(t,v[0],v[1],v[2],u),o.nodeShapes[f].draw(t,n.x,n.y,i+l*2,s+l*2,c),t.fill()}}}};dt.drawNodeOverlay=xf("overlay");dt.drawNodeUnderlay=xf("underlay");dt.hasPie=function(r){return r=r[0],r._private.hasPie};dt.hasStripe=function(r){return r=r[0],r._private.hasStripe};dt.drawPie=function(r,e,t,a){e=e[0],a=a||e.position();var n=e.cy().style(),i=e.pstyle("pie-size"),s=e.pstyle("pie-hole"),o=e.pstyle("pie-start-angle").pfValue,l=a.x,u=a.y,v=e.width(),f=e.height(),c=Math.min(v,f)/2,h,d=0,y=this.usePaths();if(y&&(l=0,u=0),i.units==="%"?c=c*i.pfValue:i.pfValue!==void 0&&(c=i.pfValue/2),s.units==="%"?h=c*s.pfValue:s.pfValue!==void 0&&(h=s.pfValue/2),!(h>=c))for(var g=1;g<=n.pieBackgroundN;g++){var p=e.pstyle("pie-"+g+"-background-size").value,m=e.pstyle("pie-"+g+"-background-color").value,b=e.pstyle("pie-"+g+"-background-opacity").value*t,w=p/100;w+d>1&&(w=1-d);var E=1.5*Math.PI+2*Math.PI*d;E+=o;var C=2*Math.PI*w,x=E+C;p===0||d>=1||d+w>1||(h===0?(r.beginPath(),r.moveTo(l,u),r.arc(l,u,c,E,x),r.closePath()):(r.beginPath(),r.arc(l,u,c,E,x),r.arc(l,u,h,x,E,!0),r.closePath()),this.colorFillStyle(r,m[0],m[1],m[2],b),r.fill(),d+=w)}};dt.drawStripe=function(r,e,t,a){e=e[0],a=a||e.position();var n=e.cy().style(),i=a.x,s=a.y,o=e.width(),l=e.height(),u=0,v=this.usePaths();r.save();var f=e.pstyle("stripe-direction").value,c=e.pstyle("stripe-size");switch(f){case"vertical":break;case"righward":r.rotate(-Math.PI/2);break}var h=o,d=l;c.units==="%"?(h=h*c.pfValue,d=d*c.pfValue):c.pfValue!==void 0&&(h=c.pfValue,d=c.pfValue),v&&(i=0,s=0),s-=h/2,i-=d/2;for(var y=1;y<=n.stripeBackgroundN;y++){var g=e.pstyle("stripe-"+y+"-background-size").value,p=e.pstyle("stripe-"+y+"-background-color").value,m=e.pstyle("stripe-"+y+"-background-opacity").value*t,b=g/100;b+u>1&&(b=1-u),!(g===0||u>=1||u+b>1)&&(r.beginPath(),r.rect(i,s+d*u,h,d*b),r.closePath(),this.colorFillStyle(r,p[0],p[1],p[2],m),r.fill(),u+=b)}r.restore()};var mr={},By=100;mr.getPixelRatio=function(){var r=this.data.contexts[0];if(this.forcedPixelRatio!=null)return this.forcedPixelRatio;var e=this.cy.window(),t=r.backingStorePixelRatio||r.webkitBackingStorePixelRatio||r.mozBackingStorePixelRatio||r.msBackingStorePixelRatio||r.oBackingStorePixelRatio||r.backingStorePixelRatio||1;return(e.devicePixelRatio||1)/t};mr.paintCache=function(r){for(var e=this.paintCaches=this.paintCaches||[],t=!0,a,n=0;n<e.length;n++)if(a=e[n],a.context===r){t=!1;break}return t&&(a={context:r},e.push(a)),a};mr.createGradientStyleFor=function(r,e,t,a,n){var i,s=this.usePaths(),o=t.pstyle(e+"-gradient-stop-colors").value,l=t.pstyle(e+"-gradient-stop-positions").pfValue;if(a==="radial-gradient")if(t.isEdge()){var u=t.sourceEndpoint(),v=t.targetEndpoint(),f=t.midpoint(),c=St(u,f),h=St(v,f);i=r.createRadialGradient(f.x,f.y,0,f.x,f.y,Math.max(c,h))}else{var d=s?{x:0,y:0}:t.position(),y=t.paddedWidth(),g=t.paddedHeight();i=r.createRadialGradient(d.x,d.y,0,d.x,d.y,Math.max(y,g))}else if(t.isEdge()){var p=t.sourceEndpoint(),m=t.targetEndpoint();i=r.createLinearGradient(p.x,p.y,m.x,m.y)}else{var b=s?{x:0,y:0}:t.position(),w=t.paddedWidth(),E=t.paddedHeight(),C=w/2,x=E/2,k=t.pstyle("background-gradient-direction").value;switch(k){case"to-bottom":i=r.createLinearGradient(b.x,b.y-x,b.x,b.y+x);break;case"to-top":i=r.createLinearGradient(b.x,b.y+x,b.x,b.y-x);break;case"to-left":i=r.createLinearGradient(b.x+C,b.y,b.x-C,b.y);break;case"to-right":i=r.createLinearGradient(b.x-C,b.y,b.x+C,b.y);break;case"to-bottom-right":case"to-right-bottom":i=r.createLinearGradient(b.x-C,b.y-x,b.x+C,b.y+x);break;case"to-top-right":case"to-right-top":i=r.createLinearGradient(b.x-C,b.y+x,b.x+C,b.y-x);break;case"to-bottom-left":case"to-left-bottom":i=r.createLinearGradient(b.x+C,b.y-x,b.x-C,b.y+x);break;case"to-top-left":case"to-left-top":i=r.createLinearGradient(b.x+C,b.y+x,b.x-C,b.y-x);break}}if(!i)return null;for(var S=l.length===o.length,P=o.length,D=0;D<P;D++)i.addColorStop(S?l[D]:D/(P-1),"rgba("+o[D][0]+","+o[D][1]+","+o[D][2]+","+n+")");return i};mr.gradientFillStyle=function(r,e,t,a){var n=this.createGradientStyleFor(r,"background",e,t,a);if(!n)return null;r.fillStyle=n};mr.colorFillStyle=function(r,e,t,a,n){r.fillStyle="rgba("+e+","+t+","+a+","+n+")"};mr.eleFillStyle=function(r,e,t){var a=e.pstyle("background-fill").value;if(a==="linear-gradient"||a==="radial-gradient")this.gradientFillStyle(r,e,a,t);else{var n=e.pstyle("background-color").value;this.colorFillStyle(r,n[0],n[1],n[2],t)}};mr.gradientStrokeStyle=function(r,e,t,a){var n=this.createGradientStyleFor(r,"line",e,t,a);if(!n)return null;r.strokeStyle=n};mr.colorStrokeStyle=function(r,e,t,a,n){r.strokeStyle="rgba("+e+","+t+","+a+","+n+")"};mr.eleStrokeStyle=function(r,e,t){var a=e.pstyle("line-fill").value;if(a==="linear-gradient"||a==="radial-gradient")this.gradientStrokeStyle(r,e,a,t);else{var n=e.pstyle("line-color").value;this.colorStrokeStyle(r,n[0],n[1],n[2],t)}};mr.matchCanvasSize=function(r){var e=this,t=e.data,a=e.findContainerClientCoords(),n=a[2],i=a[3],s=e.getPixelRatio(),o=e.motionBlurPxRatio;(r===e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_NODE]||r===e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_DRAG])&&(s=o);var l=n*s,u=i*s,v;if(!(l===e.canvasWidth&&u===e.canvasHeight)){e.fontCaches=null;var f=t.canvasContainer;f.style.width=n+"px",f.style.height=i+"px";for(var c=0;c<e.CANVAS_LAYERS;c++)v=t.canvases[c],v.width=l,v.height=u,v.style.width=n+"px",v.style.height=i+"px";for(var c=0;c<e.BUFFER_COUNT;c++)v=t.bufferCanvases[c],v.width=l,v.height=u,v.style.width=n+"px",v.style.height=i+"px";e.textureMult=1,s<=1&&(v=t.bufferCanvases[e.TEXTURE_BUFFER],e.textureMult=2,v.width=l*e.textureMult,v.height=u*e.textureMult),e.canvasWidth=l,e.canvasHeight=u,e.pixelRatio=s}};mr.renderTo=function(r,e,t,a){this.render({forcedContext:r,forcedZoom:e,forcedPan:t,drawAllLayers:!0,forcedPxRatio:a})};mr.clearCanvas=function(){var r=this,e=r.data;function t(a){a.clearRect(0,0,r.canvasWidth,r.canvasHeight)}t(e.contexts[r.NODE]),t(e.contexts[r.DRAG])};mr.render=function(r){var e=this;r=r||vv();var t=e.cy,a=r.forcedContext,n=r.drawAllLayers,i=r.drawOnlyNodeLayer,s=r.forcedZoom,o=r.forcedPan,l=r.forcedPxRatio===void 0?this.getPixelRatio():r.forcedPxRatio,u=e.data,v=u.canvasNeedsRedraw,f=e.textureOnViewport&&!a&&(e.pinching||e.hoverData.dragging||e.swipePanning||e.data.wheelZooming),c=r.motionBlur!==void 0?r.motionBlur:e.motionBlur,h=e.motionBlurPxRatio,d=t.hasCompoundNodes(),y=e.hoverData.draggingEles,g=!!(e.hoverData.selecting||e.touchData.selecting);c=c&&!a&&e.motionBlurEnabled&&!g;var p=c;a||(e.prevPxRatio!==l&&(e.invalidateContainerClientCoordsCache(),e.matchCanvasSize(e.container),e.redrawHint("eles",!0),e.redrawHint("drag",!0)),e.prevPxRatio=l),!a&&e.motionBlurTimeout&&clearTimeout(e.motionBlurTimeout),c&&(e.mbFrames==null&&(e.mbFrames=0),e.mbFrames++,e.mbFrames<3&&(p=!1),e.mbFrames>e.minMbLowQualFrames&&(e.motionBlurPxRatio=e.mbPxRBlurry)),e.clearingMotionBlur&&(e.motionBlurPxRatio=1),e.textureDrawLastFrame&&!f&&(v[e.NODE]=!0,v[e.SELECT_BOX]=!0);var m=t.style(),b=t.zoom(),w=s!==void 0?s:b,E=t.pan(),C={x:E.x,y:E.y},x={zoom:b,pan:{x:E.x,y:E.y}},k=e.prevViewport,S=k===void 0||x.zoom!==k.zoom||x.pan.x!==k.pan.x||x.pan.y!==k.pan.y;!S&&!(y&&!d)&&(e.motionBlurPxRatio=1),o&&(C=o),w*=l,C.x*=l,C.y*=l;var P=e.getCachedZSortedEles();function D(J,z,q,H,ee){var ne=J.globalCompositeOperation;J.globalCompositeOperation="destination-out",e.colorFillStyle(J,255,255,255,e.motionBlurTransparency),J.fillRect(z,q,H,ee),J.globalCompositeOperation=ne}function A(J,z){var q,H,ee,ne;!e.clearingMotionBlur&&(J===u.bufferContexts[e.MOTIONBLUR_BUFFER_NODE]||J===u.bufferContexts[e.MOTIONBLUR_BUFFER_DRAG])?(q={x:E.x*h,y:E.y*h},H=b*h,ee=e.canvasWidth*h,ne=e.canvasHeight*h):(q=C,H=w,ee=e.canvasWidth,ne=e.canvasHeight),J.setTransform(1,0,0,1,0,0),z==="motionBlur"?D(J,0,0,ee,ne):!a&&(z===void 0||z)&&J.clearRect(0,0,ee,ne),n||(J.translate(q.x,q.y),J.scale(H,H)),o&&J.translate(o.x,o.y),s&&J.scale(s,s)}if(f||(e.textureDrawLastFrame=!1),f){if(e.textureDrawLastFrame=!0,!e.textureCache){e.textureCache={},e.textureCache.bb=t.mutableElements().boundingBox(),e.textureCache.texture=e.data.bufferCanvases[e.TEXTURE_BUFFER];var B=e.data.bufferContexts[e.TEXTURE_BUFFER];B.setTransform(1,0,0,1,0,0),B.clearRect(0,0,e.canvasWidth*e.textureMult,e.canvasHeight*e.textureMult),e.render({forcedContext:B,drawOnlyNodeLayer:!0,forcedPxRatio:l*e.textureMult});var x=e.textureCache.viewport={zoom:t.zoom(),pan:t.pan(),width:e.canvasWidth,height:e.canvasHeight};x.mpan={x:(0-x.pan.x)/x.zoom,y:(0-x.pan.y)/x.zoom}}v[e.DRAG]=!1,v[e.NODE]=!1;var R=u.contexts[e.NODE],M=e.textureCache.texture,x=e.textureCache.viewport;R.setTransform(1,0,0,1,0,0),c?D(R,0,0,x.width,x.height):R.clearRect(0,0,x.width,x.height);var I=m.core("outside-texture-bg-color").value,L=m.core("outside-texture-bg-opacity").value;e.colorFillStyle(R,I[0],I[1],I[2],L),R.fillRect(0,0,x.width,x.height);var b=t.zoom();A(R,!1),R.clearRect(x.mpan.x,x.mpan.y,x.width/x.zoom/l,x.height/x.zoom/l),R.drawImage(M,x.mpan.x,x.mpan.y,x.width/x.zoom/l,x.height/x.zoom/l)}else e.textureOnViewport&&!a&&(e.textureCache=null);var O=t.extent(),V=e.pinching||e.hoverData.dragging||e.swipePanning||e.data.wheelZooming||e.hoverData.draggingEles||e.cy.animated(),G=e.hideEdgesOnViewport&&V,N=[];if(N[e.NODE]=!v[e.NODE]&&c&&!e.clearedForMotionBlur[e.NODE]||e.clearingMotionBlur,N[e.NODE]&&(e.clearedForMotionBlur[e.NODE]=!0),N[e.DRAG]=!v[e.DRAG]&&c&&!e.clearedForMotionBlur[e.DRAG]||e.clearingMotionBlur,N[e.DRAG]&&(e.clearedForMotionBlur[e.DRAG]=!0),v[e.NODE]||n||i||N[e.NODE]){var F=c&&!N[e.NODE]&&h!==1,R=a||(F?e.data.bufferContexts[e.MOTIONBLUR_BUFFER_NODE]:u.contexts[e.NODE]),K=c&&!F?"motionBlur":void 0;A(R,K),G?e.drawCachedNodes(R,P.nondrag,l,O):e.drawLayeredElements(R,P.nondrag,l,O),e.debug&&e.drawDebugPoints(R,P.nondrag),!n&&!c&&(v[e.NODE]=!1)}if(!i&&(v[e.DRAG]||n||N[e.DRAG])){var F=c&&!N[e.DRAG]&&h!==1,R=a||(F?e.data.bufferContexts[e.MOTIONBLUR_BUFFER_DRAG]:u.contexts[e.DRAG]);A(R,c&&!F?"motionBlur":void 0),G?e.drawCachedNodes(R,P.drag,l,O):e.drawCachedElements(R,P.drag,l,O),e.debug&&e.drawDebugPoints(R,P.drag),!n&&!c&&(v[e.DRAG]=!1)}if(this.drawSelectionRectangle(r,A),c&&h!==1){var X=u.contexts[e.NODE],Q=e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_NODE],Z=u.contexts[e.DRAG],re=e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_DRAG],ae=function(z,q,H){z.setTransform(1,0,0,1,0,0),H||!p?z.clearRect(0,0,e.canvasWidth,e.canvasHeight):D(z,0,0,e.canvasWidth,e.canvasHeight);var ee=h;z.drawImage(q,0,0,e.canvasWidth*ee,e.canvasHeight*ee,0,0,e.canvasWidth,e.canvasHeight)};(v[e.NODE]||N[e.NODE])&&(ae(X,Q,N[e.NODE]),v[e.NODE]=!1),(v[e.DRAG]||N[e.DRAG])&&(ae(Z,re,N[e.DRAG]),v[e.DRAG]=!1)}e.prevViewport=x,e.clearingMotionBlur&&(e.clearingMotionBlur=!1,e.motionBlurCleared=!0,e.motionBlur=!0),c&&(e.motionBlurTimeout=setTimeout(function(){e.motionBlurTimeout=null,e.clearedForMotionBlur[e.NODE]=!1,e.clearedForMotionBlur[e.DRAG]=!1,e.motionBlur=!1,e.clearingMotionBlur=!f,e.mbFrames=0,v[e.NODE]=!0,v[e.DRAG]=!0,e.redraw()},By)),a||t.emit("render")};var fa;mr.drawSelectionRectangle=function(r,e){var t=this,a=t.cy,n=t.data,i=a.style(),s=r.drawOnlyNodeLayer,o=r.drawAllLayers,l=n.canvasNeedsRedraw,u=r.forcedContext;if(t.showFps||!s&&l[t.SELECT_BOX]&&!o){var v=u||n.contexts[t.SELECT_BOX];if(e(v),t.selection[4]==1&&(t.hoverData.selecting||t.touchData.selecting)){var f=t.cy.zoom(),c=i.core("selection-box-border-width").value/f;v.lineWidth=c,v.fillStyle="rgba("+i.core("selection-box-color").value[0]+","+i.core("selection-box-color").value[1]+","+i.core("selection-box-color").value[2]+","+i.core("selection-box-opacity").value+")",v.fillRect(t.selection[0],t.selection[1],t.selection[2]-t.selection[0],t.selection[3]-t.selection[1]),c>0&&(v.strokeStyle="rgba("+i.core("selection-box-border-color").value[0]+","+i.core("selection-box-border-color").value[1]+","+i.core("selection-box-border-color").value[2]+","+i.core("selection-box-opacity").value+")",v.strokeRect(t.selection[0],t.selection[1],t.selection[2]-t.selection[0],t.selection[3]-t.selection[1]))}if(n.bgActivePosistion&&!t.hoverData.selecting){var f=t.cy.zoom(),h=n.bgActivePosistion;v.fillStyle="rgba("+i.core("active-bg-color").value[0]+","+i.core("active-bg-color").value[1]+","+i.core("active-bg-color").value[2]+","+i.core("active-bg-opacity").value+")",v.beginPath(),v.arc(h.x,h.y,i.core("active-bg-size").pfValue/f,0,2*Math.PI),v.fill()}var d=t.lastRedrawTime;if(t.showFps&&d){d=Math.round(d);var y=Math.round(1e3/d),g="1 frame = "+d+" ms = "+y+" fps";if(v.setTransform(1,0,0,1,0,0),v.fillStyle="rgba(255, 0, 0, 0.75)",v.strokeStyle="rgba(255, 0, 0, 0.75)",v.font="30px Arial",!fa){var p=v.measureText(g);fa=p.actualBoundingBoxAscent}v.fillText(g,0,fa);var m=60;v.strokeRect(0,fa+10,250,20),v.fillRect(0,fa+10,250*Math.min(y/m,1),20)}o||(l[t.SELECT_BOX]=!1)}};function zl(r,e,t){var a=r.createShader(e);if(r.shaderSource(a,t),r.compileShader(a),!r.getShaderParameter(a,r.COMPILE_STATUS))throw new Error(r.getShaderInfoLog(a));return a}function Py(r,e,t){var a=zl(r,r.VERTEX_SHADER,e),n=zl(r,r.FRAGMENT_SHADER,t),i=r.createProgram();if(r.attachShader(i,a),r.attachShader(i,n),r.linkProgram(i),!r.getProgramParameter(i,r.LINK_STATUS))throw new Error("Could not initialize shaders");return i}function Ay(r,e,t){t===void 0&&(t=e);var a=r.makeOffscreenCanvas(e,t),n=a.context=a.getContext("2d");return a.clear=function(){return n.clearRect(0,0,a.width,a.height)},a.clear(),a}function po(r){var e=r.pixelRatio,t=r.cy.zoom(),a=r.cy.pan();return{zoom:t*e,pan:{x:a.x*e,y:a.y*e}}}function Ry(r){var e=r.pixelRatio,t=r.cy.zoom();return t*e}function My(r,e,t,a,n){var i=a*t+e.x,s=n*t+e.y;return s=Math.round(r.canvasHeight-s),[i,s]}function Ly(r){return r.pstyle("background-fill").value!=="solid"||r.pstyle("background-image").strValue!=="none"?!1:r.pstyle("border-width").value===0||r.pstyle("border-opacity").value===0?!0:r.pstyle("border-style").value==="solid"}function Iy(r,e){if(r.length!==e.length)return!1;for(var t=0;t<r.length;t++)if(r[t]!==e[t])return!1;return!0}function yt(r,e,t){var a=r[0]/255,n=r[1]/255,i=r[2]/255,s=e,o=t||new Array(4);return o[0]=a*s,o[1]=n*s,o[2]=i*s,o[3]=s,o}function zt(r,e){var t=e||new Array(4);return t[0]=(r>>0&255)/255,t[1]=(r>>8&255)/255,t[2]=(r>>16&255)/255,t[3]=(r>>24&255)/255,t}function Oy(r){return r[0]+(r[1]<<8)+(r[2]<<16)+(r[3]<<24)}function Ny(r,e){var t=r.createTexture();return t.buffer=function(a){r.bindTexture(r.TEXTURE_2D,t),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_WRAP_S,r.CLAMP_TO_EDGE),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_WRAP_T,r.CLAMP_TO_EDGE),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_MAG_FILTER,r.LINEAR),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_MIN_FILTER,r.LINEAR_MIPMAP_NEAREST),r.pixelStorei(r.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0),r.texImage2D(r.TEXTURE_2D,0,r.RGBA,r.RGBA,r.UNSIGNED_BYTE,a),r.generateMipmap(r.TEXTURE_2D),r.bindTexture(r.TEXTURE_2D,null)},t.deleteTexture=function(){r.deleteTexture(t)},t}function Ef(r,e){switch(e){case"float":return[1,r.FLOAT,4];case"vec2":return[2,r.FLOAT,4];case"vec3":return[3,r.FLOAT,4];case"vec4":return[4,r.FLOAT,4];case"int":return[1,r.INT,4];case"ivec2":return[2,r.INT,4]}}function Cf(r,e,t){switch(e){case r.FLOAT:return new Float32Array(t);case r.INT:return new Int32Array(t)}}function zy(r,e,t,a,n,i){switch(e){case r.FLOAT:return new Float32Array(t.buffer,i*a,n);case r.INT:return new Int32Array(t.buffer,i*a,n)}}function Fy(r,e,t,a){var n=Ef(r,e),i=je(n,2),s=i[0],o=i[1],l=Cf(r,o,a),u=r.createBuffer();return r.bindBuffer(r.ARRAY_BUFFER,u),r.bufferData(r.ARRAY_BUFFER,l,r.STATIC_DRAW),o===r.FLOAT?r.vertexAttribPointer(t,s,o,!1,0,0):o===r.INT&&r.vertexAttribIPointer(t,s,o,0,0),r.enableVertexAttribArray(t),r.bindBuffer(r.ARRAY_BUFFER,null),u}function Fr(r,e,t,a){var n=Ef(r,t),i=je(n,3),s=i[0],o=i[1],l=i[2],u=Cf(r,o,e*s),v=s*l,f=r.createBuffer();r.bindBuffer(r.ARRAY_BUFFER,f),r.bufferData(r.ARRAY_BUFFER,e*v,r.DYNAMIC_DRAW),r.enableVertexAttribArray(a),o===r.FLOAT?r.vertexAttribPointer(a,s,o,!1,v,0):o===r.INT&&r.vertexAttribIPointer(a,s,o,v,0),r.vertexAttribDivisor(a,1),r.bindBuffer(r.ARRAY_BUFFER,null);for(var c=new Array(e),h=0;h<e;h++)c[h]=zy(r,o,u,v,s,h);return f.dataArray=u,f.stride=v,f.size=s,f.getView=function(d){return c[d]},f.setPoint=function(d,y,g){var p=c[d];p[0]=y,p[1]=g},f.bufferSubData=function(d){r.bindBuffer(r.ARRAY_BUFFER,f),d?r.bufferSubData(r.ARRAY_BUFFER,0,u,0,d*s):r.bufferSubData(r.ARRAY_BUFFER,0,u)},f}function Vy(r,e,t){for(var a=9,n=new Float32Array(e*a),i=new Array(e),s=0;s<e;s++){var o=s*a*4;i[s]=new Float32Array(n.buffer,o,a)}var l=r.createBuffer();r.bindBuffer(r.ARRAY_BUFFER,l),r.bufferData(r.ARRAY_BUFFER,n.byteLength,r.DYNAMIC_DRAW);for(var u=0;u<3;u++){var v=t+u;r.enableVertexAttribArray(v),r.vertexAttribPointer(v,3,r.FLOAT,!1,3*12,u*12),r.vertexAttribDivisor(v,1)}return r.bindBuffer(r.ARRAY_BUFFER,null),l.getMatrixView=function(f){return i[f]},l.setData=function(f,c){i[c].set(f,0)},l.bufferSubData=function(){r.bindBuffer(r.ARRAY_BUFFER,l),r.bufferSubData(r.ARRAY_BUFFER,0,n)},l}function qy(r){var e=r.createFramebuffer();r.bindFramebuffer(r.FRAMEBUFFER,e);var t=r.createTexture();return r.bindTexture(r.TEXTURE_2D,t),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_MIN_FILTER,r.LINEAR),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_WRAP_S,r.CLAMP_TO_EDGE),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_WRAP_T,r.CLAMP_TO_EDGE),r.framebufferTexture2D(r.FRAMEBUFFER,r.COLOR_ATTACHMENT0,r.TEXTURE_2D,t,0),r.bindFramebuffer(r.FRAMEBUFFER,null),e.setFramebufferAttachmentSizes=function(a,n){r.bindTexture(r.TEXTURE_2D,t),r.texImage2D(r.TEXTURE_2D,0,r.RGBA,a,n,0,r.RGBA,r.UNSIGNED_BYTE,null)},e}var Fl=typeof Float32Array<"u"?Float32Array:Array;Math.hypot||(Math.hypot=function(){for(var r=0,e=arguments.length;e--;)r+=arguments[e]*arguments[e];return Math.sqrt(r)});function bs(){var r=new Fl(9);return Fl!=Float32Array&&(r[1]=0,r[2]=0,r[3]=0,r[5]=0,r[6]=0,r[7]=0),r[0]=1,r[4]=1,r[8]=1,r}function Vl(r){return r[0]=1,r[1]=0,r[2]=0,r[3]=0,r[4]=1,r[5]=0,r[6]=0,r[7]=0,r[8]=1,r}function _y(r,e,t){var a=e[0],n=e[1],i=e[2],s=e[3],o=e[4],l=e[5],u=e[6],v=e[7],f=e[8],c=t[0],h=t[1],d=t[2],y=t[3],g=t[4],p=t[5],m=t[6],b=t[7],w=t[8];return r[0]=c*a+h*s+d*u,r[1]=c*n+h*o+d*v,r[2]=c*i+h*l+d*f,r[3]=y*a+g*s+p*u,r[4]=y*n+g*o+p*v,r[5]=y*i+g*l+p*f,r[6]=m*a+b*s+w*u,r[7]=m*n+b*o+w*v,r[8]=m*i+b*l+w*f,r}function gn(r,e,t){var a=e[0],n=e[1],i=e[2],s=e[3],o=e[4],l=e[5],u=e[6],v=e[7],f=e[8],c=t[0],h=t[1];return r[0]=a,r[1]=n,r[2]=i,r[3]=s,r[4]=o,r[5]=l,r[6]=c*a+h*s+u,r[7]=c*n+h*o+v,r[8]=c*i+h*l+f,r}function ql(r,e,t){var a=e[0],n=e[1],i=e[2],s=e[3],o=e[4],l=e[5],u=e[6],v=e[7],f=e[8],c=Math.sin(t),h=Math.cos(t);return r[0]=h*a+c*s,r[1]=h*n+c*o,r[2]=h*i+c*l,r[3]=h*s-c*a,r[4]=h*o-c*n,r[5]=h*l-c*i,r[6]=u,r[7]=v,r[8]=f,r}function _s(r,e,t){var a=t[0],n=t[1];return r[0]=a*e[0],r[1]=a*e[1],r[2]=a*e[2],r[3]=n*e[3],r[4]=n*e[4],r[5]=n*e[5],r[6]=e[6],r[7]=e[7],r[8]=e[8],r}function Gy(r,e,t){return r[0]=2/e,r[1]=0,r[2]=0,r[3]=0,r[4]=-2/t,r[5]=0,r[6]=-1,r[7]=1,r[8]=1,r}var Hy=function(){function r(e,t,a,n){vt(this,r),this.debugID=Math.floor(Math.random()*1e4),this.r=e,this.texSize=t,this.texRows=a,this.texHeight=Math.floor(t/a),this.enableWrapping=!0,this.locked=!1,this.texture=null,this.needsBuffer=!0,this.freePointer={x:0,row:0},this.keyToLocation=new Map,this.canvas=n(e,t,t),this.scratch=n(e,t,this.texHeight,"scratch")}return ft(r,[{key:"lock",value:function(){this.locked=!0}},{key:"getKeys",value:function(){return new Set(this.keyToLocation.keys())}},{key:"getScale",value:function(t){var a=t.w,n=t.h,i=this.texHeight,s=this.texSize,o=i/n,l=a*o,u=n*o;return l>s&&(o=s/a,l=a*o,u=n*o),{scale:o,texW:l,texH:u}}},{key:"draw",value:function(t,a,n){var i=this;if(this.locked)throw new Error("can't draw, atlas is locked");var s=this.texSize,o=this.texRows,l=this.texHeight,u=this.getScale(a),v=u.scale,f=u.texW,c=u.texH,h=function(b,w){if(n&&w){var E=w.context,C=b.x,x=b.row,k=C,S=l*x;E.save(),E.translate(k,S),E.scale(v,v),n(E,a),E.restore()}},d=[null,null],y=function(){h(i.freePointer,i.canvas),d[0]={x:i.freePointer.x,y:i.freePointer.row*l,w:f,h:c},d[1]={x:i.freePointer.x+f,y:i.freePointer.row*l,w:0,h:c},i.freePointer.x+=f,i.freePointer.x==s&&(i.freePointer.x=0,i.freePointer.row++)},g=function(){var b=i.scratch,w=i.canvas;b.clear(),h({x:0,row:0},b);var E=s-i.freePointer.x,C=f-E,x=l;{var k=i.freePointer.x,S=i.freePointer.row*l,P=E;w.context.drawImage(b,0,0,P,x,k,S,P,x),d[0]={x:k,y:S,w:P,h:c}}{var D=E,A=(i.freePointer.row+1)*l,B=C;w&&w.context.drawImage(b,D,0,B,x,0,A,B,x),d[1]={x:0,y:A,w:B,h:c}}i.freePointer.x=C,i.freePointer.row++},p=function(){i.freePointer.x=0,i.freePointer.row++};if(this.freePointer.x+f<=s)y();else{if(this.freePointer.row>=o-1)return!1;this.freePointer.x===s?(p(),y()):this.enableWrapping?g():(p(),y())}return this.keyToLocation.set(t,d),this.needsBuffer=!0,d}},{key:"getOffsets",value:function(t){return this.keyToLocation.get(t)}},{key:"isEmpty",value:function(){return this.freePointer.x===0&&this.freePointer.row===0}},{key:"canFit",value:function(t){if(this.locked)return!1;var a=this.texSize,n=this.texRows,i=this.getScale(t),s=i.texW;return this.freePointer.x+s>a?this.freePointer.row<n-1:!0}},{key:"bufferIfNeeded",value:function(t){this.texture||(this.texture=Ny(t,this.debugID)),this.needsBuffer&&(this.texture.buffer(this.canvas),this.needsBuffer=!1,this.locked&&(this.canvas=null,this.scratch=null))}},{key:"dispose",value:function(){this.texture&&(this.texture.deleteTexture(),this.texture=null),this.canvas=null,this.scratch=null,this.locked=!0}}])}(),Wy=function(){function r(e,t,a,n){vt(this,r),this.r=e,this.texSize=t,this.texRows=a,this.createTextureCanvas=n,this.atlases=[],this.styleKeyToAtlas=new Map,this.markedKeys=new Set}return ft(r,[{key:"getKeys",value:function(){return new Set(this.styleKeyToAtlas.keys())}},{key:"_createAtlas",value:function(){var t=this.r,a=this.texSize,n=this.texRows,i=this.createTextureCanvas;return new Hy(t,a,n,i)}},{key:"_getScratchCanvas",value:function(){if(!this.scratch){var t=this.r,a=this.texSize,n=this.texRows,i=this.createTextureCanvas,s=Math.floor(a/n);this.scratch=i(t,a,s,"scratch")}return this.scratch}},{key:"draw",value:function(t,a,n){var i=this.styleKeyToAtlas.get(t);return i||(i=this.atlases[this.atlases.length-1],(!i||!i.canFit(a))&&(i&&i.lock(),i=this._createAtlas(),this.atlases.push(i)),i.draw(t,a,n),this.styleKeyToAtlas.set(t,i)),i}},{key:"getAtlas",value:function(t){return this.styleKeyToAtlas.get(t)}},{key:"hasAtlas",value:function(t){return this.styleKeyToAtlas.has(t)}},{key:"markKeyForGC",value:function(t){this.markedKeys.add(t)}},{key:"gc",value:function(){var t=this,a=this.markedKeys;if(a.size===0){console.log("nothing to garbage collect");return}var n=[],i=new Map,s=null,o=Tr(this.atlases),l;try{var u=function(){var f=l.value,c=f.getKeys(),h=Uy(a,c);if(h.size===0)return n.push(f),c.forEach(function(E){return i.set(E,f)}),1;s||(s=t._createAtlas(),n.push(s));var d=Tr(c),y;try{for(d.s();!(y=d.n()).done;){var g=y.value;if(!h.has(g)){var p=f.getOffsets(g),m=je(p,2),b=m[0],w=m[1];s.canFit({w:b.w+w.w,h:b.h})||(s.lock(),s=t._createAtlas(),n.push(s)),f.canvas&&(t._copyTextureToNewAtlas(g,f,s),i.set(g,s))}}}catch(E){d.e(E)}finally{d.f()}f.dispose()};for(o.s();!(l=o.n()).done;)u()}catch(v){o.e(v)}finally{o.f()}this.atlases=n,this.styleKeyToAtlas=i,this.markedKeys=new Set}},{key:"_copyTextureToNewAtlas",value:function(t,a,n){var i=a.getOffsets(t),s=je(i,2),o=s[0],l=s[1];if(l.w===0)n.draw(t,o,function(c){c.drawImage(a.canvas,o.x,o.y,o.w,o.h,0,0,o.w,o.h)});else{var u=this._getScratchCanvas();u.clear(),u.context.drawImage(a.canvas,o.x,o.y,o.w,o.h,0,0,o.w,o.h),u.context.drawImage(a.canvas,l.x,l.y,l.w,l.h,o.w,0,l.w,l.h);var v=o.w+l.w,f=o.h;n.draw(t,{w:v,h:f},function(c){c.drawImage(u,0,0,v,f,0,0,v,f)})}}},{key:"getCounts",value:function(){return{keyCount:this.styleKeyToAtlas.size,atlasCount:new Set(this.styleKeyToAtlas.values()).size}}}])}();function Uy(r,e){return r.intersection?r.intersection(e):new Set(pn(r).filter(function(t){return e.has(t)}))}var $y=function(){function r(e,t){vt(this,r),this.r=e,this.globalOptions=t,this.atlasSize=t.webglTexSize,this.maxAtlasesPerBatch=t.webglTexPerBatch,this.renderTypes=new Map,this.collections=new Map,this.typeAndIdToKey=new Map}return ft(r,[{key:"getAtlasSize",value:function(){return this.atlasSize}},{key:"addAtlasCollection",value:function(t,a){var n=this.globalOptions,i=n.webglTexSize,s=n.createTextureCanvas,o=a.texRows,l=this._cacheScratchCanvas(s),u=new Wy(this.r,i,o,l);this.collections.set(t,u)}},{key:"addRenderType",value:function(t,a){var n=a.collection;if(!this.collections.has(n))throw new Error("invalid atlas collection name '".concat(n,"'"));var i=this.collections.get(n),s=he({type:t,atlasCollection:i},a);this.renderTypes.set(t,s)}},{key:"getRenderTypeOpts",value:function(t){return this.renderTypes.get(t)}},{key:"getAtlasCollection",value:function(t){return this.collections.get(t)}},{key:"_cacheScratchCanvas",value:function(t){var a=-1,n=-1,i=null;return function(s,o,l,u){return u?((!i||o!=a||l!=n)&&(a=o,n=l,i=t(s,o,l)),i):t(s,o,l)}}},{key:"_key",value:function(t,a){return"".concat(t,"-").concat(a)}},{key:"invalidate",value:function(t){var a=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.forceRedraw,s=i===void 0?!1:i,o=n.filterEle,l=o===void 0?function(){return!0}:o,u=n.filterType,v=u===void 0?function(){return!0}:u,f=!1,c=!1,h=Tr(t),d;try{for(h.s();!(d=h.n()).done;){var y=d.value;if(l(y)){var g=Tr(this.renderTypes.values()),p;try{var m=function(){var w=p.value,E=w.type;if(v(E)){var C=a.collections.get(w.collection),x=w.getKey(y),k=Array.isArray(x)?x:[x];if(s)k.forEach(function(A){return C.markKeyForGC(A)}),c=!0;else{var S=w.getID?w.getID(y):y.id(),P=a._key(E,S),D=a.typeAndIdToKey.get(P);D!==void 0&&!Iy(k,D)&&(f=!0,a.typeAndIdToKey.delete(P),D.forEach(function(A){return C.markKeyForGC(A)}))}}};for(g.s();!(p=g.n()).done;)m()}catch(b){g.e(b)}finally{g.f()}}}}catch(b){h.e(b)}finally{h.f()}return c&&(this.gc(),f=!1),f}},{key:"gc",value:function(){var t=Tr(this.collections.values()),a;try{for(t.s();!(a=t.n()).done;){var n=a.value;n.gc()}}catch(i){t.e(i)}finally{t.f()}}},{key:"getOrCreateAtlas",value:function(t,a,n,i){var s=this.renderTypes.get(a),o=this.collections.get(s.collection),l=!1,u=o.draw(i,n,function(c){s.drawClipped?(c.save(),c.beginPath(),c.rect(0,0,n.w,n.h),c.clip(),s.drawElement(c,t,n,!0,!0),c.restore()):s.drawElement(c,t,n,!0,!0),l=!0});if(l){var v=s.getID?s.getID(t):t.id(),f=this._key(a,v);this.typeAndIdToKey.has(f)?this.typeAndIdToKey.get(f).push(i):this.typeAndIdToKey.set(f,[i])}return u}},{key:"getAtlasInfo",value:function(t,a){var n=this,i=this.renderTypes.get(a),s=i.getKey(t),o=Array.isArray(s)?s:[s];return o.map(function(l){var u=i.getBoundingBox(t,l),v=n.getOrCreateAtlas(t,a,u,l),f=v.getOffsets(l),c=je(f,2),h=c[0],d=c[1];return{atlas:v,tex:h,tex1:h,tex2:d,bb:u}})}},{key:"getDebugInfo",value:function(){var t=[],a=Tr(this.collections),n;try{for(a.s();!(n=a.n()).done;){var i=je(n.value,2),s=i[0],o=i[1],l=o.getCounts(),u=l.keyCount,v=l.atlasCount;t.push({type:s,keyCount:u,atlasCount:v})}}catch(f){a.e(f)}finally{a.f()}return t}}])}(),Ky=function(){function r(e){vt(this,r),this.globalOptions=e,this.atlasSize=e.webglTexSize,this.maxAtlasesPerBatch=e.webglTexPerBatch,this.batchAtlases=[]}return ft(r,[{key:"getMaxAtlasesPerBatch",value:function(){return this.maxAtlasesPerBatch}},{key:"getAtlasSize",value:function(){return this.atlasSize}},{key:"getIndexArray",value:function(){return Array.from({length:this.maxAtlasesPerBatch},function(t,a){return a})}},{key:"startBatch",value:function(){this.batchAtlases=[]}},{key:"getAtlasCount",value:function(){return this.batchAtlases.length}},{key:"getAtlases",value:function(){return this.batchAtlases}},{key:"canAddToCurrentBatch",value:function(t){return this.batchAtlases.length===this.maxAtlasesPerBatch?this.batchAtlases.includes(t):!0}},{key:"getAtlasIndexForBatch",value:function(t){var a=this.batchAtlases.indexOf(t);if(a<0){if(this.batchAtlases.length===this.maxAtlasesPerBatch)throw new Error("cannot add more atlases to batch");this.batchAtlases.push(t),a=this.batchAtlases.length-1}return a}}])}(),Yy=`
  float circleSD(vec2 p, float r) {
    return distance(vec2(0), p) - r; // signed distance
  }
`,Xy=`
  float rectangleSD(vec2 p, vec2 b) {
    vec2 d = abs(p)-b;
    return distance(vec2(0),max(d,0.0)) + min(max(d.x,d.y),0.0);
  }
`,Zy=`
  float roundRectangleSD(vec2 p, vec2 b, vec4 cr) {
    cr.xy = (p.x > 0.0) ? cr.xy : cr.zw;
    cr.x  = (p.y > 0.0) ? cr.x  : cr.y;
    vec2 q = abs(p) - b + cr.x;
    return min(max(q.x, q.y), 0.0) + distance(vec2(0), max(q, 0.0)) - cr.x;
  }
`,Qy=`
  float ellipseSD(vec2 p, vec2 ab) {
    p = abs( p ); // symmetry

    // find root with Newton solver
    vec2 q = ab*(p-ab);
    float w = (q.x<q.y)? 1.570796327 : 0.0;
    for( int i=0; i<5; i++ ) {
      vec2 cs = vec2(cos(w),sin(w));
      vec2 u = ab*vec2( cs.x,cs.y);
      vec2 v = ab*vec2(-cs.y,cs.x);
      w = w + dot(p-u,v)/(dot(p-u,u)+dot(v,v));
    }
    
    // compute final point and distance
    float d = length(p-ab*vec2(cos(w),sin(w)));
    
    // return signed distance
    return (dot(p/ab,p/ab)>1.0) ? d : -d;
  }
`,ba={SCREEN:{name:"screen",screen:!0},PICKING:{name:"picking",picking:!0}},Bn={IGNORE:1,USE_BB:2},ws=0,_l=1,Gl=2,xs=3,Ft=4,an=5,ca=6,da=7,Jy=function(){function r(e,t,a){vt(this,r),this.r=e,this.gl=t,this.maxInstances=a.webglBatchSize,this.atlasSize=a.webglTexSize,this.bgColor=a.bgColor,this.debug=a.webglDebug,this.batchDebugInfo=[],a.enableWrapping=!0,a.createTextureCanvas=Ay,this.atlasManager=new $y(e,a),this.batchManager=new Ky(a),this.simpleShapeOptions=new Map,this.program=this._createShaderProgram(ba.SCREEN),this.pickingProgram=this._createShaderProgram(ba.PICKING),this.vao=this._createVAO()}return ft(r,[{key:"addAtlasCollection",value:function(t,a){this.atlasManager.addAtlasCollection(t,a)}},{key:"addTextureAtlasRenderType",value:function(t,a){this.atlasManager.addRenderType(t,a)}},{key:"addSimpleShapeRenderType",value:function(t,a){this.simpleShapeOptions.set(t,a)}},{key:"invalidate",value:function(t){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=a.type,i=this.atlasManager;return n?i.invalidate(t,{filterType:function(o){return o===n},forceRedraw:!0}):i.invalidate(t)}},{key:"gc",value:function(){this.atlasManager.gc()}},{key:"_createShaderProgram",value:function(t){var a=this.gl,n=`#version 300 es
      precision highp float;

      uniform mat3 uPanZoomMatrix;
      uniform int  uAtlasSize;
      
      // instanced
      in vec2 aPosition; // a vertex from the unit square
      
      in mat3 aTransform; // used to transform verticies, eg into a bounding box
      in int aVertType; // the type of thing we are rendering

      // the z-index that is output when using picking mode
      in vec4 aIndex;
      
      // For textures
      in int aAtlasId; // which shader unit/atlas to use
      in vec4 aTex; // x/y/w/h of texture in atlas

      // for edges
      in vec4 aPointAPointB;
      in vec4 aPointCPointD;
      in vec2 aLineWidth; // also used for node border width

      // simple shapes
      in vec4 aCornerRadius; // for round-rectangle [top-right, bottom-right, top-left, bottom-left]
      in vec4 aColor; // also used for edges
      in vec4 aBorderColor; // aLineWidth is used for border width

      // output values passed to the fragment shader
      out vec2 vTexCoord;
      out vec4 vColor;
      out vec2 vPosition;
      // flat values are not interpolated
      flat out int vAtlasId; 
      flat out int vVertType;
      flat out vec2 vTopRight;
      flat out vec2 vBotLeft;
      flat out vec4 vCornerRadius;
      flat out vec4 vBorderColor;
      flat out vec2 vBorderWidth;
      flat out vec4 vIndex;
      
      void main(void) {
        int vid = gl_VertexID;
        vec2 position = aPosition; // TODO make this a vec3, simplifies some code below

        if(aVertType == `.concat(ws,`) {
          float texX = aTex.x; // texture coordinates
          float texY = aTex.y;
          float texW = aTex.z;
          float texH = aTex.w;

          if(vid == 1 || vid == 2 || vid == 4) {
            texX += texW;
          }
          if(vid == 2 || vid == 4 || vid == 5) {
            texY += texH;
          }

          float d = float(uAtlasSize);
          vTexCoord = vec2(texX / d, texY / d); // tex coords must be between 0 and 1

          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);
        }
        else if(aVertType == `).concat(Ft," || aVertType == ").concat(da,` 
             || aVertType == `).concat(an," || aVertType == ").concat(ca,`) { // simple shapes

          // the bounding box is needed by the fragment shader
          vBotLeft  = (aTransform * vec3(0, 0, 1)).xy; // flat
          vTopRight = (aTransform * vec3(1, 1, 1)).xy; // flat
          vPosition = (aTransform * vec3(position, 1)).xy; // will be interpolated

          // calculations are done in the fragment shader, just pass these along
          vColor = aColor;
          vCornerRadius = aCornerRadius;
          vBorderColor = aBorderColor;
          vBorderWidth = aLineWidth;

          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);
        }
        else if(aVertType == `).concat(_l,`) {
          vec2 source = aPointAPointB.xy;
          vec2 target = aPointAPointB.zw;

          // adjust the geometry so that the line is centered on the edge
          position.y = position.y - 0.5;

          // stretch the unit square into a long skinny rectangle
          vec2 xBasis = target - source;
          vec2 yBasis = normalize(vec2(-xBasis.y, xBasis.x));
          vec2 point = source + xBasis * position.x + yBasis * aLineWidth[0] * position.y;

          gl_Position = vec4(uPanZoomMatrix * vec3(point, 1.0), 1.0);
          vColor = aColor;
        } 
        else if(aVertType == `).concat(Gl,`) {
          vec2 pointA = aPointAPointB.xy;
          vec2 pointB = aPointAPointB.zw;
          vec2 pointC = aPointCPointD.xy;
          vec2 pointD = aPointCPointD.zw;

          // adjust the geometry so that the line is centered on the edge
          position.y = position.y - 0.5;

          vec2 p0, p1, p2, pos;
          if(position.x == 0.0) { // The left side of the unit square
            p0 = pointA;
            p1 = pointB;
            p2 = pointC;
            pos = position;
          } else { // The right side of the unit square, use same approach but flip the geometry upside down
            p0 = pointD;
            p1 = pointC;
            p2 = pointB;
            pos = vec2(0.0, -position.y);
          }

          vec2 p01 = p1 - p0;
          vec2 p12 = p2 - p1;
          vec2 p21 = p1 - p2;

          // Find the normal vector.
          vec2 tangent = normalize(normalize(p12) + normalize(p01));
          vec2 normal = vec2(-tangent.y, tangent.x);

          // Find the vector perpendicular to p0 -> p1.
          vec2 p01Norm = normalize(vec2(-p01.y, p01.x));

          // Determine the bend direction.
          float sigma = sign(dot(p01 + p21, normal));
          float width = aLineWidth[0];

          if(sign(pos.y) == -sigma) {
            // This is an intersecting vertex. Adjust the position so that there's no overlap.
            vec2 point = 0.5 * width * normal * -sigma / dot(normal, p01Norm);
            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);
          } else {
            // This is a non-intersecting vertex. Treat it like a mitre join.
            vec2 point = 0.5 * width * normal * sigma * dot(normal, p01Norm);
            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);
          }

          vColor = aColor;
        } 
        else if(aVertType == `).concat(xs,` && vid < 3) {
          // massage the first triangle into an edge arrow
          if(vid == 0)
            position = vec2(-0.15, -0.3);
          if(vid == 1)
            position = vec2(  0.0,  0.0);
          if(vid == 2)
            position = vec2( 0.15, -0.3);

          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);
          vColor = aColor;
        }
        else {
          gl_Position = vec4(2.0, 0.0, 0.0, 1.0); // discard vertex by putting it outside webgl clip space
        }

        vAtlasId = aAtlasId;
        vVertType = aVertType;
        vIndex = aIndex;
      }
    `),i=this.batchManager.getIndexArray(),s=`#version 300 es
      precision highp float;

      // declare texture unit for each texture atlas in the batch
      `.concat(i.map(function(u){return"uniform sampler2D uTexture".concat(u,";")}).join(`
	`),`

      uniform vec4 uBGColor;
      uniform float uZoom;

      in vec2 vTexCoord;
      in vec4 vColor;
      in vec2 vPosition; // model coordinates

      flat in int vAtlasId;
      flat in vec4 vIndex;
      flat in int vVertType;
      flat in vec2 vTopRight;
      flat in vec2 vBotLeft;
      flat in vec4 vCornerRadius;
      flat in vec4 vBorderColor;
      flat in vec2 vBorderWidth;

      out vec4 outColor;

      `).concat(Yy,`
      `).concat(Xy,`
      `).concat(Zy,`
      `).concat(Qy,`

      vec4 blend(vec4 top, vec4 bot) { // blend colors with premultiplied alpha
        return vec4( 
          top.rgb + (bot.rgb * (1.0 - top.a)),
          top.a   + (bot.a   * (1.0 - top.a)) 
        );
      }

      vec4 distInterp(vec4 cA, vec4 cB, float d) { // interpolate color using Signed Distance
        // scale to the zoom level so that borders don't look blurry when zoomed in
        // note 1.5 is an aribitrary value chosen because it looks good
        return mix(cA, cB, 1.0 - smoothstep(0.0, 1.5 / uZoom, abs(d))); 
      }

      void main(void) {
        if(vVertType == `).concat(ws,`) {
          // look up the texel from the texture unit
          `).concat(i.map(function(u){return"if(vAtlasId == ".concat(u,") outColor = texture(uTexture").concat(u,", vTexCoord);")}).join(`
	else `),`
        } 
        else if(vVertType == `).concat(xs,`) {
          // mimics how canvas renderer uses context.globalCompositeOperation = 'destination-out';
          outColor = blend(vColor, uBGColor);
          outColor.a = 1.0; // make opaque, masks out line under arrow
        }
        else if(vVertType == `).concat(Ft,` && vBorderWidth == vec2(0.0)) { // simple rectangle with no border
          outColor = vColor; // unit square is already transformed to the rectangle, nothing else needs to be done
        }
        else if(vVertType == `).concat(Ft," || vVertType == ").concat(da,` 
          || vVertType == `).concat(an," || vVertType == ").concat(ca,`) { // use SDF

          float outerBorder = vBorderWidth[0];
          float innerBorder = vBorderWidth[1];
          float borderPadding = outerBorder * 2.0;
          float w = vTopRight.x - vBotLeft.x - borderPadding;
          float h = vTopRight.y - vBotLeft.y - borderPadding;
          vec2 b = vec2(w/2.0, h/2.0); // half width, half height
          vec2 p = vPosition - vec2(vTopRight.x - b[0] - outerBorder, vTopRight.y - b[1] - outerBorder); // translate to center

          float d; // signed distance
          if(vVertType == `).concat(Ft,`) {
            d = rectangleSD(p, b);
          } else if(vVertType == `).concat(da,` && w == h) {
            d = circleSD(p, b.x); // faster than ellipse
          } else if(vVertType == `).concat(da,`) {
            d = ellipseSD(p, b);
          } else {
            d = roundRectangleSD(p, b, vCornerRadius.wzyx);
          }

          // use the distance to interpolate a color to smooth the edges of the shape, doesn't need multisampling
          // we must smooth colors inwards, because we can't change pixels outside the shape's bounding box
          if(d > 0.0) {
            if(d > outerBorder) {
              discard;
            } else {
              outColor = distInterp(vBorderColor, vec4(0), d - outerBorder);
            }
          } else {
            if(d > innerBorder) {
              vec4 outerColor = outerBorder == 0.0 ? vec4(0) : vBorderColor;
              vec4 innerBorderColor = blend(vBorderColor, vColor);
              outColor = distInterp(innerBorderColor, outerColor, d);
            } 
            else {
              vec4 outerColor;
              if(innerBorder == 0.0 && outerBorder == 0.0) {
                outerColor = vec4(0);
              } else if(innerBorder == 0.0) {
                outerColor = vBorderColor;
              } else {
                outerColor = blend(vBorderColor, vColor);
              }
              outColor = distInterp(vColor, outerColor, d - innerBorder);
            }
          }
        }
        else {
          outColor = vColor;
        }

        `).concat(t.picking?`if(outColor.a == 0.0) discard;
             else outColor = vIndex;`:"",`
      }
    `),o=Py(a,n,s);o.aPosition=a.getAttribLocation(o,"aPosition"),o.aIndex=a.getAttribLocation(o,"aIndex"),o.aVertType=a.getAttribLocation(o,"aVertType"),o.aTransform=a.getAttribLocation(o,"aTransform"),o.aAtlasId=a.getAttribLocation(o,"aAtlasId"),o.aTex=a.getAttribLocation(o,"aTex"),o.aPointAPointB=a.getAttribLocation(o,"aPointAPointB"),o.aPointCPointD=a.getAttribLocation(o,"aPointCPointD"),o.aLineWidth=a.getAttribLocation(o,"aLineWidth"),o.aColor=a.getAttribLocation(o,"aColor"),o.aCornerRadius=a.getAttribLocation(o,"aCornerRadius"),o.aBorderColor=a.getAttribLocation(o,"aBorderColor"),o.uPanZoomMatrix=a.getUniformLocation(o,"uPanZoomMatrix"),o.uAtlasSize=a.getUniformLocation(o,"uAtlasSize"),o.uBGColor=a.getUniformLocation(o,"uBGColor"),o.uZoom=a.getUniformLocation(o,"uZoom"),o.uTextures=[];for(var l=0;l<this.batchManager.getMaxAtlasesPerBatch();l++)o.uTextures.push(a.getUniformLocation(o,"uTexture".concat(l)));return o}},{key:"_createVAO",value:function(){var t=[0,0,1,0,1,1,0,0,1,1,0,1];this.vertexCount=t.length/2;var a=this.maxInstances,n=this.gl,i=this.program,s=n.createVertexArray();return n.bindVertexArray(s),Fy(n,"vec2",i.aPosition,t),this.transformBuffer=Vy(n,a,i.aTransform),this.indexBuffer=Fr(n,a,"vec4",i.aIndex),this.vertTypeBuffer=Fr(n,a,"int",i.aVertType),this.atlasIdBuffer=Fr(n,a,"int",i.aAtlasId),this.texBuffer=Fr(n,a,"vec4",i.aTex),this.pointAPointBBuffer=Fr(n,a,"vec4",i.aPointAPointB),this.pointCPointDBuffer=Fr(n,a,"vec4",i.aPointCPointD),this.lineWidthBuffer=Fr(n,a,"vec2",i.aLineWidth),this.colorBuffer=Fr(n,a,"vec4",i.aColor),this.cornerRadiusBuffer=Fr(n,a,"vec4",i.aCornerRadius),this.borderColorBuffer=Fr(n,a,"vec4",i.aBorderColor),n.bindVertexArray(null),s}},{key:"buffers",get:function(){var t=this;return this._buffers||(this._buffers=Object.keys(this).filter(function(a){return a.endsWith("Buffer")}).map(function(a){return t[a]})),this._buffers}},{key:"startFrame",value:function(t){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ba.SCREEN;this.panZoomMatrix=t,this.renderTarget=a,this.batchDebugInfo=[],this.wrappedCount=0,this.simpleCount=0,this.startBatch()}},{key:"startBatch",value:function(){this.instanceCount=0,this.batchManager.startBatch()}},{key:"endFrame",value:function(){this.endBatch()}},{key:"_isVisible",value:function(t,a){return t.visible()?a&&a.isVisible?a.isVisible(t):!0:!1}},{key:"drawTexture",value:function(t,a,n){var i=this.atlasManager,s=this.batchManager,o=i.getRenderTypeOpts(n);if(this._isVisible(t,o)){if(this.renderTarget.picking&&o.getTexPickingMode){var l=o.getTexPickingMode(t);if(l===Bn.IGNORE)return;if(l==Bn.USE_BB){this.drawPickingRectangle(t,a,n);return}}var u=i.getAtlasInfo(t,n),v=Tr(u),f;try{for(v.s();!(f=v.n()).done;){var c=f.value,h=c.atlas,d=c.tex1,y=c.tex2;s.canAddToCurrentBatch(h)||this.endBatch();for(var g=s.getAtlasIndexForBatch(h),p=0,m=[[d,!0],[y,!1]];p<m.length;p++){var b=je(m[p],2),w=b[0],E=b[1];if(w.w!=0){var C=this.instanceCount;this.vertTypeBuffer.getView(C)[0]=ws;var x=this.indexBuffer.getView(C);zt(a,x);var k=this.atlasIdBuffer.getView(C);k[0]=g;var S=this.texBuffer.getView(C);S[0]=w.x,S[1]=w.y,S[2]=w.w,S[3]=w.h;var P=this.transformBuffer.getMatrixView(C);this.setTransformMatrix(t,P,o,c,E),this.instanceCount++,E||this.wrappedCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}}catch(D){v.e(D)}finally{v.f()}}}},{key:"setTransformMatrix",value:function(t,a,n,i){var s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,o=0;if(n.shapeProps&&n.shapeProps.padding&&(o=t.pstyle(n.shapeProps.padding).pfValue),i){var l=i.bb,u=i.tex1,v=i.tex2,f=u.w/(u.w+v.w);s||(f=1-f);var c=this._getAdjustedBB(l,o,s,f);this._applyTransformMatrix(a,c,n,t)}else{var h=n.getBoundingBox(t),d=this._getAdjustedBB(h,o,!0,1);this._applyTransformMatrix(a,d,n,t)}}},{key:"_applyTransformMatrix",value:function(t,a,n,i){var s,o;Vl(t);var l=n.getRotation?n.getRotation(i):0;if(l!==0){var u=n.getRotationPoint(i),v=u.x,f=u.y;gn(t,t,[v,f]),ql(t,t,l);var c=n.getRotationOffset(i);s=c.x+(a.xOffset||0),o=c.y+(a.yOffset||0)}else s=a.x1,o=a.y1;gn(t,t,[s,o]),_s(t,t,[a.w,a.h])}},{key:"_getAdjustedBB",value:function(t,a,n,i){var s=t.x1,o=t.y1,l=t.w,u=t.h,v=t.yOffset;a&&(s-=a,o-=a,l+=2*a,u+=2*a);var f=0,c=l*i;return n&&i<1?l=c:!n&&i<1&&(f=l-c,s+=f,l=c),{x1:s,y1:o,w:l,h:u,xOffset:f,yOffset:v}}},{key:"drawPickingRectangle",value:function(t,a,n){var i=this.atlasManager.getRenderTypeOpts(n),s=this.instanceCount;this.vertTypeBuffer.getView(s)[0]=Ft;var o=this.indexBuffer.getView(s);zt(a,o);var l=this.colorBuffer.getView(s);yt([0,0,0],1,l);var u=this.transformBuffer.getMatrixView(s);this.setTransformMatrix(t,u,i),this.simpleCount++,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}},{key:"drawNode",value:function(t,a,n){var i=this.simpleShapeOptions.get(n);if(this._isVisible(t,i)){var s=i.shapeProps,o=this._getVertTypeForShape(t,s.shape);if(o===void 0||i.isSimple&&!i.isSimple(t)){this.drawTexture(t,a,n);return}var l=this.instanceCount;if(this.vertTypeBuffer.getView(l)[0]=o,o===an||o===ca){var u=i.getBoundingBox(t),v=this._getCornerRadius(t,s.radius,u),f=this.cornerRadiusBuffer.getView(l);f[0]=v,f[1]=v,f[2]=v,f[3]=v,o===ca&&(f[0]=0,f[2]=0)}var c=this.indexBuffer.getView(l);zt(a,c);var h=t.pstyle(s.color).value,d=t.pstyle(s.opacity).value,y=this.colorBuffer.getView(l);yt(h,d,y);var g=this.lineWidthBuffer.getView(l);if(g[0]=0,g[1]=0,s.border){var p=t.pstyle("border-width").value;if(p>0){var m=t.pstyle("border-color").value,b=t.pstyle("border-opacity").value,w=this.borderColorBuffer.getView(l);yt(m,b,w);var E=t.pstyle("border-position").value;if(E==="inside")g[0]=0,g[1]=-p;else if(E==="outside")g[0]=p,g[1]=0;else{var C=p/2;g[0]=C,g[1]=-C}}}var x=this.transformBuffer.getMatrixView(l);this.setTransformMatrix(t,x,i),this.simpleCount++,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}},{key:"_getVertTypeForShape",value:function(t,a){var n=t.pstyle(a).value;switch(n){case"rectangle":return Ft;case"ellipse":return da;case"roundrectangle":case"round-rectangle":return an;case"bottom-round-rectangle":return ca;default:return}}},{key:"_getCornerRadius",value:function(t,a,n){var i=n.w,s=n.h;if(t.pstyle(a).value==="auto")return st(i,s);var o=t.pstyle(a).pfValue,l=i/2,u=s/2;return Math.min(o,u,l)}},{key:"drawEdgeArrow",value:function(t,a,n){if(t.visible()){var i=t._private.rscratch,s,o,l;if(n==="source"?(s=i.arrowStartX,o=i.arrowStartY,l=i.srcArrowAngle):(s=i.arrowEndX,o=i.arrowEndY,l=i.tgtArrowAngle),!(isNaN(s)||s==null||isNaN(o)||o==null||isNaN(l)||l==null)){var u=t.pstyle(n+"-arrow-shape").value;if(u!=="none"){var v=t.pstyle(n+"-arrow-color").value,f=t.pstyle("opacity").value,c=t.pstyle("line-opacity").value,h=f*c,d=t.pstyle("width").pfValue,y=t.pstyle("arrow-scale").value,g=this.r.getArrowWidth(d,y),p=this.instanceCount,m=this.transformBuffer.getMatrixView(p);Vl(m),gn(m,m,[s,o]),_s(m,m,[g,g]),ql(m,m,l),this.vertTypeBuffer.getView(p)[0]=xs;var b=this.indexBuffer.getView(p);zt(a,b);var w=this.colorBuffer.getView(p);yt(v,h,w),this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}}},{key:"drawEdgeLine",value:function(t,a){if(t.visible()){var n=this._getEdgePoints(t);if(n){var i=t.pstyle("opacity").value,s=t.pstyle("line-opacity").value,o=t.pstyle("width").pfValue,l=t.pstyle("line-color").value,u=i*s;if(n.length/2+this.instanceCount>this.maxInstances&&this.endBatch(),n.length==4){var v=this.instanceCount;this.vertTypeBuffer.getView(v)[0]=_l;var f=this.indexBuffer.getView(v);zt(a,f);var c=this.colorBuffer.getView(v);yt(l,u,c);var h=this.lineWidthBuffer.getView(v);h[0]=o;var d=this.pointAPointBBuffer.getView(v);d[0]=n[0],d[1]=n[1],d[2]=n[2],d[3]=n[3],this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}else for(var y=0;y<n.length-2;y+=2){var g=this.instanceCount;this.vertTypeBuffer.getView(g)[0]=Gl;var p=this.indexBuffer.getView(g);zt(a,p);var m=this.colorBuffer.getView(g);yt(l,u,m);var b=this.lineWidthBuffer.getView(g);b[0]=o;var w=n[y-2],E=n[y-1],C=n[y],x=n[y+1],k=n[y+2],S=n[y+3],P=n[y+4],D=n[y+5];y==0&&(w=2*C-k+.001,E=2*x-S+.001),y==n.length-4&&(P=2*k-C+.001,D=2*S-x+.001);var A=this.pointAPointBBuffer.getView(g);A[0]=w,A[1]=E,A[2]=C,A[3]=x;var B=this.pointCPointDBuffer.getView(g);B[0]=k,B[1]=S,B[2]=P,B[3]=D,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}}},{key:"_getEdgePoints",value:function(t){var a=t._private.rscratch;if(!(a.badLine||a.allpts==null||isNaN(a.allpts[0]))){var n=a.allpts;if(n.length==4)return n;var i=this._getNumSegments(t);return this._getCurveSegmentPoints(n,i)}}},{key:"_getNumSegments",value:function(t){var a=15;return Math.min(Math.max(a,5),this.maxInstances)}},{key:"_getCurveSegmentPoints",value:function(t,a){if(t.length==4)return t;for(var n=Array((a+1)*2),i=0;i<=a;i++)if(i==0)n[0]=t[0],n[1]=t[1];else if(i==a)n[i*2]=t[t.length-2],n[i*2+1]=t[t.length-1];else{var s=i/a;this._setCurvePoint(t,s,n,i*2)}return n}},{key:"_setCurvePoint",value:function(t,a,n,i){if(t.length<=2)n[i]=t[0],n[i+1]=t[1];else{for(var s=Array(t.length-2),o=0;o<s.length;o+=2){var l=(1-a)*t[o]+a*t[o+2],u=(1-a)*t[o+1]+a*t[o+3];s[o]=l,s[o+1]=u}return this._setCurvePoint(s,a,n,i)}}},{key:"endBatch",value:function(){var t=this.gl,a=this.vao,n=this.vertexCount,i=this.instanceCount;if(i!==0){var s=this.renderTarget.picking?this.pickingProgram:this.program;t.useProgram(s),t.bindVertexArray(a);var o=Tr(this.buffers),l;try{for(o.s();!(l=o.n()).done;){var u=l.value;u.bufferSubData(i)}}catch(d){o.e(d)}finally{o.f()}for(var v=this.batchManager.getAtlases(),f=0;f<v.length;f++)v[f].bufferIfNeeded(t);for(var c=0;c<v.length;c++)t.activeTexture(t.TEXTURE0+c),t.bindTexture(t.TEXTURE_2D,v[c].texture),t.uniform1i(s.uTextures[c],c);t.uniform1f(s.uZoom,Ry(this.r)),t.uniformMatrix3fv(s.uPanZoomMatrix,!1,this.panZoomMatrix),t.uniform1i(s.uAtlasSize,this.batchManager.getAtlasSize());var h=yt(this.bgColor,1);t.uniform4fv(s.uBGColor,h),t.drawArraysInstanced(t.TRIANGLES,0,n,i),t.bindVertexArray(null),t.bindTexture(t.TEXTURE_2D,null),this.debug&&this.batchDebugInfo.push({count:i,atlasCount:v.length}),this.startBatch()}}},{key:"getDebugInfo",value:function(){var t=this.atlasManager.getDebugInfo(),a=t.reduce(function(s,o){return s+o.atlasCount},0),n=this.batchDebugInfo,i=n.reduce(function(s,o){return s+o.count},0);return{atlasInfo:t,totalAtlases:a,wrappedCount:this.wrappedCount,simpleCount:this.simpleCount,batchCount:n.length,batchInfo:n,totalInstances:i}}}])}(),Tf={};Tf.initWebgl=function(r,e){var t=this,a=t.data.contexts[t.WEBGL];r.bgColor=jy(t),r.webglTexSize=Math.min(r.webglTexSize,a.getParameter(a.MAX_TEXTURE_SIZE)),r.webglTexRows=Math.min(r.webglTexRows,54),r.webglTexRowsNodes=Math.min(r.webglTexRowsNodes,54),r.webglBatchSize=Math.min(r.webglBatchSize,16384),r.webglTexPerBatch=Math.min(r.webglTexPerBatch,a.getParameter(a.MAX_TEXTURE_IMAGE_UNITS)),t.webglDebug=r.webglDebug,t.webglDebugShowAtlases=r.webglDebugShowAtlases,t.pickingFrameBuffer=qy(a),t.pickingFrameBuffer.needsDraw=!0,t.drawing=new Jy(t,a,r);var n=function(f){return function(c){return t.getTextAngle(c,f)}},i=function(f){return function(c){var h=c.pstyle(f);return h&&h.value}},s=function(f){return function(c){return c.pstyle("".concat(f,"-opacity")).value>0}},o=function(f){var c=f.pstyle("text-events").strValue==="yes";return c?Bn.USE_BB:Bn.IGNORE},l=function(f){var c=f.position(),h=c.x,d=c.y,y=f.outerWidth(),g=f.outerHeight();return{w:y,h:g,x1:h-y/2,y1:d-g/2}};t.drawing.addAtlasCollection("node",{texRows:r.webglTexRowsNodes}),t.drawing.addAtlasCollection("label",{texRows:r.webglTexRows}),t.drawing.addTextureAtlasRenderType("node-body",{collection:"node",getKey:e.getStyleKey,getBoundingBox:e.getElementBox,drawElement:e.drawElement}),t.drawing.addSimpleShapeRenderType("node-body",{getBoundingBox:l,isSimple:Ly,shapeProps:{shape:"shape",color:"background-color",opacity:"background-opacity",radius:"corner-radius",border:!0}}),t.drawing.addSimpleShapeRenderType("node-overlay",{getBoundingBox:l,isVisible:s("overlay"),shapeProps:{shape:"overlay-shape",color:"overlay-color",opacity:"overlay-opacity",padding:"overlay-padding",radius:"overlay-corner-radius"}}),t.drawing.addSimpleShapeRenderType("node-underlay",{getBoundingBox:l,isVisible:s("underlay"),shapeProps:{shape:"underlay-shape",color:"underlay-color",opacity:"underlay-opacity",padding:"underlay-padding",radius:"underlay-corner-radius"}}),t.drawing.addTextureAtlasRenderType("label",{collection:"label",getTexPickingMode:o,getKey:Es(e.getLabelKey,null),getBoundingBox:Cs(e.getLabelBox,null),drawClipped:!0,drawElement:e.drawLabel,getRotation:n(null),getRotationPoint:e.getLabelRotationPoint,getRotationOffset:e.getLabelRotationOffset,isVisible:i("label")}),t.drawing.addTextureAtlasRenderType("edge-source-label",{collection:"label",getTexPickingMode:o,getKey:Es(e.getSourceLabelKey,"source"),getBoundingBox:Cs(e.getSourceLabelBox,"source"),drawClipped:!0,drawElement:e.drawSourceLabel,getRotation:n("source"),getRotationPoint:e.getSourceLabelRotationPoint,getRotationOffset:e.getSourceLabelRotationOffset,isVisible:i("source-label")}),t.drawing.addTextureAtlasRenderType("edge-target-label",{collection:"label",getTexPickingMode:o,getKey:Es(e.getTargetLabelKey,"target"),getBoundingBox:Cs(e.getTargetLabelBox,"target"),drawClipped:!0,drawElement:e.drawTargetLabel,getRotation:n("target"),getRotationPoint:e.getTargetLabelRotationPoint,getRotationOffset:e.getTargetLabelRotationOffset,isVisible:i("target-label")});var u=Oa(function(){console.log("garbage collect flag set"),t.data.gc=!0},1e4);t.onUpdateEleCalcs(function(v,f){var c=!1;f&&f.length>0&&(c|=t.drawing.invalidate(f)),c&&u()}),em(t)};function jy(r){var e=r.cy.container(),t=e&&e.style&&e.style.backgroundColor||"white";return jl(t)}function Sf(r,e){var t=r._private.rscratch;return Er(t,"labelWrapCachedLines",e)||[]}var Es=function(e,t){return function(a){var n=e(a),i=Sf(a,t);return i.length>1?i.map(function(s,o){return"".concat(n,"_").concat(o)}):n}},Cs=function(e,t){return function(a,n){var i=e(a);if(typeof n=="string"){var s=n.indexOf("_");if(s>0){var o=Number(n.substring(s+1)),l=Sf(a,t),u=i.h/l.length,v=u*o,f=i.y1+v;return{x1:i.x1,w:i.w,y1:f,h:u,yOffset:v}}}return i}};function em(r){{var e=r.render;r.render=function(i){i=i||{};var s=r.cy;r.webgl&&(s.zoom()>yf?(rm(r),e.call(r,i)):(tm(r),kf(r,i,ba.SCREEN)))}}{var t=r.matchCanvasSize;r.matchCanvasSize=function(i){t.call(r,i),r.pickingFrameBuffer.setFramebufferAttachmentSizes(r.canvasWidth,r.canvasHeight),r.pickingFrameBuffer.needsDraw=!0}}r.findNearestElements=function(i,s,o,l){return um(r,i,s)};{var a=r.invalidateCachedZSortedEles;r.invalidateCachedZSortedEles=function(){a.call(r),r.pickingFrameBuffer.needsDraw=!0}}{var n=r.notify;r.notify=function(i,s){n.call(r,i,s),i==="viewport"||i==="bounds"?r.pickingFrameBuffer.needsDraw=!0:i==="background"&&r.drawing.invalidate(s,{type:"node-body"})}}}function rm(r){var e=r.data.contexts[r.WEBGL];e.clear(e.COLOR_BUFFER_BIT|e.DEPTH_BUFFER_BIT)}function tm(r){var e=function(a){a.save(),a.setTransform(1,0,0,1,0,0),a.clearRect(0,0,r.canvasWidth,r.canvasHeight),a.restore()};e(r.data.contexts[r.NODE]),e(r.data.contexts[r.DRAG])}function am(r){var e=r.canvasWidth,t=r.canvasHeight,a=po(r),n=a.pan,i=a.zoom,s=bs();gn(s,s,[n.x,n.y]),_s(s,s,[i,i]);var o=bs();Gy(o,e,t);var l=bs();return _y(l,o,s),l}function Df(r,e){var t=r.canvasWidth,a=r.canvasHeight,n=po(r),i=n.pan,s=n.zoom;e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,t,a),e.translate(i.x,i.y),e.scale(s,s)}function nm(r,e){r.drawSelectionRectangle(e,function(t){return Df(r,t)})}function im(r){var e=r.data.contexts[r.NODE];e.save(),Df(r,e),e.strokeStyle="rgba(0, 0, 0, 0.3)",e.beginPath(),e.moveTo(-1e3,0),e.lineTo(1e3,0),e.stroke(),e.beginPath(),e.moveTo(0,-1e3),e.lineTo(0,1e3),e.stroke(),e.restore()}function sm(r){var e=function(n,i,s){for(var o=n.atlasManager.getAtlasCollection(i),l=r.data.contexts[r.NODE],u=o.atlases,v=0;v<u.length;v++){var f=u[v],c=f.canvas;if(c){var h=c.width,d=c.height,y=h*v,g=c.height*s,p=.4;l.save(),l.scale(p,p),l.drawImage(c,y,g),l.strokeStyle="black",l.rect(y,g,h,d),l.stroke(),l.restore()}}},t=0;e(r.drawing,"node",t++),e(r.drawing,"label",t++)}function om(r,e,t,a,n){var i,s,o,l,u=po(r),v=u.pan,f=u.zoom;{var c=My(r,v,f,e,t),h=je(c,2),d=h[0],y=h[1],g=6;i=d-g/2,s=y-g/2,o=g,l=g}if(o===0||l===0)return[];var p=r.data.contexts[r.WEBGL];p.bindFramebuffer(p.FRAMEBUFFER,r.pickingFrameBuffer),r.pickingFrameBuffer.needsDraw&&(p.viewport(0,0,p.canvas.width,p.canvas.height),kf(r,null,ba.PICKING),r.pickingFrameBuffer.needsDraw=!1);var m=o*l,b=new Uint8Array(m*4);p.readPixels(i,s,o,l,p.RGBA,p.UNSIGNED_BYTE,b),p.bindFramebuffer(p.FRAMEBUFFER,null);for(var w=new Set,E=0;E<m;E++){var C=b.slice(E*4,E*4+4),x=Oy(C)-1;x>=0&&w.add(x)}return w}function um(r,e,t){var a=om(r,e,t),n=r.getCachedZSortedEles(),i,s,o=Tr(a),l;try{for(o.s();!(l=o.n()).done;){var u=l.value,v=n[u];if(!i&&v.isNode()&&(i=v),!s&&v.isEdge()&&(s=v),i&&s)break}}catch(f){o.e(f)}finally{o.f()}return[i,s].filter(Boolean)}function Ts(r,e,t){var a=r.drawing;e+=1,t.isNode()?(a.drawNode(t,e,"node-underlay"),a.drawNode(t,e,"node-body"),a.drawTexture(t,e,"label"),a.drawNode(t,e,"node-overlay")):(a.drawEdgeLine(t,e),a.drawEdgeArrow(t,e,"source"),a.drawEdgeArrow(t,e,"target"),a.drawTexture(t,e,"label"),a.drawTexture(t,e,"edge-source-label"),a.drawTexture(t,e,"edge-target-label"))}function kf(r,e,t){var a;r.webglDebug&&(a=performance.now());var n=r.drawing,i=0;if(t.screen&&r.data.canvasNeedsRedraw[r.SELECT_BOX]&&nm(r,e),r.data.canvasNeedsRedraw[r.NODE]||t.picking){var s=r.data.contexts[r.WEBGL];t.screen?(s.clearColor(0,0,0,0),s.enable(s.BLEND),s.blendFunc(s.ONE,s.ONE_MINUS_SRC_ALPHA)):s.disable(s.BLEND),s.clear(s.COLOR_BUFFER_BIT|s.DEPTH_BUFFER_BIT),s.viewport(0,0,s.canvas.width,s.canvas.height);var o=am(r),l=r.getCachedZSortedEles();if(i=l.length,n.startFrame(o,t),t.screen){for(var u=0;u<l.nondrag.length;u++)Ts(r,u,l.nondrag[u]);for(var v=0;v<l.drag.length;v++)Ts(r,v,l.drag[v])}else if(t.picking)for(var f=0;f<l.length;f++)Ts(r,f,l[f]);n.endFrame(),t.screen&&r.webglDebugShowAtlases&&(im(r),sm(r)),r.data.canvasNeedsRedraw[r.NODE]=!1,r.data.canvasNeedsRedraw[r.DRAG]=!1}if(r.webglDebug){var c=performance.now(),h=!1,d=Math.ceil(c-a),y=n.getDebugInfo(),g=["".concat(i," elements"),"".concat(y.totalInstances," instances"),"".concat(y.batchCount," batches"),"".concat(y.totalAtlases," atlases"),"".concat(y.wrappedCount," wrapped textures"),"".concat(y.simpleCount," simple shapes")].join(", ");if(h)console.log("WebGL (".concat(t.name,") - time ").concat(d,"ms, ").concat(g));else{console.log("WebGL (".concat(t.name,") - frame time ").concat(d,"ms")),console.log("Totals:"),console.log("  ".concat(g)),console.log("Texture Atlases Used:");var p=y.atlasInfo,m=Tr(p),b;try{for(m.s();!(b=m.n()).done;){var w=b.value;console.log("  ".concat(w.type,": ").concat(w.keyCount," keys, ").concat(w.atlasCount," atlases"))}}catch(E){m.e(E)}finally{m.f()}console.log("")}}r.data.gc&&(console.log("Garbage Collect!"),r.data.gc=!1,n.gc())}var ht={};ht.drawPolygonPath=function(r,e,t,a,n,i){var s=a/2,o=n/2;r.beginPath&&r.beginPath(),r.moveTo(e+s*i[0],t+o*i[1]);for(var l=1;l<i.length/2;l++)r.lineTo(e+s*i[l*2],t+o*i[l*2+1]);r.closePath()};ht.drawRoundPolygonPath=function(r,e,t,a,n,i,s){s.forEach(function(o){return uf(r,o)}),r.closePath()};ht.drawRoundRectanglePath=function(r,e,t,a,n,i){var s=a/2,o=n/2,l=i==="auto"?st(a,n):Math.min(i,o,s);r.beginPath&&r.beginPath(),r.moveTo(e,t-o),r.arcTo(e+s,t-o,e+s,t,l),r.arcTo(e+s,t+o,e,t+o,l),r.arcTo(e-s,t+o,e-s,t,l),r.arcTo(e-s,t-o,e,t-o,l),r.lineTo(e,t-o),r.closePath()};ht.drawBottomRoundRectanglePath=function(r,e,t,a,n,i){var s=a/2,o=n/2,l=i==="auto"?st(a,n):i;r.beginPath&&r.beginPath(),r.moveTo(e,t-o),r.lineTo(e+s,t-o),r.lineTo(e+s,t),r.arcTo(e+s,t+o,e,t+o,l),r.arcTo(e-s,t+o,e-s,t,l),r.lineTo(e-s,t-o),r.lineTo(e,t-o),r.closePath()};ht.drawCutRectanglePath=function(r,e,t,a,n,i,s){var o=a/2,l=n/2,u=s==="auto"?ro():s;r.beginPath&&r.beginPath(),r.moveTo(e-o+u,t-l),r.lineTo(e+o-u,t-l),r.lineTo(e+o,t-l+u),r.lineTo(e+o,t+l-u),r.lineTo(e+o-u,t+l),r.lineTo(e-o+u,t+l),r.lineTo(e-o,t+l-u),r.lineTo(e-o,t-l+u),r.closePath()};ht.drawBarrelPath=function(r,e,t,a,n){var i=a/2,s=n/2,o=e-i,l=e+i,u=t-s,v=t+s,f=ks(a,n),c=f.widthOffset,h=f.heightOffset,d=f.ctrlPtOffsetPct*c;r.beginPath&&r.beginPath(),r.moveTo(o,u+h),r.lineTo(o,v-h),r.quadraticCurveTo(o+d,v,o+c,v),r.lineTo(l-c,v),r.quadraticCurveTo(l-d,v,l,v-h),r.lineTo(l,u+h),r.quadraticCurveTo(l-d,u,l-c,u),r.lineTo(o+c,u),r.quadraticCurveTo(o+d,u,o,u+h),r.closePath()};var Hl=Math.sin(0),Wl=Math.cos(0),Gs={},Hs={},Bf=Math.PI/40;for(var Vt=0*Math.PI;Vt<2*Math.PI;Vt+=Bf)Gs[Vt]=Math.sin(Vt),Hs[Vt]=Math.cos(Vt);ht.drawEllipsePath=function(r,e,t,a,n){if(r.beginPath&&r.beginPath(),r.ellipse)r.ellipse(e,t,a/2,n/2,0,0,2*Math.PI);else for(var i,s,o=a/2,l=n/2,u=0*Math.PI;u<2*Math.PI;u+=Bf)i=e-o*Gs[u]*Hl+o*Hs[u]*Wl,s=t+l*Hs[u]*Hl+l*Gs[u]*Wl,u===0?r.moveTo(i,s):r.lineTo(i,s);r.closePath()};var _a={};_a.createBuffer=function(r,e){var t=document.createElement("canvas");return t.width=r,t.height=e,[t,t.getContext("2d")]};_a.bufferCanvasImage=function(r){var e=this.cy,t=e.mutableElements(),a=t.boundingBox(),n=this.findContainerClientCoords(),i=r.full?Math.ceil(a.w):n[2],s=r.full?Math.ceil(a.h):n[3],o=te(r.maxWidth)||te(r.maxHeight),l=this.getPixelRatio(),u=1;if(r.scale!==void 0)i*=r.scale,s*=r.scale,u=r.scale;else if(o){var v=1/0,f=1/0;te(r.maxWidth)&&(v=u*r.maxWidth/i),te(r.maxHeight)&&(f=u*r.maxHeight/s),u=Math.min(v,f),i*=u,s*=u}o||(i*=l,s*=l,u*=l);var c=document.createElement("canvas");c.width=i,c.height=s,c.style.width=i+"px",c.style.height=s+"px";var h=c.getContext("2d");if(i>0&&s>0){h.clearRect(0,0,i,s),h.globalCompositeOperation="source-over";var d=this.getCachedZSortedEles();if(r.full)h.translate(-a.x1*u,-a.y1*u),h.scale(u,u),this.drawElements(h,d),h.scale(1/u,1/u),h.translate(a.x1*u,a.y1*u);else{var y=e.pan(),g={x:y.x*u,y:y.y*u};u*=e.zoom(),h.translate(g.x,g.y),h.scale(u,u),this.drawElements(h,d),h.scale(1/u,1/u),h.translate(-g.x,-g.y)}r.bg&&(h.globalCompositeOperation="destination-over",h.fillStyle=r.bg,h.rect(0,0,i,s),h.fill())}return c};function lm(r,e){for(var t=atob(r),a=new ArrayBuffer(t.length),n=new Uint8Array(a),i=0;i<t.length;i++)n[i]=t.charCodeAt(i);return new Blob([a],{type:e})}function Ul(r){var e=r.indexOf(",");return r.substr(e+1)}function Pf(r,e,t){var a=function(){return e.toDataURL(t,r.quality)};switch(r.output){case"blob-promise":return new ea(function(n,i){try{e.toBlob(function(s){s!=null?n(s):i(new Error("`canvas.toBlob()` sent a null value in its callback"))},t,r.quality)}catch(s){i(s)}});case"blob":return lm(Ul(a()),t);case"base64":return Ul(a());case"base64uri":default:return a()}}_a.png=function(r){return Pf(r,this.bufferCanvasImage(r),"image/png")};_a.jpg=function(r){return Pf(r,this.bufferCanvasImage(r),"image/jpeg")};var Af={};Af.nodeShapeImpl=function(r,e,t,a,n,i,s,o){switch(r){case"ellipse":return this.drawEllipsePath(e,t,a,n,i);case"polygon":return this.drawPolygonPath(e,t,a,n,i,s);case"round-polygon":return this.drawRoundPolygonPath(e,t,a,n,i,s,o);case"roundrectangle":case"round-rectangle":return this.drawRoundRectanglePath(e,t,a,n,i,o);case"cutrectangle":case"cut-rectangle":return this.drawCutRectanglePath(e,t,a,n,i,s,o);case"bottomroundrectangle":case"bottom-round-rectangle":return this.drawBottomRoundRectanglePath(e,t,a,n,i,o);case"barrel":return this.drawBarrelPath(e,t,a,n,i)}};var vm=Rf,Te=Rf.prototype;Te.CANVAS_LAYERS=3;Te.SELECT_BOX=0;Te.DRAG=1;Te.NODE=2;Te.WEBGL=3;Te.CANVAS_TYPES=["2d","2d","2d","webgl2"];Te.BUFFER_COUNT=3;Te.TEXTURE_BUFFER=0;Te.MOTIONBLUR_BUFFER_NODE=1;Te.MOTIONBLUR_BUFFER_DRAG=2;function Rf(r){var e=this,t=e.cy.window(),a=t.document;r.webgl&&(Te.CANVAS_LAYERS=e.CANVAS_LAYERS=4,console.log("webgl rendering enabled")),e.data={canvases:new Array(Te.CANVAS_LAYERS),contexts:new Array(Te.CANVAS_LAYERS),canvasNeedsRedraw:new Array(Te.CANVAS_LAYERS),bufferCanvases:new Array(Te.BUFFER_COUNT),bufferContexts:new Array(Te.CANVAS_LAYERS)};var n="-webkit-tap-highlight-color",i="rgba(0,0,0,0)";e.data.canvasContainer=a.createElement("div");var s=e.data.canvasContainer.style;e.data.canvasContainer.style[n]=i,s.position="relative",s.zIndex="0",s.overflow="hidden";var o=r.cy.container();o.appendChild(e.data.canvasContainer),o.style[n]=i;var l={"-webkit-user-select":"none","-moz-user-select":"-moz-none","user-select":"none","-webkit-tap-highlight-color":"rgba(0,0,0,0)","outline-style":"none"};uc()&&(l["-ms-touch-action"]="none",l["touch-action"]="none");for(var u=0;u<Te.CANVAS_LAYERS;u++){var v=e.data.canvases[u]=a.createElement("canvas"),f=Te.CANVAS_TYPES[u];e.data.contexts[u]=v.getContext(f),e.data.contexts[u]||He("Could not create canvas of type "+f),Object.keys(l).forEach(function(J){v.style[J]=l[J]}),v.style.position="absolute",v.setAttribute("data-id","layer"+u),v.style.zIndex=String(Te.CANVAS_LAYERS-u),e.data.canvasContainer.appendChild(v),e.data.canvasNeedsRedraw[u]=!1}e.data.topCanvas=e.data.canvases[0],e.data.canvases[Te.NODE].setAttribute("data-id","layer"+Te.NODE+"-node"),e.data.canvases[Te.SELECT_BOX].setAttribute("data-id","layer"+Te.SELECT_BOX+"-selectbox"),e.data.canvases[Te.DRAG].setAttribute("data-id","layer"+Te.DRAG+"-drag"),e.data.canvases[Te.WEBGL]&&e.data.canvases[Te.WEBGL].setAttribute("data-id","layer"+Te.WEBGL+"-webgl");for(var u=0;u<Te.BUFFER_COUNT;u++)e.data.bufferCanvases[u]=a.createElement("canvas"),e.data.bufferContexts[u]=e.data.bufferCanvases[u].getContext("2d"),e.data.bufferCanvases[u].style.position="absolute",e.data.bufferCanvases[u].setAttribute("data-id","buffer"+u),e.data.bufferCanvases[u].style.zIndex=String(-u-1),e.data.bufferCanvases[u].style.visibility="hidden";e.pathsEnabled=!0;var c=Sr(),h=function(z){return{x:(z.x1+z.x2)/2,y:(z.y1+z.y2)/2}},d=function(z){return{x:-z.w/2,y:-z.h/2}},y=function(z){var q=z[0]._private,H=q.oldBackgroundTimestamp===q.backgroundTimestamp;return!H},g=function(z){return z[0]._private.nodeKey},p=function(z){return z[0]._private.labelStyleKey},m=function(z){return z[0]._private.sourceLabelStyleKey},b=function(z){return z[0]._private.targetLabelStyleKey},w=function(z,q,H,ee,ne){return e.drawElement(z,q,H,!1,!1,ne)},E=function(z,q,H,ee,ne){return e.drawElementText(z,q,H,ee,"main",ne)},C=function(z,q,H,ee,ne){return e.drawElementText(z,q,H,ee,"source",ne)},x=function(z,q,H,ee,ne){return e.drawElementText(z,q,H,ee,"target",ne)},k=function(z){return z.boundingBox(),z[0]._private.bodyBounds},S=function(z){return z.boundingBox(),z[0]._private.labelBounds.main||c},P=function(z){return z.boundingBox(),z[0]._private.labelBounds.source||c},D=function(z){return z.boundingBox(),z[0]._private.labelBounds.target||c},A=function(z,q){return q},B=function(z){return h(k(z))},R=function(z,q,H){var ee=z?z+"-":"";return{x:q.x+H.pstyle(ee+"text-margin-x").pfValue,y:q.y+H.pstyle(ee+"text-margin-y").pfValue}},M=function(z,q,H){var ee=z[0]._private.rscratch;return{x:ee[q],y:ee[H]}},I=function(z){return R("",M(z,"labelX","labelY"),z)},L=function(z){return R("source",M(z,"sourceLabelX","sourceLabelY"),z)},O=function(z){return R("target",M(z,"targetLabelX","targetLabelY"),z)},V=function(z){return d(k(z))},G=function(z){return d(P(z))},N=function(z){return d(D(z))},F=function(z){var q=S(z),H=d(S(z));if(z.isNode()){switch(z.pstyle("text-halign").value){case"left":H.x=-q.w-(q.leftPad||0);break;case"right":H.x=-(q.rightPad||0);break}switch(z.pstyle("text-valign").value){case"top":H.y=-q.h-(q.topPad||0);break;case"bottom":H.y=-(q.botPad||0);break}}return H},K=e.data.eleTxrCache=new pa(e,{getKey:g,doesEleInvalidateKey:y,drawElement:w,getBoundingBox:k,getRotationPoint:B,getRotationOffset:V,allowEdgeTxrCaching:!1,allowParentTxrCaching:!1}),X=e.data.lblTxrCache=new pa(e,{getKey:p,drawElement:E,getBoundingBox:S,getRotationPoint:I,getRotationOffset:F,isVisible:A}),Q=e.data.slbTxrCache=new pa(e,{getKey:m,drawElement:C,getBoundingBox:P,getRotationPoint:L,getRotationOffset:G,isVisible:A}),Z=e.data.tlbTxrCache=new pa(e,{getKey:b,drawElement:x,getBoundingBox:D,getRotationPoint:O,getRotationOffset:N,isVisible:A}),re=e.data.lyrTxrCache=new mf(e);e.onUpdateEleCalcs(function(z,q){K.invalidateElements(q),X.invalidateElements(q),Q.invalidateElements(q),Z.invalidateElements(q),re.invalidateElements(q);for(var H=0;H<q.length;H++){var ee=q[H]._private;ee.oldBackgroundTimestamp=ee.backgroundTimestamp}});var ae=function(z){for(var q=0;q<z.length;q++)re.enqueueElementRefinement(z[q].ele)};K.onDequeue(ae),X.onDequeue(ae),Q.onDequeue(ae),Z.onDequeue(ae),r.webgl&&e.initWebgl(r,{getStyleKey:g,getLabelKey:p,getSourceLabelKey:m,getTargetLabelKey:b,drawElement:w,drawLabel:E,drawSourceLabel:C,drawTargetLabel:x,getElementBox:k,getLabelBox:S,getSourceLabelBox:P,getTargetLabelBox:D,getElementRotationPoint:B,getElementRotationOffset:V,getLabelRotationPoint:I,getSourceLabelRotationPoint:L,getTargetLabelRotationPoint:O,getLabelRotationOffset:F,getSourceLabelRotationOffset:G,getTargetLabelRotationOffset:N})}Te.redrawHint=function(r,e){var t=this;switch(r){case"eles":t.data.canvasNeedsRedraw[Te.NODE]=e;break;case"drag":t.data.canvasNeedsRedraw[Te.DRAG]=e;break;case"select":t.data.canvasNeedsRedraw[Te.SELECT_BOX]=e;break;case"gc":t.data.gc=!0;break}};var fm=typeof Path2D<"u";Te.path2dEnabled=function(r){if(r===void 0)return this.pathsEnabled;this.pathsEnabled=!!r};Te.usePaths=function(){return fm&&this.pathsEnabled};Te.setImgSmoothing=function(r,e){r.imageSmoothingEnabled!=null?r.imageSmoothingEnabled=e:(r.webkitImageSmoothingEnabled=e,r.mozImageSmoothingEnabled=e,r.msImageSmoothingEnabled=e)};Te.getImgSmoothing=function(r){return r.imageSmoothingEnabled!=null?r.imageSmoothingEnabled:r.webkitImageSmoothingEnabled||r.mozImageSmoothingEnabled||r.msImageSmoothingEnabled};Te.makeOffscreenCanvas=function(r,e){var t;if((typeof OffscreenCanvas>"u"?"undefined":rr(OffscreenCanvas))!=="undefined")t=new OffscreenCanvas(r,e);else{var a=this.cy.window(),n=a.document;t=n.createElement("canvas"),t.width=r,t.height=e}return t};[bf,Hr,Qr,go,At,dt,mr,Tf,ht,_a,Af].forEach(function(r){he(Te,r)});var cm=[{name:"null",impl:nf},{name:"base",impl:gf},{name:"canvas",impl:vm}],dm=[{type:"layout",extensions:Vp},{type:"renderer",extensions:cm}],Mf={},Lf={};function If(r,e,t){var a=t,n=function(k){Le("Can not register `"+e+"` for `"+r+"` since `"+k+"` already exists in the prototype and can not be overridden")};if(r==="core"){if(Ba.prototype[e])return n(e);Ba.prototype[e]=t}else if(r==="collection"){if(vr.prototype[e])return n(e);vr.prototype[e]=t}else if(r==="layout"){for(var i=function(k){this.options=k,t.call(this,k),Pe(this._private)||(this._private={}),this._private.cy=k.cy,this._private.listeners=[],this.createEmitter()},s=i.prototype=Object.create(t.prototype),o=[],l=0;l<o.length;l++){var u=o[l];s[u]=s[u]||function(){return this}}s.start&&!s.run?s.run=function(){return this.start(),this}:!s.start&&s.run&&(s.start=function(){return this.run(),this});var v=t.prototype.stop;s.stop=function(){var x=this.options;if(x&&x.animate){var k=this.animations;if(k)for(var S=0;S<k.length;S++)k[S].stop()}return v?v.call(this):this.emit("layoutstop"),this},s.destroy||(s.destroy=function(){return this}),s.cy=function(){return this._private.cy};var f=function(k){return k._private.cy},c={addEventFields:function(k,S){S.layout=k,S.cy=f(k),S.target=k},bubble:function(){return!0},parent:function(k){return f(k)}};he(s,{createEmitter:function(){return this._private.emitter=new qn(c,this),this},emitter:function(){return this._private.emitter},on:function(k,S){return this.emitter().on(k,S),this},one:function(k,S){return this.emitter().one(k,S),this},once:function(k,S){return this.emitter().one(k,S),this},removeListener:function(k,S){return this.emitter().removeListener(k,S),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},emit:function(k,S){return this.emitter().emit(k,S),this}}),Me.eventAliasesOn(s),a=i}else if(r==="renderer"&&e!=="null"&&e!=="base"){var h=Of("renderer","base"),d=h.prototype,y=t,g=t.prototype,p=function(){h.apply(this,arguments),y.apply(this,arguments)},m=p.prototype;for(var b in d){var w=d[b],E=g[b]!=null;if(E)return n(b);m[b]=w}for(var C in g)m[C]=g[C];d.clientFunctions.forEach(function(x){m[x]=m[x]||function(){He("Renderer does not implement `renderer."+x+"()` on its prototype")}}),a=p}else if(r==="__proto__"||r==="constructor"||r==="prototype")return He(r+" is an illegal type to be registered, possibly lead to prototype pollutions");return ev({map:Mf,keys:[r,e],value:a})}function Of(r,e){return rv({map:Mf,keys:[r,e]})}function hm(r,e,t,a,n){return ev({map:Lf,keys:[r,e,t,a],value:n})}function gm(r,e,t,a){return rv({map:Lf,keys:[r,e,t,a]})}var Ws=function(){if(arguments.length===2)return Of.apply(null,arguments);if(arguments.length===3)return If.apply(null,arguments);if(arguments.length===4)return gm.apply(null,arguments);if(arguments.length===5)return hm.apply(null,arguments);He("Invalid extension access syntax")};Ba.prototype.extension=Ws;dm.forEach(function(r){r.extensions.forEach(function(e){If(r.type,e.name,e.impl)})});var Pn=function(){if(!(this instanceof Pn))return new Pn;this.length=0},Bt=Pn.prototype;Bt.instanceString=function(){return"stylesheet"};Bt.selector=function(r){var e=this.length++;return this[e]={selector:r,properties:[]},this};Bt.css=function(r,e){var t=this.length-1;if(fe(r))this[t].properties.push({name:r,value:e});else if(Pe(r))for(var a=r,n=Object.keys(a),i=0;i<n.length;i++){var s=n[i],o=a[s];if(o!=null){var l=ir.properties[s]||ir.properties[An(s)];if(l!=null){var u=l.name,v=o;this[t].properties.push({name:u,value:v})}}}return this};Bt.style=Bt.css;Bt.generateStyle=function(r){var e=new ir(r);return this.appendToStyle(e)};Bt.appendToStyle=function(r){for(var e=0;e<this.length;e++){var t=this[e],a=t.selector,n=t.properties;r.selector(a);for(var i=0;i<n.length;i++){var s=n[i];r.css(s.name,s.value)}}return r};var pm="3.32.0",Jt=function(e){if(e===void 0&&(e={}),Pe(e))return new Ba(e);if(fe(e))return Ws.apply(Ws,arguments)};Jt.use=function(r){var e=Array.prototype.slice.call(arguments,1);return e.unshift(Jt),r.apply(null,e),this};Jt.warnings=function(r){return uv(r)};Jt.version=pm;Jt.stylesheet=Jt.Stylesheet=Pn;export{Jt as c};
