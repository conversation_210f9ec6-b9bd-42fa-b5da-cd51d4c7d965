import {
  __name
} from "./chunk-AEQEJSV4.js";

// node_modules/mermaid/dist/chunks/mermaid.core/chunk-K557N5IZ.mjs
var getSubGraphTitleMargins = __name(({
  flowchart
}) => {
  var _a, _b;
  const subGraphTitleTopMargin = ((_a = flowchart == null ? void 0 : flowchart.subGraphTitleMargin) == null ? void 0 : _a.top) ?? 0;
  const subGraphTitleBottomMargin = ((_b = flowchart == null ? void 0 : flowchart.subGraphTitleMargin) == null ? void 0 : _b.bottom) ?? 0;
  const subGraphTitleTotalMargin = subGraphTitleTopMargin + subGraphTitleBottomMargin;
  return {
    subGraphTitleTopMargin,
    subGraphTitleBottomMargin,
    subGraphTitleTotalMargin
  };
}, "getSubGraphTitleMargins");

export {
  getSubGraphTitleMargins
};
//# sourceMappingURL=chunk-F5VURT3L.js.map
