#!/usr/bin/env node

/**
 * 🚀 CRÉATEUR EXE SIMPLE WEMA IA
 * Méthode simple avec PowerShell et conversion EXE
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 CRÉATEUR EXE SIMPLE WEMA IA');
console.log('MÉTHODE SIMPLE ET EFFICACE');
console.log('='.repeat(50));

// 1. CRÉER SCRIPT POWERSHELL COMPLET
console.log('🔧 Création script PowerShell...');

const psScript = `
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.IO.Compression.FileSystem

# Interface utilisateur
[System.Windows.Forms.Application]::EnableVisualStyles()

# Afficher message d'installation
$result = [System.Windows.Forms.MessageBox]::Show(
    "Installer WeMa IA - Assistant IA Professionnel ?\\n\\nL'application sera installée dans votre dossier utilisateur et lancée automatiquement.",
    "WeMa IA - Installation",
    [System.Windows.Forms.MessageBoxButtons]::YesNo,
    [System.Windows.Forms.MessageBoxIcon]::Question
)

if ($result -eq [System.Windows.Forms.DialogResult]::No) {
    exit 0
}

# Définir répertoires
$installDir = "$env:APPDATA\\WeMa_IA"
$appExe = "$installDir\\WeMa IA.exe"

# Vérifier si déjà installé
if (Test-Path $appExe) {
    $launch = [System.Windows.Forms.MessageBox]::Show(
        "WeMa IA est déjà installé !\\n\\nVoulez-vous le lancer ?",
        "WeMa IA",
        [System.Windows.Forms.MessageBoxButtons]::YesNo,
        [System.Windows.Forms.MessageBoxIcon]::Information
    )
    
    if ($launch -eq [System.Windows.Forms.DialogResult]::Yes) {
        Start-Process $appExe
    }
    exit 0
}

# Afficher progression
$progressForm = New-Object System.Windows.Forms.Form
$progressForm.Text = "Installation WeMa IA"
$progressForm.Size = New-Object System.Drawing.Size(400, 150)
$progressForm.StartPosition = "CenterScreen"
$progressForm.FormBorderStyle = "FixedDialog"
$progressForm.MaximizeBox = $false
$progressForm.MinimizeBox = $false

$progressLabel = New-Object System.Windows.Forms.Label
$progressLabel.Text = "Installation en cours..."
$progressLabel.Location = New-Object System.Drawing.Point(20, 20)
$progressLabel.Size = New-Object System.Drawing.Size(350, 30)
$progressForm.Controls.Add($progressLabel)

$progressBar = New-Object System.Windows.Forms.ProgressBar
$progressBar.Location = New-Object System.Drawing.Point(20, 60)
$progressBar.Size = New-Object System.Drawing.Size(350, 30)
$progressBar.Style = "Continuous"
$progressForm.Controls.Add($progressBar)

$progressForm.Show()
$progressForm.Refresh()

try {
    # Étape 1: Créer répertoire
    $progressLabel.Text = "Création du répertoire d'installation..."
    $progressBar.Value = 20
    $progressForm.Refresh()
    
    if (-not (Test-Path $installDir)) {
        New-Item -ItemType Directory -Path $installDir -Force | Out-Null
    }
    
    Start-Sleep -Milliseconds 500
    
    # Étape 2: Extraire l'application
    $progressLabel.Text = "Extraction de l'application..."
    $progressBar.Value = 40
    $progressForm.Refresh()
    
    $zipPath = "$PSScriptRoot\\wema_app.zip"
    if (Test-Path $zipPath) {
        [System.IO.Compression.ZipFile]::ExtractToDirectory($zipPath, $installDir)
    } else {
        throw "Archive d'installation non trouvée"
    }
    
    $progressBar.Value = 70
    $progressForm.Refresh()
    Start-Sleep -Milliseconds 500
    
    # Étape 3: Créer raccourcis
    $progressLabel.Text = "Création des raccourcis..."
    $progressBar.Value = 85
    $progressForm.Refresh()
    
    # Raccourci bureau
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut("$env:USERPROFILE\\Desktop\\WeMa IA.lnk")
    $Shortcut.TargetPath = $appExe
    $Shortcut.WorkingDirectory = $installDir
    $Shortcut.IconLocation = $appExe
    $Shortcut.Description = "WeMa IA - Assistant IA Professionnel"
    $Shortcut.Save()
    
    # Raccourci menu démarrer
    $startMenuDir = "$env:APPDATA\\Microsoft\\Windows\\Start Menu\\Programs"
    $startShortcut = $WshShell.CreateShortcut("$startMenuDir\\WeMa IA.lnk")
    $startShortcut.TargetPath = $appExe
    $startShortcut.WorkingDirectory = $installDir
    $startShortcut.IconLocation = $appExe
    $startShortcut.Description = "WeMa IA - Assistant IA Professionnel"
    $startShortcut.Save()
    
    $progressBar.Value = 100
    $progressForm.Refresh()
    Start-Sleep -Milliseconds 500
    
    $progressForm.Close()
    
    # Message de succès
    $success = [System.Windows.Forms.MessageBox]::Show(
        "WeMa IA installé avec succès !\\n\\n✅ Raccourci créé sur le bureau\\n✅ Raccourci ajouté au menu démarrer\\n\\nLancer WeMa IA maintenant ?",
        "Installation terminée",
        [System.Windows.Forms.MessageBoxButtons]::YesNo,
        [System.Windows.Forms.MessageBoxIcon]::Information
    )
    
    if ($success -eq [System.Windows.Forms.DialogResult]::Yes) {
        Start-Process $appExe
    }
    
} catch {
    $progressForm.Close()
    [System.Windows.Forms.MessageBox]::Show(
        "Erreur lors de l'installation :\\n\\n$($_.Exception.Message)",
        "Erreur",
        [System.Windows.Forms.MessageBoxButtons]::OK,
        [System.Windows.Forms.MessageBoxIcon]::Error
    )
}
`;

fs.writeFileSync('WeMa_IA_Installer.ps1', psScript);
console.log('✅ Script PowerShell créé');

// 2. INSTALLER PS2EXE SI NÉCESSAIRE
console.log('📦 Installation ps2exe...');

try {
    execSync('powershell -Command "if (-not (Get-Module -ListAvailable -Name ps2exe)) { Install-Module ps2exe -Force -Scope CurrentUser }"', { stdio: 'inherit' });
    console.log('✅ ps2exe installé');
} catch (e) {
    console.log('⚠️ Erreur installation ps2exe, méthode alternative...');
}

// 3. CONVERTIR EN EXE
console.log('🔨 Conversion PowerShell vers EXE...');

const outputExe = path.join(require('os').homedir(), 'Downloads', 'WeMa_IA_Installer.exe');

try {
    // Méthode 1: ps2exe
    execSync(`powershell -Command "ps2exe -inputFile 'WeMa_IA_Installer.ps1' -outputFile '${outputExe}' -iconFile 'app\\resources\\wema-icon-256.ico' -title 'WeMa IA Installer' -description 'WeMa IA - Assistant IA Professionnel' -company 'WeMa IA Team' -version '0.1.2' -noConsole"`, { stdio: 'inherit' });
    console.log('✅ EXE créé avec ps2exe');
} catch (e) {
    console.log('⚠️ ps2exe échoué, méthode alternative...');
    
    // Méthode 2: Créer un batch qui lance PowerShell
    const batchWrapper = `@echo off
title WeMa IA - Installation
powershell -WindowStyle Hidden -ExecutionPolicy Bypass -File "%~dp0WeMa_IA_Installer.ps1"
`;
    
    const batchPath = path.join(require('os').homedir(), 'Downloads', 'WeMa_IA_Installer.bat');
    fs.writeFileSync(batchPath, batchWrapper);
    
    // Copier aussi le script PowerShell
    const psPath = path.join(require('os').homedir(), 'Downloads', 'WeMa_IA_Installer.ps1');
    fs.copyFileSync('WeMa_IA_Installer.ps1', psPath);
    
    console.log('✅ Installateur batch créé');
    console.log(`📍 Fichiers: ${batchPath} + ${psPath}`);
}

// 4. COPIER L'ARCHIVE
console.log('📦 Copie archive application...');

const zipSource = 'wema_app.zip';
const zipDest = path.join(require('os').homedir(), 'Downloads', 'wema_app.zip');

if (fs.existsSync(zipSource)) {
    fs.copyFileSync(zipSource, zipDest);
    console.log('✅ Archive copiée');
} else {
    console.log('❌ Archive non trouvée');
}

// 5. VÉRIFICATION FINALE
console.log('');
console.log('🎉 INSTALLATEUR EXE CRÉÉ !');
console.log('='.repeat(50));

if (fs.existsSync(outputExe)) {
    const exeSize = fs.statSync(outputExe).size;
    const exeSizeMB = (exeSize / 1024 / 1024).toFixed(2);
    
    console.log(`📦 EXE: WeMa_IA_Installer.exe (${exeSizeMB} MB)`);
    console.log(`📦 Archive: wema_app.zip (549 MB)`);
    console.log('');
    console.log('👥 UTILISATION UTILISATEUR:');
    console.log('1. Donnez les 2 fichiers ensemble');
    console.log('2. Double-clic sur WeMa_IA_Installer.exe');
    console.log('3. Interface graphique d\'installation');
    console.log('4. Installation et lancement automatiques');
    
} else {
    console.log('📦 Installateur: WeMa_IA_Installer.bat');
    console.log('📦 Script: WeMa_IA_Installer.ps1');
    console.log('📦 Archive: wema_app.zip');
    console.log('');
    console.log('👥 UTILISATION UTILISATEUR:');
    console.log('1. Donnez les 3 fichiers ensemble');
    console.log('2. Double-clic sur WeMa_IA_Installer.bat');
    console.log('3. Installation automatique');
}

console.log('');
console.log('✅ SOLUTION PROFESSIONNELLE !');
console.log('🛡️ Interface graphique moderne');
console.log('📤 Facilement distribuable');
