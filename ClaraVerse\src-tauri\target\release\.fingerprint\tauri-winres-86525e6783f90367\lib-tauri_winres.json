{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 2086567024422996381, "profile": 1369601567987815722, "path": 14853136952707538089, "deps": [[6493259146304816786, "indexmap", false, 10022660878463869370], [6941104557053927479, "embed_resource", false, 9387198286902126340], [15609422047640926750, "toml", false, 12347453665498926721]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-winres-86525e6783f90367\\dep-lib-tauri_winres", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}