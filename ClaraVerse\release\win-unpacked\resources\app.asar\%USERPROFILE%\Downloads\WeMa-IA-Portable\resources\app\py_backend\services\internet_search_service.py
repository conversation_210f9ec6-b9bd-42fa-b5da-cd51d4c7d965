"""
🔍 Internet Search Service - Backend Python
Service de recherche internet via SearXNG pour WeMa IA
"""

import asyncio
import aiohttp
import json
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from urllib.parse import urlencode

logger = logging.getLogger(__name__)

@dataclass
class SearchResult:
    title: str
    url: str
    content: str
    snippet: Optional[str] = None
    published_date: Optional[str] = None
    engine: Optional[str] = None
    category: Optional[str] = None
    score: Optional[float] = None

@dataclass
class SearchResponse:
    query: str
    results: List[SearchResult]
    suggestions: List[str]
    total_results: Optional[int] = None
    search_time: Optional[float] = None
    engines: List[str] = None
    error: Optional[str] = None

class InternetSearchService:
    """Service de recherche internet via SearXNG"""
    
    def __init__(self, searxng_url: str = "http://***********:8888"):
        self.searxng_url = searxng_url.rstrip('/')
        self.session: Optional[aiohttp.ClientSession] = None
        self.is_available = False
        
    async def __aenter__(self):
        """Gestionnaire de contexte async"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=20),  # 20 secondes selon recommandations dev
            headers={'User-Agent': 'WeMa-IA/1.0'}
        )
        await self.check_availability()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Fermeture de la session"""
        if self.session:
            await self.session.close()
            
    async def check_availability(self) -> bool:
        """Vérifier si SearXNG est disponible"""
        try:
            if not self.session:
                return False
                
            async with self.session.get(
                f"{self.searxng_url}/search",
                params={'q': 'test', 'format': 'json'},
                timeout=aiohttp.ClientTimeout(total=3)
            ) as response:
                self.is_available = response.status == 200
                logger.info(f"🔍 SearXNG {'disponible' if self.is_available else 'indisponible'} sur {self.searxng_url}")
                return self.is_available
                
        except Exception as e:
            self.is_available = False
            logger.warning(f"🔍 SearXNG non disponible: {e}")
            return False
            
    async def search(
        self,
        query: str,
        search_type: str = "general",
        max_results: int = 8,
        time_range: Optional[str] = None,
        language: str = "fr"
    ) -> SearchResponse:
        """Effectuer une recherche internet"""
        
        if not self.session:
            return SearchResponse(
                query=query,
                results=[],
                suggestions=[],
                error="Session non initialisée"
            )
            
        if not self.is_available:
            await self.check_availability()
            if not self.is_available:
                return SearchResponse(
                    query=query,
                    results=[],
                    suggestions=[],
                    error="SearXNG service non disponible"
                )
        
        try:
            # Paramètres de recherche
            params = {
                'q': query,
                'format': 'json',
                'language': language,
                'safesearch': '1',
                'pageno': '1'
            }
            
            # Ajouter les catégories selon le type
            if search_type == "news":
                params['categories'] = 'news'
                if time_range:
                    params['time_range'] = time_range
            elif search_type == "scientific":
                params['categories'] = 'science'
            elif search_type == "technical":
                params['categories'] = 'it'
            else:
                params['categories'] = 'general'
                
            logger.info(f"🔍 Recherche {search_type}: '{query}'")
            
            # Effectuer la requête
            async with self.session.get(
                f"{self.searxng_url}/search",
                params=params
            ) as response:
                
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"❌ Erreur SearXNG {response.status}: {error_text}")
                    return SearchResponse(
                        query=query,
                        results=[],
                        suggestions=[],
                        error=f"Erreur SearXNG: {response.status}"
                    )
                
                data = await response.json()
                
                # Traiter les résultats
                results = []
                for result_data in (data.get('results', []))[:max_results]:
                    result = SearchResult(
                        title=result_data.get('title', ''),
                        url=result_data.get('url', ''),
                        content=result_data.get('content', result_data.get('snippet', '')),
                        snippet=result_data.get('snippet'),
                        published_date=result_data.get('publishedDate'),
                        engine=result_data.get('engine'),
                        category=result_data.get('category'),
                        score=result_data.get('score')
                    )
                    results.append(result)
                
                logger.info(f"✅ Recherche terminée: {len(results)} résultats")
                
                return SearchResponse(
                    query=query,
                    results=results,
                    suggestions=data.get('suggestions', []),
                    total_results=len(results),  # BUG: number_of_results retourne toujours 0, on utilise len(results)
                    search_time=None,  # SearXNG ne fournit pas ce timing
                    engines=data.get('engines', [])
                )
                
        except asyncio.TimeoutError:
            logger.error("❌ Timeout lors de la recherche internet")
            return SearchResponse(
                query=query,
                results=[],
                suggestions=[],
                error="Timeout de recherche"
            )
        except Exception as e:
            logger.error(f"❌ Erreur recherche internet: {e}")
            return SearchResponse(
                query=query,
                results=[],
                suggestions=[],
                error=f"Erreur inattendue: {str(e)}"
            )
    
    async def quick_search(self, query: str) -> str:
        """Recherche rapide avec résultats formatés"""
        response = await self.search(query, max_results=5)
        
        if response.error:
            return f"❌ Erreur de recherche: {response.error}"
            
        if not response.results:
            return f"🔍 Aucun résultat trouvé pour: '{query}'"
            
        # Formater les résultats
        formatted = f"🔍 **Résultats de recherche pour: '{query}'**\n\n"
        
        for i, result in enumerate(response.results, 1):
            formatted += f"**{i}. {result.title}**\n"
            if result.content:
                content = result.content[:200] + "..." if len(result.content) > 200 else result.content
                formatted += f"{content}\n"
            formatted += f"🔗 {result.url}\n\n"
            
        formatted += f"_Recherche effectuée via SearXNG - {len(response.results)} résultat(s)_"
        return formatted
    
    def format_for_context(self, response: SearchResponse) -> str:
        """Formater les résultats pour le contexte de l'IA avec limitation de taille"""
        if response.error:
            return f"[RECHERCHE ERREUR] {response.error}"

        if not response.results:
            return f"[RECHERCHE] Aucun résultat pour '{response.query}'"

        # 🚨 LIMITATION CONTEXTE : Limiter le nombre de résultats et la taille
        max_results = min(3, len(response.results))  # Maximum 3 résultats au lieu de 5
        max_context_size = 2000  # Limite totale de 2000 caractères

        context = f"[RECHERCHE INTERNET] '{response.query}' - {max_results} résultat(s) (limité pour optimiser le contexte):\n\n"
        current_size = len(context)

        for i, result in enumerate(response.results[:max_results], 1):
            result_text = f"{i}. {result.title}\n"
            if result.content:
                # Réduire encore plus la taille des snippets
                snippet = result.content[:100] + "..." if len(result.content) > 100 else result.content
                result_text += f"   {snippet}\n"
            result_text += f"   Source: {result.url}\n\n"

            # Vérifier si on dépasse la limite
            if current_size + len(result_text) > max_context_size:
                context += f"... (résultats tronqués pour éviter surcharge contexte)\n"
                break

            context += result_text
            current_size += len(result_text)

        return context

# Instance globale
internet_search_service = InternetSearchService()

async def search_internet(
    query: str,
    search_type: str = "general",
    max_results: int = 6
) -> SearchResponse:
    """Fonction utilitaire pour effectuer une recherche"""
    async with InternetSearchService() as service:
        return await service.search(query, search_type, max_results)

async def quick_internet_search(query: str) -> str:
    """Fonction utilitaire pour une recherche rapide"""
    async with InternetSearchService() as service:
        return await service.quick_search(query)
