use std::process::Command;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  tauri::Builder::default()
    .setup(|app| {
      if cfg!(debug_assertions) {
        app.handle().plugin(
          tauri_plugin_log::Builder::default()
            .level(log::LevelFilter::Info)
            .build(),
        )?;
      }

      // Démarrer le backend Python complet
      std::thread::spawn(|| {
        let exe_path = std::env::current_exe().unwrap();
        let exe_dir = exe_path.parent().unwrap();

        // Chercher le script de démarrage backend
        let backend_script = exe_dir.join("start_backend.bat");

        if backend_script.exists() {
          println!("🚀 Démarrage du backend Python complet...");
          let _ = Command::new("cmd")
            .args(&["/C", "start_backend.bat"])
            .current_dir(&exe_dir)
            .spawn();
        } else {
          // Fallback : démarrer directement depuis py_backend
          let backend_dir = exe_dir.join("py_backend");
          if backend_dir.exists() && backend_dir.join("main.py").exists() {
            println!("🚀 Démarrage backend Python (fallback)...");
            let _ = Command::new("python")
              .arg("main.py")
              .current_dir(&backend_dir)
              .spawn();
          }
        }
      });

      Ok(())
    })
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}
