use std::process::Command;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  tauri::Builder::default()
    .setup(|app| {
      if cfg!(debug_assertions) {
        app.handle().plugin(
          tauri_plugin_log::Builder::default()
            .level(log::LevelFilter::Info)
            .build(),
        )?;
      }

      // Démarrer le backend Python complet
      std::thread::spawn(|| {
        let exe_path = std::env::current_exe().unwrap();
        let exe_dir = exe_path.parent().unwrap();

        // MODE PRODUCTION : Chercher Python portable et backend dans le répertoire de l'EXE
        let python_portable = exe_dir.join("python-embed").join("python.exe");
        let backend_dir_prod = exe_dir.join("py_backend");

        // MODE DEV : Chercher dans le répertoire de développement
        let current_dir = std::env::current_dir().unwrap();
        let backend_dir_dev = current_dir.parent().unwrap_or(&current_dir).join("py_backend");

        if python_portable.exists() && backend_dir_prod.exists() {
          // MODE PRODUCTION avec Python portable
          println!("🚀 Démarrage backend Python (mode production avec Python portable)...");
          let result = Command::new(&python_portable)
            .arg("main.py")
            .current_dir(&backend_dir_prod)
            .spawn();

          match result {
            Ok(_) => println!("✅ Backend Python portable démarré avec succès"),
            Err(e) => println!("❌ Erreur démarrage backend portable: {}", e),
          }
        } else if backend_dir_dev.exists() && backend_dir_dev.join("main.py").exists() {
          // MODE DEV avec Python système
          println!("🚀 Démarrage backend Python (mode dev)...");
          let result = Command::new("python")
            .arg("main.py")
            .current_dir(&backend_dir_dev)
            .spawn();

          match result {
            Ok(_) => println!("✅ Backend Python système démarré avec succès"),
            Err(e) => println!("❌ Erreur démarrage backend système: {}", e),
          }
        } else {
          println!("❌ Aucun backend Python trouvé !");
          println!("   - Python portable: {:?}", python_portable);
          println!("   - Backend prod: {:?}", backend_dir_prod);
          println!("   - Backend dev: {:?}", backend_dir_dev);
        }
      });

      Ok(())
    })
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}
