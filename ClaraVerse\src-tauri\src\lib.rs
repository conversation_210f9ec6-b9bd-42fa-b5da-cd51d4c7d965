use std::process::Command;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  tauri::Builder::default()
    .setup(|app| {
      if cfg!(debug_assertions) {
        app.handle().plugin(
          tauri_plugin_log::Builder::default()
            .level(log::LevelFilter::Info)
            .build(),
        )?;
      }

      // Démarrer le backend Python
      std::thread::spawn(|| {
        let exe_path = std::env::current_exe().unwrap();
        let exe_dir = exe_path.parent().unwrap();

        // Essayer plusieurs chemins possibles
        let possible_paths = vec![
          exe_dir.join("py_backend"),
          exe_dir.join("_up_").join("py_backend"),
          exe_dir.join("..").join("py_backend"),
        ];

        for backend_path in possible_paths {
          if backend_path.exists() && backend_path.join("main.py").exists() {
            println!("🚀 Démarrage backend Python: {:?}", backend_path);
            let _ = Command::new("python")
              .arg("main.py")
              .current_dir(&backend_path)
              .spawn();
            break;
          }
        }
      });

      Ok(())
    })
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}
