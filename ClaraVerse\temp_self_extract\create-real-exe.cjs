#!/usr/bin/env node

/**
 * 🚀 CRÉATEUR VRAI EXE WEMA IA
 * Crée UN SEUL EXE que l'utilisateur double-clique
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 CRÉATEUR VRAI EXE WEMA IA');
console.log('UN SEUL EXE - DOUBLE-CLIC ET ÇA MARCHE');
console.log('='.repeat(50));

// 1. CRÉER SCRIPT NSIS POUR VRAI EXE
console.log('🔧 Création script NSIS...');

const nsisScript = `
!define APPNAME "WeMa IA"
!define APPVERSION "0.1.2"
!define APPEXE "WeMa IA.exe"
!define APPDIR "WeMa_IA"

Name "\${APPNAME}"
OutFile "${path.join(require('os').homedir(), 'Downloads', 'WeMa_IA_Setup.exe')}"
InstallDir "$APPDATA\\\${APPDIR}"
RequestExecutionLevel user
SilentI<PERSON><PERSON> silent
AutoCloseWindow true

; Interface moderne
!include "MUI2.nsh"
!define MUI_ICON "app\\\\resources\\\\wema-icon-256.ico"
!define MUI_HEADERIMAGE
!define MUI_HEADERIMAGE_RIGHT
!define MUI_HEADERIMAGE_BITMAP "app\\\\resources\\\\wema-logo-light.png"

; Pages (aucune page = installation silencieuse)
!insertmacro MUI_PAGE_INSTFILES

; Langues
!insertmacro MUI_LANGUAGE "French"

Section "Install" SecInstall
    SetOutPath $INSTDIR
    
    ; Copier tous les fichiers de l'application
    File /r "app\\\\*.*"
    
    ; Créer raccourcis
    CreateDirectory "$SMPROGRAMS\\\${APPNAME}"
    CreateShortCut "$SMPROGRAMS\\\${APPNAME}\\\${APPNAME}.lnk" "$INSTDIR\\\${APPEXE}"
    CreateShortCut "$DESKTOP\\\${APPNAME}.lnk" "$INSTDIR\\\${APPEXE}"
    
    ; Créer désinstalleur
    WriteUninstaller "$INSTDIR\\\\Uninstall.exe"
    
    ; Ajouter au registre pour Ajout/Suppression programmes
    WriteRegStr HKCU "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\${APPNAME}" "DisplayName" "\${APPNAME}"
    WriteRegStr HKCU "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\${APPNAME}" "UninstallString" "$INSTDIR\\\\Uninstall.exe"
    WriteRegStr HKCU "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\${APPNAME}" "DisplayVersion" "\${APPVERSION}"
    WriteRegStr HKCU "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\${APPNAME}" "Publisher" "WeMa IA Team"
    
    ; Lancer l'application automatiquement
    Exec "$INSTDIR\\\${APPEXE}"
SectionEnd

Section "Uninstall"
    ; Supprimer fichiers
    RMDir /r "$INSTDIR"
    
    ; Supprimer raccourcis
    Delete "$SMPROGRAMS\\\${APPNAME}\\\\*.*"
    RMDir "$SMPROGRAMS\\\${APPNAME}"
    Delete "$DESKTOP\\\${APPNAME}.lnk"
    
    ; Supprimer du registre
    DeleteRegKey HKCU "Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\${APPNAME}"
SectionEnd
`;

fs.writeFileSync('installer.nsi', nsisScript);
console.log('✅ Script NSIS créé');

// 2. VÉRIFIER SI NSIS EST INSTALLÉ
console.log('🔍 Vérification NSIS...');

let nsisPath = '';
const possiblePaths = [
    'C:\\Program Files (x86)\\NSIS\\makensis.exe',
    'C:\\Program Files\\NSIS\\makensis.exe',
    'makensis.exe' // Si dans PATH
];

for (const testPath of possiblePaths) {
    try {
        execSync(`"${testPath}" /VERSION`, { stdio: 'ignore' });
        nsisPath = testPath;
        console.log(`✅ NSIS trouvé: ${testPath}`);
        break;
    } catch (e) {
        // Continue
    }
}

if (!nsisPath) {
    console.log('⚠️ NSIS non installé');
    console.log('📥 Téléchargement et installation automatique...');
    
    // Créer script PowerShell pour télécharger NSIS
    const downloadScript = `
    $nsisUrl = "https://sourceforge.net/projects/nsis/files/NSIS%203/3.09/nsis-3.09-setup.exe/download"
    $nsisInstaller = "$env:TEMP\\\\nsis-setup.exe"
    
    Write-Host "Téléchargement NSIS..."
    Invoke-WebRequest -Uri $nsisUrl -OutFile $nsisInstaller -UseBasicParsing
    
    Write-Host "Installation NSIS..."
    Start-Process $nsisInstaller -ArgumentList "/S" -Wait
    
    Write-Host "NSIS installé !"
    `;
    
    fs.writeFileSync('download_nsis.ps1', downloadScript);
    
    try {
        execSync('powershell -ExecutionPolicy Bypass -File download_nsis.ps1', { stdio: 'inherit' });
        nsisPath = 'C:\\Program Files (x86)\\NSIS\\makensis.exe';
        console.log('✅ NSIS installé automatiquement');
    } catch (e) {
        console.log('❌ Impossible d\'installer NSIS automatiquement');
        console.log('');
        console.log('📋 SOLUTION MANUELLE:');
        console.log('1. Téléchargez NSIS: https://nsis.sourceforge.io/Download');
        console.log('2. Installez NSIS');
        console.log('3. Relancez ce script');
        process.exit(1);
    }
}

// 3. COMPILER L'EXE AVEC NSIS
console.log('🏗️ Compilation EXE avec NSIS...');

try {
    execSync(`"${nsisPath}" installer.nsi`, { stdio: 'inherit' });
    console.log('✅ EXE compilé avec succès !');
} catch (error) {
    console.error('❌ Erreur compilation NSIS:', error.message);
    
    // Solution alternative : Créer un EXE simple avec PowerShell
    console.log('🔄 Solution alternative...');
    
    const altScript = `
    Add-Type -AssemblyName System.Windows.Forms
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    
    [System.Windows.Forms.MessageBox]::Show("Installation de WeMa IA en cours...", "WeMa IA", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
    
    $installDir = "$env:APPDATA\\\\WeMa_IA"
    if (-not (Test-Path $installDir)) {
        New-Item -ItemType Directory -Path $installDir -Force | Out-Null
    }
    
    # Extraire l'application
    [System.IO.Compression.ZipFile]::ExtractToDirectory("wema_app.zip", $installDir)
    
    # Créer raccourci bureau
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut("$env:USERPROFILE\\\\Desktop\\\\WeMa IA.lnk")
    $Shortcut.TargetPath = "$installDir\\\\WeMa IA.exe"
    $Shortcut.Save()
    
    # Lancer l'application
    Start-Process "$installDir\\\\WeMa IA.exe"
    
    [System.Windows.Forms.MessageBox]::Show("WeMa IA installé et lancé avec succès !", "WeMa IA", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
    `;
    
    fs.writeFileSync('install_alt.ps1', altScript);
    
    // Convertir PowerShell en EXE avec ps2exe si disponible
    try {
        execSync('powershell -Command "Install-Module ps2exe -Force"', { stdio: 'ignore' });
        execSync(`powershell -Command "ps2exe install_alt.ps1 '${path.join(require('os').homedir(), 'Downloads', 'WeMa_IA_Installer.exe')}'"`, { stdio: 'inherit' });
        console.log('✅ EXE alternatif créé avec ps2exe');
    } catch (e) {
        console.log('⚠️ ps2exe non disponible');
        console.log('📋 Utilisez le fichier .bat comme solution temporaire');
    }
}

// 4. VÉRIFICATION FINALE
const finalExe = path.join(require('os').homedir(), 'Downloads', 'WeMa_IA_Setup.exe');
if (fs.existsSync(finalExe)) {
    const exeSize = fs.statSync(finalExe).size;
    const exeSizeMB = (exeSize / 1024 / 1024).toFixed(2);
    
    console.log('');
    console.log('🎉 VRAI EXE CRÉÉ AVEC SUCCÈS !');
    console.log('='.repeat(50));
    console.log(`📦 Fichier: WeMa_IA_Setup.exe`);
    console.log(`📏 Taille: ${exeSizeMB} MB`);
    console.log(`📍 Emplacement: ${finalExe}`);
    console.log('');
    console.log('👥 UTILISATION UTILISATEUR:');
    console.log('1. Double-clic sur WeMa_IA_Setup.exe');
    console.log('2. Installation automatique silencieuse');
    console.log('3. Lancement automatique de l\'application');
    console.log('4. Raccourci créé sur le bureau');
    console.log('');
    console.log('✅ PARFAIT POUR DISTRIBUTION !');
    console.log('🛡️ EXE professionnel anti-antivirus');
    console.log('📤 Un seul fichier à partager');
    
} else {
    console.log('⚠️ EXE non créé, vérifiez les logs ci-dessus');
}

console.log('');
console.log('🎯 MISSION ACCOMPLIE !');
console.log('UN SEUL EXE - DOUBLE-CLIC ET ÇA MARCHE !');
