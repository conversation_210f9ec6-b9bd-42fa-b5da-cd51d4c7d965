{"rustc": 16591470773350601817, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 1369601567987815722, "path": 663818779439922449, "deps": [[3150220818285335163, "url", false, 6198143711200766027], [6913375703034175521, "build_script_build", false, 2073999146308727926], [8319709847752024821, "uuid1", false, 2961789799076527780], [9122563107207267705, "dyn_clone", false, 14486923776429847596], [9689903380558560274, "serde", false, 651161780775042544], [14923790796823607459, "indexmap", false, 30059224043438050], [15367738274754116744, "serde_json", false, 12147971697298994304], [16071897500792579091, "schemars_derive", false, 16858173661582859803]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\schemars-85cea317ba3e51ce\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}