{"version": 3, "sources": ["../../roughjs/bundled/rough.esm.js", "../../mermaid/dist/chunks/mermaid.core/chunk-HRU6DDCH.mjs"], "sourcesContent": ["function t(t,e,s){if(t&&t.length){const[n,o]=e,a=Math.PI/180*s,h=Math.cos(a),r=Math.sin(a);for(const e of t){const[t,s]=e;e[0]=(t-n)*h-(s-o)*r+n,e[1]=(t-n)*r+(s-o)*h+o}}}function e(t,e){return t[0]===e[0]&&t[1]===e[1]}function s(s,n,o,a=1){const h=o,r=Math.max(n,.1),i=s[0]&&s[0][0]&&\"number\"==typeof s[0][0]?[s]:s,c=[0,0];if(h)for(const e of i)t(e,c,h);const l=function(t,s,n){const o=[];for(const s of t){const t=[...s];e(t[0],t[t.length-1])||t.push([t[0][0],t[0][1]]),t.length>2&&o.push(t)}const a=[];s=Math.max(s,.1);const h=[];for(const t of o)for(let e=0;e<t.length-1;e++){const s=t[e],n=t[e+1];if(s[1]!==n[1]){const t=Math.min(s[1],n[1]);h.push({ymin:t,ymax:Math.max(s[1],n[1]),x:t===s[1]?s[0]:n[0],islope:(n[0]-s[0])/(n[1]-s[1])})}}if(h.sort(((t,e)=>t.ymin<e.ymin?-1:t.ymin>e.ymin?1:t.x<e.x?-1:t.x>e.x?1:t.ymax===e.ymax?0:(t.ymax-e.ymax)/Math.abs(t.ymax-e.ymax))),!h.length)return a;let r=[],i=h[0].ymin,c=0;for(;r.length||h.length;){if(h.length){let t=-1;for(let e=0;e<h.length&&!(h[e].ymin>i);e++)t=e;h.splice(0,t+1).forEach((t=>{r.push({s:i,edge:t})}))}if(r=r.filter((t=>!(t.edge.ymax<=i))),r.sort(((t,e)=>t.edge.x===e.edge.x?0:(t.edge.x-e.edge.x)/Math.abs(t.edge.x-e.edge.x))),(1!==n||c%s==0)&&r.length>1)for(let t=0;t<r.length;t+=2){const e=t+1;if(e>=r.length)break;const s=r[t].edge,n=r[e].edge;a.push([[Math.round(s.x),i],[Math.round(n.x),i]])}i+=n,r.forEach((t=>{t.edge.x=t.edge.x+n*t.edge.islope})),c++}return a}(i,r,a);if(h){for(const e of i)t(e,c,-h);!function(e,s,n){const o=[];e.forEach((t=>o.push(...t))),t(o,s,n)}(l,c,-h)}return l}function n(t,e){var n;const o=e.hachureAngle+90;let a=e.hachureGap;a<0&&(a=4*e.strokeWidth),a=Math.round(Math.max(a,.1));let h=1;return e.roughness>=1&&((null===(n=e.randomizer)||void 0===n?void 0:n.next())||Math.random())>.7&&(h=a),s(t,a,o,h||1)}class o{constructor(t){this.helper=t}fillPolygons(t,e){return this._fillPolygons(t,e)}_fillPolygons(t,e){const s=n(t,e);return{type:\"fillSketch\",ops:this.renderLines(s,e)}}renderLines(t,e){const s=[];for(const n of t)s.push(...this.helper.doubleLineOps(n[0][0],n[0][1],n[1][0],n[1][1],e));return s}}function a(t){const e=t[0],s=t[1];return Math.sqrt(Math.pow(e[0]-s[0],2)+Math.pow(e[1]-s[1],2))}class h extends o{fillPolygons(t,e){let s=e.hachureGap;s<0&&(s=4*e.strokeWidth),s=Math.max(s,.1);const o=n(t,Object.assign({},e,{hachureGap:s})),h=Math.PI/180*e.hachureAngle,r=[],i=.5*s*Math.cos(h),c=.5*s*Math.sin(h);for(const[t,e]of o)a([t,e])&&r.push([[t[0]-i,t[1]+c],[...e]],[[t[0]+i,t[1]-c],[...e]]);return{type:\"fillSketch\",ops:this.renderLines(r,e)}}}class r extends o{fillPolygons(t,e){const s=this._fillPolygons(t,e),n=Object.assign({},e,{hachureAngle:e.hachureAngle+90}),o=this._fillPolygons(t,n);return s.ops=s.ops.concat(o.ops),s}}class i{constructor(t){this.helper=t}fillPolygons(t,e){const s=n(t,e=Object.assign({},e,{hachureAngle:0}));return this.dotsOnLines(s,e)}dotsOnLines(t,e){const s=[];let n=e.hachureGap;n<0&&(n=4*e.strokeWidth),n=Math.max(n,.1);let o=e.fillWeight;o<0&&(o=e.strokeWidth/2);const h=n/4;for(const r of t){const t=a(r),i=t/n,c=Math.ceil(i)-1,l=t-c*n,u=(r[0][0]+r[1][0])/2-n/4,p=Math.min(r[0][1],r[1][1]);for(let t=0;t<c;t++){const a=p+l+t*n,r=u-h+2*Math.random()*h,i=a-h+2*Math.random()*h,c=this.helper.ellipse(r,i,o,o,e);s.push(...c.ops)}}return{type:\"fillSketch\",ops:s}}}class c{constructor(t){this.helper=t}fillPolygons(t,e){const s=n(t,e);return{type:\"fillSketch\",ops:this.dashedLine(s,e)}}dashedLine(t,e){const s=e.dashOffset<0?e.hachureGap<0?4*e.strokeWidth:e.hachureGap:e.dashOffset,n=e.dashGap<0?e.hachureGap<0?4*e.strokeWidth:e.hachureGap:e.dashGap,o=[];return t.forEach((t=>{const h=a(t),r=Math.floor(h/(s+n)),i=(h+n-r*(s+n))/2;let c=t[0],l=t[1];c[0]>l[0]&&(c=t[1],l=t[0]);const u=Math.atan((l[1]-c[1])/(l[0]-c[0]));for(let t=0;t<r;t++){const a=t*(s+n),h=a+s,r=[c[0]+a*Math.cos(u)+i*Math.cos(u),c[1]+a*Math.sin(u)+i*Math.sin(u)],l=[c[0]+h*Math.cos(u)+i*Math.cos(u),c[1]+h*Math.sin(u)+i*Math.sin(u)];o.push(...this.helper.doubleLineOps(r[0],r[1],l[0],l[1],e))}})),o}}class l{constructor(t){this.helper=t}fillPolygons(t,e){const s=e.hachureGap<0?4*e.strokeWidth:e.hachureGap,o=e.zigzagOffset<0?s:e.zigzagOffset,a=n(t,e=Object.assign({},e,{hachureGap:s+o}));return{type:\"fillSketch\",ops:this.zigzagLines(a,o,e)}}zigzagLines(t,e,s){const n=[];return t.forEach((t=>{const o=a(t),h=Math.round(o/(2*e));let r=t[0],i=t[1];r[0]>i[0]&&(r=t[1],i=t[0]);const c=Math.atan((i[1]-r[1])/(i[0]-r[0]));for(let t=0;t<h;t++){const o=2*t*e,a=2*(t+1)*e,h=Math.sqrt(2*Math.pow(e,2)),i=[r[0]+o*Math.cos(c),r[1]+o*Math.sin(c)],l=[r[0]+a*Math.cos(c),r[1]+a*Math.sin(c)],u=[i[0]+h*Math.cos(c+Math.PI/4),i[1]+h*Math.sin(c+Math.PI/4)];n.push(...this.helper.doubleLineOps(i[0],i[1],u[0],u[1],s),...this.helper.doubleLineOps(u[0],u[1],l[0],l[1],s))}})),n}}const u={};class p{constructor(t){this.seed=t}next(){return this.seed?(2**31-1&(this.seed=Math.imul(48271,this.seed)))/2**31:Math.random()}}const f=0,d=1,g=2,M={A:7,a:7,C:6,c:6,H:1,h:1,L:2,l:2,M:2,m:2,Q:4,q:4,S:4,s:4,T:2,t:2,V:1,v:1,Z:0,z:0};function k(t,e){return t.type===e}function b(t){const e=[],s=function(t){const e=new Array;for(;\"\"!==t;)if(t.match(/^([ \\t\\r\\n,]+)/))t=t.substr(RegExp.$1.length);else if(t.match(/^([aAcChHlLmMqQsStTvVzZ])/))e[e.length]={type:f,text:RegExp.$1},t=t.substr(RegExp.$1.length);else{if(!t.match(/^(([-+]?[0-9]+(\\.[0-9]*)?|[-+]?\\.[0-9]+)([eE][-+]?[0-9]+)?)/))return[];e[e.length]={type:d,text:`${parseFloat(RegExp.$1)}`},t=t.substr(RegExp.$1.length)}return e[e.length]={type:g,text:\"\"},e}(t);let n=\"BOD\",o=0,a=s[o];for(;!k(a,g);){let h=0;const r=[];if(\"BOD\"===n){if(\"M\"!==a.text&&\"m\"!==a.text)return b(\"M0,0\"+t);o++,h=M[a.text],n=a.text}else k(a,d)?h=M[n]:(o++,h=M[a.text],n=a.text);if(!(o+h<s.length))throw new Error(\"Path data ended short\");for(let t=o;t<o+h;t++){const e=s[t];if(!k(e,d))throw new Error(\"Param not a number: \"+n+\",\"+e.text);r[r.length]=+e.text}if(\"number\"!=typeof M[n])throw new Error(\"Bad segment: \"+n);{const t={key:n,data:r};e.push(t),o+=h,a=s[o],\"M\"===n&&(n=\"L\"),\"m\"===n&&(n=\"l\")}}return e}function y(t){let e=0,s=0,n=0,o=0;const a=[];for(const{key:h,data:r}of t)switch(h){case\"M\":a.push({key:\"M\",data:[...r]}),[e,s]=r,[n,o]=r;break;case\"m\":e+=r[0],s+=r[1],a.push({key:\"M\",data:[e,s]}),n=e,o=s;break;case\"L\":a.push({key:\"L\",data:[...r]}),[e,s]=r;break;case\"l\":e+=r[0],s+=r[1],a.push({key:\"L\",data:[e,s]});break;case\"C\":a.push({key:\"C\",data:[...r]}),e=r[4],s=r[5];break;case\"c\":{const t=r.map(((t,n)=>n%2?t+s:t+e));a.push({key:\"C\",data:t}),e=t[4],s=t[5];break}case\"Q\":a.push({key:\"Q\",data:[...r]}),e=r[2],s=r[3];break;case\"q\":{const t=r.map(((t,n)=>n%2?t+s:t+e));a.push({key:\"Q\",data:t}),e=t[2],s=t[3];break}case\"A\":a.push({key:\"A\",data:[...r]}),e=r[5],s=r[6];break;case\"a\":e+=r[5],s+=r[6],a.push({key:\"A\",data:[r[0],r[1],r[2],r[3],r[4],e,s]});break;case\"H\":a.push({key:\"H\",data:[...r]}),e=r[0];break;case\"h\":e+=r[0],a.push({key:\"H\",data:[e]});break;case\"V\":a.push({key:\"V\",data:[...r]}),s=r[0];break;case\"v\":s+=r[0],a.push({key:\"V\",data:[s]});break;case\"S\":a.push({key:\"S\",data:[...r]}),e=r[2],s=r[3];break;case\"s\":{const t=r.map(((t,n)=>n%2?t+s:t+e));a.push({key:\"S\",data:t}),e=t[2],s=t[3];break}case\"T\":a.push({key:\"T\",data:[...r]}),e=r[0],s=r[1];break;case\"t\":e+=r[0],s+=r[1],a.push({key:\"T\",data:[e,s]});break;case\"Z\":case\"z\":a.push({key:\"Z\",data:[]}),e=n,s=o}return a}function m(t){const e=[];let s=\"\",n=0,o=0,a=0,h=0,r=0,i=0;for(const{key:c,data:l}of t){switch(c){case\"M\":e.push({key:\"M\",data:[...l]}),[n,o]=l,[a,h]=l;break;case\"C\":e.push({key:\"C\",data:[...l]}),n=l[4],o=l[5],r=l[2],i=l[3];break;case\"L\":e.push({key:\"L\",data:[...l]}),[n,o]=l;break;case\"H\":n=l[0],e.push({key:\"L\",data:[n,o]});break;case\"V\":o=l[0],e.push({key:\"L\",data:[n,o]});break;case\"S\":{let t=0,a=0;\"C\"===s||\"S\"===s?(t=n+(n-r),a=o+(o-i)):(t=n,a=o),e.push({key:\"C\",data:[t,a,...l]}),r=l[0],i=l[1],n=l[2],o=l[3];break}case\"T\":{const[t,a]=l;let h=0,c=0;\"Q\"===s||\"T\"===s?(h=n+(n-r),c=o+(o-i)):(h=n,c=o);const u=n+2*(h-n)/3,p=o+2*(c-o)/3,f=t+2*(h-t)/3,d=a+2*(c-a)/3;e.push({key:\"C\",data:[u,p,f,d,t,a]}),r=h,i=c,n=t,o=a;break}case\"Q\":{const[t,s,a,h]=l,c=n+2*(t-n)/3,u=o+2*(s-o)/3,p=a+2*(t-a)/3,f=h+2*(s-h)/3;e.push({key:\"C\",data:[c,u,p,f,a,h]}),r=t,i=s,n=a,o=h;break}case\"A\":{const t=Math.abs(l[0]),s=Math.abs(l[1]),a=l[2],h=l[3],r=l[4],i=l[5],c=l[6];if(0===t||0===s)e.push({key:\"C\",data:[n,o,i,c,i,c]}),n=i,o=c;else if(n!==i||o!==c){x(n,o,i,c,t,s,a,h,r).forEach((function(t){e.push({key:\"C\",data:t})})),n=i,o=c}break}case\"Z\":e.push({key:\"Z\",data:[]}),n=a,o=h}s=c}return e}function w(t,e,s){return[t*Math.cos(s)-e*Math.sin(s),t*Math.sin(s)+e*Math.cos(s)]}function x(t,e,s,n,o,a,h,r,i,c){const l=(u=h,Math.PI*u/180);var u;let p=[],f=0,d=0,g=0,M=0;if(c)[f,d,g,M]=c;else{[t,e]=w(t,e,-l),[s,n]=w(s,n,-l);const h=(t-s)/2,c=(e-n)/2;let u=h*h/(o*o)+c*c/(a*a);u>1&&(u=Math.sqrt(u),o*=u,a*=u);const p=o*o,k=a*a,b=p*k-p*c*c-k*h*h,y=p*c*c+k*h*h,m=(r===i?-1:1)*Math.sqrt(Math.abs(b/y));g=m*o*c/a+(t+s)/2,M=m*-a*h/o+(e+n)/2,f=Math.asin(parseFloat(((e-M)/a).toFixed(9))),d=Math.asin(parseFloat(((n-M)/a).toFixed(9))),t<g&&(f=Math.PI-f),s<g&&(d=Math.PI-d),f<0&&(f=2*Math.PI+f),d<0&&(d=2*Math.PI+d),i&&f>d&&(f-=2*Math.PI),!i&&d>f&&(d-=2*Math.PI)}let k=d-f;if(Math.abs(k)>120*Math.PI/180){const t=d,e=s,r=n;d=i&&d>f?f+120*Math.PI/180*1:f+120*Math.PI/180*-1,p=x(s=g+o*Math.cos(d),n=M+a*Math.sin(d),e,r,o,a,h,0,i,[d,t,g,M])}k=d-f;const b=Math.cos(f),y=Math.sin(f),m=Math.cos(d),P=Math.sin(d),v=Math.tan(k/4),S=4/3*o*v,O=4/3*a*v,L=[t,e],T=[t+S*y,e-O*b],D=[s+S*P,n-O*m],A=[s,n];if(T[0]=2*L[0]-T[0],T[1]=2*L[1]-T[1],c)return[T,D,A].concat(p);{p=[T,D,A].concat(p);const t=[];for(let e=0;e<p.length;e+=3){const s=w(p[e][0],p[e][1],l),n=w(p[e+1][0],p[e+1][1],l),o=w(p[e+2][0],p[e+2][1],l);t.push([s[0],s[1],n[0],n[1],o[0],o[1]])}return t}}const P={randOffset:function(t,e){return G(t,e)},randOffsetWithRange:function(t,e,s){return E(t,e,s)},ellipse:function(t,e,s,n,o){const a=T(s,n,o);return D(t,e,o,a).opset},doubleLineOps:function(t,e,s,n,o){return $(t,e,s,n,o,!0)}};function v(t,e,s,n,o){return{type:\"path\",ops:$(t,e,s,n,o)}}function S(t,e,s){const n=(t||[]).length;if(n>2){const o=[];for(let e=0;e<n-1;e++)o.push(...$(t[e][0],t[e][1],t[e+1][0],t[e+1][1],s));return e&&o.push(...$(t[n-1][0],t[n-1][1],t[0][0],t[0][1],s)),{type:\"path\",ops:o}}return 2===n?v(t[0][0],t[0][1],t[1][0],t[1][1],s):{type:\"path\",ops:[]}}function O(t,e,s,n,o){return function(t,e){return S(t,!0,e)}([[t,e],[t+s,e],[t+s,e+n],[t,e+n]],o)}function L(t,e){if(t.length){const s=\"number\"==typeof t[0][0]?[t]:t,n=j(s[0],1*(1+.2*e.roughness),e),o=e.disableMultiStroke?[]:j(s[0],1.5*(1+.22*e.roughness),z(e));for(let t=1;t<s.length;t++){const a=s[t];if(a.length){const t=j(a,1*(1+.2*e.roughness),e),s=e.disableMultiStroke?[]:j(a,1.5*(1+.22*e.roughness),z(e));for(const e of t)\"move\"!==e.op&&n.push(e);for(const t of s)\"move\"!==t.op&&o.push(t)}}return{type:\"path\",ops:n.concat(o)}}return{type:\"path\",ops:[]}}function T(t,e,s){const n=Math.sqrt(2*Math.PI*Math.sqrt((Math.pow(t/2,2)+Math.pow(e/2,2))/2)),o=Math.ceil(Math.max(s.curveStepCount,s.curveStepCount/Math.sqrt(200)*n)),a=2*Math.PI/o;let h=Math.abs(t/2),r=Math.abs(e/2);const i=1-s.curveFitting;return h+=G(h*i,s),r+=G(r*i,s),{increment:a,rx:h,ry:r}}function D(t,e,s,n){const[o,a]=F(n.increment,t,e,n.rx,n.ry,1,n.increment*E(.1,E(.4,1,s),s),s);let h=q(o,null,s);if(!s.disableMultiStroke&&0!==s.roughness){const[o]=F(n.increment,t,e,n.rx,n.ry,1.5,0,s),a=q(o,null,s);h=h.concat(a)}return{estimatedPoints:a,opset:{type:\"path\",ops:h}}}function A(t,e,s,n,o,a,h,r,i){const c=t,l=e;let u=Math.abs(s/2),p=Math.abs(n/2);u+=G(.01*u,i),p+=G(.01*p,i);let f=o,d=a;for(;f<0;)f+=2*Math.PI,d+=2*Math.PI;d-f>2*Math.PI&&(f=0,d=2*Math.PI);const g=2*Math.PI/i.curveStepCount,M=Math.min(g/2,(d-f)/2),k=V(M,c,l,u,p,f,d,1,i);if(!i.disableMultiStroke){const t=V(M,c,l,u,p,f,d,1.5,i);k.push(...t)}return h&&(r?k.push(...$(c,l,c+u*Math.cos(f),l+p*Math.sin(f),i),...$(c,l,c+u*Math.cos(d),l+p*Math.sin(d),i)):k.push({op:\"lineTo\",data:[c,l]},{op:\"lineTo\",data:[c+u*Math.cos(f),l+p*Math.sin(f)]})),{type:\"path\",ops:k}}function _(t,e){const s=m(y(b(t))),n=[];let o=[0,0],a=[0,0];for(const{key:t,data:h}of s)switch(t){case\"M\":a=[h[0],h[1]],o=[h[0],h[1]];break;case\"L\":n.push(...$(a[0],a[1],h[0],h[1],e)),a=[h[0],h[1]];break;case\"C\":{const[t,s,o,r,i,c]=h;n.push(...Z(t,s,o,r,i,c,a,e)),a=[i,c];break}case\"Z\":n.push(...$(a[0],a[1],o[0],o[1],e)),a=[o[0],o[1]]}return{type:\"path\",ops:n}}function I(t,e){const s=[];for(const n of t)if(n.length){const t=e.maxRandomnessOffset||0,o=n.length;if(o>2){s.push({op:\"move\",data:[n[0][0]+G(t,e),n[0][1]+G(t,e)]});for(let a=1;a<o;a++)s.push({op:\"lineTo\",data:[n[a][0]+G(t,e),n[a][1]+G(t,e)]})}}return{type:\"fillPath\",ops:s}}function C(t,e){return function(t,e){let s=t.fillStyle||\"hachure\";if(!u[s])switch(s){case\"zigzag\":u[s]||(u[s]=new h(e));break;case\"cross-hatch\":u[s]||(u[s]=new r(e));break;case\"dots\":u[s]||(u[s]=new i(e));break;case\"dashed\":u[s]||(u[s]=new c(e));break;case\"zigzag-line\":u[s]||(u[s]=new l(e));break;default:s=\"hachure\",u[s]||(u[s]=new o(e))}return u[s]}(e,P).fillPolygons(t,e)}function z(t){const e=Object.assign({},t);return e.randomizer=void 0,t.seed&&(e.seed=t.seed+1),e}function W(t){return t.randomizer||(t.randomizer=new p(t.seed||0)),t.randomizer.next()}function E(t,e,s,n=1){return s.roughness*n*(W(s)*(e-t)+t)}function G(t,e,s=1){return E(-t,t,e,s)}function $(t,e,s,n,o,a=!1){const h=a?o.disableMultiStrokeFill:o.disableMultiStroke,r=R(t,e,s,n,o,!0,!1);if(h)return r;const i=R(t,e,s,n,o,!0,!0);return r.concat(i)}function R(t,e,s,n,o,a,h){const r=Math.pow(t-s,2)+Math.pow(e-n,2),i=Math.sqrt(r);let c=1;c=i<200?1:i>500?.4:-.0016668*i+1.233334;let l=o.maxRandomnessOffset||0;l*l*100>r&&(l=i/10);const u=l/2,p=.2+.2*W(o);let f=o.bowing*o.maxRandomnessOffset*(n-e)/200,d=o.bowing*o.maxRandomnessOffset*(t-s)/200;f=G(f,o,c),d=G(d,o,c);const g=[],M=()=>G(u,o,c),k=()=>G(l,o,c),b=o.preserveVertices;return a&&(h?g.push({op:\"move\",data:[t+(b?0:M()),e+(b?0:M())]}):g.push({op:\"move\",data:[t+(b?0:G(l,o,c)),e+(b?0:G(l,o,c))]})),h?g.push({op:\"bcurveTo\",data:[f+t+(s-t)*p+M(),d+e+(n-e)*p+M(),f+t+2*(s-t)*p+M(),d+e+2*(n-e)*p+M(),s+(b?0:M()),n+(b?0:M())]}):g.push({op:\"bcurveTo\",data:[f+t+(s-t)*p+k(),d+e+(n-e)*p+k(),f+t+2*(s-t)*p+k(),d+e+2*(n-e)*p+k(),s+(b?0:k()),n+(b?0:k())]}),g}function j(t,e,s){if(!t.length)return[];const n=[];n.push([t[0][0]+G(e,s),t[0][1]+G(e,s)]),n.push([t[0][0]+G(e,s),t[0][1]+G(e,s)]);for(let o=1;o<t.length;o++)n.push([t[o][0]+G(e,s),t[o][1]+G(e,s)]),o===t.length-1&&n.push([t[o][0]+G(e,s),t[o][1]+G(e,s)]);return q(n,null,s)}function q(t,e,s){const n=t.length,o=[];if(n>3){const a=[],h=1-s.curveTightness;o.push({op:\"move\",data:[t[1][0],t[1][1]]});for(let e=1;e+2<n;e++){const s=t[e];a[0]=[s[0],s[1]],a[1]=[s[0]+(h*t[e+1][0]-h*t[e-1][0])/6,s[1]+(h*t[e+1][1]-h*t[e-1][1])/6],a[2]=[t[e+1][0]+(h*t[e][0]-h*t[e+2][0])/6,t[e+1][1]+(h*t[e][1]-h*t[e+2][1])/6],a[3]=[t[e+1][0],t[e+1][1]],o.push({op:\"bcurveTo\",data:[a[1][0],a[1][1],a[2][0],a[2][1],a[3][0],a[3][1]]})}if(e&&2===e.length){const t=s.maxRandomnessOffset;o.push({op:\"lineTo\",data:[e[0]+G(t,s),e[1]+G(t,s)]})}}else 3===n?(o.push({op:\"move\",data:[t[1][0],t[1][1]]}),o.push({op:\"bcurveTo\",data:[t[1][0],t[1][1],t[2][0],t[2][1],t[2][0],t[2][1]]})):2===n&&o.push(...R(t[0][0],t[0][1],t[1][0],t[1][1],s,!0,!0));return o}function F(t,e,s,n,o,a,h,r){const i=[],c=[];if(0===r.roughness){t/=4,c.push([e+n*Math.cos(-t),s+o*Math.sin(-t)]);for(let a=0;a<=2*Math.PI;a+=t){const t=[e+n*Math.cos(a),s+o*Math.sin(a)];i.push(t),c.push(t)}c.push([e+n*Math.cos(0),s+o*Math.sin(0)]),c.push([e+n*Math.cos(t),s+o*Math.sin(t)])}else{const l=G(.5,r)-Math.PI/2;c.push([G(a,r)+e+.9*n*Math.cos(l-t),G(a,r)+s+.9*o*Math.sin(l-t)]);const u=2*Math.PI+l-.01;for(let h=l;h<u;h+=t){const t=[G(a,r)+e+n*Math.cos(h),G(a,r)+s+o*Math.sin(h)];i.push(t),c.push(t)}c.push([G(a,r)+e+n*Math.cos(l+2*Math.PI+.5*h),G(a,r)+s+o*Math.sin(l+2*Math.PI+.5*h)]),c.push([G(a,r)+e+.98*n*Math.cos(l+h),G(a,r)+s+.98*o*Math.sin(l+h)]),c.push([G(a,r)+e+.9*n*Math.cos(l+.5*h),G(a,r)+s+.9*o*Math.sin(l+.5*h)])}return[c,i]}function V(t,e,s,n,o,a,h,r,i){const c=a+G(.1,i),l=[];l.push([G(r,i)+e+.9*n*Math.cos(c-t),G(r,i)+s+.9*o*Math.sin(c-t)]);for(let a=c;a<=h;a+=t)l.push([G(r,i)+e+n*Math.cos(a),G(r,i)+s+o*Math.sin(a)]);return l.push([e+n*Math.cos(h),s+o*Math.sin(h)]),l.push([e+n*Math.cos(h),s+o*Math.sin(h)]),q(l,null,i)}function Z(t,e,s,n,o,a,h,r){const i=[],c=[r.maxRandomnessOffset||1,(r.maxRandomnessOffset||1)+.3];let l=[0,0];const u=r.disableMultiStroke?1:2,p=r.preserveVertices;for(let f=0;f<u;f++)0===f?i.push({op:\"move\",data:[h[0],h[1]]}):i.push({op:\"move\",data:[h[0]+(p?0:G(c[0],r)),h[1]+(p?0:G(c[0],r))]}),l=p?[o,a]:[o+G(c[f],r),a+G(c[f],r)],i.push({op:\"bcurveTo\",data:[t+G(c[f],r),e+G(c[f],r),s+G(c[f],r),n+G(c[f],r),l[0],l[1]]});return i}function Q(t){return[...t]}function H(t,e=0){const s=t.length;if(s<3)throw new Error(\"A curve must have at least three points.\");const n=[];if(3===s)n.push(Q(t[0]),Q(t[1]),Q(t[2]),Q(t[2]));else{const s=[];s.push(t[0],t[0]);for(let e=1;e<t.length;e++)s.push(t[e]),e===t.length-1&&s.push(t[e]);const o=[],a=1-e;n.push(Q(s[0]));for(let t=1;t+2<s.length;t++){const e=s[t];o[0]=[e[0],e[1]],o[1]=[e[0]+(a*s[t+1][0]-a*s[t-1][0])/6,e[1]+(a*s[t+1][1]-a*s[t-1][1])/6],o[2]=[s[t+1][0]+(a*s[t][0]-a*s[t+2][0])/6,s[t+1][1]+(a*s[t][1]-a*s[t+2][1])/6],o[3]=[s[t+1][0],s[t+1][1]],n.push(o[1],o[2],o[3])}}return n}function N(t,e){return Math.pow(t[0]-e[0],2)+Math.pow(t[1]-e[1],2)}function B(t,e,s){const n=N(e,s);if(0===n)return N(t,e);let o=((t[0]-e[0])*(s[0]-e[0])+(t[1]-e[1])*(s[1]-e[1]))/n;return o=Math.max(0,Math.min(1,o)),N(t,J(e,s,o))}function J(t,e,s){return[t[0]+(e[0]-t[0])*s,t[1]+(e[1]-t[1])*s]}function K(t,e,s,n){const o=n||[];if(function(t,e){const s=t[e+0],n=t[e+1],o=t[e+2],a=t[e+3];let h=3*n[0]-2*s[0]-a[0];h*=h;let r=3*n[1]-2*s[1]-a[1];r*=r;let i=3*o[0]-2*a[0]-s[0];i*=i;let c=3*o[1]-2*a[1]-s[1];return c*=c,h<i&&(h=i),r<c&&(r=c),h+r}(t,e)<s){const s=t[e+0];if(o.length){(a=o[o.length-1],h=s,Math.sqrt(N(a,h)))>1&&o.push(s)}else o.push(s);o.push(t[e+3])}else{const n=.5,a=t[e+0],h=t[e+1],r=t[e+2],i=t[e+3],c=J(a,h,n),l=J(h,r,n),u=J(r,i,n),p=J(c,l,n),f=J(l,u,n),d=J(p,f,n);K([a,c,p,d],0,s,o),K([d,f,u,i],0,s,o)}var a,h;return o}function U(t,e){return X(t,0,t.length,e)}function X(t,e,s,n,o){const a=o||[],h=t[e],r=t[s-1];let i=0,c=1;for(let n=e+1;n<s-1;++n){const e=B(t[n],h,r);e>i&&(i=e,c=n)}return Math.sqrt(i)>n?(X(t,e,c+1,n,a),X(t,c,s,n,a)):(a.length||a.push(h),a.push(r)),a}function Y(t,e=.15,s){const n=[],o=(t.length-1)/3;for(let s=0;s<o;s++){K(t,3*s,e,n)}return s&&s>0?X(n,0,n.length,s):n}const tt=\"none\";class et{constructor(t){this.defaultOptions={maxRandomnessOffset:2,roughness:1,bowing:1,stroke:\"#000\",strokeWidth:1,curveTightness:0,curveFitting:.95,curveStepCount:9,fillStyle:\"hachure\",fillWeight:-1,hachureAngle:-41,hachureGap:-1,dashOffset:-1,dashGap:-1,zigzagOffset:-1,seed:0,disableMultiStroke:!1,disableMultiStrokeFill:!1,preserveVertices:!1,fillShapeRoughnessGain:.8},this.config=t||{},this.config.options&&(this.defaultOptions=this._o(this.config.options))}static newSeed(){return Math.floor(Math.random()*2**31)}_o(t){return t?Object.assign({},this.defaultOptions,t):this.defaultOptions}_d(t,e,s){return{shape:t,sets:e||[],options:s||this.defaultOptions}}line(t,e,s,n,o){const a=this._o(o);return this._d(\"line\",[v(t,e,s,n,a)],a)}rectangle(t,e,s,n,o){const a=this._o(o),h=[],r=O(t,e,s,n,a);if(a.fill){const o=[[t,e],[t+s,e],[t+s,e+n],[t,e+n]];\"solid\"===a.fillStyle?h.push(I([o],a)):h.push(C([o],a))}return a.stroke!==tt&&h.push(r),this._d(\"rectangle\",h,a)}ellipse(t,e,s,n,o){const a=this._o(o),h=[],r=T(s,n,a),i=D(t,e,a,r);if(a.fill)if(\"solid\"===a.fillStyle){const s=D(t,e,a,r).opset;s.type=\"fillPath\",h.push(s)}else h.push(C([i.estimatedPoints],a));return a.stroke!==tt&&h.push(i.opset),this._d(\"ellipse\",h,a)}circle(t,e,s,n){const o=this.ellipse(t,e,s,s,n);return o.shape=\"circle\",o}linearPath(t,e){const s=this._o(e);return this._d(\"linearPath\",[S(t,!1,s)],s)}arc(t,e,s,n,o,a,h=!1,r){const i=this._o(r),c=[],l=A(t,e,s,n,o,a,h,!0,i);if(h&&i.fill)if(\"solid\"===i.fillStyle){const h=Object.assign({},i);h.disableMultiStroke=!0;const r=A(t,e,s,n,o,a,!0,!1,h);r.type=\"fillPath\",c.push(r)}else c.push(function(t,e,s,n,o,a,h){const r=t,i=e;let c=Math.abs(s/2),l=Math.abs(n/2);c+=G(.01*c,h),l+=G(.01*l,h);let u=o,p=a;for(;u<0;)u+=2*Math.PI,p+=2*Math.PI;p-u>2*Math.PI&&(u=0,p=2*Math.PI);const f=(p-u)/h.curveStepCount,d=[];for(let t=u;t<=p;t+=f)d.push([r+c*Math.cos(t),i+l*Math.sin(t)]);return d.push([r+c*Math.cos(p),i+l*Math.sin(p)]),d.push([r,i]),C([d],h)}(t,e,s,n,o,a,i));return i.stroke!==tt&&c.push(l),this._d(\"arc\",c,i)}curve(t,e){const s=this._o(e),n=[],o=L(t,s);if(s.fill&&s.fill!==tt)if(\"solid\"===s.fillStyle){const e=L(t,Object.assign(Object.assign({},s),{disableMultiStroke:!0,roughness:s.roughness?s.roughness+s.fillShapeRoughnessGain:0}));n.push({type:\"fillPath\",ops:this._mergedShape(e.ops)})}else{const e=[],o=t;if(o.length){const t=\"number\"==typeof o[0][0]?[o]:o;for(const n of t)n.length<3?e.push(...n):3===n.length?e.push(...Y(H([n[0],n[0],n[1],n[2]]),10,(1+s.roughness)/2)):e.push(...Y(H(n),10,(1+s.roughness)/2))}e.length&&n.push(C([e],s))}return s.stroke!==tt&&n.push(o),this._d(\"curve\",n,s)}polygon(t,e){const s=this._o(e),n=[],o=S(t,!0,s);return s.fill&&(\"solid\"===s.fillStyle?n.push(I([t],s)):n.push(C([t],s))),s.stroke!==tt&&n.push(o),this._d(\"polygon\",n,s)}path(t,e){const s=this._o(e),n=[];if(!t)return this._d(\"path\",n,s);t=(t||\"\").replace(/\\n/g,\" \").replace(/(-\\s)/g,\"-\").replace(\"/(ss)/g\",\" \");const o=s.fill&&\"transparent\"!==s.fill&&s.fill!==tt,a=s.stroke!==tt,h=!!(s.simplification&&s.simplification<1),r=function(t,e,s){const n=m(y(b(t))),o=[];let a=[],h=[0,0],r=[];const i=()=>{r.length>=4&&a.push(...Y(r,e)),r=[]},c=()=>{i(),a.length&&(o.push(a),a=[])};for(const{key:t,data:e}of n)switch(t){case\"M\":c(),h=[e[0],e[1]],a.push(h);break;case\"L\":i(),a.push([e[0],e[1]]);break;case\"C\":if(!r.length){const t=a.length?a[a.length-1]:h;r.push([t[0],t[1]])}r.push([e[0],e[1]]),r.push([e[2],e[3]]),r.push([e[4],e[5]]);break;case\"Z\":i(),a.push([h[0],h[1]])}if(c(),!s)return o;const l=[];for(const t of o){const e=U(t,s);e.length&&l.push(e)}return l}(t,1,h?4-4*(s.simplification||1):(1+s.roughness)/2),i=_(t,s);if(o)if(\"solid\"===s.fillStyle)if(1===r.length){const e=_(t,Object.assign(Object.assign({},s),{disableMultiStroke:!0,roughness:s.roughness?s.roughness+s.fillShapeRoughnessGain:0}));n.push({type:\"fillPath\",ops:this._mergedShape(e.ops)})}else n.push(I(r,s));else n.push(C(r,s));return a&&(h?r.forEach((t=>{n.push(S(t,!1,s))})):n.push(i)),this._d(\"path\",n,s)}opsToPath(t,e){let s=\"\";for(const n of t.ops){const t=\"number\"==typeof e&&e>=0?n.data.map((t=>+t.toFixed(e))):n.data;switch(n.op){case\"move\":s+=`M${t[0]} ${t[1]} `;break;case\"bcurveTo\":s+=`C${t[0]} ${t[1]}, ${t[2]} ${t[3]}, ${t[4]} ${t[5]} `;break;case\"lineTo\":s+=`L${t[0]} ${t[1]} `}}return s.trim()}toPaths(t){const e=t.sets||[],s=t.options||this.defaultOptions,n=[];for(const t of e){let e=null;switch(t.type){case\"path\":e={d:this.opsToPath(t),stroke:s.stroke,strokeWidth:s.strokeWidth,fill:tt};break;case\"fillPath\":e={d:this.opsToPath(t),stroke:tt,strokeWidth:0,fill:s.fill||tt};break;case\"fillSketch\":e=this.fillSketch(t,s)}e&&n.push(e)}return n}fillSketch(t,e){let s=e.fillWeight;return s<0&&(s=e.strokeWidth/2),{d:this.opsToPath(t),stroke:e.fill||tt,strokeWidth:s,fill:tt}}_mergedShape(t){return t.filter(((t,e)=>0===e||\"move\"!==t.op))}}class st{constructor(t,e){this.canvas=t,this.ctx=this.canvas.getContext(\"2d\"),this.gen=new et(e)}draw(t){const e=t.sets||[],s=t.options||this.getDefaultOptions(),n=this.ctx,o=t.options.fixedDecimalPlaceDigits;for(const a of e)switch(a.type){case\"path\":n.save(),n.strokeStyle=\"none\"===s.stroke?\"transparent\":s.stroke,n.lineWidth=s.strokeWidth,s.strokeLineDash&&n.setLineDash(s.strokeLineDash),s.strokeLineDashOffset&&(n.lineDashOffset=s.strokeLineDashOffset),this._drawToContext(n,a,o),n.restore();break;case\"fillPath\":{n.save(),n.fillStyle=s.fill||\"\";const e=\"curve\"===t.shape||\"polygon\"===t.shape||\"path\"===t.shape?\"evenodd\":\"nonzero\";this._drawToContext(n,a,o,e),n.restore();break}case\"fillSketch\":this.fillSketch(n,a,s)}}fillSketch(t,e,s){let n=s.fillWeight;n<0&&(n=s.strokeWidth/2),t.save(),s.fillLineDash&&t.setLineDash(s.fillLineDash),s.fillLineDashOffset&&(t.lineDashOffset=s.fillLineDashOffset),t.strokeStyle=s.fill||\"\",t.lineWidth=n,this._drawToContext(t,e,s.fixedDecimalPlaceDigits),t.restore()}_drawToContext(t,e,s,n=\"nonzero\"){t.beginPath();for(const n of e.ops){const e=\"number\"==typeof s&&s>=0?n.data.map((t=>+t.toFixed(s))):n.data;switch(n.op){case\"move\":t.moveTo(e[0],e[1]);break;case\"bcurveTo\":t.bezierCurveTo(e[0],e[1],e[2],e[3],e[4],e[5]);break;case\"lineTo\":t.lineTo(e[0],e[1])}}\"fillPath\"===e.type?t.fill(n):t.stroke()}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}line(t,e,s,n,o){const a=this.gen.line(t,e,s,n,o);return this.draw(a),a}rectangle(t,e,s,n,o){const a=this.gen.rectangle(t,e,s,n,o);return this.draw(a),a}ellipse(t,e,s,n,o){const a=this.gen.ellipse(t,e,s,n,o);return this.draw(a),a}circle(t,e,s,n){const o=this.gen.circle(t,e,s,n);return this.draw(o),o}linearPath(t,e){const s=this.gen.linearPath(t,e);return this.draw(s),s}polygon(t,e){const s=this.gen.polygon(t,e);return this.draw(s),s}arc(t,e,s,n,o,a,h=!1,r){const i=this.gen.arc(t,e,s,n,o,a,h,r);return this.draw(i),i}curve(t,e){const s=this.gen.curve(t,e);return this.draw(s),s}path(t,e){const s=this.gen.path(t,e);return this.draw(s),s}}const nt=\"http://www.w3.org/2000/svg\";class ot{constructor(t,e){this.svg=t,this.gen=new et(e)}draw(t){const e=t.sets||[],s=t.options||this.getDefaultOptions(),n=this.svg.ownerDocument||window.document,o=n.createElementNS(nt,\"g\"),a=t.options.fixedDecimalPlaceDigits;for(const h of e){let e=null;switch(h.type){case\"path\":e=n.createElementNS(nt,\"path\"),e.setAttribute(\"d\",this.opsToPath(h,a)),e.setAttribute(\"stroke\",s.stroke),e.setAttribute(\"stroke-width\",s.strokeWidth+\"\"),e.setAttribute(\"fill\",\"none\"),s.strokeLineDash&&e.setAttribute(\"stroke-dasharray\",s.strokeLineDash.join(\" \").trim()),s.strokeLineDashOffset&&e.setAttribute(\"stroke-dashoffset\",`${s.strokeLineDashOffset}`);break;case\"fillPath\":e=n.createElementNS(nt,\"path\"),e.setAttribute(\"d\",this.opsToPath(h,a)),e.setAttribute(\"stroke\",\"none\"),e.setAttribute(\"stroke-width\",\"0\"),e.setAttribute(\"fill\",s.fill||\"\"),\"curve\"!==t.shape&&\"polygon\"!==t.shape||e.setAttribute(\"fill-rule\",\"evenodd\");break;case\"fillSketch\":e=this.fillSketch(n,h,s)}e&&o.appendChild(e)}return o}fillSketch(t,e,s){let n=s.fillWeight;n<0&&(n=s.strokeWidth/2);const o=t.createElementNS(nt,\"path\");return o.setAttribute(\"d\",this.opsToPath(e,s.fixedDecimalPlaceDigits)),o.setAttribute(\"stroke\",s.fill||\"\"),o.setAttribute(\"stroke-width\",n+\"\"),o.setAttribute(\"fill\",\"none\"),s.fillLineDash&&o.setAttribute(\"stroke-dasharray\",s.fillLineDash.join(\" \").trim()),s.fillLineDashOffset&&o.setAttribute(\"stroke-dashoffset\",`${s.fillLineDashOffset}`),o}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}opsToPath(t,e){return this.gen.opsToPath(t,e)}line(t,e,s,n,o){const a=this.gen.line(t,e,s,n,o);return this.draw(a)}rectangle(t,e,s,n,o){const a=this.gen.rectangle(t,e,s,n,o);return this.draw(a)}ellipse(t,e,s,n,o){const a=this.gen.ellipse(t,e,s,n,o);return this.draw(a)}circle(t,e,s,n){const o=this.gen.circle(t,e,s,n);return this.draw(o)}linearPath(t,e){const s=this.gen.linearPath(t,e);return this.draw(s)}polygon(t,e){const s=this.gen.polygon(t,e);return this.draw(s)}arc(t,e,s,n,o,a,h=!1,r){const i=this.gen.arc(t,e,s,n,o,a,h,r);return this.draw(i)}curve(t,e){const s=this.gen.curve(t,e);return this.draw(s)}path(t,e){const s=this.gen.path(t,e);return this.draw(s)}}var at={canvas:(t,e)=>new st(t,e),svg:(t,e)=>new ot(t,e),generator:t=>new et(t),newSeed:()=>et.newSeed()};export{at as default};\n", "import {\n  getSubGraphTitleMargins\n} from \"./chunk-K557N5IZ.mjs\";\nimport {\n  getIconSVG\n} from \"./chunk-H2D2JQ3I.mjs\";\nimport {\n  createText\n} from \"./chunk-C3MQ5ANM.mjs\";\nimport {\n  calculateTextWidth,\n  decodeEntities,\n  handleUndefinedAttr,\n  parseFontSize\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  common_default,\n  defaultConfig_default,\n  evaluate,\n  getConfig,\n  getConfig2,\n  hasKatex,\n  log,\n  parseGenericTypes,\n  renderKatex,\n  sanitizeText,\n  sanitizeText2\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/rendering-elements/shapes/util.ts\nimport { select } from \"d3\";\nvar labelHelper = /* @__PURE__ */ __name(async (parent, node, _classes) => {\n  let cssClasses;\n  const useHtmlLabels = node.useHtmlLabels || evaluate(getConfig2()?.htmlLabels);\n  if (!_classes) {\n    cssClasses = \"node default\";\n  } else {\n    cssClasses = _classes;\n  }\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", cssClasses).attr(\"id\", node.domId || node.id);\n  const labelEl = shapeSvg.insert(\"g\").attr(\"class\", \"label\").attr(\"style\", handleUndefinedAttr(node.labelStyle));\n  let label;\n  if (node.label === void 0) {\n    label = \"\";\n  } else {\n    label = typeof node.label === \"string\" ? node.label : node.label[0];\n  }\n  const text2 = await createText(labelEl, sanitizeText(decodeEntities(label), getConfig2()), {\n    useHtmlLabels,\n    width: node.width || getConfig2().flowchart?.wrappingWidth,\n    // @ts-expect-error -- This is currently not used. Should this be `classes` instead?\n    cssClasses: \"markdown-node-label\",\n    style: node.labelStyle,\n    addSvgBackground: !!node.icon || !!node.img\n  });\n  let bbox = text2.getBBox();\n  const halfPadding = (node?.padding ?? 0) / 2;\n  if (useHtmlLabels) {\n    const div = text2.children[0];\n    const dv = select(text2);\n    const images = div.getElementsByTagName(\"img\");\n    if (images) {\n      const noImgText = label.replace(/<img[^>]*>/g, \"\").trim() === \"\";\n      await Promise.all(\n        [...images].map(\n          (img) => new Promise((res) => {\n            function setupImage() {\n              img.style.display = \"flex\";\n              img.style.flexDirection = \"column\";\n              if (noImgText) {\n                const bodyFontSize = getConfig2().fontSize ? getConfig2().fontSize : window.getComputedStyle(document.body).fontSize;\n                const enlargingFactor = 5;\n                const [parsedBodyFontSize = defaultConfig_default.fontSize] = parseFontSize(bodyFontSize);\n                const width = parsedBodyFontSize * enlargingFactor + \"px\";\n                img.style.minWidth = width;\n                img.style.maxWidth = width;\n              } else {\n                img.style.width = \"100%\";\n              }\n              res(img);\n            }\n            __name(setupImage, \"setupImage\");\n            setTimeout(() => {\n              if (img.complete) {\n                setupImage();\n              }\n            });\n            img.addEventListener(\"error\", setupImage);\n            img.addEventListener(\"load\", setupImage);\n          })\n        )\n      );\n    }\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  if (useHtmlLabels) {\n    labelEl.attr(\"transform\", \"translate(\" + -bbox.width / 2 + \", \" + -bbox.height / 2 + \")\");\n  } else {\n    labelEl.attr(\"transform\", \"translate(0, \" + -bbox.height / 2 + \")\");\n  }\n  if (node.centerLabel) {\n    labelEl.attr(\"transform\", \"translate(\" + -bbox.width / 2 + \", \" + -bbox.height / 2 + \")\");\n  }\n  labelEl.insert(\"rect\", \":first-child\");\n  return { shapeSvg, bbox, halfPadding, label: labelEl };\n}, \"labelHelper\");\nvar insertLabel = /* @__PURE__ */ __name(async (parent, label, options) => {\n  const useHtmlLabels = options.useHtmlLabels || evaluate(getConfig2()?.flowchart?.htmlLabels);\n  const labelEl = parent.insert(\"g\").attr(\"class\", \"label\").attr(\"style\", options.labelStyle || \"\");\n  const text2 = await createText(labelEl, sanitizeText(decodeEntities(label), getConfig2()), {\n    useHtmlLabels,\n    width: options.width || getConfig2()?.flowchart?.wrappingWidth,\n    style: options.labelStyle,\n    addSvgBackground: !!options.icon || !!options.img\n  });\n  let bbox = text2.getBBox();\n  const halfPadding = options.padding / 2;\n  if (evaluate(getConfig2()?.flowchart?.htmlLabels)) {\n    const div = text2.children[0];\n    const dv = select(text2);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  if (useHtmlLabels) {\n    labelEl.attr(\"transform\", \"translate(\" + -bbox.width / 2 + \", \" + -bbox.height / 2 + \")\");\n  } else {\n    labelEl.attr(\"transform\", \"translate(0, \" + -bbox.height / 2 + \")\");\n  }\n  if (options.centerLabel) {\n    labelEl.attr(\"transform\", \"translate(\" + -bbox.width / 2 + \", \" + -bbox.height / 2 + \")\");\n  }\n  labelEl.insert(\"rect\", \":first-child\");\n  return { shapeSvg: parent, bbox, halfPadding, label: labelEl };\n}, \"insertLabel\");\nvar updateNodeBounds = /* @__PURE__ */ __name((node, element) => {\n  const bbox = element.node().getBBox();\n  node.width = bbox.width;\n  node.height = bbox.height;\n}, \"updateNodeBounds\");\nvar getNodeClasses = /* @__PURE__ */ __name((node, extra) => (node.look === \"handDrawn\" ? \"rough-node\" : \"node\") + \" \" + node.cssClasses + \" \" + (extra || \"\"), \"getNodeClasses\");\nfunction createPathFromPoints(points) {\n  const pointStrings = points.map((p, i) => `${i === 0 ? \"M\" : \"L\"}${p.x},${p.y}`);\n  pointStrings.push(\"Z\");\n  return pointStrings.join(\" \");\n}\n__name(createPathFromPoints, \"createPathFromPoints\");\nfunction generateFullSineWavePoints(x1, y1, x2, y2, amplitude, numCycles) {\n  const points = [];\n  const steps = 50;\n  const deltaX = x2 - x1;\n  const deltaY = y2 - y1;\n  const cycleLength = deltaX / numCycles;\n  const frequency = 2 * Math.PI / cycleLength;\n  const midY = y1 + deltaY / 2;\n  for (let i = 0; i <= steps; i++) {\n    const t = i / steps;\n    const x = x1 + t * deltaX;\n    const y = midY + amplitude * Math.sin(frequency * (x - x1));\n    points.push({ x, y });\n  }\n  return points;\n}\n__name(generateFullSineWavePoints, \"generateFullSineWavePoints\");\nfunction generateCirclePoints(centerX, centerY, radius, numPoints, startAngle, endAngle) {\n  const points = [];\n  const startAngleRad = startAngle * Math.PI / 180;\n  const endAngleRad = endAngle * Math.PI / 180;\n  const angleRange = endAngleRad - startAngleRad;\n  const angleStep = angleRange / (numPoints - 1);\n  for (let i = 0; i < numPoints; i++) {\n    const angle = startAngleRad + i * angleStep;\n    const x = centerX + radius * Math.cos(angle);\n    const y = centerY + radius * Math.sin(angle);\n    points.push({ x: -x, y: -y });\n  }\n  return points;\n}\n__name(generateCirclePoints, \"generateCirclePoints\");\n\n// src/rendering-util/rendering-elements/clusters.js\nimport { select as select3 } from \"d3\";\nimport rough from \"roughjs\";\n\n// src/rendering-util/rendering-elements/intersect/intersect-rect.js\nvar intersectRect = /* @__PURE__ */ __name((node, point) => {\n  var x = node.x;\n  var y = node.y;\n  var dx = point.x - x;\n  var dy = point.y - y;\n  var w = node.width / 2;\n  var h = node.height / 2;\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = dy === 0 ? 0 : h * dx / dy;\n    sy = h;\n  } else {\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = dx === 0 ? 0 : w * dy / dx;\n  }\n  return { x: x + sx, y: y + sy };\n}, \"intersectRect\");\nvar intersect_rect_default = intersectRect;\n\n// src/rendering-util/rendering-elements/createLabel.js\nimport { select as select2 } from \"d3\";\nfunction applyStyle(dom, styleFn) {\n  if (styleFn) {\n    dom.attr(\"style\", styleFn);\n  }\n}\n__name(applyStyle, \"applyStyle\");\nasync function addHtmlLabel(node) {\n  const fo = select2(document.createElementNS(\"http://www.w3.org/2000/svg\", \"foreignObject\"));\n  const div = fo.append(\"xhtml:div\");\n  let label = node.label;\n  if (node.label && hasKatex(node.label)) {\n    label = await renderKatex(node.label.replace(common_default.lineBreakRegex, \"\\n\"), getConfig2());\n  }\n  const labelClass = node.isNode ? \"nodeLabel\" : \"edgeLabel\";\n  div.html(\n    '<span class=\"' + labelClass + '\" ' + (node.labelStyle ? 'style=\"' + node.labelStyle + '\"' : \"\") + // codeql [js/html-constructed-from-input] : false positive\n    \">\" + label + \"</span>\"\n  );\n  applyStyle(div, node.labelStyle);\n  div.style(\"display\", \"inline-block\");\n  div.style(\"padding-right\", \"1px\");\n  div.style(\"white-space\", \"nowrap\");\n  div.attr(\"xmlns\", \"http://www.w3.org/1999/xhtml\");\n  return fo.node();\n}\n__name(addHtmlLabel, \"addHtmlLabel\");\nvar createLabel = /* @__PURE__ */ __name(async (_vertexText, style, isTitle, isNode) => {\n  let vertexText = _vertexText || \"\";\n  if (typeof vertexText === \"object\") {\n    vertexText = vertexText[0];\n  }\n  if (evaluate(getConfig2().flowchart.htmlLabels)) {\n    vertexText = vertexText.replace(/\\\\n|\\n/g, \"<br />\");\n    log.info(\"vertexText\" + vertexText);\n    const node = {\n      isNode,\n      label: decodeEntities(vertexText).replace(\n        /fa[blrs]?:fa-[\\w-]+/g,\n        (s) => `<i class='${s.replace(\":\", \" \")}'></i>`\n      ),\n      labelStyle: style ? style.replace(\"fill:\", \"color:\") : style\n    };\n    let vertexNode = await addHtmlLabel(node);\n    return vertexNode;\n  } else {\n    const svgLabel = document.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n    svgLabel.setAttribute(\"style\", style.replace(\"color:\", \"fill:\"));\n    let rows = [];\n    if (typeof vertexText === \"string\") {\n      rows = vertexText.split(/\\\\n|\\n|<br\\s*\\/?>/gi);\n    } else if (Array.isArray(vertexText)) {\n      rows = vertexText;\n    } else {\n      rows = [];\n    }\n    for (const row of rows) {\n      const tspan = document.createElementNS(\"http://www.w3.org/2000/svg\", \"tspan\");\n      tspan.setAttributeNS(\"http://www.w3.org/XML/1998/namespace\", \"xml:space\", \"preserve\");\n      tspan.setAttribute(\"dy\", \"1em\");\n      tspan.setAttribute(\"x\", \"0\");\n      if (isTitle) {\n        tspan.setAttribute(\"class\", \"title-row\");\n      } else {\n        tspan.setAttribute(\"class\", \"row\");\n      }\n      tspan.textContent = row.trim();\n      svgLabel.appendChild(tspan);\n    }\n    return svgLabel;\n  }\n}, \"createLabel\");\nvar createLabel_default = createLabel;\n\n// src/rendering-util/rendering-elements/shapes/roundedRectPath.ts\nvar createRoundedRectPathD = /* @__PURE__ */ __name((x, y, totalWidth, totalHeight, radius) => [\n  \"M\",\n  x + radius,\n  y,\n  // Move to the first point\n  \"H\",\n  x + totalWidth - radius,\n  // Draw horizontal line to the beginning of the right corner\n  \"A\",\n  radius,\n  radius,\n  0,\n  0,\n  1,\n  x + totalWidth,\n  y + radius,\n  // Draw arc to the right top corner\n  \"V\",\n  y + totalHeight - radius,\n  // Draw vertical line down to the beginning of the right bottom corner\n  \"A\",\n  radius,\n  radius,\n  0,\n  0,\n  1,\n  x + totalWidth - radius,\n  y + totalHeight,\n  // Draw arc to the right bottom corner\n  \"H\",\n  x + radius,\n  // Draw horizontal line to the beginning of the left bottom corner\n  \"A\",\n  radius,\n  radius,\n  0,\n  0,\n  1,\n  x,\n  y + totalHeight - radius,\n  // Draw arc to the left bottom corner\n  \"V\",\n  y + radius,\n  // Draw vertical line up to the beginning of the left top corner\n  \"A\",\n  radius,\n  radius,\n  0,\n  0,\n  1,\n  x + radius,\n  y,\n  // Draw arc to the left top corner\n  \"Z\"\n  // Close the path\n].join(\" \"), \"createRoundedRectPathD\");\n\n// src/rendering-util/rendering-elements/shapes/handDrawnShapeStyles.ts\nvar solidStateFill = /* @__PURE__ */ __name((color) => {\n  const { handDrawnSeed } = getConfig2();\n  return {\n    fill: color,\n    hachureAngle: 120,\n    // angle of hachure,\n    hachureGap: 4,\n    fillWeight: 2,\n    roughness: 0.7,\n    stroke: color,\n    seed: handDrawnSeed\n  };\n}, \"solidStateFill\");\nvar compileStyles = /* @__PURE__ */ __name((node) => {\n  const stylesMap = styles2Map([...node.cssCompiledStyles || [], ...node.cssStyles || []]);\n  return { stylesMap, stylesArray: [...stylesMap] };\n}, \"compileStyles\");\nvar styles2Map = /* @__PURE__ */ __name((styles) => {\n  const styleMap = /* @__PURE__ */ new Map();\n  styles.forEach((style) => {\n    const [key, value] = style.split(\":\");\n    styleMap.set(key.trim(), value?.trim());\n  });\n  return styleMap;\n}, \"styles2Map\");\nvar isLabelStyle = /* @__PURE__ */ __name((key) => {\n  return key === \"color\" || key === \"font-size\" || key === \"font-family\" || key === \"font-weight\" || key === \"font-style\" || key === \"text-decoration\" || key === \"text-align\" || key === \"text-transform\" || key === \"line-height\" || key === \"letter-spacing\" || key === \"word-spacing\" || key === \"text-shadow\" || key === \"text-overflow\" || key === \"white-space\" || key === \"word-wrap\" || key === \"word-break\" || key === \"overflow-wrap\" || key === \"hyphens\";\n}, \"isLabelStyle\");\nvar styles2String = /* @__PURE__ */ __name((node) => {\n  const { stylesArray } = compileStyles(node);\n  const labelStyles = [];\n  const nodeStyles = [];\n  const borderStyles = [];\n  const backgroundStyles = [];\n  stylesArray.forEach((style) => {\n    const key = style[0];\n    if (isLabelStyle(key)) {\n      labelStyles.push(style.join(\":\") + \" !important\");\n    } else {\n      nodeStyles.push(style.join(\":\") + \" !important\");\n      if (key.includes(\"stroke\")) {\n        borderStyles.push(style.join(\":\") + \" !important\");\n      }\n      if (key === \"fill\") {\n        backgroundStyles.push(style.join(\":\") + \" !important\");\n      }\n    }\n  });\n  return {\n    labelStyles: labelStyles.join(\";\"),\n    nodeStyles: nodeStyles.join(\";\"),\n    stylesArray,\n    borderStyles,\n    backgroundStyles\n  };\n}, \"styles2String\");\nvar userNodeOverrides = /* @__PURE__ */ __name((node, options) => {\n  const { themeVariables, handDrawnSeed } = getConfig2();\n  const { nodeBorder, mainBkg } = themeVariables;\n  const { stylesMap } = compileStyles(node);\n  const result = Object.assign(\n    {\n      roughness: 0.7,\n      fill: stylesMap.get(\"fill\") || mainBkg,\n      fillStyle: \"hachure\",\n      // solid fill\n      fillWeight: 4,\n      hachureGap: 5.2,\n      stroke: stylesMap.get(\"stroke\") || nodeBorder,\n      seed: handDrawnSeed,\n      strokeWidth: stylesMap.get(\"stroke-width\")?.replace(\"px\", \"\") || 1.3,\n      fillLineDash: [0, 0]\n    },\n    options\n  );\n  return result;\n}, \"userNodeOverrides\");\n\n// src/rendering-util/rendering-elements/clusters.js\nvar rect = /* @__PURE__ */ __name(async (parent, node) => {\n  log.info(\"Creating subgraph rect for \", node.id, node);\n  const siteConfig = getConfig2();\n  const { themeVariables, handDrawnSeed } = siteConfig;\n  const { clusterBkg, clusterBorder } = themeVariables;\n  const { labelStyles, nodeStyles, borderStyles, backgroundStyles } = styles2String(node);\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"cluster \" + node.cssClasses).attr(\"id\", node.id).attr(\"data-look\", node.look);\n  const useHtmlLabels = evaluate(siteConfig.flowchart.htmlLabels);\n  const labelEl = shapeSvg.insert(\"g\").attr(\"class\", \"cluster-label \");\n  const text2 = await createText(labelEl, node.label, {\n    style: node.labelStyle,\n    useHtmlLabels,\n    isNode: true\n  });\n  let bbox = text2.getBBox();\n  if (evaluate(siteConfig.flowchart.htmlLabels)) {\n    const div = text2.children[0];\n    const dv = select3(text2);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  const width = node.width <= bbox.width + node.padding ? bbox.width + node.padding : node.width;\n  if (node.width <= bbox.width + node.padding) {\n    node.diff = (width - node.width) / 2 - node.padding;\n  } else {\n    node.diff = -node.padding;\n  }\n  const height = node.height;\n  const x = node.x - width / 2;\n  const y = node.y - height / 2;\n  log.trace(\"Data \", node, JSON.stringify(node));\n  let rect2;\n  if (node.look === \"handDrawn\") {\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {\n      roughness: 0.7,\n      fill: clusterBkg,\n      // fill: 'red',\n      stroke: clusterBorder,\n      fillWeight: 3,\n      seed: handDrawnSeed\n    });\n    const roughNode = rc.path(createRoundedRectPathD(x, y, width, height, 0), options);\n    rect2 = shapeSvg.insert(() => {\n      log.debug(\"Rough node insert CXC\", roughNode);\n      return roughNode;\n    }, \":first-child\");\n    rect2.select(\"path:nth-child(2)\").attr(\"style\", borderStyles.join(\";\"));\n    rect2.select(\"path\").attr(\"style\", backgroundStyles.join(\";\").replace(\"fill\", \"stroke\"));\n  } else {\n    rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n    rect2.attr(\"style\", nodeStyles).attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height);\n  }\n  const { subGraphTitleTopMargin } = getSubGraphTitleMargins(siteConfig);\n  labelEl.attr(\n    \"transform\",\n    // This puts the label on top of the box instead of inside it\n    `translate(${node.x - bbox.width / 2}, ${node.y - node.height / 2 + subGraphTitleTopMargin})`\n  );\n  if (labelStyles) {\n    const span = labelEl.select(\"span\");\n    if (span) {\n      span.attr(\"style\", labelStyles);\n    }\n  }\n  const rectBox = rect2.node().getBBox();\n  node.offsetX = 0;\n  node.width = rectBox.width;\n  node.height = rectBox.height;\n  node.offsetY = bbox.height - node.padding / 2;\n  node.intersect = function(point) {\n    return intersect_rect_default(node, point);\n  };\n  return { cluster: shapeSvg, labelBBox: bbox };\n}, \"rect\");\nvar noteGroup = /* @__PURE__ */ __name((parent, node) => {\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"note-cluster\").attr(\"id\", node.id);\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  const padding = 0 * node.padding;\n  const halfPadding = padding / 2;\n  rect2.attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"x\", node.x - node.width / 2 - halfPadding).attr(\"y\", node.y - node.height / 2 - halfPadding).attr(\"width\", node.width + padding).attr(\"height\", node.height + padding).attr(\"fill\", \"none\");\n  const rectBox = rect2.node().getBBox();\n  node.width = rectBox.width;\n  node.height = rectBox.height;\n  node.intersect = function(point) {\n    return intersect_rect_default(node, point);\n  };\n  return { cluster: shapeSvg, labelBBox: { width: 0, height: 0 } };\n}, \"noteGroup\");\nvar roundedWithTitle = /* @__PURE__ */ __name(async (parent, node) => {\n  const siteConfig = getConfig2();\n  const { themeVariables, handDrawnSeed } = siteConfig;\n  const { altBackground, compositeBackground, compositeTitleBackground, nodeBorder } = themeVariables;\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", node.cssClasses).attr(\"id\", node.id).attr(\"data-id\", node.id).attr(\"data-look\", node.look);\n  const outerRectG = shapeSvg.insert(\"g\", \":first-child\");\n  const label = shapeSvg.insert(\"g\").attr(\"class\", \"cluster-label\");\n  let innerRect = shapeSvg.append(\"rect\");\n  const text2 = label.node().appendChild(await createLabel_default(node.label, node.labelStyle, void 0, true));\n  let bbox = text2.getBBox();\n  if (evaluate(siteConfig.flowchart.htmlLabels)) {\n    const div = text2.children[0];\n    const dv = select3(text2);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  const padding = 0 * node.padding;\n  const halfPadding = padding / 2;\n  const width = (node.width <= bbox.width + node.padding ? bbox.width + node.padding : node.width) + padding;\n  if (node.width <= bbox.width + node.padding) {\n    node.diff = (width - node.width) / 2 - node.padding;\n  } else {\n    node.diff = -node.padding;\n  }\n  const height = node.height + padding;\n  const innerHeight = node.height + padding - bbox.height - 6;\n  const x = node.x - width / 2;\n  const y = node.y - height / 2;\n  node.width = width;\n  const innerY = node.y - node.height / 2 - halfPadding + bbox.height + 2;\n  let rect2;\n  if (node.look === \"handDrawn\") {\n    const isAlt = node.cssClasses.includes(\"statediagram-cluster-alt\");\n    const rc = rough.svg(shapeSvg);\n    const roughOuterNode = node.rx || node.ry ? rc.path(createRoundedRectPathD(x, y, width, height, 10), {\n      roughness: 0.7,\n      fill: compositeTitleBackground,\n      fillStyle: \"solid\",\n      stroke: nodeBorder,\n      seed: handDrawnSeed\n    }) : rc.rectangle(x, y, width, height, { seed: handDrawnSeed });\n    rect2 = shapeSvg.insert(() => roughOuterNode, \":first-child\");\n    const roughInnerNode = rc.rectangle(x, innerY, width, innerHeight, {\n      fill: isAlt ? altBackground : compositeBackground,\n      fillStyle: isAlt ? \"hachure\" : \"solid\",\n      stroke: nodeBorder,\n      seed: handDrawnSeed\n    });\n    rect2 = shapeSvg.insert(() => roughOuterNode, \":first-child\");\n    innerRect = shapeSvg.insert(() => roughInnerNode);\n  } else {\n    rect2 = outerRectG.insert(\"rect\", \":first-child\");\n    const outerRectClass = \"outer\";\n    rect2.attr(\"class\", outerRectClass).attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height).attr(\"data-look\", node.look);\n    innerRect.attr(\"class\", \"inner\").attr(\"x\", x).attr(\"y\", innerY).attr(\"width\", width).attr(\"height\", innerHeight);\n  }\n  label.attr(\n    \"transform\",\n    `translate(${node.x - bbox.width / 2}, ${y + 1 - (evaluate(siteConfig.flowchart.htmlLabels) ? 0 : 3)})`\n  );\n  const rectBox = rect2.node().getBBox();\n  node.height = rectBox.height;\n  node.offsetX = 0;\n  node.offsetY = bbox.height - node.padding / 2;\n  node.labelBBox = bbox;\n  node.intersect = function(point) {\n    return intersect_rect_default(node, point);\n  };\n  return { cluster: shapeSvg, labelBBox: bbox };\n}, \"roundedWithTitle\");\nvar kanbanSection = /* @__PURE__ */ __name(async (parent, node) => {\n  log.info(\"Creating subgraph rect for \", node.id, node);\n  const siteConfig = getConfig2();\n  const { themeVariables, handDrawnSeed } = siteConfig;\n  const { clusterBkg, clusterBorder } = themeVariables;\n  const { labelStyles, nodeStyles, borderStyles, backgroundStyles } = styles2String(node);\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"cluster \" + node.cssClasses).attr(\"id\", node.id).attr(\"data-look\", node.look);\n  const useHtmlLabels = evaluate(siteConfig.flowchart.htmlLabels);\n  const labelEl = shapeSvg.insert(\"g\").attr(\"class\", \"cluster-label \");\n  const text2 = await createText(labelEl, node.label, {\n    style: node.labelStyle,\n    useHtmlLabels,\n    isNode: true,\n    width: node.width\n  });\n  let bbox = text2.getBBox();\n  if (evaluate(siteConfig.flowchart.htmlLabels)) {\n    const div = text2.children[0];\n    const dv = select3(text2);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  const width = node.width <= bbox.width + node.padding ? bbox.width + node.padding : node.width;\n  if (node.width <= bbox.width + node.padding) {\n    node.diff = (width - node.width) / 2 - node.padding;\n  } else {\n    node.diff = -node.padding;\n  }\n  const height = node.height;\n  const x = node.x - width / 2;\n  const y = node.y - height / 2;\n  log.trace(\"Data \", node, JSON.stringify(node));\n  let rect2;\n  if (node.look === \"handDrawn\") {\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {\n      roughness: 0.7,\n      fill: clusterBkg,\n      // fill: 'red',\n      stroke: clusterBorder,\n      fillWeight: 4,\n      seed: handDrawnSeed\n    });\n    const roughNode = rc.path(createRoundedRectPathD(x, y, width, height, node.rx), options);\n    rect2 = shapeSvg.insert(() => {\n      log.debug(\"Rough node insert CXC\", roughNode);\n      return roughNode;\n    }, \":first-child\");\n    rect2.select(\"path:nth-child(2)\").attr(\"style\", borderStyles.join(\";\"));\n    rect2.select(\"path\").attr(\"style\", backgroundStyles.join(\";\").replace(\"fill\", \"stroke\"));\n  } else {\n    rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n    rect2.attr(\"style\", nodeStyles).attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height);\n  }\n  const { subGraphTitleTopMargin } = getSubGraphTitleMargins(siteConfig);\n  labelEl.attr(\n    \"transform\",\n    // This puts the label on top of the box instead of inside it\n    `translate(${node.x - bbox.width / 2}, ${node.y - node.height / 2 + subGraphTitleTopMargin})`\n  );\n  if (labelStyles) {\n    const span = labelEl.select(\"span\");\n    if (span) {\n      span.attr(\"style\", labelStyles);\n    }\n  }\n  const rectBox = rect2.node().getBBox();\n  node.offsetX = 0;\n  node.width = rectBox.width;\n  node.height = rectBox.height;\n  node.offsetY = bbox.height - node.padding / 2;\n  node.intersect = function(point) {\n    return intersect_rect_default(node, point);\n  };\n  return { cluster: shapeSvg, labelBBox: bbox };\n}, \"kanbanSection\");\nvar divider = /* @__PURE__ */ __name((parent, node) => {\n  const siteConfig = getConfig2();\n  const { themeVariables, handDrawnSeed } = siteConfig;\n  const { nodeBorder } = themeVariables;\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", node.cssClasses).attr(\"id\", node.id).attr(\"data-look\", node.look);\n  const outerRectG = shapeSvg.insert(\"g\", \":first-child\");\n  const padding = 0 * node.padding;\n  const width = node.width + padding;\n  node.diff = -node.padding;\n  const height = node.height + padding;\n  const x = node.x - width / 2;\n  const y = node.y - height / 2;\n  node.width = width;\n  let rect2;\n  if (node.look === \"handDrawn\") {\n    const rc = rough.svg(shapeSvg);\n    const roughOuterNode = rc.rectangle(x, y, width, height, {\n      fill: \"lightgrey\",\n      roughness: 0.5,\n      strokeLineDash: [5],\n      stroke: nodeBorder,\n      seed: handDrawnSeed\n    });\n    rect2 = shapeSvg.insert(() => roughOuterNode, \":first-child\");\n  } else {\n    rect2 = outerRectG.insert(\"rect\", \":first-child\");\n    const outerRectClass = \"divider\";\n    rect2.attr(\"class\", outerRectClass).attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height).attr(\"data-look\", node.look);\n  }\n  const rectBox = rect2.node().getBBox();\n  node.height = rectBox.height;\n  node.offsetX = 0;\n  node.offsetY = 0;\n  node.intersect = function(point) {\n    return intersect_rect_default(node, point);\n  };\n  return { cluster: shapeSvg, labelBBox: {} };\n}, \"divider\");\nvar squareRect = rect;\nvar shapes = {\n  rect,\n  squareRect,\n  roundedWithTitle,\n  noteGroup,\n  divider,\n  kanbanSection\n};\nvar clusterElems = /* @__PURE__ */ new Map();\nvar insertCluster = /* @__PURE__ */ __name(async (elem, node) => {\n  const shape = node.shape || \"rect\";\n  const cluster = await shapes[shape](elem, node);\n  clusterElems.set(node.id, cluster);\n  return cluster;\n}, \"insertCluster\");\nvar clear = /* @__PURE__ */ __name(() => {\n  clusterElems = /* @__PURE__ */ new Map();\n}, \"clear\");\n\n// src/rendering-util/rendering-elements/intersect/intersect-node.js\nfunction intersectNode(node, point) {\n  return node.intersect(point);\n}\n__name(intersectNode, \"intersectNode\");\nvar intersect_node_default = intersectNode;\n\n// src/rendering-util/rendering-elements/intersect/intersect-ellipse.js\nfunction intersectEllipse(node, rx, ry, point) {\n  var cx = node.x;\n  var cy = node.y;\n  var px = cx - point.x;\n  var py = cy - point.y;\n  var det = Math.sqrt(rx * rx * py * py + ry * ry * px * px);\n  var dx = Math.abs(rx * ry * px / det);\n  if (point.x < cx) {\n    dx = -dx;\n  }\n  var dy = Math.abs(rx * ry * py / det);\n  if (point.y < cy) {\n    dy = -dy;\n  }\n  return { x: cx + dx, y: cy + dy };\n}\n__name(intersectEllipse, \"intersectEllipse\");\nvar intersect_ellipse_default = intersectEllipse;\n\n// src/rendering-util/rendering-elements/intersect/intersect-circle.js\nfunction intersectCircle(node, rx, point) {\n  return intersect_ellipse_default(node, rx, rx, point);\n}\n__name(intersectCircle, \"intersectCircle\");\nvar intersect_circle_default = intersectCircle;\n\n// src/rendering-util/rendering-elements/intersect/intersect-line.js\nfunction intersectLine(p1, p2, q1, q2) {\n  var a1, a2, b1, b2, c1, c2;\n  var r1, r2, r3, r4;\n  var denom, offset, num;\n  var x, y;\n  a1 = p2.y - p1.y;\n  b1 = p1.x - p2.x;\n  c1 = p2.x * p1.y - p1.x * p2.y;\n  r3 = a1 * q1.x + b1 * q1.y + c1;\n  r4 = a1 * q2.x + b1 * q2.y + c1;\n  if (r3 !== 0 && r4 !== 0 && sameSign(r3, r4)) {\n    return;\n  }\n  a2 = q2.y - q1.y;\n  b2 = q1.x - q2.x;\n  c2 = q2.x * q1.y - q1.x * q2.y;\n  r1 = a2 * p1.x + b2 * p1.y + c2;\n  r2 = a2 * p2.x + b2 * p2.y + c2;\n  if (r1 !== 0 && r2 !== 0 && sameSign(r1, r2)) {\n    return;\n  }\n  denom = a1 * b2 - a2 * b1;\n  if (denom === 0) {\n    return;\n  }\n  offset = Math.abs(denom / 2);\n  num = b1 * c2 - b2 * c1;\n  x = num < 0 ? (num - offset) / denom : (num + offset) / denom;\n  num = a2 * c1 - a1 * c2;\n  y = num < 0 ? (num - offset) / denom : (num + offset) / denom;\n  return { x, y };\n}\n__name(intersectLine, \"intersectLine\");\nfunction sameSign(r1, r2) {\n  return r1 * r2 > 0;\n}\n__name(sameSign, \"sameSign\");\nvar intersect_line_default = intersectLine;\n\n// src/rendering-util/rendering-elements/intersect/intersect-polygon.js\nfunction intersectPolygon(node, polyPoints, point) {\n  let x1 = node.x;\n  let y1 = node.y;\n  let intersections = [];\n  let minX = Number.POSITIVE_INFINITY;\n  let minY = Number.POSITIVE_INFINITY;\n  if (typeof polyPoints.forEach === \"function\") {\n    polyPoints.forEach(function(entry) {\n      minX = Math.min(minX, entry.x);\n      minY = Math.min(minY, entry.y);\n    });\n  } else {\n    minX = Math.min(minX, polyPoints.x);\n    minY = Math.min(minY, polyPoints.y);\n  }\n  let left = x1 - node.width / 2 - minX;\n  let top = y1 - node.height / 2 - minY;\n  for (let i = 0; i < polyPoints.length; i++) {\n    let p1 = polyPoints[i];\n    let p2 = polyPoints[i < polyPoints.length - 1 ? i + 1 : 0];\n    let intersect = intersect_line_default(\n      node,\n      point,\n      { x: left + p1.x, y: top + p1.y },\n      { x: left + p2.x, y: top + p2.y }\n    );\n    if (intersect) {\n      intersections.push(intersect);\n    }\n  }\n  if (!intersections.length) {\n    return node;\n  }\n  if (intersections.length > 1) {\n    intersections.sort(function(p, q) {\n      let pdx = p.x - point.x;\n      let pdy = p.y - point.y;\n      let distp = Math.sqrt(pdx * pdx + pdy * pdy);\n      let qdx = q.x - point.x;\n      let qdy = q.y - point.y;\n      let distq = Math.sqrt(qdx * qdx + qdy * qdy);\n      return distp < distq ? -1 : distp === distq ? 0 : 1;\n    });\n  }\n  return intersections[0];\n}\n__name(intersectPolygon, \"intersectPolygon\");\nvar intersect_polygon_default = intersectPolygon;\n\n// src/rendering-util/rendering-elements/intersect/index.js\nvar intersect_default = {\n  node: intersect_node_default,\n  circle: intersect_circle_default,\n  ellipse: intersect_ellipse_default,\n  polygon: intersect_polygon_default,\n  rect: intersect_rect_default\n};\n\n// src/rendering-util/rendering-elements/shapes/anchor.ts\nimport rough2 from \"roughjs\";\nfunction anchor(parent, node) {\n  const { labelStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const classes = getNodeClasses(node);\n  let cssClasses = classes;\n  if (!classes) {\n    cssClasses = \"anchor\";\n  }\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", cssClasses).attr(\"id\", node.domId || node.id);\n  const radius = 1;\n  const { cssStyles } = node;\n  const rc = rough2.svg(shapeSvg);\n  const options = userNodeOverrides(node, { fill: \"black\", stroke: \"none\", fillStyle: \"solid\" });\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n  }\n  const roughNode = rc.circle(0, 0, radius * 2, options);\n  const circleElem = shapeSvg.insert(() => roughNode, \":first-child\");\n  circleElem.attr(\"class\", \"anchor\").attr(\"style\", handleUndefinedAttr(cssStyles));\n  updateNodeBounds(node, circleElem);\n  node.intersect = function(point) {\n    log.info(\"Circle intersect\", node, radius, point);\n    return intersect_default.circle(node, radius, point);\n  };\n  return shapeSvg;\n}\n__name(anchor, \"anchor\");\n\n// src/rendering-util/rendering-elements/shapes/bowTieRect.ts\nimport rough3 from \"roughjs\";\nfunction generateArcPoints(x1, y1, x2, y2, rx, ry, clockwise) {\n  const numPoints = 20;\n  const midX = (x1 + x2) / 2;\n  const midY = (y1 + y2) / 2;\n  const angle = Math.atan2(y2 - y1, x2 - x1);\n  const dx = (x2 - x1) / 2;\n  const dy = (y2 - y1) / 2;\n  const transformedX = dx / rx;\n  const transformedY = dy / ry;\n  const distance = Math.sqrt(transformedX ** 2 + transformedY ** 2);\n  if (distance > 1) {\n    throw new Error(\"The given radii are too small to create an arc between the points.\");\n  }\n  const scaledCenterDistance = Math.sqrt(1 - distance ** 2);\n  const centerX = midX + scaledCenterDistance * ry * Math.sin(angle) * (clockwise ? -1 : 1);\n  const centerY = midY - scaledCenterDistance * rx * Math.cos(angle) * (clockwise ? -1 : 1);\n  const startAngle = Math.atan2((y1 - centerY) / ry, (x1 - centerX) / rx);\n  const endAngle = Math.atan2((y2 - centerY) / ry, (x2 - centerX) / rx);\n  let angleRange = endAngle - startAngle;\n  if (clockwise && angleRange < 0) {\n    angleRange += 2 * Math.PI;\n  }\n  if (!clockwise && angleRange > 0) {\n    angleRange -= 2 * Math.PI;\n  }\n  const points = [];\n  for (let i = 0; i < numPoints; i++) {\n    const t = i / (numPoints - 1);\n    const angle2 = startAngle + t * angleRange;\n    const x = centerX + rx * Math.cos(angle2);\n    const y = centerY + ry * Math.sin(angle2);\n    points.push({ x, y });\n  }\n  return points;\n}\n__name(generateArcPoints, \"generateArcPoints\");\nasync function bowTieRect(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + node.padding + 20;\n  const h = bbox.height + node.padding;\n  const ry = h / 2;\n  const rx = ry / (2.5 + h / 50);\n  const { cssStyles } = node;\n  const points = [\n    { x: w / 2, y: -h / 2 },\n    { x: -w / 2, y: -h / 2 },\n    ...generateArcPoints(-w / 2, -h / 2, -w / 2, h / 2, rx, ry, false),\n    { x: w / 2, y: h / 2 },\n    ...generateArcPoints(w / 2, h / 2, w / 2, -h / 2, rx, ry, true)\n  ];\n  const rc = rough3.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const bowTieRectPath = createPathFromPoints(points);\n  const bowTieRectShapePath = rc.path(bowTieRectPath, options);\n  const bowTieRectShape = shapeSvg.insert(() => bowTieRectShapePath, \":first-child\");\n  bowTieRectShape.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    bowTieRectShape.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    bowTieRectShape.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  bowTieRectShape.attr(\"transform\", `translate(${rx / 2}, 0)`);\n  updateNodeBounds(node, bowTieRectShape);\n  node.intersect = function(point) {\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(bowTieRect, \"bowTieRect\");\n\n// src/rendering-util/rendering-elements/shapes/card.ts\nimport rough4 from \"roughjs\";\n\n// src/rendering-util/rendering-elements/shapes/insertPolygonShape.ts\nfunction insertPolygonShape(parent, w, h, points) {\n  return parent.insert(\"polygon\", \":first-child\").attr(\n    \"points\",\n    points.map(function(d) {\n      return d.x + \",\" + d.y;\n    }).join(\" \")\n  ).attr(\"class\", \"label-container\").attr(\"transform\", \"translate(\" + -w / 2 + \",\" + h / 2 + \")\");\n}\n__name(insertPolygonShape, \"insertPolygonShape\");\n\n// src/rendering-util/rendering-elements/shapes/card.ts\nasync function card(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const h = bbox.height + node.padding;\n  const padding = 12;\n  const w = bbox.width + node.padding + padding;\n  const left = 0;\n  const right = w;\n  const top = -h;\n  const bottom = 0;\n  const points = [\n    { x: left + padding, y: top },\n    { x: right, y: top },\n    { x: right, y: bottom },\n    { x: left, y: bottom },\n    { x: left, y: top + padding },\n    { x: left + padding, y: top }\n  ];\n  let polygon;\n  const { cssStyles } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough4.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    const roughNode = rc.path(pathData, options);\n    polygon = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-w / 2}, ${h / 2})`);\n    if (cssStyles) {\n      polygon.attr(\"style\", cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n  if (nodeStyles) {\n    polygon.attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, polygon);\n  node.intersect = function(point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(card, \"card\");\n\n// src/rendering-util/rendering-elements/shapes/choice.ts\nimport rough5 from \"roughjs\";\nfunction choice(parent, node) {\n  const { nodeStyles } = styles2String(node);\n  node.label = \"\";\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", getNodeClasses(node)).attr(\"id\", node.domId ?? node.id);\n  const { cssStyles } = node;\n  const s = Math.max(28, node.width ?? 0);\n  const points = [\n    { x: 0, y: s / 2 },\n    { x: s / 2, y: 0 },\n    { x: 0, y: -s / 2 },\n    { x: -s / 2, y: 0 }\n  ];\n  const rc = rough5.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const choicePath = createPathFromPoints(points);\n  const roughNode = rc.path(choicePath, options);\n  const choiceShape = shapeSvg.insert(() => roughNode, \":first-child\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    choiceShape.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    choiceShape.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  node.width = 28;\n  node.height = 28;\n  node.intersect = function(point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(choice, \"choice\");\n\n// src/rendering-util/rendering-elements/shapes/circle.ts\nimport rough6 from \"roughjs\";\nasync function circle(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(parent, node, getNodeClasses(node));\n  const radius = bbox.width / 2 + halfPadding;\n  let circleElem;\n  const { cssStyles } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough6.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const roughNode = rc.circle(0, 0, radius * 2, options);\n    circleElem = shapeSvg.insert(() => roughNode, \":first-child\");\n    circleElem.attr(\"class\", \"basic label-container\").attr(\"style\", handleUndefinedAttr(cssStyles));\n  } else {\n    circleElem = shapeSvg.insert(\"circle\", \":first-child\").attr(\"class\", \"basic label-container\").attr(\"style\", nodeStyles).attr(\"r\", radius).attr(\"cx\", 0).attr(\"cy\", 0);\n  }\n  updateNodeBounds(node, circleElem);\n  node.intersect = function(point) {\n    log.info(\"Circle intersect\", node, radius, point);\n    return intersect_default.circle(node, radius, point);\n  };\n  return shapeSvg;\n}\n__name(circle, \"circle\");\n\n// src/rendering-util/rendering-elements/shapes/crossedCircle.ts\nimport rough7 from \"roughjs\";\nfunction createLine(r) {\n  const xAxis45 = Math.cos(Math.PI / 4);\n  const yAxis45 = Math.sin(Math.PI / 4);\n  const lineLength = r * 2;\n  const pointQ1 = { x: lineLength / 2 * xAxis45, y: lineLength / 2 * yAxis45 };\n  const pointQ2 = { x: -(lineLength / 2) * xAxis45, y: lineLength / 2 * yAxis45 };\n  const pointQ3 = { x: -(lineLength / 2) * xAxis45, y: -(lineLength / 2) * yAxis45 };\n  const pointQ4 = { x: lineLength / 2 * xAxis45, y: -(lineLength / 2) * yAxis45 };\n  return `M ${pointQ2.x},${pointQ2.y} L ${pointQ4.x},${pointQ4.y}\n                   M ${pointQ1.x},${pointQ1.y} L ${pointQ3.x},${pointQ3.y}`;\n}\n__name(createLine, \"createLine\");\nfunction crossedCircle(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  node.label = \"\";\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", getNodeClasses(node)).attr(\"id\", node.domId ?? node.id);\n  const radius = Math.max(30, node?.width ?? 0);\n  const { cssStyles } = node;\n  const rc = rough7.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const circleNode = rc.circle(0, 0, radius * 2, options);\n  const linePath = createLine(radius);\n  const lineNode = rc.path(linePath, options);\n  const crossedCircle2 = shapeSvg.insert(() => circleNode, \":first-child\");\n  crossedCircle2.insert(() => lineNode);\n  if (cssStyles && node.look !== \"handDrawn\") {\n    crossedCircle2.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    crossedCircle2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, crossedCircle2);\n  node.intersect = function(point) {\n    log.info(\"crossedCircle intersect\", node, { radius, point });\n    const pos = intersect_default.circle(node, radius, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(crossedCircle, \"crossedCircle\");\n\n// src/rendering-util/rendering-elements/shapes/curlyBraceLeft.ts\nimport rough8 from \"roughjs\";\nfunction generateCirclePoints2(centerX, centerY, radius, numPoints = 100, startAngle = 0, endAngle = 180) {\n  const points = [];\n  const startAngleRad = startAngle * Math.PI / 180;\n  const endAngleRad = endAngle * Math.PI / 180;\n  const angleRange = endAngleRad - startAngleRad;\n  const angleStep = angleRange / (numPoints - 1);\n  for (let i = 0; i < numPoints; i++) {\n    const angle = startAngleRad + i * angleStep;\n    const x = centerX + radius * Math.cos(angle);\n    const y = centerY + radius * Math.sin(angle);\n    points.push({ x: -x, y: -y });\n  }\n  return points;\n}\n__name(generateCirclePoints2, \"generateCirclePoints\");\nasync function curlyBraceLeft(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + (node.padding ?? 0);\n  const h = bbox.height + (node.padding ?? 0);\n  const radius = Math.max(5, h * 0.1);\n  const { cssStyles } = node;\n  const points = [\n    ...generateCirclePoints2(w / 2, -h / 2, radius, 30, -90, 0),\n    { x: -w / 2 - radius, y: radius },\n    ...generateCirclePoints2(w / 2 + radius * 2, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints2(w / 2 + radius * 2, radius, radius, 20, -90, -180),\n    { x: -w / 2 - radius, y: -h / 2 },\n    ...generateCirclePoints2(w / 2, h / 2, radius, 20, 0, 90)\n  ];\n  const rectPoints = [\n    { x: w / 2, y: -h / 2 - radius },\n    { x: -w / 2, y: -h / 2 - radius },\n    ...generateCirclePoints2(w / 2, -h / 2, radius, 20, -90, 0),\n    { x: -w / 2 - radius, y: -radius },\n    ...generateCirclePoints2(w / 2 + w * 0.1, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints2(w / 2 + w * 0.1, radius, radius, 20, -90, -180),\n    { x: -w / 2 - radius, y: h / 2 },\n    ...generateCirclePoints2(w / 2, h / 2, radius, 20, 0, 90),\n    { x: -w / 2, y: h / 2 + radius },\n    { x: w / 2, y: h / 2 + radius }\n  ];\n  const rc = rough8.svg(shapeSvg);\n  const options = userNodeOverrides(node, { fill: \"none\" });\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const curlyBraceLeftPath = createPathFromPoints(points);\n  const newCurlyBracePath = curlyBraceLeftPath.replace(\"Z\", \"\");\n  const curlyBraceLeftNode = rc.path(newCurlyBracePath, options);\n  const rectPath = createPathFromPoints(rectPoints);\n  const rectShape = rc.path(rectPath, { ...options });\n  const curlyBraceLeftShape = shapeSvg.insert(\"g\", \":first-child\");\n  curlyBraceLeftShape.insert(() => rectShape, \":first-child\").attr(\"stroke-opacity\", 0);\n  curlyBraceLeftShape.insert(() => curlyBraceLeftNode, \":first-child\");\n  curlyBraceLeftShape.attr(\"class\", \"text\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    curlyBraceLeftShape.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    curlyBraceLeftShape.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  curlyBraceLeftShape.attr(\"transform\", `translate(${radius}, 0)`);\n  label.attr(\n    \"transform\",\n    `translate(${-w / 2 + radius - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n  updateNodeBounds(node, curlyBraceLeftShape);\n  node.intersect = function(point) {\n    const pos = intersect_default.polygon(node, rectPoints, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(curlyBraceLeft, \"curlyBraceLeft\");\n\n// src/rendering-util/rendering-elements/shapes/curlyBraceRight.ts\nimport rough9 from \"roughjs\";\nfunction generateCirclePoints3(centerX, centerY, radius, numPoints = 100, startAngle = 0, endAngle = 180) {\n  const points = [];\n  const startAngleRad = startAngle * Math.PI / 180;\n  const endAngleRad = endAngle * Math.PI / 180;\n  const angleRange = endAngleRad - startAngleRad;\n  const angleStep = angleRange / (numPoints - 1);\n  for (let i = 0; i < numPoints; i++) {\n    const angle = startAngleRad + i * angleStep;\n    const x = centerX + radius * Math.cos(angle);\n    const y = centerY + radius * Math.sin(angle);\n    points.push({ x, y });\n  }\n  return points;\n}\n__name(generateCirclePoints3, \"generateCirclePoints\");\nasync function curlyBraceRight(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + (node.padding ?? 0);\n  const h = bbox.height + (node.padding ?? 0);\n  const radius = Math.max(5, h * 0.1);\n  const { cssStyles } = node;\n  const points = [\n    ...generateCirclePoints3(w / 2, -h / 2, radius, 20, -90, 0),\n    { x: w / 2 + radius, y: -radius },\n    ...generateCirclePoints3(w / 2 + radius * 2, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints3(w / 2 + radius * 2, radius, radius, 20, -90, -180),\n    { x: w / 2 + radius, y: h / 2 },\n    ...generateCirclePoints3(w / 2, h / 2, radius, 20, 0, 90)\n  ];\n  const rectPoints = [\n    { x: -w / 2, y: -h / 2 - radius },\n    { x: w / 2, y: -h / 2 - radius },\n    ...generateCirclePoints3(w / 2, -h / 2, radius, 20, -90, 0),\n    { x: w / 2 + radius, y: -radius },\n    ...generateCirclePoints3(w / 2 + radius * 2, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints3(w / 2 + radius * 2, radius, radius, 20, -90, -180),\n    { x: w / 2 + radius, y: h / 2 },\n    ...generateCirclePoints3(w / 2, h / 2, radius, 20, 0, 90),\n    { x: w / 2, y: h / 2 + radius },\n    { x: -w / 2, y: h / 2 + radius }\n  ];\n  const rc = rough9.svg(shapeSvg);\n  const options = userNodeOverrides(node, { fill: \"none\" });\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const curlyBraceRightPath = createPathFromPoints(points);\n  const newCurlyBracePath = curlyBraceRightPath.replace(\"Z\", \"\");\n  const curlyBraceRightNode = rc.path(newCurlyBracePath, options);\n  const rectPath = createPathFromPoints(rectPoints);\n  const rectShape = rc.path(rectPath, { ...options });\n  const curlyBraceRightShape = shapeSvg.insert(\"g\", \":first-child\");\n  curlyBraceRightShape.insert(() => rectShape, \":first-child\").attr(\"stroke-opacity\", 0);\n  curlyBraceRightShape.insert(() => curlyBraceRightNode, \":first-child\");\n  curlyBraceRightShape.attr(\"class\", \"text\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    curlyBraceRightShape.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    curlyBraceRightShape.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  curlyBraceRightShape.attr(\"transform\", `translate(${-radius}, 0)`);\n  label.attr(\n    \"transform\",\n    `translate(${-w / 2 + (node.padding ?? 0) / 2 - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n  updateNodeBounds(node, curlyBraceRightShape);\n  node.intersect = function(point) {\n    const pos = intersect_default.polygon(node, rectPoints, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(curlyBraceRight, \"curlyBraceRight\");\n\n// src/rendering-util/rendering-elements/shapes/curlyBraces.ts\nimport rough10 from \"roughjs\";\nfunction generateCirclePoints4(centerX, centerY, radius, numPoints = 100, startAngle = 0, endAngle = 180) {\n  const points = [];\n  const startAngleRad = startAngle * Math.PI / 180;\n  const endAngleRad = endAngle * Math.PI / 180;\n  const angleRange = endAngleRad - startAngleRad;\n  const angleStep = angleRange / (numPoints - 1);\n  for (let i = 0; i < numPoints; i++) {\n    const angle = startAngleRad + i * angleStep;\n    const x = centerX + radius * Math.cos(angle);\n    const y = centerY + radius * Math.sin(angle);\n    points.push({ x: -x, y: -y });\n  }\n  return points;\n}\n__name(generateCirclePoints4, \"generateCirclePoints\");\nasync function curlyBraces(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + (node.padding ?? 0);\n  const h = bbox.height + (node.padding ?? 0);\n  const radius = Math.max(5, h * 0.1);\n  const { cssStyles } = node;\n  const leftCurlyBracePoints = [\n    ...generateCirclePoints4(w / 2, -h / 2, radius, 30, -90, 0),\n    { x: -w / 2 - radius, y: radius },\n    ...generateCirclePoints4(w / 2 + radius * 2, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints4(w / 2 + radius * 2, radius, radius, 20, -90, -180),\n    { x: -w / 2 - radius, y: -h / 2 },\n    ...generateCirclePoints4(w / 2, h / 2, radius, 20, 0, 90)\n  ];\n  const rightCurlyBracePoints = [\n    ...generateCirclePoints4(-w / 2 + radius + radius / 2, -h / 2, radius, 20, -90, -180),\n    { x: w / 2 - radius / 2, y: radius },\n    ...generateCirclePoints4(-w / 2 - radius / 2, -radius, radius, 20, 0, 90),\n    ...generateCirclePoints4(-w / 2 - radius / 2, radius, radius, 20, -90, 0),\n    { x: w / 2 - radius / 2, y: -radius },\n    ...generateCirclePoints4(-w / 2 + radius + radius / 2, h / 2, radius, 30, -180, -270)\n  ];\n  const rectPoints = [\n    { x: w / 2, y: -h / 2 - radius },\n    { x: -w / 2, y: -h / 2 - radius },\n    ...generateCirclePoints4(w / 2, -h / 2, radius, 20, -90, 0),\n    { x: -w / 2 - radius, y: -radius },\n    ...generateCirclePoints4(w / 2 + radius * 2, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints4(w / 2 + radius * 2, radius, radius, 20, -90, -180),\n    { x: -w / 2 - radius, y: h / 2 },\n    ...generateCirclePoints4(w / 2, h / 2, radius, 20, 0, 90),\n    { x: -w / 2, y: h / 2 + radius },\n    { x: w / 2 - radius - radius / 2, y: h / 2 + radius },\n    ...generateCirclePoints4(-w / 2 + radius + radius / 2, -h / 2, radius, 20, -90, -180),\n    { x: w / 2 - radius / 2, y: radius },\n    ...generateCirclePoints4(-w / 2 - radius / 2, -radius, radius, 20, 0, 90),\n    ...generateCirclePoints4(-w / 2 - radius / 2, radius, radius, 20, -90, 0),\n    { x: w / 2 - radius / 2, y: -radius },\n    ...generateCirclePoints4(-w / 2 + radius + radius / 2, h / 2, radius, 30, -180, -270)\n  ];\n  const rc = rough10.svg(shapeSvg);\n  const options = userNodeOverrides(node, { fill: \"none\" });\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const leftCurlyBracePath = createPathFromPoints(leftCurlyBracePoints);\n  const newLeftCurlyBracePath = leftCurlyBracePath.replace(\"Z\", \"\");\n  const leftCurlyBraceNode = rc.path(newLeftCurlyBracePath, options);\n  const rightCurlyBracePath = createPathFromPoints(rightCurlyBracePoints);\n  const newRightCurlyBracePath = rightCurlyBracePath.replace(\"Z\", \"\");\n  const rightCurlyBraceNode = rc.path(newRightCurlyBracePath, options);\n  const rectPath = createPathFromPoints(rectPoints);\n  const rectShape = rc.path(rectPath, { ...options });\n  const curlyBracesShape = shapeSvg.insert(\"g\", \":first-child\");\n  curlyBracesShape.insert(() => rectShape, \":first-child\").attr(\"stroke-opacity\", 0);\n  curlyBracesShape.insert(() => leftCurlyBraceNode, \":first-child\");\n  curlyBracesShape.insert(() => rightCurlyBraceNode, \":first-child\");\n  curlyBracesShape.attr(\"class\", \"text\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    curlyBracesShape.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    curlyBracesShape.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  curlyBracesShape.attr(\"transform\", `translate(${radius - radius / 4}, 0)`);\n  label.attr(\n    \"transform\",\n    `translate(${-w / 2 + (node.padding ?? 0) / 2 - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n  updateNodeBounds(node, curlyBracesShape);\n  node.intersect = function(point) {\n    const pos = intersect_default.polygon(node, rectPoints, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(curlyBraces, \"curlyBraces\");\n\n// src/rendering-util/rendering-elements/shapes/curvedTrapezoid.ts\nimport rough11 from \"roughjs\";\nasync function curvedTrapezoid(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const minWidth = 80, minHeight = 20;\n  const w = Math.max(minWidth, (bbox.width + (node.padding ?? 0) * 2) * 1.25, node?.width ?? 0);\n  const h = Math.max(minHeight, bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const radius = h / 2;\n  const { cssStyles } = node;\n  const rc = rough11.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const totalWidth = w, totalHeight = h;\n  const rw = totalWidth - radius;\n  const tw = totalHeight / 4;\n  const points = [\n    { x: rw, y: 0 },\n    { x: tw, y: 0 },\n    { x: 0, y: totalHeight / 2 },\n    { x: tw, y: totalHeight },\n    { x: rw, y: totalHeight },\n    ...generateCirclePoints(-rw, -totalHeight / 2, radius, 50, 270, 90)\n  ];\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => shapeNode, \":first-child\");\n  polygon.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", nodeStyles);\n  }\n  polygon.attr(\"transform\", `translate(${-w / 2}, ${-h / 2})`);\n  updateNodeBounds(node, polygon);\n  node.intersect = function(point) {\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(curvedTrapezoid, \"curvedTrapezoid\");\n\n// src/rendering-util/rendering-elements/shapes/cylinder.ts\nimport rough12 from \"roughjs\";\nvar createCylinderPathD = /* @__PURE__ */ __name((x, y, width, height, rx, ry) => {\n  return [\n    `M${x},${y + ry}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `a${rx},${ry} 0,0,0 ${-width},0`,\n    `l0,${height}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `l0,${-height}`\n  ].join(\" \");\n}, \"createCylinderPathD\");\nvar createOuterCylinderPathD = /* @__PURE__ */ __name((x, y, width, height, rx, ry) => {\n  return [\n    `M${x},${y + ry}`,\n    `M${x + width},${y + ry}`,\n    `a${rx},${ry} 0,0,0 ${-width},0`,\n    `l0,${height}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `l0,${-height}`\n  ].join(\" \");\n}, \"createOuterCylinderPathD\");\nvar createInnerCylinderPathD = /* @__PURE__ */ __name((x, y, width, height, rx, ry) => {\n  return [`M${x - width / 2},${-height / 2}`, `a${rx},${ry} 0,0,0 ${width},0`].join(\" \");\n}, \"createInnerCylinderPathD\");\nasync function cylinder(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + node.padding, node.width ?? 0);\n  const rx = w / 2;\n  const ry = rx / (2.5 + w / 50);\n  const h = Math.max(bbox.height + ry + node.padding, node.height ?? 0);\n  let cylinder2;\n  const { cssStyles } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough12.svg(shapeSvg);\n    const outerPathData = createOuterCylinderPathD(0, 0, w, h, rx, ry);\n    const innerPathData = createInnerCylinderPathD(0, ry, w, h, rx, ry);\n    const outerNode = rc.path(outerPathData, userNodeOverrides(node, {}));\n    const innerLine = rc.path(innerPathData, userNodeOverrides(node, { fill: \"none\" }));\n    cylinder2 = shapeSvg.insert(() => innerLine, \":first-child\");\n    cylinder2 = shapeSvg.insert(() => outerNode, \":first-child\");\n    cylinder2.attr(\"class\", \"basic label-container\");\n    if (cssStyles) {\n      cylinder2.attr(\"style\", cssStyles);\n    }\n  } else {\n    const pathData = createCylinderPathD(0, 0, w, h, rx, ry);\n    cylinder2 = shapeSvg.insert(\"path\", \":first-child\").attr(\"d\", pathData).attr(\"class\", \"basic label-container\").attr(\"style\", handleUndefinedAttr(cssStyles)).attr(\"style\", nodeStyles);\n  }\n  cylinder2.attr(\"label-offset-y\", ry);\n  cylinder2.attr(\"transform\", `translate(${-w / 2}, ${-(h / 2 + ry)})`);\n  updateNodeBounds(node, cylinder2);\n  label.attr(\n    \"transform\",\n    `translate(${-(bbox.width / 2) - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + (node.padding ?? 0) / 1.5 - (bbox.y - (bbox.top ?? 0))})`\n  );\n  node.intersect = function(point) {\n    const pos = intersect_default.rect(node, point);\n    const x = pos.x - (node.x ?? 0);\n    if (rx != 0 && (Math.abs(x) < (node.width ?? 0) / 2 || Math.abs(x) == (node.width ?? 0) / 2 && Math.abs(pos.y - (node.y ?? 0)) > (node.height ?? 0) / 2 - ry)) {\n      let y = ry * ry * (1 - x * x / (rx * rx));\n      if (y > 0) {\n        y = Math.sqrt(y);\n      }\n      y = ry - y;\n      if (point.y - (node.y ?? 0) > 0) {\n        y = -y;\n      }\n      pos.y += y;\n    }\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(cylinder, \"cylinder\");\n\n// src/rendering-util/rendering-elements/shapes/dividedRect.ts\nimport rough13 from \"roughjs\";\nasync function dividedRectangle(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const rectOffset = h * 0.2;\n  const x = -w / 2;\n  const y = -h / 2 - rectOffset / 2;\n  const { cssStyles } = node;\n  const rc = rough13.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const pts = [\n    { x, y: y + rectOffset },\n    { x: -x, y: y + rectOffset },\n    { x: -x, y: -y },\n    { x, y: -y },\n    { x, y },\n    { x: -x, y },\n    { x: -x, y: y + rectOffset }\n  ];\n  const poly = rc.polygon(\n    pts.map((p) => [p.x, p.y]),\n    options\n  );\n  const polygon = shapeSvg.insert(() => poly, \":first-child\");\n  polygon.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    polygon.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    polygon.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  label.attr(\n    \"transform\",\n    `translate(${x + (node.padding ?? 0) / 2 - (bbox.x - (bbox.left ?? 0))}, ${y + rectOffset + (node.padding ?? 0) / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n  updateNodeBounds(node, polygon);\n  node.intersect = function(point) {\n    const pos = intersect_default.rect(node, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(dividedRectangle, \"dividedRectangle\");\n\n// src/rendering-util/rendering-elements/shapes/doubleCircle.ts\nimport rough14 from \"roughjs\";\nasync function doublecircle(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(parent, node, getNodeClasses(node));\n  const gap = 5;\n  const outerRadius = bbox.width / 2 + halfPadding + gap;\n  const innerRadius = bbox.width / 2 + halfPadding;\n  let circleGroup;\n  const { cssStyles } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough14.svg(shapeSvg);\n    const outerOptions = userNodeOverrides(node, { roughness: 0.2, strokeWidth: 2.5 });\n    const innerOptions = userNodeOverrides(node, { roughness: 0.2, strokeWidth: 1.5 });\n    const outerRoughNode = rc.circle(0, 0, outerRadius * 2, outerOptions);\n    const innerRoughNode = rc.circle(0, 0, innerRadius * 2, innerOptions);\n    circleGroup = shapeSvg.insert(\"g\", \":first-child\");\n    circleGroup.attr(\"class\", handleUndefinedAttr(node.cssClasses)).attr(\"style\", handleUndefinedAttr(cssStyles));\n    circleGroup.node()?.appendChild(outerRoughNode);\n    circleGroup.node()?.appendChild(innerRoughNode);\n  } else {\n    circleGroup = shapeSvg.insert(\"g\", \":first-child\");\n    const outerCircle = circleGroup.insert(\"circle\", \":first-child\");\n    const innerCircle = circleGroup.insert(\"circle\");\n    circleGroup.attr(\"class\", \"basic label-container\").attr(\"style\", nodeStyles);\n    outerCircle.attr(\"class\", \"outer-circle\").attr(\"style\", nodeStyles).attr(\"r\", outerRadius).attr(\"cx\", 0).attr(\"cy\", 0);\n    innerCircle.attr(\"class\", \"inner-circle\").attr(\"style\", nodeStyles).attr(\"r\", innerRadius).attr(\"cx\", 0).attr(\"cy\", 0);\n  }\n  updateNodeBounds(node, circleGroup);\n  node.intersect = function(point) {\n    log.info(\"DoubleCircle intersect\", node, outerRadius, point);\n    return intersect_default.circle(node, outerRadius, point);\n  };\n  return shapeSvg;\n}\n__name(doublecircle, \"doublecircle\");\n\n// src/rendering-util/rendering-elements/shapes/filledCircle.ts\nimport rough15 from \"roughjs\";\nfunction filledCircle(parent, node, { config: { themeVariables } }) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.label = \"\";\n  node.labelStyle = labelStyles;\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", getNodeClasses(node)).attr(\"id\", node.domId ?? node.id);\n  const radius = 7;\n  const { cssStyles } = node;\n  const rc = rough15.svg(shapeSvg);\n  const { nodeBorder } = themeVariables;\n  const options = userNodeOverrides(node, { fillStyle: \"solid\" });\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n  }\n  const circleNode = rc.circle(0, 0, radius * 2, options);\n  const filledCircle2 = shapeSvg.insert(() => circleNode, \":first-child\");\n  filledCircle2.selectAll(\"path\").attr(\"style\", `fill: ${nodeBorder} !important;`);\n  if (cssStyles && cssStyles.length > 0 && node.look !== \"handDrawn\") {\n    filledCircle2.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    filledCircle2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, filledCircle2);\n  node.intersect = function(point) {\n    log.info(\"filledCircle intersect\", node, { radius, point });\n    const pos = intersect_default.circle(node, radius, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(filledCircle, \"filledCircle\");\n\n// src/rendering-util/rendering-elements/shapes/flippedTriangle.ts\nimport rough16 from \"roughjs\";\nasync function flippedTriangle(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + (node.padding ?? 0);\n  const h = w + bbox.height;\n  const tw = w + bbox.height;\n  const points = [\n    { x: 0, y: -h },\n    { x: tw, y: -h },\n    { x: tw / 2, y: 0 }\n  ];\n  const { cssStyles } = node;\n  const rc = rough16.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const pathData = createPathFromPoints(points);\n  const roughNode = rc.path(pathData, options);\n  const flippedTriangle2 = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-h / 2}, ${h / 2})`);\n  if (cssStyles && node.look !== \"handDrawn\") {\n    flippedTriangle2.selectChildren(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    flippedTriangle2.selectChildren(\"path\").attr(\"style\", nodeStyles);\n  }\n  node.width = w;\n  node.height = h;\n  updateNodeBounds(node, flippedTriangle2);\n  label.attr(\n    \"transform\",\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))}, ${-h / 2 + (node.padding ?? 0) / 2 + (bbox.y - (bbox.top ?? 0))})`\n  );\n  node.intersect = function(point) {\n    log.info(\"Triangle intersect\", node, points, point);\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(flippedTriangle, \"flippedTriangle\");\n\n// src/rendering-util/rendering-elements/shapes/forkJoin.ts\nimport rough17 from \"roughjs\";\nfunction forkJoin(parent, node, { dir, config: { state: state2, themeVariables } }) {\n  const { nodeStyles } = styles2String(node);\n  node.label = \"\";\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", getNodeClasses(node)).attr(\"id\", node.domId ?? node.id);\n  const { cssStyles } = node;\n  let width = Math.max(70, node?.width ?? 0);\n  let height = Math.max(10, node?.height ?? 0);\n  if (dir === \"LR\") {\n    width = Math.max(10, node?.width ?? 0);\n    height = Math.max(70, node?.height ?? 0);\n  }\n  const x = -1 * width / 2;\n  const y = -1 * height / 2;\n  const rc = rough17.svg(shapeSvg);\n  const options = userNodeOverrides(node, {\n    stroke: themeVariables.lineColor,\n    fill: themeVariables.lineColor\n  });\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const roughNode = rc.rectangle(x, y, width, height, options);\n  const shape = shapeSvg.insert(() => roughNode, \":first-child\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    shape.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    shape.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, shape);\n  const padding = state2?.padding ?? 0;\n  if (node.width && node.height) {\n    node.width += padding / 2 || 0;\n    node.height += padding / 2 || 0;\n  }\n  node.intersect = function(point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(forkJoin, \"forkJoin\");\n\n// src/rendering-util/rendering-elements/shapes/halfRoundedRectangle.ts\nimport rough18 from \"roughjs\";\nasync function halfRoundedRectangle(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const minWidth = 80, minHeight = 50;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(minWidth, bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(minHeight, bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const radius = h / 2;\n  const { cssStyles } = node;\n  const rc = rough18.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [\n    { x: -w / 2, y: -h / 2 },\n    { x: w / 2 - radius, y: -h / 2 },\n    ...generateCirclePoints(-w / 2 + radius, 0, radius, 50, 90, 270),\n    { x: w / 2 - radius, y: h / 2 },\n    { x: -w / 2, y: h / 2 }\n  ];\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => shapeNode, \":first-child\");\n  polygon.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, polygon);\n  node.intersect = function(point) {\n    log.info(\"Pill intersect\", node, { radius, point });\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(halfRoundedRectangle, \"halfRoundedRectangle\");\n\n// src/rendering-util/rendering-elements/shapes/hexagon.ts\nimport rough19 from \"roughjs\";\nvar createHexagonPathD = /* @__PURE__ */ __name((x, y, width, height, m) => {\n  return [\n    `M${x + m},${y}`,\n    `L${x + width - m},${y}`,\n    `L${x + width},${y - height / 2}`,\n    `L${x + width - m},${y - height}`,\n    `L${x + m},${y - height}`,\n    `L${x},${y - height / 2}`,\n    \"Z\"\n  ].join(\" \");\n}, \"createHexagonPathD\");\nasync function hexagon(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const f = 4;\n  const h = bbox.height + node.padding;\n  const m = h / f;\n  const w = bbox.width + 2 * m + node.padding;\n  const points = [\n    { x: m, y: 0 },\n    { x: w - m, y: 0 },\n    { x: w, y: -h / 2 },\n    { x: w - m, y: -h },\n    { x: m, y: -h },\n    { x: 0, y: -h / 2 }\n  ];\n  let polygon;\n  const { cssStyles } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough19.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createHexagonPathD(0, 0, w, h, m);\n    const roughNode = rc.path(pathData, options);\n    polygon = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-w / 2}, ${h / 2})`);\n    if (cssStyles) {\n      polygon.attr(\"style\", cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n  if (nodeStyles) {\n    polygon.attr(\"style\", nodeStyles);\n  }\n  node.width = w;\n  node.height = h;\n  updateNodeBounds(node, polygon);\n  node.intersect = function(point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(hexagon, \"hexagon\");\n\n// src/rendering-util/rendering-elements/shapes/hourglass.ts\nimport rough20 from \"roughjs\";\nasync function hourglass(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.label = \"\";\n  node.labelStyle = labelStyles;\n  const { shapeSvg } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(30, node?.width ?? 0);\n  const h = Math.max(30, node?.height ?? 0);\n  const { cssStyles } = node;\n  const rc = rough20.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [\n    { x: 0, y: 0 },\n    { x: w, y: 0 },\n    { x: 0, y: h },\n    { x: w, y: h }\n  ];\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => shapeNode, \":first-child\");\n  polygon.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", nodeStyles);\n  }\n  polygon.attr(\"transform\", `translate(${-w / 2}, ${-h / 2})`);\n  updateNodeBounds(node, polygon);\n  node.intersect = function(point) {\n    log.info(\"Pill intersect\", node, { points });\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(hourglass, \"hourglass\");\n\n// src/rendering-util/rendering-elements/shapes/icon.ts\nimport rough21 from \"roughjs\";\nasync function icon(parent, node, { config: { themeVariables, flowchart } }) {\n  const { labelStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const assetHeight = node.assetHeight ?? 48;\n  const assetWidth = node.assetWidth ?? 48;\n  const iconSize = Math.max(assetHeight, assetWidth);\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.width = Math.max(iconSize, defaultWidth ?? 0);\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, \"icon-shape default\");\n  const topLabel = node.pos === \"t\";\n  const height = iconSize;\n  const width = iconSize;\n  const { nodeBorder } = themeVariables;\n  const { stylesMap } = compileStyles(node);\n  const x = -width / 2;\n  const y = -height / 2;\n  const labelPadding = node.label ? 8 : 0;\n  const rc = rough21.svg(shapeSvg);\n  const options = userNodeOverrides(node, { stroke: \"none\", fill: \"none\" });\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const iconNode = rc.rectangle(x, y, width, height, options);\n  const outerWidth = Math.max(width, bbox.width);\n  const outerHeight = height + bbox.height + labelPadding;\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: \"transparent\",\n    stroke: \"none\"\n  });\n  const iconShape = shapeSvg.insert(() => iconNode, \":first-child\");\n  const outerShape = shapeSvg.insert(() => outerNode);\n  if (node.icon) {\n    const iconElem = shapeSvg.append(\"g\");\n    iconElem.html(\n      `<g>${await getIconSVG(node.icon, {\n        height: iconSize,\n        width: iconSize,\n        fallbackPrefix: \"\"\n      })}</g>`\n    );\n    const iconBBox = iconElem.node().getBBox();\n    const iconWidth = iconBBox.width;\n    const iconHeight = iconBBox.height;\n    const iconX = iconBBox.x;\n    const iconY = iconBBox.y;\n    iconElem.attr(\n      \"transform\",\n      `translate(${-iconWidth / 2 - iconX},${topLabel ? bbox.height / 2 + labelPadding / 2 - iconHeight / 2 - iconY : -bbox.height / 2 - labelPadding / 2 - iconHeight / 2 - iconY})`\n    );\n    iconElem.attr(\"style\", `color: ${stylesMap.get(\"stroke\") ?? nodeBorder};`);\n  }\n  label.attr(\n    \"transform\",\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${topLabel ? -outerHeight / 2 : outerHeight / 2 - bbox.height})`\n  );\n  iconShape.attr(\n    \"transform\",\n    `translate(${0},${topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2})`\n  );\n  updateNodeBounds(node, outerShape);\n  node.intersect = function(point) {\n    log.info(\"iconSquare intersect\", node, point);\n    if (!node.label) {\n      return intersect_default.rect(node, point);\n    }\n    const dx = node.x ?? 0;\n    const dy = node.y ?? 0;\n    const nodeHeight = node.height ?? 0;\n    let points = [];\n    if (topLabel) {\n      points = [\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding }\n      ];\n    } else {\n      points = [\n        { x: dx - width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2 / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + height }\n      ];\n    }\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(icon, \"icon\");\n\n// src/rendering-util/rendering-elements/shapes/iconCircle.ts\nimport rough22 from \"roughjs\";\nasync function iconCircle(parent, node, { config: { themeVariables, flowchart } }) {\n  const { labelStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const assetHeight = node.assetHeight ?? 48;\n  const assetWidth = node.assetWidth ?? 48;\n  const iconSize = Math.max(assetHeight, assetWidth);\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.width = Math.max(iconSize, defaultWidth ?? 0);\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, \"icon-shape default\");\n  const padding = 20;\n  const labelPadding = node.label ? 8 : 0;\n  const topLabel = node.pos === \"t\";\n  const { nodeBorder, mainBkg } = themeVariables;\n  const { stylesMap } = compileStyles(node);\n  const rc = rough22.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const fill = stylesMap.get(\"fill\");\n  options.stroke = fill ?? mainBkg;\n  const iconElem = shapeSvg.append(\"g\");\n  if (node.icon) {\n    iconElem.html(\n      `<g>${await getIconSVG(node.icon, {\n        height: iconSize,\n        width: iconSize,\n        fallbackPrefix: \"\"\n      })}</g>`\n    );\n  }\n  const iconBBox = iconElem.node().getBBox();\n  const iconWidth = iconBBox.width;\n  const iconHeight = iconBBox.height;\n  const iconX = iconBBox.x;\n  const iconY = iconBBox.y;\n  const diameter = Math.max(iconWidth, iconHeight) * Math.SQRT2 + padding * 2;\n  const iconNode = rc.circle(0, 0, diameter, options);\n  const outerWidth = Math.max(diameter, bbox.width);\n  const outerHeight = diameter + bbox.height + labelPadding;\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: \"transparent\",\n    stroke: \"none\"\n  });\n  const iconShape = shapeSvg.insert(() => iconNode, \":first-child\");\n  const outerShape = shapeSvg.insert(() => outerNode);\n  iconElem.attr(\n    \"transform\",\n    `translate(${-iconWidth / 2 - iconX},${topLabel ? bbox.height / 2 + labelPadding / 2 - iconHeight / 2 - iconY : -bbox.height / 2 - labelPadding / 2 - iconHeight / 2 - iconY})`\n  );\n  iconElem.attr(\"style\", `color: ${stylesMap.get(\"stroke\") ?? nodeBorder};`);\n  label.attr(\n    \"transform\",\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${topLabel ? -outerHeight / 2 : outerHeight / 2 - bbox.height})`\n  );\n  iconShape.attr(\n    \"transform\",\n    `translate(${0},${topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2})`\n  );\n  updateNodeBounds(node, outerShape);\n  node.intersect = function(point) {\n    log.info(\"iconSquare intersect\", node, point);\n    const pos = intersect_default.rect(node, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(iconCircle, \"iconCircle\");\n\n// src/rendering-util/rendering-elements/shapes/iconRounded.ts\nimport rough23 from \"roughjs\";\nasync function iconRounded(parent, node, { config: { themeVariables, flowchart } }) {\n  const { labelStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const assetHeight = node.assetHeight ?? 48;\n  const assetWidth = node.assetWidth ?? 48;\n  const iconSize = Math.max(assetHeight, assetWidth);\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.width = Math.max(iconSize, defaultWidth ?? 0);\n  const { shapeSvg, bbox, halfPadding, label } = await labelHelper(\n    parent,\n    node,\n    \"icon-shape default\"\n  );\n  const topLabel = node.pos === \"t\";\n  const height = iconSize + halfPadding * 2;\n  const width = iconSize + halfPadding * 2;\n  const { nodeBorder, mainBkg } = themeVariables;\n  const { stylesMap } = compileStyles(node);\n  const x = -width / 2;\n  const y = -height / 2;\n  const labelPadding = node.label ? 8 : 0;\n  const rc = rough23.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const fill = stylesMap.get(\"fill\");\n  options.stroke = fill ?? mainBkg;\n  const iconNode = rc.path(createRoundedRectPathD(x, y, width, height, 5), options);\n  const outerWidth = Math.max(width, bbox.width);\n  const outerHeight = height + bbox.height + labelPadding;\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: \"transparent\",\n    stroke: \"none\"\n  });\n  const iconShape = shapeSvg.insert(() => iconNode, \":first-child\").attr(\"class\", \"icon-shape2\");\n  const outerShape = shapeSvg.insert(() => outerNode);\n  if (node.icon) {\n    const iconElem = shapeSvg.append(\"g\");\n    iconElem.html(\n      `<g>${await getIconSVG(node.icon, {\n        height: iconSize,\n        width: iconSize,\n        fallbackPrefix: \"\"\n      })}</g>`\n    );\n    const iconBBox = iconElem.node().getBBox();\n    const iconWidth = iconBBox.width;\n    const iconHeight = iconBBox.height;\n    const iconX = iconBBox.x;\n    const iconY = iconBBox.y;\n    iconElem.attr(\n      \"transform\",\n      `translate(${-iconWidth / 2 - iconX},${topLabel ? bbox.height / 2 + labelPadding / 2 - iconHeight / 2 - iconY : -bbox.height / 2 - labelPadding / 2 - iconHeight / 2 - iconY})`\n    );\n    iconElem.attr(\"style\", `color: ${stylesMap.get(\"stroke\") ?? nodeBorder};`);\n  }\n  label.attr(\n    \"transform\",\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${topLabel ? -outerHeight / 2 : outerHeight / 2 - bbox.height})`\n  );\n  iconShape.attr(\n    \"transform\",\n    `translate(${0},${topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2})`\n  );\n  updateNodeBounds(node, outerShape);\n  node.intersect = function(point) {\n    log.info(\"iconSquare intersect\", node, point);\n    if (!node.label) {\n      return intersect_default.rect(node, point);\n    }\n    const dx = node.x ?? 0;\n    const dy = node.y ?? 0;\n    const nodeHeight = node.height ?? 0;\n    let points = [];\n    if (topLabel) {\n      points = [\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding }\n      ];\n    } else {\n      points = [\n        { x: dx - width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2 / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + height }\n      ];\n    }\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(iconRounded, \"iconRounded\");\n\n// src/rendering-util/rendering-elements/shapes/iconSquare.ts\nimport rough24 from \"roughjs\";\nasync function iconSquare(parent, node, { config: { themeVariables, flowchart } }) {\n  const { labelStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const assetHeight = node.assetHeight ?? 48;\n  const assetWidth = node.assetWidth ?? 48;\n  const iconSize = Math.max(assetHeight, assetWidth);\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.width = Math.max(iconSize, defaultWidth ?? 0);\n  const { shapeSvg, bbox, halfPadding, label } = await labelHelper(\n    parent,\n    node,\n    \"icon-shape default\"\n  );\n  const topLabel = node.pos === \"t\";\n  const height = iconSize + halfPadding * 2;\n  const width = iconSize + halfPadding * 2;\n  const { nodeBorder, mainBkg } = themeVariables;\n  const { stylesMap } = compileStyles(node);\n  const x = -width / 2;\n  const y = -height / 2;\n  const labelPadding = node.label ? 8 : 0;\n  const rc = rough24.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const fill = stylesMap.get(\"fill\");\n  options.stroke = fill ?? mainBkg;\n  const iconNode = rc.path(createRoundedRectPathD(x, y, width, height, 0.1), options);\n  const outerWidth = Math.max(width, bbox.width);\n  const outerHeight = height + bbox.height + labelPadding;\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: \"transparent\",\n    stroke: \"none\"\n  });\n  const iconShape = shapeSvg.insert(() => iconNode, \":first-child\");\n  const outerShape = shapeSvg.insert(() => outerNode);\n  if (node.icon) {\n    const iconElem = shapeSvg.append(\"g\");\n    iconElem.html(\n      `<g>${await getIconSVG(node.icon, {\n        height: iconSize,\n        width: iconSize,\n        fallbackPrefix: \"\"\n      })}</g>`\n    );\n    const iconBBox = iconElem.node().getBBox();\n    const iconWidth = iconBBox.width;\n    const iconHeight = iconBBox.height;\n    const iconX = iconBBox.x;\n    const iconY = iconBBox.y;\n    iconElem.attr(\n      \"transform\",\n      `translate(${-iconWidth / 2 - iconX},${topLabel ? bbox.height / 2 + labelPadding / 2 - iconHeight / 2 - iconY : -bbox.height / 2 - labelPadding / 2 - iconHeight / 2 - iconY})`\n    );\n    iconElem.attr(\"style\", `color: ${stylesMap.get(\"stroke\") ?? nodeBorder};`);\n  }\n  label.attr(\n    \"transform\",\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${topLabel ? -outerHeight / 2 : outerHeight / 2 - bbox.height})`\n  );\n  iconShape.attr(\n    \"transform\",\n    `translate(${0},${topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2})`\n  );\n  updateNodeBounds(node, outerShape);\n  node.intersect = function(point) {\n    log.info(\"iconSquare intersect\", node, point);\n    if (!node.label) {\n      return intersect_default.rect(node, point);\n    }\n    const dx = node.x ?? 0;\n    const dy = node.y ?? 0;\n    const nodeHeight = node.height ?? 0;\n    let points = [];\n    if (topLabel) {\n      points = [\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding }\n      ];\n    } else {\n      points = [\n        { x: dx - width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2 / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + height }\n      ];\n    }\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(iconSquare, \"iconSquare\");\n\n// src/rendering-util/rendering-elements/shapes/imageSquare.ts\nimport rough25 from \"roughjs\";\nasync function imageSquare(parent, node, { config: { flowchart } }) {\n  const img = new Image();\n  img.src = node?.img ?? \"\";\n  await img.decode();\n  const imageNaturalWidth = Number(img.naturalWidth.toString().replace(\"px\", \"\"));\n  const imageNaturalHeight = Number(img.naturalHeight.toString().replace(\"px\", \"\"));\n  node.imageAspectRatio = imageNaturalWidth / imageNaturalHeight;\n  const { labelStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.defaultWidth = flowchart?.wrappingWidth;\n  const imageRawWidth = Math.max(\n    node.label ? defaultWidth ?? 0 : 0,\n    node?.assetWidth ?? imageNaturalWidth\n  );\n  const imageWidth = node.constraint === \"on\" ? node?.assetHeight ? node.assetHeight * node.imageAspectRatio : imageRawWidth : imageRawWidth;\n  const imageHeight = node.constraint === \"on\" ? imageWidth / node.imageAspectRatio : node?.assetHeight ?? imageNaturalHeight;\n  node.width = Math.max(imageWidth, defaultWidth ?? 0);\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, \"image-shape default\");\n  const topLabel = node.pos === \"t\";\n  const x = -imageWidth / 2;\n  const y = -imageHeight / 2;\n  const labelPadding = node.label ? 8 : 0;\n  const rc = rough25.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const imageNode = rc.rectangle(x, y, imageWidth, imageHeight, options);\n  const outerWidth = Math.max(imageWidth, bbox.width);\n  const outerHeight = imageHeight + bbox.height + labelPadding;\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: \"none\",\n    stroke: \"none\"\n  });\n  const iconShape = shapeSvg.insert(() => imageNode, \":first-child\");\n  const outerShape = shapeSvg.insert(() => outerNode);\n  if (node.img) {\n    const image = shapeSvg.append(\"image\");\n    image.attr(\"href\", node.img);\n    image.attr(\"width\", imageWidth);\n    image.attr(\"height\", imageHeight);\n    image.attr(\"preserveAspectRatio\", \"none\");\n    image.attr(\n      \"transform\",\n      `translate(${-imageWidth / 2},${topLabel ? outerHeight / 2 - imageHeight : -outerHeight / 2})`\n    );\n  }\n  label.attr(\n    \"transform\",\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${topLabel ? -imageHeight / 2 - bbox.height / 2 - labelPadding / 2 : imageHeight / 2 - bbox.height / 2 + labelPadding / 2})`\n  );\n  iconShape.attr(\n    \"transform\",\n    `translate(${0},${topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2})`\n  );\n  updateNodeBounds(node, outerShape);\n  node.intersect = function(point) {\n    log.info(\"iconSquare intersect\", node, point);\n    if (!node.label) {\n      return intersect_default.rect(node, point);\n    }\n    const dx = node.x ?? 0;\n    const dy = node.y ?? 0;\n    const nodeHeight = node.height ?? 0;\n    let points = [];\n    if (topLabel) {\n      points = [\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + imageWidth / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + imageWidth / 2, y: dy + nodeHeight / 2 },\n        { x: dx - imageWidth / 2, y: dy + nodeHeight / 2 },\n        { x: dx - imageWidth / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding }\n      ];\n    } else {\n      points = [\n        { x: dx - imageWidth / 2, y: dy - nodeHeight / 2 },\n        { x: dx + imageWidth / 2, y: dy - nodeHeight / 2 },\n        { x: dx + imageWidth / 2, y: dy - nodeHeight / 2 + imageHeight },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + imageHeight },\n        { x: dx + bbox.width / 2 / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + imageHeight },\n        { x: dx - imageWidth / 2, y: dy - nodeHeight / 2 + imageHeight }\n      ];\n    }\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(imageSquare, \"imageSquare\");\n\n// src/rendering-util/rendering-elements/shapes/invertedTrapezoid.ts\nimport rough26 from \"roughjs\";\nasync function inv_trapezoid(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const points = [\n    { x: 0, y: 0 },\n    { x: w, y: 0 },\n    { x: w + 3 * h / 6, y: -h },\n    { x: -3 * h / 6, y: -h }\n  ];\n  let polygon;\n  const { cssStyles } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough26.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    const roughNode = rc.path(pathData, options);\n    polygon = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-w / 2}, ${h / 2})`);\n    if (cssStyles) {\n      polygon.attr(\"style\", cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n  if (nodeStyles) {\n    polygon.attr(\"style\", nodeStyles);\n  }\n  node.width = w;\n  node.height = h;\n  updateNodeBounds(node, polygon);\n  node.intersect = function(point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(inv_trapezoid, \"inv_trapezoid\");\n\n// src/rendering-util/rendering-elements/shapes/drawRect.ts\nimport rough27 from \"roughjs\";\nasync function drawRect(parent, node, options) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const totalWidth = Math.max(bbox.width + options.labelPaddingX * 2, node?.width || 0);\n  const totalHeight = Math.max(bbox.height + options.labelPaddingY * 2, node?.height || 0);\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n  let rect2;\n  let { rx, ry } = node;\n  const { cssStyles } = node;\n  if (options?.rx && options.ry) {\n    rx = options.rx;\n    ry = options.ry;\n  }\n  if (node.look === \"handDrawn\") {\n    const rc = rough27.svg(shapeSvg);\n    const options2 = userNodeOverrides(node, {});\n    const roughNode = rx || ry ? rc.path(createRoundedRectPathD(x, y, totalWidth, totalHeight, rx || 0), options2) : rc.rectangle(x, y, totalWidth, totalHeight, options2);\n    rect2 = shapeSvg.insert(() => roughNode, \":first-child\");\n    rect2.attr(\"class\", \"basic label-container\").attr(\"style\", handleUndefinedAttr(cssStyles));\n  } else {\n    rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n    rect2.attr(\"class\", \"basic label-container\").attr(\"style\", nodeStyles).attr(\"rx\", handleUndefinedAttr(rx)).attr(\"ry\", handleUndefinedAttr(ry)).attr(\"x\", x).attr(\"y\", y).attr(\"width\", totalWidth).attr(\"height\", totalHeight);\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(drawRect, \"drawRect\");\n\n// src/rendering-util/rendering-elements/shapes/labelRect.ts\nasync function labelRect(parent, node) {\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, \"label\");\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  const totalWidth = 0.1;\n  const totalHeight = 0.1;\n  rect2.attr(\"width\", totalWidth).attr(\"height\", totalHeight);\n  shapeSvg.attr(\"class\", \"label edgeLabel\");\n  label.attr(\n    \"transform\",\n    `translate(${-(bbox.width / 2) - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) - (bbox.y - (bbox.top ?? 0))})`\n  );\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(labelRect, \"labelRect\");\n\n// src/rendering-util/rendering-elements/shapes/leanLeft.ts\nimport rough28 from \"roughjs\";\nasync function lean_left(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0), node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0), node?.height ?? 0);\n  const points = [\n    { x: 0, y: 0 },\n    { x: w + 3 * h / 6, y: 0 },\n    { x: w, y: -h },\n    { x: -(3 * h) / 6, y: -h }\n  ];\n  let polygon;\n  const { cssStyles } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough28.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    const roughNode = rc.path(pathData, options);\n    polygon = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-w / 2}, ${h / 2})`);\n    if (cssStyles) {\n      polygon.attr(\"style\", cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n  if (nodeStyles) {\n    polygon.attr(\"style\", nodeStyles);\n  }\n  node.width = w;\n  node.height = h;\n  updateNodeBounds(node, polygon);\n  node.intersect = function(point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(lean_left, \"lean_left\");\n\n// src/rendering-util/rendering-elements/shapes/leanRight.ts\nimport rough29 from \"roughjs\";\nasync function lean_right(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0), node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0), node?.height ?? 0);\n  const points = [\n    { x: -3 * h / 6, y: 0 },\n    { x: w, y: 0 },\n    { x: w + 3 * h / 6, y: -h },\n    { x: 0, y: -h }\n  ];\n  let polygon;\n  const { cssStyles } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough29.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    const roughNode = rc.path(pathData, options);\n    polygon = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-w / 2}, ${h / 2})`);\n    if (cssStyles) {\n      polygon.attr(\"style\", cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n  if (nodeStyles) {\n    polygon.attr(\"style\", nodeStyles);\n  }\n  node.width = w;\n  node.height = h;\n  updateNodeBounds(node, polygon);\n  node.intersect = function(point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(lean_right, \"lean_right\");\n\n// src/rendering-util/rendering-elements/shapes/lightningBolt.ts\nimport rough30 from \"roughjs\";\nfunction lightningBolt(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.label = \"\";\n  node.labelStyle = labelStyles;\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", getNodeClasses(node)).attr(\"id\", node.domId ?? node.id);\n  const { cssStyles } = node;\n  const width = Math.max(35, node?.width ?? 0);\n  const height = Math.max(35, node?.height ?? 0);\n  const gap = 7;\n  const points = [\n    { x: width, y: 0 },\n    { x: 0, y: height + gap / 2 },\n    { x: width - 2 * gap, y: height + gap / 2 },\n    { x: 0, y: 2 * height },\n    { x: width, y: height - gap / 2 },\n    { x: 2 * gap, y: height - gap / 2 }\n  ];\n  const rc = rough30.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const linePath = createPathFromPoints(points);\n  const lineNode = rc.path(linePath, options);\n  const lightningBolt2 = shapeSvg.insert(() => lineNode, \":first-child\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    lightningBolt2.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    lightningBolt2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  lightningBolt2.attr(\"transform\", `translate(-${width / 2},${-height})`);\n  updateNodeBounds(node, lightningBolt2);\n  node.intersect = function(point) {\n    log.info(\"lightningBolt intersect\", node, point);\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(lightningBolt, \"lightningBolt\");\n\n// src/rendering-util/rendering-elements/shapes/linedCylinder.ts\nimport rough31 from \"roughjs\";\nvar createCylinderPathD2 = /* @__PURE__ */ __name((x, y, width, height, rx, ry, outerOffset) => {\n  return [\n    `M${x},${y + ry}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `a${rx},${ry} 0,0,0 ${-width},0`,\n    `l0,${height}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `l0,${-height}`,\n    `M${x},${y + ry + outerOffset}`,\n    `a${rx},${ry} 0,0,0 ${width},0`\n  ].join(\" \");\n}, \"createCylinderPathD\");\nvar createOuterCylinderPathD2 = /* @__PURE__ */ __name((x, y, width, height, rx, ry, outerOffset) => {\n  return [\n    `M${x},${y + ry}`,\n    `M${x + width},${y + ry}`,\n    `a${rx},${ry} 0,0,0 ${-width},0`,\n    `l0,${height}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `l0,${-height}`,\n    `M${x},${y + ry + outerOffset}`,\n    `a${rx},${ry} 0,0,0 ${width},0`\n  ].join(\" \");\n}, \"createOuterCylinderPathD\");\nvar createInnerCylinderPathD2 = /* @__PURE__ */ __name((x, y, width, height, rx, ry) => {\n  return [`M${x - width / 2},${-height / 2}`, `a${rx},${ry} 0,0,0 ${width},0`].join(\" \");\n}, \"createInnerCylinderPathD\");\nasync function linedCylinder(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0), node.width ?? 0);\n  const rx = w / 2;\n  const ry = rx / (2.5 + w / 50);\n  const h = Math.max(bbox.height + ry + (node.padding ?? 0), node.height ?? 0);\n  const outerOffset = h * 0.1;\n  let cylinder2;\n  const { cssStyles } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough31.svg(shapeSvg);\n    const outerPathData = createOuterCylinderPathD2(0, 0, w, h, rx, ry, outerOffset);\n    const innerPathData = createInnerCylinderPathD2(0, ry, w, h, rx, ry);\n    const options = userNodeOverrides(node, {});\n    const outerNode = rc.path(outerPathData, options);\n    const innerLine = rc.path(innerPathData, options);\n    const innerLineEl = shapeSvg.insert(() => innerLine, \":first-child\");\n    innerLineEl.attr(\"class\", \"line\");\n    cylinder2 = shapeSvg.insert(() => outerNode, \":first-child\");\n    cylinder2.attr(\"class\", \"basic label-container\");\n    if (cssStyles) {\n      cylinder2.attr(\"style\", cssStyles);\n    }\n  } else {\n    const pathData = createCylinderPathD2(0, 0, w, h, rx, ry, outerOffset);\n    cylinder2 = shapeSvg.insert(\"path\", \":first-child\").attr(\"d\", pathData).attr(\"class\", \"basic label-container\").attr(\"style\", handleUndefinedAttr(cssStyles)).attr(\"style\", nodeStyles);\n  }\n  cylinder2.attr(\"label-offset-y\", ry);\n  cylinder2.attr(\"transform\", `translate(${-w / 2}, ${-(h / 2 + ry)})`);\n  updateNodeBounds(node, cylinder2);\n  label.attr(\n    \"transform\",\n    `translate(${-(bbox.width / 2) - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + ry - (bbox.y - (bbox.top ?? 0))})`\n  );\n  node.intersect = function(point) {\n    const pos = intersect_default.rect(node, point);\n    const x = pos.x - (node.x ?? 0);\n    if (rx != 0 && (Math.abs(x) < (node.width ?? 0) / 2 || Math.abs(x) == (node.width ?? 0) / 2 && Math.abs(pos.y - (node.y ?? 0)) > (node.height ?? 0) / 2 - ry)) {\n      let y = ry * ry * (1 - x * x / (rx * rx));\n      if (y > 0) {\n        y = Math.sqrt(y);\n      }\n      y = ry - y;\n      if (point.y - (node.y ?? 0) > 0) {\n        y = -y;\n      }\n      pos.y += y;\n    }\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(linedCylinder, \"linedCylinder\");\n\n// src/rendering-util/rendering-elements/shapes/linedWaveEdgedRect.ts\nimport rough32 from \"roughjs\";\nasync function linedWaveEdgedRect(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const waveAmplitude = h / 4;\n  const finalH = h + waveAmplitude;\n  const { cssStyles } = node;\n  const rc = rough32.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [\n    { x: -w / 2 - w / 2 * 0.1, y: -finalH / 2 },\n    { x: -w / 2 - w / 2 * 0.1, y: finalH / 2 },\n    ...generateFullSineWavePoints(\n      -w / 2 - w / 2 * 0.1,\n      finalH / 2,\n      w / 2 + w / 2 * 0.1,\n      finalH / 2,\n      waveAmplitude,\n      0.8\n    ),\n    { x: w / 2 + w / 2 * 0.1, y: -finalH / 2 },\n    { x: -w / 2 - w / 2 * 0.1, y: -finalH / 2 },\n    { x: -w / 2, y: -finalH / 2 },\n    { x: -w / 2, y: finalH / 2 * 1.1 },\n    { x: -w / 2, y: -finalH / 2 }\n  ];\n  const poly = rc.polygon(\n    points.map((p) => [p.x, p.y]),\n    options\n  );\n  const waveEdgeRect = shapeSvg.insert(() => poly, \":first-child\");\n  waveEdgeRect.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    waveEdgeRect.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    waveEdgeRect.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  waveEdgeRect.attr(\"transform\", `translate(0,${-waveAmplitude / 2})`);\n  label.attr(\n    \"transform\",\n    `translate(${-w / 2 + (node.padding ?? 0) + w / 2 * 0.1 / 2 - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) - waveAmplitude / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n  updateNodeBounds(node, waveEdgeRect);\n  node.intersect = function(point) {\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(linedWaveEdgedRect, \"linedWaveEdgedRect\");\n\n// src/rendering-util/rendering-elements/shapes/multiRect.ts\nimport rough33 from \"roughjs\";\nasync function multiRect(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const rectOffset = 5;\n  const x = -w / 2;\n  const y = -h / 2;\n  const { cssStyles } = node;\n  const rc = rough33.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  const outerPathPoints = [\n    { x: x - rectOffset, y: y + rectOffset },\n    { x: x - rectOffset, y: y + h + rectOffset },\n    { x: x + w - rectOffset, y: y + h + rectOffset },\n    { x: x + w - rectOffset, y: y + h },\n    { x: x + w, y: y + h },\n    { x: x + w, y: y + h - rectOffset },\n    { x: x + w + rectOffset, y: y + h - rectOffset },\n    { x: x + w + rectOffset, y: y - rectOffset },\n    { x: x + rectOffset, y: y - rectOffset },\n    { x: x + rectOffset, y },\n    { x, y },\n    { x, y: y + rectOffset }\n  ];\n  const innerPathPoints = [\n    { x, y: y + rectOffset },\n    { x: x + w - rectOffset, y: y + rectOffset },\n    { x: x + w - rectOffset, y: y + h },\n    { x: x + w, y: y + h },\n    { x: x + w, y },\n    { x, y }\n  ];\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const outerPath = createPathFromPoints(outerPathPoints);\n  const outerNode = rc.path(outerPath, options);\n  const innerPath = createPathFromPoints(innerPathPoints);\n  const innerNode = rc.path(innerPath, { ...options, fill: \"none\" });\n  const multiRect2 = shapeSvg.insert(() => innerNode, \":first-child\");\n  multiRect2.insert(() => outerNode, \":first-child\");\n  multiRect2.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    multiRect2.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    multiRect2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  label.attr(\n    \"transform\",\n    `translate(${-(bbox.width / 2) - rectOffset - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + rectOffset - (bbox.y - (bbox.top ?? 0))})`\n  );\n  updateNodeBounds(node, multiRect2);\n  node.intersect = function(point) {\n    const pos = intersect_default.polygon(node, outerPathPoints, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(multiRect, \"multiRect\");\n\n// src/rendering-util/rendering-elements/shapes/multiWaveEdgedRectangle.ts\nimport rough34 from \"roughjs\";\nasync function multiWaveEdgedRectangle(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const waveAmplitude = h / 4;\n  const finalH = h + waveAmplitude;\n  const x = -w / 2;\n  const y = -finalH / 2;\n  const rectOffset = 5;\n  const { cssStyles } = node;\n  const wavePoints = generateFullSineWavePoints(\n    x - rectOffset,\n    y + finalH + rectOffset,\n    x + w - rectOffset,\n    y + finalH + rectOffset,\n    waveAmplitude,\n    0.8\n  );\n  const lastWavePoint = wavePoints?.[wavePoints.length - 1];\n  const outerPathPoints = [\n    { x: x - rectOffset, y: y + rectOffset },\n    { x: x - rectOffset, y: y + finalH + rectOffset },\n    ...wavePoints,\n    { x: x + w - rectOffset, y: lastWavePoint.y - rectOffset },\n    { x: x + w, y: lastWavePoint.y - rectOffset },\n    { x: x + w, y: lastWavePoint.y - 2 * rectOffset },\n    { x: x + w + rectOffset, y: lastWavePoint.y - 2 * rectOffset },\n    { x: x + w + rectOffset, y: y - rectOffset },\n    { x: x + rectOffset, y: y - rectOffset },\n    { x: x + rectOffset, y },\n    { x, y },\n    { x, y: y + rectOffset }\n  ];\n  const innerPathPoints = [\n    { x, y: y + rectOffset },\n    { x: x + w - rectOffset, y: y + rectOffset },\n    { x: x + w - rectOffset, y: lastWavePoint.y - rectOffset },\n    { x: x + w, y: lastWavePoint.y - rectOffset },\n    { x: x + w, y },\n    { x, y }\n  ];\n  const rc = rough34.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const outerPath = createPathFromPoints(outerPathPoints);\n  const outerNode = rc.path(outerPath, options);\n  const innerPath = createPathFromPoints(innerPathPoints);\n  const innerNode = rc.path(innerPath, options);\n  const shape = shapeSvg.insert(() => outerNode, \":first-child\");\n  shape.insert(() => innerNode);\n  shape.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    shape.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    shape.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  shape.attr(\"transform\", `translate(0,${-waveAmplitude / 2})`);\n  label.attr(\n    \"transform\",\n    `translate(${-(bbox.width / 2) - rectOffset - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + rectOffset - waveAmplitude / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n  updateNodeBounds(node, shape);\n  node.intersect = function(point) {\n    const pos = intersect_default.polygon(node, outerPathPoints, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(multiWaveEdgedRectangle, \"multiWaveEdgedRectangle\");\n\n// src/rendering-util/rendering-elements/shapes/note.ts\nimport rough35 from \"roughjs\";\nasync function note(parent, node, { config: { themeVariables } }) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const useHtmlLabels = node.useHtmlLabels || getConfig().flowchart?.htmlLabels !== false;\n  if (!useHtmlLabels) {\n    node.centerLabel = true;\n  }\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const totalWidth = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const totalHeight = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n  const { cssStyles } = node;\n  const rc = rough35.svg(shapeSvg);\n  const options = userNodeOverrides(node, {\n    fill: themeVariables.noteBkgColor,\n    stroke: themeVariables.noteBorderColor\n  });\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const noteShapeNode = rc.rectangle(x, y, totalWidth, totalHeight, options);\n  const rect2 = shapeSvg.insert(() => noteShapeNode, \":first-child\");\n  rect2.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    rect2.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    rect2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(note, \"note\");\n\n// src/rendering-util/rendering-elements/shapes/question.ts\nimport rough36 from \"roughjs\";\nvar createDecisionBoxPathD = /* @__PURE__ */ __name((x, y, size) => {\n  return [\n    `M${x + size / 2},${y}`,\n    `L${x + size},${y - size / 2}`,\n    `L${x + size / 2},${y - size}`,\n    `L${x},${y - size / 2}`,\n    \"Z\"\n  ].join(\" \");\n}, \"createDecisionBoxPathD\");\nasync function question(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const s = w + h;\n  const points = [\n    { x: s / 2, y: 0 },\n    { x: s, y: -s / 2 },\n    { x: s / 2, y: -s },\n    { x: 0, y: -s / 2 }\n  ];\n  let polygon;\n  const { cssStyles } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough36.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createDecisionBoxPathD(0, 0, s);\n    const roughNode = rc.path(pathData, options);\n    polygon = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-s / 2}, ${s / 2})`);\n    if (cssStyles) {\n      polygon.attr(\"style\", cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, s, s, points);\n  }\n  if (nodeStyles) {\n    polygon.attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, polygon);\n  node.intersect = function(point) {\n    log.debug(\n      \"APA12 Intersect called SPLIT\\npoint:\",\n      point,\n      \"\\nnode:\\n\",\n      node,\n      \"\\nres:\",\n      intersect_default.polygon(node, points, point)\n    );\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(question, \"question\");\n\n// src/rendering-util/rendering-elements/shapes/rectLeftInvArrow.ts\nimport rough37 from \"roughjs\";\nasync function rect_left_inv_arrow(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0), node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0), node?.height ?? 0);\n  const x = -w / 2;\n  const y = -h / 2;\n  const notch = y / 2;\n  const points = [\n    { x: x + notch, y },\n    { x, y: 0 },\n    { x: x + notch, y: -y },\n    { x: -x, y: -y },\n    { x: -x, y }\n  ];\n  const { cssStyles } = node;\n  const rc = rough37.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const pathData = createPathFromPoints(points);\n  const roughNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => roughNode, \":first-child\");\n  polygon.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    polygon.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    polygon.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  polygon.attr(\"transform\", `translate(${-notch / 2},0)`);\n  label.attr(\n    \"transform\",\n    `translate(${-notch / 2 - bbox.width / 2 - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) - (bbox.y - (bbox.top ?? 0))})`\n  );\n  updateNodeBounds(node, polygon);\n  node.intersect = function(point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(rect_left_inv_arrow, \"rect_left_inv_arrow\");\n\n// src/rendering-util/rendering-elements/shapes/rectWithTitle.ts\nimport { select as select4 } from \"d3\";\nimport rough38 from \"roughjs\";\nasync function rectWithTitle(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  let classes;\n  if (!node.cssClasses) {\n    classes = \"node default\";\n  } else {\n    classes = \"node \" + node.cssClasses;\n  }\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", classes).attr(\"id\", node.domId || node.id);\n  const g = shapeSvg.insert(\"g\");\n  const label = shapeSvg.insert(\"g\").attr(\"class\", \"label\").attr(\"style\", nodeStyles);\n  const description = node.description;\n  const title = node.label;\n  const text2 = label.node().appendChild(await createLabel_default(title, node.labelStyle, true, true));\n  let bbox = { width: 0, height: 0 };\n  if (evaluate(getConfig2()?.flowchart?.htmlLabels)) {\n    const div2 = text2.children[0];\n    const dv2 = select4(text2);\n    bbox = div2.getBoundingClientRect();\n    dv2.attr(\"width\", bbox.width);\n    dv2.attr(\"height\", bbox.height);\n  }\n  log.info(\"Text 2\", description);\n  const textRows = description || [];\n  const titleBox = text2.getBBox();\n  const descr = label.node().appendChild(\n    await createLabel_default(\n      textRows.join ? textRows.join(\"<br/>\") : textRows,\n      node.labelStyle,\n      true,\n      true\n    )\n  );\n  const div = descr.children[0];\n  const dv = select4(descr);\n  bbox = div.getBoundingClientRect();\n  dv.attr(\"width\", bbox.width);\n  dv.attr(\"height\", bbox.height);\n  const halfPadding = (node.padding || 0) / 2;\n  select4(descr).attr(\n    \"transform\",\n    \"translate( \" + (bbox.width > titleBox.width ? 0 : (titleBox.width - bbox.width) / 2) + \", \" + (titleBox.height + halfPadding + 5) + \")\"\n  );\n  select4(text2).attr(\n    \"transform\",\n    \"translate( \" + (bbox.width < titleBox.width ? 0 : -(titleBox.width - bbox.width) / 2) + \", 0)\"\n  );\n  bbox = label.node().getBBox();\n  label.attr(\n    \"transform\",\n    \"translate(\" + -bbox.width / 2 + \", \" + (-bbox.height / 2 - halfPadding + 3) + \")\"\n  );\n  const totalWidth = bbox.width + (node.padding || 0);\n  const totalHeight = bbox.height + (node.padding || 0);\n  const x = -bbox.width / 2 - halfPadding;\n  const y = -bbox.height / 2 - halfPadding;\n  let rect2;\n  let innerLine;\n  if (node.look === \"handDrawn\") {\n    const rc = rough38.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const roughNode = rc.path(\n      createRoundedRectPathD(x, y, totalWidth, totalHeight, node.rx || 0),\n      options\n    );\n    const roughLine = rc.line(\n      -bbox.width / 2 - halfPadding,\n      -bbox.height / 2 - halfPadding + titleBox.height + halfPadding,\n      bbox.width / 2 + halfPadding,\n      -bbox.height / 2 - halfPadding + titleBox.height + halfPadding,\n      options\n    );\n    innerLine = shapeSvg.insert(() => {\n      log.debug(\"Rough node insert CXC\", roughNode);\n      return roughLine;\n    }, \":first-child\");\n    rect2 = shapeSvg.insert(() => {\n      log.debug(\"Rough node insert CXC\", roughNode);\n      return roughNode;\n    }, \":first-child\");\n  } else {\n    rect2 = g.insert(\"rect\", \":first-child\");\n    innerLine = g.insert(\"line\");\n    rect2.attr(\"class\", \"outer title-state\").attr(\"style\", nodeStyles).attr(\"x\", -bbox.width / 2 - halfPadding).attr(\"y\", -bbox.height / 2 - halfPadding).attr(\"width\", bbox.width + (node.padding || 0)).attr(\"height\", bbox.height + (node.padding || 0));\n    innerLine.attr(\"class\", \"divider\").attr(\"x1\", -bbox.width / 2 - halfPadding).attr(\"x2\", bbox.width / 2 + halfPadding).attr(\"y1\", -bbox.height / 2 - halfPadding + titleBox.height + halfPadding).attr(\"y2\", -bbox.height / 2 - halfPadding + titleBox.height + halfPadding);\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(rectWithTitle, \"rectWithTitle\");\n\n// src/rendering-util/rendering-elements/shapes/roundedRect.ts\nasync function roundedRect(parent, node) {\n  const options = {\n    rx: 5,\n    ry: 5,\n    classes: \"\",\n    labelPaddingX: (node?.padding || 0) * 1,\n    labelPaddingY: (node?.padding || 0) * 1\n  };\n  return drawRect(parent, node, options);\n}\n__name(roundedRect, \"roundedRect\");\n\n// src/rendering-util/rendering-elements/shapes/shadedProcess.ts\nimport rough39 from \"roughjs\";\nasync function shadedProcess(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const halfPadding = node?.padding ?? 0;\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const x = -bbox.width / 2 - halfPadding;\n  const y = -bbox.height / 2 - halfPadding;\n  const { cssStyles } = node;\n  const rc = rough39.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [\n    { x, y },\n    { x: x + w + 8, y },\n    { x: x + w + 8, y: y + h },\n    { x: x - 8, y: y + h },\n    { x: x - 8, y },\n    { x, y },\n    { x, y: y + h }\n  ];\n  const roughNode = rc.polygon(\n    points.map((p) => [p.x, p.y]),\n    options\n  );\n  const rect2 = shapeSvg.insert(() => roughNode, \":first-child\");\n  rect2.attr(\"class\", \"basic label-container\").attr(\"style\", handleUndefinedAttr(cssStyles));\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    rect2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  if (cssStyles && node.look !== \"handDrawn\") {\n    rect2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  label.attr(\n    \"transform\",\n    `translate(${-w / 2 + 4 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) - (bbox.y - (bbox.top ?? 0))})`\n  );\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(shadedProcess, \"shadedProcess\");\n\n// src/rendering-util/rendering-elements/shapes/slopedRect.ts\nimport rough40 from \"roughjs\";\nasync function slopedRect(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const x = -w / 2;\n  const y = -h / 2;\n  const { cssStyles } = node;\n  const rc = rough40.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [\n    { x, y },\n    { x, y: y + h },\n    { x: x + w, y: y + h },\n    { x: x + w, y: y - h / 2 }\n  ];\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => shapeNode, \":first-child\");\n  polygon.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", nodeStyles);\n  }\n  polygon.attr(\"transform\", `translate(0, ${h / 4})`);\n  label.attr(\n    \"transform\",\n    `translate(${-w / 2 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))}, ${-h / 4 + (node.padding ?? 0) - (bbox.y - (bbox.top ?? 0))})`\n  );\n  updateNodeBounds(node, polygon);\n  node.intersect = function(point) {\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(slopedRect, \"slopedRect\");\n\n// src/rendering-util/rendering-elements/shapes/squareRect.ts\nasync function squareRect2(parent, node) {\n  const options = {\n    rx: 0,\n    ry: 0,\n    classes: \"\",\n    labelPaddingX: (node?.padding || 0) * 2,\n    labelPaddingY: (node?.padding || 0) * 1\n  };\n  return drawRect(parent, node, options);\n}\n__name(squareRect2, \"squareRect\");\n\n// src/rendering-util/rendering-elements/shapes/stadium.ts\nimport rough41 from \"roughjs\";\nasync function stadium(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const h = bbox.height + node.padding;\n  const w = bbox.width + h / 4 + node.padding;\n  let rect2;\n  const { cssStyles } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough41.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createRoundedRectPathD(-w / 2, -h / 2, w, h, h / 2);\n    const roughNode = rc.path(pathData, options);\n    rect2 = shapeSvg.insert(() => roughNode, \":first-child\");\n    rect2.attr(\"class\", \"basic label-container\").attr(\"style\", handleUndefinedAttr(cssStyles));\n  } else {\n    rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n    rect2.attr(\"class\", \"basic label-container\").attr(\"style\", nodeStyles).attr(\"rx\", h / 2).attr(\"ry\", h / 2).attr(\"x\", -w / 2).attr(\"y\", -h / 2).attr(\"width\", w).attr(\"height\", h);\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(stadium, \"stadium\");\n\n// src/rendering-util/rendering-elements/shapes/state.ts\nasync function state(parent, node) {\n  const options = {\n    rx: 5,\n    ry: 5,\n    classes: \"flowchart-node\"\n  };\n  return drawRect(parent, node, options);\n}\n__name(state, \"state\");\n\n// src/rendering-util/rendering-elements/shapes/stateEnd.ts\nimport rough42 from \"roughjs\";\nfunction stateEnd(parent, node, { config: { themeVariables } }) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { cssStyles } = node;\n  const { lineColor, stateBorder, nodeBorder } = themeVariables;\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"node default\").attr(\"id\", node.domId || node.id);\n  const rc = rough42.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const roughNode = rc.circle(0, 0, 14, {\n    ...options,\n    stroke: lineColor,\n    strokeWidth: 2\n  });\n  const innerFill = stateBorder ?? nodeBorder;\n  const roughInnerNode = rc.circle(0, 0, 5, {\n    ...options,\n    fill: innerFill,\n    stroke: innerFill,\n    strokeWidth: 2,\n    fillStyle: \"solid\"\n  });\n  const circle2 = shapeSvg.insert(() => roughNode, \":first-child\");\n  circle2.insert(() => roughInnerNode);\n  if (cssStyles) {\n    circle2.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles) {\n    circle2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, circle2);\n  node.intersect = function(point) {\n    return intersect_default.circle(node, 7, point);\n  };\n  return shapeSvg;\n}\n__name(stateEnd, \"stateEnd\");\n\n// src/rendering-util/rendering-elements/shapes/stateStart.ts\nimport rough43 from \"roughjs\";\nfunction stateStart(parent, node, { config: { themeVariables } }) {\n  const { lineColor } = themeVariables;\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"node default\").attr(\"id\", node.domId || node.id);\n  let circle2;\n  if (node.look === \"handDrawn\") {\n    const rc = rough43.svg(shapeSvg);\n    const roughNode = rc.circle(0, 0, 14, solidStateFill(lineColor));\n    circle2 = shapeSvg.insert(() => roughNode);\n    circle2.attr(\"class\", \"state-start\").attr(\"r\", 7).attr(\"width\", 14).attr(\"height\", 14);\n  } else {\n    circle2 = shapeSvg.insert(\"circle\", \":first-child\");\n    circle2.attr(\"class\", \"state-start\").attr(\"r\", 7).attr(\"width\", 14).attr(\"height\", 14);\n  }\n  updateNodeBounds(node, circle2);\n  node.intersect = function(point) {\n    return intersect_default.circle(node, 7, point);\n  };\n  return shapeSvg;\n}\n__name(stateStart, \"stateStart\");\n\n// src/rendering-util/rendering-elements/shapes/subroutine.ts\nimport rough44 from \"roughjs\";\nasync function subroutine(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const halfPadding = (node?.padding || 0) / 2;\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const x = -bbox.width / 2 - halfPadding;\n  const y = -bbox.height / 2 - halfPadding;\n  const points = [\n    { x: 0, y: 0 },\n    { x: w, y: 0 },\n    { x: w, y: -h },\n    { x: 0, y: -h },\n    { x: 0, y: 0 },\n    { x: -8, y: 0 },\n    { x: w + 8, y: 0 },\n    { x: w + 8, y: -h },\n    { x: -8, y: -h },\n    { x: -8, y: 0 }\n  ];\n  if (node.look === \"handDrawn\") {\n    const rc = rough44.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const roughNode = rc.rectangle(x - 8, y, w + 16, h, options);\n    const l1 = rc.line(x, y, x, y + h, options);\n    const l2 = rc.line(x + w, y, x + w, y + h, options);\n    shapeSvg.insert(() => l1, \":first-child\");\n    shapeSvg.insert(() => l2, \":first-child\");\n    const rect2 = shapeSvg.insert(() => roughNode, \":first-child\");\n    const { cssStyles } = node;\n    rect2.attr(\"class\", \"basic label-container\").attr(\"style\", handleUndefinedAttr(cssStyles));\n    updateNodeBounds(node, rect2);\n  } else {\n    const el = insertPolygonShape(shapeSvg, w, h, points);\n    if (nodeStyles) {\n      el.attr(\"style\", nodeStyles);\n    }\n    updateNodeBounds(node, el);\n  }\n  node.intersect = function(point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(subroutine, \"subroutine\");\n\n// src/rendering-util/rendering-elements/shapes/taggedRect.ts\nimport rough45 from \"roughjs\";\nasync function taggedRect(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const x = -w / 2;\n  const y = -h / 2;\n  const tagWidth = 0.2 * h;\n  const tagHeight = 0.2 * h;\n  const { cssStyles } = node;\n  const rc = rough45.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  const rectPoints = [\n    { x: x - tagWidth / 2, y },\n    { x: x + w + tagWidth / 2, y },\n    { x: x + w + tagWidth / 2, y: y + h },\n    { x: x - tagWidth / 2, y: y + h }\n  ];\n  const tagPoints = [\n    { x: x + w - tagWidth / 2, y: y + h },\n    { x: x + w + tagWidth / 2, y: y + h },\n    { x: x + w + tagWidth / 2, y: y + h - tagHeight }\n  ];\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const rectPath = createPathFromPoints(rectPoints);\n  const rectNode = rc.path(rectPath, options);\n  const tagPath = createPathFromPoints(tagPoints);\n  const tagNode = rc.path(tagPath, { ...options, fillStyle: \"solid\" });\n  const taggedRect2 = shapeSvg.insert(() => tagNode, \":first-child\");\n  taggedRect2.insert(() => rectNode, \":first-child\");\n  taggedRect2.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    taggedRect2.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    taggedRect2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, taggedRect2);\n  node.intersect = function(point) {\n    const pos = intersect_default.polygon(node, rectPoints, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(taggedRect, \"taggedRect\");\n\n// src/rendering-util/rendering-elements/shapes/taggedWaveEdgedRectangle.ts\nimport rough46 from \"roughjs\";\nasync function taggedWaveEdgedRectangle(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const waveAmplitude = h / 4;\n  const tagWidth = 0.2 * w;\n  const tagHeight = 0.2 * h;\n  const finalH = h + waveAmplitude;\n  const { cssStyles } = node;\n  const rc = rough46.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [\n    { x: -w / 2 - w / 2 * 0.1, y: finalH / 2 },\n    ...generateFullSineWavePoints(\n      -w / 2 - w / 2 * 0.1,\n      finalH / 2,\n      w / 2 + w / 2 * 0.1,\n      finalH / 2,\n      waveAmplitude,\n      0.8\n    ),\n    { x: w / 2 + w / 2 * 0.1, y: -finalH / 2 },\n    { x: -w / 2 - w / 2 * 0.1, y: -finalH / 2 }\n  ];\n  const x = -w / 2 + w / 2 * 0.1;\n  const y = -finalH / 2 - tagHeight * 0.4;\n  const tagPoints = [\n    { x: x + w - tagWidth, y: (y + h) * 1.4 },\n    { x: x + w, y: y + h - tagHeight },\n    { x: x + w, y: (y + h) * 0.9 },\n    ...generateFullSineWavePoints(\n      x + w,\n      (y + h) * 1.3,\n      x + w - tagWidth,\n      (y + h) * 1.5,\n      -h * 0.03,\n      0.5\n    )\n  ];\n  const waveEdgeRectPath = createPathFromPoints(points);\n  const waveEdgeRectNode = rc.path(waveEdgeRectPath, options);\n  const taggedWaveEdgeRectPath = createPathFromPoints(tagPoints);\n  const taggedWaveEdgeRectNode = rc.path(taggedWaveEdgeRectPath, {\n    ...options,\n    fillStyle: \"solid\"\n  });\n  const waveEdgeRect = shapeSvg.insert(() => taggedWaveEdgeRectNode, \":first-child\");\n  waveEdgeRect.insert(() => waveEdgeRectNode, \":first-child\");\n  waveEdgeRect.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    waveEdgeRect.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    waveEdgeRect.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  waveEdgeRect.attr(\"transform\", `translate(0,${-waveAmplitude / 2})`);\n  label.attr(\n    \"transform\",\n    `translate(${-w / 2 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) - waveAmplitude / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n  updateNodeBounds(node, waveEdgeRect);\n  node.intersect = function(point) {\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(taggedWaveEdgedRectangle, \"taggedWaveEdgedRectangle\");\n\n// src/rendering-util/rendering-elements/shapes/text.ts\nasync function text(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const totalWidth = Math.max(bbox.width + node.padding, node?.width || 0);\n  const totalHeight = Math.max(bbox.height + node.padding, node?.height || 0);\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  rect2.attr(\"class\", \"text\").attr(\"style\", nodeStyles).attr(\"rx\", 0).attr(\"ry\", 0).attr(\"x\", x).attr(\"y\", y).attr(\"width\", totalWidth).attr(\"height\", totalHeight);\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(text, \"text\");\n\n// src/rendering-util/rendering-elements/shapes/tiltedCylinder.ts\nimport rough47 from \"roughjs\";\nvar createCylinderPathD3 = /* @__PURE__ */ __name((x, y, width, height, rx, ry) => {\n  return `M${x},${y}\n    a${rx},${ry} 0,0,1 ${0},${-height}\n    l${width},${0}\n    a${rx},${ry} 0,0,1 ${0},${height}\n    M${width},${-height}\n    a${rx},${ry} 0,0,0 ${0},${height}\n    l${-width},${0}`;\n}, \"createCylinderPathD\");\nvar createOuterCylinderPathD3 = /* @__PURE__ */ __name((x, y, width, height, rx, ry) => {\n  return [\n    `M${x},${y}`,\n    `M${x + width},${y}`,\n    `a${rx},${ry} 0,0,0 ${0},${-height}`,\n    `l${-width},0`,\n    `a${rx},${ry} 0,0,0 ${0},${height}`,\n    `l${width},0`\n  ].join(\" \");\n}, \"createOuterCylinderPathD\");\nvar createInnerCylinderPathD3 = /* @__PURE__ */ __name((x, y, width, height, rx, ry) => {\n  return [`M${x + width / 2},${-height / 2}`, `a${rx},${ry} 0,0,0 0,${height}`].join(\" \");\n}, \"createInnerCylinderPathD\");\nasync function tiltedCylinder(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label, halfPadding } = await labelHelper(\n    parent,\n    node,\n    getNodeClasses(node)\n  );\n  const labelPadding = node.look === \"neo\" ? halfPadding * 2 : halfPadding;\n  const h = bbox.height + labelPadding;\n  const ry = h / 2;\n  const rx = ry / (2.5 + h / 50);\n  const w = bbox.width + rx + labelPadding;\n  const { cssStyles } = node;\n  let cylinder2;\n  if (node.look === \"handDrawn\") {\n    const rc = rough47.svg(shapeSvg);\n    const outerPathData = createOuterCylinderPathD3(0, 0, w, h, rx, ry);\n    const innerPathData = createInnerCylinderPathD3(0, 0, w, h, rx, ry);\n    const outerNode = rc.path(outerPathData, userNodeOverrides(node, {}));\n    const innerLine = rc.path(innerPathData, userNodeOverrides(node, { fill: \"none\" }));\n    cylinder2 = shapeSvg.insert(() => innerLine, \":first-child\");\n    cylinder2 = shapeSvg.insert(() => outerNode, \":first-child\");\n    cylinder2.attr(\"class\", \"basic label-container\");\n    if (cssStyles) {\n      cylinder2.attr(\"style\", cssStyles);\n    }\n  } else {\n    const pathData = createCylinderPathD3(0, 0, w, h, rx, ry);\n    cylinder2 = shapeSvg.insert(\"path\", \":first-child\").attr(\"d\", pathData).attr(\"class\", \"basic label-container\").attr(\"style\", handleUndefinedAttr(cssStyles)).attr(\"style\", nodeStyles);\n    cylinder2.attr(\"class\", \"basic label-container\");\n    if (cssStyles) {\n      cylinder2.selectAll(\"path\").attr(\"style\", cssStyles);\n    }\n    if (nodeStyles) {\n      cylinder2.selectAll(\"path\").attr(\"style\", nodeStyles);\n    }\n  }\n  cylinder2.attr(\"label-offset-x\", rx);\n  cylinder2.attr(\"transform\", `translate(${-w / 2}, ${h / 2} )`);\n  label.attr(\n    \"transform\",\n    `translate(${-(bbox.width / 2) - rx - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) - (bbox.y - (bbox.top ?? 0))})`\n  );\n  updateNodeBounds(node, cylinder2);\n  node.intersect = function(point) {\n    const pos = intersect_default.rect(node, point);\n    const y = pos.y - (node.y ?? 0);\n    if (ry != 0 && (Math.abs(y) < (node.height ?? 0) / 2 || Math.abs(y) == (node.height ?? 0) / 2 && Math.abs(pos.x - (node.x ?? 0)) > (node.width ?? 0) / 2 - rx)) {\n      let x = rx * rx * (1 - y * y / (ry * ry));\n      if (x != 0) {\n        x = Math.sqrt(Math.abs(x));\n      }\n      x = rx - x;\n      if (point.x - (node.x ?? 0) > 0) {\n        x = -x;\n      }\n      pos.x += x;\n    }\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(tiltedCylinder, \"tiltedCylinder\");\n\n// src/rendering-util/rendering-elements/shapes/trapezoid.ts\nimport rough48 from \"roughjs\";\nasync function trapezoid(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: -3 * h / 6, y: 0 },\n    { x: w + 3 * h / 6, y: 0 },\n    { x: w, y: -h },\n    { x: 0, y: -h }\n  ];\n  let polygon;\n  const { cssStyles } = node;\n  if (node.look === \"handDrawn\") {\n    const rc = rough48.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    const roughNode = rc.path(pathData, options);\n    polygon = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-w / 2}, ${h / 2})`);\n    if (cssStyles) {\n      polygon.attr(\"style\", cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n  if (nodeStyles) {\n    polygon.attr(\"style\", nodeStyles);\n  }\n  node.width = w;\n  node.height = h;\n  updateNodeBounds(node, polygon);\n  node.intersect = function(point) {\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(trapezoid, \"trapezoid\");\n\n// src/rendering-util/rendering-elements/shapes/trapezoidalPentagon.ts\nimport rough49 from \"roughjs\";\nasync function trapezoidalPentagon(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const minWidth = 60, minHeight = 20;\n  const w = Math.max(minWidth, bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(minHeight, bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const { cssStyles } = node;\n  const rc = rough49.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [\n    { x: -w / 2 * 0.8, y: -h / 2 },\n    { x: w / 2 * 0.8, y: -h / 2 },\n    { x: w / 2, y: -h / 2 * 0.6 },\n    { x: w / 2, y: h / 2 },\n    { x: -w / 2, y: h / 2 },\n    { x: -w / 2, y: -h / 2 * 0.6 }\n  ];\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => shapeNode, \":first-child\");\n  polygon.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, polygon);\n  node.intersect = function(point) {\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(trapezoidalPentagon, \"trapezoidalPentagon\");\n\n// src/rendering-util/rendering-elements/shapes/triangle.ts\nimport rough50 from \"roughjs\";\nasync function triangle(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const useHtmlLabels = evaluate(getConfig2().flowchart?.htmlLabels);\n  const w = bbox.width + (node.padding ?? 0);\n  const h = w + bbox.height;\n  const tw = w + bbox.height;\n  const points = [\n    { x: 0, y: 0 },\n    { x: tw, y: 0 },\n    { x: tw / 2, y: -h }\n  ];\n  const { cssStyles } = node;\n  const rc = rough50.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const pathData = createPathFromPoints(points);\n  const roughNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => roughNode, \":first-child\").attr(\"transform\", `translate(${-h / 2}, ${h / 2})`);\n  if (cssStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    polygon.selectChildren(\"path\").attr(\"style\", nodeStyles);\n  }\n  node.width = w;\n  node.height = h;\n  updateNodeBounds(node, polygon);\n  label.attr(\n    \"transform\",\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))}, ${h / 2 - (bbox.height + (node.padding ?? 0) / (useHtmlLabels ? 2 : 1) - (bbox.y - (bbox.top ?? 0)))})`\n  );\n  node.intersect = function(point) {\n    log.info(\"Triangle intersect\", node, points, point);\n    return intersect_default.polygon(node, points, point);\n  };\n  return shapeSvg;\n}\n__name(triangle, \"triangle\");\n\n// src/rendering-util/rendering-elements/shapes/waveEdgedRectangle.ts\nimport rough51 from \"roughjs\";\nasync function waveEdgedRectangle(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const waveAmplitude = h / 8;\n  const finalH = h + waveAmplitude;\n  const { cssStyles } = node;\n  const minWidth = 70;\n  const widthDif = minWidth - w;\n  const extraW = widthDif > 0 ? widthDif / 2 : 0;\n  const rc = rough51.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [\n    { x: -w / 2 - extraW, y: finalH / 2 },\n    ...generateFullSineWavePoints(\n      -w / 2 - extraW,\n      finalH / 2,\n      w / 2 + extraW,\n      finalH / 2,\n      waveAmplitude,\n      0.8\n    ),\n    { x: w / 2 + extraW, y: -finalH / 2 },\n    { x: -w / 2 - extraW, y: -finalH / 2 }\n  ];\n  const waveEdgeRectPath = createPathFromPoints(points);\n  const waveEdgeRectNode = rc.path(waveEdgeRectPath, options);\n  const waveEdgeRect = shapeSvg.insert(() => waveEdgeRectNode, \":first-child\");\n  waveEdgeRect.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    waveEdgeRect.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    waveEdgeRect.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  waveEdgeRect.attr(\"transform\", `translate(0,${-waveAmplitude / 2})`);\n  label.attr(\n    \"transform\",\n    `translate(${-w / 2 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) - waveAmplitude - (bbox.y - (bbox.top ?? 0))})`\n  );\n  updateNodeBounds(node, waveEdgeRect);\n  node.intersect = function(point) {\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(waveEdgedRectangle, \"waveEdgedRectangle\");\n\n// src/rendering-util/rendering-elements/shapes/waveRectangle.ts\nimport rough52 from \"roughjs\";\nasync function waveRectangle(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const minWidth = 100;\n  const minHeight = 50;\n  const baseWidth = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const baseHeight = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const aspectRatio = baseWidth / baseHeight;\n  let w = baseWidth;\n  let h = baseHeight;\n  if (w > h * aspectRatio) {\n    h = w / aspectRatio;\n  } else {\n    w = h * aspectRatio;\n  }\n  w = Math.max(w, minWidth);\n  h = Math.max(h, minHeight);\n  const waveAmplitude = Math.min(h * 0.2, h / 4);\n  const finalH = h + waveAmplitude * 2;\n  const { cssStyles } = node;\n  const rc = rough52.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const points = [\n    { x: -w / 2, y: finalH / 2 },\n    ...generateFullSineWavePoints(-w / 2, finalH / 2, w / 2, finalH / 2, waveAmplitude, 1),\n    { x: w / 2, y: -finalH / 2 },\n    ...generateFullSineWavePoints(w / 2, -finalH / 2, -w / 2, -finalH / 2, waveAmplitude, -1)\n  ];\n  const waveRectPath = createPathFromPoints(points);\n  const waveRectNode = rc.path(waveRectPath, options);\n  const waveRect = shapeSvg.insert(() => waveRectNode, \":first-child\");\n  waveRect.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    waveRect.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    waveRect.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, waveRect);\n  node.intersect = function(point) {\n    const pos = intersect_default.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(waveRectangle, \"waveRectangle\");\n\n// src/rendering-util/rendering-elements/shapes/windowPane.ts\nimport rough53 from \"roughjs\";\nasync function windowPane(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const rectOffset = 5;\n  const x = -w / 2;\n  const y = -h / 2;\n  const { cssStyles } = node;\n  const rc = rough53.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  const outerPathPoints = [\n    { x: x - rectOffset, y: y - rectOffset },\n    { x: x - rectOffset, y: y + h },\n    { x: x + w, y: y + h },\n    { x: x + w, y: y - rectOffset }\n  ];\n  const path = `M${x - rectOffset},${y - rectOffset} L${x + w},${y - rectOffset} L${x + w},${y + h} L${x - rectOffset},${y + h} L${x - rectOffset},${y - rectOffset}\n                M${x - rectOffset},${y} L${x + w},${y}\n                M${x},${y - rectOffset} L${x},${y + h}`;\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const no = rc.path(path, options);\n  const windowPane2 = shapeSvg.insert(() => no, \":first-child\");\n  windowPane2.attr(\"transform\", `translate(${rectOffset / 2}, ${rectOffset / 2})`);\n  windowPane2.attr(\"class\", \"basic label-container\");\n  if (cssStyles && node.look !== \"handDrawn\") {\n    windowPane2.selectAll(\"path\").attr(\"style\", cssStyles);\n  }\n  if (nodeStyles && node.look !== \"handDrawn\") {\n    windowPane2.selectAll(\"path\").attr(\"style\", nodeStyles);\n  }\n  label.attr(\n    \"transform\",\n    `translate(${-(bbox.width / 2) + rectOffset / 2 - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + rectOffset / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n  updateNodeBounds(node, windowPane2);\n  node.intersect = function(point) {\n    const pos = intersect_default.polygon(node, outerPathPoints, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n__name(windowPane, \"windowPane\");\n\n// src/rendering-util/rendering-elements/shapes/erBox.ts\nimport rough54 from \"roughjs\";\nimport { select as select5 } from \"d3\";\nasync function erBox(parent, node) {\n  const entityNode = node;\n  if (entityNode.alias) {\n    node.label = entityNode.alias;\n  }\n  if (node.look === \"handDrawn\") {\n    const { themeVariables: themeVariables2 } = getConfig();\n    const { background } = themeVariables2;\n    const backgroundNode = {\n      ...node,\n      id: node.id + \"-background\",\n      look: \"default\",\n      cssStyles: [\"stroke: none\", `fill: ${background}`]\n    };\n    await erBox(parent, backgroundNode);\n  }\n  const config = getConfig();\n  node.useHtmlLabels = config.htmlLabels;\n  let PADDING = config.er?.diagramPadding ?? 10;\n  let TEXT_PADDING = config.er?.entityPadding ?? 6;\n  const { cssStyles } = node;\n  const { labelStyles } = styles2String(node);\n  if (entityNode.attributes.length === 0 && node.label) {\n    const options2 = {\n      rx: 0,\n      ry: 0,\n      labelPaddingX: PADDING,\n      labelPaddingY: PADDING * 1.5,\n      classes: \"\"\n    };\n    if (calculateTextWidth(node.label, config) + options2.labelPaddingX * 2 < config.er.minEntityWidth) {\n      node.width = config.er.minEntityWidth;\n    }\n    const shapeSvg2 = await drawRect(parent, node, options2);\n    if (!evaluate(config.htmlLabels)) {\n      const textElement = shapeSvg2.select(\"text\");\n      const bbox = textElement.node()?.getBBox();\n      textElement.attr(\"transform\", `translate(${-bbox.width / 2}, 0)`);\n    }\n    return shapeSvg2;\n  }\n  if (!config.htmlLabels) {\n    PADDING *= 1.25;\n    TEXT_PADDING *= 1.25;\n  }\n  let cssClasses = getNodeClasses(node);\n  if (!cssClasses) {\n    cssClasses = \"node default\";\n  }\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", cssClasses).attr(\"id\", node.domId || node.id);\n  const nameBBox = await addText(shapeSvg, node.label ?? \"\", config, 0, 0, [\"name\"], labelStyles);\n  nameBBox.height += TEXT_PADDING;\n  let yOffset = 0;\n  const yOffsets = [];\n  let maxTypeWidth = 0;\n  let maxNameWidth = 0;\n  let maxKeysWidth = 0;\n  let maxCommentWidth = 0;\n  let keysPresent = true;\n  let commentPresent = true;\n  for (const attribute of entityNode.attributes) {\n    const typeBBox = await addText(\n      shapeSvg,\n      attribute.type,\n      config,\n      0,\n      yOffset,\n      [\"attribute-type\"],\n      labelStyles\n    );\n    maxTypeWidth = Math.max(maxTypeWidth, typeBBox.width + PADDING);\n    const nameBBox2 = await addText(\n      shapeSvg,\n      attribute.name,\n      config,\n      0,\n      yOffset,\n      [\"attribute-name\"],\n      labelStyles\n    );\n    maxNameWidth = Math.max(maxNameWidth, nameBBox2.width + PADDING);\n    const keysBBox = await addText(\n      shapeSvg,\n      attribute.keys.join(),\n      config,\n      0,\n      yOffset,\n      [\"attribute-keys\"],\n      labelStyles\n    );\n    maxKeysWidth = Math.max(maxKeysWidth, keysBBox.width + PADDING);\n    const commentBBox = await addText(\n      shapeSvg,\n      attribute.comment,\n      config,\n      0,\n      yOffset,\n      [\"attribute-comment\"],\n      labelStyles\n    );\n    maxCommentWidth = Math.max(maxCommentWidth, commentBBox.width + PADDING);\n    yOffset += Math.max(typeBBox.height, nameBBox2.height, keysBBox.height, commentBBox.height) + TEXT_PADDING;\n    yOffsets.push(yOffset);\n  }\n  yOffsets.pop();\n  let totalWidthSections = 4;\n  if (maxKeysWidth <= PADDING) {\n    keysPresent = false;\n    maxKeysWidth = 0;\n    totalWidthSections--;\n  }\n  if (maxCommentWidth <= PADDING) {\n    commentPresent = false;\n    maxCommentWidth = 0;\n    totalWidthSections--;\n  }\n  const shapeBBox = shapeSvg.node().getBBox();\n  if (nameBBox.width + PADDING * 2 - (maxTypeWidth + maxNameWidth + maxKeysWidth + maxCommentWidth) > 0) {\n    const difference = nameBBox.width + PADDING * 2 - (maxTypeWidth + maxNameWidth + maxKeysWidth + maxCommentWidth);\n    maxTypeWidth += difference / totalWidthSections;\n    maxNameWidth += difference / totalWidthSections;\n    if (maxKeysWidth > 0) {\n      maxKeysWidth += difference / totalWidthSections;\n    }\n    if (maxCommentWidth > 0) {\n      maxCommentWidth += difference / totalWidthSections;\n    }\n  }\n  const maxWidth = maxTypeWidth + maxNameWidth + maxKeysWidth + maxCommentWidth;\n  const rc = rough54.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const w = Math.max(shapeBBox.width + PADDING * 2, node?.width || 0, maxWidth);\n  const h = Math.max(shapeBBox.height + (yOffsets[0] || yOffset) + TEXT_PADDING, node?.height || 0);\n  const x = -w / 2;\n  const y = -h / 2;\n  shapeSvg.selectAll(\"g:not(:first-child)\").each((_, i, nodes) => {\n    const text2 = select5(nodes[i]);\n    const transform = text2.attr(\"transform\");\n    let translateX = 0;\n    let translateY = 0;\n    if (transform) {\n      const regex = RegExp(/translate\\(([^,]+),([^)]+)\\)/);\n      const translate = regex.exec(transform);\n      if (translate) {\n        translateX = parseFloat(translate[1]);\n        translateY = parseFloat(translate[2]);\n        if (text2.attr(\"class\").includes(\"attribute-name\")) {\n          translateX += maxTypeWidth;\n        } else if (text2.attr(\"class\").includes(\"attribute-keys\")) {\n          translateX += maxTypeWidth + maxNameWidth;\n        } else if (text2.attr(\"class\").includes(\"attribute-comment\")) {\n          translateX += maxTypeWidth + maxNameWidth + maxKeysWidth;\n        }\n      }\n    }\n    text2.attr(\n      \"transform\",\n      `translate(${x + PADDING / 2 + translateX}, ${translateY + y + nameBBox.height + TEXT_PADDING / 2})`\n    );\n  });\n  shapeSvg.select(\".name\").attr(\"transform\", \"translate(\" + -nameBBox.width / 2 + \", \" + (y + TEXT_PADDING / 2) + \")\");\n  const roughRect = rc.rectangle(x, y, w, h, options);\n  const rect2 = shapeSvg.insert(() => roughRect, \":first-child\").attr(\"style\", cssStyles.join(\"\"));\n  const { themeVariables } = getConfig();\n  const { rowEven, rowOdd, nodeBorder } = themeVariables;\n  yOffsets.push(0);\n  for (const [i, yOffset2] of yOffsets.entries()) {\n    if (i === 0 && yOffsets.length > 1) {\n      continue;\n    }\n    const isEven = i % 2 === 0 && yOffset2 !== 0;\n    const roughRect2 = rc.rectangle(x, nameBBox.height + y + yOffset2, w, nameBBox.height, {\n      ...options,\n      fill: isEven ? rowEven : rowOdd,\n      stroke: nodeBorder\n    });\n    shapeSvg.insert(() => roughRect2, \"g.label\").attr(\"style\", cssStyles.join(\"\")).attr(\"class\", `row-rect-${i % 2 === 0 ? \"even\" : \"odd\"}`);\n  }\n  let roughLine = rc.line(x, nameBBox.height + y, w + x, nameBBox.height + y, options);\n  shapeSvg.insert(() => roughLine).attr(\"class\", \"divider\");\n  roughLine = rc.line(maxTypeWidth + x, nameBBox.height + y, maxTypeWidth + x, h + y, options);\n  shapeSvg.insert(() => roughLine).attr(\"class\", \"divider\");\n  if (keysPresent) {\n    roughLine = rc.line(\n      maxTypeWidth + maxNameWidth + x,\n      nameBBox.height + y,\n      maxTypeWidth + maxNameWidth + x,\n      h + y,\n      options\n    );\n    shapeSvg.insert(() => roughLine).attr(\"class\", \"divider\");\n  }\n  if (commentPresent) {\n    roughLine = rc.line(\n      maxTypeWidth + maxNameWidth + maxKeysWidth + x,\n      nameBBox.height + y,\n      maxTypeWidth + maxNameWidth + maxKeysWidth + x,\n      h + y,\n      options\n    );\n    shapeSvg.insert(() => roughLine).attr(\"class\", \"divider\");\n  }\n  for (const yOffset2 of yOffsets) {\n    roughLine = rc.line(\n      x,\n      nameBBox.height + y + yOffset2,\n      w + x,\n      nameBBox.height + y + yOffset2,\n      options\n    );\n    shapeSvg.insert(() => roughLine).attr(\"class\", \"divider\");\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(erBox, \"erBox\");\nasync function addText(shapeSvg, labelText, config, translateX = 0, translateY = 0, classes = [], style = \"\") {\n  const label = shapeSvg.insert(\"g\").attr(\"class\", `label ${classes.join(\" \")}`).attr(\"transform\", `translate(${translateX}, ${translateY})`).attr(\"style\", style);\n  if (labelText !== parseGenericTypes(labelText)) {\n    labelText = parseGenericTypes(labelText);\n    labelText = labelText.replaceAll(\"<\", \"&lt;\").replaceAll(\">\", \"&gt;\");\n  }\n  const text2 = label.node().appendChild(\n    await createText(\n      label,\n      labelText,\n      {\n        width: calculateTextWidth(labelText, config) + 100,\n        style,\n        useHtmlLabels: config.htmlLabels\n      },\n      config\n    )\n  );\n  if (labelText.includes(\"&lt;\") || labelText.includes(\"&gt;\")) {\n    let child = text2.children[0];\n    child.textContent = child.textContent.replaceAll(\"&lt;\", \"<\").replaceAll(\"&gt;\", \">\");\n    while (child.childNodes[0]) {\n      child = child.childNodes[0];\n      child.textContent = child.textContent.replaceAll(\"&lt;\", \"<\").replaceAll(\"&gt;\", \">\");\n    }\n  }\n  let bbox = text2.getBBox();\n  if (evaluate(config.htmlLabels)) {\n    const div = text2.children[0];\n    div.style.textAlign = \"start\";\n    const dv = select5(text2);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  return bbox;\n}\n__name(addText, \"addText\");\n\n// src/rendering-util/rendering-elements/shapes/classBox.ts\nimport { select as select7 } from \"d3\";\nimport rough55 from \"roughjs\";\n\n// src/diagrams/class/shapeUtil.ts\nimport { select as select6 } from \"d3\";\nasync function textHelper(parent, node, config, useHtmlLabels, GAP = config.class.padding ?? 12) {\n  const TEXT_PADDING = !useHtmlLabels ? 3 : 0;\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", getNodeClasses(node)).attr(\"id\", node.domId || node.id);\n  let annotationGroup = null;\n  let labelGroup = null;\n  let membersGroup = null;\n  let methodsGroup = null;\n  let annotationGroupHeight = 0;\n  let labelGroupHeight = 0;\n  let membersGroupHeight = 0;\n  annotationGroup = shapeSvg.insert(\"g\").attr(\"class\", \"annotation-group text\");\n  if (node.annotations.length > 0) {\n    const annotation = node.annotations[0];\n    await addText2(annotationGroup, { text: `\\xAB${annotation}\\xBB` }, 0);\n    const annotationGroupBBox = annotationGroup.node().getBBox();\n    annotationGroupHeight = annotationGroupBBox.height;\n  }\n  labelGroup = shapeSvg.insert(\"g\").attr(\"class\", \"label-group text\");\n  await addText2(labelGroup, node, 0, [\"font-weight: bolder\"]);\n  const labelGroupBBox = labelGroup.node().getBBox();\n  labelGroupHeight = labelGroupBBox.height;\n  membersGroup = shapeSvg.insert(\"g\").attr(\"class\", \"members-group text\");\n  let yOffset = 0;\n  for (const member of node.members) {\n    const height = await addText2(membersGroup, member, yOffset, [member.parseClassifier()]);\n    yOffset += height + TEXT_PADDING;\n  }\n  membersGroupHeight = membersGroup.node().getBBox().height;\n  if (membersGroupHeight <= 0) {\n    membersGroupHeight = GAP / 2;\n  }\n  methodsGroup = shapeSvg.insert(\"g\").attr(\"class\", \"methods-group text\");\n  let methodsYOffset = 0;\n  for (const method of node.methods) {\n    const height = await addText2(methodsGroup, method, methodsYOffset, [method.parseClassifier()]);\n    methodsYOffset += height + TEXT_PADDING;\n  }\n  let bbox = shapeSvg.node().getBBox();\n  if (annotationGroup !== null) {\n    const annotationGroupBBox = annotationGroup.node().getBBox();\n    annotationGroup.attr(\"transform\", `translate(${-annotationGroupBBox.width / 2})`);\n  }\n  labelGroup.attr(\"transform\", `translate(${-labelGroupBBox.width / 2}, ${annotationGroupHeight})`);\n  bbox = shapeSvg.node().getBBox();\n  membersGroup.attr(\n    \"transform\",\n    `translate(${0}, ${annotationGroupHeight + labelGroupHeight + GAP * 2})`\n  );\n  bbox = shapeSvg.node().getBBox();\n  methodsGroup.attr(\n    \"transform\",\n    `translate(${0}, ${annotationGroupHeight + labelGroupHeight + (membersGroupHeight ? membersGroupHeight + GAP * 4 : GAP * 2)})`\n  );\n  bbox = shapeSvg.node().getBBox();\n  return { shapeSvg, bbox };\n}\n__name(textHelper, \"textHelper\");\nasync function addText2(parentGroup, node, yOffset, styles = []) {\n  const textEl = parentGroup.insert(\"g\").attr(\"class\", \"label\").attr(\"style\", styles.join(\"; \"));\n  const config = getConfig();\n  let useHtmlLabels = \"useHtmlLabels\" in node ? node.useHtmlLabels : evaluate(config.htmlLabels) ?? true;\n  let textContent = \"\";\n  if (\"text\" in node) {\n    textContent = node.text;\n  } else {\n    textContent = node.label;\n  }\n  if (!useHtmlLabels && textContent.startsWith(\"\\\\\")) {\n    textContent = textContent.substring(1);\n  }\n  if (hasKatex(textContent)) {\n    useHtmlLabels = true;\n  }\n  const text2 = await createText(\n    textEl,\n    sanitizeText2(decodeEntities(textContent)),\n    {\n      width: calculateTextWidth(textContent, config) + 50,\n      // Add room for error when splitting text into multiple lines\n      classes: \"markdown-node-label\",\n      useHtmlLabels\n    },\n    config\n  );\n  let bbox;\n  let numberOfLines = 1;\n  if (!useHtmlLabels) {\n    if (styles.includes(\"font-weight: bolder\")) {\n      select6(text2).selectAll(\"tspan\").attr(\"font-weight\", \"\");\n    }\n    numberOfLines = text2.children.length;\n    const textChild = text2.children[0];\n    if (text2.textContent === \"\" || text2.textContent.includes(\"&gt\")) {\n      textChild.textContent = textContent[0] + textContent.substring(1).replaceAll(\"&gt;\", \">\").replaceAll(\"&lt;\", \"<\").trim();\n      const preserveSpace = textContent[1] === \" \";\n      if (preserveSpace) {\n        textChild.textContent = textChild.textContent[0] + \" \" + textChild.textContent.substring(1);\n      }\n    }\n    if (textChild.textContent === \"undefined\") {\n      textChild.textContent = \"\";\n    }\n    bbox = text2.getBBox();\n  } else {\n    const div = text2.children[0];\n    const dv = select6(text2);\n    numberOfLines = div.innerHTML.split(\"<br>\").length;\n    if (div.innerHTML.includes(\"</math>\")) {\n      numberOfLines += div.innerHTML.split(\"<mrow>\").length - 1;\n    }\n    const images = div.getElementsByTagName(\"img\");\n    if (images) {\n      const noImgText = textContent.replace(/<img[^>]*>/g, \"\").trim() === \"\";\n      await Promise.all(\n        [...images].map(\n          (img) => new Promise((res) => {\n            function setupImage() {\n              img.style.display = \"flex\";\n              img.style.flexDirection = \"column\";\n              if (noImgText) {\n                const bodyFontSize = config.fontSize?.toString() ?? window.getComputedStyle(document.body).fontSize;\n                const enlargingFactor = 5;\n                const width = parseInt(bodyFontSize, 10) * enlargingFactor + \"px\";\n                img.style.minWidth = width;\n                img.style.maxWidth = width;\n              } else {\n                img.style.width = \"100%\";\n              }\n              res(img);\n            }\n            __name(setupImage, \"setupImage\");\n            setTimeout(() => {\n              if (img.complete) {\n                setupImage();\n              }\n            });\n            img.addEventListener(\"error\", setupImage);\n            img.addEventListener(\"load\", setupImage);\n          })\n        )\n      );\n    }\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  textEl.attr(\"transform\", \"translate(0,\" + (-bbox.height / (2 * numberOfLines) + yOffset) + \")\");\n  return bbox.height;\n}\n__name(addText2, \"addText\");\n\n// src/rendering-util/rendering-elements/shapes/classBox.ts\nasync function classBox(parent, node) {\n  const config = getConfig2();\n  const PADDING = config.class.padding ?? 12;\n  const GAP = PADDING;\n  const useHtmlLabels = node.useHtmlLabels ?? evaluate(config.htmlLabels) ?? true;\n  const classNode = node;\n  classNode.annotations = classNode.annotations ?? [];\n  classNode.members = classNode.members ?? [];\n  classNode.methods = classNode.methods ?? [];\n  const { shapeSvg, bbox } = await textHelper(parent, node, config, useHtmlLabels, GAP);\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  node.cssStyles = classNode.styles || \"\";\n  const styles = classNode.styles?.join(\";\") || nodeStyles || \"\";\n  if (!node.cssStyles) {\n    node.cssStyles = styles.replaceAll(\"!important\", \"\").split(\";\");\n  }\n  const renderExtraBox = classNode.members.length === 0 && classNode.methods.length === 0 && !config.class?.hideEmptyMembersBox;\n  const rc = rough55.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const w = bbox.width;\n  let h = bbox.height;\n  if (classNode.members.length === 0 && classNode.methods.length === 0) {\n    h += GAP;\n  } else if (classNode.members.length > 0 && classNode.methods.length === 0) {\n    h += GAP * 2;\n  }\n  const x = -w / 2;\n  const y = -h / 2;\n  const roughRect = rc.rectangle(\n    x - PADDING,\n    y - PADDING - (renderExtraBox ? PADDING : classNode.members.length === 0 && classNode.methods.length === 0 ? -PADDING / 2 : 0),\n    w + 2 * PADDING,\n    h + 2 * PADDING + (renderExtraBox ? PADDING * 2 : classNode.members.length === 0 && classNode.methods.length === 0 ? -PADDING : 0),\n    options\n  );\n  const rect2 = shapeSvg.insert(() => roughRect, \":first-child\");\n  rect2.attr(\"class\", \"basic label-container\");\n  const rectBBox = rect2.node().getBBox();\n  shapeSvg.selectAll(\".text\").each((_, i, nodes) => {\n    const text2 = select7(nodes[i]);\n    const transform = text2.attr(\"transform\");\n    let translateY = 0;\n    if (transform) {\n      const regex = RegExp(/translate\\(([^,]+),([^)]+)\\)/);\n      const translate = regex.exec(transform);\n      if (translate) {\n        translateY = parseFloat(translate[2]);\n      }\n    }\n    let newTranslateY = translateY + y + PADDING - (renderExtraBox ? PADDING : classNode.members.length === 0 && classNode.methods.length === 0 ? -PADDING / 2 : 0);\n    if (!useHtmlLabels) {\n      newTranslateY -= 4;\n    }\n    let newTranslateX = x;\n    if (text2.attr(\"class\").includes(\"label-group\") || text2.attr(\"class\").includes(\"annotation-group\")) {\n      newTranslateX = -text2.node()?.getBBox().width / 2 || 0;\n      shapeSvg.selectAll(\"text\").each(function(_2, i2, nodes2) {\n        if (window.getComputedStyle(nodes2[i2]).textAnchor === \"middle\") {\n          newTranslateX = 0;\n        }\n      });\n    }\n    text2.attr(\"transform\", `translate(${newTranslateX}, ${newTranslateY})`);\n  });\n  const annotationGroupHeight = shapeSvg.select(\".annotation-group\").node().getBBox().height - (renderExtraBox ? PADDING / 2 : 0) || 0;\n  const labelGroupHeight = shapeSvg.select(\".label-group\").node().getBBox().height - (renderExtraBox ? PADDING / 2 : 0) || 0;\n  const membersGroupHeight = shapeSvg.select(\".members-group\").node().getBBox().height - (renderExtraBox ? PADDING / 2 : 0) || 0;\n  if (classNode.members.length > 0 || classNode.methods.length > 0 || renderExtraBox) {\n    const roughLine = rc.line(\n      rectBBox.x,\n      annotationGroupHeight + labelGroupHeight + y + PADDING,\n      rectBBox.x + rectBBox.width,\n      annotationGroupHeight + labelGroupHeight + y + PADDING,\n      options\n    );\n    const line = shapeSvg.insert(() => roughLine);\n    line.attr(\"class\", \"divider\").attr(\"style\", styles);\n  }\n  if (renderExtraBox || classNode.members.length > 0 || classNode.methods.length > 0) {\n    const roughLine = rc.line(\n      rectBBox.x,\n      annotationGroupHeight + labelGroupHeight + membersGroupHeight + y + GAP * 2 + PADDING,\n      rectBBox.x + rectBBox.width,\n      annotationGroupHeight + labelGroupHeight + membersGroupHeight + y + PADDING + GAP * 2,\n      options\n    );\n    const line = shapeSvg.insert(() => roughLine);\n    line.attr(\"class\", \"divider\").attr(\"style\", styles);\n  }\n  if (classNode.look !== \"handDrawn\") {\n    shapeSvg.selectAll(\"path\").attr(\"style\", styles);\n  }\n  rect2.select(\":nth-child(2)\").attr(\"style\", styles);\n  shapeSvg.selectAll(\".divider\").select(\"path\").attr(\"style\", styles);\n  if (node.labelStyle) {\n    shapeSvg.selectAll(\"span\").attr(\"style\", node.labelStyle);\n  } else {\n    shapeSvg.selectAll(\"span\").attr(\"style\", styles);\n  }\n  if (!useHtmlLabels) {\n    const colorRegex = RegExp(/color\\s*:\\s*([^;]*)/);\n    const match = colorRegex.exec(styles);\n    if (match) {\n      const colorStyle = match[0].replace(\"color\", \"fill\");\n      shapeSvg.selectAll(\"tspan\").attr(\"style\", colorStyle);\n    } else if (labelStyles) {\n      const match2 = colorRegex.exec(labelStyles);\n      if (match2) {\n        const colorStyle = match2[0].replace(\"color\", \"fill\");\n        shapeSvg.selectAll(\"tspan\").attr(\"style\", colorStyle);\n      }\n    }\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(classBox, \"classBox\");\n\n// src/rendering-util/rendering-elements/shapes/requirementBox.ts\nimport rough56 from \"roughjs\";\nimport { select as select8 } from \"d3\";\nasync function requirementBox(parent, node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const requirementNode = node;\n  const elementNode = node;\n  const padding = 20;\n  const gap = 20;\n  const isRequirementNode = \"verifyMethod\" in node;\n  const classes = getNodeClasses(node);\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", classes).attr(\"id\", node.domId ?? node.id);\n  let typeHeight;\n  if (isRequirementNode) {\n    typeHeight = await addText3(\n      shapeSvg,\n      `&lt;&lt;${requirementNode.type}&gt;&gt;`,\n      0,\n      node.labelStyle\n    );\n  } else {\n    typeHeight = await addText3(shapeSvg, \"&lt;&lt;Element&gt;&gt;\", 0, node.labelStyle);\n  }\n  let accumulativeHeight = typeHeight;\n  const nameHeight = await addText3(\n    shapeSvg,\n    requirementNode.name,\n    accumulativeHeight,\n    node.labelStyle + \"; font-weight: bold;\"\n  );\n  accumulativeHeight += nameHeight + gap;\n  if (isRequirementNode) {\n    const idHeight = await addText3(\n      shapeSvg,\n      `${requirementNode.requirementId ? `Id: ${requirementNode.requirementId}` : \"\"}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n    accumulativeHeight += idHeight;\n    const textHeight = await addText3(\n      shapeSvg,\n      `${requirementNode.text ? `Text: ${requirementNode.text}` : \"\"}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n    accumulativeHeight += textHeight;\n    const riskHeight = await addText3(\n      shapeSvg,\n      `${requirementNode.risk ? `Risk: ${requirementNode.risk}` : \"\"}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n    accumulativeHeight += riskHeight;\n    await addText3(\n      shapeSvg,\n      `${requirementNode.verifyMethod ? `Verification: ${requirementNode.verifyMethod}` : \"\"}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n  } else {\n    const typeHeight2 = await addText3(\n      shapeSvg,\n      `${elementNode.type ? `Type: ${elementNode.type}` : \"\"}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n    accumulativeHeight += typeHeight2;\n    await addText3(\n      shapeSvg,\n      `${elementNode.docRef ? `Doc Ref: ${elementNode.docRef}` : \"\"}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n  }\n  const totalWidth = (shapeSvg.node()?.getBBox().width ?? 200) + padding;\n  const totalHeight = (shapeSvg.node()?.getBBox().height ?? 200) + padding;\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n  const rc = rough56.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== \"handDrawn\") {\n    options.roughness = 0;\n    options.fillStyle = \"solid\";\n  }\n  const roughRect = rc.rectangle(x, y, totalWidth, totalHeight, options);\n  const rect2 = shapeSvg.insert(() => roughRect, \":first-child\");\n  rect2.attr(\"class\", \"basic label-container\").attr(\"style\", nodeStyles);\n  shapeSvg.selectAll(\".label\").each((_, i, nodes) => {\n    const text2 = select8(nodes[i]);\n    const transform = text2.attr(\"transform\");\n    let translateX = 0;\n    let translateY = 0;\n    if (transform) {\n      const regex = RegExp(/translate\\(([^,]+),([^)]+)\\)/);\n      const translate = regex.exec(transform);\n      if (translate) {\n        translateX = parseFloat(translate[1]);\n        translateY = parseFloat(translate[2]);\n      }\n    }\n    const newTranslateY = translateY - totalHeight / 2;\n    let newTranslateX = x + padding / 2;\n    if (i === 0 || i === 1) {\n      newTranslateX = translateX;\n    }\n    text2.attr(\"transform\", `translate(${newTranslateX}, ${newTranslateY + padding})`);\n  });\n  if (accumulativeHeight > typeHeight + nameHeight + gap) {\n    const roughLine = rc.line(\n      x,\n      y + typeHeight + nameHeight + gap,\n      x + totalWidth,\n      y + typeHeight + nameHeight + gap,\n      options\n    );\n    const dividerLine = shapeSvg.insert(() => roughLine);\n    dividerLine.attr(\"style\", nodeStyles);\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point) {\n    return intersect_default.rect(node, point);\n  };\n  return shapeSvg;\n}\n__name(requirementBox, \"requirementBox\");\nasync function addText3(parentGroup, inputText, yOffset, style = \"\") {\n  if (inputText === \"\") {\n    return 0;\n  }\n  const textEl = parentGroup.insert(\"g\").attr(\"class\", \"label\").attr(\"style\", style);\n  const config = getConfig2();\n  const useHtmlLabels = config.htmlLabels ?? true;\n  const text2 = await createText(\n    textEl,\n    sanitizeText2(decodeEntities(inputText)),\n    {\n      width: calculateTextWidth(inputText, config) + 50,\n      // Add room for error when splitting text into multiple lines\n      classes: \"markdown-node-label\",\n      useHtmlLabels,\n      style\n    },\n    config\n  );\n  let bbox;\n  if (!useHtmlLabels) {\n    const textChild = text2.children[0];\n    for (const child of textChild.children) {\n      child.textContent = child.textContent.replaceAll(\"&gt;\", \">\").replaceAll(\"&lt;\", \"<\");\n      if (style) {\n        child.setAttribute(\"style\", style);\n      }\n    }\n    bbox = text2.getBBox();\n    bbox.height += 6;\n  } else {\n    const div = text2.children[0];\n    const dv = select8(text2);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  textEl.attr(\"transform\", `translate(${-bbox.width / 2},${-bbox.height / 2 + yOffset})`);\n  return bbox.height;\n}\n__name(addText3, \"addText\");\n\n// src/rendering-util/rendering-elements/shapes/kanbanItem.ts\nimport rough57 from \"roughjs\";\nvar colorFromPriority = /* @__PURE__ */ __name((priority) => {\n  switch (priority) {\n    case \"Very High\":\n      return \"red\";\n    case \"High\":\n      return \"orange\";\n    case \"Medium\":\n      return null;\n    // no stroke\n    case \"Low\":\n      return \"blue\";\n    case \"Very Low\":\n      return \"lightblue\";\n  }\n}, \"colorFromPriority\");\nasync function kanbanItem(parent, kanbanNode, { config }) {\n  const { labelStyles, nodeStyles } = styles2String(kanbanNode);\n  kanbanNode.labelStyle = labelStyles || \"\";\n  const labelPaddingX = 10;\n  const orgWidth = kanbanNode.width;\n  kanbanNode.width = (kanbanNode.width ?? 200) - 10;\n  const {\n    shapeSvg,\n    bbox,\n    label: labelElTitle\n  } = await labelHelper(parent, kanbanNode, getNodeClasses(kanbanNode));\n  const padding = kanbanNode.padding || 10;\n  let ticketUrl = \"\";\n  let link;\n  if (\"ticket\" in kanbanNode && kanbanNode.ticket && config?.kanban?.ticketBaseUrl) {\n    ticketUrl = config?.kanban?.ticketBaseUrl.replace(\"#TICKET#\", kanbanNode.ticket);\n    link = shapeSvg.insert(\"svg:a\", \":first-child\").attr(\"class\", \"kanban-ticket-link\").attr(\"xlink:href\", ticketUrl).attr(\"target\", \"_blank\");\n  }\n  const options = {\n    useHtmlLabels: kanbanNode.useHtmlLabels,\n    labelStyle: kanbanNode.labelStyle || \"\",\n    width: kanbanNode.width,\n    img: kanbanNode.img,\n    padding: kanbanNode.padding || 8,\n    centerLabel: false\n  };\n  let labelEl, bbox2;\n  if (link) {\n    ({ label: labelEl, bbox: bbox2 } = await insertLabel(\n      link,\n      \"ticket\" in kanbanNode && kanbanNode.ticket || \"\",\n      options\n    ));\n  } else {\n    ({ label: labelEl, bbox: bbox2 } = await insertLabel(\n      shapeSvg,\n      \"ticket\" in kanbanNode && kanbanNode.ticket || \"\",\n      options\n    ));\n  }\n  const { label: labelElAssigned, bbox: bboxAssigned } = await insertLabel(\n    shapeSvg,\n    \"assigned\" in kanbanNode && kanbanNode.assigned || \"\",\n    options\n  );\n  kanbanNode.width = orgWidth;\n  const labelPaddingY = 10;\n  const totalWidth = kanbanNode?.width || 0;\n  const heightAdj = Math.max(bbox2.height, bboxAssigned.height) / 2;\n  const totalHeight = Math.max(bbox.height + labelPaddingY * 2, kanbanNode?.height || 0) + heightAdj;\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n  labelElTitle.attr(\n    \"transform\",\n    \"translate(\" + (padding - totalWidth / 2) + \", \" + (-heightAdj - bbox.height / 2) + \")\"\n  );\n  labelEl.attr(\n    \"transform\",\n    \"translate(\" + (padding - totalWidth / 2) + \", \" + (-heightAdj + bbox.height / 2) + \")\"\n  );\n  labelElAssigned.attr(\n    \"transform\",\n    \"translate(\" + (padding + totalWidth / 2 - bboxAssigned.width - 2 * labelPaddingX) + \", \" + (-heightAdj + bbox.height / 2) + \")\"\n  );\n  let rect2;\n  const { rx, ry } = kanbanNode;\n  const { cssStyles } = kanbanNode;\n  if (kanbanNode.look === \"handDrawn\") {\n    const rc = rough57.svg(shapeSvg);\n    const options2 = userNodeOverrides(kanbanNode, {});\n    const roughNode = rx || ry ? rc.path(createRoundedRectPathD(x, y, totalWidth, totalHeight, rx || 0), options2) : rc.rectangle(x, y, totalWidth, totalHeight, options2);\n    rect2 = shapeSvg.insert(() => roughNode, \":first-child\");\n    rect2.attr(\"class\", \"basic label-container\").attr(\"style\", cssStyles ? cssStyles : null);\n  } else {\n    rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n    rect2.attr(\"class\", \"basic label-container __APA__\").attr(\"style\", nodeStyles).attr(\"rx\", rx ?? 5).attr(\"ry\", ry ?? 5).attr(\"x\", x).attr(\"y\", y).attr(\"width\", totalWidth).attr(\"height\", totalHeight);\n    const priority = \"priority\" in kanbanNode && kanbanNode.priority;\n    if (priority) {\n      const line = shapeSvg.append(\"line\");\n      const lineX = x + 2;\n      const y1 = y + Math.floor((rx ?? 0) / 2);\n      const y2 = y + totalHeight - Math.floor((rx ?? 0) / 2);\n      line.attr(\"x1\", lineX).attr(\"y1\", y1).attr(\"x2\", lineX).attr(\"y2\", y2).attr(\"stroke-width\", \"4\").attr(\"stroke\", colorFromPriority(priority));\n    }\n  }\n  updateNodeBounds(kanbanNode, rect2);\n  kanbanNode.height = totalHeight;\n  kanbanNode.intersect = function(point) {\n    return intersect_default.rect(kanbanNode, point);\n  };\n  return shapeSvg;\n}\n__name(kanbanItem, \"kanbanItem\");\n\n// src/rendering-util/rendering-elements/shapes.ts\nvar shapesDefs = [\n  {\n    semanticName: \"Process\",\n    name: \"Rectangle\",\n    shortName: \"rect\",\n    description: \"Standard process shape\",\n    aliases: [\"proc\", \"process\", \"rectangle\"],\n    internalAliases: [\"squareRect\"],\n    handler: squareRect2\n  },\n  {\n    semanticName: \"Event\",\n    name: \"Rounded Rectangle\",\n    shortName: \"rounded\",\n    description: \"Represents an event\",\n    aliases: [\"event\"],\n    internalAliases: [\"roundedRect\"],\n    handler: roundedRect\n  },\n  {\n    semanticName: \"Terminal Point\",\n    name: \"Stadium\",\n    shortName: \"stadium\",\n    description: \"Terminal point\",\n    aliases: [\"terminal\", \"pill\"],\n    handler: stadium\n  },\n  {\n    semanticName: \"Subprocess\",\n    name: \"Framed Rectangle\",\n    shortName: \"fr-rect\",\n    description: \"Subprocess\",\n    aliases: [\"subprocess\", \"subproc\", \"framed-rectangle\", \"subroutine\"],\n    handler: subroutine\n  },\n  {\n    semanticName: \"Database\",\n    name: \"Cylinder\",\n    shortName: \"cyl\",\n    description: \"Database storage\",\n    aliases: [\"db\", \"database\", \"cylinder\"],\n    handler: cylinder\n  },\n  {\n    semanticName: \"Start\",\n    name: \"Circle\",\n    shortName: \"circle\",\n    description: \"Starting point\",\n    aliases: [\"circ\"],\n    handler: circle\n  },\n  {\n    semanticName: \"Decision\",\n    name: \"Diamond\",\n    shortName: \"diam\",\n    description: \"Decision-making step\",\n    aliases: [\"decision\", \"diamond\", \"question\"],\n    handler: question\n  },\n  {\n    semanticName: \"Prepare Conditional\",\n    name: \"Hexagon\",\n    shortName: \"hex\",\n    description: \"Preparation or condition step\",\n    aliases: [\"hexagon\", \"prepare\"],\n    handler: hexagon\n  },\n  {\n    semanticName: \"Data Input/Output\",\n    name: \"Lean Right\",\n    shortName: \"lean-r\",\n    description: \"Represents input or output\",\n    aliases: [\"lean-right\", \"in-out\"],\n    internalAliases: [\"lean_right\"],\n    handler: lean_right\n  },\n  {\n    semanticName: \"Data Input/Output\",\n    name: \"Lean Left\",\n    shortName: \"lean-l\",\n    description: \"Represents output or input\",\n    aliases: [\"lean-left\", \"out-in\"],\n    internalAliases: [\"lean_left\"],\n    handler: lean_left\n  },\n  {\n    semanticName: \"Priority Action\",\n    name: \"Trapezoid Base Bottom\",\n    shortName: \"trap-b\",\n    description: \"Priority action\",\n    aliases: [\"priority\", \"trapezoid-bottom\", \"trapezoid\"],\n    handler: trapezoid\n  },\n  {\n    semanticName: \"Manual Operation\",\n    name: \"Trapezoid Base Top\",\n    shortName: \"trap-t\",\n    description: \"Represents a manual task\",\n    aliases: [\"manual\", \"trapezoid-top\", \"inv-trapezoid\"],\n    internalAliases: [\"inv_trapezoid\"],\n    handler: inv_trapezoid\n  },\n  {\n    semanticName: \"Stop\",\n    name: \"Double Circle\",\n    shortName: \"dbl-circ\",\n    description: \"Represents a stop point\",\n    aliases: [\"double-circle\"],\n    internalAliases: [\"doublecircle\"],\n    handler: doublecircle\n  },\n  {\n    semanticName: \"Text Block\",\n    name: \"Text Block\",\n    shortName: \"text\",\n    description: \"Text block\",\n    handler: text\n  },\n  {\n    semanticName: \"Card\",\n    name: \"Notched Rectangle\",\n    shortName: \"notch-rect\",\n    description: \"Represents a card\",\n    aliases: [\"card\", \"notched-rectangle\"],\n    handler: card\n  },\n  {\n    semanticName: \"Lined/Shaded Process\",\n    name: \"Lined Rectangle\",\n    shortName: \"lin-rect\",\n    description: \"Lined process shape\",\n    aliases: [\"lined-rectangle\", \"lined-process\", \"lin-proc\", \"shaded-process\"],\n    handler: shadedProcess\n  },\n  {\n    semanticName: \"Start\",\n    name: \"Small Circle\",\n    shortName: \"sm-circ\",\n    description: \"Small starting point\",\n    aliases: [\"start\", \"small-circle\"],\n    internalAliases: [\"stateStart\"],\n    handler: stateStart\n  },\n  {\n    semanticName: \"Stop\",\n    name: \"Framed Circle\",\n    shortName: \"fr-circ\",\n    description: \"Stop point\",\n    aliases: [\"stop\", \"framed-circle\"],\n    internalAliases: [\"stateEnd\"],\n    handler: stateEnd\n  },\n  {\n    semanticName: \"Fork/Join\",\n    name: \"Filled Rectangle\",\n    shortName: \"fork\",\n    description: \"Fork or join in process flow\",\n    aliases: [\"join\"],\n    internalAliases: [\"forkJoin\"],\n    handler: forkJoin\n  },\n  {\n    semanticName: \"Collate\",\n    name: \"Hourglass\",\n    shortName: \"hourglass\",\n    description: \"Represents a collate operation\",\n    aliases: [\"hourglass\", \"collate\"],\n    handler: hourglass\n  },\n  {\n    semanticName: \"Comment\",\n    name: \"Curly Brace\",\n    shortName: \"brace\",\n    description: \"Adds a comment\",\n    aliases: [\"comment\", \"brace-l\"],\n    handler: curlyBraceLeft\n  },\n  {\n    semanticName: \"Comment Right\",\n    name: \"Curly Brace\",\n    shortName: \"brace-r\",\n    description: \"Adds a comment\",\n    handler: curlyBraceRight\n  },\n  {\n    semanticName: \"Comment with braces on both sides\",\n    name: \"Curly Braces\",\n    shortName: \"braces\",\n    description: \"Adds a comment\",\n    handler: curlyBraces\n  },\n  {\n    semanticName: \"Com Link\",\n    name: \"Lightning Bolt\",\n    shortName: \"bolt\",\n    description: \"Communication link\",\n    aliases: [\"com-link\", \"lightning-bolt\"],\n    handler: lightningBolt\n  },\n  {\n    semanticName: \"Document\",\n    name: \"Document\",\n    shortName: \"doc\",\n    description: \"Represents a document\",\n    aliases: [\"doc\", \"document\"],\n    handler: waveEdgedRectangle\n  },\n  {\n    semanticName: \"Delay\",\n    name: \"Half-Rounded Rectangle\",\n    shortName: \"delay\",\n    description: \"Represents a delay\",\n    aliases: [\"half-rounded-rectangle\"],\n    handler: halfRoundedRectangle\n  },\n  {\n    semanticName: \"Direct Access Storage\",\n    name: \"Horizontal Cylinder\",\n    shortName: \"h-cyl\",\n    description: \"Direct access storage\",\n    aliases: [\"das\", \"horizontal-cylinder\"],\n    handler: tiltedCylinder\n  },\n  {\n    semanticName: \"Disk Storage\",\n    name: \"Lined Cylinder\",\n    shortName: \"lin-cyl\",\n    description: \"Disk storage\",\n    aliases: [\"disk\", \"lined-cylinder\"],\n    handler: linedCylinder\n  },\n  {\n    semanticName: \"Display\",\n    name: \"Curved Trapezoid\",\n    shortName: \"curv-trap\",\n    description: \"Represents a display\",\n    aliases: [\"curved-trapezoid\", \"display\"],\n    handler: curvedTrapezoid\n  },\n  {\n    semanticName: \"Divided Process\",\n    name: \"Divided Rectangle\",\n    shortName: \"div-rect\",\n    description: \"Divided process shape\",\n    aliases: [\"div-proc\", \"divided-rectangle\", \"divided-process\"],\n    handler: dividedRectangle\n  },\n  {\n    semanticName: \"Extract\",\n    name: \"Triangle\",\n    shortName: \"tri\",\n    description: \"Extraction process\",\n    aliases: [\"extract\", \"triangle\"],\n    handler: triangle\n  },\n  {\n    semanticName: \"Internal Storage\",\n    name: \"Window Pane\",\n    shortName: \"win-pane\",\n    description: \"Internal storage\",\n    aliases: [\"internal-storage\", \"window-pane\"],\n    handler: windowPane\n  },\n  {\n    semanticName: \"Junction\",\n    name: \"Filled Circle\",\n    shortName: \"f-circ\",\n    description: \"Junction point\",\n    aliases: [\"junction\", \"filled-circle\"],\n    handler: filledCircle\n  },\n  {\n    semanticName: \"Loop Limit\",\n    name: \"Trapezoidal Pentagon\",\n    shortName: \"notch-pent\",\n    description: \"Loop limit step\",\n    aliases: [\"loop-limit\", \"notched-pentagon\"],\n    handler: trapezoidalPentagon\n  },\n  {\n    semanticName: \"Manual File\",\n    name: \"Flipped Triangle\",\n    shortName: \"flip-tri\",\n    description: \"Manual file operation\",\n    aliases: [\"manual-file\", \"flipped-triangle\"],\n    handler: flippedTriangle\n  },\n  {\n    semanticName: \"Manual Input\",\n    name: \"Sloped Rectangle\",\n    shortName: \"sl-rect\",\n    description: \"Manual input step\",\n    aliases: [\"manual-input\", \"sloped-rectangle\"],\n    handler: slopedRect\n  },\n  {\n    semanticName: \"Multi-Document\",\n    name: \"Stacked Document\",\n    shortName: \"docs\",\n    description: \"Multiple documents\",\n    aliases: [\"documents\", \"st-doc\", \"stacked-document\"],\n    handler: multiWaveEdgedRectangle\n  },\n  {\n    semanticName: \"Multi-Process\",\n    name: \"Stacked Rectangle\",\n    shortName: \"st-rect\",\n    description: \"Multiple processes\",\n    aliases: [\"procs\", \"processes\", \"stacked-rectangle\"],\n    handler: multiRect\n  },\n  {\n    semanticName: \"Stored Data\",\n    name: \"Bow Tie Rectangle\",\n    shortName: \"bow-rect\",\n    description: \"Stored data\",\n    aliases: [\"stored-data\", \"bow-tie-rectangle\"],\n    handler: bowTieRect\n  },\n  {\n    semanticName: \"Summary\",\n    name: \"Crossed Circle\",\n    shortName: \"cross-circ\",\n    description: \"Summary\",\n    aliases: [\"summary\", \"crossed-circle\"],\n    handler: crossedCircle\n  },\n  {\n    semanticName: \"Tagged Document\",\n    name: \"Tagged Document\",\n    shortName: \"tag-doc\",\n    description: \"Tagged document\",\n    aliases: [\"tag-doc\", \"tagged-document\"],\n    handler: taggedWaveEdgedRectangle\n  },\n  {\n    semanticName: \"Tagged Process\",\n    name: \"Tagged Rectangle\",\n    shortName: \"tag-rect\",\n    description: \"Tagged process\",\n    aliases: [\"tagged-rectangle\", \"tag-proc\", \"tagged-process\"],\n    handler: taggedRect\n  },\n  {\n    semanticName: \"Paper Tape\",\n    name: \"Flag\",\n    shortName: \"flag\",\n    description: \"Paper tape\",\n    aliases: [\"paper-tape\"],\n    handler: waveRectangle\n  },\n  {\n    semanticName: \"Odd\",\n    name: \"Odd\",\n    shortName: \"odd\",\n    description: \"Odd shape\",\n    internalAliases: [\"rect_left_inv_arrow\"],\n    handler: rect_left_inv_arrow\n  },\n  {\n    semanticName: \"Lined Document\",\n    name: \"Lined Document\",\n    shortName: \"lin-doc\",\n    description: \"Lined document\",\n    aliases: [\"lined-document\"],\n    handler: linedWaveEdgedRect\n  }\n];\nvar generateShapeMap = /* @__PURE__ */ __name(() => {\n  const undocumentedShapes = {\n    // States\n    state,\n    choice,\n    note,\n    // Rectangles\n    rectWithTitle,\n    labelRect,\n    // Icons\n    iconSquare,\n    iconCircle,\n    icon,\n    iconRounded,\n    imageSquare,\n    anchor,\n    // Kanban diagram\n    kanbanItem,\n    // class diagram\n    classBox,\n    // er diagram\n    erBox,\n    // Requirement diagram\n    requirementBox\n  };\n  const entries = [\n    ...Object.entries(undocumentedShapes),\n    ...shapesDefs.flatMap((shape) => {\n      const aliases = [\n        shape.shortName,\n        ...\"aliases\" in shape ? shape.aliases : [],\n        ...\"internalAliases\" in shape ? shape.internalAliases : []\n      ];\n      return aliases.map((alias) => [alias, shape.handler]);\n    })\n  ];\n  return Object.fromEntries(entries);\n}, \"generateShapeMap\");\nvar shapes2 = generateShapeMap();\nfunction isValidShape(shape) {\n  return shape in shapes2;\n}\n__name(isValidShape, \"isValidShape\");\n\n// src/rendering-util/rendering-elements/nodes.ts\nvar nodeElems = /* @__PURE__ */ new Map();\nasync function insertNode(elem, node, renderOptions) {\n  let newEl;\n  let el;\n  if (node.shape === \"rect\") {\n    if (node.rx && node.ry) {\n      node.shape = \"roundedRect\";\n    } else {\n      node.shape = \"squareRect\";\n    }\n  }\n  const shapeHandler = node.shape ? shapes2[node.shape] : void 0;\n  if (!shapeHandler) {\n    throw new Error(`No such shape: ${node.shape}. Please check your syntax.`);\n  }\n  if (node.link) {\n    let target;\n    if (renderOptions.config.securityLevel === \"sandbox\") {\n      target = \"_top\";\n    } else if (node.linkTarget) {\n      target = node.linkTarget || \"_blank\";\n    }\n    newEl = elem.insert(\"svg:a\").attr(\"xlink:href\", node.link).attr(\"target\", target ?? null);\n    el = await shapeHandler(newEl, node, renderOptions);\n  } else {\n    el = await shapeHandler(elem, node, renderOptions);\n    newEl = el;\n  }\n  if (node.tooltip) {\n    el.attr(\"title\", node.tooltip);\n  }\n  nodeElems.set(node.id, newEl);\n  if (node.haveCallback) {\n    newEl.attr(\"class\", newEl.attr(\"class\") + \" clickable\");\n  }\n  return newEl;\n}\n__name(insertNode, \"insertNode\");\nvar setNodeElem = /* @__PURE__ */ __name((elem, node) => {\n  nodeElems.set(node.id, elem);\n}, \"setNodeElem\");\nvar clear2 = /* @__PURE__ */ __name(() => {\n  nodeElems.clear();\n}, \"clear\");\nvar positionNode = /* @__PURE__ */ __name((node) => {\n  const el = nodeElems.get(node.id);\n  log.trace(\n    \"Transforming node\",\n    node.diff,\n    node,\n    \"translate(\" + (node.x - node.width / 2 - 5) + \", \" + node.width / 2 + \")\"\n  );\n  const padding = 8;\n  const diff = node.diff || 0;\n  if (node.clusterNode) {\n    el.attr(\n      \"transform\",\n      \"translate(\" + (node.x + diff - node.width / 2) + \", \" + (node.y - node.height / 2 - padding) + \")\"\n    );\n  } else {\n    el.attr(\"transform\", \"translate(\" + node.x + \", \" + node.y + \")\");\n  }\n  return diff;\n}, \"positionNode\");\n\nexport {\n  labelHelper,\n  updateNodeBounds,\n  isLabelStyle,\n  createLabel_default,\n  isValidShape,\n  insertCluster,\n  clear,\n  insertNode,\n  setNodeElem,\n  clear2,\n  positionNode\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,EAAEA,IAAEC,IAAEC,IAAE;AAAC,MAAGF,MAAGA,GAAE,QAAO;AAAC,UAAK,CAACG,IAAEC,EAAC,IAAEH,IAAEI,KAAE,KAAK,KAAG,MAAIH,IAAEI,KAAE,KAAK,IAAID,EAAC,GAAEE,KAAE,KAAK,IAAIF,EAAC;AAAE,eAAUJ,MAAKD,IAAE;AAAC,YAAK,CAACA,IAAEE,EAAC,IAAED;AAAE,MAAAA,GAAE,CAAC,KAAGD,KAAEG,MAAGG,MAAGJ,KAAEE,MAAGG,KAAEJ,IAAEF,GAAE,CAAC,KAAGD,KAAEG,MAAGI,MAAGL,KAAEE,MAAGE,KAAEF;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEJ,IAAEC,IAAE;AAAC,SAAOD,GAAE,CAAC,MAAIC,GAAE,CAAC,KAAGD,GAAE,CAAC,MAAIC,GAAE,CAAC;AAAC;AAAC,SAAS,EAAEC,IAAEC,IAAEC,IAAEC,KAAE,GAAE;AAAC,QAAMC,KAAEF,IAAEG,KAAE,KAAK,IAAIJ,IAAE,GAAE,GAAEK,KAAEN,GAAE,CAAC,KAAGA,GAAE,CAAC,EAAE,CAAC,KAAG,YAAU,OAAOA,GAAE,CAAC,EAAE,CAAC,IAAE,CAACA,EAAC,IAAEA,IAAEO,KAAE,CAAC,GAAE,CAAC;AAAE,MAAGH,GAAE,YAAUL,MAAKO,GAAE,GAAEP,IAAEQ,IAAEH,EAAC;AAAE,QAAMI,KAAE,SAASV,IAAEE,IAAEC,IAAE;AAAC,UAAMC,KAAE,CAAC;AAAE,eAAUF,MAAKF,IAAE;AAAC,YAAMA,KAAE,CAAC,GAAGE,EAAC;AAAE,QAAEF,GAAE,CAAC,GAAEA,GAAEA,GAAE,SAAO,CAAC,CAAC,KAAGA,GAAE,KAAK,CAACA,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAEA,GAAE,SAAO,KAAGI,GAAE,KAAKJ,EAAC;AAAA,IAAC;AAAC,UAAMK,KAAE,CAAC;AAAE,IAAAH,KAAE,KAAK,IAAIA,IAAE,GAAE;AAAE,UAAMI,KAAE,CAAC;AAAE,eAAUN,MAAKI,GAAE,UAAQH,KAAE,GAAEA,KAAED,GAAE,SAAO,GAAEC,MAAI;AAAC,YAAMC,KAAEF,GAAEC,EAAC,GAAEE,KAAEH,GAAEC,KAAE,CAAC;AAAE,UAAGC,GAAE,CAAC,MAAIC,GAAE,CAAC,GAAE;AAAC,cAAMH,KAAE,KAAK,IAAIE,GAAE,CAAC,GAAEC,GAAE,CAAC,CAAC;AAAE,QAAAG,GAAE,KAAK,EAAC,MAAKN,IAAE,MAAK,KAAK,IAAIE,GAAE,CAAC,GAAEC,GAAE,CAAC,CAAC,GAAE,GAAEH,OAAIE,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEC,GAAE,CAAC,GAAE,SAAQA,GAAE,CAAC,IAAED,GAAE,CAAC,MAAIC,GAAE,CAAC,IAAED,GAAE,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC;AAAC,QAAGI,GAAE,KAAM,CAACN,IAAEC,OAAID,GAAE,OAAKC,GAAE,OAAK,KAAGD,GAAE,OAAKC,GAAE,OAAK,IAAED,GAAE,IAAEC,GAAE,IAAE,KAAGD,GAAE,IAAEC,GAAE,IAAE,IAAED,GAAE,SAAOC,GAAE,OAAK,KAAGD,GAAE,OAAKC,GAAE,QAAM,KAAK,IAAID,GAAE,OAAKC,GAAE,IAAI,CAAE,GAAE,CAACK,GAAE,OAAO,QAAOD;AAAE,QAAIE,KAAE,CAAC,GAAEC,KAAEF,GAAE,CAAC,EAAE,MAAKG,KAAE;AAAE,WAAKF,GAAE,UAAQD,GAAE,UAAQ;AAAC,UAAGA,GAAE,QAAO;AAAC,YAAIN,KAAE;AAAG,iBAAQC,KAAE,GAAEA,KAAEK,GAAE,UAAQ,EAAEA,GAAEL,EAAC,EAAE,OAAKO,KAAGP,KAAI,CAAAD,KAAEC;AAAE,QAAAK,GAAE,OAAO,GAAEN,KAAE,CAAC,EAAE,QAAS,CAAAA,OAAG;AAAC,UAAAO,GAAE,KAAK,EAAC,GAAEC,IAAE,MAAKR,GAAC,CAAC;AAAA,QAAC,CAAE;AAAA,MAAC;AAAC,UAAGO,KAAEA,GAAE,OAAQ,CAAAP,OAAG,EAAEA,GAAE,KAAK,QAAMQ,GAAG,GAAED,GAAE,KAAM,CAACP,IAAEC,OAAID,GAAE,KAAK,MAAIC,GAAE,KAAK,IAAE,KAAGD,GAAE,KAAK,IAAEC,GAAE,KAAK,KAAG,KAAK,IAAID,GAAE,KAAK,IAAEC,GAAE,KAAK,CAAC,CAAE,IAAG,MAAIE,MAAGM,KAAEP,MAAG,MAAIK,GAAE,SAAO,EAAE,UAAQP,KAAE,GAAEA,KAAEO,GAAE,QAAOP,MAAG,GAAE;AAAC,cAAMC,KAAED,KAAE;AAAE,YAAGC,MAAGM,GAAE,OAAO;AAAM,cAAML,KAAEK,GAAEP,EAAC,EAAE,MAAKG,KAAEI,GAAEN,EAAC,EAAE;AAAK,QAAAI,GAAE,KAAK,CAAC,CAAC,KAAK,MAAMH,GAAE,CAAC,GAAEM,EAAC,GAAE,CAAC,KAAK,MAAML,GAAE,CAAC,GAAEK,EAAC,CAAC,CAAC;AAAA,MAAC;AAAC,MAAAA,MAAGL,IAAEI,GAAE,QAAS,CAAAP,OAAG;AAAC,QAAAA,GAAE,KAAK,IAAEA,GAAE,KAAK,IAAEG,KAAEH,GAAE,KAAK;AAAA,MAAM,CAAE,GAAES;AAAA,IAAG;AAAC,WAAOJ;AAAA,EAAC,EAAEG,IAAED,IAAEF,EAAC;AAAE,MAAGC,IAAE;AAAC,eAAUL,MAAKO,GAAE,GAAEP,IAAEQ,IAAE,CAACH,EAAC;AAAE,KAAC,SAASL,IAAEC,IAAEC,IAAE;AAAC,YAAMC,KAAE,CAAC;AAAE,MAAAH,GAAE,QAAS,CAAAD,OAAGI,GAAE,KAAK,GAAGJ,EAAC,CAAE,GAAE,EAAEI,IAAEF,IAAEC,EAAC;AAAA,IAAC,EAAEO,IAAED,IAAE,CAACH,EAAC;AAAA,EAAC;AAAC,SAAOI;AAAC;AAAC,SAAS,EAAEV,IAAEC,IAAE;AAAC,MAAIE;AAAE,QAAMC,KAAEH,GAAE,eAAa;AAAG,MAAII,KAAEJ,GAAE;AAAW,EAAAI,KAAE,MAAIA,KAAE,IAAEJ,GAAE,cAAaI,KAAE,KAAK,MAAM,KAAK,IAAIA,IAAE,GAAE,CAAC;AAAE,MAAIC,KAAE;AAAE,SAAOL,GAAE,aAAW,OAAK,UAAQE,KAAEF,GAAE,eAAa,WAASE,KAAE,SAAOA,GAAE,KAAK,MAAI,KAAK,OAAO,KAAG,QAAKG,KAAED,KAAG,EAAEL,IAAEK,IAAED,IAAEE,MAAG,CAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYN,IAAE;AAAC,SAAK,SAAOA;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAEC,IAAE;AAAC,WAAO,KAAK,cAAcD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAEF,IAAEC,EAAC;AAAE,WAAM,EAAC,MAAK,cAAa,KAAI,KAAK,YAAYC,IAAED,EAAC,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,UAAMC,KAAE,CAAC;AAAE,eAAUC,MAAKH,GAAE,CAAAE,GAAE,KAAK,GAAG,KAAK,OAAO,cAAcC,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEF,EAAC,CAAC;AAAE,WAAOC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEF,IAAE;AAAC,QAAMC,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC;AAAE,SAAO,KAAK,KAAK,KAAK,IAAIC,GAAE,CAAC,IAAEC,GAAE,CAAC,GAAE,CAAC,IAAE,KAAK,IAAID,GAAE,CAAC,IAAEC,GAAE,CAAC,GAAE,CAAC,CAAC;AAAC;AAAC,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,aAAaF,IAAEC,IAAE;AAAC,QAAIC,KAAED,GAAE;AAAW,IAAAC,KAAE,MAAIA,KAAE,IAAED,GAAE,cAAaC,KAAE,KAAK,IAAIA,IAAE,GAAE;AAAE,UAAME,KAAE,EAAEJ,IAAE,OAAO,OAAO,CAAC,GAAEC,IAAE,EAAC,YAAWC,GAAC,CAAC,CAAC,GAAEI,KAAE,KAAK,KAAG,MAAIL,GAAE,cAAaM,KAAE,CAAC,GAAEC,KAAE,MAAGN,KAAE,KAAK,IAAII,EAAC,GAAEG,KAAE,MAAGP,KAAE,KAAK,IAAII,EAAC;AAAE,eAAS,CAACN,IAAEC,EAAC,KAAIG,GAAE,GAAE,CAACJ,IAAEC,EAAC,CAAC,KAAGM,GAAE,KAAK,CAAC,CAACP,GAAE,CAAC,IAAEQ,IAAER,GAAE,CAAC,IAAES,EAAC,GAAE,CAAC,GAAGR,EAAC,CAAC,GAAE,CAAC,CAACD,GAAE,CAAC,IAAEQ,IAAER,GAAE,CAAC,IAAES,EAAC,GAAE,CAAC,GAAGR,EAAC,CAAC,CAAC;AAAE,WAAM,EAAC,MAAK,cAAa,KAAI,KAAK,YAAYM,IAAEN,EAAC,EAAC;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,aAAaD,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,cAAcF,IAAEC,EAAC,GAAEE,KAAE,OAAO,OAAO,CAAC,GAAEF,IAAE,EAAC,cAAaA,GAAE,eAAa,GAAE,CAAC,GAAEG,KAAE,KAAK,cAAcJ,IAAEG,EAAC;AAAE,WAAOD,GAAE,MAAIA,GAAE,IAAI,OAAOE,GAAE,GAAG,GAAEF;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYF,IAAE;AAAC,SAAK,SAAOA;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAEF,IAAEC,KAAE,OAAO,OAAO,CAAC,GAAEA,IAAE,EAAC,cAAa,EAAC,CAAC,CAAC;AAAE,WAAO,KAAK,YAAYC,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,UAAMC,KAAE,CAAC;AAAE,QAAIC,KAAEF,GAAE;AAAW,IAAAE,KAAE,MAAIA,KAAE,IAAEF,GAAE,cAAaE,KAAE,KAAK,IAAIA,IAAE,GAAE;AAAE,QAAIC,KAAEH,GAAE;AAAW,IAAAG,KAAE,MAAIA,KAAEH,GAAE,cAAY;AAAG,UAAMK,KAAEH,KAAE;AAAE,eAAUI,MAAKP,IAAE;AAAC,YAAMA,KAAE,EAAEO,EAAC,GAAEC,KAAER,KAAEG,IAAEM,KAAE,KAAK,KAAKD,EAAC,IAAE,GAAEE,KAAEV,KAAES,KAAEN,IAAEQ,MAAGJ,GAAE,CAAC,EAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,CAAC,KAAG,IAAEJ,KAAE,GAAES,KAAE,KAAK,IAAIL,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,CAAC;AAAE,eAAQP,KAAE,GAAEA,KAAES,IAAET,MAAI;AAAC,cAAMK,KAAEO,KAAEF,KAAEV,KAAEG,IAAEI,KAAEI,KAAEL,KAAE,IAAE,KAAK,OAAO,IAAEA,IAAEE,KAAEH,KAAEC,KAAE,IAAE,KAAK,OAAO,IAAEA,IAAEG,KAAE,KAAK,OAAO,QAAQF,IAAEC,IAAEJ,IAAEA,IAAEH,EAAC;AAAE,QAAAC,GAAE,KAAK,GAAGO,GAAE,GAAG;AAAA,MAAC;AAAA,IAAC;AAAC,WAAM,EAAC,MAAK,cAAa,KAAIP,GAAC;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYF,IAAE;AAAC,SAAK,SAAOA;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAEF,IAAEC,EAAC;AAAE,WAAM,EAAC,MAAK,cAAa,KAAI,KAAK,WAAWC,IAAED,EAAC,EAAC;AAAA,EAAC;AAAA,EAAC,WAAWD,IAAEC,IAAE;AAAC,UAAMC,KAAED,GAAE,aAAW,IAAEA,GAAE,aAAW,IAAE,IAAEA,GAAE,cAAYA,GAAE,aAAWA,GAAE,YAAWE,KAAEF,GAAE,UAAQ,IAAEA,GAAE,aAAW,IAAE,IAAEA,GAAE,cAAYA,GAAE,aAAWA,GAAE,SAAQG,KAAE,CAAC;AAAE,WAAOJ,GAAE,QAAS,CAAAA,OAAG;AAAC,YAAMM,KAAE,EAAEN,EAAC,GAAEO,KAAE,KAAK,MAAMD,MAAGJ,KAAEC,GAAE,GAAEK,MAAGF,KAAEH,KAAEI,MAAGL,KAAEC,OAAI;AAAE,UAAIM,KAAET,GAAE,CAAC,GAAEU,KAAEV,GAAE,CAAC;AAAE,MAAAS,GAAE,CAAC,IAAEC,GAAE,CAAC,MAAID,KAAET,GAAE,CAAC,GAAEU,KAAEV,GAAE,CAAC;AAAG,YAAMW,KAAE,KAAK,MAAMD,GAAE,CAAC,IAAED,GAAE,CAAC,MAAIC,GAAE,CAAC,IAAED,GAAE,CAAC,EAAE;AAAE,eAAQT,KAAE,GAAEA,KAAEO,IAAEP,MAAI;AAAC,cAAMK,KAAEL,MAAGE,KAAEC,KAAGG,KAAED,KAAEH,IAAEK,KAAE,CAACE,GAAE,CAAC,IAAEJ,KAAE,KAAK,IAAIM,EAAC,IAAEH,KAAE,KAAK,IAAIG,EAAC,GAAEF,GAAE,CAAC,IAAEJ,KAAE,KAAK,IAAIM,EAAC,IAAEH,KAAE,KAAK,IAAIG,EAAC,CAAC,GAAED,KAAE,CAACD,GAAE,CAAC,IAAEH,KAAE,KAAK,IAAIK,EAAC,IAAEH,KAAE,KAAK,IAAIG,EAAC,GAAEF,GAAE,CAAC,IAAEH,KAAE,KAAK,IAAIK,EAAC,IAAEH,KAAE,KAAK,IAAIG,EAAC,CAAC;AAAE,QAAAP,GAAE,KAAK,GAAG,KAAK,OAAO,cAAcG,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEG,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAET,EAAC,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE,GAAEG;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYJ,IAAE;AAAC,SAAK,SAAOA;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAEC,IAAE;AAAC,UAAMC,KAAED,GAAE,aAAW,IAAE,IAAEA,GAAE,cAAYA,GAAE,YAAWG,KAAEH,GAAE,eAAa,IAAEC,KAAED,GAAE,cAAaI,KAAE,EAAEL,IAAEC,KAAE,OAAO,OAAO,CAAC,GAAEA,IAAE,EAAC,YAAWC,KAAEE,GAAC,CAAC,CAAC;AAAE,WAAM,EAAC,MAAK,cAAa,KAAI,KAAK,YAAYC,IAAED,IAAEH,EAAC,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,CAAC;AAAE,WAAOH,GAAE,QAAS,CAAAA,OAAG;AAAC,YAAMI,KAAE,EAAEJ,EAAC,GAAEM,KAAE,KAAK,MAAMF,MAAG,IAAEH,GAAE;AAAE,UAAIM,KAAEP,GAAE,CAAC,GAAEQ,KAAER,GAAE,CAAC;AAAE,MAAAO,GAAE,CAAC,IAAEC,GAAE,CAAC,MAAID,KAAEP,GAAE,CAAC,GAAEQ,KAAER,GAAE,CAAC;AAAG,YAAMS,KAAE,KAAK,MAAMD,GAAE,CAAC,IAAED,GAAE,CAAC,MAAIC,GAAE,CAAC,IAAED,GAAE,CAAC,EAAE;AAAE,eAAQP,KAAE,GAAEA,KAAEM,IAAEN,MAAI;AAAC,cAAMI,KAAE,IAAEJ,KAAEC,IAAEI,KAAE,KAAGL,KAAE,KAAGC,IAAEK,KAAE,KAAK,KAAK,IAAE,KAAK,IAAIL,IAAE,CAAC,CAAC,GAAEO,KAAE,CAACD,GAAE,CAAC,IAAEH,KAAE,KAAK,IAAIK,EAAC,GAAEF,GAAE,CAAC,IAAEH,KAAE,KAAK,IAAIK,EAAC,CAAC,GAAEC,KAAE,CAACH,GAAE,CAAC,IAAEF,KAAE,KAAK,IAAII,EAAC,GAAEF,GAAE,CAAC,IAAEF,KAAE,KAAK,IAAII,EAAC,CAAC,GAAEE,KAAE,CAACH,GAAE,CAAC,IAAEF,KAAE,KAAK,IAAIG,KAAE,KAAK,KAAG,CAAC,GAAED,GAAE,CAAC,IAAEF,KAAE,KAAK,IAAIG,KAAE,KAAK,KAAG,CAAC,CAAC;AAAE,QAAAN,GAAE,KAAK,GAAG,KAAK,OAAO,cAAcK,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEG,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAET,EAAC,GAAE,GAAG,KAAK,OAAO,cAAcS,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAED,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAER,EAAC,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE,GAAEC;AAAA,EAAC;AAAC;AAAC,IAAM,IAAE,CAAC;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYH,IAAE;AAAC,SAAK,OAAKA;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,KAAK,QAAM,KAAG,KAAG,KAAG,KAAK,OAAK,KAAK,KAAK,OAAM,KAAK,IAAI,MAAI,KAAG,KAAG,KAAK,OAAO;AAAA,EAAC;AAAC;AAAC,IAAM,IAAE;AAAR,IAAU,IAAE;AAAZ,IAAc,IAAE;AAAhB,IAAkB,IAAE,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC;AAAE,SAAS,EAAEA,IAAEC,IAAE;AAAC,SAAOD,GAAE,SAAOC;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,QAAMC,KAAE,CAAC,GAAEC,KAAE,SAASF,IAAE;AAAC,UAAMC,KAAE,IAAI;AAAM,WAAK,OAAKD,KAAG,KAAGA,GAAE,MAAM,gBAAgB,EAAE,CAAAA,KAAEA,GAAE,OAAO,OAAO,GAAG,MAAM;AAAA,aAAUA,GAAE,MAAM,2BAA2B,EAAE,CAAAC,GAAEA,GAAE,MAAM,IAAE,EAAC,MAAK,GAAE,MAAK,OAAO,GAAE,GAAED,KAAEA,GAAE,OAAO,OAAO,GAAG,MAAM;AAAA,SAAM;AAAC,UAAG,CAACA,GAAE,MAAM,6DAA6D,EAAE,QAAM,CAAC;AAAE,MAAAC,GAAEA,GAAE,MAAM,IAAE,EAAC,MAAK,GAAE,MAAK,GAAG,WAAW,OAAO,EAAE,CAAC,GAAE,GAAED,KAAEA,GAAE,OAAO,OAAO,GAAG,MAAM;AAAA,IAAC;AAAC,WAAOC,GAAEA,GAAE,MAAM,IAAE,EAAC,MAAK,GAAE,MAAK,GAAE,GAAEA;AAAA,EAAC,EAAED,EAAC;AAAE,MAAIG,KAAE,OAAMC,KAAE,GAAEC,KAAEH,GAAEE,EAAC;AAAE,SAAK,CAAC,EAAEC,IAAE,CAAC,KAAG;AAAC,QAAIC,KAAE;AAAE,UAAMC,KAAE,CAAC;AAAE,QAAG,UAAQJ,IAAE;AAAC,UAAG,QAAME,GAAE,QAAM,QAAMA,GAAE,KAAK,QAAO,EAAE,SAAOL,EAAC;AAAE,MAAAI,MAAIE,KAAE,EAAED,GAAE,IAAI,GAAEF,KAAEE,GAAE;AAAA,IAAI,MAAM,GAAEA,IAAE,CAAC,IAAEC,KAAE,EAAEH,EAAC,KAAGC,MAAIE,KAAE,EAAED,GAAE,IAAI,GAAEF,KAAEE,GAAE;AAAM,QAAG,EAAED,KAAEE,KAAEJ,GAAE,QAAQ,OAAM,IAAI,MAAM,uBAAuB;AAAE,aAAQF,KAAEI,IAAEJ,KAAEI,KAAEE,IAAEN,MAAI;AAAC,YAAMC,KAAEC,GAAEF,EAAC;AAAE,UAAG,CAAC,EAAEC,IAAE,CAAC,EAAE,OAAM,IAAI,MAAM,yBAAuBE,KAAE,MAAIF,GAAE,IAAI;AAAE,MAAAM,GAAEA,GAAE,MAAM,IAAE,CAACN,GAAE;AAAA,IAAI;AAAC,QAAG,YAAU,OAAO,EAAEE,EAAC,EAAE,OAAM,IAAI,MAAM,kBAAgBA,EAAC;AAAE;AAAC,YAAMH,KAAE,EAAC,KAAIG,IAAE,MAAKI,GAAC;AAAE,MAAAN,GAAE,KAAKD,EAAC,GAAEI,MAAGE,IAAED,KAAEH,GAAEE,EAAC,GAAE,QAAMD,OAAIA,KAAE,MAAK,QAAMA,OAAIA,KAAE;AAAA,IAAI;AAAA,EAAC;AAAC,SAAOF;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,MAAIC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,QAAMC,KAAE,CAAC;AAAE,aAAS,EAAC,KAAIC,IAAE,MAAKC,GAAC,KAAIP,GAAE,SAAOM,IAAE;AAAA,IAAC,KAAI;AAAI,MAAAD,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAAC,GAAGE,EAAC,EAAC,CAAC,GAAE,CAACN,IAAEC,EAAC,IAAEK,IAAE,CAACJ,IAAEC,EAAC,IAAEG;AAAE;AAAA,IAAM,KAAI;AAAI,MAAAN,MAAGM,GAAE,CAAC,GAAEL,MAAGK,GAAE,CAAC,GAAEF,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAACJ,IAAEC,EAAC,EAAC,CAAC,GAAEC,KAAEF,IAAEG,KAAEF;AAAE;AAAA,IAAM,KAAI;AAAI,MAAAG,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAAC,GAAGE,EAAC,EAAC,CAAC,GAAE,CAACN,IAAEC,EAAC,IAAEK;AAAE;AAAA,IAAM,KAAI;AAAI,MAAAN,MAAGM,GAAE,CAAC,GAAEL,MAAGK,GAAE,CAAC,GAAEF,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAACJ,IAAEC,EAAC,EAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAI,MAAAG,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAAC,GAAGE,EAAC,EAAC,CAAC,GAAEN,KAAEM,GAAE,CAAC,GAAEL,KAAEK,GAAE,CAAC;AAAE;AAAA,IAAM,KAAI,KAAI;AAAC,YAAMP,KAAEO,GAAE,IAAK,CAACP,IAAEG,OAAIA,KAAE,IAAEH,KAAEE,KAAEF,KAAEC,EAAE;AAAE,MAAAI,GAAE,KAAK,EAAC,KAAI,KAAI,MAAKL,GAAC,CAAC,GAAEC,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC;AAAE;AAAA,IAAK;AAAA,IAAC,KAAI;AAAI,MAAAK,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAAC,GAAGE,EAAC,EAAC,CAAC,GAAEN,KAAEM,GAAE,CAAC,GAAEL,KAAEK,GAAE,CAAC;AAAE;AAAA,IAAM,KAAI,KAAI;AAAC,YAAMP,KAAEO,GAAE,IAAK,CAACP,IAAEG,OAAIA,KAAE,IAAEH,KAAEE,KAAEF,KAAEC,EAAE;AAAE,MAAAI,GAAE,KAAK,EAAC,KAAI,KAAI,MAAKL,GAAC,CAAC,GAAEC,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC;AAAE;AAAA,IAAK;AAAA,IAAC,KAAI;AAAI,MAAAK,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAAC,GAAGE,EAAC,EAAC,CAAC,GAAEN,KAAEM,GAAE,CAAC,GAAEL,KAAEK,GAAE,CAAC;AAAE;AAAA,IAAM,KAAI;AAAI,MAAAN,MAAGM,GAAE,CAAC,GAAEL,MAAGK,GAAE,CAAC,GAAEF,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAACE,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEN,IAAEC,EAAC,EAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAI,MAAAG,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAAC,GAAGE,EAAC,EAAC,CAAC,GAAEN,KAAEM,GAAE,CAAC;AAAE;AAAA,IAAM,KAAI;AAAI,MAAAN,MAAGM,GAAE,CAAC,GAAEF,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAACJ,EAAC,EAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAI,MAAAI,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAAC,GAAGE,EAAC,EAAC,CAAC,GAAEL,KAAEK,GAAE,CAAC;AAAE;AAAA,IAAM,KAAI;AAAI,MAAAL,MAAGK,GAAE,CAAC,GAAEF,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAACH,EAAC,EAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAI,MAAAG,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAAC,GAAGE,EAAC,EAAC,CAAC,GAAEN,KAAEM,GAAE,CAAC,GAAEL,KAAEK,GAAE,CAAC;AAAE;AAAA,IAAM,KAAI,KAAI;AAAC,YAAMP,KAAEO,GAAE,IAAK,CAACP,IAAEG,OAAIA,KAAE,IAAEH,KAAEE,KAAEF,KAAEC,EAAE;AAAE,MAAAI,GAAE,KAAK,EAAC,KAAI,KAAI,MAAKL,GAAC,CAAC,GAAEC,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC;AAAE;AAAA,IAAK;AAAA,IAAC,KAAI;AAAI,MAAAK,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAAC,GAAGE,EAAC,EAAC,CAAC,GAAEN,KAAEM,GAAE,CAAC,GAAEL,KAAEK,GAAE,CAAC;AAAE;AAAA,IAAM,KAAI;AAAI,MAAAN,MAAGM,GAAE,CAAC,GAAEL,MAAGK,GAAE,CAAC,GAAEF,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAACJ,IAAEC,EAAC,EAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAA,IAAI,KAAI;AAAI,MAAAG,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAAC,EAAC,CAAC,GAAEJ,KAAEE,IAAED,KAAEE;AAAA,EAAC;AAAC,SAAOC;AAAC;AAAC,SAAS,EAAEL,IAAE;AAAC,QAAMC,KAAE,CAAC;AAAE,MAAIC,KAAE,IAAGC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,aAAS,EAAC,KAAIC,IAAE,MAAKC,GAAC,KAAIV,IAAE;AAAC,YAAOS,IAAE;AAAA,MAAC,KAAI;AAAI,QAAAR,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAAC,GAAGS,EAAC,EAAC,CAAC,GAAE,CAACP,IAAEC,EAAC,IAAEM,IAAE,CAACL,IAAEC,EAAC,IAAEI;AAAE;AAAA,MAAM,KAAI;AAAI,QAAAT,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAAC,GAAGS,EAAC,EAAC,CAAC,GAAEP,KAAEO,GAAE,CAAC,GAAEN,KAAEM,GAAE,CAAC,GAAEH,KAAEG,GAAE,CAAC,GAAEF,KAAEE,GAAE,CAAC;AAAE;AAAA,MAAM,KAAI;AAAI,QAAAT,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAAC,GAAGS,EAAC,EAAC,CAAC,GAAE,CAACP,IAAEC,EAAC,IAAEM;AAAE;AAAA,MAAM,KAAI;AAAI,QAAAP,KAAEO,GAAE,CAAC,GAAET,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAACE,IAAEC,EAAC,EAAC,CAAC;AAAE;AAAA,MAAM,KAAI;AAAI,QAAAA,KAAEM,GAAE,CAAC,GAAET,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAACE,IAAEC,EAAC,EAAC,CAAC;AAAE;AAAA,MAAM,KAAI,KAAI;AAAC,YAAIJ,KAAE,GAAEK,KAAE;AAAE,gBAAMH,MAAG,QAAMA,MAAGF,KAAEG,MAAGA,KAAEI,KAAGF,KAAED,MAAGA,KAAEI,QAAKR,KAAEG,IAAEE,KAAED,KAAGH,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAACD,IAAEK,IAAE,GAAGK,EAAC,EAAC,CAAC,GAAEH,KAAEG,GAAE,CAAC,GAAEF,KAAEE,GAAE,CAAC,GAAEP,KAAEO,GAAE,CAAC,GAAEN,KAAEM,GAAE,CAAC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAI,KAAI;AAAC,cAAK,CAACV,IAAEK,EAAC,IAAEK;AAAE,YAAIJ,KAAE,GAAEG,KAAE;AAAE,gBAAMP,MAAG,QAAMA,MAAGI,KAAEH,MAAGA,KAAEI,KAAGE,KAAEL,MAAGA,KAAEI,QAAKF,KAAEH,IAAEM,KAAEL;AAAG,cAAMO,KAAER,KAAE,KAAGG,KAAEH,MAAG,GAAES,KAAER,KAAE,KAAGK,KAAEL,MAAG,GAAES,KAAEb,KAAE,KAAGM,KAAEN,MAAG,GAAEc,KAAET,KAAE,KAAGI,KAAEJ,MAAG;AAAE,QAAAJ,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAACU,IAAEC,IAAEC,IAAEC,IAAEd,IAAEK,EAAC,EAAC,CAAC,GAAEE,KAAED,IAAEE,KAAEC,IAAEN,KAAEH,IAAEI,KAAEC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAI,KAAI;AAAC,cAAK,CAACL,IAAEE,IAAEG,IAAEC,EAAC,IAAEI,IAAED,KAAEN,KAAE,KAAGH,KAAEG,MAAG,GAAEQ,KAAEP,KAAE,KAAGF,KAAEE,MAAG,GAAEQ,KAAEP,KAAE,KAAGL,KAAEK,MAAG,GAAEQ,KAAEP,KAAE,KAAGJ,KAAEI,MAAG;AAAE,QAAAL,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAACQ,IAAEE,IAAEC,IAAEC,IAAER,IAAEC,EAAC,EAAC,CAAC,GAAEC,KAAEP,IAAEQ,KAAEN,IAAEC,KAAEE,IAAED,KAAEE;AAAE;AAAA,MAAK;AAAA,MAAC,KAAI,KAAI;AAAC,cAAMN,KAAE,KAAK,IAAIU,GAAE,CAAC,CAAC,GAAER,KAAE,KAAK,IAAIQ,GAAE,CAAC,CAAC,GAAEL,KAAEK,GAAE,CAAC,GAAEJ,KAAEI,GAAE,CAAC,GAAEH,KAAEG,GAAE,CAAC,GAAEF,KAAEE,GAAE,CAAC,GAAED,KAAEC,GAAE,CAAC;AAAE,YAAG,MAAIV,MAAG,MAAIE,GAAE,CAAAD,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAACE,IAAEC,IAAEI,IAAEC,IAAED,IAAEC,EAAC,EAAC,CAAC,GAAEN,KAAEK,IAAEJ,KAAEK;AAAA,iBAAUN,OAAIK,MAAGJ,OAAIK,IAAE;AAAC,YAAEN,IAAEC,IAAEI,IAAEC,IAAET,IAAEE,IAAEG,IAAEC,IAAEC,EAAC,EAAE,QAAS,SAASP,IAAE;AAAC,YAAAC,GAAE,KAAK,EAAC,KAAI,KAAI,MAAKD,GAAC,CAAC;AAAA,UAAC,CAAE,GAAEG,KAAEK,IAAEJ,KAAEK;AAAA,QAAC;AAAC;AAAA,MAAK;AAAA,MAAC,KAAI;AAAI,QAAAR,GAAE,KAAK,EAAC,KAAI,KAAI,MAAK,CAAC,EAAC,CAAC,GAAEE,KAAEE,IAAED,KAAEE;AAAA,IAAC;AAAC,IAAAJ,KAAEO;AAAA,EAAC;AAAC,SAAOR;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEC,IAAE;AAAC,SAAM,CAACF,KAAE,KAAK,IAAIE,EAAC,IAAED,KAAE,KAAK,IAAIC,EAAC,GAAEF,KAAE,KAAK,IAAIE,EAAC,IAAED,KAAE,KAAK,IAAIC,EAAC,CAAC;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,MAAGC,KAAEL,IAAE,KAAK,KAAGK,KAAE;AAAK,MAAIA;AAAE,MAAIC,KAAE,CAAC,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,MAAGP,GAAE,EAACI,IAAEC,IAAEC,IAAEC,EAAC,IAAEP;AAAA,OAAM;AAAC,KAACT,IAAEC,EAAC,IAAE,EAAED,IAAEC,IAAE,CAACS,EAAC,GAAE,CAACR,IAAEC,EAAC,IAAE,EAAED,IAAEC,IAAE,CAACO,EAAC;AAAE,UAAMJ,MAAGN,KAAEE,MAAG,GAAEO,MAAGR,KAAEE,MAAG;AAAE,QAAIQ,KAAEL,KAAEA,MAAGF,KAAEA,MAAGK,KAAEA,MAAGJ,KAAEA;AAAG,IAAAM,KAAE,MAAIA,KAAE,KAAK,KAAKA,EAAC,GAAEP,MAAGO,IAAEN,MAAGM;AAAG,UAAMC,KAAER,KAAEA,IAAEa,KAAEZ,KAAEA,IAAEa,KAAEN,KAAEK,KAAEL,KAAEH,KAAEA,KAAEQ,KAAEX,KAAEA,IAAEa,KAAEP,KAAEH,KAAEA,KAAEQ,KAAEX,KAAEA,IAAEc,MAAGb,OAAIC,KAAE,KAAG,KAAG,KAAK,KAAK,KAAK,IAAIU,KAAEC,EAAC,CAAC;AAAE,IAAAJ,KAAEK,KAAEhB,KAAEK,KAAEJ,MAAGL,KAAEE,MAAG,GAAEc,KAAEI,KAAE,CAACf,KAAEC,KAAEF,MAAGH,KAAEE,MAAG,GAAEU,KAAE,KAAK,KAAK,aAAaZ,KAAEe,MAAGX,IAAG,QAAQ,CAAC,CAAC,CAAC,GAAES,KAAE,KAAK,KAAK,aAAaX,KAAEa,MAAGX,IAAG,QAAQ,CAAC,CAAC,CAAC,GAAEL,KAAEe,OAAIF,KAAE,KAAK,KAAGA,KAAGX,KAAEa,OAAID,KAAE,KAAK,KAAGA,KAAGD,KAAE,MAAIA,KAAE,IAAE,KAAK,KAAGA,KAAGC,KAAE,MAAIA,KAAE,IAAE,KAAK,KAAGA,KAAGN,MAAGK,KAAEC,OAAID,MAAG,IAAE,KAAK,KAAI,CAACL,MAAGM,KAAED,OAAIC,MAAG,IAAE,KAAK;AAAA,EAAG;AAAC,MAAIG,KAAEH,KAAED;AAAE,MAAG,KAAK,IAAII,EAAC,IAAE,MAAI,KAAK,KAAG,KAAI;AAAC,UAAMjB,KAAEc,IAAEb,KAAEC,IAAEK,KAAEJ;AAAE,IAAAW,KAAEN,MAAGM,KAAED,KAAEA,KAAE,MAAI,KAAK,KAAG,MAAI,IAAEA,KAAE,MAAI,KAAK,KAAG,MAAI,IAAGD,KAAE,EAAEV,KAAEa,KAAEX,KAAE,KAAK,IAAIU,EAAC,GAAEX,KAAEa,KAAEX,KAAE,KAAK,IAAIS,EAAC,GAAEb,IAAEM,IAAEH,IAAEC,IAAEC,IAAE,GAAEE,IAAE,CAACM,IAAEd,IAAEe,IAAEC,EAAC,CAAC;AAAA,EAAC;AAAC,EAAAC,KAAEH,KAAED;AAAE,QAAMK,KAAE,KAAK,IAAIL,EAAC,GAAEM,KAAE,KAAK,IAAIN,EAAC,GAAEO,KAAE,KAAK,IAAIN,EAAC,GAAEO,KAAE,KAAK,IAAIP,EAAC,GAAEQ,KAAE,KAAK,IAAIL,KAAE,CAAC,GAAEM,KAAE,IAAE,IAAEnB,KAAEkB,IAAEE,KAAE,IAAE,IAAEnB,KAAEiB,IAAEG,KAAE,CAACzB,IAAEC,EAAC,GAAEyB,KAAE,CAAC1B,KAAEuB,KAAEJ,IAAElB,KAAEuB,KAAEN,EAAC,GAAES,KAAE,CAACzB,KAAEqB,KAAEF,IAAElB,KAAEqB,KAAEJ,EAAC,GAAEQ,KAAE,CAAC1B,IAAEC,EAAC;AAAE,MAAGuB,GAAE,CAAC,IAAE,IAAED,GAAE,CAAC,IAAEC,GAAE,CAAC,GAAEA,GAAE,CAAC,IAAE,IAAED,GAAE,CAAC,IAAEC,GAAE,CAAC,GAAEjB,GAAE,QAAM,CAACiB,IAAEC,IAAEC,EAAC,EAAE,OAAOhB,EAAC;AAAE;AAAC,IAAAA,KAAE,CAACc,IAAEC,IAAEC,EAAC,EAAE,OAAOhB,EAAC;AAAE,UAAMZ,KAAE,CAAC;AAAE,aAAQC,KAAE,GAAEA,KAAEW,GAAE,QAAOX,MAAG,GAAE;AAAC,YAAMC,KAAE,EAAEU,GAAEX,EAAC,EAAE,CAAC,GAAEW,GAAEX,EAAC,EAAE,CAAC,GAAES,EAAC,GAAEP,KAAE,EAAES,GAAEX,KAAE,CAAC,EAAE,CAAC,GAAEW,GAAEX,KAAE,CAAC,EAAE,CAAC,GAAES,EAAC,GAAEN,KAAE,EAAEQ,GAAEX,KAAE,CAAC,EAAE,CAAC,GAAEW,GAAEX,KAAE,CAAC,EAAE,CAAC,GAAES,EAAC;AAAE,MAAAV,GAAE,KAAK,CAACE,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEC,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEC,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,CAAC;AAAA,IAAC;AAAC,WAAOJ;AAAA,EAAC;AAAC;AAAC,IAAM,IAAE,EAAC,YAAW,SAASA,IAAEC,IAAE;AAAC,SAAO,EAAED,IAAEC,EAAC;AAAC,GAAE,qBAAoB,SAASD,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAEF,IAAEC,IAAEC,EAAC;AAAC,GAAE,SAAQ,SAASF,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAEH,IAAEC,IAAEC,EAAC;AAAE,SAAO,EAAEJ,IAAEC,IAAEG,IAAEC,EAAC,EAAE;AAAK,GAAE,eAAc,SAASL,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAEJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAE,IAAE;AAAC,EAAC;AAAE,SAAS,EAAEJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAM,EAAC,MAAK,QAAO,KAAI,EAAEJ,IAAEC,IAAEC,IAAEC,IAAEC,EAAC,EAAC;AAAC;AAAC,SAAS,EAAEJ,IAAEC,IAAEC,IAAE;AAAC,QAAMC,MAAGH,MAAG,CAAC,GAAG;AAAO,MAAGG,KAAE,GAAE;AAAC,UAAMC,KAAE,CAAC;AAAE,aAAQH,KAAE,GAAEA,KAAEE,KAAE,GAAEF,KAAI,CAAAG,GAAE,KAAK,GAAG,EAAEJ,GAAEC,EAAC,EAAE,CAAC,GAAED,GAAEC,EAAC,EAAE,CAAC,GAAED,GAAEC,KAAE,CAAC,EAAE,CAAC,GAAED,GAAEC,KAAE,CAAC,EAAE,CAAC,GAAEC,EAAC,CAAC;AAAE,WAAOD,MAAGG,GAAE,KAAK,GAAG,EAAEJ,GAAEG,KAAE,CAAC,EAAE,CAAC,GAAEH,GAAEG,KAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEE,EAAC,CAAC,GAAE,EAAC,MAAK,QAAO,KAAIE,GAAC;AAAA,EAAC;AAAC,SAAO,MAAID,KAAE,EAAEH,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEE,EAAC,IAAE,EAAC,MAAK,QAAO,KAAI,CAAC,EAAC;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,SAASJ,IAAEC,IAAE;AAAC,WAAO,EAAED,IAAE,MAAGC,EAAC;AAAA,EAAC,EAAE,CAAC,CAACD,IAAEC,EAAC,GAAE,CAACD,KAAEE,IAAED,EAAC,GAAE,CAACD,KAAEE,IAAED,KAAEE,EAAC,GAAE,CAACH,IAAEC,KAAEE,EAAC,CAAC,GAAEC,EAAC;AAAC;AAAC,SAAS,EAAEJ,IAAEC,IAAE;AAAC,MAAGD,GAAE,QAAO;AAAC,UAAME,KAAE,YAAU,OAAOF,GAAE,CAAC,EAAE,CAAC,IAAE,CAACA,EAAC,IAAEA,IAAEG,KAAE,EAAED,GAAE,CAAC,GAAE,KAAG,IAAE,MAAGD,GAAE,YAAWA,EAAC,GAAEG,KAAEH,GAAE,qBAAmB,CAAC,IAAE,EAAEC,GAAE,CAAC,GAAE,OAAK,IAAE,OAAID,GAAE,YAAW,EAAEA,EAAC,CAAC;AAAE,aAAQD,KAAE,GAAEA,KAAEE,GAAE,QAAOF,MAAI;AAAC,YAAMK,KAAEH,GAAEF,EAAC;AAAE,UAAGK,GAAE,QAAO;AAAC,cAAML,KAAE,EAAEK,IAAE,KAAG,IAAE,MAAGJ,GAAE,YAAWA,EAAC,GAAEC,KAAED,GAAE,qBAAmB,CAAC,IAAE,EAAEI,IAAE,OAAK,IAAE,OAAIJ,GAAE,YAAW,EAAEA,EAAC,CAAC;AAAE,mBAAUA,MAAKD,GAAE,YAASC,GAAE,MAAIE,GAAE,KAAKF,EAAC;AAAE,mBAAUD,MAAKE,GAAE,YAASF,GAAE,MAAII,GAAE,KAAKJ,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAM,EAAC,MAAK,QAAO,KAAIG,GAAE,OAAOC,EAAC,EAAC;AAAA,EAAC;AAAC,SAAM,EAAC,MAAK,QAAO,KAAI,CAAC,EAAC;AAAC;AAAC,SAAS,EAAEJ,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,KAAK,KAAK,IAAE,KAAK,KAAG,KAAK,MAAM,KAAK,IAAIH,KAAE,GAAE,CAAC,IAAE,KAAK,IAAIC,KAAE,GAAE,CAAC,KAAG,CAAC,CAAC,GAAEG,KAAE,KAAK,KAAK,KAAK,IAAIF,GAAE,gBAAeA,GAAE,iBAAe,KAAK,KAAK,GAAG,IAAEC,EAAC,CAAC,GAAEE,KAAE,IAAE,KAAK,KAAGD;AAAE,MAAIE,KAAE,KAAK,IAAIN,KAAE,CAAC,GAAEO,KAAE,KAAK,IAAIN,KAAE,CAAC;AAAE,QAAMO,KAAE,IAAEN,GAAE;AAAa,SAAOI,MAAG,EAAEA,KAAEE,IAAEN,EAAC,GAAEK,MAAG,EAAEA,KAAEC,IAAEN,EAAC,GAAE,EAAC,WAAUG,IAAE,IAAGC,IAAE,IAAGC,GAAC;AAAC;AAAC,SAAS,EAAEP,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAK,CAACC,IAAEC,EAAC,IAAE,EAAEF,GAAE,WAAUH,IAAEC,IAAEE,GAAE,IAAGA,GAAE,IAAG,GAAEA,GAAE,YAAU,EAAE,KAAG,EAAE,KAAG,GAAED,EAAC,GAAEA,EAAC,GAAEA,EAAC;AAAE,MAAII,KAAE,EAAEF,IAAE,MAAKF,EAAC;AAAE,MAAG,CAACA,GAAE,sBAAoB,MAAIA,GAAE,WAAU;AAAC,UAAK,CAACE,EAAC,IAAE,EAAED,GAAE,WAAUH,IAAEC,IAAEE,GAAE,IAAGA,GAAE,IAAG,KAAI,GAAED,EAAC,GAAEG,KAAE,EAAED,IAAE,MAAKF,EAAC;AAAE,IAAAI,KAAEA,GAAE,OAAOD,EAAC;AAAA,EAAC;AAAC,SAAM,EAAC,iBAAgBA,IAAE,OAAM,EAAC,MAAK,QAAO,KAAIC,GAAC,EAAC;AAAC;AAAC,SAAS,EAAEN,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAET,IAAEU,KAAET;AAAE,MAAIU,KAAE,KAAK,IAAIT,KAAE,CAAC,GAAEU,KAAE,KAAK,IAAIT,KAAE,CAAC;AAAE,EAAAQ,MAAG,EAAE,OAAIA,IAAEH,EAAC,GAAEI,MAAG,EAAE,OAAIA,IAAEJ,EAAC;AAAE,MAAIK,KAAET,IAAEU,KAAET;AAAE,SAAKQ,KAAE,IAAG,CAAAA,MAAG,IAAE,KAAK,IAAGC,MAAG,IAAE,KAAK;AAAG,EAAAA,KAAED,KAAE,IAAE,KAAK,OAAKA,KAAE,GAAEC,KAAE,IAAE,KAAK;AAAI,QAAMC,KAAE,IAAE,KAAK,KAAGP,GAAE,gBAAeQ,KAAE,KAAK,IAAID,KAAE,IAAGD,KAAED,MAAG,CAAC,GAAEI,KAAE,EAAED,IAAEP,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE,GAAEN,EAAC;AAAE,MAAG,CAACA,GAAE,oBAAmB;AAAC,UAAMR,KAAE,EAAEgB,IAAEP,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE,KAAIN,EAAC;AAAE,IAAAS,GAAE,KAAK,GAAGjB,EAAC;AAAA,EAAC;AAAC,SAAOM,OAAIC,KAAEU,GAAE,KAAK,GAAG,EAAER,IAAEC,IAAED,KAAEE,KAAE,KAAK,IAAIE,EAAC,GAAEH,KAAEE,KAAE,KAAK,IAAIC,EAAC,GAAEL,EAAC,GAAE,GAAG,EAAEC,IAAEC,IAAED,KAAEE,KAAE,KAAK,IAAIG,EAAC,GAAEJ,KAAEE,KAAE,KAAK,IAAIE,EAAC,GAAEN,EAAC,CAAC,IAAES,GAAE,KAAK,EAAC,IAAG,UAAS,MAAK,CAACR,IAAEC,EAAC,EAAC,GAAE,EAAC,IAAG,UAAS,MAAK,CAACD,KAAEE,KAAE,KAAK,IAAIE,EAAC,GAAEH,KAAEE,KAAE,KAAK,IAAIC,EAAC,CAAC,EAAC,CAAC,IAAG,EAAC,MAAK,QAAO,KAAII,GAAC;AAAC;AAAC,SAAS,EAAEjB,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAE,EAAE,EAAEF,EAAC,CAAC,CAAC,GAAEG,KAAE,CAAC;AAAE,MAAIC,KAAE,CAAC,GAAE,CAAC,GAAEC,KAAE,CAAC,GAAE,CAAC;AAAE,aAAS,EAAC,KAAIL,IAAE,MAAKM,GAAC,KAAIJ,GAAE,SAAOF,IAAE;AAAA,IAAC,KAAI;AAAI,MAAAK,KAAE,CAACC,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAEF,KAAE,CAACE,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAI,MAAAH,GAAE,KAAK,GAAG,EAAEE,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEC,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEL,EAAC,CAAC,GAAEI,KAAE,CAACC,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAE;AAAA,IAAM,KAAI,KAAI;AAAC,YAAK,CAACN,IAAEE,IAAEE,IAAEG,IAAEC,IAAEC,EAAC,IAAEH;AAAE,MAAAH,GAAE,KAAK,GAAG,EAAEH,IAAEE,IAAEE,IAAEG,IAAEC,IAAEC,IAAEJ,IAAEJ,EAAC,CAAC,GAAEI,KAAE,CAACG,IAAEC,EAAC;AAAE;AAAA,IAAK;AAAA,IAAC,KAAI;AAAI,MAAAN,GAAE,KAAK,GAAG,EAAEE,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAED,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEH,EAAC,CAAC,GAAEI,KAAE,CAACD,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,EAAC;AAAC,SAAM,EAAC,MAAK,QAAO,KAAID,GAAC;AAAC;AAAC,SAAS,EAAEH,IAAEC,IAAE;AAAC,QAAMC,KAAE,CAAC;AAAE,aAAUC,MAAKH,GAAE,KAAGG,GAAE,QAAO;AAAC,UAAMH,KAAEC,GAAE,uBAAqB,GAAEG,KAAED,GAAE;AAAO,QAAGC,KAAE,GAAE;AAAC,MAAAF,GAAE,KAAK,EAAC,IAAG,QAAO,MAAK,CAACC,GAAE,CAAC,EAAE,CAAC,IAAE,EAAEH,IAAEC,EAAC,GAAEE,GAAE,CAAC,EAAE,CAAC,IAAE,EAAEH,IAAEC,EAAC,CAAC,EAAC,CAAC;AAAE,eAAQI,KAAE,GAAEA,KAAED,IAAEC,KAAI,CAAAH,GAAE,KAAK,EAAC,IAAG,UAAS,MAAK,CAACC,GAAEE,EAAC,EAAE,CAAC,IAAE,EAAEL,IAAEC,EAAC,GAAEE,GAAEE,EAAC,EAAE,CAAC,IAAE,EAAEL,IAAEC,EAAC,CAAC,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM,EAAC,MAAK,YAAW,KAAIC,GAAC;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAE;AAAC,SAAO,SAASD,IAAEC,IAAE;AAAC,QAAIC,KAAEF,GAAE,aAAW;AAAU,QAAG,CAAC,EAAEE,EAAC,EAAE,SAAOA,IAAE;AAAA,MAAC,KAAI;AAAS,UAAEA,EAAC,MAAI,EAAEA,EAAC,IAAE,IAAI,EAAED,EAAC;AAAG;AAAA,MAAM,KAAI;AAAc,UAAEC,EAAC,MAAI,EAAEA,EAAC,IAAE,IAAI,EAAED,EAAC;AAAG;AAAA,MAAM,KAAI;AAAO,UAAEC,EAAC,MAAI,EAAEA,EAAC,IAAE,IAAI,EAAED,EAAC;AAAG;AAAA,MAAM,KAAI;AAAS,UAAEC,EAAC,MAAI,EAAEA,EAAC,IAAE,IAAI,EAAED,EAAC;AAAG;AAAA,MAAM,KAAI;AAAc,UAAEC,EAAC,MAAI,EAAEA,EAAC,IAAE,IAAI,EAAED,EAAC;AAAG;AAAA,MAAM;AAAQ,QAAAC,KAAE,WAAU,EAAEA,EAAC,MAAI,EAAEA,EAAC,IAAE,IAAI,EAAED,EAAC;AAAA,IAAE;AAAC,WAAO,EAAEC,EAAC;AAAA,EAAC,EAAED,IAAE,CAAC,EAAE,aAAaD,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,QAAMC,KAAE,OAAO,OAAO,CAAC,GAAED,EAAC;AAAE,SAAOC,GAAE,aAAW,QAAOD,GAAE,SAAOC,GAAE,OAAKD,GAAE,OAAK,IAAGC;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,SAAOA,GAAE,eAAaA,GAAE,aAAW,IAAI,EAAEA,GAAE,QAAM,CAAC,IAAGA,GAAE,WAAW,KAAK;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAEC,IAAEC,KAAE,GAAE;AAAC,SAAOD,GAAE,YAAUC,MAAG,EAAED,EAAC,KAAGD,KAAED,MAAGA;AAAE;AAAC,SAAS,EAAEA,IAAEC,IAAEC,KAAE,GAAE;AAAC,SAAO,EAAE,CAACF,IAAEA,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,OAAG;AAAC,QAAMC,KAAED,KAAED,GAAE,yBAAuBA,GAAE,oBAAmBG,KAAE,EAAEP,IAAEC,IAAEC,IAAEC,IAAEC,IAAE,MAAG,KAAE;AAAE,MAAGE,GAAE,QAAOC;AAAE,QAAMC,KAAE,EAAER,IAAEC,IAAEC,IAAEC,IAAEC,IAAE,MAAG,IAAE;AAAE,SAAOG,GAAE,OAAOC,EAAC;AAAC;AAAC,SAAS,EAAER,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,KAAK,IAAIP,KAAEE,IAAE,CAAC,IAAE,KAAK,IAAID,KAAEE,IAAE,CAAC,GAAEK,KAAE,KAAK,KAAKD,EAAC;AAAE,MAAIE,KAAE;AAAE,EAAAA,KAAED,KAAE,MAAI,IAAEA,KAAE,MAAI,MAAG,YAAUA,KAAE;AAAS,MAAIE,KAAEN,GAAE,uBAAqB;AAAE,EAAAM,KAAEA,KAAE,MAAIH,OAAIG,KAAEF,KAAE;AAAI,QAAMG,KAAED,KAAE,GAAEE,KAAE,MAAG,MAAG,EAAER,EAAC;AAAE,MAAIS,KAAET,GAAE,SAAOA,GAAE,uBAAqBD,KAAEF,MAAG,KAAIa,KAAEV,GAAE,SAAOA,GAAE,uBAAqBJ,KAAEE,MAAG;AAAI,EAAAW,KAAE,EAAEA,IAAET,IAAEK,EAAC,GAAEK,KAAE,EAAEA,IAAEV,IAAEK,EAAC;AAAE,QAAMM,KAAE,CAAC,GAAEC,KAAE,MAAI,EAAEL,IAAEP,IAAEK,EAAC,GAAEQ,KAAE,MAAI,EAAEP,IAAEN,IAAEK,EAAC,GAAES,KAAEd,GAAE;AAAiB,SAAOC,OAAIC,KAAES,GAAE,KAAK,EAAC,IAAG,QAAO,MAAK,CAACf,MAAGkB,KAAE,IAAEF,GAAE,IAAGf,MAAGiB,KAAE,IAAEF,GAAE,EAAE,EAAC,CAAC,IAAED,GAAE,KAAK,EAAC,IAAG,QAAO,MAAK,CAACf,MAAGkB,KAAE,IAAE,EAAER,IAAEN,IAAEK,EAAC,IAAGR,MAAGiB,KAAE,IAAE,EAAER,IAAEN,IAAEK,EAAC,EAAE,EAAC,CAAC,IAAGH,KAAES,GAAE,KAAK,EAAC,IAAG,YAAW,MAAK,CAACF,KAAEb,MAAGE,KAAEF,MAAGY,KAAEI,GAAE,GAAEF,KAAEb,MAAGE,KAAEF,MAAGW,KAAEI,GAAE,GAAEH,KAAEb,KAAE,KAAGE,KAAEF,MAAGY,KAAEI,GAAE,GAAEF,KAAEb,KAAE,KAAGE,KAAEF,MAAGW,KAAEI,GAAE,GAAEd,MAAGgB,KAAE,IAAEF,GAAE,IAAGb,MAAGe,KAAE,IAAEF,GAAE,EAAE,EAAC,CAAC,IAAED,GAAE,KAAK,EAAC,IAAG,YAAW,MAAK,CAACF,KAAEb,MAAGE,KAAEF,MAAGY,KAAEK,GAAE,GAAEH,KAAEb,MAAGE,KAAEF,MAAGW,KAAEK,GAAE,GAAEJ,KAAEb,KAAE,KAAGE,KAAEF,MAAGY,KAAEK,GAAE,GAAEH,KAAEb,KAAE,KAAGE,KAAEF,MAAGW,KAAEK,GAAE,GAAEf,MAAGgB,KAAE,IAAED,GAAE,IAAGd,MAAGe,KAAE,IAAED,GAAE,EAAE,EAAC,CAAC,GAAEF;AAAC;AAAC,SAAS,EAAEf,IAAEC,IAAEC,IAAE;AAAC,MAAG,CAACF,GAAE,OAAO,QAAM,CAAC;AAAE,QAAMG,KAAE,CAAC;AAAE,EAAAA,GAAE,KAAK,CAACH,GAAE,CAAC,EAAE,CAAC,IAAE,EAAEC,IAAEC,EAAC,GAAEF,GAAE,CAAC,EAAE,CAAC,IAAE,EAAEC,IAAEC,EAAC,CAAC,CAAC,GAAEC,GAAE,KAAK,CAACH,GAAE,CAAC,EAAE,CAAC,IAAE,EAAEC,IAAEC,EAAC,GAAEF,GAAE,CAAC,EAAE,CAAC,IAAE,EAAEC,IAAEC,EAAC,CAAC,CAAC;AAAE,WAAQE,KAAE,GAAEA,KAAEJ,GAAE,QAAOI,KAAI,CAAAD,GAAE,KAAK,CAACH,GAAEI,EAAC,EAAE,CAAC,IAAE,EAAEH,IAAEC,EAAC,GAAEF,GAAEI,EAAC,EAAE,CAAC,IAAE,EAAEH,IAAEC,EAAC,CAAC,CAAC,GAAEE,OAAIJ,GAAE,SAAO,KAAGG,GAAE,KAAK,CAACH,GAAEI,EAAC,EAAE,CAAC,IAAE,EAAEH,IAAEC,EAAC,GAAEF,GAAEI,EAAC,EAAE,CAAC,IAAE,EAAEH,IAAEC,EAAC,CAAC,CAAC;AAAE,SAAO,EAAEC,IAAE,MAAKD,EAAC;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEH,GAAE,QAAOI,KAAE,CAAC;AAAE,MAAGD,KAAE,GAAE;AAAC,UAAME,KAAE,CAAC,GAAEC,KAAE,IAAEJ,GAAE;AAAe,IAAAE,GAAE,KAAK,EAAC,IAAG,QAAO,MAAK,CAACJ,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC;AAAE,aAAQC,KAAE,GAAEA,KAAE,IAAEE,IAAEF,MAAI;AAAC,YAAMC,KAAEF,GAAEC,EAAC;AAAE,MAAAI,GAAE,CAAC,IAAE,CAACH,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAEG,GAAE,CAAC,IAAE,CAACH,GAAE,CAAC,KAAGI,KAAEN,GAAEC,KAAE,CAAC,EAAE,CAAC,IAAEK,KAAEN,GAAEC,KAAE,CAAC,EAAE,CAAC,KAAG,GAAEC,GAAE,CAAC,KAAGI,KAAEN,GAAEC,KAAE,CAAC,EAAE,CAAC,IAAEK,KAAEN,GAAEC,KAAE,CAAC,EAAE,CAAC,KAAG,CAAC,GAAEI,GAAE,CAAC,IAAE,CAACL,GAAEC,KAAE,CAAC,EAAE,CAAC,KAAGK,KAAEN,GAAEC,EAAC,EAAE,CAAC,IAAEK,KAAEN,GAAEC,KAAE,CAAC,EAAE,CAAC,KAAG,GAAED,GAAEC,KAAE,CAAC,EAAE,CAAC,KAAGK,KAAEN,GAAEC,EAAC,EAAE,CAAC,IAAEK,KAAEN,GAAEC,KAAE,CAAC,EAAE,CAAC,KAAG,CAAC,GAAEI,GAAE,CAAC,IAAE,CAACL,GAAEC,KAAE,CAAC,EAAE,CAAC,GAAED,GAAEC,KAAE,CAAC,EAAE,CAAC,CAAC,GAAEG,GAAE,KAAK,EAAC,IAAG,YAAW,MAAK,CAACC,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC;AAAA,IAAC;AAAC,QAAGJ,MAAG,MAAIA,GAAE,QAAO;AAAC,YAAMD,KAAEE,GAAE;AAAoB,MAAAE,GAAE,KAAK,EAAC,IAAG,UAAS,MAAK,CAACH,GAAE,CAAC,IAAE,EAAED,IAAEE,EAAC,GAAED,GAAE,CAAC,IAAE,EAAED,IAAEE,EAAC,CAAC,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC,MAAM,OAAIC,MAAGC,GAAE,KAAK,EAAC,IAAG,QAAO,MAAK,CAACJ,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,GAAEI,GAAE,KAAK,EAAC,IAAG,YAAW,MAAK,CAACJ,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,KAAG,MAAIG,MAAGC,GAAE,KAAK,GAAG,EAAEJ,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAEE,IAAE,MAAG,IAAE,CAAC;AAAE,SAAOE;AAAC;AAAC,SAAS,EAAEJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,MAAG,MAAIF,GAAE,WAAU;AAAC,IAAAP,MAAG,GAAES,GAAE,KAAK,CAACR,KAAEE,KAAE,KAAK,IAAI,CAACH,EAAC,GAAEE,KAAEE,KAAE,KAAK,IAAI,CAACJ,EAAC,CAAC,CAAC;AAAE,aAAQK,KAAE,GAAEA,MAAG,IAAE,KAAK,IAAGA,MAAGL,IAAE;AAAC,YAAMA,KAAE,CAACC,KAAEE,KAAE,KAAK,IAAIE,EAAC,GAAEH,KAAEE,KAAE,KAAK,IAAIC,EAAC,CAAC;AAAE,MAAAG,GAAE,KAAKR,EAAC,GAAES,GAAE,KAAKT,EAAC;AAAA,IAAC;AAAC,IAAAS,GAAE,KAAK,CAACR,KAAEE,KAAE,KAAK,IAAI,CAAC,GAAED,KAAEE,KAAE,KAAK,IAAI,CAAC,CAAC,CAAC,GAAEK,GAAE,KAAK,CAACR,KAAEE,KAAE,KAAK,IAAIH,EAAC,GAAEE,KAAEE,KAAE,KAAK,IAAIJ,EAAC,CAAC,CAAC;AAAA,EAAC,OAAK;AAAC,UAAMU,KAAE,EAAE,KAAGH,EAAC,IAAE,KAAK,KAAG;AAAE,IAAAE,GAAE,KAAK,CAAC,EAAEJ,IAAEE,EAAC,IAAEN,KAAE,MAAGE,KAAE,KAAK,IAAIO,KAAEV,EAAC,GAAE,EAAEK,IAAEE,EAAC,IAAEL,KAAE,MAAGE,KAAE,KAAK,IAAIM,KAAEV,EAAC,CAAC,CAAC;AAAE,UAAMW,KAAE,IAAE,KAAK,KAAGD,KAAE;AAAI,aAAQJ,KAAEI,IAAEJ,KAAEK,IAAEL,MAAGN,IAAE;AAAC,YAAMA,KAAE,CAAC,EAAEK,IAAEE,EAAC,IAAEN,KAAEE,KAAE,KAAK,IAAIG,EAAC,GAAE,EAAED,IAAEE,EAAC,IAAEL,KAAEE,KAAE,KAAK,IAAIE,EAAC,CAAC;AAAE,MAAAE,GAAE,KAAKR,EAAC,GAAES,GAAE,KAAKT,EAAC;AAAA,IAAC;AAAC,IAAAS,GAAE,KAAK,CAAC,EAAEJ,IAAEE,EAAC,IAAEN,KAAEE,KAAE,KAAK,IAAIO,KAAE,IAAE,KAAK,KAAG,MAAGJ,EAAC,GAAE,EAAED,IAAEE,EAAC,IAAEL,KAAEE,KAAE,KAAK,IAAIM,KAAE,IAAE,KAAK,KAAG,MAAGJ,EAAC,CAAC,CAAC,GAAEG,GAAE,KAAK,CAAC,EAAEJ,IAAEE,EAAC,IAAEN,KAAE,OAAIE,KAAE,KAAK,IAAIO,KAAEJ,EAAC,GAAE,EAAED,IAAEE,EAAC,IAAEL,KAAE,OAAIE,KAAE,KAAK,IAAIM,KAAEJ,EAAC,CAAC,CAAC,GAAEG,GAAE,KAAK,CAAC,EAAEJ,IAAEE,EAAC,IAAEN,KAAE,MAAGE,KAAE,KAAK,IAAIO,KAAE,MAAGJ,EAAC,GAAE,EAAED,IAAEE,EAAC,IAAEL,KAAE,MAAGE,KAAE,KAAK,IAAIM,KAAE,MAAGJ,EAAC,CAAC,CAAC;AAAA,EAAC;AAAC,SAAM,CAACG,IAAED,EAAC;AAAC;AAAC,SAAS,EAAER,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEJ,KAAE,EAAE,KAAGG,EAAC,GAAEE,KAAE,CAAC;AAAE,EAAAA,GAAE,KAAK,CAAC,EAAEH,IAAEC,EAAC,IAAEP,KAAE,MAAGE,KAAE,KAAK,IAAIM,KAAET,EAAC,GAAE,EAAEO,IAAEC,EAAC,IAAEN,KAAE,MAAGE,KAAE,KAAK,IAAIK,KAAET,EAAC,CAAC,CAAC;AAAE,WAAQK,KAAEI,IAAEJ,MAAGC,IAAED,MAAGL,GAAE,CAAAU,GAAE,KAAK,CAAC,EAAEH,IAAEC,EAAC,IAAEP,KAAEE,KAAE,KAAK,IAAIE,EAAC,GAAE,EAAEE,IAAEC,EAAC,IAAEN,KAAEE,KAAE,KAAK,IAAIC,EAAC,CAAC,CAAC;AAAE,SAAOK,GAAE,KAAK,CAACT,KAAEE,KAAE,KAAK,IAAIG,EAAC,GAAEJ,KAAEE,KAAE,KAAK,IAAIE,EAAC,CAAC,CAAC,GAAEI,GAAE,KAAK,CAACT,KAAEE,KAAE,KAAK,IAAIG,EAAC,GAAEJ,KAAEE,KAAE,KAAK,IAAIE,EAAC,CAAC,CAAC,GAAE,EAAEI,IAAE,MAAKF,EAAC;AAAC;AAAC,SAAS,EAAER,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,CAAC,GAAEC,KAAE,CAACF,GAAE,uBAAqB,IAAGA,GAAE,uBAAqB,KAAG,GAAE;AAAE,MAAIG,KAAE,CAAC,GAAE,CAAC;AAAE,QAAMC,KAAEJ,GAAE,qBAAmB,IAAE,GAAEK,KAAEL,GAAE;AAAiB,WAAQM,KAAE,GAAEA,KAAEF,IAAEE,KAAI,OAAIA,KAAEL,GAAE,KAAK,EAAC,IAAG,QAAO,MAAK,CAACF,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAC,CAAC,IAAEE,GAAE,KAAK,EAAC,IAAG,QAAO,MAAK,CAACF,GAAE,CAAC,KAAGM,KAAE,IAAE,EAAEH,GAAE,CAAC,GAAEF,EAAC,IAAGD,GAAE,CAAC,KAAGM,KAAE,IAAE,EAAEH,GAAE,CAAC,GAAEF,EAAC,EAAE,EAAC,CAAC,GAAEG,KAAEE,KAAE,CAACR,IAAEC,EAAC,IAAE,CAACD,KAAE,EAAEK,GAAEI,EAAC,GAAEN,EAAC,GAAEF,KAAE,EAAEI,GAAEI,EAAC,GAAEN,EAAC,CAAC,GAAEC,GAAE,KAAK,EAAC,IAAG,YAAW,MAAK,CAACR,KAAE,EAAES,GAAEI,EAAC,GAAEN,EAAC,GAAEN,KAAE,EAAEQ,GAAEI,EAAC,GAAEN,EAAC,GAAEL,KAAE,EAAEO,GAAEI,EAAC,GAAEN,EAAC,GAAEJ,KAAE,EAAEM,GAAEI,EAAC,GAAEN,EAAC,GAAEG,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAC,CAAC;AAAE,SAAOF;AAAC;AAAC,SAAS,EAAER,IAAE;AAAC,SAAM,CAAC,GAAGA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAEC,KAAE,GAAE;AAAC,QAAMC,KAAEF,GAAE;AAAO,MAAGE,KAAE,EAAE,OAAM,IAAI,MAAM,0CAA0C;AAAE,QAAMC,KAAE,CAAC;AAAE,MAAG,MAAID,GAAE,CAAAC,GAAE,KAAK,EAAEH,GAAE,CAAC,CAAC,GAAE,EAAEA,GAAE,CAAC,CAAC,GAAE,EAAEA,GAAE,CAAC,CAAC,GAAE,EAAEA,GAAE,CAAC,CAAC,CAAC;AAAA,OAAM;AAAC,UAAME,KAAE,CAAC;AAAE,IAAAA,GAAE,KAAKF,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAE,aAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,CAAAC,GAAE,KAAKF,GAAEC,EAAC,CAAC,GAAEA,OAAID,GAAE,SAAO,KAAGE,GAAE,KAAKF,GAAEC,EAAC,CAAC;AAAE,UAAMG,KAAE,CAAC,GAAEC,KAAE,IAAEJ;AAAE,IAAAE,GAAE,KAAK,EAAED,GAAE,CAAC,CAAC,CAAC;AAAE,aAAQF,KAAE,GAAEA,KAAE,IAAEE,GAAE,QAAOF,MAAI;AAAC,YAAMC,KAAEC,GAAEF,EAAC;AAAE,MAAAI,GAAE,CAAC,IAAE,CAACH,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAEG,GAAE,CAAC,IAAE,CAACH,GAAE,CAAC,KAAGI,KAAEH,GAAEF,KAAE,CAAC,EAAE,CAAC,IAAEK,KAAEH,GAAEF,KAAE,CAAC,EAAE,CAAC,KAAG,GAAEC,GAAE,CAAC,KAAGI,KAAEH,GAAEF,KAAE,CAAC,EAAE,CAAC,IAAEK,KAAEH,GAAEF,KAAE,CAAC,EAAE,CAAC,KAAG,CAAC,GAAEI,GAAE,CAAC,IAAE,CAACF,GAAEF,KAAE,CAAC,EAAE,CAAC,KAAGK,KAAEH,GAAEF,EAAC,EAAE,CAAC,IAAEK,KAAEH,GAAEF,KAAE,CAAC,EAAE,CAAC,KAAG,GAAEE,GAAEF,KAAE,CAAC,EAAE,CAAC,KAAGK,KAAEH,GAAEF,EAAC,EAAE,CAAC,IAAEK,KAAEH,GAAEF,KAAE,CAAC,EAAE,CAAC,KAAG,CAAC,GAAEI,GAAE,CAAC,IAAE,CAACF,GAAEF,KAAE,CAAC,EAAE,CAAC,GAAEE,GAAEF,KAAE,CAAC,EAAE,CAAC,CAAC,GAAEG,GAAE,KAAKC,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,SAAS,EAAEH,IAAEC,IAAE;AAAC,SAAO,KAAK,IAAID,GAAE,CAAC,IAAEC,GAAE,CAAC,GAAE,CAAC,IAAE,KAAK,IAAID,GAAE,CAAC,IAAEC,GAAE,CAAC,GAAE,CAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAEF,IAAEC,EAAC;AAAE,MAAG,MAAIC,GAAE,QAAO,EAAEH,IAAEC,EAAC;AAAE,MAAIG,OAAIJ,GAAE,CAAC,IAAEC,GAAE,CAAC,MAAIC,GAAE,CAAC,IAAED,GAAE,CAAC,MAAID,GAAE,CAAC,IAAEC,GAAE,CAAC,MAAIC,GAAE,CAAC,IAAED,GAAE,CAAC,MAAIE;AAAE,SAAOC,KAAE,KAAK,IAAI,GAAE,KAAK,IAAI,GAAEA,EAAC,CAAC,GAAE,EAAEJ,IAAE,EAAEC,IAAEC,IAAEE,EAAC,CAAC;AAAC;AAAC,SAAS,EAAEJ,IAAEC,IAAEC,IAAE;AAAC,SAAM,CAACF,GAAE,CAAC,KAAGC,GAAE,CAAC,IAAED,GAAE,CAAC,KAAGE,IAAEF,GAAE,CAAC,KAAGC,GAAE,CAAC,IAAED,GAAE,CAAC,KAAGE,EAAC;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAED,MAAG,CAAC;AAAE,MAAG,SAASH,IAAEC,IAAE;AAAC,UAAMC,KAAEF,GAAEC,KAAE,CAAC,GAAEE,KAAEH,GAAEC,KAAE,CAAC,GAAEG,KAAEJ,GAAEC,KAAE,CAAC,GAAEI,KAAEL,GAAEC,KAAE,CAAC;AAAE,QAAIK,KAAE,IAAEH,GAAE,CAAC,IAAE,IAAED,GAAE,CAAC,IAAEG,GAAE,CAAC;AAAE,IAAAC,MAAGA;AAAE,QAAIC,KAAE,IAAEJ,GAAE,CAAC,IAAE,IAAED,GAAE,CAAC,IAAEG,GAAE,CAAC;AAAE,IAAAE,MAAGA;AAAE,QAAIC,KAAE,IAAEJ,GAAE,CAAC,IAAE,IAAEC,GAAE,CAAC,IAAEH,GAAE,CAAC;AAAE,IAAAM,MAAGA;AAAE,QAAIC,KAAE,IAAEL,GAAE,CAAC,IAAE,IAAEC,GAAE,CAAC,IAAEH,GAAE,CAAC;AAAE,WAAOO,MAAGA,IAAEH,KAAEE,OAAIF,KAAEE,KAAGD,KAAEE,OAAIF,KAAEE,KAAGH,KAAEC;AAAA,EAAC,EAAEP,IAAEC,EAAC,IAAEC,IAAE;AAAC,UAAMA,KAAEF,GAAEC,KAAE,CAAC;AAAE,QAAGG,GAAE,QAAO;AAAC,OAACC,KAAED,GAAEA,GAAE,SAAO,CAAC,GAAEE,KAAEJ,IAAE,KAAK,KAAK,EAAEG,IAAEC,EAAC,CAAC,KAAG,KAAGF,GAAE,KAAKF,EAAC;AAAA,IAAC,MAAM,CAAAE,GAAE,KAAKF,EAAC;AAAE,IAAAE,GAAE,KAAKJ,GAAEC,KAAE,CAAC,CAAC;AAAA,EAAC,OAAK;AAAC,UAAME,KAAE,KAAGE,KAAEL,GAAEC,KAAE,CAAC,GAAEK,KAAEN,GAAEC,KAAE,CAAC,GAAEM,KAAEP,GAAEC,KAAE,CAAC,GAAEO,KAAER,GAAEC,KAAE,CAAC,GAAEQ,KAAE,EAAEJ,IAAEC,IAAEH,EAAC,GAAEO,KAAE,EAAEJ,IAAEC,IAAEJ,EAAC,GAAEQ,KAAE,EAAEJ,IAAEC,IAAEL,EAAC,GAAES,KAAE,EAAEH,IAAEC,IAAEP,EAAC,GAAEU,KAAE,EAAEH,IAAEC,IAAER,EAAC,GAAEW,KAAE,EAAEF,IAAEC,IAAEV,EAAC;AAAE,MAAE,CAACE,IAAEI,IAAEG,IAAEE,EAAC,GAAE,GAAEZ,IAAEE,EAAC,GAAE,EAAE,CAACU,IAAED,IAAEF,IAAEH,EAAC,GAAE,GAAEN,IAAEE,EAAC;AAAA,EAAC;AAAC,MAAIC,IAAEC;AAAE,SAAOF;AAAC;AAAC,SAAS,EAAEJ,IAAEC,IAAE;AAAC,SAAO,EAAED,IAAE,GAAEA,GAAE,QAAOC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAED,MAAG,CAAC,GAAEE,KAAEN,GAAEC,EAAC,GAAEM,KAAEP,GAAEE,KAAE,CAAC;AAAE,MAAIM,KAAE,GAAEC,KAAE;AAAE,WAAQN,KAAEF,KAAE,GAAEE,KAAED,KAAE,GAAE,EAAEC,IAAE;AAAC,UAAMF,KAAE,EAAED,GAAEG,EAAC,GAAEG,IAAEC,EAAC;AAAE,IAAAN,KAAEO,OAAIA,KAAEP,IAAEQ,KAAEN;AAAA,EAAE;AAAC,SAAO,KAAK,KAAKK,EAAC,IAAEL,MAAG,EAAEH,IAAEC,IAAEQ,KAAE,GAAEN,IAAEE,EAAC,GAAE,EAAEL,IAAES,IAAEP,IAAEC,IAAEE,EAAC,MAAIA,GAAE,UAAQA,GAAE,KAAKC,EAAC,GAAED,GAAE,KAAKE,EAAC,IAAGF;AAAC;AAAC,SAAS,EAAEL,IAAEC,KAAE,MAAIC,IAAE;AAAC,QAAMC,KAAE,CAAC,GAAEC,MAAGJ,GAAE,SAAO,KAAG;AAAE,WAAQE,KAAE,GAAEA,KAAEE,IAAEF,MAAI;AAAC,MAAEF,IAAE,IAAEE,IAAED,IAAEE,EAAC;AAAA,EAAC;AAAC,SAAOD,MAAGA,KAAE,IAAE,EAAEC,IAAE,GAAEA,GAAE,QAAOD,EAAC,IAAEC;AAAC;AAAC,IAAM,KAAG;AAAO,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYH,IAAE;AAAC,SAAK,iBAAe,EAAC,qBAAoB,GAAE,WAAU,GAAE,QAAO,GAAE,QAAO,QAAO,aAAY,GAAE,gBAAe,GAAE,cAAa,MAAI,gBAAe,GAAE,WAAU,WAAU,YAAW,IAAG,cAAa,KAAI,YAAW,IAAG,YAAW,IAAG,SAAQ,IAAG,cAAa,IAAG,MAAK,GAAE,oBAAmB,OAAG,wBAAuB,OAAG,kBAAiB,OAAG,wBAAuB,IAAE,GAAE,KAAK,SAAOA,MAAG,CAAC,GAAE,KAAK,OAAO,YAAU,KAAK,iBAAe,KAAK,GAAG,KAAK,OAAO,OAAO;AAAA,EAAE;AAAA,EAAC,OAAO,UAAS;AAAC,WAAO,KAAK,MAAM,KAAK,OAAO,IAAE,KAAG,EAAE;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAE;AAAC,WAAOA,KAAE,OAAO,OAAO,CAAC,GAAE,KAAK,gBAAeA,EAAC,IAAE,KAAK;AAAA,EAAc;AAAA,EAAC,GAAGA,IAAEC,IAAEC,IAAE;AAAC,WAAM,EAAC,OAAMF,IAAE,MAAKC,MAAG,CAAC,GAAE,SAAQC,MAAG,KAAK,eAAc;AAAA,EAAC;AAAA,EAAC,KAAKF,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,GAAGD,EAAC;AAAE,WAAO,KAAK,GAAG,QAAO,CAAC,EAAEJ,IAAEC,IAAEC,IAAEC,IAAEE,EAAC,CAAC,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,UAAUL,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,GAAGD,EAAC,GAAEE,KAAE,CAAC,GAAEC,KAAE,EAAEP,IAAEC,IAAEC,IAAEC,IAAEE,EAAC;AAAE,QAAGA,GAAE,MAAK;AAAC,YAAMD,KAAE,CAAC,CAACJ,IAAEC,EAAC,GAAE,CAACD,KAAEE,IAAED,EAAC,GAAE,CAACD,KAAEE,IAAED,KAAEE,EAAC,GAAE,CAACH,IAAEC,KAAEE,EAAC,CAAC;AAAE,kBAAUE,GAAE,YAAUC,GAAE,KAAK,EAAE,CAACF,EAAC,GAAEC,EAAC,CAAC,IAAEC,GAAE,KAAK,EAAE,CAACF,EAAC,GAAEC,EAAC,CAAC;AAAA,IAAC;AAAC,WAAOA,GAAE,WAAS,MAAIC,GAAE,KAAKC,EAAC,GAAE,KAAK,GAAG,aAAYD,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQL,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,GAAGD,EAAC,GAAEE,KAAE,CAAC,GAAEC,KAAE,EAAEL,IAAEC,IAAEE,EAAC,GAAEG,KAAE,EAAER,IAAEC,IAAEI,IAAEE,EAAC;AAAE,QAAGF,GAAE,KAAK,KAAG,YAAUA,GAAE,WAAU;AAAC,YAAMH,KAAE,EAAEF,IAAEC,IAAEI,IAAEE,EAAC,EAAE;AAAM,MAAAL,GAAE,OAAK,YAAWI,GAAE,KAAKJ,EAAC;AAAA,IAAC,MAAM,CAAAI,GAAE,KAAK,EAAE,CAACE,GAAE,eAAe,GAAEH,EAAC,CAAC;AAAE,WAAOA,GAAE,WAAS,MAAIC,GAAE,KAAKE,GAAE,KAAK,GAAE,KAAK,GAAG,WAAUF,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,OAAOL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,QAAQJ,IAAEC,IAAEC,IAAEA,IAAEC,EAAC;AAAE,WAAOC,GAAE,QAAM,UAASA;AAAA,EAAC;AAAA,EAAC,WAAWJ,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,GAAGD,EAAC;AAAE,WAAO,KAAK,GAAG,cAAa,CAAC,EAAED,IAAE,OAAGE,EAAC,CAAC,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIF,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,OAAGC,IAAE;AAAC,UAAMC,KAAE,KAAK,GAAGD,EAAC,GAAEE,KAAE,CAAC,GAAEC,KAAE,EAAEV,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE,MAAGE,EAAC;AAAE,QAAGF,MAAGE,GAAE,KAAK,KAAG,YAAUA,GAAE,WAAU;AAAC,YAAMF,KAAE,OAAO,OAAO,CAAC,GAAEE,EAAC;AAAE,MAAAF,GAAE,qBAAmB;AAAG,YAAMC,KAAE,EAAEP,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE,MAAG,OAAGC,EAAC;AAAE,MAAAC,GAAE,OAAK,YAAWE,GAAE,KAAKF,EAAC;AAAA,IAAC,MAAM,CAAAE,GAAE,KAAK,SAAST,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAMC,KAAEP,IAAEQ,KAAEP;AAAE,UAAIQ,KAAE,KAAK,IAAIP,KAAE,CAAC,GAAEQ,KAAE,KAAK,IAAIP,KAAE,CAAC;AAAE,MAAAM,MAAG,EAAE,OAAIA,IAAEH,EAAC,GAAEI,MAAG,EAAE,OAAIA,IAAEJ,EAAC;AAAE,UAAIK,KAAEP,IAAEQ,KAAEP;AAAE,aAAKM,KAAE,IAAG,CAAAA,MAAG,IAAE,KAAK,IAAGC,MAAG,IAAE,KAAK;AAAG,MAAAA,KAAED,KAAE,IAAE,KAAK,OAAKA,KAAE,GAAEC,KAAE,IAAE,KAAK;AAAI,YAAMC,MAAGD,KAAED,MAAGL,GAAE,gBAAeQ,KAAE,CAAC;AAAE,eAAQd,KAAEW,IAAEX,MAAGY,IAAEZ,MAAGa,GAAE,CAAAC,GAAE,KAAK,CAACP,KAAEE,KAAE,KAAK,IAAIT,EAAC,GAAEQ,KAAEE,KAAE,KAAK,IAAIV,EAAC,CAAC,CAAC;AAAE,aAAOc,GAAE,KAAK,CAACP,KAAEE,KAAE,KAAK,IAAIG,EAAC,GAAEJ,KAAEE,KAAE,KAAK,IAAIE,EAAC,CAAC,CAAC,GAAEE,GAAE,KAAK,CAACP,IAAEC,EAAC,CAAC,GAAE,EAAE,CAACM,EAAC,GAAER,EAAC;AAAA,IAAC,EAAEN,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEG,EAAC,CAAC;AAAE,WAAOA,GAAE,WAAS,MAAIC,GAAE,KAAKC,EAAC,GAAE,KAAK,GAAG,OAAMD,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,MAAMR,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,GAAGD,EAAC,GAAEE,KAAE,CAAC,GAAEC,KAAE,EAAEJ,IAAEE,EAAC;AAAE,QAAGA,GAAE,QAAMA,GAAE,SAAO,GAAG,KAAG,YAAUA,GAAE,WAAU;AAAC,YAAMD,KAAE,EAAED,IAAE,OAAO,OAAO,OAAO,OAAO,CAAC,GAAEE,EAAC,GAAE,EAAC,oBAAmB,MAAG,WAAUA,GAAE,YAAUA,GAAE,YAAUA,GAAE,yBAAuB,EAAC,CAAC,CAAC;AAAE,MAAAC,GAAE,KAAK,EAAC,MAAK,YAAW,KAAI,KAAK,aAAaF,GAAE,GAAG,EAAC,CAAC;AAAA,IAAC,OAAK;AAAC,YAAMA,KAAE,CAAC,GAAEG,KAAEJ;AAAE,UAAGI,GAAE,QAAO;AAAC,cAAMJ,KAAE,YAAU,OAAOI,GAAE,CAAC,EAAE,CAAC,IAAE,CAACA,EAAC,IAAEA;AAAE,mBAAUD,MAAKH,GAAE,CAAAG,GAAE,SAAO,IAAEF,GAAE,KAAK,GAAGE,EAAC,IAAE,MAAIA,GAAE,SAAOF,GAAE,KAAK,GAAG,EAAE,EAAE,CAACE,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,CAAC,GAAE,KAAI,IAAED,GAAE,aAAW,CAAC,CAAC,IAAED,GAAE,KAAK,GAAG,EAAE,EAAEE,EAAC,GAAE,KAAI,IAAED,GAAE,aAAW,CAAC,CAAC;AAAA,MAAC;AAAC,MAAAD,GAAE,UAAQE,GAAE,KAAK,EAAE,CAACF,EAAC,GAAEC,EAAC,CAAC;AAAA,IAAC;AAAC,WAAOA,GAAE,WAAS,MAAIC,GAAE,KAAKC,EAAC,GAAE,KAAK,GAAG,SAAQD,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQF,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,GAAGD,EAAC,GAAEE,KAAE,CAAC,GAAEC,KAAE,EAAEJ,IAAE,MAAGE,EAAC;AAAE,WAAOA,GAAE,SAAO,YAAUA,GAAE,YAAUC,GAAE,KAAK,EAAE,CAACH,EAAC,GAAEE,EAAC,CAAC,IAAEC,GAAE,KAAK,EAAE,CAACH,EAAC,GAAEE,EAAC,CAAC,IAAGA,GAAE,WAAS,MAAIC,GAAE,KAAKC,EAAC,GAAE,KAAK,GAAG,WAAUD,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKF,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,GAAGD,EAAC,GAAEE,KAAE,CAAC;AAAE,QAAG,CAACH,GAAE,QAAO,KAAK,GAAG,QAAOG,IAAED,EAAC;AAAE,IAAAF,MAAGA,MAAG,IAAI,QAAQ,OAAM,GAAG,EAAE,QAAQ,UAAS,GAAG,EAAE,QAAQ,WAAU,GAAG;AAAE,UAAMI,KAAEF,GAAE,QAAM,kBAAgBA,GAAE,QAAMA,GAAE,SAAO,IAAGG,KAAEH,GAAE,WAAS,IAAGI,KAAE,CAAC,EAAEJ,GAAE,kBAAgBA,GAAE,iBAAe,IAAGK,KAAE,SAASP,IAAEC,IAAEC,IAAE;AAAC,YAAMC,KAAE,EAAE,EAAE,EAAEH,EAAC,CAAC,CAAC,GAAEI,KAAE,CAAC;AAAE,UAAIC,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,YAAMC,KAAE,MAAI;AAAC,QAAAD,GAAE,UAAQ,KAAGF,GAAE,KAAK,GAAG,EAAEE,IAAEN,EAAC,CAAC,GAAEM,KAAE,CAAC;AAAA,MAAC,GAAEE,KAAE,MAAI;AAAC,QAAAD,GAAE,GAAEH,GAAE,WAASD,GAAE,KAAKC,EAAC,GAAEA,KAAE,CAAC;AAAA,MAAE;AAAE,iBAAS,EAAC,KAAIL,IAAE,MAAKC,GAAC,KAAIE,GAAE,SAAOH,IAAE;AAAA,QAAC,KAAI;AAAI,UAAAS,GAAE,GAAEH,KAAE,CAACL,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAEI,GAAE,KAAKC,EAAC;AAAE;AAAA,QAAM,KAAI;AAAI,UAAAE,GAAE,GAAEH,GAAE,KAAK,CAACJ,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,CAAC;AAAE;AAAA,QAAM,KAAI;AAAI,cAAG,CAACM,GAAE,QAAO;AAAC,kBAAMP,KAAEK,GAAE,SAAOA,GAAEA,GAAE,SAAO,CAAC,IAAEC;AAAE,YAAAC,GAAE,KAAK,CAACP,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,CAAC;AAAA,UAAC;AAAC,UAAAO,GAAE,KAAK,CAACN,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,CAAC,GAAEM,GAAE,KAAK,CAACN,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,CAAC,GAAEM,GAAE,KAAK,CAACN,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,CAAC;AAAE;AAAA,QAAM,KAAI;AAAI,UAAAO,GAAE,GAAEH,GAAE,KAAK,CAACC,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,CAAC;AAAA,MAAC;AAAC,UAAGG,GAAE,GAAE,CAACP,GAAE,QAAOE;AAAE,YAAMM,KAAE,CAAC;AAAE,iBAAUV,MAAKI,IAAE;AAAC,cAAMH,KAAE,EAAED,IAAEE,EAAC;AAAE,QAAAD,GAAE,UAAQS,GAAE,KAAKT,EAAC;AAAA,MAAC;AAAC,aAAOS;AAAA,IAAC,EAAEV,IAAE,GAAEM,KAAE,IAAE,KAAGJ,GAAE,kBAAgB,MAAI,IAAEA,GAAE,aAAW,CAAC,GAAEM,KAAE,EAAER,IAAEE,EAAC;AAAE,QAAGE,GAAE,KAAG,YAAUF,GAAE,UAAU,KAAG,MAAIK,GAAE,QAAO;AAAC,YAAMN,KAAE,EAAED,IAAE,OAAO,OAAO,OAAO,OAAO,CAAC,GAAEE,EAAC,GAAE,EAAC,oBAAmB,MAAG,WAAUA,GAAE,YAAUA,GAAE,YAAUA,GAAE,yBAAuB,EAAC,CAAC,CAAC;AAAE,MAAAC,GAAE,KAAK,EAAC,MAAK,YAAW,KAAI,KAAK,aAAaF,GAAE,GAAG,EAAC,CAAC;AAAA,IAAC,MAAM,CAAAE,GAAE,KAAK,EAAEI,IAAEL,EAAC,CAAC;AAAA,QAAO,CAAAC,GAAE,KAAK,EAAEI,IAAEL,EAAC,CAAC;AAAE,WAAOG,OAAIC,KAAEC,GAAE,QAAS,CAAAP,OAAG;AAAC,MAAAG,GAAE,KAAK,EAAEH,IAAE,OAAGE,EAAC,CAAC;AAAA,IAAC,CAAE,IAAEC,GAAE,KAAKK,EAAC,IAAG,KAAK,GAAG,QAAOL,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,UAAUF,IAAEC,IAAE;AAAC,QAAIC,KAAE;AAAG,eAAUC,MAAKH,GAAE,KAAI;AAAC,YAAMA,KAAE,YAAU,OAAOC,MAAGA,MAAG,IAAEE,GAAE,KAAK,IAAK,CAAAH,OAAG,CAACA,GAAE,QAAQC,EAAC,CAAE,IAAEE,GAAE;AAAK,cAAOA,GAAE,IAAG;AAAA,QAAC,KAAI;AAAO,UAAAD,MAAG,IAAIF,GAAE,CAAC,CAAC,IAAIA,GAAE,CAAC,CAAC;AAAI;AAAA,QAAM,KAAI;AAAW,UAAAE,MAAG,IAAIF,GAAE,CAAC,CAAC,IAAIA,GAAE,CAAC,CAAC,KAAKA,GAAE,CAAC,CAAC,IAAIA,GAAE,CAAC,CAAC,KAAKA,GAAE,CAAC,CAAC,IAAIA,GAAE,CAAC,CAAC;AAAI;AAAA,QAAM,KAAI;AAAS,UAAAE,MAAG,IAAIF,GAAE,CAAC,CAAC,IAAIA,GAAE,CAAC,CAAC;AAAA,MAAG;AAAA,IAAC;AAAC,WAAOE,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,QAAQF,IAAE;AAAC,UAAMC,KAAED,GAAE,QAAM,CAAC,GAAEE,KAAEF,GAAE,WAAS,KAAK,gBAAeG,KAAE,CAAC;AAAE,eAAUH,MAAKC,IAAE;AAAC,UAAIA,KAAE;AAAK,cAAOD,GAAE,MAAK;AAAA,QAAC,KAAI;AAAO,UAAAC,KAAE,EAAC,GAAE,KAAK,UAAUD,EAAC,GAAE,QAAOE,GAAE,QAAO,aAAYA,GAAE,aAAY,MAAK,GAAE;AAAE;AAAA,QAAM,KAAI;AAAW,UAAAD,KAAE,EAAC,GAAE,KAAK,UAAUD,EAAC,GAAE,QAAO,IAAG,aAAY,GAAE,MAAKE,GAAE,QAAM,GAAE;AAAE;AAAA,QAAM,KAAI;AAAa,UAAAD,KAAE,KAAK,WAAWD,IAAEE,EAAC;AAAA,MAAC;AAAC,MAAAD,MAAGE,GAAE,KAAKF,EAAC;AAAA,IAAC;AAAC,WAAOE;AAAA,EAAC;AAAA,EAAC,WAAWH,IAAEC,IAAE;AAAC,QAAIC,KAAED,GAAE;AAAW,WAAOC,KAAE,MAAIA,KAAED,GAAE,cAAY,IAAG,EAAC,GAAE,KAAK,UAAUD,EAAC,GAAE,QAAOC,GAAE,QAAM,IAAG,aAAYC,IAAE,MAAK,GAAE;AAAA,EAAC;AAAA,EAAC,aAAaF,IAAE;AAAC,WAAOA,GAAE,OAAQ,CAACA,IAAEC,OAAI,MAAIA,MAAG,WAASD,GAAE,EAAG;AAAA,EAAC;AAAC;AAAC,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,SAAK,SAAOD,IAAE,KAAK,MAAI,KAAK,OAAO,WAAW,IAAI,GAAE,KAAK,MAAI,IAAI,GAAGC,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKD,IAAE;AAAC,UAAMC,KAAED,GAAE,QAAM,CAAC,GAAEE,KAAEF,GAAE,WAAS,KAAK,kBAAkB,GAAEG,KAAE,KAAK,KAAIC,KAAEJ,GAAE,QAAQ;AAAwB,eAAUK,MAAKJ,GAAE,SAAOI,GAAE,MAAK;AAAA,MAAC,KAAI;AAAO,QAAAF,GAAE,KAAK,GAAEA,GAAE,cAAY,WAASD,GAAE,SAAO,gBAAcA,GAAE,QAAOC,GAAE,YAAUD,GAAE,aAAYA,GAAE,kBAAgBC,GAAE,YAAYD,GAAE,cAAc,GAAEA,GAAE,yBAAuBC,GAAE,iBAAeD,GAAE,uBAAsB,KAAK,eAAeC,IAAEE,IAAED,EAAC,GAAED,GAAE,QAAQ;AAAE;AAAA,MAAM,KAAI,YAAW;AAAC,QAAAA,GAAE,KAAK,GAAEA,GAAE,YAAUD,GAAE,QAAM;AAAG,cAAMD,KAAE,YAAUD,GAAE,SAAO,cAAYA,GAAE,SAAO,WAASA,GAAE,QAAM,YAAU;AAAU,aAAK,eAAeG,IAAEE,IAAED,IAAEH,EAAC,GAAEE,GAAE,QAAQ;AAAE;AAAA,MAAK;AAAA,MAAC,KAAI;AAAa,aAAK,WAAWA,IAAEE,IAAEH,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,WAAWF,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAED,GAAE;AAAW,IAAAC,KAAE,MAAIA,KAAED,GAAE,cAAY,IAAGF,GAAE,KAAK,GAAEE,GAAE,gBAAcF,GAAE,YAAYE,GAAE,YAAY,GAAEA,GAAE,uBAAqBF,GAAE,iBAAeE,GAAE,qBAAoBF,GAAE,cAAYE,GAAE,QAAM,IAAGF,GAAE,YAAUG,IAAE,KAAK,eAAeH,IAAEC,IAAEC,GAAE,uBAAuB,GAAEF,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAEC,IAAEC,IAAEC,KAAE,WAAU;AAAC,IAAAH,GAAE,UAAU;AAAE,eAAUG,MAAKF,GAAE,KAAI;AAAC,YAAMA,KAAE,YAAU,OAAOC,MAAGA,MAAG,IAAEC,GAAE,KAAK,IAAK,CAAAH,OAAG,CAACA,GAAE,QAAQE,EAAC,CAAE,IAAEC,GAAE;AAAK,cAAOA,GAAE,IAAG;AAAA,QAAC,KAAI;AAAO,UAAAH,GAAE,OAAOC,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAE;AAAA,QAAM,KAAI;AAAW,UAAAD,GAAE,cAAcC,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAE;AAAA,QAAM,KAAI;AAAS,UAAAD,GAAE,OAAOC,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,MAAC;AAAA,IAAC;AAAC,mBAAaA,GAAE,OAAKD,GAAE,KAAKG,EAAC,IAAEH,GAAE,OAAO;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK;AAAA,EAAG;AAAA,EAAC,oBAAmB;AAAC,WAAO,KAAK,IAAI;AAAA,EAAc;AAAA,EAAC,KAAKA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,KAAKL,IAAEC,IAAEC,IAAEC,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,UAAUL,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,UAAUL,IAAEC,IAAEC,IAAEC,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,QAAQL,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,QAAQL,IAAEC,IAAEC,IAAEC,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,OAAOL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,OAAOJ,IAAEC,IAAEC,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,WAAWJ,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,WAAWF,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,QAAQF,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,QAAQF,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,IAAIF,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,OAAGC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,IAAIR,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,MAAMR,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,MAAMF,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,KAAKF,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,KAAKF,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC,GAAEA;AAAA,EAAC;AAAC;AAAC,IAAM,KAAG;AAA6B,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYF,IAAEC,IAAE;AAAC,SAAK,MAAID,IAAE,KAAK,MAAI,IAAI,GAAGC,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKD,IAAE;AAAC,UAAMC,KAAED,GAAE,QAAM,CAAC,GAAEE,KAAEF,GAAE,WAAS,KAAK,kBAAkB,GAAEG,KAAE,KAAK,IAAI,iBAAe,OAAO,UAASC,KAAED,GAAE,gBAAgB,IAAG,GAAG,GAAEE,KAAEL,GAAE,QAAQ;AAAwB,eAAUM,MAAKL,IAAE;AAAC,UAAIA,KAAE;AAAK,cAAOK,GAAE,MAAK;AAAA,QAAC,KAAI;AAAO,UAAAL,KAAEE,GAAE,gBAAgB,IAAG,MAAM,GAAEF,GAAE,aAAa,KAAI,KAAK,UAAUK,IAAED,EAAC,CAAC,GAAEJ,GAAE,aAAa,UAASC,GAAE,MAAM,GAAED,GAAE,aAAa,gBAAeC,GAAE,cAAY,EAAE,GAAED,GAAE,aAAa,QAAO,MAAM,GAAEC,GAAE,kBAAgBD,GAAE,aAAa,oBAAmBC,GAAE,eAAe,KAAK,GAAG,EAAE,KAAK,CAAC,GAAEA,GAAE,wBAAsBD,GAAE,aAAa,qBAAoB,GAAGC,GAAE,oBAAoB,EAAE;AAAE;AAAA,QAAM,KAAI;AAAW,UAAAD,KAAEE,GAAE,gBAAgB,IAAG,MAAM,GAAEF,GAAE,aAAa,KAAI,KAAK,UAAUK,IAAED,EAAC,CAAC,GAAEJ,GAAE,aAAa,UAAS,MAAM,GAAEA,GAAE,aAAa,gBAAe,GAAG,GAAEA,GAAE,aAAa,QAAOC,GAAE,QAAM,EAAE,GAAE,YAAUF,GAAE,SAAO,cAAYA,GAAE,SAAOC,GAAE,aAAa,aAAY,SAAS;AAAE;AAAA,QAAM,KAAI;AAAa,UAAAA,KAAE,KAAK,WAAWE,IAAEG,IAAEJ,EAAC;AAAA,MAAC;AAAC,MAAAD,MAAGG,GAAE,YAAYH,EAAC;AAAA,IAAC;AAAC,WAAOG;AAAA,EAAC;AAAA,EAAC,WAAWJ,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAED,GAAE;AAAW,IAAAC,KAAE,MAAIA,KAAED,GAAE,cAAY;AAAG,UAAME,KAAEJ,GAAE,gBAAgB,IAAG,MAAM;AAAE,WAAOI,GAAE,aAAa,KAAI,KAAK,UAAUH,IAAEC,GAAE,uBAAuB,CAAC,GAAEE,GAAE,aAAa,UAASF,GAAE,QAAM,EAAE,GAAEE,GAAE,aAAa,gBAAeD,KAAE,EAAE,GAAEC,GAAE,aAAa,QAAO,MAAM,GAAEF,GAAE,gBAAcE,GAAE,aAAa,oBAAmBF,GAAE,aAAa,KAAK,GAAG,EAAE,KAAK,CAAC,GAAEA,GAAE,sBAAoBE,GAAE,aAAa,qBAAoB,GAAGF,GAAE,kBAAkB,EAAE,GAAEE;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK;AAAA,EAAG;AAAA,EAAC,oBAAmB;AAAC,WAAO,KAAK,IAAI;AAAA,EAAc;AAAA,EAAC,UAAUJ,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAI,UAAUD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKD,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,KAAKL,IAAEC,IAAEC,IAAEC,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC;AAAA,EAAC;AAAA,EAAC,UAAUL,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,UAAUL,IAAEC,IAAEC,IAAEC,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQL,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,QAAQL,IAAEC,IAAEC,IAAEC,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC;AAAA,EAAC;AAAA,EAAC,OAAOL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,OAAOJ,IAAEC,IAAEC,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC;AAAA,EAAC;AAAA,EAAC,WAAWJ,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,WAAWF,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQF,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,QAAQF,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIF,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,OAAGC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,IAAIR,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAMR,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,MAAMF,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKF,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,IAAI,KAAKF,IAAEC,EAAC;AAAE,WAAO,KAAK,KAAKC,EAAC;AAAA,EAAC;AAAC;AAAC,IAAI,KAAG,EAAC,QAAO,CAACF,IAAEC,OAAI,IAAI,GAAGD,IAAEC,EAAC,GAAE,KAAI,CAACD,IAAEC,OAAI,IAAI,GAAGD,IAAEC,EAAC,GAAE,WAAU,CAAAD,OAAG,IAAI,GAAGA,EAAC,GAAE,SAAQ,MAAI,GAAG,QAAQ,EAAC;;;ACgC3k2B,IAAI,cAA8B,OAAO,OAAO,QAAQ,MAAM,aAAa;AAhC3E;AAiCE,MAAI;AACJ,QAAM,gBAAgB,KAAK,iBAAiB,UAAS,gBAAW,MAAX,mBAAc,UAAU;AAC7E,MAAI,CAAC,UAAU;AACb,iBAAa;AAAA,EACf,OAAO;AACL,iBAAa;AAAA,EACf;AACA,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAC9F,QAAM,UAAU,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,SAAS,oBAAoB,KAAK,UAAU,CAAC;AAC9G,MAAI;AACJ,MAAI,KAAK,UAAU,QAAQ;AACzB,YAAQ;AAAA,EACV,OAAO;AACL,YAAQ,OAAO,KAAK,UAAU,WAAW,KAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,EACpE;AACA,QAAM,QAAQ,MAAM,WAAW,SAAS,aAAa,eAAe,KAAK,GAAG,WAAW,CAAC,GAAG;AAAA,IACzF;AAAA,IACA,OAAO,KAAK,WAAS,gBAAW,EAAE,cAAb,mBAAwB;AAAA;AAAA,IAE7C,YAAY;AAAA,IACZ,OAAO,KAAK;AAAA,IACZ,kBAAkB,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,KAAK;AAAA,EAC1C,CAAC;AACD,MAAI,OAAO,MAAM,QAAQ;AACzB,QAAM,gBAAe,6BAAM,YAAW,KAAK;AAC3C,MAAI,eAAe;AACjB,UAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,UAAM,KAAK,eAAO,KAAK;AACvB,UAAM,SAAS,IAAI,qBAAqB,KAAK;AAC7C,QAAI,QAAQ;AACV,YAAM,YAAY,MAAM,QAAQ,eAAe,EAAE,EAAE,KAAK,MAAM;AAC9D,YAAM,QAAQ;AAAA,QACZ,CAAC,GAAG,MAAM,EAAE;AAAA,UACV,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ;AAC5B,qBAAS,aAAa;AACpB,kBAAI,MAAM,UAAU;AACpB,kBAAI,MAAM,gBAAgB;AAC1B,kBAAI,WAAW;AACb,sBAAM,eAAe,WAAW,EAAE,WAAW,WAAW,EAAE,WAAW,OAAO,iBAAiB,SAAS,IAAI,EAAE;AAC5G,sBAAM,kBAAkB;AACxB,sBAAM,CAAC,qBAAqB,sBAAsB,QAAQ,IAAI,cAAc,YAAY;AACxF,sBAAM,QAAQ,qBAAqB,kBAAkB;AACrD,oBAAI,MAAM,WAAW;AACrB,oBAAI,MAAM,WAAW;AAAA,cACvB,OAAO;AACL,oBAAI,MAAM,QAAQ;AAAA,cACpB;AACA,kBAAI,GAAG;AAAA,YACT;AACA,mBAAO,YAAY,YAAY;AAC/B,uBAAW,MAAM;AACf,kBAAI,IAAI,UAAU;AAChB,2BAAW;AAAA,cACb;AAAA,YACF,CAAC;AACD,gBAAI,iBAAiB,SAAS,UAAU;AACxC,gBAAI,iBAAiB,QAAQ,UAAU;AAAA,UACzC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AACA,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AACA,MAAI,eAAe;AACjB,YAAQ,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EAC1F,OAAO;AACL,YAAQ,KAAK,aAAa,kBAAkB,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EACpE;AACA,MAAI,KAAK,aAAa;AACpB,YAAQ,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EAC1F;AACA,UAAQ,OAAO,QAAQ,cAAc;AACrC,SAAO,EAAE,UAAU,MAAM,aAAa,OAAO,QAAQ;AACvD,GAAG,aAAa;AAChB,IAAI,cAA8B,OAAO,OAAO,QAAQ,OAAO,YAAY;AA7G3E;AA8GE,QAAM,gBAAgB,QAAQ,iBAAiB,UAAS,sBAAW,MAAX,mBAAc,cAAd,mBAAyB,UAAU;AAC3F,QAAM,UAAU,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,SAAS,QAAQ,cAAc,EAAE;AAChG,QAAM,QAAQ,MAAM,WAAW,SAAS,aAAa,eAAe,KAAK,GAAG,WAAW,CAAC,GAAG;AAAA,IACzF;AAAA,IACA,OAAO,QAAQ,WAAS,sBAAW,MAAX,mBAAc,cAAd,mBAAyB;AAAA,IACjD,OAAO,QAAQ;AAAA,IACf,kBAAkB,CAAC,CAAC,QAAQ,QAAQ,CAAC,CAAC,QAAQ;AAAA,EAChD,CAAC;AACD,MAAI,OAAO,MAAM,QAAQ;AACzB,QAAM,cAAc,QAAQ,UAAU;AACtC,MAAI,UAAS,sBAAW,MAAX,mBAAc,cAAd,mBAAyB,UAAU,GAAG;AACjD,UAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,UAAM,KAAK,eAAO,KAAK;AACvB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AACA,MAAI,eAAe;AACjB,YAAQ,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EAC1F,OAAO;AACL,YAAQ,KAAK,aAAa,kBAAkB,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EACpE;AACA,MAAI,QAAQ,aAAa;AACvB,YAAQ,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AAAA,EAC1F;AACA,UAAQ,OAAO,QAAQ,cAAc;AACrC,SAAO,EAAE,UAAU,QAAQ,MAAM,aAAa,OAAO,QAAQ;AAC/D,GAAG,aAAa;AAChB,IAAI,mBAAmC,OAAO,CAAC,MAAM,YAAY;AAC/D,QAAM,OAAO,QAAQ,KAAK,EAAE,QAAQ;AACpC,OAAK,QAAQ,KAAK;AAClB,OAAK,SAAS,KAAK;AACrB,GAAG,kBAAkB;AACrB,IAAI,iBAAiC,OAAO,CAAC,MAAM,WAAW,KAAK,SAAS,cAAc,eAAe,UAAU,MAAM,KAAK,aAAa,OAAO,SAAS,KAAK,gBAAgB;AAChL,SAAS,qBAAqB,QAAQ;AACpC,QAAM,eAAe,OAAO,IAAI,CAAC6B,IAAGC,OAAM,GAAGA,OAAM,IAAI,MAAM,GAAG,GAAGD,GAAE,CAAC,IAAIA,GAAE,CAAC,EAAE;AAC/E,eAAa,KAAK,GAAG;AACrB,SAAO,aAAa,KAAK,GAAG;AAC9B;AACA,OAAO,sBAAsB,sBAAsB;AACnD,SAAS,2BAA2B,IAAI,IAAI,IAAI,IAAI,WAAW,WAAW;AACxE,QAAM,SAAS,CAAC;AAChB,QAAM,QAAQ;AACd,QAAM,SAAS,KAAK;AACpB,QAAM,SAAS,KAAK;AACpB,QAAM,cAAc,SAAS;AAC7B,QAAM,YAAY,IAAI,KAAK,KAAK;AAChC,QAAM,OAAO,KAAK,SAAS;AAC3B,WAASC,KAAI,GAAGA,MAAK,OAAOA,MAAK;AAC/B,UAAMC,KAAID,KAAI;AACd,UAAME,KAAI,KAAKD,KAAI;AACnB,UAAME,KAAI,OAAO,YAAY,KAAK,IAAI,aAAaD,KAAI,GAAG;AAC1D,WAAO,KAAK,EAAE,GAAAA,IAAG,GAAAC,GAAE,CAAC;AAAA,EACtB;AACA,SAAO;AACT;AACA,OAAO,4BAA4B,4BAA4B;AAC/D,SAAS,qBAAqB,SAAS,SAAS,QAAQ,WAAW,YAAY,UAAU;AACvF,QAAM,SAAS,CAAC;AAChB,QAAM,gBAAgB,aAAa,KAAK,KAAK;AAC7C,QAAM,cAAc,WAAW,KAAK,KAAK;AACzC,QAAM,aAAa,cAAc;AACjC,QAAM,YAAY,cAAc,YAAY;AAC5C,WAASH,KAAI,GAAGA,KAAI,WAAWA,MAAK;AAClC,UAAM,QAAQ,gBAAgBA,KAAI;AAClC,UAAME,KAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,UAAMC,KAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,WAAO,KAAK,EAAE,GAAG,CAACD,IAAG,GAAG,CAACC,GAAE,CAAC;AAAA,EAC9B;AACA,SAAO;AACT;AACA,OAAO,sBAAsB,sBAAsB;AAOnD,IAAI,gBAAgC,OAAO,CAAC,MAAM,UAAU;AAC1D,MAAID,KAAI,KAAK;AACb,MAAIC,KAAI,KAAK;AACb,MAAI,KAAK,MAAM,IAAID;AACnB,MAAI,KAAK,MAAM,IAAIC;AACnB,MAAIC,KAAI,KAAK,QAAQ;AACrB,MAAIC,KAAI,KAAK,SAAS;AACtB,MAAI,IAAI;AACR,MAAI,KAAK,IAAI,EAAE,IAAID,KAAI,KAAK,IAAI,EAAE,IAAIC,IAAG;AACvC,QAAI,KAAK,GAAG;AACV,MAAAA,KAAI,CAACA;AAAA,IACP;AACA,SAAK,OAAO,IAAI,IAAIA,KAAI,KAAK;AAC7B,SAAKA;AAAA,EACP,OAAO;AACL,QAAI,KAAK,GAAG;AACV,MAAAD,KAAI,CAACA;AAAA,IACP;AACA,SAAKA;AACL,SAAK,OAAO,IAAI,IAAIA,KAAI,KAAK;AAAA,EAC/B;AACA,SAAO,EAAE,GAAGF,KAAI,IAAI,GAAGC,KAAI,GAAG;AAChC,GAAG,eAAe;AAClB,IAAI,yBAAyB;AAI7B,SAAS,WAAW,KAAK,SAAS;AAChC,MAAI,SAAS;AACX,QAAI,KAAK,SAAS,OAAO;AAAA,EAC3B;AACF;AACA,OAAO,YAAY,YAAY;AAC/B,eAAe,aAAa,MAAM;AAChC,QAAM,KAAK,eAAQ,SAAS,gBAAgB,8BAA8B,eAAe,CAAC;AAC1F,QAAM,MAAM,GAAG,OAAO,WAAW;AACjC,MAAI,QAAQ,KAAK;AACjB,MAAI,KAAK,SAAS,SAAS,KAAK,KAAK,GAAG;AACtC,YAAQ,MAAM,YAAY,KAAK,MAAM,QAAQ,eAAe,gBAAgB,IAAI,GAAG,WAAW,CAAC;AAAA,EACjG;AACA,QAAM,aAAa,KAAK,SAAS,cAAc;AAC/C,MAAI;AAAA,IACF,kBAAkB,aAAa,QAAQ,KAAK,aAAa,YAAY,KAAK,aAAa,MAAM;AAAA,IAC7F,MAAM,QAAQ;AAAA,EAChB;AACA,aAAW,KAAK,KAAK,UAAU;AAC/B,MAAI,MAAM,WAAW,cAAc;AACnC,MAAI,MAAM,iBAAiB,KAAK;AAChC,MAAI,MAAM,eAAe,QAAQ;AACjC,MAAI,KAAK,SAAS,8BAA8B;AAChD,SAAO,GAAG,KAAK;AACjB;AACA,OAAO,cAAc,cAAc;AACnC,IAAI,cAA8B,OAAO,OAAO,aAAa,OAAO,SAAS,WAAW;AACtF,MAAI,aAAa,eAAe;AAChC,MAAI,OAAO,eAAe,UAAU;AAClC,iBAAa,WAAW,CAAC;AAAA,EAC3B;AACA,MAAI,SAAS,WAAW,EAAE,UAAU,UAAU,GAAG;AAC/C,iBAAa,WAAW,QAAQ,WAAW,QAAQ;AACnD,QAAI,KAAK,eAAe,UAAU;AAClC,UAAM,OAAO;AAAA,MACX;AAAA,MACA,OAAO,eAAe,UAAU,EAAE;AAAA,QAChC;AAAA,QACA,CAACG,OAAM,aAAaA,GAAE,QAAQ,KAAK,GAAG,CAAC;AAAA,MACzC;AAAA,MACA,YAAY,QAAQ,MAAM,QAAQ,SAAS,QAAQ,IAAI;AAAA,IACzD;AACA,QAAI,aAAa,MAAM,aAAa,IAAI;AACxC,WAAO;AAAA,EACT,OAAO;AACL,UAAM,WAAW,SAAS,gBAAgB,8BAA8B,MAAM;AAC9E,aAAS,aAAa,SAAS,MAAM,QAAQ,UAAU,OAAO,CAAC;AAC/D,QAAI,OAAO,CAAC;AACZ,QAAI,OAAO,eAAe,UAAU;AAClC,aAAO,WAAW,MAAM,qBAAqB;AAAA,IAC/C,WAAW,MAAM,QAAQ,UAAU,GAAG;AACpC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,CAAC;AAAA,IACV;AACA,eAAW,OAAO,MAAM;AACtB,YAAM,QAAQ,SAAS,gBAAgB,8BAA8B,OAAO;AAC5E,YAAM,eAAe,wCAAwC,aAAa,UAAU;AACpF,YAAM,aAAa,MAAM,KAAK;AAC9B,YAAM,aAAa,KAAK,GAAG;AAC3B,UAAI,SAAS;AACX,cAAM,aAAa,SAAS,WAAW;AAAA,MACzC,OAAO;AACL,cAAM,aAAa,SAAS,KAAK;AAAA,MACnC;AACA,YAAM,cAAc,IAAI,KAAK;AAC7B,eAAS,YAAY,KAAK;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AACF,GAAG,aAAa;AAChB,IAAI,sBAAsB;AAG1B,IAAI,yBAAyC,OAAO,CAACJ,IAAGC,IAAG,YAAY,aAAa,WAAW;AAAA,EAC7F;AAAA,EACAD,KAAI;AAAA,EACJC;AAAA;AAAA,EAEA;AAAA,EACAD,KAAI,aAAa;AAAA;AAAA,EAEjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACAA,KAAI;AAAA,EACJC,KAAI;AAAA;AAAA,EAEJ;AAAA,EACAA,KAAI,cAAc;AAAA;AAAA,EAElB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACAD,KAAI,aAAa;AAAA,EACjBC,KAAI;AAAA;AAAA,EAEJ;AAAA,EACAD,KAAI;AAAA;AAAA,EAEJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACAA;AAAA,EACAC,KAAI,cAAc;AAAA;AAAA,EAElB;AAAA,EACAA,KAAI;AAAA;AAAA,EAEJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACAD,KAAI;AAAA,EACJC;AAAA;AAAA,EAEA;AAAA;AAEF,EAAE,KAAK,GAAG,GAAG,wBAAwB;AAGrC,IAAI,iBAAiC,OAAO,CAAC,UAAU;AACrD,QAAM,EAAE,cAAc,IAAI,WAAW;AACrC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,cAAc;AAAA;AAAA,IAEd,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF,GAAG,gBAAgB;AACnB,IAAI,gBAAgC,OAAO,CAAC,SAAS;AACnD,QAAM,YAAY,WAAW,CAAC,GAAG,KAAK,qBAAqB,CAAC,GAAG,GAAG,KAAK,aAAa,CAAC,CAAC,CAAC;AACvF,SAAO,EAAE,WAAW,aAAa,CAAC,GAAG,SAAS,EAAE;AAClD,GAAG,eAAe;AAClB,IAAI,aAA6B,OAAO,CAAC,WAAW;AAClD,QAAM,WAA2B,oBAAI,IAAI;AACzC,SAAO,QAAQ,CAAC,UAAU;AACxB,UAAM,CAAC,KAAK,KAAK,IAAI,MAAM,MAAM,GAAG;AACpC,aAAS,IAAI,IAAI,KAAK,GAAG,+BAAO,MAAM;AAAA,EACxC,CAAC;AACD,SAAO;AACT,GAAG,YAAY;AACf,IAAI,eAA+B,OAAO,CAAC,QAAQ;AACjD,SAAO,QAAQ,WAAW,QAAQ,eAAe,QAAQ,iBAAiB,QAAQ,iBAAiB,QAAQ,gBAAgB,QAAQ,qBAAqB,QAAQ,gBAAgB,QAAQ,oBAAoB,QAAQ,iBAAiB,QAAQ,oBAAoB,QAAQ,kBAAkB,QAAQ,iBAAiB,QAAQ,mBAAmB,QAAQ,iBAAiB,QAAQ,eAAe,QAAQ,gBAAgB,QAAQ,mBAAmB,QAAQ;AAC5b,GAAG,cAAc;AACjB,IAAI,gBAAgC,OAAO,CAAC,SAAS;AACnD,QAAM,EAAE,YAAY,IAAI,cAAc,IAAI;AAC1C,QAAM,cAAc,CAAC;AACrB,QAAM,aAAa,CAAC;AACpB,QAAM,eAAe,CAAC;AACtB,QAAM,mBAAmB,CAAC;AAC1B,cAAY,QAAQ,CAAC,UAAU;AAC7B,UAAM,MAAM,MAAM,CAAC;AACnB,QAAI,aAAa,GAAG,GAAG;AACrB,kBAAY,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;AAAA,IAClD,OAAO;AACL,iBAAW,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;AAC/C,UAAI,IAAI,SAAS,QAAQ,GAAG;AAC1B,qBAAa,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;AAAA,MACnD;AACA,UAAI,QAAQ,QAAQ;AAClB,yBAAiB,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;AAAA,MACvD;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL,aAAa,YAAY,KAAK,GAAG;AAAA,IACjC,YAAY,WAAW,KAAK,GAAG;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF,GAAG,eAAe;AAClB,IAAI,oBAAoC,OAAO,CAAC,MAAM,YAAY;AAnZlE;AAoZE,QAAM,EAAE,gBAAgB,cAAc,IAAI,WAAW;AACrD,QAAM,EAAE,YAAY,QAAQ,IAAI;AAChC,QAAM,EAAE,UAAU,IAAI,cAAc,IAAI;AACxC,QAAM,SAAS,OAAO;AAAA,IACpB;AAAA,MACE,WAAW;AAAA,MACX,MAAM,UAAU,IAAI,MAAM,KAAK;AAAA,MAC/B,WAAW;AAAA;AAAA,MAEX,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,QAAQ,UAAU,IAAI,QAAQ,KAAK;AAAA,MACnC,MAAM;AAAA,MACN,eAAa,eAAU,IAAI,cAAc,MAA5B,mBAA+B,QAAQ,MAAM,QAAO;AAAA,MACjE,cAAc,CAAC,GAAG,CAAC;AAAA,IACrB;AAAA,IACA;AAAA,EACF;AACA,SAAO;AACT,GAAG,mBAAmB;AAGtB,IAAI,OAAuB,OAAO,OAAO,QAAQ,SAAS;AACxD,MAAI,KAAK,+BAA+B,KAAK,IAAI,IAAI;AACrD,QAAM,aAAa,WAAW;AAC9B,QAAM,EAAE,gBAAgB,cAAc,IAAI;AAC1C,QAAM,EAAE,YAAY,cAAc,IAAI;AACtC,QAAM,EAAE,aAAa,YAAY,cAAc,iBAAiB,IAAI,cAAc,IAAI;AACtF,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,aAAa,KAAK,UAAU,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,aAAa,KAAK,IAAI;AAC/H,QAAM,gBAAgB,SAAS,WAAW,UAAU,UAAU;AAC9D,QAAM,UAAU,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,gBAAgB;AACnE,QAAM,QAAQ,MAAM,WAAW,SAAS,KAAK,OAAO;AAAA,IAClD,OAAO,KAAK;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,OAAO,MAAM,QAAQ;AACzB,MAAI,SAAS,WAAW,UAAU,UAAU,GAAG;AAC7C,UAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,UAAM,KAAK,eAAQ,KAAK;AACxB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AACA,QAAM,QAAQ,KAAK,SAAS,KAAK,QAAQ,KAAK,UAAU,KAAK,QAAQ,KAAK,UAAU,KAAK;AACzF,MAAI,KAAK,SAAS,KAAK,QAAQ,KAAK,SAAS;AAC3C,SAAK,QAAQ,QAAQ,KAAK,SAAS,IAAI,KAAK;AAAA,EAC9C,OAAO;AACL,SAAK,OAAO,CAAC,KAAK;AAAA,EACpB;AACA,QAAM,SAAS,KAAK;AACpB,QAAMD,KAAI,KAAK,IAAI,QAAQ;AAC3B,QAAMC,KAAI,KAAK,IAAI,SAAS;AAC5B,MAAI,MAAM,SAAS,MAAM,KAAK,UAAU,IAAI,CAAC;AAC7C,MAAI;AACJ,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAM,IAAI,QAAQ;AAC7B,UAAM,UAAU,kBAAkB,MAAM;AAAA,MACtC,WAAW;AAAA,MACX,MAAM;AAAA;AAAA,MAEN,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AACD,UAAM,YAAY,GAAG,KAAK,uBAAuBD,IAAGC,IAAG,OAAO,QAAQ,CAAC,GAAG,OAAO;AACjF,YAAQ,SAAS,OAAO,MAAM;AAC5B,UAAI,MAAM,yBAAyB,SAAS;AAC5C,aAAO;AAAA,IACT,GAAG,cAAc;AACjB,UAAM,OAAO,mBAAmB,EAAE,KAAK,SAAS,aAAa,KAAK,GAAG,CAAC;AACtE,UAAM,OAAO,MAAM,EAAE,KAAK,SAAS,iBAAiB,KAAK,GAAG,EAAE,QAAQ,QAAQ,QAAQ,CAAC;AAAA,EACzF,OAAO;AACL,YAAQ,SAAS,OAAO,QAAQ,cAAc;AAC9C,UAAM,KAAK,SAAS,UAAU,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,KAAKD,EAAC,EAAE,KAAK,KAAKC,EAAC,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,MAAM;AAAA,EAC9I;AACA,QAAM,EAAE,uBAAuB,IAAI,wBAAwB,UAAU;AACrE,UAAQ;AAAA,IACN;AAAA;AAAA,IAEA,aAAa,KAAK,IAAI,KAAK,QAAQ,CAAC,KAAK,KAAK,IAAI,KAAK,SAAS,IAAI,sBAAsB;AAAA,EAC5F;AACA,MAAI,aAAa;AACf,UAAM,OAAO,QAAQ,OAAO,MAAM;AAClC,QAAI,MAAM;AACR,WAAK,KAAK,SAAS,WAAW;AAAA,IAChC;AAAA,EACF;AACA,QAAM,UAAU,MAAM,KAAK,EAAE,QAAQ;AACrC,OAAK,UAAU;AACf,OAAK,QAAQ,QAAQ;AACrB,OAAK,SAAS,QAAQ;AACtB,OAAK,UAAU,KAAK,SAAS,KAAK,UAAU;AAC5C,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,uBAAuB,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO,EAAE,SAAS,UAAU,WAAW,KAAK;AAC9C,GAAG,MAAM;AACT,IAAI,YAA4B,OAAO,CAAC,QAAQ,SAAS;AACvD,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,cAAc,EAAE,KAAK,MAAM,KAAK,EAAE;AACpF,QAAM,QAAQ,SAAS,OAAO,QAAQ,cAAc;AACpD,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,cAAc,UAAU;AAC9B,QAAM,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,KAAK,KAAK,IAAI,KAAK,QAAQ,IAAI,WAAW,EAAE,KAAK,KAAK,KAAK,IAAI,KAAK,SAAS,IAAI,WAAW,EAAE,KAAK,SAAS,KAAK,QAAQ,OAAO,EAAE,KAAK,UAAU,KAAK,SAAS,OAAO,EAAE,KAAK,QAAQ,MAAM;AAC9O,QAAM,UAAU,MAAM,KAAK,EAAE,QAAQ;AACrC,OAAK,QAAQ,QAAQ;AACrB,OAAK,SAAS,QAAQ;AACtB,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,uBAAuB,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO,EAAE,SAAS,UAAU,WAAW,EAAE,OAAO,GAAG,QAAQ,EAAE,EAAE;AACjE,GAAG,WAAW;AACd,IAAI,mBAAmC,OAAO,OAAO,QAAQ,SAAS;AACpE,QAAM,aAAa,WAAW;AAC9B,QAAM,EAAE,gBAAgB,cAAc,IAAI;AAC1C,QAAM,EAAE,eAAe,qBAAqB,0BAA0B,WAAW,IAAI;AACrF,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,KAAK,UAAU,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,WAAW,KAAK,EAAE,EAAE,KAAK,aAAa,KAAK,IAAI;AAC3I,QAAM,aAAa,SAAS,OAAO,KAAK,cAAc;AACtD,QAAM,QAAQ,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AAChE,MAAI,YAAY,SAAS,OAAO,MAAM;AACtC,QAAM,QAAQ,MAAM,KAAK,EAAE,YAAY,MAAM,oBAAoB,KAAK,OAAO,KAAK,YAAY,QAAQ,IAAI,CAAC;AAC3G,MAAI,OAAO,MAAM,QAAQ;AACzB,MAAI,SAAS,WAAW,UAAU,UAAU,GAAG;AAC7C,UAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,UAAM,KAAK,eAAQ,KAAK;AACxB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AACA,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,cAAc,UAAU;AAC9B,QAAM,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,UAAU,KAAK,QAAQ,KAAK,UAAU,KAAK,SAAS;AACnG,MAAI,KAAK,SAAS,KAAK,QAAQ,KAAK,SAAS;AAC3C,SAAK,QAAQ,QAAQ,KAAK,SAAS,IAAI,KAAK;AAAA,EAC9C,OAAO;AACL,SAAK,OAAO,CAAC,KAAK;AAAA,EACpB;AACA,QAAM,SAAS,KAAK,SAAS;AAC7B,QAAM,cAAc,KAAK,SAAS,UAAU,KAAK,SAAS;AAC1D,QAAMD,KAAI,KAAK,IAAI,QAAQ;AAC3B,QAAMC,KAAI,KAAK,IAAI,SAAS;AAC5B,OAAK,QAAQ;AACb,QAAM,SAAS,KAAK,IAAI,KAAK,SAAS,IAAI,cAAc,KAAK,SAAS;AACtE,MAAI;AACJ,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,QAAQ,KAAK,WAAW,SAAS,0BAA0B;AACjE,UAAM,KAAK,GAAM,IAAI,QAAQ;AAC7B,UAAM,iBAAiB,KAAK,MAAM,KAAK,KAAK,GAAG,KAAK,uBAAuBD,IAAGC,IAAG,OAAO,QAAQ,EAAE,GAAG;AAAA,MACnG,WAAW;AAAA,MACX,MAAM;AAAA,MACN,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC,IAAI,GAAG,UAAUD,IAAGC,IAAG,OAAO,QAAQ,EAAE,MAAM,cAAc,CAAC;AAC9D,YAAQ,SAAS,OAAO,MAAM,gBAAgB,cAAc;AAC5D,UAAM,iBAAiB,GAAG,UAAUD,IAAG,QAAQ,OAAO,aAAa;AAAA,MACjE,MAAM,QAAQ,gBAAgB;AAAA,MAC9B,WAAW,QAAQ,YAAY;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AACD,YAAQ,SAAS,OAAO,MAAM,gBAAgB,cAAc;AAC5D,gBAAY,SAAS,OAAO,MAAM,cAAc;AAAA,EAClD,OAAO;AACL,YAAQ,WAAW,OAAO,QAAQ,cAAc;AAChD,UAAM,iBAAiB;AACvB,UAAM,KAAK,SAAS,cAAc,EAAE,KAAK,KAAKA,EAAC,EAAE,KAAK,KAAKC,EAAC,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,aAAa,KAAK,IAAI;AACrI,cAAU,KAAK,SAAS,OAAO,EAAE,KAAK,KAAKD,EAAC,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,WAAW;AAAA,EACjH;AACA,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,KAAK,IAAI,KAAK,QAAQ,CAAC,KAAKC,KAAI,KAAK,SAAS,WAAW,UAAU,UAAU,IAAI,IAAI,EAAE;AAAA,EACtG;AACA,QAAM,UAAU,MAAM,KAAK,EAAE,QAAQ;AACrC,OAAK,SAAS,QAAQ;AACtB,OAAK,UAAU;AACf,OAAK,UAAU,KAAK,SAAS,KAAK,UAAU;AAC5C,OAAK,YAAY;AACjB,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,uBAAuB,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO,EAAE,SAAS,UAAU,WAAW,KAAK;AAC9C,GAAG,kBAAkB;AACrB,IAAI,gBAAgC,OAAO,OAAO,QAAQ,SAAS;AACjE,MAAI,KAAK,+BAA+B,KAAK,IAAI,IAAI;AACrD,QAAM,aAAa,WAAW;AAC9B,QAAM,EAAE,gBAAgB,cAAc,IAAI;AAC1C,QAAM,EAAE,YAAY,cAAc,IAAI;AACtC,QAAM,EAAE,aAAa,YAAY,cAAc,iBAAiB,IAAI,cAAc,IAAI;AACtF,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,aAAa,KAAK,UAAU,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,aAAa,KAAK,IAAI;AAC/H,QAAM,gBAAgB,SAAS,WAAW,UAAU,UAAU;AAC9D,QAAM,UAAU,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,gBAAgB;AACnE,QAAM,QAAQ,MAAM,WAAW,SAAS,KAAK,OAAO;AAAA,IAClD,OAAO,KAAK;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,IACR,OAAO,KAAK;AAAA,EACd,CAAC;AACD,MAAI,OAAO,MAAM,QAAQ;AACzB,MAAI,SAAS,WAAW,UAAU,UAAU,GAAG;AAC7C,UAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,UAAM,KAAK,eAAQ,KAAK;AACxB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AACA,QAAM,QAAQ,KAAK,SAAS,KAAK,QAAQ,KAAK,UAAU,KAAK,QAAQ,KAAK,UAAU,KAAK;AACzF,MAAI,KAAK,SAAS,KAAK,QAAQ,KAAK,SAAS;AAC3C,SAAK,QAAQ,QAAQ,KAAK,SAAS,IAAI,KAAK;AAAA,EAC9C,OAAO;AACL,SAAK,OAAO,CAAC,KAAK;AAAA,EACpB;AACA,QAAM,SAAS,KAAK;AACpB,QAAMD,KAAI,KAAK,IAAI,QAAQ;AAC3B,QAAMC,KAAI,KAAK,IAAI,SAAS;AAC5B,MAAI,MAAM,SAAS,MAAM,KAAK,UAAU,IAAI,CAAC;AAC7C,MAAI;AACJ,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAM,IAAI,QAAQ;AAC7B,UAAM,UAAU,kBAAkB,MAAM;AAAA,MACtC,WAAW;AAAA,MACX,MAAM;AAAA;AAAA,MAEN,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AACD,UAAM,YAAY,GAAG,KAAK,uBAAuBD,IAAGC,IAAG,OAAO,QAAQ,KAAK,EAAE,GAAG,OAAO;AACvF,YAAQ,SAAS,OAAO,MAAM;AAC5B,UAAI,MAAM,yBAAyB,SAAS;AAC5C,aAAO;AAAA,IACT,GAAG,cAAc;AACjB,UAAM,OAAO,mBAAmB,EAAE,KAAK,SAAS,aAAa,KAAK,GAAG,CAAC;AACtE,UAAM,OAAO,MAAM,EAAE,KAAK,SAAS,iBAAiB,KAAK,GAAG,EAAE,QAAQ,QAAQ,QAAQ,CAAC;AAAA,EACzF,OAAO;AACL,YAAQ,SAAS,OAAO,QAAQ,cAAc;AAC9C,UAAM,KAAK,SAAS,UAAU,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,KAAKD,EAAC,EAAE,KAAK,KAAKC,EAAC,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,MAAM;AAAA,EAC9I;AACA,QAAM,EAAE,uBAAuB,IAAI,wBAAwB,UAAU;AACrE,UAAQ;AAAA,IACN;AAAA;AAAA,IAEA,aAAa,KAAK,IAAI,KAAK,QAAQ,CAAC,KAAK,KAAK,IAAI,KAAK,SAAS,IAAI,sBAAsB;AAAA,EAC5F;AACA,MAAI,aAAa;AACf,UAAM,OAAO,QAAQ,OAAO,MAAM;AAClC,QAAI,MAAM;AACR,WAAK,KAAK,SAAS,WAAW;AAAA,IAChC;AAAA,EACF;AACA,QAAM,UAAU,MAAM,KAAK,EAAE,QAAQ;AACrC,OAAK,UAAU;AACf,OAAK,QAAQ,QAAQ;AACrB,OAAK,SAAS,QAAQ;AACtB,OAAK,UAAU,KAAK,SAAS,KAAK,UAAU;AAC5C,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,uBAAuB,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO,EAAE,SAAS,UAAU,WAAW,KAAK;AAC9C,GAAG,eAAe;AAClB,IAAI,UAA0B,OAAO,CAAC,QAAQ,SAAS;AACrD,QAAM,aAAa,WAAW;AAC9B,QAAM,EAAE,gBAAgB,cAAc,IAAI;AAC1C,QAAM,EAAE,WAAW,IAAI;AACvB,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,KAAK,UAAU,EAAE,KAAK,MAAM,KAAK,EAAE,EAAE,KAAK,aAAa,KAAK,IAAI;AAClH,QAAM,aAAa,SAAS,OAAO,KAAK,cAAc;AACtD,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,QAAQ,KAAK,QAAQ;AAC3B,OAAK,OAAO,CAAC,KAAK;AAClB,QAAM,SAAS,KAAK,SAAS;AAC7B,QAAMD,KAAI,KAAK,IAAI,QAAQ;AAC3B,QAAMC,KAAI,KAAK,IAAI,SAAS;AAC5B,OAAK,QAAQ;AACb,MAAI;AACJ,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAM,IAAI,QAAQ;AAC7B,UAAM,iBAAiB,GAAG,UAAUD,IAAGC,IAAG,OAAO,QAAQ;AAAA,MACvD,MAAM;AAAA,MACN,WAAW;AAAA,MACX,gBAAgB,CAAC,CAAC;AAAA,MAClB,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AACD,YAAQ,SAAS,OAAO,MAAM,gBAAgB,cAAc;AAAA,EAC9D,OAAO;AACL,YAAQ,WAAW,OAAO,QAAQ,cAAc;AAChD,UAAM,iBAAiB;AACvB,UAAM,KAAK,SAAS,cAAc,EAAE,KAAK,KAAKD,EAAC,EAAE,KAAK,KAAKC,EAAC,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,aAAa,KAAK,IAAI;AAAA,EACvI;AACA,QAAM,UAAU,MAAM,KAAK,EAAE,QAAQ;AACrC,OAAK,SAAS,QAAQ;AACtB,OAAK,UAAU;AACf,OAAK,UAAU;AACf,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,uBAAuB,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO,EAAE,SAAS,UAAU,WAAW,CAAC,EAAE;AAC5C,GAAG,SAAS;AACZ,IAAI,aAAa;AACjB,IAAI,SAAS;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,eAA+B,oBAAI,IAAI;AAC3C,IAAI,gBAAgC,OAAO,OAAO,MAAM,SAAS;AAC/D,QAAM,QAAQ,KAAK,SAAS;AAC5B,QAAM,UAAU,MAAM,OAAO,KAAK,EAAE,MAAM,IAAI;AAC9C,eAAa,IAAI,KAAK,IAAI,OAAO;AACjC,SAAO;AACT,GAAG,eAAe;AAClB,IAAI,QAAwB,OAAO,MAAM;AACvC,iBAA+B,oBAAI,IAAI;AACzC,GAAG,OAAO;AAGV,SAAS,cAAc,MAAM,OAAO;AAClC,SAAO,KAAK,UAAU,KAAK;AAC7B;AACA,OAAO,eAAe,eAAe;AACrC,IAAI,yBAAyB;AAG7B,SAAS,iBAAiB,MAAM,IAAI,IAAI,OAAO;AAC7C,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK,MAAM;AACpB,MAAI,KAAK,KAAK,MAAM;AACpB,MAAI,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AACzD,MAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG;AACpC,MAAI,MAAM,IAAI,IAAI;AAChB,SAAK,CAAC;AAAA,EACR;AACA,MAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG;AACpC,MAAI,MAAM,IAAI,IAAI;AAChB,SAAK,CAAC;AAAA,EACR;AACA,SAAO,EAAE,GAAG,KAAK,IAAI,GAAG,KAAK,GAAG;AAClC;AACA,OAAO,kBAAkB,kBAAkB;AAC3C,IAAI,4BAA4B;AAGhC,SAAS,gBAAgB,MAAM,IAAI,OAAO;AACxC,SAAO,0BAA0B,MAAM,IAAI,IAAI,KAAK;AACtD;AACA,OAAO,iBAAiB,iBAAiB;AACzC,IAAI,2BAA2B;AAG/B,SAAS,cAAc,IAAI,IAAI,IAAI,IAAI;AACrC,MAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,MAAI,IAAI,IAAI,IAAI;AAChB,MAAI,OAAO,QAAQ;AACnB,MAAID,IAAGC;AACP,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG;AAC7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAC7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAC7B,MAAI,OAAO,KAAK,OAAO,KAAK,SAAS,IAAI,EAAE,GAAG;AAC5C;AAAA,EACF;AACA,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG;AACf,OAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG;AAC7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAC7B,OAAK,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAC7B,MAAI,OAAO,KAAK,OAAO,KAAK,SAAS,IAAI,EAAE,GAAG;AAC5C;AAAA,EACF;AACA,UAAQ,KAAK,KAAK,KAAK;AACvB,MAAI,UAAU,GAAG;AACf;AAAA,EACF;AACA,WAAS,KAAK,IAAI,QAAQ,CAAC;AAC3B,QAAM,KAAK,KAAK,KAAK;AACrB,EAAAD,KAAI,MAAM,KAAK,MAAM,UAAU,SAAS,MAAM,UAAU;AACxD,QAAM,KAAK,KAAK,KAAK;AACrB,EAAAC,KAAI,MAAM,KAAK,MAAM,UAAU,SAAS,MAAM,UAAU;AACxD,SAAO,EAAE,GAAAD,IAAG,GAAAC,GAAE;AAChB;AACA,OAAO,eAAe,eAAe;AACrC,SAAS,SAAS,IAAI,IAAI;AACxB,SAAO,KAAK,KAAK;AACnB;AACA,OAAO,UAAU,UAAU;AAC3B,IAAI,yBAAyB;AAG7B,SAAS,iBAAiB,MAAM,YAAY,OAAO;AACjD,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK;AACd,MAAI,gBAAgB,CAAC;AACrB,MAAI,OAAO,OAAO;AAClB,MAAI,OAAO,OAAO;AAClB,MAAI,OAAO,WAAW,YAAY,YAAY;AAC5C,eAAW,QAAQ,SAAS,OAAO;AACjC,aAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,aAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAAA,IAC/B,CAAC;AAAA,EACH,OAAO;AACL,WAAO,KAAK,IAAI,MAAM,WAAW,CAAC;AAClC,WAAO,KAAK,IAAI,MAAM,WAAW,CAAC;AAAA,EACpC;AACA,MAAI,OAAO,KAAK,KAAK,QAAQ,IAAI;AACjC,MAAI,MAAM,KAAK,KAAK,SAAS,IAAI;AACjC,WAASH,KAAI,GAAGA,KAAI,WAAW,QAAQA,MAAK;AAC1C,QAAI,KAAK,WAAWA,EAAC;AACrB,QAAI,KAAK,WAAWA,KAAI,WAAW,SAAS,IAAIA,KAAI,IAAI,CAAC;AACzD,QAAI,YAAY;AAAA,MACd;AAAA,MACA;AAAA,MACA,EAAE,GAAG,OAAO,GAAG,GAAG,GAAG,MAAM,GAAG,EAAE;AAAA,MAChC,EAAE,GAAG,OAAO,GAAG,GAAG,GAAG,MAAM,GAAG,EAAE;AAAA,IAClC;AACA,QAAI,WAAW;AACb,oBAAc,KAAK,SAAS;AAAA,IAC9B;AAAA,EACF;AACA,MAAI,CAAC,cAAc,QAAQ;AACzB,WAAO;AAAA,EACT;AACA,MAAI,cAAc,SAAS,GAAG;AAC5B,kBAAc,KAAK,SAASD,IAAGQ,IAAG;AAChC,UAAI,MAAMR,GAAE,IAAI,MAAM;AACtB,UAAI,MAAMA,GAAE,IAAI,MAAM;AACtB,UAAI,QAAQ,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAC3C,UAAI,MAAMQ,GAAE,IAAI,MAAM;AACtB,UAAI,MAAMA,GAAE,IAAI,MAAM;AACtB,UAAI,QAAQ,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAC3C,aAAO,QAAQ,QAAQ,KAAK,UAAU,QAAQ,IAAI;AAAA,IACpD,CAAC;AAAA,EACH;AACA,SAAO,cAAc,CAAC;AACxB;AACA,OAAO,kBAAkB,kBAAkB;AAC3C,IAAI,4BAA4B;AAGhC,IAAI,oBAAoB;AAAA,EACtB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,MAAM;AACR;AAIA,SAAS,OAAO,QAAQ,MAAM;AAC5B,QAAM,EAAE,YAAY,IAAI,cAAc,IAAI;AAC1C,OAAK,aAAa;AAClB,QAAM,UAAU,eAAe,IAAI;AACnC,MAAI,aAAa;AACjB,MAAI,CAAC,SAAS;AACZ,iBAAa;AAAA,EACf;AACA,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAC9F,QAAM,SAAS;AACf,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAO,IAAI,QAAQ;AAC9B,QAAM,UAAU,kBAAkB,MAAM,EAAE,MAAM,SAAS,QAAQ,QAAQ,WAAW,QAAQ,CAAC;AAC7F,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,YAAY,GAAG,OAAO,GAAG,GAAG,SAAS,GAAG,OAAO;AACrD,QAAM,aAAa,SAAS,OAAO,MAAM,WAAW,cAAc;AAClE,aAAW,KAAK,SAAS,QAAQ,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AAC/E,mBAAiB,MAAM,UAAU;AACjC,OAAK,YAAY,SAAS,OAAO;AAC/B,QAAI,KAAK,oBAAoB,MAAM,QAAQ,KAAK;AAChD,WAAO,kBAAkB,OAAO,MAAM,QAAQ,KAAK;AAAA,EACrD;AACA,SAAO;AACT;AACA,OAAO,QAAQ,QAAQ;AAIvB,SAAS,kBAAkB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,WAAW;AAC5D,QAAM,YAAY;AAClB,QAAM,QAAQ,KAAK,MAAM;AACzB,QAAM,QAAQ,KAAK,MAAM;AACzB,QAAM,QAAQ,KAAK,MAAM,KAAK,IAAI,KAAK,EAAE;AACzC,QAAM,MAAM,KAAK,MAAM;AACvB,QAAM,MAAM,KAAK,MAAM;AACvB,QAAM,eAAe,KAAK;AAC1B,QAAM,eAAe,KAAK;AAC1B,QAAM,WAAW,KAAK,KAAK,gBAAgB,IAAI,gBAAgB,CAAC;AAChE,MAAI,WAAW,GAAG;AAChB,UAAM,IAAI,MAAM,oEAAoE;AAAA,EACtF;AACA,QAAM,uBAAuB,KAAK,KAAK,IAAI,YAAY,CAAC;AACxD,QAAM,UAAU,OAAO,uBAAuB,KAAK,KAAK,IAAI,KAAK,KAAK,YAAY,KAAK;AACvF,QAAM,UAAU,OAAO,uBAAuB,KAAK,KAAK,IAAI,KAAK,KAAK,YAAY,KAAK;AACvF,QAAM,aAAa,KAAK,OAAO,KAAK,WAAW,KAAK,KAAK,WAAW,EAAE;AACtE,QAAM,WAAW,KAAK,OAAO,KAAK,WAAW,KAAK,KAAK,WAAW,EAAE;AACpE,MAAI,aAAa,WAAW;AAC5B,MAAI,aAAa,aAAa,GAAG;AAC/B,kBAAc,IAAI,KAAK;AAAA,EACzB;AACA,MAAI,CAAC,aAAa,aAAa,GAAG;AAChC,kBAAc,IAAI,KAAK;AAAA,EACzB;AACA,QAAM,SAAS,CAAC;AAChB,WAASP,KAAI,GAAGA,KAAI,WAAWA,MAAK;AAClC,UAAMC,KAAID,MAAK,YAAY;AAC3B,UAAM,SAAS,aAAaC,KAAI;AAChC,UAAMC,KAAI,UAAU,KAAK,KAAK,IAAI,MAAM;AACxC,UAAMC,KAAI,UAAU,KAAK,KAAK,IAAI,MAAM;AACxC,WAAO,KAAK,EAAE,GAAAD,IAAG,GAAAC,GAAE,CAAC;AAAA,EACtB;AACA,SAAO;AACT;AACA,OAAO,mBAAmB,mBAAmB;AAC7C,eAAe,WAAW,QAAQ,MAAM;AACtC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAMC,KAAI,KAAK,QAAQ,KAAK,UAAU;AACtC,QAAMC,KAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,KAAKA,KAAI;AACf,QAAM,KAAK,MAAM,MAAMA,KAAI;AAC3B,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,SAAS;AAAA,IACb,EAAE,GAAGD,KAAI,GAAG,GAAG,CAACC,KAAI,EAAE;AAAA,IACtB,EAAE,GAAG,CAACD,KAAI,GAAG,GAAG,CAACC,KAAI,EAAE;AAAA,IACvB,GAAG,kBAAkB,CAACD,KAAI,GAAG,CAACC,KAAI,GAAG,CAACD,KAAI,GAAGC,KAAI,GAAG,IAAI,IAAI,KAAK;AAAA,IACjE,EAAE,GAAGD,KAAI,GAAG,GAAGC,KAAI,EAAE;AAAA,IACrB,GAAG,kBAAkBD,KAAI,GAAGC,KAAI,GAAGD,KAAI,GAAG,CAACC,KAAI,GAAG,IAAI,IAAI,IAAI;AAAA,EAChE;AACA,QAAM,KAAK,GAAO,IAAI,QAAQ;AAC9B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,iBAAiB,qBAAqB,MAAM;AAClD,QAAM,sBAAsB,GAAG,KAAK,gBAAgB,OAAO;AAC3D,QAAM,kBAAkB,SAAS,OAAO,MAAM,qBAAqB,cAAc;AACjF,kBAAgB,KAAK,SAAS,uBAAuB;AACrD,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,oBAAgB,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EAC3D;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,oBAAgB,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAC5D;AACA,kBAAgB,KAAK,aAAa,aAAa,KAAK,CAAC,MAAM;AAC3D,mBAAiB,MAAM,eAAe;AACtC,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,YAAY,YAAY;AAM/B,SAAS,mBAAmB,QAAQD,IAAGC,IAAG,QAAQ;AAChD,SAAO,OAAO,OAAO,WAAW,cAAc,EAAE;AAAA,IAC9C;AAAA,IACA,OAAO,IAAI,SAASG,IAAG;AACrB,aAAOA,GAAE,IAAI,MAAMA,GAAE;AAAA,IACvB,CAAC,EAAE,KAAK,GAAG;AAAA,EACb,EAAE,KAAK,SAAS,iBAAiB,EAAE,KAAK,aAAa,eAAe,CAACJ,KAAI,IAAI,MAAMC,KAAI,IAAI,GAAG;AAChG;AACA,OAAO,oBAAoB,oBAAoB;AAG/C,eAAe,KAAK,QAAQ,MAAM;AAChC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAMA,KAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,UAAU;AAChB,QAAMD,KAAI,KAAK,QAAQ,KAAK,UAAU;AACtC,QAAM,OAAO;AACb,QAAM,QAAQA;AACd,QAAM,MAAM,CAACC;AACb,QAAM,SAAS;AACf,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,OAAO,SAAS,GAAG,IAAI;AAAA,IAC5B,EAAE,GAAG,OAAO,GAAG,IAAI;AAAA,IACnB,EAAE,GAAG,OAAO,GAAG,OAAO;AAAA,IACtB,EAAE,GAAG,MAAM,GAAG,OAAO;AAAA,IACrB,EAAE,GAAG,MAAM,GAAG,MAAM,QAAQ;AAAA,IAC5B,EAAE,GAAG,OAAO,SAAS,GAAG,IAAI;AAAA,EAC9B;AACA,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAO,IAAI,QAAQ;AAC9B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,WAAW,qBAAqB,MAAM;AAC5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,cAAU,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAKC,KAAI,CAAC,GAAG;AAC7G,QAAI,WAAW;AACb,cAAQ,KAAK,SAAS,SAAS;AAAA,IACjC;AAAA,EACF,OAAO;AACL,cAAU,mBAAmB,UAAUD,IAAGC,IAAG,MAAM;AAAA,EACrD;AACA,MAAI,YAAY;AACd,YAAQ,KAAK,SAAS,UAAU;AAAA,EAClC;AACA,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,EACtD;AACA,SAAO;AACT;AACA,OAAO,MAAM,MAAM;AAInB,SAAS,OAAO,QAAQ,MAAM;AAC5B,QAAM,EAAE,WAAW,IAAI,cAAc,IAAI;AACzC,OAAK,QAAQ;AACb,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACxG,QAAM,EAAE,UAAU,IAAI;AACtB,QAAMC,KAAI,KAAK,IAAI,IAAI,KAAK,SAAS,CAAC;AACtC,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAGA,KAAI,EAAE;AAAA,IACjB,EAAE,GAAGA,KAAI,GAAG,GAAG,EAAE;AAAA,IACjB,EAAE,GAAG,GAAG,GAAG,CAACA,KAAI,EAAE;AAAA,IAClB,EAAE,GAAG,CAACA,KAAI,GAAG,GAAG,EAAE;AAAA,EACpB;AACA,QAAM,KAAK,GAAO,IAAI,QAAQ;AAC9B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,aAAa,qBAAqB,MAAM;AAC9C,QAAM,YAAY,GAAG,KAAK,YAAY,OAAO;AAC7C,QAAM,cAAc,SAAS,OAAO,MAAM,WAAW,cAAc;AACnE,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,gBAAY,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACvD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,gBAAY,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACxD;AACA,OAAK,QAAQ;AACb,OAAK,SAAS;AACd,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,EACtD;AACA,SAAO;AACT;AACA,OAAO,QAAQ,QAAQ;AAIvB,eAAe,OAAO,QAAQ,MAAM;AAClC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,YAAY,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC5F,QAAM,SAAS,KAAK,QAAQ,IAAI;AAChC,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAO,IAAI,QAAQ;AAC9B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,YAAY,GAAG,OAAO,GAAG,GAAG,SAAS,GAAG,OAAO;AACrD,iBAAa,SAAS,OAAO,MAAM,WAAW,cAAc;AAC5D,eAAW,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AAAA,EAChG,OAAO;AACL,iBAAa,SAAS,OAAO,UAAU,cAAc,EAAE,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC;AAAA,EACtK;AACA,mBAAiB,MAAM,UAAU;AACjC,OAAK,YAAY,SAAS,OAAO;AAC/B,QAAI,KAAK,oBAAoB,MAAM,QAAQ,KAAK;AAChD,WAAO,kBAAkB,OAAO,MAAM,QAAQ,KAAK;AAAA,EACrD;AACA,SAAO;AACT;AACA,OAAO,QAAQ,QAAQ;AAIvB,SAAS,WAAWG,IAAG;AACrB,QAAM,UAAU,KAAK,IAAI,KAAK,KAAK,CAAC;AACpC,QAAM,UAAU,KAAK,IAAI,KAAK,KAAK,CAAC;AACpC,QAAM,aAAaA,KAAI;AACvB,QAAM,UAAU,EAAE,GAAG,aAAa,IAAI,SAAS,GAAG,aAAa,IAAI,QAAQ;AAC3E,QAAM,UAAU,EAAE,GAAG,EAAE,aAAa,KAAK,SAAS,GAAG,aAAa,IAAI,QAAQ;AAC9E,QAAM,UAAU,EAAE,GAAG,EAAE,aAAa,KAAK,SAAS,GAAG,EAAE,aAAa,KAAK,QAAQ;AACjF,QAAM,UAAU,EAAE,GAAG,aAAa,IAAI,SAAS,GAAG,EAAE,aAAa,KAAK,QAAQ;AAC9E,SAAO,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM,QAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,uBACzC,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM,QAAQ,CAAC,IAAI,QAAQ,CAAC;AACzE;AACA,OAAO,YAAY,YAAY;AAC/B,SAAS,cAAc,QAAQ,MAAM;AACnC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,OAAK,QAAQ;AACb,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACxG,QAAM,SAAS,KAAK,IAAI,KAAI,6BAAM,UAAS,CAAC;AAC5C,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAO,IAAI,QAAQ;AAC9B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,aAAa,GAAG,OAAO,GAAG,GAAG,SAAS,GAAG,OAAO;AACtD,QAAM,WAAW,WAAW,MAAM;AAClC,QAAM,WAAW,GAAG,KAAK,UAAU,OAAO;AAC1C,QAAM,iBAAiB,SAAS,OAAO,MAAM,YAAY,cAAc;AACvE,iBAAe,OAAO,MAAM,QAAQ;AACpC,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,mBAAe,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EAC1D;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,mBAAe,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAC3D;AACA,mBAAiB,MAAM,cAAc;AACrC,OAAK,YAAY,SAAS,OAAO;AAC/B,QAAI,KAAK,2BAA2B,MAAM,EAAE,QAAQ,MAAM,CAAC;AAC3D,UAAM,MAAM,kBAAkB,OAAO,MAAM,QAAQ,KAAK;AACxD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,eAAe,eAAe;AAIrC,SAAS,sBAAsB,SAAS,SAAS,QAAQ,YAAY,KAAK,aAAa,GAAG,WAAW,KAAK;AACxG,QAAM,SAAS,CAAC;AAChB,QAAM,gBAAgB,aAAa,KAAK,KAAK;AAC7C,QAAM,cAAc,WAAW,KAAK,KAAK;AACzC,QAAM,aAAa,cAAc;AACjC,QAAM,YAAY,cAAc,YAAY;AAC5C,WAAST,KAAI,GAAGA,KAAI,WAAWA,MAAK;AAClC,UAAM,QAAQ,gBAAgBA,KAAI;AAClC,UAAME,KAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,UAAMC,KAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,WAAO,KAAK,EAAE,GAAG,CAACD,IAAG,GAAG,CAACC,GAAE,CAAC;AAAA,EAC9B;AACA,SAAO;AACT;AACA,OAAO,uBAAuB,sBAAsB;AACpD,eAAe,eAAe,QAAQ,MAAM;AAC1C,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAMC,KAAI,KAAK,SAAS,KAAK,WAAW;AACxC,QAAMC,KAAI,KAAK,UAAU,KAAK,WAAW;AACzC,QAAM,SAAS,KAAK,IAAI,GAAGA,KAAI,GAAG;AAClC,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,SAAS;AAAA,IACb,GAAG,sBAAsBD,KAAI,GAAG,CAACC,KAAI,GAAG,QAAQ,IAAI,KAAK,CAAC;AAAA,IAC1D,EAAE,GAAG,CAACD,KAAI,IAAI,QAAQ,GAAG,OAAO;AAAA,IAChC,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI;AAAA,IAC5E,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,IAAI;AAAA,IAC1E,EAAE,GAAG,CAACA,KAAI,IAAI,QAAQ,GAAG,CAACC,KAAI,EAAE;AAAA,IAChC,GAAG,sBAAsBD,KAAI,GAAGC,KAAI,GAAG,QAAQ,IAAI,GAAG,EAAE;AAAA,EAC1D;AACA,QAAM,aAAa;AAAA,IACjB,EAAE,GAAGD,KAAI,GAAG,GAAG,CAACC,KAAI,IAAI,OAAO;AAAA,IAC/B,EAAE,GAAG,CAACD,KAAI,GAAG,GAAG,CAACC,KAAI,IAAI,OAAO;AAAA,IAChC,GAAG,sBAAsBD,KAAI,GAAG,CAACC,KAAI,GAAG,QAAQ,IAAI,KAAK,CAAC;AAAA,IAC1D,EAAE,GAAG,CAACD,KAAI,IAAI,QAAQ,GAAG,CAAC,OAAO;AAAA,IACjC,GAAG,sBAAsBA,KAAI,IAAIA,KAAI,KAAK,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI;AAAA,IACzE,GAAG,sBAAsBA,KAAI,IAAIA,KAAI,KAAK,QAAQ,QAAQ,IAAI,KAAK,IAAI;AAAA,IACvE,EAAE,GAAG,CAACA,KAAI,IAAI,QAAQ,GAAGC,KAAI,EAAE;AAAA,IAC/B,GAAG,sBAAsBD,KAAI,GAAGC,KAAI,GAAG,QAAQ,IAAI,GAAG,EAAE;AAAA,IACxD,EAAE,GAAG,CAACD,KAAI,GAAG,GAAGC,KAAI,IAAI,OAAO;AAAA,IAC/B,EAAE,GAAGD,KAAI,GAAG,GAAGC,KAAI,IAAI,OAAO;AAAA,EAChC;AACA,QAAM,KAAK,GAAO,IAAI,QAAQ;AAC9B,QAAM,UAAU,kBAAkB,MAAM,EAAE,MAAM,OAAO,CAAC;AACxD,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,qBAAqB,qBAAqB,MAAM;AACtD,QAAM,oBAAoB,mBAAmB,QAAQ,KAAK,EAAE;AAC5D,QAAM,qBAAqB,GAAG,KAAK,mBAAmB,OAAO;AAC7D,QAAM,WAAW,qBAAqB,UAAU;AAChD,QAAM,YAAY,GAAG,KAAK,UAAU,EAAE,GAAG,QAAQ,CAAC;AAClD,QAAM,sBAAsB,SAAS,OAAO,KAAK,cAAc;AAC/D,sBAAoB,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,kBAAkB,CAAC;AACpF,sBAAoB,OAAO,MAAM,oBAAoB,cAAc;AACnE,sBAAoB,KAAK,SAAS,MAAM;AACxC,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,wBAAoB,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EAC/D;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,wBAAoB,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAChE;AACA,sBAAoB,KAAK,aAAa,aAAa,MAAM,MAAM;AAC/D,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAACD,KAAI,IAAI,UAAU,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACC,KAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC7H;AACA,mBAAiB,MAAM,mBAAmB;AAC1C,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,QAAQ,MAAM,YAAY,KAAK;AAC7D,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,gBAAgB,gBAAgB;AAIvC,SAAS,sBAAsB,SAAS,SAAS,QAAQ,YAAY,KAAK,aAAa,GAAG,WAAW,KAAK;AACxG,QAAM,SAAS,CAAC;AAChB,QAAM,gBAAgB,aAAa,KAAK,KAAK;AAC7C,QAAM,cAAc,WAAW,KAAK,KAAK;AACzC,QAAM,aAAa,cAAc;AACjC,QAAM,YAAY,cAAc,YAAY;AAC5C,WAASL,KAAI,GAAGA,KAAI,WAAWA,MAAK;AAClC,UAAM,QAAQ,gBAAgBA,KAAI;AAClC,UAAME,KAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,UAAMC,KAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,WAAO,KAAK,EAAE,GAAAD,IAAG,GAAAC,GAAE,CAAC;AAAA,EACtB;AACA,SAAO;AACT;AACA,OAAO,uBAAuB,sBAAsB;AACpD,eAAe,gBAAgB,QAAQ,MAAM;AAC3C,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAMC,KAAI,KAAK,SAAS,KAAK,WAAW;AACxC,QAAMC,KAAI,KAAK,UAAU,KAAK,WAAW;AACzC,QAAM,SAAS,KAAK,IAAI,GAAGA,KAAI,GAAG;AAClC,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,SAAS;AAAA,IACb,GAAG,sBAAsBD,KAAI,GAAG,CAACC,KAAI,GAAG,QAAQ,IAAI,KAAK,CAAC;AAAA,IAC1D,EAAE,GAAGD,KAAI,IAAI,QAAQ,GAAG,CAAC,OAAO;AAAA,IAChC,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI;AAAA,IAC5E,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,IAAI;AAAA,IAC1E,EAAE,GAAGA,KAAI,IAAI,QAAQ,GAAGC,KAAI,EAAE;AAAA,IAC9B,GAAG,sBAAsBD,KAAI,GAAGC,KAAI,GAAG,QAAQ,IAAI,GAAG,EAAE;AAAA,EAC1D;AACA,QAAM,aAAa;AAAA,IACjB,EAAE,GAAG,CAACD,KAAI,GAAG,GAAG,CAACC,KAAI,IAAI,OAAO;AAAA,IAChC,EAAE,GAAGD,KAAI,GAAG,GAAG,CAACC,KAAI,IAAI,OAAO;AAAA,IAC/B,GAAG,sBAAsBD,KAAI,GAAG,CAACC,KAAI,GAAG,QAAQ,IAAI,KAAK,CAAC;AAAA,IAC1D,EAAE,GAAGD,KAAI,IAAI,QAAQ,GAAG,CAAC,OAAO;AAAA,IAChC,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI;AAAA,IAC5E,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,IAAI;AAAA,IAC1E,EAAE,GAAGA,KAAI,IAAI,QAAQ,GAAGC,KAAI,EAAE;AAAA,IAC9B,GAAG,sBAAsBD,KAAI,GAAGC,KAAI,GAAG,QAAQ,IAAI,GAAG,EAAE;AAAA,IACxD,EAAE,GAAGD,KAAI,GAAG,GAAGC,KAAI,IAAI,OAAO;AAAA,IAC9B,EAAE,GAAG,CAACD,KAAI,GAAG,GAAGC,KAAI,IAAI,OAAO;AAAA,EACjC;AACA,QAAM,KAAK,GAAO,IAAI,QAAQ;AAC9B,QAAM,UAAU,kBAAkB,MAAM,EAAE,MAAM,OAAO,CAAC;AACxD,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,sBAAsB,qBAAqB,MAAM;AACvD,QAAM,oBAAoB,oBAAoB,QAAQ,KAAK,EAAE;AAC7D,QAAM,sBAAsB,GAAG,KAAK,mBAAmB,OAAO;AAC9D,QAAM,WAAW,qBAAqB,UAAU;AAChD,QAAM,YAAY,GAAG,KAAK,UAAU,EAAE,GAAG,QAAQ,CAAC;AAClD,QAAM,uBAAuB,SAAS,OAAO,KAAK,cAAc;AAChE,uBAAqB,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,kBAAkB,CAAC;AACrF,uBAAqB,OAAO,MAAM,qBAAqB,cAAc;AACrE,uBAAqB,KAAK,SAAS,MAAM;AACzC,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,yBAAqB,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EAChE;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,yBAAqB,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACjE;AACA,uBAAqB,KAAK,aAAa,aAAa,CAAC,MAAM,MAAM;AACjE,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAACD,KAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACC,KAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC9I;AACA,mBAAiB,MAAM,oBAAoB;AAC3C,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,QAAQ,MAAM,YAAY,KAAK;AAC7D,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,iBAAiB,iBAAiB;AAIzC,SAAS,sBAAsB,SAAS,SAAS,QAAQ,YAAY,KAAK,aAAa,GAAG,WAAW,KAAK;AACxG,QAAM,SAAS,CAAC;AAChB,QAAM,gBAAgB,aAAa,KAAK,KAAK;AAC7C,QAAM,cAAc,WAAW,KAAK,KAAK;AACzC,QAAM,aAAa,cAAc;AACjC,QAAM,YAAY,cAAc,YAAY;AAC5C,WAASL,KAAI,GAAGA,KAAI,WAAWA,MAAK;AAClC,UAAM,QAAQ,gBAAgBA,KAAI;AAClC,UAAME,KAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,UAAMC,KAAI,UAAU,SAAS,KAAK,IAAI,KAAK;AAC3C,WAAO,KAAK,EAAE,GAAG,CAACD,IAAG,GAAG,CAACC,GAAE,CAAC;AAAA,EAC9B;AACA,SAAO;AACT;AACA,OAAO,uBAAuB,sBAAsB;AACpD,eAAe,YAAY,QAAQ,MAAM;AACvC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAMC,KAAI,KAAK,SAAS,KAAK,WAAW;AACxC,QAAMC,KAAI,KAAK,UAAU,KAAK,WAAW;AACzC,QAAM,SAAS,KAAK,IAAI,GAAGA,KAAI,GAAG;AAClC,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,uBAAuB;AAAA,IAC3B,GAAG,sBAAsBD,KAAI,GAAG,CAACC,KAAI,GAAG,QAAQ,IAAI,KAAK,CAAC;AAAA,IAC1D,EAAE,GAAG,CAACD,KAAI,IAAI,QAAQ,GAAG,OAAO;AAAA,IAChC,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI;AAAA,IAC5E,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,IAAI;AAAA,IAC1E,EAAE,GAAG,CAACA,KAAI,IAAI,QAAQ,GAAG,CAACC,KAAI,EAAE;AAAA,IAChC,GAAG,sBAAsBD,KAAI,GAAGC,KAAI,GAAG,QAAQ,IAAI,GAAG,EAAE;AAAA,EAC1D;AACA,QAAM,wBAAwB;AAAA,IAC5B,GAAG,sBAAsB,CAACD,KAAI,IAAI,SAAS,SAAS,GAAG,CAACC,KAAI,GAAG,QAAQ,IAAI,KAAK,IAAI;AAAA,IACpF,EAAE,GAAGD,KAAI,IAAI,SAAS,GAAG,GAAG,OAAO;AAAA,IACnC,GAAG,sBAAsB,CAACA,KAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,GAAG,EAAE;AAAA,IACxE,GAAG,sBAAsB,CAACA,KAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,CAAC;AAAA,IACxE,EAAE,GAAGA,KAAI,IAAI,SAAS,GAAG,GAAG,CAAC,OAAO;AAAA,IACpC,GAAG,sBAAsB,CAACA,KAAI,IAAI,SAAS,SAAS,GAAGC,KAAI,GAAG,QAAQ,IAAI,MAAM,IAAI;AAAA,EACtF;AACA,QAAM,aAAa;AAAA,IACjB,EAAE,GAAGD,KAAI,GAAG,GAAG,CAACC,KAAI,IAAI,OAAO;AAAA,IAC/B,EAAE,GAAG,CAACD,KAAI,GAAG,GAAG,CAACC,KAAI,IAAI,OAAO;AAAA,IAChC,GAAG,sBAAsBD,KAAI,GAAG,CAACC,KAAI,GAAG,QAAQ,IAAI,KAAK,CAAC;AAAA,IAC1D,EAAE,GAAG,CAACD,KAAI,IAAI,QAAQ,GAAG,CAAC,OAAO;AAAA,IACjC,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,MAAM,IAAI;AAAA,IAC5E,GAAG,sBAAsBA,KAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,IAAI;AAAA,IAC1E,EAAE,GAAG,CAACA,KAAI,IAAI,QAAQ,GAAGC,KAAI,EAAE;AAAA,IAC/B,GAAG,sBAAsBD,KAAI,GAAGC,KAAI,GAAG,QAAQ,IAAI,GAAG,EAAE;AAAA,IACxD,EAAE,GAAG,CAACD,KAAI,GAAG,GAAGC,KAAI,IAAI,OAAO;AAAA,IAC/B,EAAE,GAAGD,KAAI,IAAI,SAAS,SAAS,GAAG,GAAGC,KAAI,IAAI,OAAO;AAAA,IACpD,GAAG,sBAAsB,CAACD,KAAI,IAAI,SAAS,SAAS,GAAG,CAACC,KAAI,GAAG,QAAQ,IAAI,KAAK,IAAI;AAAA,IACpF,EAAE,GAAGD,KAAI,IAAI,SAAS,GAAG,GAAG,OAAO;AAAA,IACnC,GAAG,sBAAsB,CAACA,KAAI,IAAI,SAAS,GAAG,CAAC,QAAQ,QAAQ,IAAI,GAAG,EAAE;AAAA,IACxE,GAAG,sBAAsB,CAACA,KAAI,IAAI,SAAS,GAAG,QAAQ,QAAQ,IAAI,KAAK,CAAC;AAAA,IACxE,EAAE,GAAGA,KAAI,IAAI,SAAS,GAAG,GAAG,CAAC,OAAO;AAAA,IACpC,GAAG,sBAAsB,CAACA,KAAI,IAAI,SAAS,SAAS,GAAGC,KAAI,GAAG,QAAQ,IAAI,MAAM,IAAI;AAAA,EACtF;AACA,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,EAAE,MAAM,OAAO,CAAC;AACxD,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,qBAAqB,qBAAqB,oBAAoB;AACpE,QAAM,wBAAwB,mBAAmB,QAAQ,KAAK,EAAE;AAChE,QAAM,qBAAqB,GAAG,KAAK,uBAAuB,OAAO;AACjE,QAAM,sBAAsB,qBAAqB,qBAAqB;AACtE,QAAM,yBAAyB,oBAAoB,QAAQ,KAAK,EAAE;AAClE,QAAM,sBAAsB,GAAG,KAAK,wBAAwB,OAAO;AACnE,QAAM,WAAW,qBAAqB,UAAU;AAChD,QAAM,YAAY,GAAG,KAAK,UAAU,EAAE,GAAG,QAAQ,CAAC;AAClD,QAAM,mBAAmB,SAAS,OAAO,KAAK,cAAc;AAC5D,mBAAiB,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,kBAAkB,CAAC;AACjF,mBAAiB,OAAO,MAAM,oBAAoB,cAAc;AAChE,mBAAiB,OAAO,MAAM,qBAAqB,cAAc;AACjE,mBAAiB,KAAK,SAAS,MAAM;AACrC,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,qBAAiB,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EAC5D;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,qBAAiB,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAC7D;AACA,mBAAiB,KAAK,aAAa,aAAa,SAAS,SAAS,CAAC,MAAM;AACzE,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAACD,KAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACC,KAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC9I;AACA,mBAAiB,MAAM,gBAAgB;AACvC,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,QAAQ,MAAM,YAAY,KAAK;AAC7D,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,aAAa,aAAa;AAIjC,eAAe,gBAAgB,QAAQ,MAAM;AAC3C,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAM,WAAW,IAAI,YAAY;AACjC,QAAMD,KAAI,KAAK,IAAI,WAAW,KAAK,SAAS,KAAK,WAAW,KAAK,KAAK,OAAM,6BAAM,UAAS,CAAC;AAC5F,QAAMC,KAAI,KAAK,IAAI,WAAW,KAAK,UAAU,KAAK,WAAW,KAAK,IAAG,6BAAM,WAAU,CAAC;AACtF,QAAM,SAASA,KAAI;AACnB,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,aAAaD,IAAG,cAAcC;AACpC,QAAM,KAAK,aAAa;AACxB,QAAM,KAAK,cAAc;AACzB,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,IAAI,GAAG,EAAE;AAAA,IACd,EAAE,GAAG,IAAI,GAAG,EAAE;AAAA,IACd,EAAE,GAAG,GAAG,GAAG,cAAc,EAAE;AAAA,IAC3B,EAAE,GAAG,IAAI,GAAG,YAAY;AAAA,IACxB,EAAE,GAAG,IAAI,GAAG,YAAY;AAAA,IACxB,GAAG,qBAAqB,CAAC,IAAI,CAAC,cAAc,GAAG,QAAQ,IAAI,KAAK,EAAE;AAAA,EACpE;AACA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,QAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,UAAQ,KAAK,SAAS,uBAAuB;AAC7C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AACA,UAAQ,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAK,CAACC,KAAI,CAAC,GAAG;AAC3D,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,iBAAiB,iBAAiB;AAIzC,IAAI,sBAAsC,OAAO,CAACH,IAAGC,IAAG,OAAO,QAAQ,IAAI,OAAO;AAChF,SAAO;AAAA,IACL,IAAID,EAAC,IAAIC,KAAI,EAAE;AAAA,IACf,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK;AAAA,IAC3B,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK;AAAA,IAC5B,MAAM,MAAM;AAAA,IACZ,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK;AAAA,IAC3B,MAAM,CAAC,MAAM;AAAA,EACf,EAAE,KAAK,GAAG;AACZ,GAAG,qBAAqB;AACxB,IAAI,2BAA2C,OAAO,CAACD,IAAGC,IAAG,OAAO,QAAQ,IAAI,OAAO;AACrF,SAAO;AAAA,IACL,IAAID,EAAC,IAAIC,KAAI,EAAE;AAAA,IACf,IAAID,KAAI,KAAK,IAAIC,KAAI,EAAE;AAAA,IACvB,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK;AAAA,IAC5B,MAAM,MAAM;AAAA,IACZ,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK;AAAA,IAC3B,MAAM,CAAC,MAAM;AAAA,EACf,EAAE,KAAK,GAAG;AACZ,GAAG,0BAA0B;AAC7B,IAAI,2BAA2C,OAAO,CAACD,IAAGC,IAAG,OAAO,QAAQ,IAAI,OAAO;AACrF,SAAO,CAAC,IAAID,KAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,EAAE,KAAK,GAAG;AACvF,GAAG,0BAA0B;AAC7B,eAAe,SAAS,QAAQ,MAAM;AACpC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAME,KAAI,KAAK,IAAI,KAAK,QAAQ,KAAK,SAAS,KAAK,SAAS,CAAC;AAC7D,QAAM,KAAKA,KAAI;AACf,QAAM,KAAK,MAAM,MAAMA,KAAI;AAC3B,QAAMC,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,KAAK,SAAS,KAAK,UAAU,CAAC;AACpE,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,gBAAgB,yBAAyB,GAAG,GAAGD,IAAGC,IAAG,IAAI,EAAE;AACjE,UAAM,gBAAgB,yBAAyB,GAAG,IAAID,IAAGC,IAAG,IAAI,EAAE;AAClE,UAAM,YAAY,GAAG,KAAK,eAAe,kBAAkB,MAAM,CAAC,CAAC,CAAC;AACpE,UAAM,YAAY,GAAG,KAAK,eAAe,kBAAkB,MAAM,EAAE,MAAM,OAAO,CAAC,CAAC;AAClF,gBAAY,SAAS,OAAO,MAAM,WAAW,cAAc;AAC3D,gBAAY,SAAS,OAAO,MAAM,WAAW,cAAc;AAC3D,cAAU,KAAK,SAAS,uBAAuB;AAC/C,QAAI,WAAW;AACb,gBAAU,KAAK,SAAS,SAAS;AAAA,IACnC;AAAA,EACF,OAAO;AACL,UAAM,WAAW,oBAAoB,GAAG,GAAGD,IAAGC,IAAG,IAAI,EAAE;AACvD,gBAAY,SAAS,OAAO,QAAQ,cAAc,EAAE,KAAK,KAAK,QAAQ,EAAE,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC,EAAE,KAAK,SAAS,UAAU;AAAA,EACvL;AACA,YAAU,KAAK,kBAAkB,EAAE;AACnC,YAAU,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAK,EAAEC,KAAI,IAAI,GAAG,GAAG;AACpE,mBAAiB,MAAM,SAAS;AAChC,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,EAAE,KAAK,QAAQ,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,WAAW,KAAK,OAAO,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC9I;AACA,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,KAAK,MAAM,KAAK;AAC9C,UAAMH,KAAI,IAAI,KAAK,KAAK,KAAK;AAC7B,QAAI,MAAM,MAAM,KAAK,IAAIA,EAAC,KAAK,KAAK,SAAS,KAAK,KAAK,KAAK,IAAIA,EAAC,MAAM,KAAK,SAAS,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE,KAAK,KAAK,UAAU,KAAK,IAAI,KAAK;AAC7J,UAAIC,KAAI,KAAK,MAAM,IAAID,KAAIA,MAAK,KAAK;AACrC,UAAIC,KAAI,GAAG;AACT,QAAAA,KAAI,KAAK,KAAKA,EAAC;AAAA,MACjB;AACA,MAAAA,KAAI,KAAKA;AACT,UAAI,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG;AAC/B,QAAAA,KAAI,CAACA;AAAA,MACP;AACA,UAAI,KAAKA;AAAA,IACX;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,UAAU,UAAU;AAI3B,eAAe,iBAAiB,QAAQ,MAAM;AAC5C,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAMC,KAAI,KAAK,QAAQ,KAAK;AAC5B,QAAMC,KAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,aAAaA,KAAI;AACvB,QAAMH,KAAI,CAACE,KAAI;AACf,QAAMD,KAAI,CAACE,KAAI,IAAI,aAAa;AAChC,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,MAAM;AAAA,IACV,EAAE,GAAAH,IAAG,GAAGC,KAAI,WAAW;AAAA,IACvB,EAAE,GAAG,CAACD,IAAG,GAAGC,KAAI,WAAW;AAAA,IAC3B,EAAE,GAAG,CAACD,IAAG,GAAG,CAACC,GAAE;AAAA,IACf,EAAE,GAAAD,IAAG,GAAG,CAACC,GAAE;AAAA,IACX,EAAE,GAAAD,IAAG,GAAAC,GAAE;AAAA,IACP,EAAE,GAAG,CAACD,IAAG,GAAAC,GAAE;AAAA,IACX,EAAE,GAAG,CAACD,IAAG,GAAGC,KAAI,WAAW;AAAA,EAC7B;AACA,QAAM,OAAO,GAAG;AAAA,IACd,IAAI,IAAI,CAACJ,OAAM,CAACA,GAAE,GAAGA,GAAE,CAAC,CAAC;AAAA,IACzB;AAAA,EACF;AACA,QAAM,UAAU,SAAS,OAAO,MAAM,MAAM,cAAc;AAC1D,UAAQ,KAAK,SAAS,uBAAuB;AAC7C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAQ,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACnD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAQ,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACpD;AACA,QAAM;AAAA,IACJ;AAAA,IACA,aAAaG,MAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAKC,KAAI,cAAc,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAClJ;AACA,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,KAAK,MAAM,KAAK;AAC9C,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,kBAAkB,kBAAkB;AAI3C,eAAe,aAAa,QAAQ,MAAM;AAviD1C;AAwiDE,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,YAAY,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC5F,QAAM,MAAM;AACZ,QAAM,cAAc,KAAK,QAAQ,IAAI,cAAc;AACnD,QAAM,cAAc,KAAK,QAAQ,IAAI;AACrC,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,eAAe,kBAAkB,MAAM,EAAE,WAAW,KAAK,aAAa,IAAI,CAAC;AACjF,UAAM,eAAe,kBAAkB,MAAM,EAAE,WAAW,KAAK,aAAa,IAAI,CAAC;AACjF,UAAM,iBAAiB,GAAG,OAAO,GAAG,GAAG,cAAc,GAAG,YAAY;AACpE,UAAM,iBAAiB,GAAG,OAAO,GAAG,GAAG,cAAc,GAAG,YAAY;AACpE,kBAAc,SAAS,OAAO,KAAK,cAAc;AACjD,gBAAY,KAAK,SAAS,oBAAoB,KAAK,UAAU,CAAC,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AAC5G,sBAAY,KAAK,MAAjB,mBAAoB,YAAY;AAChC,sBAAY,KAAK,MAAjB,mBAAoB,YAAY;AAAA,EAClC,OAAO;AACL,kBAAc,SAAS,OAAO,KAAK,cAAc;AACjD,UAAM,cAAc,YAAY,OAAO,UAAU,cAAc;AAC/D,UAAM,cAAc,YAAY,OAAO,QAAQ;AAC/C,gBAAY,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,UAAU;AAC3E,gBAAY,KAAK,SAAS,cAAc,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,KAAK,WAAW,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC;AACrH,gBAAY,KAAK,SAAS,cAAc,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,KAAK,WAAW,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC;AAAA,EACvH;AACA,mBAAiB,MAAM,WAAW;AAClC,OAAK,YAAY,SAAS,OAAO;AAC/B,QAAI,KAAK,0BAA0B,MAAM,aAAa,KAAK;AAC3D,WAAO,kBAAkB,OAAO,MAAM,aAAa,KAAK;AAAA,EAC1D;AACA,SAAO;AACT;AACA,OAAO,cAAc,cAAc;AAInC,SAAS,aAAa,QAAQ,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;AAClE,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,QAAQ;AACb,OAAK,aAAa;AAClB,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACxG,QAAM,SAAS;AACf,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,EAAE,WAAW,IAAI;AACvB,QAAM,UAAU,kBAAkB,MAAM,EAAE,WAAW,QAAQ,CAAC;AAC9D,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,aAAa,GAAG,OAAO,GAAG,GAAG,SAAS,GAAG,OAAO;AACtD,QAAM,gBAAgB,SAAS,OAAO,MAAM,YAAY,cAAc;AACtE,gBAAc,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS,UAAU,cAAc;AAC/E,MAAI,aAAa,UAAU,SAAS,KAAK,KAAK,SAAS,aAAa;AAClE,kBAAc,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACzD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,kBAAc,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAC1D;AACA,mBAAiB,MAAM,aAAa;AACpC,OAAK,YAAY,SAAS,OAAO;AAC/B,QAAI,KAAK,0BAA0B,MAAM,EAAE,QAAQ,MAAM,CAAC;AAC1D,UAAM,MAAM,kBAAkB,OAAO,MAAM,QAAQ,KAAK;AACxD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,cAAc,cAAc;AAInC,eAAe,gBAAgB,QAAQ,MAAM;AAC3C,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAMC,KAAI,KAAK,SAAS,KAAK,WAAW;AACxC,QAAMC,KAAID,KAAI,KAAK;AACnB,QAAM,KAAKA,KAAI,KAAK;AACpB,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,CAACC,GAAE;AAAA,IACd,EAAE,GAAG,IAAI,GAAG,CAACA,GAAE;AAAA,IACf,EAAE,GAAG,KAAK,GAAG,GAAG,EAAE;AAAA,EACpB;AACA,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,QAAM,mBAAmB,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACA,KAAI,CAAC,KAAKA,KAAI,CAAC,GAAG;AAC5H,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,qBAAiB,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACjE;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,qBAAiB,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAClE;AACA,OAAK,QAAQD;AACb,OAAK,SAASC;AACd,mBAAiB,MAAM,gBAAgB;AACvC,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,CAACA,KAAI,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC9H;AACA,OAAK,YAAY,SAAS,OAAO;AAC/B,QAAI,KAAK,sBAAsB,MAAM,QAAQ,KAAK;AAClD,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,EACtD;AACA,SAAO;AACT;AACA,OAAO,iBAAiB,iBAAiB;AAIzC,SAAS,SAAS,QAAQ,MAAM,EAAE,KAAK,QAAQ,EAAE,OAAO,QAAQ,eAAe,EAAE,GAAG;AAClF,QAAM,EAAE,WAAW,IAAI,cAAc,IAAI;AACzC,OAAK,QAAQ;AACb,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACxG,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,QAAQ,KAAK,IAAI,KAAI,6BAAM,UAAS,CAAC;AACzC,MAAI,SAAS,KAAK,IAAI,KAAI,6BAAM,WAAU,CAAC;AAC3C,MAAI,QAAQ,MAAM;AAChB,YAAQ,KAAK,IAAI,KAAI,6BAAM,UAAS,CAAC;AACrC,aAAS,KAAK,IAAI,KAAI,6BAAM,WAAU,CAAC;AAAA,EACzC;AACA,QAAMH,KAAI,KAAK,QAAQ;AACvB,QAAMC,KAAI,KAAK,SAAS;AACxB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM;AAAA,IACtC,QAAQ,eAAe;AAAA,IACvB,MAAM,eAAe;AAAA,EACvB,CAAC;AACD,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,YAAY,GAAG,UAAUD,IAAGC,IAAG,OAAO,QAAQ,OAAO;AAC3D,QAAM,QAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AAC7D,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,UAAM,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACjD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,UAAM,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAClD;AACA,mBAAiB,MAAM,KAAK;AAC5B,QAAM,WAAU,iCAAQ,YAAW;AACnC,MAAI,KAAK,SAAS,KAAK,QAAQ;AAC7B,SAAK,SAAS,UAAU,KAAK;AAC7B,SAAK,UAAU,UAAU,KAAK;AAAA,EAChC;AACA,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AACT;AACA,OAAO,UAAU,UAAU;AAI3B,eAAe,qBAAqB,QAAQ,MAAM;AAChD,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,WAAW,IAAI,YAAY;AACjC,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAMC,KAAI,KAAK,IAAI,UAAU,KAAK,SAAS,KAAK,WAAW,KAAK,IAAG,6BAAM,UAAS,CAAC;AACnF,QAAMC,KAAI,KAAK,IAAI,WAAW,KAAK,UAAU,KAAK,WAAW,KAAK,IAAG,6BAAM,WAAU,CAAC;AACtF,QAAM,SAASA,KAAI;AACnB,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,CAACD,KAAI,GAAG,GAAG,CAACC,KAAI,EAAE;AAAA,IACvB,EAAE,GAAGD,KAAI,IAAI,QAAQ,GAAG,CAACC,KAAI,EAAE;AAAA,IAC/B,GAAG,qBAAqB,CAACD,KAAI,IAAI,QAAQ,GAAG,QAAQ,IAAI,IAAI,GAAG;AAAA,IAC/D,EAAE,GAAGA,KAAI,IAAI,QAAQ,GAAGC,KAAI,EAAE;AAAA,IAC9B,EAAE,GAAG,CAACD,KAAI,GAAG,GAAGC,KAAI,EAAE;AAAA,EACxB;AACA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,QAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,UAAQ,KAAK,SAAS,uBAAuB;AAC7C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AACA,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,OAAO;AAC/B,QAAI,KAAK,kBAAkB,MAAM,EAAE,QAAQ,MAAM,CAAC;AAClD,UAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,sBAAsB,sBAAsB;AAInD,IAAI,qBAAqC,OAAO,CAACH,IAAGC,IAAG,OAAO,QAAQO,OAAM;AAC1E,SAAO;AAAA,IACL,IAAIR,KAAIQ,EAAC,IAAIP,EAAC;AAAA,IACd,IAAID,KAAI,QAAQQ,EAAC,IAAIP,EAAC;AAAA,IACtB,IAAID,KAAI,KAAK,IAAIC,KAAI,SAAS,CAAC;AAAA,IAC/B,IAAID,KAAI,QAAQQ,EAAC,IAAIP,KAAI,MAAM;AAAA,IAC/B,IAAID,KAAIQ,EAAC,IAAIP,KAAI,MAAM;AAAA,IACvB,IAAID,EAAC,IAAIC,KAAI,SAAS,CAAC;AAAA,IACvB;AAAA,EACF,EAAE,KAAK,GAAG;AACZ,GAAG,oBAAoB;AACvB,eAAe,QAAQ,QAAQ,MAAM;AACnC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAMQ,KAAI;AACV,QAAMN,KAAI,KAAK,SAAS,KAAK;AAC7B,QAAMK,KAAIL,KAAIM;AACd,QAAMP,KAAI,KAAK,QAAQ,IAAIM,KAAI,KAAK;AACpC,QAAM,SAAS;AAAA,IACb,EAAE,GAAGA,IAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAGN,KAAIM,IAAG,GAAG,EAAE;AAAA,IACjB,EAAE,GAAGN,IAAG,GAAG,CAACC,KAAI,EAAE;AAAA,IAClB,EAAE,GAAGD,KAAIM,IAAG,GAAG,CAACL,GAAE;AAAA,IAClB,EAAE,GAAGK,IAAG,GAAG,CAACL,GAAE;AAAA,IACd,EAAE,GAAG,GAAG,GAAG,CAACA,KAAI,EAAE;AAAA,EACpB;AACA,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,WAAW,mBAAmB,GAAG,GAAGD,IAAGC,IAAGK,EAAC;AACjD,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,cAAU,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACN,KAAI,CAAC,KAAKC,KAAI,CAAC,GAAG;AAC7G,QAAI,WAAW;AACb,cAAQ,KAAK,SAAS,SAAS;AAAA,IACjC;AAAA,EACF,OAAO;AACL,cAAU,mBAAmB,UAAUD,IAAGC,IAAG,MAAM;AAAA,EACrD;AACA,MAAI,YAAY;AACd,YAAQ,KAAK,SAAS,UAAU;AAAA,EAClC;AACA,OAAK,QAAQD;AACb,OAAK,SAASC;AACd,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,EACtD;AACA,SAAO;AACT;AACA,OAAO,SAAS,SAAS;AAIzB,eAAe,UAAU,QAAQ,MAAM;AACrC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,QAAQ;AACb,OAAK,aAAa;AAClB,QAAM,EAAE,SAAS,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACzE,QAAMD,KAAI,KAAK,IAAI,KAAI,6BAAM,UAAS,CAAC;AACvC,QAAMC,KAAI,KAAK,IAAI,KAAI,6BAAM,WAAU,CAAC;AACxC,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAGD,IAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,GAAG,GAAGC,GAAE;AAAA,IACb,EAAE,GAAGD,IAAG,GAAGC,GAAE;AAAA,EACf;AACA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,QAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,UAAQ,KAAK,SAAS,uBAAuB;AAC7C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AACA,UAAQ,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAK,CAACC,KAAI,CAAC,GAAG;AAC3D,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,OAAO;AAC/B,QAAI,KAAK,kBAAkB,MAAM,EAAE,OAAO,CAAC;AAC3C,UAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,WAAW,WAAW;AAI7B,eAAe,KAAK,QAAQ,MAAM,EAAE,QAAQ,EAAE,gBAAgB,UAAU,EAAE,GAAG;AAC3E,QAAM,EAAE,YAAY,IAAI,cAAc,IAAI;AAC1C,OAAK,aAAa;AAClB,QAAM,cAAc,KAAK,eAAe;AACxC,QAAM,aAAa,KAAK,cAAc;AACtC,QAAM,WAAW,KAAK,IAAI,aAAa,UAAU;AACjD,QAAM,eAAe,uCAAW;AAChC,OAAK,QAAQ,KAAK,IAAI,UAAU,gBAAgB,CAAC;AACjD,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,oBAAoB;AACtF,QAAM,WAAW,KAAK,QAAQ;AAC9B,QAAM,SAAS;AACf,QAAM,QAAQ;AACd,QAAM,EAAE,WAAW,IAAI;AACvB,QAAM,EAAE,UAAU,IAAI,cAAc,IAAI;AACxC,QAAMH,KAAI,CAAC,QAAQ;AACnB,QAAMC,KAAI,CAAC,SAAS;AACpB,QAAM,eAAe,KAAK,QAAQ,IAAI;AACtC,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,EAAE,QAAQ,QAAQ,MAAM,OAAO,CAAC;AACxE,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,WAAW,GAAG,UAAUD,IAAGC,IAAG,OAAO,QAAQ,OAAO;AAC1D,QAAM,aAAa,KAAK,IAAI,OAAO,KAAK,KAAK;AAC7C,QAAM,cAAc,SAAS,KAAK,SAAS;AAC3C,QAAM,YAAY,GAAG,UAAU,CAAC,aAAa,GAAG,CAAC,cAAc,GAAG,YAAY,aAAa;AAAA,IACzF,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,YAAY,SAAS,OAAO,MAAM,UAAU,cAAc;AAChE,QAAM,aAAa,SAAS,OAAO,MAAM,SAAS;AAClD,MAAI,KAAK,MAAM;AACb,UAAM,WAAW,SAAS,OAAO,GAAG;AACpC,aAAS;AAAA,MACP,MAAM,MAAM,WAAW,KAAK,MAAM;AAAA,QAChC,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,gBAAgB;AAAA,MAClB,CAAC,CAAC;AAAA,IACJ;AACA,UAAM,WAAW,SAAS,KAAK,EAAE,QAAQ;AACzC,UAAM,YAAY,SAAS;AAC3B,UAAM,aAAa,SAAS;AAC5B,UAAM,QAAQ,SAAS;AACvB,UAAM,QAAQ,SAAS;AACvB,aAAS;AAAA,MACP;AAAA,MACA,aAAa,CAAC,YAAY,IAAI,KAAK,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,QAAQ,CAAC,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,KAAK;AAAA,IAC9K;AACA,aAAS,KAAK,SAAS,UAAU,UAAU,IAAI,QAAQ,KAAK,UAAU,GAAG;AAAA,EAC3E;AACA,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,WAAW,CAAC,cAAc,IAAI,cAAc,IAAI,KAAK,MAAM;AAAA,EAC3H;AACA,YAAU;AAAA,IACR;AAAA,IACA,aAAa,CAAC,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,CAAC,KAAK,SAAS,IAAI,eAAe,CAAC;AAAA,EACvG;AACA,mBAAiB,MAAM,UAAU;AACjC,OAAK,YAAY,SAAS,OAAO;AAC/B,QAAI,KAAK,wBAAwB,MAAM,KAAK;AAC5C,QAAI,CAAC,KAAK,OAAO;AACf,aAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,IAC3C;AACA,UAAM,KAAK,KAAK,KAAK;AACrB,UAAM,KAAK,KAAK,KAAK;AACrB,UAAM,aAAa,KAAK,UAAU;AAClC,QAAI,SAAS,CAAC;AACd,QAAI,UAAU;AACZ,eAAS;AAAA,QACP,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QAC9E,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QACzE,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QACzE,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,MAChF;AAAA,IACF,OAAO;AACL,eAAS;AAAA,QACP,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QACrD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QAC1D,EAAE,GAAG,KAAK,KAAK,QAAQ,IAAI,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACrD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QAC1D,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,MACvD;AAAA,IACF;AACA,UAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,MAAM,MAAM;AAInB,eAAe,WAAW,QAAQ,MAAM,EAAE,QAAQ,EAAE,gBAAgB,UAAU,EAAE,GAAG;AACjF,QAAM,EAAE,YAAY,IAAI,cAAc,IAAI;AAC1C,OAAK,aAAa;AAClB,QAAM,cAAc,KAAK,eAAe;AACxC,QAAM,aAAa,KAAK,cAAc;AACtC,QAAM,WAAW,KAAK,IAAI,aAAa,UAAU;AACjD,QAAM,eAAe,uCAAW;AAChC,OAAK,QAAQ,KAAK,IAAI,UAAU,gBAAgB,CAAC;AACjD,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,oBAAoB;AACtF,QAAM,UAAU;AAChB,QAAM,eAAe,KAAK,QAAQ,IAAI;AACtC,QAAM,WAAW,KAAK,QAAQ;AAC9B,QAAM,EAAE,YAAY,QAAQ,IAAI;AAChC,QAAM,EAAE,UAAU,IAAI,cAAc,IAAI;AACxC,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,OAAO,UAAU,IAAI,MAAM;AACjC,UAAQ,SAAS,QAAQ;AACzB,QAAM,WAAW,SAAS,OAAO,GAAG;AACpC,MAAI,KAAK,MAAM;AACb,aAAS;AAAA,MACP,MAAM,MAAM,WAAW,KAAK,MAAM;AAAA,QAChC,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,gBAAgB;AAAA,MAClB,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACA,QAAM,WAAW,SAAS,KAAK,EAAE,QAAQ;AACzC,QAAM,YAAY,SAAS;AAC3B,QAAM,aAAa,SAAS;AAC5B,QAAM,QAAQ,SAAS;AACvB,QAAM,QAAQ,SAAS;AACvB,QAAM,WAAW,KAAK,IAAI,WAAW,UAAU,IAAI,KAAK,QAAQ,UAAU;AAC1E,QAAM,WAAW,GAAG,OAAO,GAAG,GAAG,UAAU,OAAO;AAClD,QAAM,aAAa,KAAK,IAAI,UAAU,KAAK,KAAK;AAChD,QAAM,cAAc,WAAW,KAAK,SAAS;AAC7C,QAAM,YAAY,GAAG,UAAU,CAAC,aAAa,GAAG,CAAC,cAAc,GAAG,YAAY,aAAa;AAAA,IACzF,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,YAAY,SAAS,OAAO,MAAM,UAAU,cAAc;AAChE,QAAM,aAAa,SAAS,OAAO,MAAM,SAAS;AAClD,WAAS;AAAA,IACP;AAAA,IACA,aAAa,CAAC,YAAY,IAAI,KAAK,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,QAAQ,CAAC,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,KAAK;AAAA,EAC9K;AACA,WAAS,KAAK,SAAS,UAAU,UAAU,IAAI,QAAQ,KAAK,UAAU,GAAG;AACzE,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,WAAW,CAAC,cAAc,IAAI,cAAc,IAAI,KAAK,MAAM;AAAA,EAC3H;AACA,YAAU;AAAA,IACR;AAAA,IACA,aAAa,CAAC,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,CAAC,KAAK,SAAS,IAAI,eAAe,CAAC;AAAA,EACvG;AACA,mBAAiB,MAAM,UAAU;AACjC,OAAK,YAAY,SAAS,OAAO;AAC/B,QAAI,KAAK,wBAAwB,MAAM,KAAK;AAC5C,UAAM,MAAM,kBAAkB,KAAK,MAAM,KAAK;AAC9C,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,YAAY,YAAY;AAI/B,eAAe,YAAY,QAAQ,MAAM,EAAE,QAAQ,EAAE,gBAAgB,UAAU,EAAE,GAAG;AAClF,QAAM,EAAE,YAAY,IAAI,cAAc,IAAI;AAC1C,OAAK,aAAa;AAClB,QAAM,cAAc,KAAK,eAAe;AACxC,QAAM,aAAa,KAAK,cAAc;AACtC,QAAM,WAAW,KAAK,IAAI,aAAa,UAAU;AACjD,QAAM,eAAe,uCAAW;AAChC,OAAK,QAAQ,KAAK,IAAI,UAAU,gBAAgB,CAAC;AACjD,QAAM,EAAE,UAAU,MAAM,aAAa,MAAM,IAAI,MAAM;AAAA,IACnD;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,WAAW,KAAK,QAAQ;AAC9B,QAAM,SAAS,WAAW,cAAc;AACxC,QAAM,QAAQ,WAAW,cAAc;AACvC,QAAM,EAAE,YAAY,QAAQ,IAAI;AAChC,QAAM,EAAE,UAAU,IAAI,cAAc,IAAI;AACxC,QAAMD,KAAI,CAAC,QAAQ;AACnB,QAAMC,KAAI,CAAC,SAAS;AACpB,QAAM,eAAe,KAAK,QAAQ,IAAI;AACtC,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,OAAO,UAAU,IAAI,MAAM;AACjC,UAAQ,SAAS,QAAQ;AACzB,QAAM,WAAW,GAAG,KAAK,uBAAuBD,IAAGC,IAAG,OAAO,QAAQ,CAAC,GAAG,OAAO;AAChF,QAAM,aAAa,KAAK,IAAI,OAAO,KAAK,KAAK;AAC7C,QAAM,cAAc,SAAS,KAAK,SAAS;AAC3C,QAAM,YAAY,GAAG,UAAU,CAAC,aAAa,GAAG,CAAC,cAAc,GAAG,YAAY,aAAa;AAAA,IACzF,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,YAAY,SAAS,OAAO,MAAM,UAAU,cAAc,EAAE,KAAK,SAAS,aAAa;AAC7F,QAAM,aAAa,SAAS,OAAO,MAAM,SAAS;AAClD,MAAI,KAAK,MAAM;AACb,UAAM,WAAW,SAAS,OAAO,GAAG;AACpC,aAAS;AAAA,MACP,MAAM,MAAM,WAAW,KAAK,MAAM;AAAA,QAChC,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,gBAAgB;AAAA,MAClB,CAAC,CAAC;AAAA,IACJ;AACA,UAAM,WAAW,SAAS,KAAK,EAAE,QAAQ;AACzC,UAAM,YAAY,SAAS;AAC3B,UAAM,aAAa,SAAS;AAC5B,UAAM,QAAQ,SAAS;AACvB,UAAM,QAAQ,SAAS;AACvB,aAAS;AAAA,MACP;AAAA,MACA,aAAa,CAAC,YAAY,IAAI,KAAK,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,QAAQ,CAAC,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,KAAK;AAAA,IAC9K;AACA,aAAS,KAAK,SAAS,UAAU,UAAU,IAAI,QAAQ,KAAK,UAAU,GAAG;AAAA,EAC3E;AACA,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,WAAW,CAAC,cAAc,IAAI,cAAc,IAAI,KAAK,MAAM;AAAA,EAC3H;AACA,YAAU;AAAA,IACR;AAAA,IACA,aAAa,CAAC,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,CAAC,KAAK,SAAS,IAAI,eAAe,CAAC;AAAA,EACvG;AACA,mBAAiB,MAAM,UAAU;AACjC,OAAK,YAAY,SAAS,OAAO;AAC/B,QAAI,KAAK,wBAAwB,MAAM,KAAK;AAC5C,QAAI,CAAC,KAAK,OAAO;AACf,aAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,IAC3C;AACA,UAAM,KAAK,KAAK,KAAK;AACrB,UAAM,KAAK,KAAK,KAAK;AACrB,UAAM,aAAa,KAAK,UAAU;AAClC,QAAI,SAAS,CAAC;AACd,QAAI,UAAU;AACZ,eAAS;AAAA,QACP,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QAC9E,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QACzE,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QACzE,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,MAChF;AAAA,IACF,OAAO;AACL,eAAS;AAAA,QACP,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QACrD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QAC1D,EAAE,GAAG,KAAK,KAAK,QAAQ,IAAI,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACrD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QAC1D,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,MACvD;AAAA,IACF;AACA,UAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,aAAa,aAAa;AAIjC,eAAe,WAAW,QAAQ,MAAM,EAAE,QAAQ,EAAE,gBAAgB,UAAU,EAAE,GAAG;AACjF,QAAM,EAAE,YAAY,IAAI,cAAc,IAAI;AAC1C,OAAK,aAAa;AAClB,QAAM,cAAc,KAAK,eAAe;AACxC,QAAM,aAAa,KAAK,cAAc;AACtC,QAAM,WAAW,KAAK,IAAI,aAAa,UAAU;AACjD,QAAM,eAAe,uCAAW;AAChC,OAAK,QAAQ,KAAK,IAAI,UAAU,gBAAgB,CAAC;AACjD,QAAM,EAAE,UAAU,MAAM,aAAa,MAAM,IAAI,MAAM;AAAA,IACnD;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,WAAW,KAAK,QAAQ;AAC9B,QAAM,SAAS,WAAW,cAAc;AACxC,QAAM,QAAQ,WAAW,cAAc;AACvC,QAAM,EAAE,YAAY,QAAQ,IAAI;AAChC,QAAM,EAAE,UAAU,IAAI,cAAc,IAAI;AACxC,QAAMD,KAAI,CAAC,QAAQ;AACnB,QAAMC,KAAI,CAAC,SAAS;AACpB,QAAM,eAAe,KAAK,QAAQ,IAAI;AACtC,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,OAAO,UAAU,IAAI,MAAM;AACjC,UAAQ,SAAS,QAAQ;AACzB,QAAM,WAAW,GAAG,KAAK,uBAAuBD,IAAGC,IAAG,OAAO,QAAQ,GAAG,GAAG,OAAO;AAClF,QAAM,aAAa,KAAK,IAAI,OAAO,KAAK,KAAK;AAC7C,QAAM,cAAc,SAAS,KAAK,SAAS;AAC3C,QAAM,YAAY,GAAG,UAAU,CAAC,aAAa,GAAG,CAAC,cAAc,GAAG,YAAY,aAAa;AAAA,IACzF,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,YAAY,SAAS,OAAO,MAAM,UAAU,cAAc;AAChE,QAAM,aAAa,SAAS,OAAO,MAAM,SAAS;AAClD,MAAI,KAAK,MAAM;AACb,UAAM,WAAW,SAAS,OAAO,GAAG;AACpC,aAAS;AAAA,MACP,MAAM,MAAM,WAAW,KAAK,MAAM;AAAA,QAChC,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,gBAAgB;AAAA,MAClB,CAAC,CAAC;AAAA,IACJ;AACA,UAAM,WAAW,SAAS,KAAK,EAAE,QAAQ;AACzC,UAAM,YAAY,SAAS;AAC3B,UAAM,aAAa,SAAS;AAC5B,UAAM,QAAQ,SAAS;AACvB,UAAM,QAAQ,SAAS;AACvB,aAAS;AAAA,MACP;AAAA,MACA,aAAa,CAAC,YAAY,IAAI,KAAK,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,QAAQ,CAAC,KAAK,SAAS,IAAI,eAAe,IAAI,aAAa,IAAI,KAAK;AAAA,IAC9K;AACA,aAAS,KAAK,SAAS,UAAU,UAAU,IAAI,QAAQ,KAAK,UAAU,GAAG;AAAA,EAC3E;AACA,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,WAAW,CAAC,cAAc,IAAI,cAAc,IAAI,KAAK,MAAM;AAAA,EAC3H;AACA,YAAU;AAAA,IACR;AAAA,IACA,aAAa,CAAC,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,CAAC,KAAK,SAAS,IAAI,eAAe,CAAC;AAAA,EACvG;AACA,mBAAiB,MAAM,UAAU;AACjC,OAAK,YAAY,SAAS,OAAO;AAC/B,QAAI,KAAK,wBAAwB,MAAM,KAAK;AAC5C,QAAI,CAAC,KAAK,OAAO;AACf,aAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,IAC3C;AACA,UAAM,KAAK,KAAK,KAAK;AACrB,UAAM,KAAK,KAAK,KAAK;AACrB,UAAM,aAAa,KAAK,UAAU;AAClC,QAAI,SAAS,CAAC;AACd,QAAI,UAAU;AACZ,eAAS;AAAA,QACP,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QAC9E,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QACzE,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QACzE,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,MAChF;AAAA,IACF,OAAO;AACL,eAAS;AAAA,QACP,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QAC5C,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QACrD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QAC1D,EAAE,GAAG,KAAK,KAAK,QAAQ,IAAI,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACrD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,QAC1D,EAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,OAAO;AAAA,MACvD;AAAA,IACF;AACA,UAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,YAAY,YAAY;AAI/B,eAAe,YAAY,QAAQ,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG;AAClE,QAAM,MAAM,IAAI,MAAM;AACtB,MAAI,OAAM,6BAAM,QAAO;AACvB,QAAM,IAAI,OAAO;AACjB,QAAM,oBAAoB,OAAO,IAAI,aAAa,SAAS,EAAE,QAAQ,MAAM,EAAE,CAAC;AAC9E,QAAM,qBAAqB,OAAO,IAAI,cAAc,SAAS,EAAE,QAAQ,MAAM,EAAE,CAAC;AAChF,OAAK,mBAAmB,oBAAoB;AAC5C,QAAM,EAAE,YAAY,IAAI,cAAc,IAAI;AAC1C,OAAK,aAAa;AAClB,QAAM,eAAe,uCAAW;AAChC,OAAK,eAAe,uCAAW;AAC/B,QAAM,gBAAgB,KAAK;AAAA,IACzB,KAAK,QAAQ,gBAAgB,IAAI;AAAA,KACjC,6BAAM,eAAc;AAAA,EACtB;AACA,QAAM,aAAa,KAAK,eAAe,QAAO,6BAAM,eAAc,KAAK,cAAc,KAAK,mBAAmB,gBAAgB;AAC7H,QAAM,cAAc,KAAK,eAAe,OAAO,aAAa,KAAK,oBAAmB,6BAAM,gBAAe;AACzG,OAAK,QAAQ,KAAK,IAAI,YAAY,gBAAgB,CAAC;AACnD,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,qBAAqB;AACvF,QAAM,WAAW,KAAK,QAAQ;AAC9B,QAAMD,KAAI,CAAC,aAAa;AACxB,QAAMC,KAAI,CAAC,cAAc;AACzB,QAAM,eAAe,KAAK,QAAQ,IAAI;AACtC,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,YAAY,GAAG,UAAUD,IAAGC,IAAG,YAAY,aAAa,OAAO;AACrE,QAAM,aAAa,KAAK,IAAI,YAAY,KAAK,KAAK;AAClD,QAAM,cAAc,cAAc,KAAK,SAAS;AAChD,QAAM,YAAY,GAAG,UAAU,CAAC,aAAa,GAAG,CAAC,cAAc,GAAG,YAAY,aAAa;AAAA,IACzF,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,YAAY,SAAS,OAAO,MAAM,WAAW,cAAc;AACjE,QAAM,aAAa,SAAS,OAAO,MAAM,SAAS;AAClD,MAAI,KAAK,KAAK;AACZ,UAAM,QAAQ,SAAS,OAAO,OAAO;AACrC,UAAM,KAAK,QAAQ,KAAK,GAAG;AAC3B,UAAM,KAAK,SAAS,UAAU;AAC9B,UAAM,KAAK,UAAU,WAAW;AAChC,UAAM,KAAK,uBAAuB,MAAM;AACxC,UAAM;AAAA,MACJ;AAAA,MACA,aAAa,CAAC,aAAa,CAAC,IAAI,WAAW,cAAc,IAAI,cAAc,CAAC,cAAc,CAAC;AAAA,IAC7F;AAAA,EACF;AACA,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,WAAW,CAAC,cAAc,IAAI,KAAK,SAAS,IAAI,eAAe,IAAI,cAAc,IAAI,KAAK,SAAS,IAAI,eAAe,CAAC;AAAA,EACvL;AACA,YAAU;AAAA,IACR;AAAA,IACA,aAAa,CAAC,IAAI,WAAW,KAAK,SAAS,IAAI,eAAe,IAAI,CAAC,KAAK,SAAS,IAAI,eAAe,CAAC;AAAA,EACvG;AACA,mBAAiB,MAAM,UAAU;AACjC,OAAK,YAAY,SAAS,OAAO;AAC/B,QAAI,KAAK,wBAAwB,MAAM,KAAK;AAC5C,QAAI,CAAC,KAAK,OAAO;AACf,aAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,IAC3C;AACA,UAAM,KAAK,KAAK,KAAK;AACrB,UAAM,KAAK,KAAK,KAAK;AACrB,UAAM,aAAa,KAAK,UAAU;AAClC,QAAI,SAAS,CAAC;AACd,QAAI,UAAU;AACZ,eAAS;AAAA,QACP,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QAC9E,EAAE,GAAG,KAAK,aAAa,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QAC9E,EAAE,GAAG,KAAK,aAAa,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,aAAa,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,aAAa,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,QAC9E,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,KAAK,SAAS,aAAa;AAAA,MAChF;AAAA,IACF,OAAO;AACL,eAAS;AAAA,QACP,EAAE,GAAG,KAAK,aAAa,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,aAAa,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,aAAa,GAAG,GAAG,KAAK,aAAa,IAAI,YAAY;AAAA,QAC/D,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,YAAY;AAAA,QAC/D,EAAE,GAAG,KAAK,KAAK,QAAQ,IAAI,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACrD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,EAAE;AAAA,QACjD,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,aAAa,IAAI,YAAY;AAAA,QAC/D,EAAE,GAAG,KAAK,aAAa,GAAG,GAAG,KAAK,aAAa,IAAI,YAAY;AAAA,MACjE;AAAA,IACF;AACA,UAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,aAAa,aAAa;AAIjC,eAAe,cAAc,QAAQ,MAAM;AACzC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAMC,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,IAAG,6BAAM,UAAS,CAAC;AACzE,QAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,IAAG,6BAAM,WAAU,CAAC;AAC3E,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAGD,IAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAGA,KAAI,IAAIC,KAAI,GAAG,GAAG,CAACA,GAAE;AAAA,IAC1B,EAAE,GAAG,KAAKA,KAAI,GAAG,GAAG,CAACA,GAAE;AAAA,EACzB;AACA,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,WAAW,qBAAqB,MAAM;AAC5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,cAAU,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAKC,KAAI,CAAC,GAAG;AAC7G,QAAI,WAAW;AACb,cAAQ,KAAK,SAAS,SAAS;AAAA,IACjC;AAAA,EACF,OAAO;AACL,cAAU,mBAAmB,UAAUD,IAAGC,IAAG,MAAM;AAAA,EACrD;AACA,MAAI,YAAY;AACd,YAAQ,KAAK,SAAS,UAAU;AAAA,EAClC;AACA,OAAK,QAAQD;AACb,OAAK,SAASC;AACd,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,EACtD;AACA,SAAO;AACT;AACA,OAAO,eAAe,eAAe;AAIrC,eAAe,SAAS,QAAQ,MAAM,SAAS;AAC7C,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAM,aAAa,KAAK,IAAI,KAAK,QAAQ,QAAQ,gBAAgB,IAAG,6BAAM,UAAS,CAAC;AACpF,QAAM,cAAc,KAAK,IAAI,KAAK,SAAS,QAAQ,gBAAgB,IAAG,6BAAM,WAAU,CAAC;AACvF,QAAMH,KAAI,CAAC,aAAa;AACxB,QAAMC,KAAI,CAAC,cAAc;AACzB,MAAI;AACJ,MAAI,EAAE,IAAI,GAAG,IAAI;AACjB,QAAM,EAAE,UAAU,IAAI;AACtB,OAAI,mCAAS,OAAM,QAAQ,IAAI;AAC7B,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AACA,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,WAAW,kBAAkB,MAAM,CAAC,CAAC;AAC3C,UAAM,YAAY,MAAM,KAAK,GAAG,KAAK,uBAAuBD,IAAGC,IAAG,YAAY,aAAa,MAAM,CAAC,GAAG,QAAQ,IAAI,GAAG,UAAUD,IAAGC,IAAG,YAAY,aAAa,QAAQ;AACrK,YAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AACvD,UAAM,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AAAA,EAC3F,OAAO;AACL,YAAQ,SAAS,OAAO,QAAQ,cAAc;AAC9C,UAAM,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,MAAM,oBAAoB,EAAE,CAAC,EAAE,KAAK,MAAM,oBAAoB,EAAE,CAAC,EAAE,KAAK,KAAKD,EAAC,EAAE,KAAK,KAAKC,EAAC,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,UAAU,WAAW;AAAA,EAC/N;AACA,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AACT;AACA,OAAO,UAAU,UAAU;AAG3B,eAAe,UAAU,QAAQ,MAAM;AACrC,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,OAAO;AACzE,QAAM,QAAQ,SAAS,OAAO,QAAQ,cAAc;AACpD,QAAM,aAAa;AACnB,QAAM,cAAc;AACpB,QAAM,KAAK,SAAS,UAAU,EAAE,KAAK,UAAU,WAAW;AAC1D,WAAS,KAAK,SAAS,iBAAiB;AACxC,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,EAAE,KAAK,QAAQ,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAClH;AACA,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AACT;AACA,OAAO,WAAW,WAAW;AAI7B,eAAe,UAAU,QAAQ,MAAM;AACrC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAMC,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAI,6BAAM,UAAS,CAAC;AACrE,QAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAI,6BAAM,WAAU,CAAC;AACvE,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAGD,KAAI,IAAIC,KAAI,GAAG,GAAG,EAAE;AAAA,IACzB,EAAE,GAAGD,IAAG,GAAG,CAACC,GAAE;AAAA,IACd,EAAE,GAAG,EAAE,IAAIA,MAAK,GAAG,GAAG,CAACA,GAAE;AAAA,EAC3B;AACA,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,WAAW,qBAAqB,MAAM;AAC5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,cAAU,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAKC,KAAI,CAAC,GAAG;AAC7G,QAAI,WAAW;AACb,cAAQ,KAAK,SAAS,SAAS;AAAA,IACjC;AAAA,EACF,OAAO;AACL,cAAU,mBAAmB,UAAUD,IAAGC,IAAG,MAAM;AAAA,EACrD;AACA,MAAI,YAAY;AACd,YAAQ,KAAK,SAAS,UAAU;AAAA,EAClC;AACA,OAAK,QAAQD;AACb,OAAK,SAASC;AACd,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,EACtD;AACA,SAAO;AACT;AACA,OAAO,WAAW,WAAW;AAI7B,eAAe,WAAW,QAAQ,MAAM;AACtC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAMD,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAI,6BAAM,UAAS,CAAC;AACrE,QAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAI,6BAAM,WAAU,CAAC;AACvE,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,KAAKA,KAAI,GAAG,GAAG,EAAE;AAAA,IACtB,EAAE,GAAGD,IAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAGA,KAAI,IAAIC,KAAI,GAAG,GAAG,CAACA,GAAE;AAAA,IAC1B,EAAE,GAAG,GAAG,GAAG,CAACA,GAAE;AAAA,EAChB;AACA,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,WAAW,qBAAqB,MAAM;AAC5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,cAAU,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAKC,KAAI,CAAC,GAAG;AAC7G,QAAI,WAAW;AACb,cAAQ,KAAK,SAAS,SAAS;AAAA,IACjC;AAAA,EACF,OAAO;AACL,cAAU,mBAAmB,UAAUD,IAAGC,IAAG,MAAM;AAAA,EACrD;AACA,MAAI,YAAY;AACd,YAAQ,KAAK,SAAS,UAAU;AAAA,EAClC;AACA,OAAK,QAAQD;AACb,OAAK,SAASC;AACd,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,EACtD;AACA,SAAO;AACT;AACA,OAAO,YAAY,YAAY;AAI/B,SAAS,cAAc,QAAQ,MAAM;AACnC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,QAAQ;AACb,OAAK,aAAa;AAClB,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACxG,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,QAAQ,KAAK,IAAI,KAAI,6BAAM,UAAS,CAAC;AAC3C,QAAM,SAAS,KAAK,IAAI,KAAI,6BAAM,WAAU,CAAC;AAC7C,QAAM,MAAM;AACZ,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,OAAO,GAAG,EAAE;AAAA,IACjB,EAAE,GAAG,GAAG,GAAG,SAAS,MAAM,EAAE;AAAA,IAC5B,EAAE,GAAG,QAAQ,IAAI,KAAK,GAAG,SAAS,MAAM,EAAE;AAAA,IAC1C,EAAE,GAAG,GAAG,GAAG,IAAI,OAAO;AAAA,IACtB,EAAE,GAAG,OAAO,GAAG,SAAS,MAAM,EAAE;AAAA,IAChC,EAAE,GAAG,IAAI,KAAK,GAAG,SAAS,MAAM,EAAE;AAAA,EACpC;AACA,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,WAAW,GAAG,KAAK,UAAU,OAAO;AAC1C,QAAM,iBAAiB,SAAS,OAAO,MAAM,UAAU,cAAc;AACrE,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,mBAAe,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EAC1D;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,mBAAe,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAC3D;AACA,iBAAe,KAAK,aAAa,cAAc,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG;AACtE,mBAAiB,MAAM,cAAc;AACrC,OAAK,YAAY,SAAS,OAAO;AAC/B,QAAI,KAAK,2BAA2B,MAAM,KAAK;AAC/C,UAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,eAAe,eAAe;AAIrC,IAAI,uBAAuC,OAAO,CAACH,IAAGC,IAAG,OAAO,QAAQ,IAAI,IAAI,gBAAgB;AAC9F,SAAO;AAAA,IACL,IAAID,EAAC,IAAIC,KAAI,EAAE;AAAA,IACf,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK;AAAA,IAC3B,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK;AAAA,IAC5B,MAAM,MAAM;AAAA,IACZ,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK;AAAA,IAC3B,MAAM,CAAC,MAAM;AAAA,IACb,IAAID,EAAC,IAAIC,KAAI,KAAK,WAAW;AAAA,IAC7B,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK;AAAA,EAC7B,EAAE,KAAK,GAAG;AACZ,GAAG,qBAAqB;AACxB,IAAI,4BAA4C,OAAO,CAACD,IAAGC,IAAG,OAAO,QAAQ,IAAI,IAAI,gBAAgB;AACnG,SAAO;AAAA,IACL,IAAID,EAAC,IAAIC,KAAI,EAAE;AAAA,IACf,IAAID,KAAI,KAAK,IAAIC,KAAI,EAAE;AAAA,IACvB,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK;AAAA,IAC5B,MAAM,MAAM;AAAA,IACZ,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK;AAAA,IAC3B,MAAM,CAAC,MAAM;AAAA,IACb,IAAID,EAAC,IAAIC,KAAI,KAAK,WAAW;AAAA,IAC7B,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK;AAAA,EAC7B,EAAE,KAAK,GAAG;AACZ,GAAG,0BAA0B;AAC7B,IAAI,4BAA4C,OAAO,CAACD,IAAGC,IAAG,OAAO,QAAQ,IAAI,OAAO;AACtF,SAAO,CAAC,IAAID,KAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,EAAE,KAAK,GAAG;AACvF,GAAG,0BAA0B;AAC7B,eAAe,cAAc,QAAQ,MAAM;AACzC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAME,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,IAAI,KAAK,SAAS,CAAC;AACpE,QAAM,KAAKA,KAAI;AACf,QAAM,KAAK,MAAM,MAAMA,KAAI;AAC3B,QAAMC,KAAI,KAAK,IAAI,KAAK,SAAS,MAAM,KAAK,WAAW,IAAI,KAAK,UAAU,CAAC;AAC3E,QAAM,cAAcA,KAAI;AACxB,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,gBAAgB,0BAA0B,GAAG,GAAGD,IAAGC,IAAG,IAAI,IAAI,WAAW;AAC/E,UAAM,gBAAgB,0BAA0B,GAAG,IAAID,IAAGC,IAAG,IAAI,EAAE;AACnE,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,YAAY,GAAG,KAAK,eAAe,OAAO;AAChD,UAAM,YAAY,GAAG,KAAK,eAAe,OAAO;AAChD,UAAM,cAAc,SAAS,OAAO,MAAM,WAAW,cAAc;AACnE,gBAAY,KAAK,SAAS,MAAM;AAChC,gBAAY,SAAS,OAAO,MAAM,WAAW,cAAc;AAC3D,cAAU,KAAK,SAAS,uBAAuB;AAC/C,QAAI,WAAW;AACb,gBAAU,KAAK,SAAS,SAAS;AAAA,IACnC;AAAA,EACF,OAAO;AACL,UAAM,WAAW,qBAAqB,GAAG,GAAGD,IAAGC,IAAG,IAAI,IAAI,WAAW;AACrE,gBAAY,SAAS,OAAO,QAAQ,cAAc,EAAE,KAAK,KAAK,QAAQ,EAAE,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC,EAAE,KAAK,SAAS,UAAU;AAAA,EACvL;AACA,YAAU,KAAK,kBAAkB,EAAE;AACnC,YAAU,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAK,EAAEC,KAAI,IAAI,GAAG,GAAG;AACpE,mBAAiB,MAAM,SAAS;AAChC,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,EAAE,KAAK,QAAQ,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,KAAK,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EACvH;AACA,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,KAAK,MAAM,KAAK;AAC9C,UAAMH,KAAI,IAAI,KAAK,KAAK,KAAK;AAC7B,QAAI,MAAM,MAAM,KAAK,IAAIA,EAAC,KAAK,KAAK,SAAS,KAAK,KAAK,KAAK,IAAIA,EAAC,MAAM,KAAK,SAAS,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE,KAAK,KAAK,UAAU,KAAK,IAAI,KAAK;AAC7J,UAAIC,KAAI,KAAK,MAAM,IAAID,KAAIA,MAAK,KAAK;AACrC,UAAIC,KAAI,GAAG;AACT,QAAAA,KAAI,KAAK,KAAKA,EAAC;AAAA,MACjB;AACA,MAAAA,KAAI,KAAKA;AACT,UAAI,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG;AAC/B,QAAAA,KAAI,CAACA;AAAA,MACP;AACA,UAAI,KAAKA;AAAA,IACX;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,eAAe,eAAe;AAIrC,eAAe,mBAAmB,QAAQ,MAAM;AAC9C,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAMC,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,IAAG,6BAAM,UAAS,CAAC;AACzE,QAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,IAAG,6BAAM,WAAU,CAAC;AAC3E,QAAM,gBAAgBA,KAAI;AAC1B,QAAM,SAASA,KAAI;AACnB,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,CAACD,KAAI,IAAIA,KAAI,IAAI,KAAK,GAAG,CAAC,SAAS,EAAE;AAAA,IAC1C,EAAE,GAAG,CAACA,KAAI,IAAIA,KAAI,IAAI,KAAK,GAAG,SAAS,EAAE;AAAA,IACzC,GAAG;AAAA,MACD,CAACA,KAAI,IAAIA,KAAI,IAAI;AAAA,MACjB,SAAS;AAAA,MACTA,KAAI,IAAIA,KAAI,IAAI;AAAA,MAChB,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACA,EAAE,GAAGA,KAAI,IAAIA,KAAI,IAAI,KAAK,GAAG,CAAC,SAAS,EAAE;AAAA,IACzC,EAAE,GAAG,CAACA,KAAI,IAAIA,KAAI,IAAI,KAAK,GAAG,CAAC,SAAS,EAAE;AAAA,IAC1C,EAAE,GAAG,CAACA,KAAI,GAAG,GAAG,CAAC,SAAS,EAAE;AAAA,IAC5B,EAAE,GAAG,CAACA,KAAI,GAAG,GAAG,SAAS,IAAI,IAAI;AAAA,IACjC,EAAE,GAAG,CAACA,KAAI,GAAG,GAAG,CAAC,SAAS,EAAE;AAAA,EAC9B;AACA,QAAM,OAAO,GAAG;AAAA,IACd,OAAO,IAAI,CAACL,OAAM,CAACA,GAAE,GAAGA,GAAE,CAAC,CAAC;AAAA,IAC5B;AAAA,EACF;AACA,QAAM,eAAe,SAAS,OAAO,MAAM,MAAM,cAAc;AAC/D,eAAa,KAAK,SAAS,uBAAuB;AAClD,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,iBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,iBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AACA,eAAa,KAAK,aAAa,eAAe,CAAC,gBAAgB,CAAC,GAAG;AACnE,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAACK,KAAI,KAAK,KAAK,WAAW,KAAKA,KAAI,IAAI,MAAM,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACC,KAAI,KAAK,KAAK,WAAW,KAAK,gBAAgB,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC5K;AACA,mBAAiB,MAAM,YAAY;AACnC,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,oBAAoB,oBAAoB;AAI/C,eAAe,UAAU,QAAQ,MAAM;AACrC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAMD,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,IAAG,6BAAM,UAAS,CAAC;AACzE,QAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,IAAG,6BAAM,WAAU,CAAC;AAC3E,QAAM,aAAa;AACnB,QAAMH,KAAI,CAACE,KAAI;AACf,QAAMD,KAAI,CAACE,KAAI;AACf,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAM,kBAAkB;AAAA,IACtB,EAAE,GAAGH,KAAI,YAAY,GAAGC,KAAI,WAAW;AAAA,IACvC,EAAE,GAAGD,KAAI,YAAY,GAAGC,KAAIE,KAAI,WAAW;AAAA,IAC3C,EAAE,GAAGH,KAAIE,KAAI,YAAY,GAAGD,KAAIE,KAAI,WAAW;AAAA,IAC/C,EAAE,GAAGH,KAAIE,KAAI,YAAY,GAAGD,KAAIE,GAAE;AAAA,IAClC,EAAE,GAAGH,KAAIE,IAAG,GAAGD,KAAIE,GAAE;AAAA,IACrB,EAAE,GAAGH,KAAIE,IAAG,GAAGD,KAAIE,KAAI,WAAW;AAAA,IAClC,EAAE,GAAGH,KAAIE,KAAI,YAAY,GAAGD,KAAIE,KAAI,WAAW;AAAA,IAC/C,EAAE,GAAGH,KAAIE,KAAI,YAAY,GAAGD,KAAI,WAAW;AAAA,IAC3C,EAAE,GAAGD,KAAI,YAAY,GAAGC,KAAI,WAAW;AAAA,IACvC,EAAE,GAAGD,KAAI,YAAY,GAAAC,GAAE;AAAA,IACvB,EAAE,GAAAD,IAAG,GAAAC,GAAE;AAAA,IACP,EAAE,GAAAD,IAAG,GAAGC,KAAI,WAAW;AAAA,EACzB;AACA,QAAM,kBAAkB;AAAA,IACtB,EAAE,GAAAD,IAAG,GAAGC,KAAI,WAAW;AAAA,IACvB,EAAE,GAAGD,KAAIE,KAAI,YAAY,GAAGD,KAAI,WAAW;AAAA,IAC3C,EAAE,GAAGD,KAAIE,KAAI,YAAY,GAAGD,KAAIE,GAAE;AAAA,IAClC,EAAE,GAAGH,KAAIE,IAAG,GAAGD,KAAIE,GAAE;AAAA,IACrB,EAAE,GAAGH,KAAIE,IAAG,GAAAD,GAAE;AAAA,IACd,EAAE,GAAAD,IAAG,GAAAC,GAAE;AAAA,EACT;AACA,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,YAAY,qBAAqB,eAAe;AACtD,QAAM,YAAY,GAAG,KAAK,WAAW,OAAO;AAC5C,QAAM,YAAY,qBAAqB,eAAe;AACtD,QAAM,YAAY,GAAG,KAAK,WAAW,EAAE,GAAG,SAAS,MAAM,OAAO,CAAC;AACjE,QAAM,aAAa,SAAS,OAAO,MAAM,WAAW,cAAc;AAClE,aAAW,OAAO,MAAM,WAAW,cAAc;AACjD,aAAW,KAAK,SAAS,uBAAuB;AAChD,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,eAAW,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACtD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,eAAW,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACvD;AACA,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,EAAE,KAAK,QAAQ,KAAK,cAAc,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,KAAK,cAAc,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC5I;AACA,mBAAiB,MAAM,UAAU;AACjC,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,QAAQ,MAAM,iBAAiB,KAAK;AAClE,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,WAAW,WAAW;AAI7B,eAAe,wBAAwB,QAAQ,MAAM;AACnD,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAMC,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,IAAG,6BAAM,UAAS,CAAC;AACzE,QAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,IAAG,6BAAM,WAAU,CAAC;AAC3E,QAAM,gBAAgBA,KAAI;AAC1B,QAAM,SAASA,KAAI;AACnB,QAAMH,KAAI,CAACE,KAAI;AACf,QAAMD,KAAI,CAAC,SAAS;AACpB,QAAM,aAAa;AACnB,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,aAAa;AAAA,IACjBD,KAAI;AAAA,IACJC,KAAI,SAAS;AAAA,IACbD,KAAIE,KAAI;AAAA,IACRD,KAAI,SAAS;AAAA,IACb;AAAA,IACA;AAAA,EACF;AACA,QAAM,gBAAgB,yCAAa,WAAW,SAAS;AACvD,QAAM,kBAAkB;AAAA,IACtB,EAAE,GAAGD,KAAI,YAAY,GAAGC,KAAI,WAAW;AAAA,IACvC,EAAE,GAAGD,KAAI,YAAY,GAAGC,KAAI,SAAS,WAAW;AAAA,IAChD,GAAG;AAAA,IACH,EAAE,GAAGD,KAAIE,KAAI,YAAY,GAAG,cAAc,IAAI,WAAW;AAAA,IACzD,EAAE,GAAGF,KAAIE,IAAG,GAAG,cAAc,IAAI,WAAW;AAAA,IAC5C,EAAE,GAAGF,KAAIE,IAAG,GAAG,cAAc,IAAI,IAAI,WAAW;AAAA,IAChD,EAAE,GAAGF,KAAIE,KAAI,YAAY,GAAG,cAAc,IAAI,IAAI,WAAW;AAAA,IAC7D,EAAE,GAAGF,KAAIE,KAAI,YAAY,GAAGD,KAAI,WAAW;AAAA,IAC3C,EAAE,GAAGD,KAAI,YAAY,GAAGC,KAAI,WAAW;AAAA,IACvC,EAAE,GAAGD,KAAI,YAAY,GAAAC,GAAE;AAAA,IACvB,EAAE,GAAAD,IAAG,GAAAC,GAAE;AAAA,IACP,EAAE,GAAAD,IAAG,GAAGC,KAAI,WAAW;AAAA,EACzB;AACA,QAAM,kBAAkB;AAAA,IACtB,EAAE,GAAAD,IAAG,GAAGC,KAAI,WAAW;AAAA,IACvB,EAAE,GAAGD,KAAIE,KAAI,YAAY,GAAGD,KAAI,WAAW;AAAA,IAC3C,EAAE,GAAGD,KAAIE,KAAI,YAAY,GAAG,cAAc,IAAI,WAAW;AAAA,IACzD,EAAE,GAAGF,KAAIE,IAAG,GAAG,cAAc,IAAI,WAAW;AAAA,IAC5C,EAAE,GAAGF,KAAIE,IAAG,GAAAD,GAAE;AAAA,IACd,EAAE,GAAAD,IAAG,GAAAC,GAAE;AAAA,EACT;AACA,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,YAAY,qBAAqB,eAAe;AACtD,QAAM,YAAY,GAAG,KAAK,WAAW,OAAO;AAC5C,QAAM,YAAY,qBAAqB,eAAe;AACtD,QAAM,YAAY,GAAG,KAAK,WAAW,OAAO;AAC5C,QAAM,QAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AAC7D,QAAM,OAAO,MAAM,SAAS;AAC5B,QAAM,KAAK,SAAS,uBAAuB;AAC3C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,UAAM,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACjD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,UAAM,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAClD;AACA,QAAM,KAAK,aAAa,eAAe,CAAC,gBAAgB,CAAC,GAAG;AAC5D,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,EAAE,KAAK,QAAQ,KAAK,cAAc,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,KAAK,aAAa,gBAAgB,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAChK;AACA,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,QAAQ,MAAM,iBAAiB,KAAK;AAClE,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,yBAAyB,yBAAyB;AAIzD,eAAe,KAAK,QAAQ,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;AAt0FlE;AAu0FE,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,gBAAgB,KAAK,mBAAiB,eAAU,EAAE,cAAZ,mBAAuB,gBAAe;AAClF,MAAI,CAAC,eAAe;AAClB,SAAK,cAAc;AAAA,EACrB;AACA,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAM,aAAa,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,IAAG,6BAAM,UAAS,CAAC;AAClF,QAAM,cAAc,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,IAAG,6BAAM,WAAU,CAAC;AACrF,QAAMD,KAAI,CAAC,aAAa;AACxB,QAAMC,KAAI,CAAC,cAAc;AACzB,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM;AAAA,IACtC,MAAM,eAAe;AAAA,IACrB,QAAQ,eAAe;AAAA,EACzB,CAAC;AACD,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,gBAAgB,GAAG,UAAUD,IAAGC,IAAG,YAAY,aAAa,OAAO;AACzE,QAAM,QAAQ,SAAS,OAAO,MAAM,eAAe,cAAc;AACjE,QAAM,KAAK,SAAS,uBAAuB;AAC3C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,UAAM,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACjD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,UAAM,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAClD;AACA,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AACT;AACA,OAAO,MAAM,MAAM;AAInB,IAAI,yBAAyC,OAAO,CAACD,IAAGC,IAAG,SAAS;AAClE,SAAO;AAAA,IACL,IAAID,KAAI,OAAO,CAAC,IAAIC,EAAC;AAAA,IACrB,IAAID,KAAI,IAAI,IAAIC,KAAI,OAAO,CAAC;AAAA,IAC5B,IAAID,KAAI,OAAO,CAAC,IAAIC,KAAI,IAAI;AAAA,IAC5B,IAAID,EAAC,IAAIC,KAAI,OAAO,CAAC;AAAA,IACrB;AAAA,EACF,EAAE,KAAK,GAAG;AACZ,GAAG,wBAAwB;AAC3B,eAAe,SAAS,QAAQ,MAAM;AACpC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAMC,KAAI,KAAK,QAAQ,KAAK;AAC5B,QAAMC,KAAI,KAAK,SAAS,KAAK;AAC7B,QAAMC,KAAIF,KAAIC;AACd,QAAM,SAAS;AAAA,IACb,EAAE,GAAGC,KAAI,GAAG,GAAG,EAAE;AAAA,IACjB,EAAE,GAAGA,IAAG,GAAG,CAACA,KAAI,EAAE;AAAA,IAClB,EAAE,GAAGA,KAAI,GAAG,GAAG,CAACA,GAAE;AAAA,IAClB,EAAE,GAAG,GAAG,GAAG,CAACA,KAAI,EAAE;AAAA,EACpB;AACA,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,WAAW,uBAAuB,GAAG,GAAGA,EAAC;AAC/C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,cAAU,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACA,KAAI,CAAC,KAAKA,KAAI,CAAC,GAAG;AAC7G,QAAI,WAAW;AACb,cAAQ,KAAK,SAAS,SAAS;AAAA,IACjC;AAAA,EACF,OAAO;AACL,cAAU,mBAAmB,UAAUA,IAAGA,IAAG,MAAM;AAAA,EACrD;AACA,MAAI,YAAY;AACd,YAAQ,KAAK,SAAS,UAAU;AAAA,EAClC;AACA,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,OAAO;AAC/B,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,IAC/C;AACA,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,EACtD;AACA,SAAO;AACT;AACA,OAAO,UAAU,UAAU;AAI3B,eAAe,oBAAoB,QAAQ,MAAM;AAC/C,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAMF,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAI,6BAAM,UAAS,CAAC;AACrE,QAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAI,6BAAM,WAAU,CAAC;AACvE,QAAMH,KAAI,CAACE,KAAI;AACf,QAAMD,KAAI,CAACE,KAAI;AACf,QAAM,QAAQF,KAAI;AAClB,QAAM,SAAS;AAAA,IACb,EAAE,GAAGD,KAAI,OAAO,GAAAC,GAAE;AAAA,IAClB,EAAE,GAAAD,IAAG,GAAG,EAAE;AAAA,IACV,EAAE,GAAGA,KAAI,OAAO,GAAG,CAACC,GAAE;AAAA,IACtB,EAAE,GAAG,CAACD,IAAG,GAAG,CAACC,GAAE;AAAA,IACf,EAAE,GAAG,CAACD,IAAG,GAAAC,GAAE;AAAA,EACb;AACA,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,QAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,UAAQ,KAAK,SAAS,uBAAuB;AAC7C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAQ,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACnD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAQ,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACpD;AACA,UAAQ,KAAK,aAAa,aAAa,CAAC,QAAQ,CAAC,KAAK;AACtD,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,QAAQ,IAAI,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC5H;AACA,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,EACtD;AACA,SAAO;AACT;AACA,OAAO,qBAAqB,qBAAqB;AAKjD,eAAe,cAAc,QAAQ,MAAM;AAz9F3C;AA09FE,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,MAAI;AACJ,MAAI,CAAC,KAAK,YAAY;AACpB,cAAU;AAAA,EACZ,OAAO;AACL,cAAU,UAAU,KAAK;AAAA,EAC3B;AACA,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAC3F,QAAMS,KAAI,SAAS,OAAO,GAAG;AAC7B,QAAM,QAAQ,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,SAAS,UAAU;AAClF,QAAM,cAAc,KAAK;AACzB,QAAM,QAAQ,KAAK;AACnB,QAAM,QAAQ,MAAM,KAAK,EAAE,YAAY,MAAM,oBAAoB,OAAO,KAAK,YAAY,MAAM,IAAI,CAAC;AACpG,MAAI,OAAO,EAAE,OAAO,GAAG,QAAQ,EAAE;AACjC,MAAI,UAAS,sBAAW,MAAX,mBAAc,cAAd,mBAAyB,UAAU,GAAG;AACjD,UAAM,OAAO,MAAM,SAAS,CAAC;AAC7B,UAAM,MAAM,eAAQ,KAAK;AACzB,WAAO,KAAK,sBAAsB;AAClC,QAAI,KAAK,SAAS,KAAK,KAAK;AAC5B,QAAI,KAAK,UAAU,KAAK,MAAM;AAAA,EAChC;AACA,MAAI,KAAK,UAAU,WAAW;AAC9B,QAAM,WAAW,eAAe,CAAC;AACjC,QAAM,WAAW,MAAM,QAAQ;AAC/B,QAAM,QAAQ,MAAM,KAAK,EAAE;AAAA,IACzB,MAAM;AAAA,MACJ,SAAS,OAAO,SAAS,KAAK,OAAO,IAAI;AAAA,MACzC,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,QAAM,KAAK,eAAQ,KAAK;AACxB,SAAO,IAAI,sBAAsB;AACjC,KAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,KAAG,KAAK,UAAU,KAAK,MAAM;AAC7B,QAAM,eAAe,KAAK,WAAW,KAAK;AAC1C,iBAAQ,KAAK,EAAE;AAAA,IACb;AAAA,IACA,iBAAiB,KAAK,QAAQ,SAAS,QAAQ,KAAK,SAAS,QAAQ,KAAK,SAAS,KAAK,QAAQ,SAAS,SAAS,cAAc,KAAK;AAAA,EACvI;AACA,iBAAQ,KAAK,EAAE;AAAA,IACb;AAAA,IACA,iBAAiB,KAAK,QAAQ,SAAS,QAAQ,IAAI,EAAE,SAAS,QAAQ,KAAK,SAAS,KAAK;AAAA,EAC3F;AACA,SAAO,MAAM,KAAK,EAAE,QAAQ;AAC5B,QAAM;AAAA,IACJ;AAAA,IACA,eAAe,CAAC,KAAK,QAAQ,IAAI,QAAQ,CAAC,KAAK,SAAS,IAAI,cAAc,KAAK;AAAA,EACjF;AACA,QAAM,aAAa,KAAK,SAAS,KAAK,WAAW;AACjD,QAAM,cAAc,KAAK,UAAU,KAAK,WAAW;AACnD,QAAMV,KAAI,CAAC,KAAK,QAAQ,IAAI;AAC5B,QAAMC,KAAI,CAAC,KAAK,SAAS,IAAI;AAC7B,MAAI;AACJ,MAAI;AACJ,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,YAAY,GAAG;AAAA,MACnB,uBAAuBD,IAAGC,IAAG,YAAY,aAAa,KAAK,MAAM,CAAC;AAAA,MAClE;AAAA,IACF;AACA,UAAM,YAAY,GAAG;AAAA,MACnB,CAAC,KAAK,QAAQ,IAAI;AAAA,MAClB,CAAC,KAAK,SAAS,IAAI,cAAc,SAAS,SAAS;AAAA,MACnD,KAAK,QAAQ,IAAI;AAAA,MACjB,CAAC,KAAK,SAAS,IAAI,cAAc,SAAS,SAAS;AAAA,MACnD;AAAA,IACF;AACA,gBAAY,SAAS,OAAO,MAAM;AAChC,UAAI,MAAM,yBAAyB,SAAS;AAC5C,aAAO;AAAA,IACT,GAAG,cAAc;AACjB,YAAQ,SAAS,OAAO,MAAM;AAC5B,UAAI,MAAM,yBAAyB,SAAS;AAC5C,aAAO;AAAA,IACT,GAAG,cAAc;AAAA,EACnB,OAAO;AACL,YAAQS,GAAE,OAAO,QAAQ,cAAc;AACvC,gBAAYA,GAAE,OAAO,MAAM;AAC3B,UAAM,KAAK,SAAS,mBAAmB,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,KAAK,CAAC,KAAK,QAAQ,IAAI,WAAW,EAAE,KAAK,KAAK,CAAC,KAAK,SAAS,IAAI,WAAW,EAAE,KAAK,SAAS,KAAK,SAAS,KAAK,WAAW,EAAE,EAAE,KAAK,UAAU,KAAK,UAAU,KAAK,WAAW,EAAE;AACtP,cAAU,KAAK,SAAS,SAAS,EAAE,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,WAAW,EAAE,KAAK,MAAM,KAAK,QAAQ,IAAI,WAAW,EAAE,KAAK,MAAM,CAAC,KAAK,SAAS,IAAI,cAAc,SAAS,SAAS,WAAW,EAAE,KAAK,MAAM,CAAC,KAAK,SAAS,IAAI,cAAc,SAAS,SAAS,WAAW;AAAA,EAC5Q;AACA,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AACT;AACA,OAAO,eAAe,eAAe;AAGrC,eAAe,YAAY,QAAQ,MAAM;AACvC,QAAM,UAAU;AAAA,IACd,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,iBAAgB,6BAAM,YAAW,KAAK;AAAA,IACtC,iBAAgB,6BAAM,YAAW,KAAK;AAAA,EACxC;AACA,SAAO,SAAS,QAAQ,MAAM,OAAO;AACvC;AACA,OAAO,aAAa,aAAa;AAIjC,eAAe,cAAc,QAAQ,MAAM;AACzC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAM,eAAc,6BAAM,YAAW;AACrC,QAAMR,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,IAAG,6BAAM,UAAS,CAAC;AACzE,QAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,IAAG,6BAAM,WAAU,CAAC;AAC3E,QAAMH,KAAI,CAAC,KAAK,QAAQ,IAAI;AAC5B,QAAMC,KAAI,CAAC,KAAK,SAAS,IAAI;AAC7B,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,SAAS;AAAA,IACb,EAAE,GAAAD,IAAG,GAAAC,GAAE;AAAA,IACP,EAAE,GAAGD,KAAIE,KAAI,GAAG,GAAAD,GAAE;AAAA,IAClB,EAAE,GAAGD,KAAIE,KAAI,GAAG,GAAGD,KAAIE,GAAE;AAAA,IACzB,EAAE,GAAGH,KAAI,GAAG,GAAGC,KAAIE,GAAE;AAAA,IACrB,EAAE,GAAGH,KAAI,GAAG,GAAAC,GAAE;AAAA,IACd,EAAE,GAAAD,IAAG,GAAAC,GAAE;AAAA,IACP,EAAE,GAAAD,IAAG,GAAGC,KAAIE,GAAE;AAAA,EAChB;AACA,QAAM,YAAY,GAAG;AAAA,IACnB,OAAO,IAAI,CAACN,OAAM,CAACA,GAAE,GAAGA,GAAE,CAAC,CAAC;AAAA,IAC5B;AAAA,EACF;AACA,QAAM,QAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AAC7D,QAAM,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AACzF,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,UAAM,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAClD;AACA,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,UAAM,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EAClD;AACA,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAACK,KAAI,IAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACC,KAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC1I;AACA,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AACT;AACA,OAAO,eAAe,eAAe;AAIrC,eAAe,WAAW,QAAQ,MAAM;AACtC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAMD,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,IAAG,6BAAM,UAAS,CAAC;AACzE,QAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,IAAG,6BAAM,WAAU,CAAC;AAC3E,QAAMH,KAAI,CAACE,KAAI;AACf,QAAMD,KAAI,CAACE,KAAI;AACf,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,SAAS;AAAA,IACb,EAAE,GAAAH,IAAG,GAAAC,GAAE;AAAA,IACP,EAAE,GAAAD,IAAG,GAAGC,KAAIE,GAAE;AAAA,IACd,EAAE,GAAGH,KAAIE,IAAG,GAAGD,KAAIE,GAAE;AAAA,IACrB,EAAE,GAAGH,KAAIE,IAAG,GAAGD,KAAIE,KAAI,EAAE;AAAA,EAC3B;AACA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,QAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,UAAQ,KAAK,SAAS,uBAAuB;AAC7C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AACA,UAAQ,KAAK,aAAa,gBAAgBA,KAAI,CAAC,GAAG;AAClD,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAACD,KAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,CAACC,KAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EACvI;AACA,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,YAAY,YAAY;AAG/B,eAAe,YAAY,QAAQ,MAAM;AACvC,QAAM,UAAU;AAAA,IACd,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,iBAAgB,6BAAM,YAAW,KAAK;AAAA,IACtC,iBAAgB,6BAAM,YAAW,KAAK;AAAA,EACxC;AACA,SAAO,SAAS,QAAQ,MAAM,OAAO;AACvC;AACA,OAAO,aAAa,YAAY;AAIhC,eAAe,QAAQ,QAAQ,MAAM;AACnC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAMA,KAAI,KAAK,SAAS,KAAK;AAC7B,QAAMD,KAAI,KAAK,QAAQC,KAAI,IAAI,KAAK;AACpC,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,WAAW,uBAAuB,CAACD,KAAI,GAAG,CAACC,KAAI,GAAGD,IAAGC,IAAGA,KAAI,CAAC;AACnE,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,YAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AACvD,UAAM,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AAAA,EAC3F,OAAO;AACL,YAAQ,SAAS,OAAO,QAAQ,cAAc;AAC9C,UAAM,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,MAAMA,KAAI,CAAC,EAAE,KAAK,MAAMA,KAAI,CAAC,EAAE,KAAK,KAAK,CAACD,KAAI,CAAC,EAAE,KAAK,KAAK,CAACC,KAAI,CAAC,EAAE,KAAK,SAASD,EAAC,EAAE,KAAK,UAAUC,EAAC;AAAA,EAClL;AACA,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AACT;AACA,OAAO,SAAS,SAAS;AAGzB,eAAe,MAAM,QAAQ,MAAM;AACjC,QAAM,UAAU;AAAA,IACd,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,SAAS;AAAA,EACX;AACA,SAAO,SAAS,QAAQ,MAAM,OAAO;AACvC;AACA,OAAO,OAAO,OAAO;AAIrB,SAAS,SAAS,QAAQ,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;AAC9D,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,EAAE,WAAW,aAAa,WAAW,IAAI;AAC/C,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,cAAc,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAClG,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,YAAY,GAAG,OAAO,GAAG,GAAG,IAAI;AAAA,IACpC,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,aAAa;AAAA,EACf,CAAC;AACD,QAAM,YAAY,eAAe;AACjC,QAAM,iBAAiB,GAAG,OAAO,GAAG,GAAG,GAAG;AAAA,IACxC,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,WAAW;AAAA,EACb,CAAC;AACD,QAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,UAAQ,OAAO,MAAM,cAAc;AACnC,MAAI,WAAW;AACb,YAAQ,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACnD;AACA,MAAI,YAAY;AACd,YAAQ,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACpD;AACA,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,OAAO,MAAM,GAAG,KAAK;AAAA,EAChD;AACA,SAAO;AACT;AACA,OAAO,UAAU,UAAU;AAI3B,SAAS,WAAW,QAAQ,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;AAChE,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,cAAc,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAClG,MAAI;AACJ,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,YAAY,GAAG,OAAO,GAAG,GAAG,IAAI,eAAe,SAAS,CAAC;AAC/D,cAAU,SAAS,OAAO,MAAM,SAAS;AACzC,YAAQ,KAAK,SAAS,aAAa,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,EAAE,EAAE,KAAK,UAAU,EAAE;AAAA,EACvF,OAAO;AACL,cAAU,SAAS,OAAO,UAAU,cAAc;AAClD,YAAQ,KAAK,SAAS,aAAa,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,EAAE,EAAE,KAAK,UAAU,EAAE;AAAA,EACvF;AACA,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,OAAO,MAAM,GAAG,KAAK;AAAA,EAChD;AACA,SAAO;AACT;AACA,OAAO,YAAY,YAAY;AAI/B,eAAe,WAAW,QAAQ,MAAM;AACtC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAM,gBAAe,6BAAM,YAAW,KAAK;AAC3C,QAAMD,KAAI,KAAK,QAAQ,KAAK;AAC5B,QAAMC,KAAI,KAAK,SAAS,KAAK;AAC7B,QAAMH,KAAI,CAAC,KAAK,QAAQ,IAAI;AAC5B,QAAMC,KAAI,CAAC,KAAK,SAAS,IAAI;AAC7B,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAGC,IAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAGA,IAAG,GAAG,CAACC,GAAE;AAAA,IACd,EAAE,GAAG,GAAG,GAAG,CAACA,GAAE;AAAA,IACd,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,IAAI,GAAG,EAAE;AAAA,IACd,EAAE,GAAGD,KAAI,GAAG,GAAG,EAAE;AAAA,IACjB,EAAE,GAAGA,KAAI,GAAG,GAAG,CAACC,GAAE;AAAA,IAClB,EAAE,GAAG,IAAI,GAAG,CAACA,GAAE;AAAA,IACf,EAAE,GAAG,IAAI,GAAG,EAAE;AAAA,EAChB;AACA,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,YAAY,GAAG,UAAUH,KAAI,GAAGC,IAAGC,KAAI,IAAIC,IAAG,OAAO;AAC3D,UAAM,KAAK,GAAG,KAAKH,IAAGC,IAAGD,IAAGC,KAAIE,IAAG,OAAO;AAC1C,UAAM,KAAK,GAAG,KAAKH,KAAIE,IAAGD,IAAGD,KAAIE,IAAGD,KAAIE,IAAG,OAAO;AAClD,aAAS,OAAO,MAAM,IAAI,cAAc;AACxC,aAAS,OAAO,MAAM,IAAI,cAAc;AACxC,UAAM,QAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AAC7D,UAAM,EAAE,UAAU,IAAI;AACtB,UAAM,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC;AACzF,qBAAiB,MAAM,KAAK;AAAA,EAC9B,OAAO;AACL,UAAM,KAAK,mBAAmB,UAAUD,IAAGC,IAAG,MAAM;AACpD,QAAI,YAAY;AACd,SAAG,KAAK,SAAS,UAAU;AAAA,IAC7B;AACA,qBAAiB,MAAM,EAAE;AAAA,EAC3B;AACA,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,EACtD;AACA,SAAO;AACT;AACA,OAAO,YAAY,YAAY;AAI/B,eAAe,WAAW,QAAQ,MAAM;AACtC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAMD,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,IAAG,6BAAM,UAAS,CAAC;AACzE,QAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,IAAG,6BAAM,WAAU,CAAC;AAC3E,QAAMH,KAAI,CAACE,KAAI;AACf,QAAMD,KAAI,CAACE,KAAI;AACf,QAAM,WAAW,MAAMA;AACvB,QAAM,YAAY,MAAMA;AACxB,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAM,aAAa;AAAA,IACjB,EAAE,GAAGH,KAAI,WAAW,GAAG,GAAAC,GAAE;AAAA,IACzB,EAAE,GAAGD,KAAIE,KAAI,WAAW,GAAG,GAAAD,GAAE;AAAA,IAC7B,EAAE,GAAGD,KAAIE,KAAI,WAAW,GAAG,GAAGD,KAAIE,GAAE;AAAA,IACpC,EAAE,GAAGH,KAAI,WAAW,GAAG,GAAGC,KAAIE,GAAE;AAAA,EAClC;AACA,QAAM,YAAY;AAAA,IAChB,EAAE,GAAGH,KAAIE,KAAI,WAAW,GAAG,GAAGD,KAAIE,GAAE;AAAA,IACpC,EAAE,GAAGH,KAAIE,KAAI,WAAW,GAAG,GAAGD,KAAIE,GAAE;AAAA,IACpC,EAAE,GAAGH,KAAIE,KAAI,WAAW,GAAG,GAAGD,KAAIE,KAAI,UAAU;AAAA,EAClD;AACA,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,WAAW,qBAAqB,UAAU;AAChD,QAAM,WAAW,GAAG,KAAK,UAAU,OAAO;AAC1C,QAAM,UAAU,qBAAqB,SAAS;AAC9C,QAAM,UAAU,GAAG,KAAK,SAAS,EAAE,GAAG,SAAS,WAAW,QAAQ,CAAC;AACnE,QAAM,cAAc,SAAS,OAAO,MAAM,SAAS,cAAc;AACjE,cAAY,OAAO,MAAM,UAAU,cAAc;AACjD,cAAY,KAAK,SAAS,uBAAuB;AACjD,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,gBAAY,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACvD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,gBAAY,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACxD;AACA,mBAAiB,MAAM,WAAW;AAClC,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,QAAQ,MAAM,YAAY,KAAK;AAC7D,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,YAAY,YAAY;AAI/B,eAAe,yBAAyB,QAAQ,MAAM;AACpD,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAMD,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,IAAG,6BAAM,UAAS,CAAC;AACzE,QAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,IAAG,6BAAM,WAAU,CAAC;AAC3E,QAAM,gBAAgBA,KAAI;AAC1B,QAAM,WAAW,MAAMD;AACvB,QAAM,YAAY,MAAMC;AACxB,QAAM,SAASA,KAAI;AACnB,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,CAACD,KAAI,IAAIA,KAAI,IAAI,KAAK,GAAG,SAAS,EAAE;AAAA,IACzC,GAAG;AAAA,MACD,CAACA,KAAI,IAAIA,KAAI,IAAI;AAAA,MACjB,SAAS;AAAA,MACTA,KAAI,IAAIA,KAAI,IAAI;AAAA,MAChB,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACA,EAAE,GAAGA,KAAI,IAAIA,KAAI,IAAI,KAAK,GAAG,CAAC,SAAS,EAAE;AAAA,IACzC,EAAE,GAAG,CAACA,KAAI,IAAIA,KAAI,IAAI,KAAK,GAAG,CAAC,SAAS,EAAE;AAAA,EAC5C;AACA,QAAMF,KAAI,CAACE,KAAI,IAAIA,KAAI,IAAI;AAC3B,QAAMD,KAAI,CAAC,SAAS,IAAI,YAAY;AACpC,QAAM,YAAY;AAAA,IAChB,EAAE,GAAGD,KAAIE,KAAI,UAAU,IAAID,KAAIE,MAAK,IAAI;AAAA,IACxC,EAAE,GAAGH,KAAIE,IAAG,GAAGD,KAAIE,KAAI,UAAU;AAAA,IACjC,EAAE,GAAGH,KAAIE,IAAG,IAAID,KAAIE,MAAK,IAAI;AAAA,IAC7B,GAAG;AAAA,MACDH,KAAIE;AAAA,OACHD,KAAIE,MAAK;AAAA,MACVH,KAAIE,KAAI;AAAA,OACPD,KAAIE,MAAK;AAAA,MACV,CAACA,KAAI;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACA,QAAM,mBAAmB,qBAAqB,MAAM;AACpD,QAAM,mBAAmB,GAAG,KAAK,kBAAkB,OAAO;AAC1D,QAAM,yBAAyB,qBAAqB,SAAS;AAC7D,QAAM,yBAAyB,GAAG,KAAK,wBAAwB;AAAA,IAC7D,GAAG;AAAA,IACH,WAAW;AAAA,EACb,CAAC;AACD,QAAM,eAAe,SAAS,OAAO,MAAM,wBAAwB,cAAc;AACjF,eAAa,OAAO,MAAM,kBAAkB,cAAc;AAC1D,eAAa,KAAK,SAAS,uBAAuB;AAClD,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,iBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,iBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AACA,eAAa,KAAK,aAAa,eAAe,CAAC,gBAAgB,CAAC,GAAG;AACnE,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAACD,KAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACC,KAAI,KAAK,KAAK,WAAW,KAAK,gBAAgB,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EAC1J;AACA,mBAAiB,MAAM,YAAY;AACnC,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,0BAA0B,0BAA0B;AAG3D,eAAe,KAAK,QAAQ,MAAM;AAChC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAM,aAAa,KAAK,IAAI,KAAK,QAAQ,KAAK,UAAS,6BAAM,UAAS,CAAC;AACvE,QAAM,cAAc,KAAK,IAAI,KAAK,SAAS,KAAK,UAAS,6BAAM,WAAU,CAAC;AAC1E,QAAMH,KAAI,CAAC,aAAa;AACxB,QAAMC,KAAI,CAAC,cAAc;AACzB,QAAM,QAAQ,SAAS,OAAO,QAAQ,cAAc;AACpD,QAAM,KAAK,SAAS,MAAM,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,KAAKD,EAAC,EAAE,KAAK,KAAKC,EAAC,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,UAAU,WAAW;AAChK,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AACT;AACA,OAAO,MAAM,MAAM;AAInB,IAAI,uBAAuC,OAAO,CAACD,IAAGC,IAAG,OAAO,QAAQ,IAAI,OAAO;AACjF,SAAO,IAAID,EAAC,IAAIC,EAAC;AAAA,OACZ,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,MAAM;AAAA,OAC9B,KAAK,IAAI,CAAC;AAAA,OACV,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,MAAM;AAAA,OAC7B,KAAK,IAAI,CAAC,MAAM;AAAA,OAChB,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,MAAM;AAAA,OAC7B,CAAC,KAAK,IAAI,CAAC;AAClB,GAAG,qBAAqB;AACxB,IAAI,4BAA4C,OAAO,CAACD,IAAGC,IAAG,OAAO,QAAQ,IAAI,OAAO;AACtF,SAAO;AAAA,IACL,IAAID,EAAC,IAAIC,EAAC;AAAA,IACV,IAAID,KAAI,KAAK,IAAIC,EAAC;AAAA,IAClB,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,MAAM;AAAA,IAClC,IAAI,CAAC,KAAK;AAAA,IACV,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,MAAM;AAAA,IACjC,IAAI,KAAK;AAAA,EACX,EAAE,KAAK,GAAG;AACZ,GAAG,0BAA0B;AAC7B,IAAI,4BAA4C,OAAO,CAACD,IAAGC,IAAG,OAAO,QAAQ,IAAI,OAAO;AACtF,SAAO,CAAC,IAAID,KAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,YAAY,MAAM,EAAE,EAAE,KAAK,GAAG;AACxF,GAAG,0BAA0B;AAC7B,eAAe,eAAe,QAAQ,MAAM;AAC1C,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,OAAO,YAAY,IAAI,MAAM;AAAA,IACnD;AAAA,IACA;AAAA,IACA,eAAe,IAAI;AAAA,EACrB;AACA,QAAM,eAAe,KAAK,SAAS,QAAQ,cAAc,IAAI;AAC7D,QAAMG,KAAI,KAAK,SAAS;AACxB,QAAM,KAAKA,KAAI;AACf,QAAM,KAAK,MAAM,MAAMA,KAAI;AAC3B,QAAMD,KAAI,KAAK,QAAQ,KAAK;AAC5B,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI;AACJ,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,gBAAgB,0BAA0B,GAAG,GAAGA,IAAGC,IAAG,IAAI,EAAE;AAClE,UAAM,gBAAgB,0BAA0B,GAAG,GAAGD,IAAGC,IAAG,IAAI,EAAE;AAClE,UAAM,YAAY,GAAG,KAAK,eAAe,kBAAkB,MAAM,CAAC,CAAC,CAAC;AACpE,UAAM,YAAY,GAAG,KAAK,eAAe,kBAAkB,MAAM,EAAE,MAAM,OAAO,CAAC,CAAC;AAClF,gBAAY,SAAS,OAAO,MAAM,WAAW,cAAc;AAC3D,gBAAY,SAAS,OAAO,MAAM,WAAW,cAAc;AAC3D,cAAU,KAAK,SAAS,uBAAuB;AAC/C,QAAI,WAAW;AACb,gBAAU,KAAK,SAAS,SAAS;AAAA,IACnC;AAAA,EACF,OAAO;AACL,UAAM,WAAW,qBAAqB,GAAG,GAAGD,IAAGC,IAAG,IAAI,EAAE;AACxD,gBAAY,SAAS,OAAO,QAAQ,cAAc,EAAE,KAAK,KAAK,QAAQ,EAAE,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,oBAAoB,SAAS,CAAC,EAAE,KAAK,SAAS,UAAU;AACrL,cAAU,KAAK,SAAS,uBAAuB;AAC/C,QAAI,WAAW;AACb,gBAAU,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,IACrD;AACA,QAAI,YAAY;AACd,gBAAU,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,IACtD;AAAA,EACF;AACA,YAAU,KAAK,kBAAkB,EAAE;AACnC,YAAU,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAKC,KAAI,CAAC,IAAI;AAC7D,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,EAAE,KAAK,QAAQ,KAAK,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EACvH;AACA,mBAAiB,MAAM,SAAS;AAChC,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,KAAK,MAAM,KAAK;AAC9C,UAAMF,KAAI,IAAI,KAAK,KAAK,KAAK;AAC7B,QAAI,MAAM,MAAM,KAAK,IAAIA,EAAC,KAAK,KAAK,UAAU,KAAK,KAAK,KAAK,IAAIA,EAAC,MAAM,KAAK,UAAU,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE,KAAK,KAAK,SAAS,KAAK,IAAI,KAAK;AAC9J,UAAID,KAAI,KAAK,MAAM,IAAIC,KAAIA,MAAK,KAAK;AACrC,UAAID,MAAK,GAAG;AACV,QAAAA,KAAI,KAAK,KAAK,KAAK,IAAIA,EAAC,CAAC;AAAA,MAC3B;AACA,MAAAA,KAAI,KAAKA;AACT,UAAI,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG;AAC/B,QAAAA,KAAI,CAACA;AAAA,MACP;AACA,UAAI,KAAKA;AAAA,IACX;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,gBAAgB,gBAAgB;AAIvC,eAAe,UAAU,QAAQ,MAAM;AACrC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAME,KAAI,KAAK,QAAQ,KAAK;AAC5B,QAAMC,KAAI,KAAK,SAAS,KAAK;AAC7B,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,KAAKA,KAAI,GAAG,GAAG,EAAE;AAAA,IACtB,EAAE,GAAGD,KAAI,IAAIC,KAAI,GAAG,GAAG,EAAE;AAAA,IACzB,EAAE,GAAGD,IAAG,GAAG,CAACC,GAAE;AAAA,IACd,EAAE,GAAG,GAAG,GAAG,CAACA,GAAE;AAAA,EAChB;AACA,MAAI;AACJ,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,UAAM,WAAW,qBAAqB,MAAM;AAC5C,UAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,cAAU,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACD,KAAI,CAAC,KAAKC,KAAI,CAAC,GAAG;AAC7G,QAAI,WAAW;AACb,cAAQ,KAAK,SAAS,SAAS;AAAA,IACjC;AAAA,EACF,OAAO;AACL,cAAU,mBAAmB,UAAUD,IAAGC,IAAG,MAAM;AAAA,EACrD;AACA,MAAI,YAAY;AACd,YAAQ,KAAK,SAAS,UAAU;AAAA,EAClC;AACA,OAAK,QAAQD;AACb,OAAK,SAASC;AACd,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,EACtD;AACA,SAAO;AACT;AACA,OAAO,WAAW,WAAW;AAI7B,eAAe,oBAAoB,QAAQ,MAAM;AAC/C,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAM,WAAW,IAAI,YAAY;AACjC,QAAMD,KAAI,KAAK,IAAI,UAAU,KAAK,SAAS,KAAK,WAAW,KAAK,IAAG,6BAAM,UAAS,CAAC;AACnF,QAAMC,KAAI,KAAK,IAAI,WAAW,KAAK,UAAU,KAAK,WAAW,KAAK,IAAG,6BAAM,WAAU,CAAC;AACtF,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,CAACD,KAAI,IAAI,KAAK,GAAG,CAACC,KAAI,EAAE;AAAA,IAC7B,EAAE,GAAGD,KAAI,IAAI,KAAK,GAAG,CAACC,KAAI,EAAE;AAAA,IAC5B,EAAE,GAAGD,KAAI,GAAG,GAAG,CAACC,KAAI,IAAI,IAAI;AAAA,IAC5B,EAAE,GAAGD,KAAI,GAAG,GAAGC,KAAI,EAAE;AAAA,IACrB,EAAE,GAAG,CAACD,KAAI,GAAG,GAAGC,KAAI,EAAE;AAAA,IACtB,EAAE,GAAG,CAACD,KAAI,GAAG,GAAG,CAACC,KAAI,IAAI,IAAI;AAAA,EAC/B;AACA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,QAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc;AAC/D,UAAQ,KAAK,SAAS,uBAAuB;AAC7C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AACA,mBAAiB,MAAM,OAAO;AAC9B,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,qBAAqB,qBAAqB;AAIjD,eAAe,SAAS,QAAQ,MAAM;AAlpHtC;AAmpHE,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAM,gBAAgB,UAAS,gBAAW,EAAE,cAAb,mBAAwB,UAAU;AACjE,QAAMD,KAAI,KAAK,SAAS,KAAK,WAAW;AACxC,QAAMC,KAAID,KAAI,KAAK;AACnB,QAAM,KAAKA,KAAI,KAAK;AACpB,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,IAAI,GAAG,EAAE;AAAA,IACd,EAAE,GAAG,KAAK,GAAG,GAAG,CAACC,GAAE;AAAA,EACrB;AACA,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,WAAW,qBAAqB,MAAM;AAC5C,QAAM,YAAY,GAAG,KAAK,UAAU,OAAO;AAC3C,QAAM,UAAU,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,aAAa,aAAa,CAACA,KAAI,CAAC,KAAKA,KAAI,CAAC,GAAG;AACnH,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,YAAQ,eAAe,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AACA,OAAK,QAAQD;AACb,OAAK,SAASC;AACd,mBAAiB,MAAM,OAAO;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAKA,KAAI,KAAK,KAAK,UAAU,KAAK,WAAW,MAAM,gBAAgB,IAAI,MAAM,KAAK,KAAK,KAAK,OAAO,IAAI;AAAA,EACnK;AACA,OAAK,YAAY,SAAS,OAAO;AAC/B,QAAI,KAAK,sBAAsB,MAAM,QAAQ,KAAK;AAClD,WAAO,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AAAA,EACtD;AACA,SAAO;AACT;AACA,OAAO,UAAU,UAAU;AAI3B,eAAe,mBAAmB,QAAQ,MAAM;AAC9C,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAMD,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,IAAG,6BAAM,UAAS,CAAC;AACzE,QAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,IAAG,6BAAM,WAAU,CAAC;AAC3E,QAAM,gBAAgBA,KAAI;AAC1B,QAAM,SAASA,KAAI;AACnB,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,WAAW;AACjB,QAAM,WAAW,WAAWD;AAC5B,QAAM,SAAS,WAAW,IAAI,WAAW,IAAI;AAC7C,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,CAACA,KAAI,IAAI,QAAQ,GAAG,SAAS,EAAE;AAAA,IACpC,GAAG;AAAA,MACD,CAACA,KAAI,IAAI;AAAA,MACT,SAAS;AAAA,MACTA,KAAI,IAAI;AAAA,MACR,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACA,EAAE,GAAGA,KAAI,IAAI,QAAQ,GAAG,CAAC,SAAS,EAAE;AAAA,IACpC,EAAE,GAAG,CAACA,KAAI,IAAI,QAAQ,GAAG,CAAC,SAAS,EAAE;AAAA,EACvC;AACA,QAAM,mBAAmB,qBAAqB,MAAM;AACpD,QAAM,mBAAmB,GAAG,KAAK,kBAAkB,OAAO;AAC1D,QAAM,eAAe,SAAS,OAAO,MAAM,kBAAkB,cAAc;AAC3E,eAAa,KAAK,SAAS,uBAAuB;AAClD,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,iBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACxD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,iBAAa,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACzD;AACA,eAAa,KAAK,aAAa,eAAe,CAAC,gBAAgB,CAAC,GAAG;AACnE,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAACA,KAAI,KAAK,KAAK,WAAW,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACC,KAAI,KAAK,KAAK,WAAW,KAAK,iBAAiB,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EACtJ;AACA,mBAAiB,MAAM,YAAY;AACnC,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,oBAAoB,oBAAoB;AAI/C,eAAe,cAAc,QAAQ,MAAM;AACzC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AAC/E,QAAM,WAAW;AACjB,QAAM,YAAY;AAClB,QAAM,YAAY,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,IAAG,6BAAM,UAAS,CAAC;AACjF,QAAM,aAAa,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,IAAG,6BAAM,WAAU,CAAC;AACpF,QAAM,cAAc,YAAY;AAChC,MAAID,KAAI;AACR,MAAIC,KAAI;AACR,MAAID,KAAIC,KAAI,aAAa;AACvB,IAAAA,KAAID,KAAI;AAAA,EACV,OAAO;AACL,IAAAA,KAAIC,KAAI;AAAA,EACV;AACA,EAAAD,KAAI,KAAK,IAAIA,IAAG,QAAQ;AACxB,EAAAC,KAAI,KAAK,IAAIA,IAAG,SAAS;AACzB,QAAM,gBAAgB,KAAK,IAAIA,KAAI,KAAKA,KAAI,CAAC;AAC7C,QAAM,SAASA,KAAI,gBAAgB;AACnC,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,SAAS;AAAA,IACb,EAAE,GAAG,CAACD,KAAI,GAAG,GAAG,SAAS,EAAE;AAAA,IAC3B,GAAG,2BAA2B,CAACA,KAAI,GAAG,SAAS,GAAGA,KAAI,GAAG,SAAS,GAAG,eAAe,CAAC;AAAA,IACrF,EAAE,GAAGA,KAAI,GAAG,GAAG,CAAC,SAAS,EAAE;AAAA,IAC3B,GAAG,2BAA2BA,KAAI,GAAG,CAAC,SAAS,GAAG,CAACA,KAAI,GAAG,CAAC,SAAS,GAAG,eAAe,EAAE;AAAA,EAC1F;AACA,QAAM,eAAe,qBAAqB,MAAM;AAChD,QAAM,eAAe,GAAG,KAAK,cAAc,OAAO;AAClD,QAAM,WAAW,SAAS,OAAO,MAAM,cAAc,cAAc;AACnE,WAAS,KAAK,SAAS,uBAAuB;AAC9C,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,aAAS,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACpD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,aAAS,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACrD;AACA,mBAAiB,MAAM,QAAQ;AAC/B,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,QAAQ,MAAM,QAAQ,KAAK;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,eAAe,eAAe;AAIrC,eAAe,WAAW,QAAQ,MAAM;AACtC,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,YAAY,QAAQ,MAAM,eAAe,IAAI,CAAC;AACtF,QAAMA,KAAI,KAAK,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,IAAG,6BAAM,UAAS,CAAC;AACzE,QAAMC,KAAI,KAAK,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,IAAG,6BAAM,WAAU,CAAC;AAC3E,QAAM,aAAa;AACnB,QAAMH,KAAI,CAACE,KAAI;AACf,QAAMD,KAAI,CAACE,KAAI;AACf,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,QAAM,kBAAkB;AAAA,IACtB,EAAE,GAAGH,KAAI,YAAY,GAAGC,KAAI,WAAW;AAAA,IACvC,EAAE,GAAGD,KAAI,YAAY,GAAGC,KAAIE,GAAE;AAAA,IAC9B,EAAE,GAAGH,KAAIE,IAAG,GAAGD,KAAIE,GAAE;AAAA,IACrB,EAAE,GAAGH,KAAIE,IAAG,GAAGD,KAAI,WAAW;AAAA,EAChC;AACA,QAAM,OAAO,IAAID,KAAI,UAAU,IAAIC,KAAI,UAAU,KAAKD,KAAIE,EAAC,IAAID,KAAI,UAAU,KAAKD,KAAIE,EAAC,IAAID,KAAIE,EAAC,KAAKH,KAAI,UAAU,IAAIC,KAAIE,EAAC,KAAKH,KAAI,UAAU,IAAIC,KAAI,UAAU;AAAA,mBAChJD,KAAI,UAAU,IAAIC,EAAC,KAAKD,KAAIE,EAAC,IAAID,EAAC;AAAA,mBAClCD,EAAC,IAAIC,KAAI,UAAU,KAAKD,EAAC,IAAIC,KAAIE,EAAC;AACnD,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,KAAK,GAAG,KAAK,MAAM,OAAO;AAChC,QAAM,cAAc,SAAS,OAAO,MAAM,IAAI,cAAc;AAC5D,cAAY,KAAK,aAAa,aAAa,aAAa,CAAC,KAAK,aAAa,CAAC,GAAG;AAC/E,cAAY,KAAK,SAAS,uBAAuB;AACjD,MAAI,aAAa,KAAK,SAAS,aAAa;AAC1C,gBAAY,UAAU,MAAM,EAAE,KAAK,SAAS,SAAS;AAAA,EACvD;AACA,MAAI,cAAc,KAAK,SAAS,aAAa;AAC3C,gBAAY,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU;AAAA,EACxD;AACA,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,EAAE,KAAK,QAAQ,KAAK,aAAa,KAAK,KAAK,KAAK,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,SAAS,KAAK,aAAa,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG;AAAA,EACpJ;AACA,mBAAiB,MAAM,WAAW;AAClC,OAAK,YAAY,SAAS,OAAO;AAC/B,UAAM,MAAM,kBAAkB,QAAQ,MAAM,iBAAiB,KAAK;AAClE,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,OAAO,YAAY,YAAY;AAK/B,eAAe,MAAM,QAAQ,MAAM;AAl2HnC;AAm2HE,QAAM,aAAa;AACnB,MAAI,WAAW,OAAO;AACpB,SAAK,QAAQ,WAAW;AAAA,EAC1B;AACA,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,EAAE,gBAAgB,gBAAgB,IAAI,UAAU;AACtD,UAAM,EAAE,WAAW,IAAI;AACvB,UAAM,iBAAiB;AAAA,MACrB,GAAG;AAAA,MACH,IAAI,KAAK,KAAK;AAAA,MACd,MAAM;AAAA,MACN,WAAW,CAAC,gBAAgB,SAAS,UAAU,EAAE;AAAA,IACnD;AACA,UAAM,MAAM,QAAQ,cAAc;AAAA,EACpC;AACA,QAAM,SAAS,UAAU;AACzB,OAAK,gBAAgB,OAAO;AAC5B,MAAI,YAAU,YAAO,OAAP,mBAAW,mBAAkB;AAC3C,MAAI,iBAAe,YAAO,OAAP,mBAAW,kBAAiB;AAC/C,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,EAAE,YAAY,IAAI,cAAc,IAAI;AAC1C,MAAI,WAAW,WAAW,WAAW,KAAK,KAAK,OAAO;AACpD,UAAM,WAAW;AAAA,MACf,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,eAAe;AAAA,MACf,eAAe,UAAU;AAAA,MACzB,SAAS;AAAA,IACX;AACA,QAAI,mBAAmB,KAAK,OAAO,MAAM,IAAI,SAAS,gBAAgB,IAAI,OAAO,GAAG,gBAAgB;AAClG,WAAK,QAAQ,OAAO,GAAG;AAAA,IACzB;AACA,UAAM,YAAY,MAAM,SAAS,QAAQ,MAAM,QAAQ;AACvD,QAAI,CAAC,SAAS,OAAO,UAAU,GAAG;AAChC,YAAM,cAAc,UAAU,OAAO,MAAM;AAC3C,YAAM,QAAO,iBAAY,KAAK,MAAjB,mBAAoB;AACjC,kBAAY,KAAK,aAAa,aAAa,CAAC,KAAK,QAAQ,CAAC,MAAM;AAAA,IAClE;AACA,WAAO;AAAA,EACT;AACA,MAAI,CAAC,OAAO,YAAY;AACtB,eAAW;AACX,oBAAgB;AAAA,EAClB;AACA,MAAI,aAAa,eAAe,IAAI;AACpC,MAAI,CAAC,YAAY;AACf,iBAAa;AAAA,EACf;AACA,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAC9F,QAAM,WAAW,MAAM,QAAQ,UAAU,KAAK,SAAS,IAAI,QAAQ,GAAG,GAAG,CAAC,MAAM,GAAG,WAAW;AAC9F,WAAS,UAAU;AACnB,MAAI,UAAU;AACd,QAAM,WAAW,CAAC;AAClB,MAAI,eAAe;AACnB,MAAI,eAAe;AACnB,MAAI,eAAe;AACnB,MAAI,kBAAkB;AACtB,MAAI,cAAc;AAClB,MAAI,iBAAiB;AACrB,aAAW,aAAa,WAAW,YAAY;AAC7C,UAAM,WAAW,MAAM;AAAA,MACrB;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC,gBAAgB;AAAA,MACjB;AAAA,IACF;AACA,mBAAe,KAAK,IAAI,cAAc,SAAS,QAAQ,OAAO;AAC9D,UAAM,YAAY,MAAM;AAAA,MACtB;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC,gBAAgB;AAAA,MACjB;AAAA,IACF;AACA,mBAAe,KAAK,IAAI,cAAc,UAAU,QAAQ,OAAO;AAC/D,UAAM,WAAW,MAAM;AAAA,MACrB;AAAA,MACA,UAAU,KAAK,KAAK;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC,gBAAgB;AAAA,MACjB;AAAA,IACF;AACA,mBAAe,KAAK,IAAI,cAAc,SAAS,QAAQ,OAAO;AAC9D,UAAM,cAAc,MAAM;AAAA,MACxB;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC,mBAAmB;AAAA,MACpB;AAAA,IACF;AACA,sBAAkB,KAAK,IAAI,iBAAiB,YAAY,QAAQ,OAAO;AACvE,eAAW,KAAK,IAAI,SAAS,QAAQ,UAAU,QAAQ,SAAS,QAAQ,YAAY,MAAM,IAAI;AAC9F,aAAS,KAAK,OAAO;AAAA,EACvB;AACA,WAAS,IAAI;AACb,MAAI,qBAAqB;AACzB,MAAI,gBAAgB,SAAS;AAC3B,kBAAc;AACd,mBAAe;AACf;AAAA,EACF;AACA,MAAI,mBAAmB,SAAS;AAC9B,qBAAiB;AACjB,sBAAkB;AAClB;AAAA,EACF;AACA,QAAM,YAAY,SAAS,KAAK,EAAE,QAAQ;AAC1C,MAAI,SAAS,QAAQ,UAAU,KAAK,eAAe,eAAe,eAAe,mBAAmB,GAAG;AACrG,UAAM,aAAa,SAAS,QAAQ,UAAU,KAAK,eAAe,eAAe,eAAe;AAChG,oBAAgB,aAAa;AAC7B,oBAAgB,aAAa;AAC7B,QAAI,eAAe,GAAG;AACpB,sBAAgB,aAAa;AAAA,IAC/B;AACA,QAAI,kBAAkB,GAAG;AACvB,yBAAmB,aAAa;AAAA,IAClC;AAAA,EACF;AACA,QAAM,WAAW,eAAe,eAAe,eAAe;AAC9D,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAMD,KAAI,KAAK,IAAI,UAAU,QAAQ,UAAU,IAAG,6BAAM,UAAS,GAAG,QAAQ;AAC5E,QAAMC,KAAI,KAAK,IAAI,UAAU,UAAU,SAAS,CAAC,KAAK,WAAW,eAAc,6BAAM,WAAU,CAAC;AAChG,QAAMH,KAAI,CAACE,KAAI;AACf,QAAMD,KAAI,CAACE,KAAI;AACf,WAAS,UAAU,qBAAqB,EAAE,KAAK,CAACQ,IAAGb,IAAG,UAAU;AAC9D,UAAM,QAAQ,eAAQ,MAAMA,EAAC,CAAC;AAC9B,UAAM,YAAY,MAAM,KAAK,WAAW;AACxC,QAAI,aAAa;AACjB,QAAI,aAAa;AACjB,QAAI,WAAW;AACb,YAAM,QAAQ,OAAO,8BAA8B;AACnD,YAAM,YAAY,MAAM,KAAK,SAAS;AACtC,UAAI,WAAW;AACb,qBAAa,WAAW,UAAU,CAAC,CAAC;AACpC,qBAAa,WAAW,UAAU,CAAC,CAAC;AACpC,YAAI,MAAM,KAAK,OAAO,EAAE,SAAS,gBAAgB,GAAG;AAClD,wBAAc;AAAA,QAChB,WAAW,MAAM,KAAK,OAAO,EAAE,SAAS,gBAAgB,GAAG;AACzD,wBAAc,eAAe;AAAA,QAC/B,WAAW,MAAM,KAAK,OAAO,EAAE,SAAS,mBAAmB,GAAG;AAC5D,wBAAc,eAAe,eAAe;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,MACA,aAAaE,KAAI,UAAU,IAAI,UAAU,KAAK,aAAaC,KAAI,SAAS,SAAS,eAAe,CAAC;AAAA,IACnG;AAAA,EACF,CAAC;AACD,WAAS,OAAO,OAAO,EAAE,KAAK,aAAa,eAAe,CAAC,SAAS,QAAQ,IAAI,QAAQA,KAAI,eAAe,KAAK,GAAG;AACnH,QAAM,YAAY,GAAG,UAAUD,IAAGC,IAAGC,IAAGC,IAAG,OAAO;AAClD,QAAM,QAAQ,SAAS,OAAO,MAAM,WAAW,cAAc,EAAE,KAAK,SAAS,UAAU,KAAK,EAAE,CAAC;AAC/F,QAAM,EAAE,eAAe,IAAI,UAAU;AACrC,QAAM,EAAE,SAAS,QAAQ,WAAW,IAAI;AACxC,WAAS,KAAK,CAAC;AACf,aAAW,CAACL,IAAG,QAAQ,KAAK,SAAS,QAAQ,GAAG;AAC9C,QAAIA,OAAM,KAAK,SAAS,SAAS,GAAG;AAClC;AAAA,IACF;AACA,UAAM,SAASA,KAAI,MAAM,KAAK,aAAa;AAC3C,UAAM,aAAa,GAAG,UAAUE,IAAG,SAAS,SAASC,KAAI,UAAUC,IAAG,SAAS,QAAQ;AAAA,MACrF,GAAG;AAAA,MACH,MAAM,SAAS,UAAU;AAAA,MACzB,QAAQ;AAAA,IACV,CAAC;AACD,aAAS,OAAO,MAAM,YAAY,SAAS,EAAE,KAAK,SAAS,UAAU,KAAK,EAAE,CAAC,EAAE,KAAK,SAAS,YAAYJ,KAAI,MAAM,IAAI,SAAS,KAAK,EAAE;AAAA,EACzI;AACA,MAAI,YAAY,GAAG,KAAKE,IAAG,SAAS,SAASC,IAAGC,KAAIF,IAAG,SAAS,SAASC,IAAG,OAAO;AACnF,WAAS,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,SAAS;AACxD,cAAY,GAAG,KAAK,eAAeD,IAAG,SAAS,SAASC,IAAG,eAAeD,IAAGG,KAAIF,IAAG,OAAO;AAC3F,WAAS,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,SAAS;AACxD,MAAI,aAAa;AACf,gBAAY,GAAG;AAAA,MACb,eAAe,eAAeD;AAAA,MAC9B,SAAS,SAASC;AAAA,MAClB,eAAe,eAAeD;AAAA,MAC9BG,KAAIF;AAAA,MACJ;AAAA,IACF;AACA,aAAS,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,SAAS;AAAA,EAC1D;AACA,MAAI,gBAAgB;AAClB,gBAAY,GAAG;AAAA,MACb,eAAe,eAAe,eAAeD;AAAA,MAC7C,SAAS,SAASC;AAAA,MAClB,eAAe,eAAe,eAAeD;AAAA,MAC7CG,KAAIF;AAAA,MACJ;AAAA,IACF;AACA,aAAS,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,SAAS;AAAA,EAC1D;AACA,aAAW,YAAY,UAAU;AAC/B,gBAAY,GAAG;AAAA,MACbD;AAAA,MACA,SAAS,SAASC,KAAI;AAAA,MACtBC,KAAIF;AAAA,MACJ,SAAS,SAASC,KAAI;AAAA,MACtB;AAAA,IACF;AACA,aAAS,OAAO,MAAM,SAAS,EAAE,KAAK,SAAS,SAAS;AAAA,EAC1D;AACA,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AACT;AACA,OAAO,OAAO,OAAO;AACrB,eAAe,QAAQ,UAAU,WAAW,QAAQ,aAAa,GAAG,aAAa,GAAG,UAAU,CAAC,GAAG,QAAQ,IAAI;AAC5G,QAAM,QAAQ,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,SAAS,QAAQ,KAAK,GAAG,CAAC,EAAE,EAAE,KAAK,aAAa,aAAa,UAAU,KAAK,UAAU,GAAG,EAAE,KAAK,SAAS,KAAK;AAC/J,MAAI,cAAc,kBAAkB,SAAS,GAAG;AAC9C,gBAAY,kBAAkB,SAAS;AACvC,gBAAY,UAAU,WAAW,KAAK,MAAM,EAAE,WAAW,KAAK,MAAM;AAAA,EACtE;AACA,QAAM,QAAQ,MAAM,KAAK,EAAE;AAAA,IACzB,MAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,QACE,OAAO,mBAAmB,WAAW,MAAM,IAAI;AAAA,QAC/C;AAAA,QACA,eAAe,OAAO;AAAA,MACxB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,MAAI,UAAU,SAAS,MAAM,KAAK,UAAU,SAAS,MAAM,GAAG;AAC5D,QAAI,QAAQ,MAAM,SAAS,CAAC;AAC5B,UAAM,cAAc,MAAM,YAAY,WAAW,QAAQ,GAAG,EAAE,WAAW,QAAQ,GAAG;AACpF,WAAO,MAAM,WAAW,CAAC,GAAG;AAC1B,cAAQ,MAAM,WAAW,CAAC;AAC1B,YAAM,cAAc,MAAM,YAAY,WAAW,QAAQ,GAAG,EAAE,WAAW,QAAQ,GAAG;AAAA,IACtF;AAAA,EACF;AACA,MAAI,OAAO,MAAM,QAAQ;AACzB,MAAI,SAAS,OAAO,UAAU,GAAG;AAC/B,UAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,QAAI,MAAM,YAAY;AACtB,UAAM,KAAK,eAAQ,KAAK;AACxB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AACA,SAAO;AACT;AACA,OAAO,SAAS,SAAS;AAQzB,eAAe,WAAW,QAAQ,MAAM,QAAQ,eAAe,MAAM,OAAO,MAAM,WAAW,IAAI;AAC/F,QAAM,eAAe,CAAC,gBAAgB,IAAI;AAC1C,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AACxG,MAAI,kBAAkB;AACtB,MAAI,aAAa;AACjB,MAAI,eAAe;AACnB,MAAI,eAAe;AACnB,MAAI,wBAAwB;AAC5B,MAAI,mBAAmB;AACvB,MAAI,qBAAqB;AACzB,oBAAkB,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,uBAAuB;AAC5E,MAAI,KAAK,YAAY,SAAS,GAAG;AAC/B,UAAM,aAAa,KAAK,YAAY,CAAC;AACrC,UAAM,SAAS,iBAAiB,EAAE,MAAM,IAAO,UAAU,IAAO,GAAG,CAAC;AACpE,UAAM,sBAAsB,gBAAgB,KAAK,EAAE,QAAQ;AAC3D,4BAAwB,oBAAoB;AAAA,EAC9C;AACA,eAAa,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,kBAAkB;AAClE,QAAM,SAAS,YAAY,MAAM,GAAG,CAAC,qBAAqB,CAAC;AAC3D,QAAM,iBAAiB,WAAW,KAAK,EAAE,QAAQ;AACjD,qBAAmB,eAAe;AAClC,iBAAe,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,oBAAoB;AACtE,MAAI,UAAU;AACd,aAAW,UAAU,KAAK,SAAS;AACjC,UAAM,SAAS,MAAM,SAAS,cAAc,QAAQ,SAAS,CAAC,OAAO,gBAAgB,CAAC,CAAC;AACvF,eAAW,SAAS;AAAA,EACtB;AACA,uBAAqB,aAAa,KAAK,EAAE,QAAQ,EAAE;AACnD,MAAI,sBAAsB,GAAG;AAC3B,yBAAqB,MAAM;AAAA,EAC7B;AACA,iBAAe,SAAS,OAAO,GAAG,EAAE,KAAK,SAAS,oBAAoB;AACtE,MAAI,iBAAiB;AACrB,aAAW,UAAU,KAAK,SAAS;AACjC,UAAM,SAAS,MAAM,SAAS,cAAc,QAAQ,gBAAgB,CAAC,OAAO,gBAAgB,CAAC,CAAC;AAC9F,sBAAkB,SAAS;AAAA,EAC7B;AACA,MAAI,OAAO,SAAS,KAAK,EAAE,QAAQ;AACnC,MAAI,oBAAoB,MAAM;AAC5B,UAAM,sBAAsB,gBAAgB,KAAK,EAAE,QAAQ;AAC3D,oBAAgB,KAAK,aAAa,aAAa,CAAC,oBAAoB,QAAQ,CAAC,GAAG;AAAA,EAClF;AACA,aAAW,KAAK,aAAa,aAAa,CAAC,eAAe,QAAQ,CAAC,KAAK,qBAAqB,GAAG;AAChG,SAAO,SAAS,KAAK,EAAE,QAAQ;AAC/B,eAAa;AAAA,IACX;AAAA,IACA,aAAa,CAAC,KAAK,wBAAwB,mBAAmB,MAAM,CAAC;AAAA,EACvE;AACA,SAAO,SAAS,KAAK,EAAE,QAAQ;AAC/B,eAAa;AAAA,IACX;AAAA,IACA,aAAa,CAAC,KAAK,wBAAwB,oBAAoB,qBAAqB,qBAAqB,MAAM,IAAI,MAAM,EAAE;AAAA,EAC7H;AACA,SAAO,SAAS,KAAK,EAAE,QAAQ;AAC/B,SAAO,EAAE,UAAU,KAAK;AAC1B;AACA,OAAO,YAAY,YAAY;AAC/B,eAAe,SAAS,aAAa,MAAM,SAAS,SAAS,CAAC,GAAG;AAC/D,QAAM,SAAS,YAAY,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,SAAS,OAAO,KAAK,IAAI,CAAC;AAC7F,QAAM,SAAS,UAAU;AACzB,MAAI,gBAAgB,mBAAmB,OAAO,KAAK,gBAAgB,SAAS,OAAO,UAAU,KAAK;AAClG,MAAI,cAAc;AAClB,MAAI,UAAU,MAAM;AAClB,kBAAc,KAAK;AAAA,EACrB,OAAO;AACL,kBAAc,KAAK;AAAA,EACrB;AACA,MAAI,CAAC,iBAAiB,YAAY,WAAW,IAAI,GAAG;AAClD,kBAAc,YAAY,UAAU,CAAC;AAAA,EACvC;AACA,MAAI,SAAS,WAAW,GAAG;AACzB,oBAAgB;AAAA,EAClB;AACA,QAAM,QAAQ,MAAM;AAAA,IAClB;AAAA,IACA,cAAc,eAAe,WAAW,CAAC;AAAA,IACzC;AAAA,MACE,OAAO,mBAAmB,aAAa,MAAM,IAAI;AAAA;AAAA,MAEjD,SAAS;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,MAAI;AACJ,MAAI,gBAAgB;AACpB,MAAI,CAAC,eAAe;AAClB,QAAI,OAAO,SAAS,qBAAqB,GAAG;AAC1C,qBAAQ,KAAK,EAAE,UAAU,OAAO,EAAE,KAAK,eAAe,EAAE;AAAA,IAC1D;AACA,oBAAgB,MAAM,SAAS;AAC/B,UAAM,YAAY,MAAM,SAAS,CAAC;AAClC,QAAI,MAAM,gBAAgB,MAAM,MAAM,YAAY,SAAS,KAAK,GAAG;AACjE,gBAAU,cAAc,YAAY,CAAC,IAAI,YAAY,UAAU,CAAC,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,KAAK;AACvH,YAAM,gBAAgB,YAAY,CAAC,MAAM;AACzC,UAAI,eAAe;AACjB,kBAAU,cAAc,UAAU,YAAY,CAAC,IAAI,MAAM,UAAU,YAAY,UAAU,CAAC;AAAA,MAC5F;AAAA,IACF;AACA,QAAI,UAAU,gBAAgB,aAAa;AACzC,gBAAU,cAAc;AAAA,IAC1B;AACA,WAAO,MAAM,QAAQ;AAAA,EACvB,OAAO;AACL,UAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,UAAM,KAAK,eAAQ,KAAK;AACxB,oBAAgB,IAAI,UAAU,MAAM,MAAM,EAAE;AAC5C,QAAI,IAAI,UAAU,SAAS,SAAS,GAAG;AACrC,uBAAiB,IAAI,UAAU,MAAM,QAAQ,EAAE,SAAS;AAAA,IAC1D;AACA,UAAM,SAAS,IAAI,qBAAqB,KAAK;AAC7C,QAAI,QAAQ;AACV,YAAM,YAAY,YAAY,QAAQ,eAAe,EAAE,EAAE,KAAK,MAAM;AACpE,YAAM,QAAQ;AAAA,QACZ,CAAC,GAAG,MAAM,EAAE;AAAA,UACV,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ;AAC5B,qBAAS,aAAa;AAluIlC;AAmuIc,kBAAI,MAAM,UAAU;AACpB,kBAAI,MAAM,gBAAgB;AAC1B,kBAAI,WAAW;AACb,sBAAM,iBAAe,YAAO,aAAP,mBAAiB,eAAc,OAAO,iBAAiB,SAAS,IAAI,EAAE;AAC3F,sBAAM,kBAAkB;AACxB,sBAAM,QAAQ,SAAS,cAAc,EAAE,IAAI,kBAAkB;AAC7D,oBAAI,MAAM,WAAW;AACrB,oBAAI,MAAM,WAAW;AAAA,cACvB,OAAO;AACL,oBAAI,MAAM,QAAQ;AAAA,cACpB;AACA,kBAAI,GAAG;AAAA,YACT;AACA,mBAAO,YAAY,YAAY;AAC/B,uBAAW,MAAM;AACf,kBAAI,IAAI,UAAU;AAChB,2BAAW;AAAA,cACb;AAAA,YACF,CAAC;AACD,gBAAI,iBAAiB,SAAS,UAAU;AACxC,gBAAI,iBAAiB,QAAQ,UAAU;AAAA,UACzC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AACA,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AACA,SAAO,KAAK,aAAa,kBAAkB,CAAC,KAAK,UAAU,IAAI,iBAAiB,WAAW,GAAG;AAC9F,SAAO,KAAK;AACd;AACA,OAAO,UAAU,SAAS;AAG1B,eAAe,SAAS,QAAQ,MAAM;AAtwItC;AAuwIE,QAAM,SAAS,WAAW;AAC1B,QAAM,UAAU,OAAO,MAAM,WAAW;AACxC,QAAM,MAAM;AACZ,QAAM,gBAAgB,KAAK,iBAAiB,SAAS,OAAO,UAAU,KAAK;AAC3E,QAAM,YAAY;AAClB,YAAU,cAAc,UAAU,eAAe,CAAC;AAClD,YAAU,UAAU,UAAU,WAAW,CAAC;AAC1C,YAAU,UAAU,UAAU,WAAW,CAAC;AAC1C,QAAM,EAAE,UAAU,KAAK,IAAI,MAAM,WAAW,QAAQ,MAAM,QAAQ,eAAe,GAAG;AACpF,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,OAAK,YAAY,UAAU,UAAU;AACrC,QAAM,WAAS,eAAU,WAAV,mBAAkB,KAAK,SAAQ,cAAc;AAC5D,MAAI,CAAC,KAAK,WAAW;AACnB,SAAK,YAAY,OAAO,WAAW,cAAc,EAAE,EAAE,MAAM,GAAG;AAAA,EAChE;AACA,QAAM,iBAAiB,UAAU,QAAQ,WAAW,KAAK,UAAU,QAAQ,WAAW,KAAK,GAAC,YAAO,UAAP,mBAAc;AAC1G,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAMC,KAAI,KAAK;AACf,MAAIC,KAAI,KAAK;AACb,MAAI,UAAU,QAAQ,WAAW,KAAK,UAAU,QAAQ,WAAW,GAAG;AACpE,IAAAA,MAAK;AAAA,EACP,WAAW,UAAU,QAAQ,SAAS,KAAK,UAAU,QAAQ,WAAW,GAAG;AACzE,IAAAA,MAAK,MAAM;AAAA,EACb;AACA,QAAMH,KAAI,CAACE,KAAI;AACf,QAAMD,KAAI,CAACE,KAAI;AACf,QAAM,YAAY,GAAG;AAAA,IACnBH,KAAI;AAAA,IACJC,KAAI,WAAW,iBAAiB,UAAU,UAAU,QAAQ,WAAW,KAAK,UAAU,QAAQ,WAAW,IAAI,CAAC,UAAU,IAAI;AAAA,IAC5HC,KAAI,IAAI;AAAA,IACRC,KAAI,IAAI,WAAW,iBAAiB,UAAU,IAAI,UAAU,QAAQ,WAAW,KAAK,UAAU,QAAQ,WAAW,IAAI,CAAC,UAAU;AAAA,IAChI;AAAA,EACF;AACA,QAAM,QAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AAC7D,QAAM,KAAK,SAAS,uBAAuB;AAC3C,QAAM,WAAW,MAAM,KAAK,EAAE,QAAQ;AACtC,WAAS,UAAU,OAAO,EAAE,KAAK,CAACQ,IAAGb,IAAG,UAAU;AAjzIpD,QAAAc;AAkzII,UAAM,QAAQ,eAAQ,MAAMd,EAAC,CAAC;AAC9B,UAAM,YAAY,MAAM,KAAK,WAAW;AACxC,QAAI,aAAa;AACjB,QAAI,WAAW;AACb,YAAM,QAAQ,OAAO,8BAA8B;AACnD,YAAM,YAAY,MAAM,KAAK,SAAS;AACtC,UAAI,WAAW;AACb,qBAAa,WAAW,UAAU,CAAC,CAAC;AAAA,MACtC;AAAA,IACF;AACA,QAAI,gBAAgB,aAAaG,KAAI,WAAW,iBAAiB,UAAU,UAAU,QAAQ,WAAW,KAAK,UAAU,QAAQ,WAAW,IAAI,CAAC,UAAU,IAAI;AAC7J,QAAI,CAAC,eAAe;AAClB,uBAAiB;AAAA,IACnB;AACA,QAAI,gBAAgBD;AACpB,QAAI,MAAM,KAAK,OAAO,EAAE,SAAS,aAAa,KAAK,MAAM,KAAK,OAAO,EAAE,SAAS,kBAAkB,GAAG;AACnG,sBAAgB,GAACY,MAAA,MAAM,KAAK,MAAX,gBAAAA,IAAc,UAAU,SAAQ,KAAK;AACtD,eAAS,UAAU,MAAM,EAAE,KAAK,SAASC,KAAIC,KAAI,QAAQ;AACvD,YAAI,OAAO,iBAAiB,OAAOA,GAAE,CAAC,EAAE,eAAe,UAAU;AAC/D,0BAAgB;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,KAAK,aAAa,aAAa,aAAa,KAAK,aAAa,GAAG;AAAA,EACzE,CAAC;AACD,QAAM,wBAAwB,SAAS,OAAO,mBAAmB,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,iBAAiB,UAAU,IAAI,MAAM;AACnI,QAAM,mBAAmB,SAAS,OAAO,cAAc,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,iBAAiB,UAAU,IAAI,MAAM;AACzH,QAAM,qBAAqB,SAAS,OAAO,gBAAgB,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,iBAAiB,UAAU,IAAI,MAAM;AAC7H,MAAI,UAAU,QAAQ,SAAS,KAAK,UAAU,QAAQ,SAAS,KAAK,gBAAgB;AAClF,UAAM,YAAY,GAAG;AAAA,MACnB,SAAS;AAAA,MACT,wBAAwB,mBAAmBb,KAAI;AAAA,MAC/C,SAAS,IAAI,SAAS;AAAA,MACtB,wBAAwB,mBAAmBA,KAAI;AAAA,MAC/C;AAAA,IACF;AACA,UAAM,OAAO,SAAS,OAAO,MAAM,SAAS;AAC5C,SAAK,KAAK,SAAS,SAAS,EAAE,KAAK,SAAS,MAAM;AAAA,EACpD;AACA,MAAI,kBAAkB,UAAU,QAAQ,SAAS,KAAK,UAAU,QAAQ,SAAS,GAAG;AAClF,UAAM,YAAY,GAAG;AAAA,MACnB,SAAS;AAAA,MACT,wBAAwB,mBAAmB,qBAAqBA,KAAI,MAAM,IAAI;AAAA,MAC9E,SAAS,IAAI,SAAS;AAAA,MACtB,wBAAwB,mBAAmB,qBAAqBA,KAAI,UAAU,MAAM;AAAA,MACpF;AAAA,IACF;AACA,UAAM,OAAO,SAAS,OAAO,MAAM,SAAS;AAC5C,SAAK,KAAK,SAAS,SAAS,EAAE,KAAK,SAAS,MAAM;AAAA,EACpD;AACA,MAAI,UAAU,SAAS,aAAa;AAClC,aAAS,UAAU,MAAM,EAAE,KAAK,SAAS,MAAM;AAAA,EACjD;AACA,QAAM,OAAO,eAAe,EAAE,KAAK,SAAS,MAAM;AAClD,WAAS,UAAU,UAAU,EAAE,OAAO,MAAM,EAAE,KAAK,SAAS,MAAM;AAClE,MAAI,KAAK,YAAY;AACnB,aAAS,UAAU,MAAM,EAAE,KAAK,SAAS,KAAK,UAAU;AAAA,EAC1D,OAAO;AACL,aAAS,UAAU,MAAM,EAAE,KAAK,SAAS,MAAM;AAAA,EACjD;AACA,MAAI,CAAC,eAAe;AAClB,UAAM,aAAa,OAAO,qBAAqB;AAC/C,UAAM,QAAQ,WAAW,KAAK,MAAM;AACpC,QAAI,OAAO;AACT,YAAM,aAAa,MAAM,CAAC,EAAE,QAAQ,SAAS,MAAM;AACnD,eAAS,UAAU,OAAO,EAAE,KAAK,SAAS,UAAU;AAAA,IACtD,WAAW,aAAa;AACtB,YAAM,SAAS,WAAW,KAAK,WAAW;AAC1C,UAAI,QAAQ;AACV,cAAM,aAAa,OAAO,CAAC,EAAE,QAAQ,SAAS,MAAM;AACpD,iBAAS,UAAU,OAAO,EAAE,KAAK,SAAS,UAAU;AAAA,MACtD;AAAA,IACF;AAAA,EACF;AACA,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AACT;AACA,OAAO,UAAU,UAAU;AAK3B,eAAe,eAAe,QAAQ,MAAM;AAv4I5C;AAw4IE,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,IAAI;AACtD,OAAK,aAAa;AAClB,QAAM,kBAAkB;AACxB,QAAM,cAAc;AACpB,QAAM,UAAU;AAChB,QAAM,MAAM;AACZ,QAAM,oBAAoB,kBAAkB;AAC5C,QAAM,UAAU,eAAe,IAAI;AACnC,QAAM,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,MAAM,KAAK,SAAS,KAAK,EAAE;AAC3F,MAAI;AACJ,MAAI,mBAAmB;AACrB,iBAAa,MAAM;AAAA,MACjB;AAAA,MACA,WAAW,gBAAgB,IAAI;AAAA,MAC/B;AAAA,MACA,KAAK;AAAA,IACP;AAAA,EACF,OAAO;AACL,iBAAa,MAAM,SAAS,UAAU,2BAA2B,GAAG,KAAK,UAAU;AAAA,EACrF;AACA,MAAI,qBAAqB;AACzB,QAAM,aAAa,MAAM;AAAA,IACvB;AAAA,IACA,gBAAgB;AAAA,IAChB;AAAA,IACA,KAAK,aAAa;AAAA,EACpB;AACA,wBAAsB,aAAa;AACnC,MAAI,mBAAmB;AACrB,UAAM,WAAW,MAAM;AAAA,MACrB;AAAA,MACA,GAAG,gBAAgB,gBAAgB,OAAO,gBAAgB,aAAa,KAAK,EAAE;AAAA,MAC9E;AAAA,MACA,KAAK;AAAA,IACP;AACA,0BAAsB;AACtB,UAAM,aAAa,MAAM;AAAA,MACvB;AAAA,MACA,GAAG,gBAAgB,OAAO,SAAS,gBAAgB,IAAI,KAAK,EAAE;AAAA,MAC9D;AAAA,MACA,KAAK;AAAA,IACP;AACA,0BAAsB;AACtB,UAAM,aAAa,MAAM;AAAA,MACvB;AAAA,MACA,GAAG,gBAAgB,OAAO,SAAS,gBAAgB,IAAI,KAAK,EAAE;AAAA,MAC9D;AAAA,MACA,KAAK;AAAA,IACP;AACA,0BAAsB;AACtB,UAAM;AAAA,MACJ;AAAA,MACA,GAAG,gBAAgB,eAAe,iBAAiB,gBAAgB,YAAY,KAAK,EAAE;AAAA,MACtF;AAAA,MACA,KAAK;AAAA,IACP;AAAA,EACF,OAAO;AACL,UAAM,cAAc,MAAM;AAAA,MACxB;AAAA,MACA,GAAG,YAAY,OAAO,SAAS,YAAY,IAAI,KAAK,EAAE;AAAA,MACtD;AAAA,MACA,KAAK;AAAA,IACP;AACA,0BAAsB;AACtB,UAAM;AAAA,MACJ;AAAA,MACA,GAAG,YAAY,SAAS,YAAY,YAAY,MAAM,KAAK,EAAE;AAAA,MAC7D;AAAA,MACA,KAAK;AAAA,IACP;AAAA,EACF;AACA,QAAM,gBAAc,cAAS,KAAK,MAAd,mBAAiB,UAAU,UAAS,OAAO;AAC/D,QAAM,iBAAe,cAAS,KAAK,MAAd,mBAAiB,UAAU,WAAU,OAAO;AACjE,QAAMD,KAAI,CAAC,aAAa;AACxB,QAAMC,KAAI,CAAC,cAAc;AACzB,QAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,QAAM,UAAU,kBAAkB,MAAM,CAAC,CAAC;AAC1C,MAAI,KAAK,SAAS,aAAa;AAC7B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAAA,EACtB;AACA,QAAM,YAAY,GAAG,UAAUD,IAAGC,IAAG,YAAY,aAAa,OAAO;AACrE,QAAM,QAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AAC7D,QAAM,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,UAAU;AACrE,WAAS,UAAU,QAAQ,EAAE,KAAK,CAACU,IAAGb,IAAG,UAAU;AACjD,UAAM,QAAQ,eAAQ,MAAMA,EAAC,CAAC;AAC9B,UAAM,YAAY,MAAM,KAAK,WAAW;AACxC,QAAI,aAAa;AACjB,QAAI,aAAa;AACjB,QAAI,WAAW;AACb,YAAM,QAAQ,OAAO,8BAA8B;AACnD,YAAM,YAAY,MAAM,KAAK,SAAS;AACtC,UAAI,WAAW;AACb,qBAAa,WAAW,UAAU,CAAC,CAAC;AACpC,qBAAa,WAAW,UAAU,CAAC,CAAC;AAAA,MACtC;AAAA,IACF;AACA,UAAM,gBAAgB,aAAa,cAAc;AACjD,QAAI,gBAAgBE,KAAI,UAAU;AAClC,QAAIF,OAAM,KAAKA,OAAM,GAAG;AACtB,sBAAgB;AAAA,IAClB;AACA,UAAM,KAAK,aAAa,aAAa,aAAa,KAAK,gBAAgB,OAAO,GAAG;AAAA,EACnF,CAAC;AACD,MAAI,qBAAqB,aAAa,aAAa,KAAK;AACtD,UAAM,YAAY,GAAG;AAAA,MACnBE;AAAA,MACAC,KAAI,aAAa,aAAa;AAAA,MAC9BD,KAAI;AAAA,MACJC,KAAI,aAAa,aAAa;AAAA,MAC9B;AAAA,IACF;AACA,UAAM,cAAc,SAAS,OAAO,MAAM,SAAS;AACnD,gBAAY,KAAK,SAAS,UAAU;AAAA,EACtC;AACA,mBAAiB,MAAM,KAAK;AAC5B,OAAK,YAAY,SAAS,OAAO;AAC/B,WAAO,kBAAkB,KAAK,MAAM,KAAK;AAAA,EAC3C;AACA,SAAO;AACT;AACA,OAAO,gBAAgB,gBAAgB;AACvC,eAAe,SAAS,aAAa,WAAW,SAAS,QAAQ,IAAI;AACnE,MAAI,cAAc,IAAI;AACpB,WAAO;AAAA,EACT;AACA,QAAM,SAAS,YAAY,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,SAAS,KAAK;AACjF,QAAM,SAAS,WAAW;AAC1B,QAAM,gBAAgB,OAAO,cAAc;AAC3C,QAAM,QAAQ,MAAM;AAAA,IAClB;AAAA,IACA,cAAc,eAAe,SAAS,CAAC;AAAA,IACvC;AAAA,MACE,OAAO,mBAAmB,WAAW,MAAM,IAAI;AAAA;AAAA,MAE/C,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,MAAI;AACJ,MAAI,CAAC,eAAe;AAClB,UAAM,YAAY,MAAM,SAAS,CAAC;AAClC,eAAW,SAAS,UAAU,UAAU;AACtC,YAAM,cAAc,MAAM,YAAY,WAAW,QAAQ,GAAG,EAAE,WAAW,QAAQ,GAAG;AACpF,UAAI,OAAO;AACT,cAAM,aAAa,SAAS,KAAK;AAAA,MACnC;AAAA,IACF;AACA,WAAO,MAAM,QAAQ;AACrB,SAAK,UAAU;AAAA,EACjB,OAAO;AACL,UAAM,MAAM,MAAM,SAAS,CAAC;AAC5B,UAAM,KAAK,eAAQ,KAAK;AACxB,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AACA,SAAO,KAAK,aAAa,aAAa,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,KAAK,SAAS,IAAI,OAAO,GAAG;AACtF,SAAO,KAAK;AACd;AACA,OAAO,UAAU,SAAS;AAI1B,IAAI,oBAAoC,OAAO,CAAC,aAAa;AAC3D,UAAQ,UAAU;AAAA,IAChB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IAET,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,EACX;AACF,GAAG,mBAAmB;AACtB,eAAe,WAAW,QAAQ,YAAY,EAAE,OAAO,GAAG;AA7jJ1D;AA8jJE,QAAM,EAAE,aAAa,WAAW,IAAI,cAAc,UAAU;AAC5D,aAAW,aAAa,eAAe;AACvC,QAAM,gBAAgB;AACtB,QAAM,WAAW,WAAW;AAC5B,aAAW,SAAS,WAAW,SAAS,OAAO;AAC/C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACT,IAAI,MAAM,YAAY,QAAQ,YAAY,eAAe,UAAU,CAAC;AACpE,QAAM,UAAU,WAAW,WAAW;AACtC,MAAI,YAAY;AAChB,MAAI;AACJ,MAAI,YAAY,cAAc,WAAW,YAAU,sCAAQ,WAAR,mBAAgB,gBAAe;AAChF,iBAAY,sCAAQ,WAAR,mBAAgB,cAAc,QAAQ,YAAY,WAAW;AACzE,WAAO,SAAS,OAAO,SAAS,cAAc,EAAE,KAAK,SAAS,oBAAoB,EAAE,KAAK,cAAc,SAAS,EAAE,KAAK,UAAU,QAAQ;AAAA,EAC3I;AACA,QAAM,UAAU;AAAA,IACd,eAAe,WAAW;AAAA,IAC1B,YAAY,WAAW,cAAc;AAAA,IACrC,OAAO,WAAW;AAAA,IAClB,KAAK,WAAW;AAAA,IAChB,SAAS,WAAW,WAAW;AAAA,IAC/B,aAAa;AAAA,EACf;AACA,MAAI,SAAS;AACb,MAAI,MAAM;AACR,KAAC,EAAE,OAAO,SAAS,MAAM,MAAM,IAAI,MAAM;AAAA,MACvC;AAAA,MACA,YAAY,cAAc,WAAW,UAAU;AAAA,MAC/C;AAAA,IACF;AAAA,EACF,OAAO;AACL,KAAC,EAAE,OAAO,SAAS,MAAM,MAAM,IAAI,MAAM;AAAA,MACvC;AAAA,MACA,YAAY,cAAc,WAAW,UAAU;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AACA,QAAM,EAAE,OAAO,iBAAiB,MAAM,aAAa,IAAI,MAAM;AAAA,IAC3D;AAAA,IACA,cAAc,cAAc,WAAW,YAAY;AAAA,IACnD;AAAA,EACF;AACA,aAAW,QAAQ;AACnB,QAAM,gBAAgB;AACtB,QAAM,cAAa,yCAAY,UAAS;AACxC,QAAM,YAAY,KAAK,IAAI,MAAM,QAAQ,aAAa,MAAM,IAAI;AAChE,QAAM,cAAc,KAAK,IAAI,KAAK,SAAS,gBAAgB,IAAG,yCAAY,WAAU,CAAC,IAAI;AACzF,QAAMD,KAAI,CAAC,aAAa;AACxB,QAAMC,KAAI,CAAC,cAAc;AACzB,eAAa;AAAA,IACX;AAAA,IACA,gBAAgB,UAAU,aAAa,KAAK,QAAQ,CAAC,YAAY,KAAK,SAAS,KAAK;AAAA,EACtF;AACA,UAAQ;AAAA,IACN;AAAA,IACA,gBAAgB,UAAU,aAAa,KAAK,QAAQ,CAAC,YAAY,KAAK,SAAS,KAAK;AAAA,EACtF;AACA,kBAAgB;AAAA,IACd;AAAA,IACA,gBAAgB,UAAU,aAAa,IAAI,aAAa,QAAQ,IAAI,iBAAiB,QAAQ,CAAC,YAAY,KAAK,SAAS,KAAK;AAAA,EAC/H;AACA,MAAI;AACJ,QAAM,EAAE,IAAI,GAAG,IAAI;AACnB,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,WAAW,SAAS,aAAa;AACnC,UAAM,KAAK,GAAQ,IAAI,QAAQ;AAC/B,UAAM,WAAW,kBAAkB,YAAY,CAAC,CAAC;AACjD,UAAM,YAAY,MAAM,KAAK,GAAG,KAAK,uBAAuBD,IAAGC,IAAG,YAAY,aAAa,MAAM,CAAC,GAAG,QAAQ,IAAI,GAAG,UAAUD,IAAGC,IAAG,YAAY,aAAa,QAAQ;AACrK,YAAQ,SAAS,OAAO,MAAM,WAAW,cAAc;AACvD,UAAM,KAAK,SAAS,uBAAuB,EAAE,KAAK,SAAS,YAAY,YAAY,IAAI;AAAA,EACzF,OAAO;AACL,YAAQ,SAAS,OAAO,QAAQ,cAAc;AAC9C,UAAM,KAAK,SAAS,+BAA+B,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,MAAM,MAAM,CAAC,EAAE,KAAK,MAAM,MAAM,CAAC,EAAE,KAAK,KAAKD,EAAC,EAAE,KAAK,KAAKC,EAAC,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,UAAU,WAAW;AACrM,UAAM,WAAW,cAAc,cAAc,WAAW;AACxD,QAAI,UAAU;AACZ,YAAM,OAAO,SAAS,OAAO,MAAM;AACnC,YAAM,QAAQD,KAAI;AAClB,YAAM,KAAKC,KAAI,KAAK,OAAO,MAAM,KAAK,CAAC;AACvC,YAAMc,MAAKd,KAAI,cAAc,KAAK,OAAO,MAAM,KAAK,CAAC;AACrD,WAAK,KAAK,MAAM,KAAK,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,MAAM,KAAK,EAAE,KAAK,MAAMc,GAAE,EAAE,KAAK,gBAAgB,GAAG,EAAE,KAAK,UAAU,kBAAkB,QAAQ,CAAC;AAAA,IAC7I;AAAA,EACF;AACA,mBAAiB,YAAY,KAAK;AAClC,aAAW,SAAS;AACpB,aAAW,YAAY,SAAS,OAAO;AACrC,WAAO,kBAAkB,KAAK,YAAY,KAAK;AAAA,EACjD;AACA,SAAO;AACT;AACA,OAAO,YAAY,YAAY;AAG/B,IAAI,aAAa;AAAA,EACf;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,QAAQ,WAAW,WAAW;AAAA,IACxC,iBAAiB,CAAC,YAAY;AAAA,IAC9B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,OAAO;AAAA,IACjB,iBAAiB,CAAC,aAAa;AAAA,IAC/B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,YAAY,MAAM;AAAA,IAC5B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,cAAc,WAAW,oBAAoB,YAAY;AAAA,IACnE,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,MAAM,YAAY,UAAU;AAAA,IACtC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,MAAM;AAAA,IAChB,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,YAAY,WAAW,UAAU;AAAA,IAC3C,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,WAAW,SAAS;AAAA,IAC9B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,cAAc,QAAQ;AAAA,IAChC,iBAAiB,CAAC,YAAY;AAAA,IAC9B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,aAAa,QAAQ;AAAA,IAC/B,iBAAiB,CAAC,WAAW;AAAA,IAC7B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,YAAY,oBAAoB,WAAW;AAAA,IACrD,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,UAAU,iBAAiB,eAAe;AAAA,IACpD,iBAAiB,CAAC,eAAe;AAAA,IACjC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,eAAe;AAAA,IACzB,iBAAiB,CAAC,cAAc;AAAA,IAChC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,QAAQ,mBAAmB;AAAA,IACrC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,mBAAmB,iBAAiB,YAAY,gBAAgB;AAAA,IAC1E,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,SAAS,cAAc;AAAA,IACjC,iBAAiB,CAAC,YAAY;AAAA,IAC9B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,QAAQ,eAAe;AAAA,IACjC,iBAAiB,CAAC,UAAU;AAAA,IAC5B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,MAAM;AAAA,IAChB,iBAAiB,CAAC,UAAU;AAAA,IAC5B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,aAAa,SAAS;AAAA,IAChC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,WAAW,SAAS;AAAA,IAC9B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,YAAY,gBAAgB;AAAA,IACtC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,OAAO,UAAU;AAAA,IAC3B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,wBAAwB;AAAA,IAClC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,OAAO,qBAAqB;AAAA,IACtC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,QAAQ,gBAAgB;AAAA,IAClC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,oBAAoB,SAAS;AAAA,IACvC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,YAAY,qBAAqB,iBAAiB;AAAA,IAC5D,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,WAAW,UAAU;AAAA,IAC/B,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,oBAAoB,aAAa;AAAA,IAC3C,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,YAAY,eAAe;AAAA,IACrC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,cAAc,kBAAkB;AAAA,IAC1C,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,eAAe,kBAAkB;AAAA,IAC3C,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,gBAAgB,kBAAkB;AAAA,IAC5C,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,aAAa,UAAU,kBAAkB;AAAA,IACnD,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,SAAS,aAAa,mBAAmB;AAAA,IACnD,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,eAAe,mBAAmB;AAAA,IAC5C,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,WAAW,gBAAgB;AAAA,IACrC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,WAAW,iBAAiB;AAAA,IACtC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,oBAAoB,YAAY,gBAAgB;AAAA,IAC1D,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,YAAY;AAAA,IACtB,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,iBAAiB,CAAC,qBAAqB;AAAA,IACvC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS,CAAC,gBAAgB;AAAA,IAC1B,SAAS;AAAA,EACX;AACF;AACA,IAAI,mBAAmC,OAAO,MAAM;AAClD,QAAM,qBAAqB;AAAA;AAAA,IAEzB;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,EACF;AACA,QAAM,UAAU;AAAA,IACd,GAAG,OAAO,QAAQ,kBAAkB;AAAA,IACpC,GAAG,WAAW,QAAQ,CAAC,UAAU;AAC/B,YAAM,UAAU;AAAA,QACd,MAAM;AAAA,QACN,GAAG,aAAa,QAAQ,MAAM,UAAU,CAAC;AAAA,QACzC,GAAG,qBAAqB,QAAQ,MAAM,kBAAkB,CAAC;AAAA,MAC3D;AACA,aAAO,QAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM,OAAO,CAAC;AAAA,IACtD,CAAC;AAAA,EACH;AACA,SAAO,OAAO,YAAY,OAAO;AACnC,GAAG,kBAAkB;AACrB,IAAI,UAAU,iBAAiB;AAC/B,SAAS,aAAa,OAAO;AAC3B,SAAO,SAAS;AAClB;AACA,OAAO,cAAc,cAAc;AAGnC,IAAI,YAA4B,oBAAI,IAAI;AACxC,eAAe,WAAW,MAAM,MAAM,eAAe;AACnD,MAAI;AACJ,MAAI;AACJ,MAAI,KAAK,UAAU,QAAQ;AACzB,QAAI,KAAK,MAAM,KAAK,IAAI;AACtB,WAAK,QAAQ;AAAA,IACf,OAAO;AACL,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AACA,QAAM,eAAe,KAAK,QAAQ,QAAQ,KAAK,KAAK,IAAI;AACxD,MAAI,CAAC,cAAc;AACjB,UAAM,IAAI,MAAM,kBAAkB,KAAK,KAAK,6BAA6B;AAAA,EAC3E;AACA,MAAI,KAAK,MAAM;AACb,QAAI;AACJ,QAAI,cAAc,OAAO,kBAAkB,WAAW;AACpD,eAAS;AAAA,IACX,WAAW,KAAK,YAAY;AAC1B,eAAS,KAAK,cAAc;AAAA,IAC9B;AACA,YAAQ,KAAK,OAAO,OAAO,EAAE,KAAK,cAAc,KAAK,IAAI,EAAE,KAAK,UAAU,UAAU,IAAI;AACxF,SAAK,MAAM,aAAa,OAAO,MAAM,aAAa;AAAA,EACpD,OAAO;AACL,SAAK,MAAM,aAAa,MAAM,MAAM,aAAa;AACjD,YAAQ;AAAA,EACV;AACA,MAAI,KAAK,SAAS;AAChB,OAAG,KAAK,SAAS,KAAK,OAAO;AAAA,EAC/B;AACA,YAAU,IAAI,KAAK,IAAI,KAAK;AAC5B,MAAI,KAAK,cAAc;AACrB,UAAM,KAAK,SAAS,MAAM,KAAK,OAAO,IAAI,YAAY;AAAA,EACxD;AACA,SAAO;AACT;AACA,OAAO,YAAY,YAAY;AAC/B,IAAI,cAA8B,OAAO,CAAC,MAAM,SAAS;AACvD,YAAU,IAAI,KAAK,IAAI,IAAI;AAC7B,GAAG,aAAa;AAChB,IAAI,SAAyB,OAAO,MAAM;AACxC,YAAU,MAAM;AAClB,GAAG,OAAO;AACV,IAAI,eAA+B,OAAO,CAAC,SAAS;AAClD,QAAM,KAAK,UAAU,IAAI,KAAK,EAAE;AAChC,MAAI;AAAA,IACF;AAAA,IACA,KAAK;AAAA,IACL;AAAA,IACA,gBAAgB,KAAK,IAAI,KAAK,QAAQ,IAAI,KAAK,OAAO,KAAK,QAAQ,IAAI;AAAA,EACzE;AACA,QAAM,UAAU;AAChB,QAAM,OAAO,KAAK,QAAQ;AAC1B,MAAI,KAAK,aAAa;AACpB,OAAG;AAAA,MACD;AAAA,MACA,gBAAgB,KAAK,IAAI,OAAO,KAAK,QAAQ,KAAK,QAAQ,KAAK,IAAI,KAAK,SAAS,IAAI,WAAW;AAAA,IAClG;AAAA,EACF,OAAO;AACL,OAAG,KAAK,aAAa,eAAe,KAAK,IAAI,OAAO,KAAK,IAAI,GAAG;AAAA,EAClE;AACA,SAAO;AACT,GAAG,cAAc;", "names": ["t", "e", "s", "n", "o", "a", "h", "r", "i", "c", "l", "u", "p", "f", "d", "g", "M", "k", "b", "y", "m", "P", "v", "S", "O", "L", "T", "D", "A", "p", "i", "t", "x", "y", "w", "h", "s", "q", "d", "r", "m", "f", "g", "_", "_a", "_2", "i2", "y2"]}