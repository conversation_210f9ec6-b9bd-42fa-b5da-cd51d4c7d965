{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 12427970797809321433, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 7627041243772429328], [3150220818285335163, "url", false, 14199394255531846341], [3191507132440681679, "serde_untagged", false, 12924922332816925429], [4071963112282141418, "serde_with", false, 2325131398589658848], [4899080583175475170, "semver", false, 16978511449633190971], [5986029879202738730, "log", false, 2351528888146949315], [6606131838865521726, "ctor", false, 12096649712254066326], [7170110829644101142, "json_patch", false, 11716537829792061409], [8319709847752024821, "uuid", false, 8852148228219361089], [9010263965687315507, "http", false, 9326335740251934562], [9451456094439810778, "regex", false, 11610437707374617284], [9556762810601084293, "brotli", false, 6255207127309063429], [9689903380558560274, "serde", false, 11664928829986004705], [10806645703491011684, "thiserror", false, 12814716657068513109], [11989259058781683633, "dunce", false, 11858794786811574169], [13625485746686963219, "anyhow", false, 6546084173510242191], [15367738274754116744, "serde_json", false, 2199351709971545488], [15609422047640926750, "toml", false, 1929144316205022447], [15622660310229662834, "walkdir", false, 14757043263921548175], [15932120279885307830, "memchr", false, 10218923838535464402], [17146114186171651583, "infer", false, 1896725725876957537], [17155886227862585100, "glob", false, 2178336415492610584], [17186037756130803222, "phf", false, 4478376533272114497]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-0c87f269eb2a09ff\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}