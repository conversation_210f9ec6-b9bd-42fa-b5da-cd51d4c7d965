
# WeMa IA Extracteur Portable
$ErrorActionPreference = "SilentlyContinue"

Write-Host "WeMa IA - Extraction en cours..." -ForegroundColor Green

# Créer dossier temporaire
$tempDir = "$env:TEMP\WeMa_IA_" + (Get-Random)
New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

# Extraire archive (sera intégrée)
$archivePath = "$PSScriptRoot\wema-ia-portable.7z"
$7zipPath = "$env:ProgramFiles\7-Zip\7z.exe"

if (Test-Path $7zipPath) {
    & "$7zipPath" x "$archivePath" -o"$tempDir" -y | Out-Null
} else {
    # Fallback: extraction PowerShell native
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::ExtractToDirectory($archivePath, $tempDir)
}

# Lancer WeMa IA
$exePath = "$tempDir\WeMa IA.exe"
if (Test-Path $exePath) {
    Start-Process -FilePath $exePath -WorkingDirectory $tempDir
    Write-Host "WeMa IA lance avec succes!" -ForegroundColor Green
} else {
    Write-Host "Erreur: Impossible de trouver WeMa IA.exe" -ForegroundColor Red
    Read-Host "Appuyez sur Entree pour fermer"
}
