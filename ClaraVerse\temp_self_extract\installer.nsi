
!define APPNAME "WeMa IA"
!define APPVERSION "0.1.2"
!define APPEXE "WeMa IA.exe"
!define APPDIR "WeMa_IA"

Name "${APPNAME}"
OutFile "C:\Users\<USER>\Downloads\WeMa_IA_Setup.exe"
InstallDir "$APPDATA\${APPDIR}"
RequestExecutionLevel user
SilentI<PERSON><PERSON> silent
AutoCloseWindow true

; Interface moderne
!include "MUI2.nsh"
!define MUI_ICON "app\\resources\\wema-icon-256.ico"
!define MUI_HEADERIMAGE
!define MUI_HEADERIMAGE_RIGHT
!define MUI_HEADERIMAGE_BITMAP "app\\resources\\wema-logo-light.png"

; Pages (aucune page = installation silencieuse)
!insertmacro MUI_PAGE_INSTFILES

; Langues
!insertmacro MUI_LANGUAGE "French"

Section "Install" SecInstall
    SetOutPath $INSTDIR
    
    ; Copier tous les fichiers de l'application
    File /r "app\\*.*"
    
    ; <PERSON><PERSON><PERSON> raccourcis
    CreateDirectory "$SMPROGRAMS\${APPNAME}"
    CreateShortCut "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk" "$INSTDIR\${APPEXE}"
    CreateShortCut "$DESKTOP\${APPNAME}.lnk" "$INSTDIR\${APPEXE}"
    
    ; Créer désinstalleur
    WriteUninstaller "$INSTDIR\\Uninstall.exe"
    
    ; Ajouter au registre pour Ajout/Suppression programmes
    WriteRegStr HKCU "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\${APPNAME}" "DisplayName" "${APPNAME}"
    WriteRegStr HKCU "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\${APPNAME}" "UninstallString" "$INSTDIR\\Uninstall.exe"
    WriteRegStr HKCU "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\${APPNAME}" "DisplayVersion" "${APPVERSION}"
    WriteRegStr HKCU "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\${APPNAME}" "Publisher" "WeMa IA Team"
    
    ; Lancer l'application automatiquement
    Exec "$INSTDIR\${APPEXE}"
SectionEnd

Section "Uninstall"
    ; Supprimer fichiers
    RMDir /r "$INSTDIR"
    
    ; Supprimer raccourcis
    Delete "$SMPROGRAMS\${APPNAME}\\*.*"
    RMDir "$SMPROGRAMS\${APPNAME}"
    Delete "$DESKTOP\${APPNAME}.lnk"
    
    ; Supprimer du registre
    DeleteRegKey HKCU "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\${APPNAME}"
SectionEnd
