
!define APPNAME "WeMa IA"
!define APPVERSION "0.1.2"
!define APPEXE "WeMa IA.exe"

Name "${APPNAME}"
OutFile "C:\Users\<USER>\Downloads\WeMa_IA_AutoExtract.exe"
InstallDir "$APPDATA\WeMa_IA"
RequestExecutionLevel user

Page directory
Page instfiles

Section "Install"
    SetOutPath $INSTDIR
    
    ; Copier tous les fichiers
    File /r "temp_self_extract\app\*.*"
    
    ; Créer raccourcis
    CreateDirectory "$SMPROGRAMS\WeMa IA"
    CreateShortCut "$SMPROGRAMS\WeMa IA\WeMa IA.lnk" "$INSTDIR\WeMa IA.exe"
    CreateShortCut "$DESKTOP\WeMa IA.lnk" "$INSTDIR\WeMa IA.exe"
    
    ; Créer désinstalleur
    WriteUninstaller "$INSTDIR\Uninstall.exe"
    
    ; Lancer l'application
    Exec "$INSTDIR\WeMa IA.exe"
SectionEnd

Section "Uninstall"
    Delete "$INSTDIR\*.*"
    RMDir /r "$INSTDIR"
    Delete "$SMPROGRAMS\WeMa IA\*.*"
    RMDir "$SMPROGRAMS\WeMa IA"
    Delete "$DESKTOP\WeMa IA.lnk"
SectionEnd
