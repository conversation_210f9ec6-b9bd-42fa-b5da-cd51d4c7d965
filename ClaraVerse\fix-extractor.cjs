#!/usr/bin/env node

/**
 * 🔧 CORRECTIF EXTRACTEUR - CHEMIN SIMPLE
 * Corrige le problème de chemin d'extraction
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 CORRECTIF EXTRACTEUR - CHEMIN SIMPLE');
console.log('Objectif: Extraction dans un chemin accessible');
console.log('='.repeat(50));

// Configuration SFX corrigée avec chemin simple
const sfxConfigFixed = `
;!@Install@!UTF-8!
Title=WeMa IA - Assistant IA Professionnel
Text=Installation de WeMa IA en cours...\\nVeuillez patienter pendant l'extraction.
ExtractTitle=WeMa IA - Extraction
ExtractDialogText=Extraction des fichiers WeMa IA...
ExtractDialogWidth=400
ExtractDialogHeight=180
ExtractCancelText=Annuler
BeginPrompt=Voulez-vous lancer WeMa IA - Assistant IA Professionnel ?
RunProgram="%%T\\WeMa IA.exe"
Delete="%%T"
Path="C:\\Temp\\WeMaIA"
SavePath
Silent=0
Overwrite=1
Update=U
Miscflags=4
ExecuteFile="%%T\\WeMa IA.exe"
ExecuteParameters=""
;!@InstallEnd@!
`;

fs.writeFileSync('sfx-config-fixed.txt', sfxConfigFixed);

// Recréer le SFX avec la config corrigée
console.log('🔧 Recréation SFX avec chemin corrigé...');

const winrarPath = 'C:\\Program Files\\WinRAR\\WinRAR.exe';
const archiveName = 'wema-ia-professional.rar';
const sfxName = 'WeMa_IA_Working.exe';

try {
    // Vérifier si l'archive existe
    if (!fs.existsSync(archiveName)) {
        console.log('📦 Recréation de l\'archive...');
        const portableDir = 'release/win-unpacked';
        const winrarCmd = `"${winrarPath}" a -r -s -m5 -md4096 -ma5 -ep1 "${archiveName}" "${portableDir}\\*"`;
        execSync(winrarCmd, { stdio: 'inherit' });
    }
    
    // Créer nouveau SFX avec config corrigée
    const sfxCmd = `"${winrarPath}" s -z"sfx-config-fixed.txt" -sfx "${archiveName}" "${sfxName}"`;
    execSync(sfxCmd, { stdio: 'inherit' });
    
    console.log('✅ SFX corrigé créé:', sfxName);
    
    // Copier vers Downloads
    const downloadsPath = path.join(require('os').homedir(), 'Downloads', sfxName);
    fs.copyFileSync(sfxName, downloadsPath);
    
    console.log('📥 Copié vers Downloads:', downloadsPath);
    
} catch (error) {
    console.error('❌ Erreur:', error.message);
}

// Nettoyage
try {
    fs.unlinkSync('sfx-config-fixed.txt');
} catch (e) {
    // Ignore
}

console.log('');
console.log('🎉 EXTRACTEUR CORRIGÉ CRÉÉ !');
console.log('📁 FICHIER: WeMa_IA_Fixed.exe');
console.log('🎯 CHEMIN D\'EXTRACTION: %TEMP%\\WeMaIA (simple)');
console.log('✅ Testez maintenant le nouveau fichier !');
