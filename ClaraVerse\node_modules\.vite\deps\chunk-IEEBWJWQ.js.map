{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/chunk-VV3M67IP.mjs"], "sourcesContent": ["import {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/utils/lineWithOffset.ts\nvar markerOffsets = {\n  aggregation: 18,\n  extension: 18,\n  composition: 18,\n  dependency: 6,\n  lollipop: 13.5,\n  arrow_point: 4\n};\nfunction calculateDeltaAndAngle(point1, point2) {\n  if (point1 === void 0 || point2 === void 0) {\n    return { angle: 0, deltaX: 0, deltaY: 0 };\n  }\n  point1 = pointTransformer(point1);\n  point2 = pointTransformer(point2);\n  const [x1, y1] = [point1.x, point1.y];\n  const [x2, y2] = [point2.x, point2.y];\n  const deltaX = x2 - x1;\n  const deltaY = y2 - y1;\n  return { angle: Math.atan(deltaY / deltaX), deltaX, deltaY };\n}\n__name(calculateDeltaAndAngle, \"calculateDeltaAndAngle\");\nvar pointTransformer = /* @__PURE__ */ __name((data) => {\n  if (Array.isArray(data)) {\n    return { x: data[0], y: data[1] };\n  }\n  return data;\n}, \"pointTransformer\");\nvar getLineFunctionsWithOffset = /* @__PURE__ */ __name((edge) => {\n  return {\n    x: /* @__PURE__ */ __name(function(d, i, data) {\n      let offset = 0;\n      const DIRECTION = pointTransformer(data[0]).x < pointTransformer(data[data.length - 1]).x ? \"left\" : \"right\";\n      if (i === 0 && Object.hasOwn(markerOffsets, edge.arrowTypeStart)) {\n        const { angle, deltaX } = calculateDeltaAndAngle(data[0], data[1]);\n        offset = markerOffsets[edge.arrowTypeStart] * Math.cos(angle) * (deltaX >= 0 ? 1 : -1);\n      } else if (i === data.length - 1 && Object.hasOwn(markerOffsets, edge.arrowTypeEnd)) {\n        const { angle, deltaX } = calculateDeltaAndAngle(\n          data[data.length - 1],\n          data[data.length - 2]\n        );\n        offset = markerOffsets[edge.arrowTypeEnd] * Math.cos(angle) * (deltaX >= 0 ? 1 : -1);\n      }\n      const differenceToEnd = Math.abs(\n        pointTransformer(d).x - pointTransformer(data[data.length - 1]).x\n      );\n      const differenceInYEnd = Math.abs(\n        pointTransformer(d).y - pointTransformer(data[data.length - 1]).y\n      );\n      const differenceToStart = Math.abs(pointTransformer(d).x - pointTransformer(data[0]).x);\n      const differenceInYStart = Math.abs(pointTransformer(d).y - pointTransformer(data[0]).y);\n      const startMarkerHeight = markerOffsets[edge.arrowTypeStart];\n      const endMarkerHeight = markerOffsets[edge.arrowTypeEnd];\n      const extraRoom = 1;\n      if (differenceToEnd < endMarkerHeight && differenceToEnd > 0 && differenceInYEnd < endMarkerHeight) {\n        let adjustment = endMarkerHeight + extraRoom - differenceToEnd;\n        adjustment *= DIRECTION === \"right\" ? -1 : 1;\n        offset -= adjustment;\n      }\n      if (differenceToStart < startMarkerHeight && differenceToStart > 0 && differenceInYStart < startMarkerHeight) {\n        let adjustment = startMarkerHeight + extraRoom - differenceToStart;\n        adjustment *= DIRECTION === \"right\" ? -1 : 1;\n        offset += adjustment;\n      }\n      return pointTransformer(d).x + offset;\n    }, \"x\"),\n    y: /* @__PURE__ */ __name(function(d, i, data) {\n      let offset = 0;\n      const DIRECTION = pointTransformer(data[0]).y < pointTransformer(data[data.length - 1]).y ? \"down\" : \"up\";\n      if (i === 0 && Object.hasOwn(markerOffsets, edge.arrowTypeStart)) {\n        const { angle, deltaY } = calculateDeltaAndAngle(data[0], data[1]);\n        offset = markerOffsets[edge.arrowTypeStart] * Math.abs(Math.sin(angle)) * (deltaY >= 0 ? 1 : -1);\n      } else if (i === data.length - 1 && Object.hasOwn(markerOffsets, edge.arrowTypeEnd)) {\n        const { angle, deltaY } = calculateDeltaAndAngle(\n          data[data.length - 1],\n          data[data.length - 2]\n        );\n        offset = markerOffsets[edge.arrowTypeEnd] * Math.abs(Math.sin(angle)) * (deltaY >= 0 ? 1 : -1);\n      }\n      const differenceToEnd = Math.abs(\n        pointTransformer(d).y - pointTransformer(data[data.length - 1]).y\n      );\n      const differenceInXEnd = Math.abs(\n        pointTransformer(d).x - pointTransformer(data[data.length - 1]).x\n      );\n      const differenceToStart = Math.abs(pointTransformer(d).y - pointTransformer(data[0]).y);\n      const differenceInXStart = Math.abs(pointTransformer(d).x - pointTransformer(data[0]).x);\n      const startMarkerHeight = markerOffsets[edge.arrowTypeStart];\n      const endMarkerHeight = markerOffsets[edge.arrowTypeEnd];\n      const extraRoom = 1;\n      if (differenceToEnd < endMarkerHeight && differenceToEnd > 0 && differenceInXEnd < endMarkerHeight) {\n        let adjustment = endMarkerHeight + extraRoom - differenceToEnd;\n        adjustment *= DIRECTION === \"up\" ? -1 : 1;\n        offset -= adjustment;\n      }\n      if (differenceToStart < startMarkerHeight && differenceToStart > 0 && differenceInXStart < startMarkerHeight) {\n        let adjustment = startMarkerHeight + extraRoom - differenceToStart;\n        adjustment *= DIRECTION === \"up\" ? -1 : 1;\n        offset += adjustment;\n      }\n      return pointTransformer(d).y + offset;\n    }, \"y\")\n  };\n}, \"getLineFunctionsWithOffset\");\nif (void 0) {\n  const { it, expect, describe } = void 0;\n  describe(\"calculateDeltaAndAngle\", () => {\n    it(\"should calculate the angle and deltas between two points\", () => {\n      expect(calculateDeltaAndAngle([0, 0], [0, 1])).toStrictEqual({\n        angle: 1.5707963267948966,\n        deltaX: 0,\n        deltaY: 1\n      });\n      expect(calculateDeltaAndAngle([1, 0], [0, -1])).toStrictEqual({\n        angle: 0.7853981633974483,\n        deltaX: -1,\n        deltaY: -1\n      });\n      expect(calculateDeltaAndAngle({ x: 1, y: 0 }, [0, -1])).toStrictEqual({\n        angle: 0.7853981633974483,\n        deltaX: -1,\n        deltaY: -1\n      });\n      expect(calculateDeltaAndAngle({ x: 1, y: 0 }, { x: 1, y: 0 })).toStrictEqual({\n        angle: NaN,\n        deltaX: 0,\n        deltaY: 0\n      });\n    });\n    it(\"should calculate the angle and deltas if one point in undefined\", () => {\n      expect(calculateDeltaAndAngle(void 0, [0, 1])).toStrictEqual({\n        angle: 0,\n        deltaX: 0,\n        deltaY: 0\n      });\n      expect(calculateDeltaAndAngle([0, 1], void 0)).toStrictEqual({\n        angle: 0,\n        deltaX: 0,\n        deltaY: 0\n      });\n    });\n  });\n}\n\nexport {\n  getLineFunctionsWithOffset\n};\n"], "mappings": ";;;;;AAKA,IAAI,gBAAgB;AAAA,EAClB,aAAa;AAAA,EACb,WAAW;AAAA,EACX,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AACf;AACA,SAAS,uBAAuB,QAAQ,QAAQ;AAC9C,MAAI,WAAW,UAAU,WAAW,QAAQ;AAC1C,WAAO,EAAE,OAAO,GAAG,QAAQ,GAAG,QAAQ,EAAE;AAAA,EAC1C;AACA,WAAS,iBAAiB,MAAM;AAChC,WAAS,iBAAiB,MAAM;AAChC,QAAM,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACpC,QAAM,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACpC,QAAM,SAAS,KAAK;AACpB,QAAM,SAAS,KAAK;AACpB,SAAO,EAAE,OAAO,KAAK,KAAK,SAAS,MAAM,GAAG,QAAQ,OAAO;AAC7D;AACA,OAAO,wBAAwB,wBAAwB;AACvD,IAAI,mBAAmC,OAAO,CAAC,SAAS;AACtD,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAO,EAAE,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE;AAAA,EAClC;AACA,SAAO;AACT,GAAG,kBAAkB;AACrB,IAAI,6BAA6C,OAAO,CAAC,SAAS;AAChE,SAAO;AAAA,IACL,GAAmB,OAAO,SAAS,GAAG,GAAG,MAAM;AAC7C,UAAI,SAAS;AACb,YAAM,YAAY,iBAAiB,KAAK,CAAC,CAAC,EAAE,IAAI,iBAAiB,KAAK,KAAK,SAAS,CAAC,CAAC,EAAE,IAAI,SAAS;AACrG,UAAI,MAAM,KAAK,OAAO,OAAO,eAAe,KAAK,cAAc,GAAG;AAChE,cAAM,EAAE,OAAO,OAAO,IAAI,uBAAuB,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AACjE,iBAAS,cAAc,KAAK,cAAc,IAAI,KAAK,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI;AAAA,MACrF,WAAW,MAAM,KAAK,SAAS,KAAK,OAAO,OAAO,eAAe,KAAK,YAAY,GAAG;AACnF,cAAM,EAAE,OAAO,OAAO,IAAI;AAAA,UACxB,KAAK,KAAK,SAAS,CAAC;AAAA,UACpB,KAAK,KAAK,SAAS,CAAC;AAAA,QACtB;AACA,iBAAS,cAAc,KAAK,YAAY,IAAI,KAAK,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI;AAAA,MACnF;AACA,YAAM,kBAAkB,KAAK;AAAA,QAC3B,iBAAiB,CAAC,EAAE,IAAI,iBAAiB,KAAK,KAAK,SAAS,CAAC,CAAC,EAAE;AAAA,MAClE;AACA,YAAM,mBAAmB,KAAK;AAAA,QAC5B,iBAAiB,CAAC,EAAE,IAAI,iBAAiB,KAAK,KAAK,SAAS,CAAC,CAAC,EAAE;AAAA,MAClE;AACA,YAAM,oBAAoB,KAAK,IAAI,iBAAiB,CAAC,EAAE,IAAI,iBAAiB,KAAK,CAAC,CAAC,EAAE,CAAC;AACtF,YAAM,qBAAqB,KAAK,IAAI,iBAAiB,CAAC,EAAE,IAAI,iBAAiB,KAAK,CAAC,CAAC,EAAE,CAAC;AACvF,YAAM,oBAAoB,cAAc,KAAK,cAAc;AAC3D,YAAM,kBAAkB,cAAc,KAAK,YAAY;AACvD,YAAM,YAAY;AAClB,UAAI,kBAAkB,mBAAmB,kBAAkB,KAAK,mBAAmB,iBAAiB;AAClG,YAAI,aAAa,kBAAkB,YAAY;AAC/C,sBAAc,cAAc,UAAU,KAAK;AAC3C,kBAAU;AAAA,MACZ;AACA,UAAI,oBAAoB,qBAAqB,oBAAoB,KAAK,qBAAqB,mBAAmB;AAC5G,YAAI,aAAa,oBAAoB,YAAY;AACjD,sBAAc,cAAc,UAAU,KAAK;AAC3C,kBAAU;AAAA,MACZ;AACA,aAAO,iBAAiB,CAAC,EAAE,IAAI;AAAA,IACjC,GAAG,GAAG;AAAA,IACN,GAAmB,OAAO,SAAS,GAAG,GAAG,MAAM;AAC7C,UAAI,SAAS;AACb,YAAM,YAAY,iBAAiB,KAAK,CAAC,CAAC,EAAE,IAAI,iBAAiB,KAAK,KAAK,SAAS,CAAC,CAAC,EAAE,IAAI,SAAS;AACrG,UAAI,MAAM,KAAK,OAAO,OAAO,eAAe,KAAK,cAAc,GAAG;AAChE,cAAM,EAAE,OAAO,OAAO,IAAI,uBAAuB,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AACjE,iBAAS,cAAc,KAAK,cAAc,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,UAAU,IAAI,IAAI;AAAA,MAC/F,WAAW,MAAM,KAAK,SAAS,KAAK,OAAO,OAAO,eAAe,KAAK,YAAY,GAAG;AACnF,cAAM,EAAE,OAAO,OAAO,IAAI;AAAA,UACxB,KAAK,KAAK,SAAS,CAAC;AAAA,UACpB,KAAK,KAAK,SAAS,CAAC;AAAA,QACtB;AACA,iBAAS,cAAc,KAAK,YAAY,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,UAAU,IAAI,IAAI;AAAA,MAC7F;AACA,YAAM,kBAAkB,KAAK;AAAA,QAC3B,iBAAiB,CAAC,EAAE,IAAI,iBAAiB,KAAK,KAAK,SAAS,CAAC,CAAC,EAAE;AAAA,MAClE;AACA,YAAM,mBAAmB,KAAK;AAAA,QAC5B,iBAAiB,CAAC,EAAE,IAAI,iBAAiB,KAAK,KAAK,SAAS,CAAC,CAAC,EAAE;AAAA,MAClE;AACA,YAAM,oBAAoB,KAAK,IAAI,iBAAiB,CAAC,EAAE,IAAI,iBAAiB,KAAK,CAAC,CAAC,EAAE,CAAC;AACtF,YAAM,qBAAqB,KAAK,IAAI,iBAAiB,CAAC,EAAE,IAAI,iBAAiB,KAAK,CAAC,CAAC,EAAE,CAAC;AACvF,YAAM,oBAAoB,cAAc,KAAK,cAAc;AAC3D,YAAM,kBAAkB,cAAc,KAAK,YAAY;AACvD,YAAM,YAAY;AAClB,UAAI,kBAAkB,mBAAmB,kBAAkB,KAAK,mBAAmB,iBAAiB;AAClG,YAAI,aAAa,kBAAkB,YAAY;AAC/C,sBAAc,cAAc,OAAO,KAAK;AACxC,kBAAU;AAAA,MACZ;AACA,UAAI,oBAAoB,qBAAqB,oBAAoB,KAAK,qBAAqB,mBAAmB;AAC5G,YAAI,aAAa,oBAAoB,YAAY;AACjD,sBAAc,cAAc,OAAO,KAAK;AACxC,kBAAU;AAAA,MACZ;AACA,aAAO,iBAAiB,CAAC,EAAE,IAAI;AAAA,IACjC,GAAG,GAAG;AAAA,EACR;AACF,GAAG,4BAA4B;AAC/B,IAAI,QAAQ;AACV,QAAM,EAAE,IAAI,QAAQ,SAAS,IAAI;AACjC,WAAS,0BAA0B,MAAM;AACvC,OAAG,4DAA4D,MAAM;AACnE,aAAO,uBAAuB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,cAAc;AAAA,QAC3D,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AACD,aAAO,uBAAuB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,cAAc;AAAA,QAC5D,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AACD,aAAO,uBAAuB,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,cAAc;AAAA,QACpE,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AACD,aAAO,uBAAuB,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,EAAE,cAAc;AAAA,QAC3E,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AACD,OAAG,mEAAmE,MAAM;AAC1E,aAAO,uBAAuB,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,cAAc;AAAA,QAC3D,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AACD,aAAO,uBAAuB,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE,cAAc;AAAA,QAC3D,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH;", "names": []}