{"name": "wema-ia", "version": "0.1.2", "private": true, "description": "WeMa IA – Assistant IA avancé avec OCR, anonymisation GDPR et pipeline RAG. Interface moderne et sécurisée pour vos documents.", "author": {"name": "WeMa IA Team", "email": "<EMAIL>"}, "type": "module", "main": "public/electron.cjs", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "npx vitest run", "test:watch": "npx vitest", "test:ui": "npx vitest --ui", "test:coverage": "npx vitest run --coverage", "test:ci": "npx vitest run --coverage --reporter=json --outputFile=./test-results.json", "electron": "cross-env NODE_ENV=development ELECTRON_HOT_RELOAD=true concurrently \"npm run dev\" \"electron .\"", "electron:dev": "cross-env NODE_ENV=development electron .", "electron:dev:hot": "cross-env NODE_ENV=development ELECTRON_HOT_RELOAD=true concurrently \"npm run dev\" \"electron .\"", "electron:dev:fast": "cross-env NODE_ENV=development ELECTRON_HOT_RELOAD=true FAST_START=true DISABLE_N8N=true concurrently \"npm run dev\" \"electron .\"", "electron:dev:ultra": "cross-env NODE_ENV=development ELECTRON_HOT_RELOAD=true FAST_START=true DISABLE_N8N=true DISABLE_WATCHDOG=true concurrently \"npm run dev\" \"electron .\"", "electron:prod": "cross-env NODE_ENV=production electron .", "build:user": "cross-env NODE_ENV=production npm run build && electron-builder --config.productName=\"WeMa IA Utilisateur\" --config.appId=\"com.wema-ia.user\"", "test:llama-swap": "node test-llama-swap.js", "test:model-manager": "node test-model-manager.js", "electron:build-all": "npm run build && electron-builder --mac --win --linux", "build:exe": "node scripts/build-simple-exe.cjs build", "build:exe:clean": "node scripts/build-simple-exe.cjs clean", "electron:build": "npm run build && electron-builder", "electron:preview": "npm run build && electron .", "electron:build-mac": "npm run build && electron-builder --mac --universal --publish always", "electron:build-mac-arm64": "npm run build && electron-builder --mac", "electron:build-mac-dev": "npm run build && electron-builder --mac --universal --publish always", "electron:build-win": "npm run build && electron-builder --win --config.npmRebuild=false", "electron:build-win-admin": "node scripts/build-win-admin.cjs", "cleanup:win": "node scripts/cleanup-win.cjs", "electron:clean-build-win": "npm run cleanup:win && npm run electron:build-win", "package": "npm run build && electron-builder build --publish always", "package:mac-unsigned": "npm run build && electron-builder build -c.mac.identity=null --publish always", "electron:build-mac-publish": "npm run build && electron-builder --mac --universal --publish always", "docker:check-env": "node -e \"if(!process.env.DOCKER_USERNAME) { console.error('Error: DOCKER_USERNAME environment variable is not set.\\nPlease set it using: export DOCKER_USERNAME=your-username'); process.exit(1); }\"", "docker:build": "if [ -z \"$DOCKER_USERNAME\" ]; then docker build -t clara-ollama:latest .; else docker build -t $DOCKER_USERNAME/clara-ollama:latest .; fi", "docker:run": "if [ -z \"$DOCKER_USERNAME\" ]; then docker run -p 8069:8069 clara-ollama:latest; else docker run -p 8069:8069 $DOCKER_USERNAME/clara-ollama:latest; fi", "docker:login": "docker login", "docker:push": "npm run docker:check-env && docker push $DOCKER_USERNAME/clara-ollama:latest", "docker:all": "npm run docker:check-env && npm run docker:login && npm run docker:build && npm run docker:push", "docker:publish": "chmod +x ./docker-publish.sh && ./docker-publish.sh", "docker:build-backend": "./scripts/build-backend-docker.sh", "build:standalone": "node scripts/build-standalone.js", "build:server": "REACT_APP_SERVER_MODE=true npm run build", "update:create": "node scripts/update-system.js create", "update:server": "node scripts/update-system.js server", "update:build-and-serve": "node scripts/update-system.js build-and-serve", "poles:create": "node scripts/multi-pole-manager.js create", "poles:start": "node scripts/multi-pole-manager.js start", "poles:update": "node scripts/multi-pole-manager.js update", "poles:dashboard": "node scripts/multi-pole-manager.js dashboard", "build:admin": "node scripts/build-admin-user-apps.js admin", "build:users": "node scripts/build-admin-user-apps.js users", "build:admin-users": "node scripts/build-admin-user-apps.js all", "simple:create": "node scripts/simple-multi-pole.js create", "simple:create-all": "node scripts/simple-multi-pole.js create-all", "simple:admin": "node scripts/simple-multi-pole.js admin", "simple:tokens": "node scripts/simple-multi-pole.js tokens", "perfect:all": "node scripts/perfect-deployment.js all", "perfect:admin": "node scripts/perfect-deployment.js admin", "perfect:server": "node scripts/perfect-deployment.js server", "perfect:clients": "node scripts/perfect-deployment.js clients", "exe:all": "node scripts/exe-deployment.js all", "exe:admin": "node scripts/exe-deployment.js admin", "exe:server": "node scripts/exe-deployment.js server", "exe:users": "node scripts/exe-deployment.js users", "build:exe:all": "node scripts/build-exe.js all", "build:exe:patrimoine": "node scripts/build-exe.js patrimoine", "build:exe:ma": "node scripts/build-exe.js ma", "build:exe:rh": "node scripts/build-exe.js rh", "build:exe:admin": "node scripts/build-exe.js admin", "build:simple-exe": "node build-simple-exe.js", "build:final-exe": "node create-final-exe.js", "build:fixed-exe": "node build-fixed-exe.js", "build:working-exe": "node build-working-exe.js", "build:perfect-exe": "node build-perfect-exe.cjs", "build:wema-final": "node build-wema-final.cjs", "build:exe-fixed": "node build-exe-fixed.cjs", "build:diagnostic": "node build-diagnostic.cjs", "build:final": "node build-final-working.cjs", "test-and-build": "node test-and-build.cjs", "build:production": "node build-production-only.cjs", "build:wema": "node build-wema-optimized.cjs", "build:wema:clean": "node build-wema-optimized.cjs clean", "electron:build-mac-test": "npm run build && electron-builder --mac --universal -c.mac.identity=null -c.mac.notarize=false --publish always", "electron:build-linux": "npm run build && electron-builder --linux", "electron:build-mac-permissions": "npm run build && electron-builder --mac --universal --config.mac.hardenedRuntime=true --config.mac.entitlements=build/entitlements.mac.plist --config.mac.entitlementsInherit=build/entitlements.mac.plist --publish always", "start": "react-scripts start", "eject": "react-scripts eject"}, "dependencies": {"@anthropic-ai/sdk": "^0.52.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@langchain/core": "^0.3.42", "@mapbox/node-pre-gyp": "^2.0.0", "@modelcontextprotocol/sdk": "^1.0.0", "@monaco-editor/react": "^4.7.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@ricky0123/vad-web": "^0.0.24", "@saintno/comfyui-sdk": "^0.2.45", "@supabase/supabase-js": "^2.49.4", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "@types/crypto-js": "^4.2.2", "@types/jest": "^27.5.2", "@types/node": "^20.0.0", "@types/prismjs": "^1.26.5", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/uuid": "^10.0.0", "@webcontainer/api": "^1.6.1", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "adm-zip": "^0.5.16", "chalk": "^5.3.0", "chokidar": "^3.5.0", "clara-flow-sdk": "^1.3.0", "commander": "^11.0.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dockerode": "^4.0.6", "dotenv": "^16.4.7", "express": "^4.18.0", "extract-zip": "^2.0.1", "fs-extra": "^11.0.0", "glob": "^10.0.0", "gsap": "^3.13.0", "idb": "^8.0.2", "immer": "^10.1.1", "inquirer": "^9.2.0", "langchain": "^0.3.19", "lucide-react": "^0.294.0", "mcp-remote": "^0.1.9", "mermaid": "^11.6.0", "ngrok": "^5.0.0-beta.2", "node-fetch": "^3.3.2", "ollama": "^0.5.14", "openai": "^4.0.0", "openai-zod-functions": "^0.1.2", "ora": "^7.0.0", "pdfjs-dist": "^3.11.174", "prismjs": "^1.30.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-grid-layout": "^1.5.1", "react-icons": "^5.5.0", "react-image-crop": "^11.0.7", "react-joyride": "^2.9.3", "react-markdown": "^9.1.0", "react-player": "^2.16.0", "react-resizable": "^3.0.5", "react-syntax-highlighter": "^15.6.1", "react-transition-group": "^4.4.5", "reactflow": "^11.10.1", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "socket.io": "^4.7.0", "tar": "^6.2.1", "tar-fs": "^3.0.8", "uuid": "^11.1.0", "ws": "^8.18.1", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0", "yaml": "^2.3.0", "zod": "^3.22.0", "zustand": "^5.0.5"}, "devDependencies": {"@electron/notarize": "^2.5.0", "@eslint/js": "^9.9.1", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/express": "^4.17.0", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.0", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-grid-layout": "^1.3.5", "@types/react-syntax-highlighter": "^15.5.13", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^1.6.1", "@vitest/ui": "^1.6.1", "autoprefixer": "^10.4.18", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "electron": "^37.2.1", "electron-builder": "^26.0.12", "electron-packager": "^17.1.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "fs-extra": "^11.3.0", "globals": "^15.9.0", "jsdom": "^23.2.0", "nodemon": "^3.0.0", "postcss": "^8.4.35", "rimraf": "^5.0.10", "tailwindcss": "^3.4.1", "ts-node": "^10.9.0", "typescript": "^5.0.0", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vitest": "^1.6.1", "wait-on": "^8.0.3"}, "build": {"appId": "com.wema-ia.app", "productName": "WeMa IA", "compression": "store", "electronVersion": "32.0.0", "files": ["dist/**/*", "public/electron.cjs", "public/preload.js", "public/wema-icon-256.ico", "public/wema-logo-light.png", "public/wema-logo-dark.png", "package.json", "!clara_interpreter", "!clara_interpreter/**/*", "!**/clara_interpreter", "!**/clara_interpreter/**/*", "!**/**/venv/**/*", "!**/venv/**/*", "!venv", "!venv/**/*", "!screenshots/**/*", "!documentation/**/*", "!mock/**/*", "!mascot/**/*", "!*.log", "!*.cache", "!dist-*/**/*", "!release/**/*", "!**/ngrok/**/*", "!**/ssh2/**/*", "!**/cpu-features/**/*", "!**/*.map", "!**/test/**/*", "!**/tests/**/*", "!**/__tests__/**/*"], "asarUnpack": ["!**/canvas/**/*"], "extraResources": [{"from": "py_backend", "to": "py_backend", "filter": ["**/*", "!**/__pycache__/**", "!**/*.pyc", "!**/*.log", "!**/venv/**/*", "!**/test_*", "!**/*_test.py"]}], "directories": {"buildResources": "assets", "output": "release"}, "publish": {"provider": "github", "owner": "badboysm890", "repo": "WeMa-IA", "releaseType": "release", "private": false}, "mac": {"category": "public.app-category.developer-tools", "icon": "assets/icons/mac/icon.icns", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist", "notarize": true, "target": [{"target": "default", "arch": ["arm64"]}], "darkModeSupport": true, "artifactName": "${productName}-${version}-arm64.${ext}", "type": "distribution", "files": ["!clara_interpreter/**/*", "!**/**/venv/**/*", "!**/venv/**/*", "!venv", "!venv/**/*"]}, "win": {"target": [{"target": "portable", "arch": ["x64"]}], "icon": "public/wema-icon-256.ico", "cscLink": "wema-ia-cert.pfx", "cscKeyPassword": "WeMaIA2025!", "signAndEditExecutable": true, "extraResources": [{"from": "py_backend", "to": "py_backend", "filter": ["**/*", "!**/__pycache__/**", "!**/*.pyc"]}], "extraFiles": [], "files": ["!clara_interpreter/**/*", "!**/**/venv/**/*", "!**/venv/**/*", "!venv", "!venv/**/*"]}, "linux": {"target": ["deb", "AppImage"], "category": "Development", "icon": "assets/icons/png", "desktop": {"entry": {"StartupNotify": "false", "Encoding": "UTF-8", "MimeType": "x-scheme-handler/wema-ia"}}, "artifactName": "${productName}-${version}.${ext}", "files": ["dist/**/*", "public/electron.cjs", "!clara_interpreter/**/*", "!**/**/venv/**/*", "!**/venv/**/*", "!venv", "!venv/**/*", "!src/**/*", "!node_modules/**/*", "!**/*.map", "!**/*.ts", "!**/*.tsx", "!dist/assets/monaco-editor*", "!dist/assets/mermaid*", "!dist/assets/katex*", "!dist/assets/cytoscape*", "!node_modules/canvas/**/*"]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "include": "installer.nsh", "installerIcon": "assets/icons/win/icon.ico", "uninstallerIcon": "assets/icons/win/icon.ico", "installerHeaderIcon": "assets/icons/win/icon.ico", "createStartMenuShortcut": true, "shortcutName": "WeMa IA", "license": "LICENSE", "deleteAppDataOnUninstall": true, "displayLanguageSelector": false, "artifactName": "${productName} Setup ${version}a.${ext}", "unicode": true, "differentialPackage": false, "allowElevation": false, "perMachine": false, "packElevateHelper": false, "warningsAsErrors": false, "guid": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "installerSidebar": "assets/icons/win/icon.ico", "uninstallerSidebar": "assets/icons/win/icon.ico"}}, "vite": {"build": {"chunkSizeWarningLimit": 6000, "rollupOptions": {"output": {"manualChunks": {"vendor": ["react", "react-dom", "langchain", "@langchain/core", "openai"]}}}}}, "bin": {"wema-studio": "./src/lib/agent-studio/cli/wema-studio.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}