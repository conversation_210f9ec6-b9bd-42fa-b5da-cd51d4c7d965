
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.IO.Compression.FileSystem

# Interface utilisateur
[System.Windows.Forms.Application]::EnableVisualStyles()

# Afficher message d'installation
$result = [System.Windows.Forms.MessageBox]::Show(
    "Installer WeMa IA - Assistant IA Professionnel ?\n\nL'application sera installée dans votre dossier utilisateur et lancée automatiquement.",
    "WeMa IA - Installation",
    [System.Windows.Forms.MessageBoxButtons]::YesNo,
    [System.Windows.Forms.MessageBoxIcon]::Question
)

if ($result -eq [System.Windows.Forms.DialogResult]::No) {
    exit 0
}

# Définir répertoires
$installDir = "$env:APPDATA\WeMa_IA"
$appExe = "$installDir\WeMa IA.exe"

# Vérifier si déjà installé
if (Test-Path $appExe) {
    $launch = [System.Windows.Forms.MessageBox]::Show(
        "WeMa IA est déjà installé !\n\nVoulez-vous le lancer ?",
        "WeMa IA",
        [System.Windows.Forms.MessageBoxButtons]::YesNo,
        [System.Windows.Forms.MessageBoxIcon]::Information
    )
    
    if ($launch -eq [System.Windows.Forms.DialogResult]::Yes) {
        Start-Process $appExe
    }
    exit 0
}

# Afficher progression
$progressForm = New-Object System.Windows.Forms.Form
$progressForm.Text = "Installation WeMa IA"
$progressForm.Size = New-Object System.Drawing.Size(400, 150)
$progressForm.StartPosition = "CenterScreen"
$progressForm.FormBorderStyle = "FixedDialog"
$progressForm.MaximizeBox = $false
$progressForm.MinimizeBox = $false

$progressLabel = New-Object System.Windows.Forms.Label
$progressLabel.Text = "Installation en cours..."
$progressLabel.Location = New-Object System.Drawing.Point(20, 20)
$progressLabel.Size = New-Object System.Drawing.Size(350, 30)
$progressForm.Controls.Add($progressLabel)

$progressBar = New-Object System.Windows.Forms.ProgressBar
$progressBar.Location = New-Object System.Drawing.Point(20, 60)
$progressBar.Size = New-Object System.Drawing.Size(350, 30)
$progressBar.Style = "Continuous"
$progressForm.Controls.Add($progressBar)

$progressForm.Show()
$progressForm.Refresh()

try {
    # Étape 1: Créer répertoire
    $progressLabel.Text = "Création du répertoire d'installation..."
    $progressBar.Value = 20
    $progressForm.Refresh()
    
    if (-not (Test-Path $installDir)) {
        New-Item -ItemType Directory -Path $installDir -Force | Out-Null
    }
    
    Start-Sleep -Milliseconds 500
    
    # Étape 2: Extraire l'application
    $progressLabel.Text = "Extraction de l'application..."
    $progressBar.Value = 40
    $progressForm.Refresh()
    
    $zipPath = "$PSScriptRoot\wema_app.zip"
    if (Test-Path $zipPath) {
        [System.IO.Compression.ZipFile]::ExtractToDirectory($zipPath, $installDir)
    } else {
        throw "Archive d'installation non trouvée"
    }
    
    $progressBar.Value = 70
    $progressForm.Refresh()
    Start-Sleep -Milliseconds 500
    
    # Étape 3: Créer raccourcis
    $progressLabel.Text = "Création des raccourcis..."
    $progressBar.Value = 85
    $progressForm.Refresh()
    
    # Raccourci bureau
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut("$env:USERPROFILE\Desktop\WeMa IA.lnk")
    $Shortcut.TargetPath = $appExe
    $Shortcut.WorkingDirectory = $installDir
    $Shortcut.IconLocation = $appExe
    $Shortcut.Description = "WeMa IA - Assistant IA Professionnel"
    $Shortcut.Save()
    
    # Raccourci menu démarrer
    $startMenuDir = "$env:APPDATA\Microsoft\Windows\Start Menu\Programs"
    $startShortcut = $WshShell.CreateShortcut("$startMenuDir\WeMa IA.lnk")
    $startShortcut.TargetPath = $appExe
    $startShortcut.WorkingDirectory = $installDir
    $startShortcut.IconLocation = $appExe
    $startShortcut.Description = "WeMa IA - Assistant IA Professionnel"
    $startShortcut.Save()
    
    $progressBar.Value = 100
    $progressForm.Refresh()
    Start-Sleep -Milliseconds 500
    
    $progressForm.Close()
    
    # Message de succès
    $success = [System.Windows.Forms.MessageBox]::Show(
        "WeMa IA installé avec succès !\n\n✅ Raccourci créé sur le bureau\n✅ Raccourci ajouté au menu démarrer\n\nLancer WeMa IA maintenant ?",
        "Installation terminée",
        [System.Windows.Forms.MessageBoxButtons]::YesNo,
        [System.Windows.Forms.MessageBoxIcon]::Information
    )
    
    if ($success -eq [System.Windows.Forms.DialogResult]::Yes) {
        Start-Process $appExe
    }
    
} catch {
    $progressForm.Close()
    [System.Windows.Forms.MessageBox]::Show(
        "Erreur lors de l'installation :\n\n$($_.Exception.Message)",
        "Erreur",
        [System.Windows.Forms.MessageBoxButtons]::OK,
        [System.Windows.Forms.MessageBoxIcon]::Error
    )
}
