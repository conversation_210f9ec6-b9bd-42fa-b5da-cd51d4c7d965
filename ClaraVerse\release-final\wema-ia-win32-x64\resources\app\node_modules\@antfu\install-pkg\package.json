{"name": "@antfu/install-pkg", "type": "module", "version": "1.1.0", "description": "Install package programmatically.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/install-pkg#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/install-pkg.git"}, "bugs": {"url": "https://github.com/antfu/install-pkg/issues"}, "sideEffects": false, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "dependencies": {"package-manager-detector": "^1.3.0", "tinyexec": "^1.0.1"}, "devDependencies": {"@antfu/eslint-config": "^4.12.1", "@antfu/ni": "^24.3.0", "@types/node": "^22.15.12", "bumpp": "^10.1.0", "eslint": "^9.26.0", "publint": "^0.3.12", "tsup": "^8.4.0", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "scripts": {"dev": "nr build --watch", "start": "tsx src/index.ts", "build": "tsup src/index.ts --format cjs,esm --dts --no-splitting", "release": "bumpp --commit --push --tag && pnpm publish", "lint": "eslint ."}}