{"version": 3, "sources": ["../src/diagrams/c4/c4Detector.ts", "../src/diagrams/flowchart/flowDetector.ts", "../src/diagrams/flowchart/flowDetector-v2.ts", "../src/diagrams/er/erDetector.ts", "../src/diagrams/git/gitGraphDetector.ts", "../src/diagrams/gantt/ganttDetector.ts", "../src/diagrams/info/infoDetector.ts", "../src/diagrams/pie/pieDetector.ts", "../src/diagrams/quadrant-chart/quadrantDetector.ts", "../src/diagrams/xychart/xychartDetector.ts", "../src/diagrams/requirement/requirementDetector.ts", "../src/diagrams/sequence/sequenceDetector.ts", "../src/diagrams/class/classDetector.ts", "../src/diagrams/class/classDetector-V2.ts", "../src/diagrams/state/stateDetector.ts", "../src/diagrams/state/stateDetector-V2.ts", "../src/diagrams/user-journey/journeyDetector.ts", "../src/diagrams/error/errorRenderer.ts", "../src/diagrams/error/errorDiagram.ts", "../src/diagrams/flowchart/elk/detector.ts", "../src/diagrams/timeline/detector.ts", "../src/diagrams/mindmap/detector.ts", "../src/diagrams/kanban/detector.ts", "../src/diagrams/sankey/sankeyDetector.ts", "../src/diagrams/packet/detector.ts", "../src/diagrams/radar/detector.ts", "../src/diagrams/block/blockDetector.ts", "../src/diagrams/architecture/architectureDetector.ts", "../src/diagram-api/diagram-orchestration.ts", "../src/diagram-api/loadDiagram.ts", "../../../node_modules/.pnpm/stylis@4.3.6/node_modules/stylis/src/Enum.js", "../../../node_modules/.pnpm/stylis@4.3.6/node_modules/stylis/src/Utility.js", "../../../node_modules/.pnpm/stylis@4.3.6/node_modules/stylis/src/Tokenizer.js", "../../../node_modules/.pnpm/stylis@4.3.6/node_modules/stylis/src/Parser.js", "../../../node_modules/.pnpm/stylis@4.3.6/node_modules/stylis/src/Serializer.js", "../src/accessibility.ts", "../src/Diagram.ts", "../src/interactionDb.ts", "../src/diagram-api/comments.ts", "../src/diagram-api/frontmatter.ts", "../src/preprocess.ts", "../src/utils/base64.ts", "../src/mermaidAPI.ts", "../src/mermaid.ts"], "sourcesContent": ["import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'c4';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*C4Context|C4Container|C4Component|C4Dynamic|C4Deployment/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./c4Diagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'flowchart';\n\nconst detector: DiagramDetector = (txt, config) => {\n  // If we have conferred to only use new flow charts this function should always return false\n  // as in not signalling true for a legacy flowchart\n  if (\n    config?.flowchart?.defaultRenderer === 'dagre-wrapper' ||\n    config?.flowchart?.defaultRenderer === 'elk'\n  ) {\n    return false;\n  }\n  return /^\\s*graph/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./flowDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'flowchart-v2';\n\nconst detector: DiagramDetector = (txt, config) => {\n  if (config?.flowchart?.defaultRenderer === 'dagre-d3') {\n    return false;\n  }\n\n  if (config?.flowchart?.defaultRenderer === 'elk') {\n    config.layout = 'elk';\n  }\n\n  // If we have configured to use dagre-wrapper then we should return true in this function for graph code thus making it use the new flowchart diagram\n  if (/^\\s*graph/.test(txt) && config?.flowchart?.defaultRenderer === 'dagre-wrapper') {\n    return true;\n  }\n  return /^\\s*flowchart/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./flowDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'er';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*erDiagram/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./erDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type { DiagramDetector, DiagramLoader } from '../../diagram-api/types.js';\nimport type { ExternalDiagramDefinition } from '../../diagram-api/types.js';\n\nconst id = 'gitGraph';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*gitGraph/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./gitGraphDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'gantt';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*gantt/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./ganttDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'info';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*info/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./infoDiagram.js');\n  return { id, diagram };\n};\n\nexport const info: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'pie';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*pie/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./pieDiagram.js');\n  return { id, diagram };\n};\n\nexport const pie: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'quadrantChart';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*quadrantChart/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./quadrantDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'xychart';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*xychart-beta/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./xychartDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'requirement';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*requirement(Diagram)?/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./requirementDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'sequence';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*sequenceDiagram/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./sequenceDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'class';\n\nconst detector: DiagramDetector = (txt, config) => {\n  // If we have configured to use dagre-wrapper then we should never return true in this function\n  if (config?.class?.defaultRenderer === 'dagre-wrapper') {\n    return false;\n  }\n  // We have not opted to use the new renderer so we should return true if we detect a class diagram\n  return /^\\s*classDiagram/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./classDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'classDiagram';\n\nconst detector: DiagramDetector = (txt, config) => {\n  // If we have configured to use dagre-wrapper then we should return true in this function for classDiagram code thus making it use the new class diagram\n  if (/^\\s*classDiagram/.test(txt) && config?.class?.defaultRenderer === 'dagre-wrapper') {\n    return true;\n  }\n  // We have not opted to use the new renderer so we should return true if we detect a class diagram\n  return /^\\s*classDiagram-v2/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./classDiagram-v2.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'state';\n\nconst detector: DiagramDetector = (txt, config) => {\n  // If we have confirmed to only use new state diagrams this function should always return false\n  // as in not signalling true for a legacy state diagram\n  if (config?.state?.defaultRenderer === 'dagre-wrapper') {\n    return false;\n  }\n  return /^\\s*stateDiagram/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./stateDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'stateDiagram';\n\nconst detector: DiagramDetector = (txt, config) => {\n  if (/^\\s*stateDiagram-v2/.test(txt)) {\n    return true;\n  }\n  if (/^\\s*stateDiagram/.test(txt) && config?.state?.defaultRenderer === 'dagre-wrapper') {\n    return true;\n  }\n  return false;\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./stateDiagram-v2.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'journey';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*journey/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./journeyDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type { SVG, SVGGroup } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport { selectSvgElement } from '../../rendering-util/selectSvgElement.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\n\n/**\n * Draws an info picture in the tag with id: id based on the graph definition in text.\n *\n * @param _text - Mermaid graph definition.\n * @param id - The text for the error\n * @param version - The version\n */\nexport const draw = (_text: string, id: string, version: string) => {\n  log.debug('rendering svg for syntax error\\n');\n  const svg: SVG = selectSvgElement(id);\n  const g: SVGGroup = svg.append('g');\n\n  svg.attr('viewBox', '0 0 2412 512');\n  configureSvgSize(svg, 100, 512, true);\n\n  g.append('path')\n    .attr('class', 'error-icon')\n    .attr(\n      'd',\n      'm411.313,123.313c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32-9.375,9.375-20.688-20.688c-12.484-12.5-32.766-12.5-45.25,0l-16,16c-1.261,1.261-2.304,2.648-3.31,4.051-21.739-8.561-45.324-13.426-70.065-13.426-105.867,0-192,86.133-192,192s86.133,192 192,192 192-86.133 192-192c0-24.741-4.864-48.327-13.426-70.065 1.402-1.007 2.79-2.049 4.051-3.31l16-16c12.5-12.492 12.5-32.758 0-45.25l-20.688-20.688 9.375-9.375 32.001-31.999zm-219.313,100.687c-52.938,0-96,43.063-96,96 0,8.836-7.164,16-16,16s-16-7.164-16-16c0-70.578 57.***********-128 8.836,0 16,7.164 16,16s-7.164,16-16,16z'\n    );\n\n  g.append('path')\n    .attr('class', 'error-icon')\n    .attr(\n      'd',\n      'm459.02,148.98c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l16,16c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16.001-16z'\n    );\n\n  g.append('path')\n    .attr('class', 'error-icon')\n    .attr(\n      'd',\n      'm340.395,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16-16c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l15.999,16z'\n    );\n\n  g.append('path')\n    .attr('class', 'error-icon')\n    .attr(\n      'd',\n      'm400,64c8.844,0 16-7.164 16-16v-32c0-8.836-7.156-16-16-16-8.844,0-16,7.164-16,16v32c0,8.836 7.156,16 16,16z'\n    );\n\n  g.append('path')\n    .attr('class', 'error-icon')\n    .attr(\n      'd',\n      'm496,96.586h-32c-8.844,0-16,7.164-16,16 0,8.836 7.156,16 16,16h32c8.844,0 16-7.164 16-16 0-8.836-7.156-16-16-16z'\n    );\n\n  g.append('path')\n    .attr('class', 'error-icon')\n    .attr(\n      'd',\n      'm436.98,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688l32-32c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32c-6.251,6.25-6.251,16.375-0.001,22.625z'\n    );\n\n  g.append('text') // text label for the x axis\n    .attr('class', 'error-text')\n    .attr('x', 1440)\n    .attr('y', 250)\n    .attr('font-size', '150px')\n    .style('text-anchor', 'middle')\n    .text('Syntax error in text');\n  g.append('text') // text label for the x axis\n    .attr('class', 'error-text')\n    .attr('x', 1250)\n    .attr('y', 400)\n    .attr('font-size', '100px')\n    .style('text-anchor', 'middle')\n    .text(`mermaid version ${version}`);\n};\n\nexport const renderer = { draw };\n\nexport default renderer;\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\nimport { renderer } from './errorRenderer.js';\n\nconst diagram: DiagramDefinition = {\n  db: {},\n  renderer,\n  parser: {\n    parse: (): void => {\n      return;\n    },\n  },\n};\n\nexport default diagram;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../../diagram-api/types.js';\n\nconst id = 'flowchart-elk';\n\nconst detector: DiagramDetector = (txt, config = {}): boolean => {\n  if (\n    // If diagram explicitly states flowchart-elk\n    /^\\s*flowchart-elk/.test(txt) ||\n    // If a flowchart/graph diagram has their default renderer set to elk\n    (/^\\s*flowchart|graph/.test(txt) && config?.flowchart?.defaultRenderer === 'elk')\n  ) {\n    config.layout = 'elk';\n    return true;\n  }\n  return false;\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('../flowDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'timeline';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*timeline/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./timeline-definition.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\nconst id = 'mindmap';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*mindmap/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./mindmap-definition.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\nconst id = 'kanban';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*kanban/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./kanban-definition.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type { DiagramDetector, ExternalDiagramDefinition } from '../../diagram-api/types.js';\n\nconst id = 'sankey';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*sankey-beta/.test(txt);\n};\n\nconst loader = async () => {\n  const { diagram } = await import('./sankeyDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'packet';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*packet-beta/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./diagram.js');\n  return { id, diagram };\n};\n\nexport const packet: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'radar';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*radar-beta/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./diagram.js');\n  return { id, diagram };\n};\n\nexport const radar: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n", "import type { DiagramDetector, ExternalDiagramDefinition } from '../../diagram-api/types.js';\n\nconst id = 'block';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*block-beta/.test(txt);\n};\n\nconst loader = async () => {\n  const { diagram } = await import('./blockDiagram.js');\n  return { id, diagram };\n};\n\nconst plugin: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default plugin;\n", "import type {\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from '../../diagram-api/types.js';\n\nconst id = 'architecture';\n\nconst detector: DiagramDetector = (txt) => {\n  return /^\\s*architecture/.test(txt);\n};\n\nconst loader: DiagramLoader = async () => {\n  const { diagram } = await import('./architectureDiagram.js');\n  return { id, diagram };\n};\n\nconst architecture: ExternalDiagramDefinition = {\n  id,\n  detector,\n  loader,\n};\n\nexport default architecture;\n", "import c4 from '../diagrams/c4/c4Detector.js';\nimport flowchart from '../diagrams/flowchart/flowDetector.js';\nimport flowchartV2 from '../diagrams/flowchart/flowDetector-v2.js';\nimport er from '../diagrams/er/erDetector.js';\nimport git from '../diagrams/git/gitGraphDetector.js';\nimport gantt from '../diagrams/gantt/ganttDetector.js';\nimport { info } from '../diagrams/info/infoDetector.js';\nimport { pie } from '../diagrams/pie/pieDetector.js';\nimport quadrantChart from '../diagrams/quadrant-chart/quadrantDetector.js';\nimport xychart from '../diagrams/xychart/xychartDetector.js';\nimport requirement from '../diagrams/requirement/requirementDetector.js';\nimport sequence from '../diagrams/sequence/sequenceDetector.js';\nimport classDiagram from '../diagrams/class/classDetector.js';\nimport classDiagramV2 from '../diagrams/class/classDetector-V2.js';\nimport state from '../diagrams/state/stateDetector.js';\nimport stateV2 from '../diagrams/state/stateDetector-V2.js';\nimport journey from '../diagrams/user-journey/journeyDetector.js';\nimport errorDiagram from '../diagrams/error/errorDiagram.js';\nimport flowchartElk from '../diagrams/flowchart/elk/detector.js';\nimport timeline from '../diagrams/timeline/detector.js';\nimport mindmap from '../diagrams/mindmap/detector.js';\nimport kanban from '../diagrams/kanban/detector.js';\nimport sankey from '../diagrams/sankey/sankeyDetector.js';\nimport { packet } from '../diagrams/packet/detector.js';\nimport { radar } from '../diagrams/radar/detector.js';\nimport block from '../diagrams/block/blockDetector.js';\nimport architecture from '../diagrams/architecture/architectureDetector.js';\nimport { registerLazyLoadedDiagrams } from './detectType.js';\nimport { registerDiagram } from './diagramAPI.js';\n\nlet hasLoadedDiagrams = false;\nexport const addDiagrams = () => {\n  if (hasLoadedDiagrams) {\n    return;\n  }\n  // This is added here to avoid race-conditions.\n  // We could optimize the loading logic somehow.\n  hasLoadedDiagrams = true;\n  registerDiagram('error', errorDiagram, (text) => {\n    return text.toLowerCase().trim() === 'error';\n  });\n  registerDiagram(\n    '---',\n    // --- diagram type may appear if YAML front-matter is not parsed correctly\n    {\n      db: {\n        clear: () => {\n          // Quite ok, clear needs to be there for --- to work as a regular diagram\n        },\n      },\n      styles: {}, // should never be used\n      renderer: {\n        draw: () => {\n          // should never be used\n        },\n      },\n      parser: {\n        parse: () => {\n          throw new Error(\n            'Diagrams beginning with --- are not valid. ' +\n              'If you were trying to use a YAML front-matter, please ensure that ' +\n              \"you've correctly opened and closed the YAML front-matter with un-indented `---` blocks\"\n          );\n        },\n      },\n      init: () => null, // no op\n    },\n    (text) => {\n      return text.toLowerCase().trimStart().startsWith('---');\n    }\n  );\n  // Ordering of detectors is important. The first one to return true will be used.\n  registerLazyLoadedDiagrams(\n    c4,\n    kanban,\n    classDiagramV2,\n    classDiagram,\n    er,\n    gantt,\n    info,\n    pie,\n    requirement,\n    sequence,\n    flowchartElk,\n    flowchartV2,\n    flowchart,\n    mindmap,\n    timeline,\n    git,\n    stateV2,\n    state,\n    journey,\n    quadrantChart,\n    sankey,\n    packet,\n    xychart,\n    block,\n    architecture,\n    radar\n  );\n};\n", "import { log } from '../logger.js';\nimport { detectors } from './detectType.js';\nimport { getDiagram, registerDiagram } from './diagramAPI.js';\n\nexport const loadRegisteredDiagrams = async () => {\n  log.debug(`Loading registered diagrams`);\n  // Load all lazy loaded diagrams in parallel\n  const results = await Promise.allSettled(\n    Object.entries(detectors).map(async ([key, { detector, loader }]) => {\n      if (loader) {\n        try {\n          getDiagram(key);\n        } catch {\n          try {\n            // Register diagram if it is not already registered\n            const { diagram, id } = await loader();\n            registerDiagram(id, diagram, detector);\n          } catch (err) {\n            // Remove failed diagram from detectors\n            log.error(`Failed to load external diagram with key ${key}. Removing from detectors.`);\n            delete detectors[key];\n            throw err;\n          }\n        }\n      }\n    })\n  );\n  const failed = results.filter((result) => result.status === 'rejected');\n  if (failed.length > 0) {\n    log.error(`Failed to load ${failed.length} external diagrams`);\n    for (const res of failed) {\n      log.error(res);\n    }\n    throw new Error(`Failed to load ${failed.length} external diagrams`);\n  }\n};\n", "export var MS = '-ms-'\nexport var MOZ = '-moz-'\nexport var WEBKIT = '-webkit-'\n\nexport var COMMENT = 'comm'\nexport var RULESET = 'rule'\nexport var DECLARATION = 'decl'\n\nexport var PAGE = '@page'\nexport var MEDIA = '@media'\nexport var IMPORT = '@import'\nexport var CHARSET = '@charset'\nexport var VIEWPORT = '@viewport'\nexport var SUPPORTS = '@supports'\nexport var DOCUMENT = '@document'\nexport var NAMESPACE = '@namespace'\nexport var KEYFRAMES = '@keyframes'\nexport var FONT_FACE = '@font-face'\nexport var COUNTER_STYLE = '@counter-style'\nexport var FONT_FEATURE_VALUES = '@font-feature-values'\nexport var LAYER = '@layer'\nexport var SCOPE = '@scope'\n", "/**\n * @param {number}\n * @return {number}\n */\nexport var abs = Math.abs\n\n/**\n * @param {number}\n * @return {string}\n */\nexport var from = String.fromCharCode\n\n/**\n * @param {object}\n * @return {object}\n */\nexport var assign = Object.assign\n\n/**\n * @param {string} value\n * @param {number} length\n * @return {number}\n */\nexport function hash (value, length) {\n\treturn charat(value, 0) ^ 45 ? (((((((length << 2) ^ charat(value, 0)) << 2) ^ charat(value, 1)) << 2) ^ charat(value, 2)) << 2) ^ charat(value, 3) : 0\n}\n\n/**\n * @param {string} value\n * @return {string}\n */\nexport function trim (value) {\n\treturn value.trim()\n}\n\n/**\n * @param {string} value\n * @param {RegExp} pattern\n * @return {string?}\n */\nexport function match (value, pattern) {\n\treturn (value = pattern.exec(value)) ? value[0] : value\n}\n\n/**\n * @param {string} value\n * @param {(string|RegExp)} pattern\n * @param {string} replacement\n * @return {string}\n */\nexport function replace (value, pattern, replacement) {\n\treturn value.replace(pattern, replacement)\n}\n\n/**\n * @param {string} value\n * @param {string} search\n * @param {number} position\n * @return {number}\n */\nexport function indexof (value, search, position) {\n\treturn value.indexOf(search, position)\n}\n\n/**\n * @param {string} value\n * @param {number} index\n * @return {number}\n */\nexport function charat (value, index) {\n\treturn value.charCodeAt(index) | 0\n}\n\n/**\n * @param {string} value\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function substr (value, begin, end) {\n\treturn value.slice(begin, end)\n}\n\n/**\n * @param {string} value\n * @return {number}\n */\nexport function strlen (value) {\n\treturn value.length\n}\n\n/**\n * @param {any[]} value\n * @return {number}\n */\nexport function sizeof (value) {\n\treturn value.length\n}\n\n/**\n * @param {any} value\n * @param {any[]} array\n * @return {any}\n */\nexport function append (value, array) {\n\treturn array.push(value), value\n}\n\n/**\n * @param {string[]} array\n * @param {function} callback\n * @return {string}\n */\nexport function combine (array, callback) {\n\treturn array.map(callback).join('')\n}\n\n/**\n * @param {string[]} array\n * @param {RegExp} pattern\n * @return {string[]}\n */\nexport function filter (array, pattern) {\n\treturn array.filter(function (value) { return !match(value, pattern) })\n}\n", "import {from, trim, charat, strlen, substr, append, assign} from './Utility.js'\n\nexport var line = 1\nexport var column = 1\nexport var length = 0\nexport var position = 0\nexport var character = 0\nexport var characters = ''\n\n/**\n * @param {string} value\n * @param {object | null} root\n * @param {object | null} parent\n * @param {string} type\n * @param {string[] | string} props\n * @param {object[] | string} children\n * @param {object[]} siblings\n * @param {number} length\n */\nexport function node (value, root, parent, type, props, children, length, siblings) {\n\treturn {value: value, root: root, parent: parent, type: type, props: props, children: children, line: line, column: column, length: length, return: '', siblings: siblings}\n}\n\n/**\n * @param {object} root\n * @param {object} props\n * @return {object}\n */\nexport function copy (root, props) {\n\treturn assign(node('', null, null, '', null, null, 0, root.siblings), root, {length: -root.length}, props)\n}\n\n/**\n * @param {object} root\n */\nexport function lift (root) {\n\twhile (root.root)\n\t\troot = copy(root.root, {children: [root]})\n\n\tappend(root, root.siblings)\n}\n\n/**\n * @return {number}\n */\nexport function char () {\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function prev () {\n\tcharacter = position > 0 ? charat(characters, --position) : 0\n\n\tif (column--, character === 10)\n\t\tcolumn = 1, line--\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function next () {\n\tcharacter = position < length ? charat(characters, position++) : 0\n\n\tif (column++, character === 10)\n\t\tcolumn = 1, line++\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function peek () {\n\treturn charat(characters, position)\n}\n\n/**\n * @return {number}\n */\nexport function caret () {\n\treturn position\n}\n\n/**\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function slice (begin, end) {\n\treturn substr(characters, begin, end)\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function token (type) {\n\tswitch (type) {\n\t\t// \\0 \\t \\n \\r \\s whitespace token\n\t\tcase 0: case 9: case 10: case 13: case 32:\n\t\t\treturn 5\n\t\t// ! + , / > @ ~ isolate token\n\t\tcase 33: case 43: case 44: case 47: case 62: case 64: case 126:\n\t\t// ; { } breakpoint token\n\t\tcase 59: case 123: case 125:\n\t\t\treturn 4\n\t\t// : accompanied token\n\t\tcase 58:\n\t\t\treturn 3\n\t\t// \" ' ( [ opening delimit token\n\t\tcase 34: case 39: case 40: case 91:\n\t\t\treturn 2\n\t\t// ) ] closing delimit token\n\t\tcase 41: case 93:\n\t\t\treturn 1\n\t}\n\n\treturn 0\n}\n\n/**\n * @param {string} value\n * @return {any[]}\n */\nexport function alloc (value) {\n\treturn line = column = 1, length = strlen(characters = value), position = 0, []\n}\n\n/**\n * @param {any} value\n * @return {any}\n */\nexport function dealloc (value) {\n\treturn characters = '', value\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function delimit (type) {\n\treturn trim(slice(position - 1, delimiter(type === 91 ? type + 2 : type === 40 ? type + 1 : type)))\n}\n\n/**\n * @param {string} value\n * @return {string[]}\n */\nexport function tokenize (value) {\n\treturn dealloc(tokenizer(alloc(value)))\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function whitespace (type) {\n\twhile (character = peek())\n\t\tif (character < 33)\n\t\t\tnext()\n\t\telse\n\t\t\tbreak\n\n\treturn token(type) > 2 || token(character) > 3 ? '' : ' '\n}\n\n/**\n * @param {string[]} children\n * @return {string[]}\n */\nexport function tokenizer (children) {\n\twhile (next())\n\t\tswitch (token(character)) {\n\t\t\tcase 0: append(identifier(position - 1), children)\n\t\t\t\tbreak\n\t\t\tcase 2: append(delimit(character), children)\n\t\t\t\tbreak\n\t\t\tdefault: append(from(character), children)\n\t\t}\n\n\treturn children\n}\n\n/**\n * @param {number} index\n * @param {number} count\n * @return {string}\n */\nexport function escaping (index, count) {\n\twhile (--count && next())\n\t\t// not 0-9 A-F a-f\n\t\tif (character < 48 || character > 102 || (character > 57 && character < 65) || (character > 70 && character < 97))\n\t\t\tbreak\n\n\treturn slice(index, caret() + (count < 6 && peek() == 32 && next() == 32))\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function delimiter (type) {\n\twhile (next())\n\t\tswitch (character) {\n\t\t\t// ] ) \" '\n\t\t\tcase type:\n\t\t\t\treturn position\n\t\t\t// \" '\n\t\t\tcase 34: case 39:\n\t\t\t\tif (type !== 34 && type !== 39)\n\t\t\t\t\tdelimiter(character)\n\t\t\t\tbreak\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (type === 41)\n\t\t\t\t\tdelimiter(type)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tnext()\n\t\t\t\tbreak\n\t\t}\n\n\treturn position\n}\n\n/**\n * @param {number} type\n * @param {number} index\n * @return {number}\n */\nexport function commenter (type, index) {\n\twhile (next())\n\t\t// //\n\t\tif (type + character === 47 + 10)\n\t\t\tbreak\n\t\t// /*\n\t\telse if (type + character === 42 + 42 && peek() === 47)\n\t\t\tbreak\n\n\treturn '/*' + slice(index, position - 1) + '*' + from(type === 47 ? type : next())\n}\n\n/**\n * @param {number} index\n * @return {string}\n */\nexport function identifier (index) {\n\twhile (!token(peek()))\n\t\tnext()\n\n\treturn slice(index, position)\n}\n", "import {COMMENT, RULESET, DECLARATION} from './Enum.js'\nimport {abs, charat, trim, from, sizeof, strlen, substr, append, replace, indexof} from './Utility.js'\nimport {node, char, prev, next, peek, token, caret, alloc, dealloc, delimit, whitespace, escaping, identifier, commenter} from './Tokenizer.js'\n\n/**\n * @param {string} value\n * @return {object[]}\n */\nexport function compile (value) {\n\treturn dealloc(parse('', null, null, null, [''], value = alloc(value), 0, [0], value))\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {string[]} rule\n * @param {string[]} rules\n * @param {string[]} rulesets\n * @param {number[]} pseudo\n * @param {number[]} points\n * @param {string[]} declarations\n * @return {object}\n */\nexport function parse (value, root, parent, rule, rules, rulesets, pseudo, points, declarations) {\n\tvar index = 0\n\tvar offset = 0\n\tvar length = pseudo\n\tvar atrule = 0\n\tvar property = 0\n\tvar previous = 0\n\tvar variable = 1\n\tvar scanning = 1\n\tvar ampersand = 1\n\tvar character = 0\n\tvar type = ''\n\tvar props = rules\n\tvar children = rulesets\n\tvar reference = rule\n\tvar characters = type\n\n\twhile (scanning)\n\t\tswitch (previous = character, character = next()) {\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (previous != 108 && charat(characters, length - 1) == 58) {\n\t\t\t\t\tif (indexof(characters += replace(delimit(character), '&', '&\\f'), '&\\f', abs(index ? points[index - 1] : 0)) != -1)\n\t\t\t\t\t\tampersand = -1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t// \" ' [\n\t\t\tcase 34: case 39: case 91:\n\t\t\t\tcharacters += delimit(character)\n\t\t\t\tbreak\n\t\t\t// \\t \\n \\r \\s\n\t\t\tcase 9: case 10: case 13: case 32:\n\t\t\t\tcharacters += whitespace(previous)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tcharacters += escaping(caret() - 1, 7)\n\t\t\t\tcontinue\n\t\t\t// /\n\t\t\tcase 47:\n\t\t\t\tswitch (peek()) {\n\t\t\t\t\tcase 42: case 47:\n\t\t\t\t\t\tappend(comment(commenter(next(), caret()), root, parent, declarations), declarations)\n\t\t\t\t\t\tif ((token(previous || 1) == 5 || token(peek() || 1) == 5) && strlen(characters) && substr(characters, -1, void 0) !== ' ') characters += ' '\n\t\t\t\t\t\tbreak\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tcharacters += '/'\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\t// {\n\t\t\tcase 123 * variable:\n\t\t\t\tpoints[index++] = strlen(characters) * ampersand\n\t\t\t// } ; \\0\n\t\t\tcase 125 * variable: case 59: case 0:\n\t\t\t\tswitch (character) {\n\t\t\t\t\t// \\0 }\n\t\t\t\t\tcase 0: case 125: scanning = 0\n\t\t\t\t\t// ;\n\t\t\t\t\tcase 59 + offset: if (ampersand == -1) characters = replace(characters, /\\f/g, '')\n\t\t\t\t\t\tif (property > 0 && (strlen(characters) - length || (variable === 0 && previous === 47)))\n\t\t\t\t\t\t\tappend(property > 32 ? declaration(characters + ';', rule, parent, length - 1, declarations) : declaration(replace(characters, ' ', '') + ';', rule, parent, length - 2, declarations), declarations)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @ ;\n\t\t\t\t\tcase 59: characters += ';'\n\t\t\t\t\t// { rule/at-rule\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tappend(reference = ruleset(characters, root, parent, index, offset, rules, points, type, props = [], children = [], length, rulesets), rulesets)\n\n\t\t\t\t\t\tif (character === 123)\n\t\t\t\t\t\t\tif (offset === 0)\n\t\t\t\t\t\t\t\tparse(characters, root, reference, reference, props, rulesets, length, points, children)\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tswitch (atrule) {\n\t\t\t\t\t\t\t\t\t// c(ontainer)\n\t\t\t\t\t\t\t\t\tcase 99:\n\t\t\t\t\t\t\t\t\t\tif (charat(characters, 3) === 110) break\n\t\t\t\t\t\t\t\t\t// l(ayer)\n\t\t\t\t\t\t\t\t\tcase 108:\n\t\t\t\t\t\t\t\t\t\tif (charat(characters, 2) === 97) break\n\t\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\t\toffset = 0\n\t\t\t\t\t\t\t\t\t// d(ocument) m(edia) s(upports)\n\t\t\t\t\t\t\t\t\tcase 100: case 109: case 115:\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (offset) parse(value, reference, reference, rule && append(ruleset(value, reference, reference, 0, 0, rules, points, type, rules, props = [], length, children), children), rules, children, length, points, rule ? props : children)\n\t\t\t\t\t\t\t\telse parse(characters, reference, reference, reference, [''], children, 0, points, children)\n\t\t\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tindex = offset = property = 0, variable = ampersand = 1, type = characters = '', length = pseudo\n\t\t\t\tbreak\n\t\t\t// :\n\t\t\tcase 58:\n\t\t\t\tlength = 1 + strlen(characters), property = previous\n\t\t\tdefault:\n\t\t\t\tif (variable < 1)\n\t\t\t\t\tif (character == 123)\n\t\t\t\t\t\t--variable\n\t\t\t\t\telse if (character == 125 && variable++ == 0 && prev() == 125)\n\t\t\t\t\t\tcontinue\n\n\t\t\t\tswitch (characters += from(character), character * variable) {\n\t\t\t\t\t// &\n\t\t\t\t\tcase 38:\n\t\t\t\t\t\tampersand = offset > 0 ? 1 : (characters += '\\f', -1)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// ,\n\t\t\t\t\tcase 44:\n\t\t\t\t\t\tpoints[index++] = (strlen(characters) - 1) * ampersand, ampersand = 1\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @\n\t\t\t\t\tcase 64:\n\t\t\t\t\t\t// -\n\t\t\t\t\t\tif (peek() === 45)\n\t\t\t\t\t\t\tcharacters += delimit(next())\n\n\t\t\t\t\t\tatrule = peek(), offset = length = strlen(type = characters += identifier(caret())), character++\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// -\n\t\t\t\t\tcase 45:\n\t\t\t\t\t\tif (previous === 45 && strlen(characters) == 2)\n\t\t\t\t\t\t\tvariable = 0\n\t\t\t\t}\n\t\t}\n\n\treturn rulesets\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} index\n * @param {number} offset\n * @param {string[]} rules\n * @param {number[]} points\n * @param {string} type\n * @param {string[]} props\n * @param {string[]} children\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function ruleset (value, root, parent, index, offset, rules, points, type, props, children, length, siblings) {\n\tvar post = offset - 1\n\tvar rule = offset === 0 ? rules : ['']\n\tvar size = sizeof(rule)\n\n\tfor (var i = 0, j = 0, k = 0; i < index; ++i)\n\t\tfor (var x = 0, y = substr(value, post + 1, post = abs(j = points[i])), z = value; x < size; ++x)\n\t\t\tif (z = trim(j > 0 ? rule[x] + ' ' + y : replace(y, /&\\f/g, rule[x])))\n\t\t\t\tprops[k++] = z\n\n\treturn node(value, root, parent, offset === 0 ? RULESET : type, props, children, length, siblings)\n}\n\n/**\n * @param {number} value\n * @param {object} root\n * @param {object?} parent\n * @param {object[]} siblings\n * @return {object}\n */\nexport function comment (value, root, parent, siblings) {\n\treturn node(value, root, parent, COMMENT, from(char()), substr(value, 2, -2), 0, siblings)\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function declaration (value, root, parent, length, siblings) {\n\treturn node(value, root, parent, DECLARATION, substr(value, 0, length), substr(value, length + 1, -1), length, siblings)\n}\n", "import {IMPOR<PERSON>, LAYER, COMMENT, RU<PERSON>SE<PERSON>, DECL<PERSON>AT<PERSON>, KEYFRAMES, NAMESPACE} from './Enum.js'\nimport {strlen} from './Utility.js'\n\n/**\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function serialize (children, callback) {\n\tvar output = ''\n\n\tfor (var i = 0; i < children.length; i++)\n\t\toutput += callback(children[i], i, children, callback) || ''\n\n\treturn output\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function stringify (element, index, children, callback) {\n\tswitch (element.type) {\n\t\tcase LAYER: if (element.children.length) break\n\t\tcase IMPORT: case NAMESPACE: case DECLARATION: return element.return = element.return || element.value\n\t\tcase COMMENT: return ''\n\t\tcase KEYFRAMES: return element.return = element.value + '{' + serialize(element.children, callback) + '}'\n\t\tcase RULESET: if (!strlen(element.value = element.props.join(','))) return ''\n\t}\n\n\treturn strlen(children = serialize(element.children, callback)) ? element.return = element.value + '{' + children + '}' : ''\n}\n", "/**\n * Accessibility (a11y) functions, types, helpers.\n *\n * @see https://www.w3.org/WAI/\n * @see https://www.w3.org/TR/wai-aria-1.1/\n * @see https://www.w3.org/TR/svg-aam-1.0/\n */\nimport type { D3Element } from './types.js';\n\n/**\n * SVG element role:\n * The SVG element role _should_ be set to 'graphics-document' per SVG standard\n * but in practice is not always done by browsers, etc. (As of 2022-12-08).\n * A fallback role of 'document' should be set for those browsers, etc., that only support ARIA 1.0.\n *\n * @see https://www.w3.org/TR/svg-aam-1.0/#roleMappingGeneralRules\n * @see https://www.w3.org/TR/graphics-aria-1.0/#graphics-document\n */\nconst SVG_ROLE = 'graphics-document document';\n\n/**\n * Add role and aria-roledescription to the svg element.\n *\n * @param svg - d3 object that contains the SVG HTML element\n * @param diagramType - diagram name for to the aria-roledescription\n */\nexport function setA11yDiagramInfo(svg: D3Element, diagramType: string) {\n  svg.attr('role', SVG_ROLE);\n  if (diagramType !== '') {\n    svg.attr('aria-roledescription', diagramType);\n  }\n}\n\n/**\n * Add an accessible title and/or description element to a chart.\n * The title is usually not displayed and the description is never displayed.\n *\n * The following charts display their title as a visual and accessibility element: gantt.\n *\n * @param svg - d3 node to insert the a11y title and desc info\n * @param a11yTitle - a11y title. undefined or empty strings mean to skip them\n * @param a11yDesc - a11y description. undefined or empty strings mean to skip them\n * @param baseId - id used to construct the a11y title and description id\n */\nexport function addSVGa11yTitleDescription(\n  svg: D3Element,\n  a11yTitle: string | undefined,\n  a11yDesc: string | undefined,\n  baseId: string\n): void {\n  if (svg.insert === undefined) {\n    return;\n  }\n\n  if (a11yDesc) {\n    const descId = `chart-desc-${baseId}`;\n    svg.attr('aria-describedby', descId);\n    svg.insert('desc', ':first-child').attr('id', descId).text(a11yDesc);\n  }\n  if (a11yTitle) {\n    const titleId = `chart-title-${baseId}`;\n    svg.attr('aria-labelledby', titleId);\n    svg.insert('title', ':first-child').attr('id', titleId).text(a11yTitle);\n  }\n}\n", "import * as configApi from './config.js';\nimport { getDiagram, registerDiagram } from './diagram-api/diagramAPI.js';\nimport { detectType, getDiagramLoader } from './diagram-api/detectType.js';\nimport { UnknownDiagramError } from './errors.js';\nimport { encodeEntities } from './utils.js';\nimport type { DetailedError } from './utils.js';\nimport type { DiagramDefinition, DiagramMetadata } from './diagram-api/types.js';\n\n// eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents\nexport type ParseErrorFunction = (err: string | DetailedError | unknown, hash?: any) => void;\n\n/**\n * An object representing a parsed mermaid diagram definition.\n * @privateRemarks This is exported as part of the public mermaidAPI.\n */\nexport class Diagram {\n  public static async fromText(text: string, metadata: Pick<DiagramMetadata, 'title'> = {}) {\n    const config = configApi.getConfig();\n    const type = detectType(text, config);\n    text = encodeEntities(text) + '\\n';\n    try {\n      getDiagram(type);\n    } catch {\n      const loader = getDiagramLoader(type);\n      if (!loader) {\n        throw new UnknownDiagramError(`Diagram ${type} not found.`);\n      }\n      // Diagram not available, loading it.\n      // new diagram will try getDiagram again and if fails then it is a valid throw\n      const { id, diagram } = await loader();\n      registerDiagram(id, diagram);\n    }\n    const { db, parser, renderer, init } = getDiagram(type);\n    if (parser.parser) {\n      // The parser.parser.yy is only present in JISON parsers. So, we'll only set if required.\n      parser.parser.yy = db;\n    }\n    db.clear?.();\n    init?.(config);\n    // This block was added for legacy compatibility. Use frontmatter instead of adding more special cases.\n    if (metadata.title) {\n      db.setDiagramTitle?.(metadata.title);\n    }\n    await parser.parse(text);\n    return new Diagram(type, text, db, parser, renderer);\n  }\n\n  private constructor(\n    public type: string,\n    public text: string,\n    public db: DiagramDefinition['db'],\n    public parser: DiagramDefinition['parser'],\n    public renderer: DiagramDefinition['renderer']\n  ) {}\n\n  async render(id: string, version: string) {\n    await this.renderer.draw(this.text, id, version, this);\n  }\n\n  getParser() {\n    return this.parser;\n  }\n\n  getType() {\n    return this.type;\n  }\n}\n", "let interactionFunctions: (() => void)[] = [];\nexport const addFunction = (func: () => void) => {\n  interactionFunctions.push(func);\n};\nexport const attachFunctions = () => {\n  interactionFunctions.forEach((f) => {\n    f();\n  });\n  interactionFunctions = [];\n};\n", "/**\n * Remove all lines starting with `%%` from the text that don't contain a `%%{`\n * @param text - The text to remove comments from\n * @returns cleaned text\n */\nexport const cleanupComments = (text: string): string => {\n  return text.replace(/^\\s*%%(?!{)[^\\n]+\\n?/gm, '').trimStart();\n};\n", "import type { GanttDiagramConfig, MermaidConfig } from '../config.type.js';\nimport { frontMatterRegex } from './regexes.js';\n// The \"* as yaml\" part is necessary for tree-shaking\nimport * as yaml from 'js-yaml';\n\ninterface FrontMatterMetadata {\n  title?: string;\n  // Allows custom display modes. Currently used for compact mode in gantt charts.\n  displayMode?: GanttDiagramConfig['displayMode'];\n  config?: MermaidConfig;\n}\n\nexport interface FrontMatterResult {\n  text: string;\n  metadata: FrontMatterMetadata;\n}\n\n/**\n * Extract and parse frontmatter from text, if present, and sets appropriate\n * properties in the provided db.\n * @param text - The text that may have a YAML frontmatter.\n * @returns text with frontmatter stripped out\n */\nexport function extractFrontMatter(text: string): FrontMatterResult {\n  const matches = text.match(frontMatterRegex);\n  if (!matches) {\n    return {\n      text,\n      metadata: {},\n    };\n  }\n\n  let parsed: FrontMatterMetadata =\n    yaml.load(matches[1], {\n      // To support config, we need JSON schema.\n      // https://www.yaml.org/spec/1.2/spec.html#id2803231\n      schema: yaml.JSON_SCHEMA,\n    }) ?? {};\n\n  // To handle runtime data type changes\n  parsed = typeof parsed === 'object' && !Array.isArray(parsed) ? parsed : {};\n\n  const metadata: FrontMatterMetadata = {};\n\n  // Only add properties that are explicitly supported, if they exist\n  if (parsed.displayMode) {\n    metadata.displayMode = parsed.displayMode.toString() as GanttDiagramConfig['displayMode'];\n  }\n  if (parsed.title) {\n    metadata.title = parsed.title.toString();\n  }\n  if (parsed.config) {\n    metadata.config = parsed.config;\n  }\n\n  return {\n    text: text.slice(matches[0].length),\n    metadata,\n  };\n}\n", "import { cleanupComments } from './diagram-api/comments.js';\nimport { extractFrontMatter } from './diagram-api/frontmatter.js';\nimport type { DiagramMetadata } from './diagram-api/types.js';\nimport utils, { cleanAndMerge, removeDirectives } from './utils.js';\n\nconst cleanupText = (code: string) => {\n  return (\n    code\n      // parser problems on CRLF ignore all CR and leave LF;;\n      .replace(/\\r\\n?/g, '\\n')\n      // clean up html tags so that all attributes use single quotes, parser throws error on double quotes\n      .replace(\n        /<(\\w+)([^>]*)>/g,\n        (match, tag, attributes) => '<' + tag + attributes.replace(/=\"([^\"]*)\"/g, \"='$1'\") + '>'\n      )\n  );\n};\n\nconst processFrontmatter = (code: string) => {\n  const { text, metadata } = extractFrontMatter(code);\n  const { displayMode, title, config = {} } = metadata;\n  if (displayMode) {\n    // Needs to be supported for legacy reasons\n    if (!config.gantt) {\n      config.gantt = {};\n    }\n    config.gantt.displayMode = displayMode;\n  }\n  return { title, config, text };\n};\n\nconst processDirectives = (code: string) => {\n  const initDirective = utils.detectInit(code) ?? {};\n  const wrapDirectives = utils.detectDirective(code, 'wrap');\n  if (Array.isArray(wrapDirectives)) {\n    initDirective.wrap = wrapDirectives.some(({ type }) => type === 'wrap');\n  } else if (wrapDirectives?.type === 'wrap') {\n    initDirective.wrap = true;\n  }\n  return {\n    text: removeDirectives(code),\n    directive: initDirective,\n  };\n};\n\n/**\n * Preprocess the given code by cleaning it up, extracting front matter and directives,\n * cleaning and merging configuration, and removing comments.\n * @param code - The code to preprocess.\n * @returns The object containing the preprocessed code, title, and configuration.\n */\nexport function preprocessDiagram(code: string) {\n  const cleanedCode = cleanupText(code);\n  const frontMatterResult = processFrontmatter(cleanedCode);\n  const directiveResult = processDirectives(frontMatterResult.text);\n  const config = cleanAndMerge(frontMatterResult.config, directiveResult.directive);\n  code = cleanupComments(directiveResult.text);\n  return {\n    code,\n    title: frontMatterResult.title,\n    config,\n  } satisfies DiagramMetadata & { code: string };\n}\n", "export function toBase64(str: string) {\n  // ref: https://developer.mozilla.org/en-US/docs/Glossary/Base64#the_unicode_problem\n  const utf8Bytes = new TextEncoder().encode(str);\n  const utf8Str = Array.from(utf8Bytes, (byte) => String.fromCodePoint(byte)).join('');\n  return btoa(utf8Str);\n}\n", "/**\n * This file contains functions that are used internally by mermaid\n * and is not intended to be used by the end user.\n */\n// @ts-ignore TODO: Investigate D3 issue\nimport { select } from 'd3';\nimport { compile, serialize, stringify } from 'stylis';\n// @ts-ignore: TODO Fix ts errors\nimport DOMPurify from 'dompurify';\nimport isEmpty from 'lodash-es/isEmpty.js';\nimport packageJson from '../package.json' assert { type: 'json' };\nimport { addSVGa11yTitleDescription, setA11yDiagramInfo } from './accessibility.js';\nimport assignWithDepth from './assignWithDepth.js';\nimport * as configApi from './config.js';\nimport type { MermaidConfig } from './config.type.js';\nimport { addDiagrams } from './diagram-api/diagram-orchestration.js';\nimport type { DiagramMetadata, DiagramStyleClassDef } from './diagram-api/types.js';\nimport { Diagram } from './Diagram.js';\nimport { evaluate } from './diagrams/common/common.js';\nimport errorRenderer from './diagrams/error/errorRenderer.js';\nimport { attachFunctions } from './interactionDb.js';\nimport { log, setLogLevel } from './logger.js';\nimport { preprocessDiagram } from './preprocess.js';\nimport getStyles from './styles.js';\nimport theme from './themes/index.js';\nimport type { D3Element, ParseOptions, ParseResult, RenderResult } from './types.js';\nimport { decodeEntities } from './utils.js';\nimport { toBase64 } from './utils/base64.js';\n\nconst MAX_TEXTLENGTH = 50_000;\nconst MAX_TEXTLENGTH_EXCEEDED_MSG =\n  'graph TB;a[Maximum text size in diagram exceeded];style a fill:#faa';\n\nconst SECURITY_LVL_SANDBOX = 'sandbox';\nconst SECURITY_LVL_LOOSE = 'loose';\n\nconst XMLNS_SVG_STD = 'http://www.w3.org/2000/svg';\nconst XMLNS_XLINK_STD = 'http://www.w3.org/1999/xlink';\nconst XMLNS_XHTML_STD = 'http://www.w3.org/1999/xhtml';\n\n// ------------------------------\n// iFrame\nconst IFRAME_WIDTH = '100%';\nconst IFRAME_HEIGHT = '100%';\nconst IFRAME_STYLES = 'border:0;margin:0;';\nconst IFRAME_BODY_STYLE = 'margin:0';\nconst IFRAME_SANDBOX_OPTS = 'allow-top-navigation-by-user-activation allow-popups';\nconst IFRAME_NOT_SUPPORTED_MSG = 'The \"iframe\" tag is not supported by your browser.';\n\n// DOMPurify settings for svgCode\nconst DOMPURIFY_TAGS = ['foreignobject'];\nconst DOMPURIFY_ATTR = ['dominant-baseline'];\n\nfunction processAndSetConfigs(text: string) {\n  const processed = preprocessDiagram(text);\n  configApi.reset();\n  configApi.addDirective(processed.config ?? {});\n  return processed;\n}\n\n/**\n * Parse the text and validate the syntax.\n * @param text - The mermaid diagram definition.\n * @param parseOptions - Options for parsing. @see {@link ParseOptions}\n * @returns An object with the `diagramType` set to type of the diagram if valid. Otherwise `false` if parseOptions.suppressErrors is `true`.\n * @throws Error if the diagram is invalid and parseOptions.suppressErrors is false or not set.\n */\nasync function parse(\n  text: string,\n  parseOptions: ParseOptions & { suppressErrors: true }\n): Promise<ParseResult | false>;\nasync function parse(text: string, parseOptions?: ParseOptions): Promise<ParseResult>;\nasync function parse(text: string, parseOptions?: ParseOptions): Promise<ParseResult | false> {\n  addDiagrams();\n  try {\n    const { code, config } = processAndSetConfigs(text);\n    const diagram = await getDiagramFromText(code);\n    return { diagramType: diagram.type, config };\n  } catch (error) {\n    if (parseOptions?.suppressErrors) {\n      return false;\n    }\n    throw error;\n  }\n}\n\n/**\n * Create a CSS style that starts with the given class name, then the element,\n * with an enclosing block that has each of the cssClasses followed by !important;\n * @param cssClass - CSS class name\n * @param element - CSS element\n * @param cssClasses - list of CSS styles to append after the element\n * @returns - the constructed string\n */\nexport const cssImportantStyles = (\n  cssClass: string,\n  element: string,\n  cssClasses: string[] = []\n): string => {\n  return `\\n.${cssClass} ${element} { ${cssClasses.join(' !important; ')} !important; }`;\n};\n\n/**\n * Create the user styles\n * @internal\n * @param  config - configuration that has style and theme settings to use\n * @param  classDefs - the classDefs in the diagram text. Might be null if none were defined. Usually is the result of a call to getClasses(...)\n * @returns  the string with all the user styles\n */\nexport const createCssStyles = (\n  config: MermaidConfig,\n  classDefs: Map<string, DiagramStyleClassDef> | null | undefined = new Map()\n): string => {\n  let cssStyles = '';\n\n  // user provided theme CSS info\n  // If you add more configuration driven data into the user styles make sure that the value is\n  // sanitized by the sanitize CSS function TODO where is this method?  what should be used to replace it?  refactor so that it's always sanitized\n  if (config.themeCSS !== undefined) {\n    cssStyles += `\\n${config.themeCSS}`;\n  }\n\n  if (config.fontFamily !== undefined) {\n    cssStyles += `\\n:root { --mermaid-font-family: ${config.fontFamily}}`;\n  }\n  if (config.altFontFamily !== undefined) {\n    cssStyles += `\\n:root { --mermaid-alt-font-family: ${config.altFontFamily}}`;\n  }\n\n  // classDefs defined in the diagram text\n  if (classDefs instanceof Map) {\n    const htmlLabels = config.htmlLabels ?? config.flowchart?.htmlLabels; // TODO why specifically check the Flowchart diagram config?\n\n    const cssHtmlElements = ['> *', 'span']; // TODO make a constant\n    const cssShapeElements = ['rect', 'polygon', 'ellipse', 'circle', 'path']; // TODO make a constant\n\n    const cssElements = htmlLabels ? cssHtmlElements : cssShapeElements;\n\n    // create the CSS styles needed for each styleClass definition and css element\n    classDefs.forEach((styleClassDef) => {\n      // create the css styles for each cssElement and the styles (only if there are styles)\n      if (!isEmpty(styleClassDef.styles)) {\n        cssElements.forEach((cssElement) => {\n          cssStyles += cssImportantStyles(styleClassDef.id, cssElement, styleClassDef.styles);\n        });\n      }\n      // create the css styles for the tspan element and the text styles (only if there are textStyles)\n      if (!isEmpty(styleClassDef.textStyles)) {\n        cssStyles += cssImportantStyles(\n          styleClassDef.id,\n          'tspan',\n          (styleClassDef?.textStyles || []).map((s) => s.replace('color', 'fill'))\n        );\n      }\n    });\n  }\n  return cssStyles;\n};\n\nexport const createUserStyles = (\n  config: MermaidConfig,\n  graphType: string,\n  classDefs: Map<string, DiagramStyleClassDef> | undefined,\n  svgId: string\n): string => {\n  const userCSSstyles = createCssStyles(config, classDefs);\n  const allStyles = getStyles(graphType, userCSSstyles, config.themeVariables);\n\n  // Now turn all of the styles into a (compiled) string that starts with the id\n  // use the stylis library to compile the css, turn the results into a valid CSS string (serialize(...., stringify))\n  // @see https://github.com/thysultan/stylis\n  return serialize(compile(`${svgId}{${allStyles}}`), stringify);\n};\n\n/**\n * Clean up svgCode. Do replacements needed\n *\n * @param svgCode - the code to clean up\n * @param inSandboxMode - security level\n * @param useArrowMarkerUrls - should arrow marker's use full urls? (vs. just the anchors)\n * @returns the cleaned up svgCode\n */\nexport const cleanUpSvgCode = (\n  svgCode = '',\n  inSandboxMode: boolean,\n  useArrowMarkerUrls: boolean\n): string => {\n  let cleanedUpSvg = svgCode;\n\n  // Replace marker-end urls with just the # anchor (remove the preceding part of the URL)\n  if (!useArrowMarkerUrls && !inSandboxMode) {\n    cleanedUpSvg = cleanedUpSvg.replace(\n      /marker-end=\"url\\([\\d+./:=?A-Za-z-]*?#/g,\n      'marker-end=\"url(#'\n    );\n  }\n\n  cleanedUpSvg = decodeEntities(cleanedUpSvg);\n\n  // replace old br tags with newer style\n  cleanedUpSvg = cleanedUpSvg.replace(/<br>/g, '<br/>');\n\n  return cleanedUpSvg;\n};\n\n/**\n * Put the svgCode into an iFrame. Return the iFrame code\n *\n * @param svgCode - the svg code to put inside the iFrame\n * @param svgElement - the d3 node that has the current svgElement so we can get the height from it\n * @returns  - the code with the iFrame that now contains the svgCode\n */\nexport const putIntoIFrame = (svgCode = '', svgElement?: D3Element): string => {\n  const height = svgElement?.viewBox?.baseVal?.height\n    ? svgElement.viewBox.baseVal.height + 'px'\n    : IFRAME_HEIGHT;\n  const base64encodedSrc = toBase64(`<body style=\"${IFRAME_BODY_STYLE}\">${svgCode}</body>`);\n  return `<iframe style=\"width:${IFRAME_WIDTH};height:${height};${IFRAME_STYLES}\" src=\"data:text/html;charset=UTF-8;base64,${base64encodedSrc}\" sandbox=\"${IFRAME_SANDBOX_OPTS}\">\n  ${IFRAME_NOT_SUPPORTED_MSG}\n</iframe>`;\n};\n\n/**\n * Append an enclosing div, then svg, then g (group) to the d3 parentRoot. Set attributes.\n * Only set the style attribute on the enclosing div if divStyle is given.\n * Only set the xmlns:xlink attribute on svg if svgXlink is given.\n * Return the last node appended\n *\n * @param parentRoot - the d3 node to append things to\n * @param id - the value to set the id attr to\n * @param enclosingDivId - the id to set the enclosing div to\n * @param divStyle - if given, the style to set the enclosing div to\n * @param svgXlink - if given, the link to set the new svg element to\n * @returns - returns the parentRoot that had nodes appended\n */\nexport const appendDivSvgG = (\n  parentRoot: D3Element,\n  id: string,\n  enclosingDivId: string,\n  divStyle?: string,\n  svgXlink?: string\n): D3Element => {\n  const enclosingDiv = parentRoot.append('div');\n  enclosingDiv.attr('id', enclosingDivId);\n  if (divStyle) {\n    enclosingDiv.attr('style', divStyle);\n  }\n\n  const svgNode = enclosingDiv\n    .append('svg')\n    .attr('id', id)\n    .attr('width', '100%')\n    .attr('xmlns', XMLNS_SVG_STD);\n  if (svgXlink) {\n    svgNode.attr('xmlns:xlink', svgXlink);\n  }\n\n  svgNode.append('g');\n  return parentRoot;\n};\n\n/**\n * Append an iFrame node to the given parentNode and set the id, style, and 'sandbox' attributes\n *  Return the appended iframe d3 node\n *\n * @param parentNode - the d3 node to append the iFrame node to\n * @param iFrameId - id to use for the iFrame\n * @returns the appended iframe d3 node\n */\nfunction sandboxedIframe(parentNode: D3Element, iFrameId: string): D3Element {\n  return parentNode\n    .append('iframe')\n    .attr('id', iFrameId)\n    .attr('style', 'width: 100%; height: 100%;')\n    .attr('sandbox', '');\n}\n\n/**\n * Remove any existing elements from the given document\n *\n * @param doc - the document to removed elements from\n * @param id - id for any existing SVG element\n * @param divSelector - selector for any existing enclosing div element\n * @param iFrameSelector - selector for any existing iFrame element\n */\nexport const removeExistingElements = (\n  doc: Document,\n  id: string,\n  divId: string,\n  iFrameId: string\n) => {\n  // Remove existing SVG element if it exists\n  doc.getElementById(id)?.remove();\n  // Remove previous temporary element if it exists\n  // Both div and iframe needs to be cleared in case there is a config change happening between renders.\n  doc.getElementById(divId)?.remove();\n  doc.getElementById(iFrameId)?.remove();\n};\n\n/**\n * @deprecated - use the `mermaid.render` function instead of `mermaid.mermaidAPI.render`\n *\n * Deprecated for external use.\n */\n\nconst render = async function (\n  id: string,\n  text: string,\n  svgContainingElement?: Element\n): Promise<RenderResult> {\n  addDiagrams();\n\n  const processed = processAndSetConfigs(text);\n  text = processed.code;\n\n  const config = configApi.getConfig();\n  log.debug(config);\n\n  // Check the maximum allowed text size\n  if (text.length > (config?.maxTextSize ?? MAX_TEXTLENGTH)) {\n    text = MAX_TEXTLENGTH_EXCEEDED_MSG;\n  }\n\n  const idSelector = '#' + id;\n  const iFrameID = 'i' + id;\n  const iFrameID_selector = '#' + iFrameID;\n  const enclosingDivID = 'd' + id;\n  const enclosingDivID_selector = '#' + enclosingDivID;\n\n  const removeTempElements = () => {\n    // -------------------------------------------------------------------------------\n    // Remove the temporary HTML element if appropriate\n    const tmpElementSelector = isSandboxed ? iFrameID_selector : enclosingDivID_selector;\n    const node = select(tmpElementSelector).node();\n    if (node && 'remove' in node) {\n      node.remove();\n    }\n  };\n\n  let root: any = select('body');\n\n  const isSandboxed = config.securityLevel === SECURITY_LVL_SANDBOX;\n  const isLooseSecurityLevel = config.securityLevel === SECURITY_LVL_LOOSE;\n\n  const fontFamily = config.fontFamily;\n\n  // -------------------------------------------------------------------------------\n  // Define the root d3 node\n  // In regular execution the svgContainingElement will be the element with a mermaid class\n\n  if (svgContainingElement !== undefined) {\n    if (svgContainingElement) {\n      svgContainingElement.innerHTML = '';\n    }\n\n    if (isSandboxed) {\n      // If we are in sandboxed mode, we do everything mermaid related in a (sandboxed )iFrame\n      const iframe = sandboxedIframe(select(svgContainingElement), iFrameID);\n      root = select(iframe.nodes()[0]!.contentDocument!.body);\n      root.node().style.margin = 0;\n    } else {\n      root = select(svgContainingElement);\n    }\n    appendDivSvgG(root, id, enclosingDivID, `font-family: ${fontFamily}`, XMLNS_XLINK_STD);\n  } else {\n    // No svgContainingElement was provided\n\n    // If there is an existing element with the id, we remove it. This likely a previously rendered diagram\n    removeExistingElements(document, id, enclosingDivID, iFrameID);\n\n    // Add the temporary div used for rendering with the enclosingDivID.\n    // This temporary div will contain a svg with the id == id\n\n    if (isSandboxed) {\n      // If we are in sandboxed mode, we do everything mermaid related in a (sandboxed) iFrame\n      const iframe = sandboxedIframe(select('body'), iFrameID);\n      root = select(iframe.nodes()[0]!.contentDocument!.body);\n      root.node().style.margin = 0;\n    } else {\n      root = select('body');\n    }\n\n    appendDivSvgG(root, id, enclosingDivID);\n  }\n\n  // -------------------------------------------------------------------------------\n  // Create the diagram\n\n  // Important that we do not create the diagram until after the directives have been included\n  let diag: Diagram;\n  let parseEncounteredException;\n\n  try {\n    diag = await Diagram.fromText(text, { title: processed.title });\n  } catch (error) {\n    if (config.suppressErrorRendering) {\n      removeTempElements();\n      throw error;\n    }\n    diag = await Diagram.fromText('error');\n    parseEncounteredException = error;\n  }\n\n  // Get the temporary div element containing the svg\n  const element = root.select(enclosingDivID_selector).node();\n  const diagramType = diag.type;\n\n  // -------------------------------------------------------------------------------\n  // Create and insert the styles (user styles, theme styles, config styles)\n\n  // Insert an element into svg. This is where we put the styles\n  const svg = element.firstChild;\n  const firstChild = svg.firstChild;\n  const diagramClassDefs = diag.renderer.getClasses?.(text, diag);\n\n  const rules = createUserStyles(config, diagramType, diagramClassDefs, idSelector);\n\n  const style1 = document.createElement('style');\n  style1.innerHTML = rules;\n  svg.insertBefore(style1, firstChild);\n\n  // -------------------------------------------------------------------------------\n  // Draw the diagram with the renderer\n  try {\n    await diag.renderer.draw(text, id, packageJson.version, diag);\n  } catch (e) {\n    if (config.suppressErrorRendering) {\n      removeTempElements();\n    } else {\n      errorRenderer.draw(text, id, packageJson.version);\n    }\n    throw e;\n  }\n\n  // This is the d3 node for the svg element\n  const svgNode = root.select(`${enclosingDivID_selector} svg`);\n  const a11yTitle: string | undefined = diag.db.getAccTitle?.();\n  const a11yDescr: string | undefined = diag.db.getAccDescription?.();\n  addA11yInfo(diagramType, svgNode, a11yTitle, a11yDescr);\n  // -------------------------------------------------------------------------------\n  // Clean up SVG code\n  root.select(`[id=\"${id}\"]`).selectAll('foreignobject > *').attr('xmlns', XMLNS_XHTML_STD);\n\n  // Fix for when the base tag is used\n  let svgCode: string = root.select(enclosingDivID_selector).node().innerHTML;\n\n  log.debug('config.arrowMarkerAbsolute', config.arrowMarkerAbsolute);\n  svgCode = cleanUpSvgCode(svgCode, isSandboxed, evaluate(config.arrowMarkerAbsolute));\n\n  if (isSandboxed) {\n    const svgEl = root.select(enclosingDivID_selector + ' svg').node();\n    svgCode = putIntoIFrame(svgCode, svgEl);\n  } else if (!isLooseSecurityLevel) {\n    // Sanitize the svgCode using DOMPurify\n    svgCode = DOMPurify.sanitize(svgCode, {\n      ADD_TAGS: DOMPURIFY_TAGS,\n      ADD_ATTR: DOMPURIFY_ATTR,\n      HTML_INTEGRATION_POINTS: { foreignobject: true },\n    });\n  }\n\n  attachFunctions();\n\n  if (parseEncounteredException) {\n    throw parseEncounteredException;\n  }\n\n  removeTempElements();\n\n  return {\n    diagramType,\n    svg: svgCode,\n    bindFunctions: diag.db.bindFunctions,\n  };\n};\n\n/**\n * @param  userOptions - Initial Mermaid options\n */\nfunction initialize(userOptions: MermaidConfig = {}) {\n  const options: MermaidConfig = assignWithDepth({}, userOptions);\n  // Handle legacy location of font-family configuration\n  if (options?.fontFamily && !options.themeVariables?.fontFamily) {\n    if (!options.themeVariables) {\n      options.themeVariables = {};\n    }\n    options.themeVariables.fontFamily = options.fontFamily;\n  }\n\n  // Set default options\n  configApi.saveConfigFromInitialize(options);\n\n  if (options?.theme && options.theme in theme) {\n    // Todo merge with user options\n    options.themeVariables = theme[options.theme as keyof typeof theme].getThemeVariables(\n      options.themeVariables\n    );\n  } else if (options) {\n    options.themeVariables = theme.default.getThemeVariables(options.themeVariables);\n  }\n\n  const config =\n    typeof options === 'object' ? configApi.setSiteConfig(options) : configApi.getSiteConfig();\n\n  setLogLevel(config.logLevel);\n  addDiagrams();\n}\n\nconst getDiagramFromText = (text: string, metadata: Pick<DiagramMetadata, 'title'> = {}) => {\n  const { code } = preprocessDiagram(text);\n  return Diagram.fromText(code, metadata);\n};\n\n/**\n * Add accessibility (a11y) information to the diagram.\n *\n * @param diagramType - diagram type\n * @param svgNode - d3 node to insert the a11y title and desc info\n * @param a11yTitle - a11y title\n * @param a11yDescr - a11y description\n */\nfunction addA11yInfo(\n  diagramType: string,\n  svgNode: D3Element,\n  a11yTitle?: string,\n  a11yDescr?: string\n): void {\n  setA11yDiagramInfo(svgNode, diagramType);\n  addSVGa11yTitleDescription(svgNode, a11yTitle, a11yDescr, svgNode.attr('id'));\n}\n\n/**\n * @internal - Use mermaid.function instead of mermaid.mermaidAPI.function\n */\nexport const mermaidAPI = Object.freeze({\n  render,\n  parse,\n  getDiagramFromText,\n  initialize,\n  getConfig: configApi.getConfig,\n  setConfig: configApi.setConfig,\n  getSiteConfig: configApi.getSiteConfig,\n  updateSiteConfig: configApi.updateSiteConfig,\n  reset: () => {\n    configApi.reset();\n  },\n  globalReset: () => {\n    configApi.reset(configApi.defaultConfig);\n  },\n  defaultConfig: configApi.defaultConfig,\n});\n\nsetLogLevel(configApi.getConfig().logLevel);\nconfigApi.reset(configApi.getConfig());\nexport default mermaidAPI;\n", "/**\n * Web page integration module for the mermaid framework. It uses the mermaidAPI for mermaid\n * functionality and to render the diagrams to svg code!\n */\nimport { registerIconPacks } from './rendering-util/icons.js';\nimport { dedent } from 'ts-dedent';\nimport type { MermaidConfig } from './config.type.js';\nimport { detectType, registerLazyLoadedDiagrams } from './diagram-api/detectType.js';\nimport { addDiagrams } from './diagram-api/diagram-orchestration.js';\nimport { loadRegisteredDiagrams } from './diagram-api/loadDiagram.js';\nimport type { ExternalDiagramDefinition, SVG, SVGGroup } from './diagram-api/types.js';\nimport type { ParseErrorFunction } from './Diagram.js';\nimport type { UnknownDiagramError } from './errors.js';\nimport type { InternalHelpers } from './internals.js';\nimport { log } from './logger.js';\nimport { mermaidAPI } from './mermaidAPI.js';\nimport type { LayoutLoaderDefinition, RenderOptions } from './rendering-util/render.js';\nimport { registerLayoutLoaders } from './rendering-util/render.js';\nimport type { LayoutData } from './rendering-util/types.js';\nimport type { ParseOptions, ParseResult, RenderResult } from './types.js';\nimport type { DetailedError } from './utils.js';\nimport utils, { isDetailedError } from './utils.js';\n\nexport type {\n  DetailedError,\n  ExternalDiagramDefinition,\n  InternalHelpers,\n  LayoutData,\n  LayoutLoaderDefinition,\n  MermaidConfig,\n  ParseErrorFunction,\n  ParseOptions,\n  ParseResult,\n  RenderOptions,\n  RenderResult,\n  SVG,\n  SVGGroup,\n  UnknownDiagramError,\n};\n\nexport interface RunOptions {\n  /**\n   * The query selector to use when finding elements to render. Default: `\".mermaid\"`.\n   */\n  querySelector?: string;\n  /**\n   * The nodes to render. If this is set, `querySelector` will be ignored.\n   */\n  nodes?: ArrayLike<HTMLElement>;\n  /**\n   * A callback to call after each diagram is rendered.\n   */\n  postRenderCallback?: (id: string) => unknown;\n  /**\n   * If `true`, errors will be logged to the console, but not thrown. Default: `false`\n   */\n  suppressErrors?: boolean;\n}\n\nconst handleError = (error: unknown, errors: DetailedError[], parseError?: ParseErrorFunction) => {\n  log.warn(error);\n  if (isDetailedError(error)) {\n    // handle case where error string and hash were\n    // wrapped in object like`const error = { str, hash };`\n    if (parseError) {\n      parseError(error.str, error.hash);\n    }\n    errors.push({ ...error, message: error.str, error });\n  } else {\n    // assume it is just error string and pass it on\n    if (parseError) {\n      parseError(error);\n    }\n    if (error instanceof Error) {\n      errors.push({\n        str: error.message,\n        message: error.message,\n        hash: error.name,\n        error,\n      });\n    }\n  }\n};\n\n/**\n * ## run\n *\n * Function that goes through the document to find the chart definitions in there and render them.\n *\n * The function tags the processed attributes with the attribute data-processed and ignores found\n * elements with the attribute already set. This way the init function can be triggered several\n * times.\n *\n * ```mermaid\n * graph LR;\n *  a(Find elements)-->b{Processed}\n *  b-->|Yes|c(Leave element)\n *  b-->|No |d(Transform)\n * ```\n *\n * Renders the mermaid diagrams\n *\n * @param options - Optional runtime configs\n */\nconst run = async function (\n  options: RunOptions = {\n    querySelector: '.mermaid',\n  }\n) {\n  try {\n    await runThrowsErrors(options);\n  } catch (e) {\n    if (isDetailedError(e)) {\n      log.error(e.str);\n    }\n    if (mermaid.parseError) {\n      mermaid.parseError(e as string);\n    }\n    if (!options.suppressErrors) {\n      log.error('Use the suppressErrors option to suppress these errors');\n      throw e;\n    }\n  }\n};\n\nconst runThrowsErrors = async function (\n  { postRenderCallback, querySelector, nodes }: Omit<RunOptions, 'suppressErrors'> = {\n    querySelector: '.mermaid',\n  }\n) {\n  const conf = mermaidAPI.getConfig();\n\n  log.debug(`${!postRenderCallback ? 'No ' : ''}Callback function found`);\n\n  let nodesToProcess: ArrayLike<HTMLElement>;\n  if (nodes) {\n    nodesToProcess = nodes;\n  } else if (querySelector) {\n    nodesToProcess = document.querySelectorAll(querySelector);\n  } else {\n    throw new Error('Nodes and querySelector are both undefined');\n  }\n\n  log.debug(`Found ${nodesToProcess.length} diagrams`);\n  if (conf?.startOnLoad !== undefined) {\n    log.debug('Start On Load: ' + conf?.startOnLoad);\n    mermaidAPI.updateSiteConfig({ startOnLoad: conf?.startOnLoad });\n  }\n\n  // generate the id of the diagram\n  const idGenerator = new utils.InitIDGenerator(conf.deterministicIds, conf.deterministicIDSeed);\n\n  let txt: string;\n  const errors: DetailedError[] = [];\n\n  // element is the current div with mermaid class\n  // eslint-disable-next-line unicorn/prefer-spread\n  for (const element of Array.from(nodesToProcess)) {\n    log.info('Rendering diagram: ' + element.id);\n    /*! Check if previously processed */\n    if (element.getAttribute('data-processed')) {\n      continue;\n    }\n    element.setAttribute('data-processed', 'true');\n\n    const id = `mermaid-${idGenerator.next()}`;\n\n    // Fetch the graph definition including tags\n    txt = element.innerHTML;\n\n    // transforms the html to pure text\n    txt = dedent(utils.entityDecode(txt)) // removes indentation, required for YAML parsing\n      .trim()\n      .replace(/<br\\s*\\/?>/gi, '<br/>');\n\n    const init = utils.detectInit(txt);\n    if (init) {\n      log.debug('Detected early reinit: ', init);\n    }\n    try {\n      const { svg, bindFunctions } = await render(id, txt, element);\n      element.innerHTML = svg;\n      if (postRenderCallback) {\n        await postRenderCallback(id);\n      }\n      if (bindFunctions) {\n        bindFunctions(element);\n      }\n    } catch (error) {\n      handleError(error, errors, mermaid.parseError);\n    }\n  }\n  if (errors.length > 0) {\n    // TODO: We should be throwing an error object.\n    throw errors[0];\n  }\n};\n\n/**\n * Used to set configurations for mermaid.\n * This function should be called before the run function.\n * @param config - Configuration object for mermaid.\n */\n\nconst initialize = function (config: MermaidConfig) {\n  mermaidAPI.initialize(config);\n};\n\n/**\n * ## init\n *\n * @deprecated Use {@link initialize} and {@link run} instead.\n *\n * Renders the mermaid diagrams\n *\n * @param config - **Deprecated**, please set configuration in {@link initialize}.\n * @param nodes - **Default**: `.mermaid`. One of the following:\n * - A DOM Node\n * - An array of DOM nodes (as would come from a jQuery selector)\n * - A W3C selector, a la `.mermaid`\n * @param callback - Called once for each rendered diagram's id.\n */\nconst init = async function (\n  config?: MermaidConfig,\n  nodes?: string | HTMLElement | NodeListOf<HTMLElement>,\n  callback?: (id: string) => unknown\n) {\n  log.warn('mermaid.init is deprecated. Please use run instead.');\n  if (config) {\n    initialize(config);\n  }\n  const runOptions: RunOptions = { postRenderCallback: callback, querySelector: '.mermaid' };\n  if (typeof nodes === 'string') {\n    runOptions.querySelector = nodes;\n  } else if (nodes) {\n    if (nodes instanceof HTMLElement) {\n      runOptions.nodes = [nodes];\n    } else {\n      runOptions.nodes = nodes;\n    }\n  }\n  await run(runOptions);\n};\n\n/**\n * Used to register external diagram types.\n * @param diagrams - Array of {@link ExternalDiagramDefinition}.\n * @param opts - If opts.lazyLoad is false, the diagrams will be loaded immediately.\n */\nconst registerExternalDiagrams = async (\n  diagrams: ExternalDiagramDefinition[],\n  {\n    lazyLoad = true,\n  }: {\n    lazyLoad?: boolean;\n  } = {}\n) => {\n  addDiagrams();\n  registerLazyLoadedDiagrams(...diagrams);\n  if (lazyLoad === false) {\n    await loadRegisteredDiagrams();\n  }\n};\n\n/**\n * ##contentLoaded Callback function that is called when page is loaded. This functions fetches\n * configuration for mermaid rendering and calls init for rendering the mermaid diagrams on the\n * page.\n */\nconst contentLoaded = function () {\n  if (mermaid.startOnLoad) {\n    const { startOnLoad } = mermaidAPI.getConfig();\n    if (startOnLoad) {\n      mermaid.run().catch((err) => log.error('Mermaid failed to initialize', err));\n    }\n  }\n};\n\nif (typeof document !== 'undefined') {\n  /*!\n   * Wait for document loaded before starting the execution\n   */\n  window.addEventListener('load', contentLoaded, false);\n}\n\n/**\n * ## setParseErrorHandler  Alternative to directly setting parseError using:\n *\n * ```js\n * mermaid.parseError = function(err,hash) {\n *   forExampleDisplayErrorInGui(err);  // do something with the error\n * };\n * ```\n *\n * This is provided for environments where the mermaid object can't directly have a new member added\n * to it (eg. dart interop wrapper). (Initially there is no parseError member of mermaid).\n *\n * @param parseErrorHandler - New parseError() callback.\n */\nconst setParseErrorHandler = function (parseErrorHandler: (err: any, hash: any) => void) {\n  mermaid.parseError = parseErrorHandler;\n};\n\nconst executionQueue: (() => Promise<unknown>)[] = [];\nlet executionQueueRunning = false;\nconst executeQueue = async () => {\n  if (executionQueueRunning) {\n    return;\n  }\n  executionQueueRunning = true;\n  while (executionQueue.length > 0) {\n    const f = executionQueue.shift();\n    if (f) {\n      try {\n        await f();\n      } catch (e) {\n        log.error('Error executing queue', e);\n      }\n    }\n  }\n  executionQueueRunning = false;\n};\n\n/**\n * Parse the text and validate the syntax.\n * @param text - The mermaid diagram definition.\n * @param parseOptions - Options for parsing. @see {@link ParseOptions}\n * @returns If valid, {@link ParseResult} otherwise `false` if parseOptions.suppressErrors is `true`.\n * @throws Error if the diagram is invalid and parseOptions.suppressErrors is false or not set.\n *\n * @example\n * ```js\n * console.log(await mermaid.parse('flowchart \\n a --> b'));\n * // { diagramType: 'flowchart-v2' }\n * console.log(await mermaid.parse('wrong \\n a --> b', { suppressErrors: true }));\n * // false\n * console.log(await mermaid.parse('wrong \\n a --> b', { suppressErrors: false }));\n * // throws Error\n * console.log(await mermaid.parse('wrong \\n a --> b'));\n * // throws Error\n * ```\n */\nconst parse: typeof mermaidAPI.parse = async (text, parseOptions) => {\n  return new Promise((resolve, reject) => {\n    // This promise will resolve when the render call is done.\n    // It will be queued first and will be executed when it is first in line\n    const performCall = () =>\n      new Promise((res, rej) => {\n        mermaidAPI.parse(text, parseOptions).then(\n          (r) => {\n            // This resolves for the promise for the queue handling\n            res(r);\n            // This fulfills the promise sent to the value back to the original caller\n            resolve(r);\n          },\n          (e) => {\n            log.error('Error parsing', e);\n            mermaid.parseError?.(e);\n            rej(e);\n            reject(e);\n          }\n        );\n      });\n    executionQueue.push(performCall);\n    executeQueue().catch(reject);\n  });\n};\n\n/**\n * Function that renders an svg with a graph from a chart definition. Usage example below.\n *\n * ```javascript\n *  element = document.querySelector('#graphDiv');\n *  const graphDefinition = 'graph TB\\na-->b';\n *  const { svg, bindFunctions } = await mermaid.render('graphDiv', graphDefinition);\n *  element.innerHTML = svg;\n *  bindFunctions?.(element);\n * ```\n *\n * @remarks\n * Multiple calls to this function will be enqueued to run serially.\n *\n * @param id - The id for the SVG element (the element to be rendered)\n * @param text - The text for the graph definition\n * @param container - HTML element where the svg will be inserted. (Is usually element with the .mermaid class)\n *   If no svgContainingElement is provided then the SVG element will be appended to the body.\n *    Selector to element in which a div with the graph temporarily will be\n *   inserted. If one is provided a hidden div will be inserted in the body of the page instead. The\n *   element will be removed when rendering is completed.\n * @returns Returns the SVG Definition and BindFunctions.\n */\nconst render: typeof mermaidAPI.render = (id, text, container) => {\n  return new Promise((resolve, reject) => {\n    // This promise will resolve when the mermaidAPI.render call is done.\n    // It will be queued first and will be executed when it is first in line\n    const performCall = () =>\n      new Promise((res, rej) => {\n        mermaidAPI.render(id, text, container).then(\n          (r) => {\n            // This resolves for the promise for the queue handling\n            res(r);\n            // This fulfills the promise sent to the value back to the original caller\n            resolve(r);\n          },\n          (e) => {\n            log.error('Error parsing', e);\n            mermaid.parseError?.(e);\n            rej(e);\n            reject(e);\n          }\n        );\n      });\n    executionQueue.push(performCall);\n    executeQueue().catch(reject);\n  });\n};\n\nexport interface Mermaid {\n  startOnLoad: boolean;\n  parseError?: ParseErrorFunction;\n  /**\n   * @deprecated Use {@link parse} and {@link render} instead. Please [open a discussion](https://github.com/mermaid-js/mermaid/discussions) if your use case does not fit the new API.\n   * @internal\n   */\n  mermaidAPI: typeof mermaidAPI;\n  parse: typeof parse;\n  render: typeof render;\n  /**\n   * @deprecated Use {@link initialize} and {@link run} instead.\n   */\n  init: typeof init;\n  run: typeof run;\n  registerLayoutLoaders: typeof registerLayoutLoaders;\n  registerExternalDiagrams: typeof registerExternalDiagrams;\n  initialize: typeof initialize;\n  contentLoaded: typeof contentLoaded;\n  setParseErrorHandler: typeof setParseErrorHandler;\n  detectType: typeof detectType;\n  registerIconPacks: typeof registerIconPacks;\n}\n\nconst mermaid: Mermaid = {\n  startOnLoad: true,\n  mermaidAPI,\n  parse,\n  render,\n  init,\n  run,\n  registerExternalDiagrams,\n  registerLayoutLoaders,\n  initialize,\n  parseError: undefined,\n  contentLoaded,\n  setParseErrorHandler,\n  detectType,\n  registerIconPacks,\n};\n\nexport default mermaid;\n"], "mappings": "mrCAMA,IAAMA,GAAK,KAELC,GAA4BC,EAACC,GAC1B,+DAA+D,KAAKA,CAAG,EAD9C,YAI5BC,GAAwBF,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,iDAAgB,EACjD,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,EAEOG,GAAQD,GCjBf,IAAME,GAAK,YAELC,GAA4BC,EAAA,CAACC,EAAKC,IAIpCA,GAAQ,WAAW,kBAAoB,iBACvCA,GAAQ,WAAW,kBAAoB,MAEhC,GAEF,YAAY,KAAKD,CAAG,EATK,YAY5BE,GAAwBH,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAI,CAAQ,EAAI,KAAM,QAAO,mDAAkB,EACnD,MAAO,CAAE,GAAAN,GAAI,QAAAM,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAP,GACA,SAAAC,GACA,OAAAI,EACF,EAEOG,GAAQD,GCzBf,IAAME,GAAK,eAELC,GAA4BC,EAAA,CAACC,EAAKC,IAClCA,GAAQ,WAAW,kBAAoB,WAClC,IAGLA,GAAQ,WAAW,kBAAoB,QACzCA,EAAO,OAAS,OAId,YAAY,KAAKD,CAAG,GAAKC,GAAQ,WAAW,kBAAoB,gBAC3D,GAEF,gBAAgB,KAAKD,CAAG,GAbC,YAgB5BE,GAAwBH,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAI,CAAQ,EAAI,KAAM,QAAO,mDAAkB,EACnD,MAAO,CAAE,GAAAN,GAAI,QAAAM,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAP,GACA,SAAAC,GACA,OAAAI,EACF,EAEOG,GAAQD,GC7Bf,IAAME,GAAK,KAELC,GAA4BC,EAACC,GAC1B,gBAAgB,KAAKA,CAAG,EADC,YAI5BC,GAAwBF,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,iDAAgB,EACjD,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,EAEOG,GAAQD,GCpBf,IAAME,GAAK,WAELC,GAA4BC,EAACC,GAC1B,eAAe,KAAKA,CAAG,EADE,YAI5BC,GAAwBF,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,uDAAsB,EACvD,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,EAEOG,GAAQD,GCdf,IAAME,GAAK,QAELC,GAA4BC,EAACC,GAC1B,YAAY,KAAKA,CAAG,EADK,YAI5BC,GAAwBF,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,oDAAmB,EACpD,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,EAEOG,GAAQD,GCjBf,IAAME,GAAK,OAELC,GAA4BC,EAACC,GAC1B,WAAW,KAAKA,CAAG,EADM,YAI5BC,GAAwBF,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,mDAAkB,EACnD,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAH8B,UAKjBC,GAAkC,CAC7C,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,ECfA,IAAMG,GAAK,MAELC,GAA4BC,EAACC,GAC1B,UAAU,KAAKA,CAAG,EADO,YAI5BC,GAAwBF,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,kDAAiB,EAClD,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAH8B,UAKjBC,GAAiC,CAC5C,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,ECfA,IAAMG,GAAK,gBAELC,GAA4BC,EAACC,GAC1B,oBAAoB,KAAKA,CAAG,EADH,YAI5BC,GAAwBF,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,uDAAsB,EACvD,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,EAEOG,GAAQD,GCjBf,IAAME,GAAK,UAELC,GAA4BC,EAACC,GAC1B,mBAAmB,KAAKA,CAAG,EADF,YAI5BC,GAAwBF,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,sDAAqB,EACtD,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,EAEOG,GAAQD,GCjBf,IAAME,GAAK,cAELC,GAA4BC,EAACC,GAC1B,4BAA4B,KAAKA,CAAG,EADX,YAI5BC,GAAwBF,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,0DAAyB,EAC1D,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,EAEOG,GAAQD,GCjBf,IAAME,GAAK,WAELC,GAA4BC,EAACC,GAC1B,sBAAsB,KAAKA,CAAG,EADL,YAI5BC,GAAwBF,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,uDAAsB,EACvD,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,EAEOG,GAAQD,GCjBf,IAAME,GAAK,QAELC,GAA4BC,EAAA,CAACC,EAAKC,IAElCA,GAAQ,OAAO,kBAAoB,gBAC9B,GAGF,mBAAmB,KAAKD,CAAG,EANF,YAS5BE,GAAwBH,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAI,CAAQ,EAAI,KAAM,QAAO,oDAAmB,EACpD,MAAO,CAAE,GAAAN,GAAI,QAAAM,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAP,GACA,SAAAC,GACA,OAAAI,EACF,EAEOG,GAAQD,GCtBf,IAAME,GAAK,eAELC,GAA4BC,EAAA,CAACC,EAAKC,IAElC,mBAAmB,KAAKD,CAAG,GAAKC,GAAQ,OAAO,kBAAoB,gBAC9D,GAGF,sBAAsB,KAAKD,CAAG,EANL,YAS5BE,GAAwBH,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAI,CAAQ,EAAI,KAAM,QAAO,uDAAsB,EACvD,MAAO,CAAE,GAAAN,GAAI,QAAAM,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAP,GACA,SAAAC,GACA,OAAAI,EACF,EAEOG,GAAQD,GCtBf,IAAME,GAAK,QAELC,GAA4BC,EAAA,CAACC,EAAKC,IAGlCA,GAAQ,OAAO,kBAAoB,gBAC9B,GAEF,mBAAmB,KAAKD,CAAG,EANF,YAS5BE,GAAwBH,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAI,CAAQ,EAAI,KAAM,QAAO,oDAAmB,EACpD,MAAO,CAAE,GAAAN,GAAI,QAAAM,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAP,GACA,SAAAC,GACA,OAAAI,EACF,EAEOG,GAAQD,GCtBf,IAAME,GAAK,eAELC,GAA4BC,EAAA,CAACC,EAAKC,IAClC,yBAAsB,KAAKD,CAAG,GAG9B,mBAAmB,KAAKA,CAAG,GAAKC,GAAQ,OAAO,kBAAoB,iBAJvC,YAU5BC,GAAwBH,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAI,CAAQ,EAAI,KAAM,QAAO,uDAAsB,EACvD,MAAO,CAAE,GAAAN,GAAI,QAAAM,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAP,GACA,SAAAC,GACA,OAAAI,EACF,EAEOG,GAAQD,GCvBf,IAAME,GAAK,UAELC,GAA4BC,EAACC,GAC1B,cAAc,KAAKA,CAAG,EADG,YAI5BC,GAAwBF,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,sDAAqB,EACtD,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,EAEOG,GAAQD,GCXR,IAAME,GAAOC,EAAA,CAACC,EAAeC,EAAYC,IAAoB,CAClEC,EAAI,MAAM;AAAA,CAAkC,EAC5C,IAAMC,EAAWC,GAAiBJ,CAAE,EAC9BK,EAAcF,EAAI,OAAO,GAAG,EAElCA,EAAI,KAAK,UAAW,cAAc,EAClCG,GAAiBH,EAAK,IAAK,IAAK,EAAI,EAEpCE,EAAE,OAAO,MAAM,EACZ,KAAK,QAAS,YAAY,EAC1B,KACC,IACA,4kBACF,EAEFA,EAAE,OAAO,MAAM,EACZ,KAAK,QAAS,YAAY,EAC1B,KACC,IACA,6LACF,EAEFA,EAAE,OAAO,MAAM,EACZ,KAAK,QAAS,YAAY,EAC1B,KACC,IACA,8LACF,EAEFA,EAAE,OAAO,MAAM,EACZ,KAAK,QAAS,YAAY,EAC1B,KACC,IACA,6GACF,EAEFA,EAAE,OAAO,MAAM,EACZ,KAAK,QAAS,YAAY,EAC1B,KACC,IACA,kHACF,EAEFA,EAAE,OAAO,MAAM,EACZ,KAAK,QAAS,YAAY,EAC1B,KACC,IACA,+LACF,EAEFA,EAAE,OAAO,MAAM,EACZ,KAAK,QAAS,YAAY,EAC1B,KAAK,IAAK,IAAI,EACd,KAAK,IAAK,GAAG,EACb,KAAK,YAAa,OAAO,EACzB,MAAM,cAAe,QAAQ,EAC7B,KAAK,sBAAsB,EAC9BA,EAAE,OAAO,MAAM,EACZ,KAAK,QAAS,YAAY,EAC1B,KAAK,IAAK,IAAI,EACd,KAAK,IAAK,GAAG,EACb,KAAK,YAAa,OAAO,EACzB,MAAM,cAAe,QAAQ,EAC7B,KAAK,mBAAmBJ,CAAO,EAAE,CACtC,EAhEoB,QAkEPM,GAAW,CAAE,KAAAV,EAAK,EAExBW,GAAQD,GC7Ef,IAAME,GAA6B,CACjC,GAAI,CAAC,EACL,SAAAC,GACA,OAAQ,CACN,MAAOC,EAAA,IAAY,CAEnB,EAFO,QAGT,CACF,EAEOC,GAAQH,GCPf,IAAMI,GAAK,gBAELC,GAA4BC,EAAA,CAACC,EAAKC,EAAS,CAAC,IAG9C,oBAAoB,KAAKD,CAAG,GAE3B,sBAAsB,KAAKA,CAAG,GAAKC,GAAQ,WAAW,kBAAoB,OAE3EA,EAAO,OAAS,MACT,IAEF,GAVyB,YAa5BC,GAAwBH,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAI,CAAQ,EAAI,KAAM,QAAO,mDAAmB,EACpD,MAAO,CAAE,GAAAN,GAAI,QAAAM,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAP,GACA,SAAAC,GACA,OAAAI,EACF,EAEOG,GAAQD,GC1Bf,IAAME,GAAK,WAELC,GAA4BC,EAACC,GAC1B,eAAe,KAAKA,CAAG,EADE,YAI5BC,GAAwBF,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,2DAA0B,EAC3D,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,EAEOG,GAAQD,GClBf,IAAME,GAAK,UAELC,GAA4BC,EAACC,GAC1B,cAAc,KAAKA,CAAG,EADG,YAI5BC,GAAwBF,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,0DAAyB,EAC1D,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,EAEOG,GAAQD,GCjBf,IAAME,GAAK,SAELC,GAA4BC,EAACC,GAC1B,aAAa,KAAKA,CAAG,EADI,YAI5BC,GAAwBF,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,yDAAwB,EACzD,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAH8B,UAKxBC,GAAoC,CACxC,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,EAEOG,GAAQD,GCpBf,IAAME,GAAK,SAELC,GAA4BC,EAACC,GAC1B,kBAAkB,KAAKA,CAAG,EADD,YAI5BC,GAASF,EAAA,SAAY,CACzB,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,qDAAoB,EACrD,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAHe,UAKTC,GAAoC,CACxC,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,EAEOG,GAAQD,GCbf,IAAME,GAAK,SAELC,GAA4BC,EAACC,GAC1B,kBAAkB,KAAKA,CAAG,EADD,YAI5BC,GAAwBF,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,+CAAc,EAC/C,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAH8B,UAKjBC,GAAoC,CAC/C,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,ECfA,IAAMG,GAAK,QAELC,GAA4BC,EAACC,GAC1B,iBAAiB,KAAKA,CAAG,EADA,YAI5BC,GAAwBF,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,+CAAc,EAC/C,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAH8B,UAKjBC,GAAmC,CAC9C,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,ECnBA,IAAMG,GAAK,QAELC,GAA4BC,EAACC,GAC1B,iBAAiB,KAAKA,CAAG,EADA,YAI5BC,GAASF,EAAA,SAAY,CACzB,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,oDAAmB,EACpD,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAHe,UAKTC,GAAoC,CACxC,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,EAEOG,GAAQD,GCbf,IAAME,GAAK,eAELC,GAA4BC,EAACC,GAC1B,mBAAmB,KAAKA,CAAG,EADF,YAI5BC,GAAwBF,EAAA,SAAY,CACxC,GAAM,CAAE,QAAAG,CAAQ,EAAI,KAAM,QAAO,2DAA0B,EAC3D,MAAO,CAAE,GAAAL,GAAI,QAAAK,CAAQ,CACvB,EAH8B,UAKxBC,GAA0C,CAC9C,GAAAN,GACA,SAAAC,GACA,OAAAG,EACF,EAEOG,GAAQD,GCOf,IAAIE,GAAoB,GACXC,EAAcC,EAAA,IAAM,CAC3BF,KAKJA,GAAoB,GACpBG,EAAgB,QAASC,GAAeC,GAC/BA,EAAK,YAAY,EAAE,KAAK,IAAM,OACtC,EACDF,EACE,MAEA,CACE,GAAI,CACF,MAAOD,EAAA,IAAM,CAEb,EAFO,QAGT,EACA,OAAQ,CAAC,EACT,SAAU,CACR,KAAMA,EAAA,IAAM,CAEZ,EAFM,OAGR,EACA,OAAQ,CACN,MAAOA,EAAA,IAAM,CACX,MAAM,IAAI,MACR,qMAGF,CACF,EANO,QAOT,EACA,KAAMA,EAAA,IAAM,KAAN,OACR,EACCG,GACQA,EAAK,YAAY,EAAE,UAAU,EAAE,WAAW,KAAK,CAE1D,EAEAC,EACEC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAR,GACAS,GACAC,GACAV,GACAA,GACAW,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,EACF,EACF,EArE2B,eC3BpB,IAAMC,GAAyBC,EAAA,SAAY,CAChDC,EAAI,MAAM,6BAA6B,EAsBvC,IAAMC,GApBU,MAAM,QAAQ,WAC5B,OAAO,QAAQC,EAAS,EAAE,IAAI,MAAO,CAACC,EAAK,CAAE,SAAAC,EAAU,OAAAC,CAAO,CAAC,IAAM,CACnE,GAAIA,EACF,GAAI,CACFC,EAAWH,CAAG,CAChB,MAAQ,CACN,GAAI,CAEF,GAAM,CAAE,QAAAI,EAAS,GAAAC,CAAG,EAAI,MAAMH,EAAO,EACrCI,EAAgBD,EAAID,EAASH,CAAQ,CACvC,OAASM,EAAK,CAEZ,MAAAV,EAAI,MAAM,4CAA4CG,CAAG,4BAA4B,EACrF,OAAOD,GAAUC,CAAG,EACdO,CACR,CACF,CAEJ,CAAC,CACH,GACuB,OAAQC,GAAWA,EAAO,SAAW,UAAU,EACtE,GAAIV,EAAO,OAAS,EAAG,CACrBD,EAAI,MAAM,kBAAkBC,EAAO,MAAM,oBAAoB,EAC7D,QAAWW,KAAOX,EAChBD,EAAI,MAAMY,CAAG,EAEf,MAAM,IAAI,MAAM,kBAAkBX,EAAO,MAAM,oBAAoB,CACrE,CACF,EA/BsC,0BCA/B,IAAIY,GAAU,OACVC,GAAU,OACVC,GAAc,OAIlB,IAAIC,GAAS,UAKb,IAAIC,GAAY,aACZC,GAAY,aAIhB,IAAIC,GAAQ,SChBZ,IAAIC,GAAM,KAAK,IAMXC,EAAO,OAAO,aAqBlB,SAASC,GAAMC,EAAO,CAC5B,OAAOA,EAAM,KAAK,CACnB,CAFgBC,EAAAF,GAAA,QAmBT,SAASG,EAASC,EAAOC,EAASC,EAAa,CACrD,OAAOF,EAAM,QAAQC,EAASC,CAAW,CAC1C,CAFgBC,EAAAJ,EAAA,WAUT,SAASK,GAASJ,EAAOK,EAAQC,EAAU,CACjD,OAAON,EAAM,QAAQK,EAAQC,CAAQ,CACtC,CAFgBH,EAAAC,GAAA,WAST,SAASG,EAAQP,EAAOQ,EAAO,CACrC,OAAOR,EAAM,WAAWQ,CAAK,EAAI,CAClC,CAFgBL,EAAAI,EAAA,UAUT,SAASE,EAAQT,EAAOU,EAAOC,EAAK,CAC1C,OAAOX,EAAM,MAAMU,EAAOC,CAAG,CAC9B,CAFgBR,EAAAM,EAAA,UAQT,SAASG,EAAQZ,EAAO,CAC9B,OAAOA,EAAM,MACd,CAFgBG,EAAAS,EAAA,UAQT,SAASC,GAAQb,EAAO,CAC9B,OAAOA,EAAM,MACd,CAFgBG,EAAAU,GAAA,UAST,SAASC,EAAQd,EAAOe,EAAO,CACrC,OAAOA,EAAM,KAAKf,CAAK,EAAGA,CAC3B,CAFgBG,EAAAW,EAAA,UCtGT,IAAIE,GAAO,EACPC,EAAS,EACTC,GAAS,EACTC,EAAW,EACXC,EAAY,EACZC,EAAa,GAYjB,SAASC,GAAMC,EAAOC,EAAMC,EAAQC,EAAMC,EAAOC,EAAUV,EAAQW,EAAU,CACnF,MAAO,CAAC,MAAON,EAAO,KAAMC,EAAM,OAAQC,EAAQ,KAAMC,EAAM,MAAOC,EAAO,SAAUC,EAAU,KAAMZ,GAAM,OAAQC,EAAQ,OAAQC,EAAQ,OAAQ,GAAI,SAAUW,CAAQ,CAC3K,CAFgBC,EAAAR,GAAA,QA0BT,SAASS,IAAQ,CACvB,OAAOC,CACR,CAFgBC,EAAAF,GAAA,QAOT,SAASG,IAAQ,CACvB,OAAAF,EAAYG,EAAW,EAAIC,EAAOC,EAAY,EAAEF,CAAQ,EAAI,EAExDG,IAAUN,IAAc,KAC3BM,EAAS,EAAGC,MAENP,CACR,CAPgBC,EAAAC,GAAA,QAYT,SAASM,GAAQ,CACvB,OAAAR,EAAYG,EAAWM,GAASL,EAAOC,EAAYF,GAAU,EAAI,EAE7DG,IAAUN,IAAc,KAC3BM,EAAS,EAAGC,MAENP,CACR,CAPgBC,EAAAO,EAAA,QAYT,SAASE,GAAQ,CACvB,OAAON,EAAOC,EAAYF,CAAQ,CACnC,CAFgBF,EAAAS,EAAA,QAOT,SAASC,GAAS,CACxB,OAAOR,CACR,CAFgBF,EAAAU,EAAA,SAST,SAASC,GAAOC,EAAOC,EAAK,CAClC,OAAOC,EAAOV,EAAYQ,EAAOC,CAAG,CACrC,CAFgBb,EAAAW,GAAA,SAQT,SAASI,EAAOC,EAAM,CAC5B,OAAQA,EAAM,CAEb,IAAK,GAAG,IAAK,GAAG,IAAK,IAAI,IAAK,IAAI,IAAK,IACtC,MAAO,GAER,IAAK,IAAI,IAAK,IAAI,IAAK,IAAI,IAAK,IAAI,IAAK,IAAI,IAAK,IAAI,IAAK,KAE3D,IAAK,IAAI,IAAK,KAAK,IAAK,KACvB,MAAO,GAER,IAAK,IACJ,MAAO,GAER,IAAK,IAAI,IAAK,IAAI,IAAK,IAAI,IAAK,IAC/B,MAAO,GAER,IAAK,IAAI,IAAK,IACb,MAAO,EACT,CAEA,MAAO,EACR,CAtBgBhB,EAAAe,EAAA,SA4BT,SAASE,GAAOC,EAAO,CAC7B,OAAOZ,GAAOD,EAAS,EAAGG,GAASW,EAAOf,EAAac,CAAK,EAAGhB,EAAW,EAAG,CAAC,CAC/E,CAFgBF,EAAAiB,GAAA,SAQT,SAASG,GAASF,EAAO,CAC/B,OAAOd,EAAa,GAAIc,CACzB,CAFgBlB,EAAAoB,GAAA,WAQT,SAASC,GAASL,EAAM,CAC9B,OAAOM,GAAKX,GAAMT,EAAW,EAAGqB,GAAUP,IAAS,GAAKA,EAAO,EAAIA,IAAS,GAAKA,EAAO,EAAIA,CAAI,CAAC,CAAC,CACnG,CAFgBhB,EAAAqB,GAAA,WAgBT,SAASG,GAAYC,EAAM,CACjC,MAAOC,EAAYC,EAAK,IACnBD,EAAY,IACfE,EAAK,EAIP,OAAOC,EAAMJ,CAAI,EAAI,GAAKI,EAAMH,CAAS,EAAI,EAAI,GAAK,GACvD,CARgBI,EAAAN,GAAA,cAgCT,SAASO,GAAUC,EAAOC,EAAO,CACvC,KAAO,EAAEA,GAASC,EAAK,GAElB,EAAAC,EAAY,IAAMA,EAAY,KAAQA,EAAY,IAAMA,EAAY,IAAQA,EAAY,IAAMA,EAAY,KAA9G,CAGD,OAAOC,GAAMJ,EAAOK,EAAM,GAAKJ,EAAQ,GAAKK,EAAK,GAAK,IAAMJ,EAAK,GAAK,GAAG,CAC1E,CAPgBK,EAAAR,GAAA,YAaT,SAASS,GAAWC,EAAM,CAChC,KAAOP,EAAK,GACX,OAAQC,EAAW,CAElB,KAAKM,EACJ,OAAOC,EAER,IAAK,IAAI,IAAK,IACTD,IAAS,IAAMA,IAAS,IAC3BD,GAAUL,CAAS,EACpB,MAED,IAAK,IACAM,IAAS,IACZD,GAAUC,CAAI,EACf,MAED,IAAK,IACJP,EAAK,EACL,KACF,CAED,OAAOQ,CACR,CAvBgBH,EAAAC,GAAA,aA8BT,SAASG,GAAWF,EAAMT,EAAO,CACvC,KAAOE,EAAK,GAEPO,EAAON,IAAc,IAGpB,GAAIM,EAAON,IAAc,IAAWG,EAAK,IAAM,GACnD,MAEF,MAAO,KAAOF,GAAMJ,EAAOU,EAAW,CAAC,EAAI,IAAME,EAAKH,IAAS,GAAKA,EAAOP,EAAK,CAAC,CAClF,CAVgBK,EAAAI,GAAA,aAgBT,SAASE,GAAYb,EAAO,CAClC,KAAO,CAACc,EAAMR,EAAK,CAAC,GACnBJ,EAAK,EAEN,OAAOE,GAAMJ,EAAOU,CAAQ,CAC7B,CALgBH,EAAAM,GAAA,cCnPT,SAASE,GAASC,EAAO,CAC/B,OAAOC,GAAQC,GAAM,GAAI,KAAM,KAAM,KAAM,CAAC,EAAE,EAAGF,EAAQG,GAAMH,CAAK,EAAG,EAAG,CAAC,CAAC,EAAGA,CAAK,CAAC,CACtF,CAFgBI,EAAAL,GAAA,WAgBT,SAASG,GAAOF,EAAOK,EAAMC,EAAQC,EAAMC,EAAOC,EAAUC,EAAQC,EAAQC,EAAc,CAiBhG,QAhBIC,EAAQ,EACRC,EAAS,EACTC,EAASL,EACTM,EAAS,EACTC,EAAW,EACXC,EAAW,EACXC,EAAW,EACXC,EAAW,EACXC,EAAY,EACZC,EAAY,EACZC,EAAO,GACPC,EAAQhB,EACRiB,EAAWhB,EACXiB,EAAYnB,EACZoB,EAAaJ,EAEVH,GACN,OAAQF,EAAWI,EAAWA,EAAYM,EAAK,EAAG,CAEjD,IAAK,IACJ,GAAIV,GAAY,KAAOW,EAAOF,EAAYZ,EAAS,CAAC,GAAK,GAAI,CACxDe,GAAQH,GAAcI,EAAQC,GAAQV,CAAS,EAAG,IAAK,KAAK,EAAG,MAAOW,GAAIpB,EAAQF,EAAOE,EAAQ,CAAC,EAAI,CAAC,CAAC,GAAK,KAChHQ,EAAY,IACb,KACD,CAED,IAAK,IAAI,IAAK,IAAI,IAAK,IACtBM,GAAcK,GAAQV,CAAS,EAC/B,MAED,IAAK,GAAG,IAAK,IAAI,IAAK,IAAI,IAAK,IAC9BK,GAAcO,GAAWhB,CAAQ,EACjC,MAED,IAAK,IACJS,GAAcQ,GAASC,EAAM,EAAI,EAAG,CAAC,EACrC,SAED,IAAK,IACJ,OAAQC,EAAK,EAAG,CACf,IAAK,IAAI,IAAK,IACbC,EAAOC,GAAQC,GAAUZ,EAAK,EAAGQ,EAAM,CAAC,EAAG/B,EAAMC,EAAQM,CAAY,EAAGA,CAAY,GAC/E6B,EAAMvB,GAAY,CAAC,GAAK,GAAKuB,EAAMJ,EAAK,GAAK,CAAC,GAAK,IAAMK,EAAOf,CAAU,GAAKgB,EAAOhB,EAAY,GAAI,MAAM,IAAM,MAAKA,GAAc,KAC1I,MACD,QACCA,GAAc,GAChB,CACA,MAED,IAAK,KAAMR,EACVR,EAAOE,GAAO,EAAI6B,EAAOf,CAAU,EAAIN,EAExC,IAAK,KAAMF,EAAU,IAAK,IAAI,IAAK,GAClC,OAAQG,EAAW,CAElB,IAAK,GAAG,IAAK,KAAKF,EAAW,EAE7B,IAAK,IAAKN,EAAYO,GAAa,KAAIM,EAAaI,EAAQJ,EAAY,MAAO,EAAE,GAC5EV,EAAW,IAAMyB,EAAOf,CAAU,EAAIZ,GAAWI,IAAa,GAAKD,IAAa,KACnFoB,EAAOrB,EAAW,GAAK2B,GAAYjB,EAAa,IAAKpB,EAAMD,EAAQS,EAAS,EAAGH,CAAY,EAAIgC,GAAYb,EAAQJ,EAAY,IAAK,EAAE,EAAI,IAAKpB,EAAMD,EAAQS,EAAS,EAAGH,CAAY,EAAGA,CAAY,EACrM,MAED,IAAK,IAAIe,GAAc,IAEvB,QAGC,GAFAW,EAAOZ,EAAYmB,GAAQlB,EAAYtB,EAAMC,EAAQO,EAAOC,EAAQN,EAAOG,EAAQY,EAAMC,EAAQ,CAAC,EAAGC,EAAW,CAAC,EAAGV,EAAQN,CAAQ,EAAGA,CAAQ,EAE3Ia,IAAc,IACjB,GAAIR,IAAW,EACdZ,GAAMyB,EAAYtB,EAAMqB,EAAWA,EAAWF,EAAOf,EAAUM,EAAQJ,EAAQc,CAAQ,MACnF,CACJ,OAAQT,EAAQ,CAEf,IAAK,IACJ,GAAIa,EAAOF,EAAY,CAAC,IAAM,IAAK,MAEpC,IAAK,KACJ,GAAIE,EAAOF,EAAY,CAAC,IAAM,GAAI,MACnC,QACCb,EAAS,EAEV,IAAK,KAAK,IAAK,KAAK,IAAK,KAC1B,CACIA,EAAQZ,GAAMF,EAAO0B,EAAWA,EAAWnB,GAAQ+B,EAAOO,GAAQ7C,EAAO0B,EAAWA,EAAW,EAAG,EAAGlB,EAAOG,EAAQY,EAAMf,EAAOgB,EAAQ,CAAC,EAAGT,EAAQU,CAAQ,EAAGA,CAAQ,EAAGjB,EAAOiB,EAAUV,EAAQJ,EAAQJ,EAAOiB,EAAQC,CAAQ,EAClOvB,GAAMyB,EAAYD,EAAWA,EAAWA,EAAW,CAAC,EAAE,EAAGD,EAAU,EAAGd,EAAQc,CAAQ,CAC5F,CACH,CAEAZ,EAAQC,EAASG,EAAW,EAAGE,EAAWE,EAAY,EAAGE,EAAOI,EAAa,GAAIZ,EAASL,EAC1F,MAED,IAAK,IACJK,EAAS,EAAI2B,EAAOf,CAAU,EAAGV,EAAWC,EAC7C,QACC,GAAIC,EAAW,GACd,GAAIG,GAAa,IAChB,EAAEH,UACMG,GAAa,KAAOH,KAAc,GAAK2B,GAAK,GAAK,IACzD,SAEF,OAAQnB,GAAcoB,EAAKzB,CAAS,EAAGA,EAAYH,EAAU,CAE5D,IAAK,IACJE,EAAYP,EAAS,EAAI,GAAKa,GAAc,KAAM,IAClD,MAED,IAAK,IACJhB,EAAOE,GAAO,GAAK6B,EAAOf,CAAU,EAAI,GAAKN,EAAWA,EAAY,EACpE,MAED,IAAK,IAEAgB,EAAK,IAAM,KACdV,GAAcK,GAAQJ,EAAK,CAAC,GAE7BZ,EAASqB,EAAK,EAAGvB,EAASC,EAAS2B,EAAOnB,EAAOI,GAAcqB,GAAWZ,EAAM,CAAC,CAAC,EAAGd,IACrF,MAED,IAAK,IACAJ,IAAa,IAAMwB,EAAOf,CAAU,GAAK,IAC5CR,EAAW,EACd,CACF,CAED,OAAOV,CACR,CA9HgBL,EAAAF,GAAA,SA+IT,SAAS2C,GAAS7C,EAAOK,EAAMC,EAAQO,EAAOC,EAAQN,EAAOG,EAAQY,EAAMC,EAAOC,EAAUV,EAAQkC,EAAU,CAKpH,QAJIC,EAAOpC,EAAS,EAChBP,EAAOO,IAAW,EAAIN,EAAQ,CAAC,EAAE,EACjC2C,EAAOC,GAAO7C,CAAI,EAEb8C,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGF,EAAIxC,EAAO,EAAEwC,EAC1C,QAASG,EAAI,EAAGC,EAAId,EAAO3C,EAAOkD,EAAO,EAAGA,EAAOjB,GAAIqB,EAAI3C,EAAO0C,CAAC,CAAC,CAAC,EAAGK,EAAI1D,EAAOwD,EAAIL,EAAM,EAAEK,GAC1FE,EAAIC,GAAKL,EAAI,EAAI/C,EAAKiD,CAAC,EAAI,IAAMC,EAAI1B,EAAQ0B,EAAG,OAAQlD,EAAKiD,CAAC,CAAC,CAAC,KACnEhC,EAAM+B,GAAG,EAAIG,GAEhB,OAAOE,GAAK5D,EAAOK,EAAMC,EAAQQ,IAAW,EAAI+C,GAAUtC,EAAMC,EAAOC,EAAUV,EAAQkC,CAAQ,CAClG,CAXgB7C,EAAAyC,GAAA,WAoBT,SAASN,GAASvC,EAAOK,EAAMC,EAAQ2C,EAAU,CACvD,OAAOW,GAAK5D,EAAOK,EAAMC,EAAQwD,GAASf,EAAKgB,GAAK,CAAC,EAAGpB,EAAO3C,EAAO,EAAG,EAAE,EAAG,EAAGiD,CAAQ,CAC1F,CAFgB7C,EAAAmC,GAAA,WAYT,SAASK,GAAa5C,EAAOK,EAAMC,EAAQS,EAAQkC,EAAU,CACnE,OAAOW,GAAK5D,EAAOK,EAAMC,EAAQ0D,GAAarB,EAAO3C,EAAO,EAAGe,CAAM,EAAG4B,EAAO3C,EAAOe,EAAS,EAAG,EAAE,EAAGA,EAAQkC,CAAQ,CACxH,CAFgB7C,EAAAwC,GAAA,eC/LT,SAASqB,GAAWC,EAAUC,EAAU,CAG9C,QAFIC,EAAS,GAEJ,EAAI,EAAG,EAAIF,EAAS,OAAQ,IACpCE,GAAUD,EAASD,EAAS,CAAC,EAAG,EAAGA,EAAUC,CAAQ,GAAK,GAE3D,OAAOC,CACR,CAPgBC,EAAAJ,GAAA,aAgBT,SAASK,GAAWC,EAASC,EAAON,EAAUC,EAAU,CAC9D,OAAQI,EAAQ,KAAM,CACrB,KAAKE,GAAO,GAAIF,EAAQ,SAAS,OAAQ,MACzC,KAAKG,GAAQ,KAAKC,GAAW,KAAKC,GAAa,OAAOL,EAAQ,OAASA,EAAQ,QAAUA,EAAQ,MACjG,KAAKM,GAAS,MAAO,GACrB,KAAKC,GAAW,OAAOP,EAAQ,OAASA,EAAQ,MAAQ,IAAMN,GAAUM,EAAQ,SAAUJ,CAAQ,EAAI,IACtG,KAAKY,GAAS,GAAI,CAACC,EAAOT,EAAQ,MAAQA,EAAQ,MAAM,KAAK,GAAG,CAAC,EAAG,MAAO,EAC5E,CAEA,OAAOS,EAAOd,EAAWD,GAAUM,EAAQ,SAAUJ,CAAQ,CAAC,EAAII,EAAQ,OAASA,EAAQ,MAAQ,IAAML,EAAW,IAAM,EAC3H,CAVgBG,EAAAC,GAAA,aCNhB,IAAMW,GAAW,6BAQV,SAASC,GAAmBC,EAAgBC,EAAqB,CACtED,EAAI,KAAK,OAAQF,EAAQ,EACrBG,IAAgB,IAClBD,EAAI,KAAK,uBAAwBC,CAAW,CAEhD,CALgBC,EAAAH,GAAA,sBAkBT,SAASI,GACdH,EACAI,EACAC,EACAC,EACM,CACN,GAAIN,EAAI,SAAW,OAInB,IAAIK,EAAU,CACZ,IAAME,EAAS,cAAcD,CAAM,GACnCN,EAAI,KAAK,mBAAoBO,CAAM,EACnCP,EAAI,OAAO,OAAQ,cAAc,EAAE,KAAK,KAAMO,CAAM,EAAE,KAAKF,CAAQ,CACrE,CACA,GAAID,EAAW,CACb,IAAMI,EAAU,eAAeF,CAAM,GACrCN,EAAI,KAAK,kBAAmBQ,CAAO,EACnCR,EAAI,OAAO,QAAS,cAAc,EAAE,KAAK,KAAMQ,CAAO,EAAE,KAAKJ,CAAS,CACxE,EACF,CApBgBF,EAAAC,GAAA,8BC7BT,IAAMM,EAAN,MAAMC,CAAQ,CAgCX,YACCC,EACAC,EACAC,EACAC,EACAC,EACP,CALO,UAAAJ,EACA,UAAAC,EACA,QAAAC,EACA,YAAAC,EACA,cAAAC,CACN,CArDL,MAeqB,CAAAC,EAAA,gBACnB,aAAoB,SAASJ,EAAcK,EAA2C,CAAC,EAAG,CACxF,IAAMC,EAAmBC,EAAU,EAC7BR,EAAOS,EAAWR,EAAMM,CAAM,EACpCN,EAAOS,GAAeT,CAAI,EAAI;AAAA,EAC9B,GAAI,CACFU,EAAWX,CAAI,CACjB,MAAQ,CACN,IAAMY,EAASC,GAAiBb,CAAI,EACpC,GAAI,CAACY,EACH,MAAM,IAAIE,GAAoB,WAAWd,CAAI,aAAa,EAI5D,GAAM,CAAE,GAAAe,EAAI,QAAAC,CAAQ,EAAI,MAAMJ,EAAO,EACrCK,EAAgBF,EAAIC,CAAO,CAC7B,CACA,GAAM,CAAE,GAAAd,EAAI,OAAAC,EAAQ,SAAAC,EAAU,KAAAc,CAAK,EAAIP,EAAWX,CAAI,EACtD,OAAIG,EAAO,SAETA,EAAO,OAAO,GAAKD,GAErBA,EAAG,QAAQ,EACXgB,IAAOX,CAAM,EAETD,EAAS,OACXJ,EAAG,kBAAkBI,EAAS,KAAK,EAErC,MAAMH,EAAO,MAAMF,CAAI,EAChB,IAAIF,EAAQC,EAAMC,EAAMC,EAAIC,EAAQC,CAAQ,CACrD,CAUA,MAAM,OAAOW,EAAYI,EAAiB,CACxC,MAAM,KAAK,SAAS,KAAK,KAAK,KAAMJ,EAAII,EAAS,IAAI,CACvD,CAEA,WAAY,CACV,OAAO,KAAK,MACd,CAEA,SAAU,CACR,OAAO,KAAK,IACd,CACF,EClEA,IAAIC,GAAuC,CAAC,EAIrC,IAAMC,GAAkBC,EAAA,IAAM,CACnCC,GAAqB,QAASC,GAAM,CAClCA,EAAE,CACJ,CAAC,EACDD,GAAuB,CAAC,CAC1B,EAL+B,mBCCxB,IAAME,GAAkBC,EAACC,GACvBA,EAAK,QAAQ,yBAA0B,EAAE,EAAE,UAAU,EAD/B,mBCkBxB,SAASC,GAAmBC,EAAiC,CAClE,IAAMC,EAAUD,EAAK,MAAME,EAAgB,EAC3C,GAAI,CAACD,EACH,MAAO,CACL,KAAAD,EACA,SAAU,CAAC,CACb,EAGF,IAAIG,EACGC,GAAKH,EAAQ,CAAC,EAAG,CAGpB,OAAaI,EACf,CAAC,GAAK,CAAC,EAGTF,EAAS,OAAOA,GAAW,UAAY,CAAC,MAAM,QAAQA,CAAM,EAAIA,EAAS,CAAC,EAE1E,IAAMG,EAAgC,CAAC,EAGvC,OAAIH,EAAO,cACTG,EAAS,YAAcH,EAAO,YAAY,SAAS,GAEjDA,EAAO,QACTG,EAAS,MAAQH,EAAO,MAAM,SAAS,GAErCA,EAAO,SACTG,EAAS,OAASH,EAAO,QAGpB,CACL,KAAMH,EAAK,MAAMC,EAAQ,CAAC,EAAE,MAAM,EAClC,SAAAK,CACF,CACF,CApCgBC,EAAAR,GAAA,sBClBhB,IAAMS,GAAcC,EAACC,GAEjBA,EAEG,QAAQ,SAAU;AAAA,CAAI,EAEtB,QACC,kBACA,CAACC,EAAOC,EAAKC,IAAe,IAAMD,EAAMC,EAAW,QAAQ,cAAe,OAAO,EAAI,GACvF,EATc,eAadC,GAAqBL,EAACC,GAAiB,CAC3C,GAAM,CAAE,KAAAK,EAAM,SAAAC,CAAS,EAAIC,GAAmBP,CAAI,EAC5C,CAAE,YAAAQ,EAAa,MAAAC,EAAO,OAAAC,EAAS,CAAC,CAAE,EAAIJ,EAC5C,OAAIE,IAEGE,EAAO,QACVA,EAAO,MAAQ,CAAC,GAElBA,EAAO,MAAM,YAAcF,GAEtB,CAAE,MAAAC,EAAO,OAAAC,EAAQ,KAAAL,CAAK,CAC/B,EAX2B,sBAarBM,GAAoBZ,EAACC,GAAiB,CAC1C,IAAMY,EAAgBC,EAAM,WAAWb,CAAI,GAAK,CAAC,EAC3Cc,EAAiBD,EAAM,gBAAgBb,EAAM,MAAM,EACzD,OAAI,MAAM,QAAQc,CAAc,EAC9BF,EAAc,KAAOE,EAAe,KAAK,CAAC,CAAE,KAAAC,CAAK,IAAMA,IAAS,MAAM,EAC7DD,GAAgB,OAAS,SAClCF,EAAc,KAAO,IAEhB,CACL,KAAMI,GAAiBhB,CAAI,EAC3B,UAAWY,CACb,CACF,EAZ0B,qBAoBnB,SAASK,GAAkBjB,EAAc,CAC9C,IAAMkB,EAAcpB,GAAYE,CAAI,EAC9BmB,EAAoBf,GAAmBc,CAAW,EAClDE,EAAkBT,GAAkBQ,EAAkB,IAAI,EAC1DT,EAASW,GAAcF,EAAkB,OAAQC,EAAgB,SAAS,EAChF,OAAApB,EAAOsB,GAAgBF,EAAgB,IAAI,EACpC,CACL,KAAApB,EACA,MAAOmB,EAAkB,MACzB,OAAAT,CACF,CACF,CAXgBX,EAAAkB,GAAA,qBCnDT,SAASM,GAASC,EAAa,CAEpC,IAAMC,EAAY,IAAI,YAAY,EAAE,OAAOD,CAAG,EACxCE,EAAU,MAAM,KAAKD,EAAYE,GAAS,OAAO,cAAcA,CAAI,CAAC,EAAE,KAAK,EAAE,EACnF,OAAO,KAAKD,CAAO,CACrB,CALgBE,EAAAL,GAAA,YC6BhB,IAAMM,GAAiB,IACjBC,GACJ,sEAEIC,GAAuB,UACvBC,GAAqB,QAErBC,GAAgB,6BAChBC,GAAkB,+BAClBC,GAAkB,+BAIlBC,GAAe,OACfC,GAAgB,OAChBC,GAAgB,qBAChBC,GAAoB,WACpBC,GAAsB,uDACtBC,GAA2B,qDAG3BC,GAAiB,CAAC,eAAe,EACjCC,GAAiB,CAAC,mBAAmB,EAE3C,SAASC,GAAqBC,EAAc,CAC1C,IAAMC,EAAYC,GAAkBF,CAAI,EACxC,OAAUG,EAAM,EACNC,GAAaH,EAAU,QAAU,CAAC,CAAC,EACtCA,CACT,CALSI,EAAAN,GAAA,wBAmBT,eAAeO,GAAMN,EAAcO,EAA2D,CAC5FC,EAAY,EACZ,GAAI,CACF,GAAM,CAAE,KAAAC,EAAM,OAAAC,CAAO,EAAIX,GAAqBC,CAAI,EAElD,MAAO,CAAE,aADO,MAAMW,GAAmBF,CAAI,GACf,KAAM,OAAAC,CAAO,CAC7C,OAASE,EAAO,CACd,GAAIL,GAAc,eAChB,MAAO,GAET,MAAMK,CACR,CACF,CAZeP,EAAAC,GAAA,SAsBR,IAAMO,GAAqBR,EAAA,CAChCS,EACAC,EACAC,EAAuB,CAAC,IAEjB;AAAA,GAAMF,CAAQ,IAAIC,CAAO,MAAMC,EAAW,KAAK,eAAe,CAAC,iBALtC,sBAerBC,GAAkBZ,EAAA,CAC7BK,EACAQ,EAAkE,IAAI,MAC3D,CACX,IAAIC,EAAY,GAiBhB,GAZIT,EAAO,WAAa,SACtBS,GAAa;AAAA,EAAKT,EAAO,QAAQ,IAG/BA,EAAO,aAAe,SACxBS,GAAa;AAAA,iCAAoCT,EAAO,UAAU,KAEhEA,EAAO,gBAAkB,SAC3BS,GAAa;AAAA,qCAAwCT,EAAO,aAAa,KAIvEQ,aAAqB,IAAK,CAM5B,IAAME,EALaV,EAAO,YAAcA,EAAO,WAAW,WAElC,CAAC,MAAO,MAAM,EACb,CAAC,OAAQ,UAAW,UAAW,SAAU,MAAM,EAKxEQ,EAAU,QAASG,GAAkB,CAE9BC,GAAQD,EAAc,MAAM,GAC/BD,EAAY,QAASG,GAAe,CAClCJ,GAAaN,GAAmBQ,EAAc,GAAIE,EAAYF,EAAc,MAAM,CACpF,CAAC,EAGEC,GAAQD,EAAc,UAAU,IACnCF,GAAaN,GACXQ,EAAc,GACd,SACCA,GAAe,YAAc,CAAC,GAAG,IAAKG,GAAMA,EAAE,QAAQ,QAAS,MAAM,CAAC,CACzE,EAEJ,CAAC,CACH,CACA,OAAOL,CACT,EAhD+B,mBAkDlBM,GAAmBpB,EAAA,CAC9BK,EACAgB,EACAR,EACAS,IACW,CACX,IAAMC,EAAgBX,GAAgBP,EAAQQ,CAAS,EACjDW,EAAYC,GAAUJ,EAAWE,EAAelB,EAAO,cAAc,EAK3E,OAAOqB,GAAUC,GAAQ,GAAGL,CAAK,IAAIE,CAAS,GAAG,EAAGI,EAAS,CAC/D,EAbgC,oBAuBnBC,GAAiB7B,EAAA,CAC5B8B,EAAU,GACVC,EACAC,IACW,CACX,IAAIC,EAAeH,EAGnB,MAAI,CAACE,GAAsB,CAACD,IAC1BE,EAAeA,EAAa,QAC1B,yCACA,mBACF,GAGFA,EAAeC,GAAeD,CAAY,EAG1CA,EAAeA,EAAa,QAAQ,QAAS,OAAO,EAE7CA,CACT,EArB8B,kBA8BjBE,GAAgBnC,EAAA,CAAC8B,EAAU,GAAIM,IAAmC,CAC7E,IAAMC,EAASD,GAAY,SAAS,SAAS,OACzCA,EAAW,QAAQ,QAAQ,OAAS,KACpCjD,GACEmD,EAAmBC,GAAS,gBAAgBlD,EAAiB,KAAKyC,CAAO,SAAS,EACxF,MAAO,wBAAwB5C,EAAY,WAAWmD,CAAM,IAAIjD,EAAa,8CAA8CkD,CAAgB,cAAchD,EAAmB;AAAA,IAC1KC,EAAwB;AAAA,UAE5B,EAR6B,iBAuBhBiD,GAAgBxC,EAAA,CAC3ByC,EACAC,EACAC,EACAC,EACAC,IACc,CACd,IAAMC,EAAeL,EAAW,OAAO,KAAK,EAC5CK,EAAa,KAAK,KAAMH,CAAc,EAClCC,GACFE,EAAa,KAAK,QAASF,CAAQ,EAGrC,IAAMG,EAAUD,EACb,OAAO,KAAK,EACZ,KAAK,KAAMJ,CAAE,EACb,KAAK,QAAS,MAAM,EACpB,KAAK,QAAS3D,EAAa,EAC9B,OAAI8D,GACFE,EAAQ,KAAK,cAAeF,CAAQ,EAGtCE,EAAQ,OAAO,GAAG,EACXN,CACT,EAxB6B,iBAkC7B,SAASO,GAAgBC,EAAuBC,EAA6B,CAC3E,OAAOD,EACJ,OAAO,QAAQ,EACf,KAAK,KAAMC,CAAQ,EACnB,KAAK,QAAS,4BAA4B,EAC1C,KAAK,UAAW,EAAE,CACvB,CANSlD,EAAAgD,GAAA,mBAgBF,IAAMG,GAAyBnD,EAAA,CACpCoD,EACAV,EACAW,EACAH,IACG,CAEHE,EAAI,eAAeV,CAAE,GAAG,OAAO,EAG/BU,EAAI,eAAeC,CAAK,GAAG,OAAO,EAClCD,EAAI,eAAeF,CAAQ,GAAG,OAAO,CACvC,EAZsC,0BAoBhCI,GAAStD,EAAA,eACb0C,EACA/C,EACA4D,EACuB,CACvBpD,EAAY,EAEZ,IAAMP,EAAYF,GAAqBC,CAAI,EAC3CA,EAAOC,EAAU,KAEjB,IAAMS,EAAmBmD,EAAU,EACnCC,EAAI,MAAMpD,CAAM,EAGZV,EAAK,QAAUU,GAAQ,aAAe1B,MACxCgB,EAAOf,IAGT,IAAM8E,EAAa,IAAMhB,EACnBiB,EAAW,IAAMjB,EACjBkB,EAAoB,IAAMD,EAC1BE,EAAiB,IAAMnB,EACvBoB,EAA0B,IAAMD,EAEhCE,EAAqB/D,EAAA,IAAM,CAI/B,IAAMgE,GAAOC,EADcC,EAAcN,EAAoBE,CACvB,EAAE,KAAK,EACzCE,IAAQ,WAAYA,IACtBA,GAAK,OAAO,CAEhB,EAR2B,sBAUvBG,EAAYF,EAAO,MAAM,EAEvBC,EAAc7D,EAAO,gBAAkBxB,GACvCuF,EAAuB/D,EAAO,gBAAkBvB,GAEhDuF,EAAahE,EAAO,WAM1B,GAAIkD,IAAyB,OAAW,CAKtC,GAJIA,IACFA,EAAqB,UAAY,IAG/BW,EAAa,CAEf,IAAMI,EAAStB,GAAgBiB,EAAOV,CAAoB,EAAGI,CAAQ,EACrEQ,EAAOF,EAAOK,EAAO,MAAM,EAAE,CAAC,EAAG,gBAAiB,IAAI,EACtDH,EAAK,KAAK,EAAE,MAAM,OAAS,CAC7B,MACEA,EAAOF,EAAOV,CAAoB,EAEpCf,GAAc2B,EAAMzB,EAAImB,EAAgB,gBAAgBQ,CAAU,GAAIrF,EAAe,CACvF,KAAO,CASL,GALAmE,GAAuB,SAAUT,EAAImB,EAAgBF,CAAQ,EAKzDO,EAAa,CAEf,IAAMI,EAAStB,GAAgBiB,EAAO,MAAM,EAAGN,CAAQ,EACvDQ,EAAOF,EAAOK,EAAO,MAAM,EAAE,CAAC,EAAG,gBAAiB,IAAI,EACtDH,EAAK,KAAK,EAAE,MAAM,OAAS,CAC7B,MACEA,EAAOF,EAAO,MAAM,EAGtBzB,GAAc2B,EAAMzB,EAAImB,CAAc,CACxC,CAMA,IAAIU,EACAC,EAEJ,GAAI,CACFD,EAAO,MAAME,EAAQ,SAAS9E,EAAM,CAAE,MAAOC,EAAU,KAAM,CAAC,CAChE,OAASW,EAAO,CACd,GAAIF,EAAO,uBACT,MAAA0D,EAAmB,EACbxD,EAERgE,EAAO,MAAME,EAAQ,SAAS,OAAO,EACrCD,EAA4BjE,CAC9B,CAGA,IAAMG,EAAUyD,EAAK,OAAOL,CAAuB,EAAE,KAAK,EACpDY,EAAcH,EAAK,KAMnBI,EAAMjE,EAAQ,WACdkE,EAAaD,EAAI,WACjBE,EAAmBN,EAAK,SAAS,aAAa5E,EAAM4E,CAAI,EAExDO,EAAQ1D,GAAiBf,EAAQqE,EAAaG,EAAkBnB,CAAU,EAE1EqB,EAAS,SAAS,cAAc,OAAO,EAC7CA,EAAO,UAAYD,EACnBH,EAAI,aAAaI,EAAQH,CAAU,EAInC,GAAI,CACF,MAAML,EAAK,SAAS,KAAK5E,EAAM+C,EAAIsC,GAAY,QAAST,CAAI,CAC9D,OAASU,EAAG,CACV,MAAI5E,EAAO,uBACT0D,EAAmB,EAEnBmB,GAAc,KAAKvF,EAAM+C,EAAIsC,GAAY,OAAO,EAE5CC,CACR,CAGA,IAAMlC,GAAUoB,EAAK,OAAO,GAAGL,CAAuB,MAAM,EACtDqB,GAAgCZ,EAAK,GAAG,cAAc,EACtDa,GAAgCb,EAAK,GAAG,oBAAoB,EAClEc,GAAYX,EAAa3B,GAASoC,GAAWC,EAAS,EAGtDjB,EAAK,OAAO,QAAQzB,CAAE,IAAI,EAAE,UAAU,mBAAmB,EAAE,KAAK,QAASzD,EAAe,EAGxF,IAAI6C,EAAkBqC,EAAK,OAAOL,CAAuB,EAAE,KAAK,EAAE,UAKlE,GAHAL,EAAI,MAAM,6BAA8BpD,EAAO,mBAAmB,EAClEyB,EAAUD,GAAeC,EAASoC,EAAaoB,GAASjF,EAAO,mBAAmB,CAAC,EAE/E6D,EAAa,CACf,IAAMqB,EAAQpB,EAAK,OAAOL,EAA0B,MAAM,EAAE,KAAK,EACjEhC,EAAUK,GAAcL,EAASyD,CAAK,CACxC,MAAYnB,IAEVtC,EAAU0D,GAAU,SAAS1D,EAAS,CACpC,SAAUtC,GACV,SAAUC,GACV,wBAAyB,CAAE,cAAe,EAAK,CACjD,CAAC,GAKH,GAFAgG,GAAgB,EAEZjB,EACF,MAAMA,EAGR,OAAAT,EAAmB,EAEZ,CACL,YAAAW,EACA,IAAK5C,EACL,cAAeyC,EAAK,GAAG,aACzB,CACF,EAzKe,UA8Kf,SAASmB,GAAWC,EAA6B,CAAC,EAAG,CACnD,IAAMC,EAAyBC,GAAgB,CAAC,EAAGF,CAAW,EAE1DC,GAAS,YAAc,CAACA,EAAQ,gBAAgB,aAC7CA,EAAQ,iBACXA,EAAQ,eAAiB,CAAC,GAE5BA,EAAQ,eAAe,WAAaA,EAAQ,YAIpCE,GAAyBF,CAAO,EAEtCA,GAAS,OAASA,EAAQ,SAASG,GAErCH,EAAQ,eAAiBG,GAAMH,EAAQ,KAA2B,EAAE,kBAClEA,EAAQ,cACV,EACSA,IACTA,EAAQ,eAAiBG,GAAM,QAAQ,kBAAkBH,EAAQ,cAAc,GAGjF,IAAMvF,EACJ,OAAOuF,GAAY,SAAqBI,GAAcJ,CAAO,EAAcK,GAAc,EAE3FC,GAAY7F,EAAO,QAAQ,EAC3BF,EAAY,CACd,CA3BSH,EAAA0F,GAAA,cA6BT,IAAMpF,GAAqBN,EAAA,CAACL,EAAcwG,EAA2C,CAAC,IAAM,CAC1F,GAAM,CAAE,KAAA/F,CAAK,EAAIP,GAAkBF,CAAI,EACvC,OAAO8E,EAAQ,SAASrE,EAAM+F,CAAQ,CACxC,EAH2B,sBAa3B,SAASd,GACPX,EACA3B,EACAoC,EACAC,EACM,CACNgB,GAAmBrD,EAAS2B,CAAW,EACvC2B,GAA2BtD,EAASoC,EAAWC,EAAWrC,EAAQ,KAAK,IAAI,CAAC,CAC9E,CARS/C,EAAAqF,GAAA,eAaF,IAAMiB,EAAa,OAAO,OAAO,CACtC,OAAAhD,GACA,MAAArD,GACA,mBAAAK,GACA,WAAAoF,GACA,UAAqBlC,EACrB,UAAqB+C,GACrB,cAAyBN,GACzB,iBAA4BO,GAC5B,MAAOxG,EAAA,IAAM,CACDF,EAAM,CAClB,EAFO,SAGP,YAAaE,EAAA,IAAM,CACPF,EAAgB2G,EAAa,CACzC,EAFa,eAGb,cAAyBA,EAC3B,CAAC,EAEDP,GAAsB1C,EAAU,EAAE,QAAQ,EAChC1D,EAAgB0D,EAAU,CAAC,EC9erC,IAAMkD,GAAcC,EAAA,CAACC,EAAgBC,EAAyBC,IAAoC,CAChGC,EAAI,KAAKH,CAAK,EACVI,GAAgBJ,CAAK,GAGnBE,GACFA,EAAWF,EAAM,IAAKA,EAAM,IAAI,EAElCC,EAAO,KAAK,CAAE,GAAGD,EAAO,QAASA,EAAM,IAAK,MAAAA,CAAM,CAAC,IAG/CE,GACFA,EAAWF,CAAK,EAEdA,aAAiB,OACnBC,EAAO,KAAK,CACV,IAAKD,EAAM,QACX,QAASA,EAAM,QACf,KAAMA,EAAM,KACZ,MAAAA,CACF,CAAC,EAGP,EAvBoB,eA6CdK,GAAMN,EAAA,eACVO,EAAsB,CACpB,cAAe,UACjB,EACA,CACA,GAAI,CACF,MAAMC,GAAgBD,CAAO,CAC/B,OAAS,EAAG,CAOV,GANIF,GAAgB,CAAC,GACnBD,EAAI,MAAM,EAAE,GAAG,EAEbK,EAAQ,YACVA,EAAQ,WAAW,CAAW,EAE5B,CAACF,EAAQ,eACX,MAAAH,EAAI,MAAM,wDAAwD,EAC5D,CAEV,CACF,EAnBY,OAqBNI,GAAkBR,EAAA,eACtB,CAAE,mBAAAU,EAAoB,cAAAC,EAAe,MAAAC,CAAM,EAAwC,CACjF,cAAe,UACjB,EACA,CACA,IAAMC,EAAOC,EAAW,UAAU,EAElCV,EAAI,MAAM,GAAIM,EAA6B,GAAR,KAAU,yBAAyB,EAEtE,IAAIK,EACJ,GAAIH,EACFG,EAAiBH,UACRD,EACTI,EAAiB,SAAS,iBAAiBJ,CAAa,MAExD,OAAM,IAAI,MAAM,4CAA4C,EAG9DP,EAAI,MAAM,SAASW,EAAe,MAAM,WAAW,EAC/CF,GAAM,cAAgB,SACxBT,EAAI,MAAM,kBAAoBS,GAAM,WAAW,EAC/CC,EAAW,iBAAiB,CAAE,YAAaD,GAAM,WAAY,CAAC,GAIhE,IAAMG,EAAc,IAAIC,EAAM,gBAAgBJ,EAAK,iBAAkBA,EAAK,mBAAmB,EAEzFK,EACEhB,EAA0B,CAAC,EAIjC,QAAWiB,KAAW,MAAM,KAAKJ,CAAc,EAAG,CAChDX,EAAI,KAAK,sBAAwBe,EAAQ,EAAE,EAE3C,GAAIA,EAAQ,aAAa,gBAAgB,EACvC,SAEFA,EAAQ,aAAa,iBAAkB,MAAM,EAE7C,IAAMC,EAAK,WAAWJ,EAAY,KAAK,CAAC,GAGxCE,EAAMC,EAAQ,UAGdD,EAAMG,GAAOJ,EAAM,aAAaC,CAAG,CAAC,EACjC,KAAK,EACL,QAAQ,eAAgB,OAAO,EAElC,IAAMI,EAAOL,EAAM,WAAWC,CAAG,EAC7BI,GACFlB,EAAI,MAAM,0BAA2BkB,CAAI,EAE3C,GAAI,CACF,GAAM,CAAE,IAAAC,EAAK,cAAAC,CAAc,EAAI,MAAMC,GAAOL,EAAIF,EAAKC,CAAO,EAC5DA,EAAQ,UAAYI,EAChBb,GACF,MAAMA,EAAmBU,CAAE,EAEzBI,GACFA,EAAcL,CAAO,CAEzB,OAASlB,EAAO,CACdF,GAAYE,EAAOC,EAAQO,EAAQ,UAAU,CAC/C,CACF,CACA,GAAIP,EAAO,OAAS,EAElB,MAAMA,EAAO,CAAC,CAElB,EAvEwB,mBA+ElBwB,GAAa1B,EAAA,SAAU2B,EAAuB,CAClDb,EAAW,WAAWa,CAAM,CAC9B,EAFmB,cAkBbL,GAAOtB,EAAA,eACX2B,EACAf,EACAgB,EACA,CACAxB,EAAI,KAAK,qDAAqD,EAC1DuB,GACFD,GAAWC,CAAM,EAEnB,IAAME,EAAyB,CAAE,mBAAoBD,EAAU,cAAe,UAAW,EACrF,OAAOhB,GAAU,SACnBiB,EAAW,cAAgBjB,EAClBA,IACLA,aAAiB,YACnBiB,EAAW,MAAQ,CAACjB,CAAK,EAEzBiB,EAAW,MAAQjB,GAGvB,MAAMN,GAAIuB,CAAU,CACtB,EApBa,QA2BPC,GAA2B9B,EAAA,MAC/B+B,EACA,CACE,SAAAC,EAAW,EACb,EAEI,CAAC,IACF,CACHC,EAAY,EACZC,EAA2B,GAAGH,CAAQ,EAClCC,IAAa,IACf,MAAMG,GAAuB,CAEjC,EAbiC,4BAoB3BC,GAAgBpC,EAAA,UAAY,CAChC,GAAIS,EAAQ,YAAa,CACvB,GAAM,CAAE,YAAA4B,CAAY,EAAIvB,EAAW,UAAU,EACzCuB,GACF5B,EAAQ,IAAI,EAAE,MAAO6B,GAAQlC,EAAI,MAAM,+BAAgCkC,CAAG,CAAC,CAE/E,CACF,EAPsB,iBAStB,GAAI,OAAO,SAAa,IAAa,CAInC,OAAO,iBAAiB,OAAQF,GAAe,EAAK,CACtD,CAgBA,IAAMG,GAAuBvC,EAAA,SAAUwC,EAAkD,CACvF/B,EAAQ,WAAa+B,CACvB,EAF6B,wBAIvBC,GAA6C,CAAC,EAChDC,GAAwB,GACtBC,GAAe3C,EAAA,SAAY,CAC/B,GAAI,CAAA0C,GAIJ,KADAA,GAAwB,GACjBD,GAAe,OAAS,GAAG,CAChC,IAAMG,EAAIH,GAAe,MAAM,EAC/B,GAAIG,EACF,GAAI,CACF,MAAMA,EAAE,CACV,OAAS,EAAG,CACVxC,EAAI,MAAM,wBAAyB,CAAC,CACtC,CAEJ,CACAsC,GAAwB,GAC1B,EAhBqB,gBAqCfG,GAAiC7C,EAAA,MAAO8C,EAAMC,IAC3C,IAAI,QAAQ,CAACC,EAASC,IAAW,CAGtC,IAAMC,EAAclD,EAAA,IAClB,IAAI,QAAQ,CAACmD,EAAKC,IAAQ,CACxBtC,EAAW,MAAMgC,EAAMC,CAAY,EAAE,KAClCM,GAAM,CAELF,EAAIE,CAAC,EAELL,EAAQK,CAAC,CACX,EACCC,GAAM,CACLlD,EAAI,MAAM,gBAAiBkD,CAAC,EAC5B7C,EAAQ,aAAa6C,CAAC,EACtBF,EAAIE,CAAC,EACLL,EAAOK,CAAC,CACV,CACF,CACF,CAAC,EAhBiB,eAiBpBb,GAAe,KAAKS,CAAW,EAC/BP,GAAa,EAAE,MAAMM,CAAM,CAC7B,CAAC,EAvBoC,SAiDjCxB,GAAmCzB,EAAA,CAACoB,EAAI0B,EAAMS,IAC3C,IAAI,QAAQ,CAACP,EAASC,IAAW,CAGtC,IAAMC,EAAclD,EAAA,IAClB,IAAI,QAAQ,CAACmD,EAAKC,IAAQ,CACxBtC,EAAW,OAAOM,EAAI0B,EAAMS,CAAS,EAAE,KACpCF,GAAM,CAELF,EAAIE,CAAC,EAELL,EAAQK,CAAC,CACX,EACCC,GAAM,CACLlD,EAAI,MAAM,gBAAiBkD,CAAC,EAC5B7C,EAAQ,aAAa6C,CAAC,EACtBF,EAAIE,CAAC,EACLL,EAAOK,CAAC,CACV,CACF,CACF,CAAC,EAhBiB,eAiBpBb,GAAe,KAAKS,CAAW,EAC/BP,GAAa,EAAE,MAAMM,CAAM,CAC7B,CAAC,EAvBsC,UAkDnCxC,EAAmB,CACvB,YAAa,GACb,WAAAK,EACA,MAAA+B,GACA,OAAApB,GACA,KAAAH,GACA,IAAAhB,GACA,yBAAAwB,GACA,sBAAA0B,GACA,WAAA9B,GACA,WAAY,OACZ,cAAAU,GACA,qBAAAG,GACA,WAAAkB,EACA,kBAAAC,EACF,EAEOC,GAAQlD", "names": ["id", "detector", "__name", "txt", "loader", "diagram", "plugin", "c4Detector_default", "id", "detector", "__name", "txt", "config", "loader", "diagram", "plugin", "flowDetector_default", "id", "detector", "__name", "txt", "config", "loader", "diagram", "plugin", "flowDetector_v2_default", "id", "detector", "__name", "txt", "loader", "diagram", "plugin", "erDetector_default", "id", "detector", "__name", "txt", "loader", "diagram", "plugin", "gitGraphDetector_default", "id", "detector", "__name", "txt", "loader", "diagram", "plugin", "ganttDetector_default", "id", "detector", "__name", "txt", "loader", "diagram", "info", "id", "detector", "__name", "txt", "loader", "diagram", "pie", "id", "detector", "__name", "txt", "loader", "diagram", "plugin", "quadrantDetector_default", "id", "detector", "__name", "txt", "loader", "diagram", "plugin", "xychartDetector_default", "id", "detector", "__name", "txt", "loader", "diagram", "plugin", "requirementDetector_default", "id", "detector", "__name", "txt", "loader", "diagram", "plugin", "sequenceDetector_default", "id", "detector", "__name", "txt", "config", "loader", "diagram", "plugin", "classDetector_default", "id", "detector", "__name", "txt", "config", "loader", "diagram", "plugin", "classDetector_V2_default", "id", "detector", "__name", "txt", "config", "loader", "diagram", "plugin", "stateDetector_default", "id", "detector", "__name", "txt", "config", "loader", "diagram", "plugin", "stateDetector_V2_default", "id", "detector", "__name", "txt", "loader", "diagram", "plugin", "journeyDetector_default", "draw", "__name", "_text", "id", "version", "log", "svg", "selectSvgElement", "g", "configureSvgSize", "renderer", "errorRenderer_default", "diagram", "renderer", "__name", "errorDiagram_default", "id", "detector", "__name", "txt", "config", "loader", "diagram", "plugin", "detector_default", "id", "detector", "__name", "txt", "loader", "diagram", "plugin", "detector_default", "id", "detector", "__name", "txt", "loader", "diagram", "plugin", "detector_default", "id", "detector", "__name", "txt", "loader", "diagram", "plugin", "detector_default", "id", "detector", "__name", "txt", "loader", "diagram", "plugin", "sankeyDetector_default", "id", "detector", "__name", "txt", "loader", "diagram", "packet", "id", "detector", "__name", "txt", "loader", "diagram", "radar", "id", "detector", "__name", "txt", "loader", "diagram", "plugin", "blockDetector_default", "id", "detector", "__name", "txt", "loader", "diagram", "architecture", "architectureDetector_default", "hasLoadedDiagrams", "addDiagrams", "__name", "registerDiagram", "errorDiagram_default", "text", "registerLazyLoadedDiagrams", "c4Detector_default", "detector_default", "classDetector_V2_default", "classDetector_default", "erDetector_default", "ganttDetector_default", "info", "pie", "requirementDetector_default", "sequenceDetector_default", "flowDetector_v2_default", "flowDetector_default", "gitGraphDetector_default", "stateDetector_V2_default", "stateDetector_default", "journeyDetector_default", "quadrantDetector_default", "sankeyDetector_default", "packet", "xychartDetector_default", "blockDetector_default", "architectureDetector_default", "radar", "loadRegisteredDiagrams", "__name", "log", "failed", "detectors", "key", "detector", "loader", "getDiagram", "diagram", "id", "registerDiagram", "err", "result", "res", "COMMENT", "RULESET", "DECLARATION", "IMPORT", "NAMESPACE", "KEYFRAMES", "LAYER", "abs", "from", "trim", "value", "__name", "replace", "value", "pattern", "replacement", "__name", "indexof", "search", "position", "charat", "index", "substr", "begin", "end", "strlen", "sizeof", "append", "array", "line", "column", "length", "position", "character", "characters", "node", "value", "root", "parent", "type", "props", "children", "siblings", "__name", "char", "character", "__name", "prev", "position", "charat", "characters", "column", "line", "next", "length", "peek", "caret", "slice", "begin", "end", "substr", "token", "type", "alloc", "value", "strlen", "dealloc", "delimit", "trim", "delimiter", "whitespace", "type", "character", "peek", "next", "token", "__name", "escaping", "index", "count", "next", "character", "slice", "caret", "peek", "__name", "delimiter", "type", "position", "commenter", "from", "identifier", "token", "compile", "value", "dealloc", "parse", "alloc", "__name", "root", "parent", "rule", "rules", "rulesets", "pseudo", "points", "declarations", "index", "offset", "length", "at<PERSON>le", "property", "previous", "variable", "scanning", "ampersand", "character", "type", "props", "children", "reference", "characters", "next", "charat", "indexof", "replace", "delimit", "abs", "whitespace", "escaping", "caret", "peek", "append", "comment", "commenter", "token", "strlen", "substr", "declaration", "ruleset", "prev", "from", "identifier", "siblings", "post", "size", "sizeof", "i", "j", "k", "x", "y", "z", "trim", "node", "RULESET", "COMMENT", "char", "DECLARATION", "serialize", "children", "callback", "output", "__name", "stringify", "element", "index", "LAYER", "IMPORT", "NAMESPACE", "DECLARATION", "COMMENT", "KEYFRAMES", "RULESET", "strlen", "SVG_ROLE", "setA11yDiagramInfo", "svg", "diagramType", "__name", "addSVGa11yTitleDescription", "a11yTitle", "a11yDesc", "baseId", "descId", "titleId", "Diagram", "_Diagram", "type", "text", "db", "parser", "renderer", "__name", "metadata", "config", "getConfig", "detectType", "encodeEntities", "getDiagram", "loader", "getDiagramLoader", "UnknownDiagramError", "id", "diagram", "registerDiagram", "init", "version", "interactionFunctions", "attachFunctions", "__name", "interactionFunctions", "f", "cleanupComments", "__name", "text", "extractFrontMatter", "text", "matches", "frontMatterRegex", "parsed", "load", "JSON_SCHEMA", "metadata", "__name", "cleanupText", "__name", "code", "match", "tag", "attributes", "processFrontmatter", "text", "metadata", "extractFrontMatter", "displayMode", "title", "config", "processDirectives", "initDirective", "utils_default", "wrapDirectives", "type", "removeDirectives", "preprocessDiagram", "cleanedCode", "frontMatterResult", "directiveResult", "cleanAndMerge", "cleanupComments", "toBase64", "str", "utf8Bytes", "utf8Str", "byte", "__name", "MAX_TEXTLENGTH", "MAX_TEXTLENGTH_EXCEEDED_MSG", "SECURITY_LVL_SANDBOX", "SECURITY_LVL_LOOSE", "XMLNS_SVG_STD", "XMLNS_XLINK_STD", "XMLNS_XHTML_STD", "IFRAME_WIDTH", "IFRAME_HEIGHT", "IFRAME_STYLES", "IFRAME_BODY_STYLE", "IFRAME_SANDBOX_OPTS", "IFRAME_NOT_SUPPORTED_MSG", "DOMPURIFY_TAGS", "DOMPURIFY_ATTR", "processAndSetConfigs", "text", "processed", "preprocessDiagram", "reset", "addDirective", "__name", "parse", "parseOptions", "addDiagrams", "code", "config", "getDiagramFromText", "error", "cssImportantStyles", "cssClass", "element", "cssClasses", "createCssStyles", "classDefs", "cssStyles", "cssElements", "styleClassDef", "isEmpty_default", "cssElement", "s", "createUserStyles", "graphType", "svgId", "userCSSstyles", "allStyles", "styles_default", "serialize", "compile", "stringify", "cleanUpSvgCode", "svgCode", "inSandboxMode", "useArrowMarkerUrls", "cleanedUpSvg", "decodeEntities", "putIntoIFrame", "svgElement", "height", "base64encodedSrc", "toBase64", "appendDivSvgG", "parentRoot", "id", "enclosingDivId", "divStyle", "svgXlink", "enclosingDiv", "svgNode", "sandboxedIframe", "parentNode", "iFrameId", "removeExistingElements", "doc", "divId", "render", "svgContainingElement", "getConfig", "log", "idSelector", "iFrameID", "iFrameID_selector", "enclosingDivID", "enclosingDivID_selector", "removeTempElements", "node", "select_default", "isSandboxed", "root", "isLooseSecurityLevel", "fontFamily", "iframe", "diag", "parseEncounteredException", "Diagram", "diagramType", "svg", "<PERSON><PERSON><PERSON><PERSON>", "diagramClassDefs", "rules", "style1", "package_default", "e", "errorRenderer_default", "a11yTitle", "a11yDescr", "addA11yInfo", "evaluate", "svgEl", "purify", "attachFunctions", "initialize", "userOptions", "options", "assignWithDepth_default", "saveConfigFromInitialize", "themes_default", "setSiteConfig", "getSiteConfig", "setLogLevel", "metadata", "setA11yDiagramInfo", "addSVGa11yTitleDescription", "mermaidAPI", "setConfig", "updateSiteConfig", "defaultConfig", "handleError", "__name", "error", "errors", "parseError", "log", "isDetailedError", "run", "options", "runThrowsErrors", "mermaid", "postRender<PERSON>allback", "querySelector", "nodes", "conf", "mermaidAPI", "nodesToProcess", "idGenerator", "utils_default", "txt", "element", "id", "dedent", "init", "svg", "bindFunctions", "render", "initialize", "config", "callback", "runOptions", "registerExternalDiagrams", "diagrams", "lazyLoad", "addDiagrams", "registerLazyLoadedDiagrams", "loadRegisteredDiagrams", "contentLoaded", "startOnLoad", "err", "setParseError<PERSON>andler", "parseE<PERSON><PERSON><PERSON><PERSON><PERSON>", "executionQueue", "execution<PERSON><PERSON><PERSON><PERSON>unning", "executeQueue", "f", "parse", "text", "parseOptions", "resolve", "reject", "performCall", "res", "rej", "r", "e", "container", "registerLayoutLoaders", "detectType", "registerIconPacks", "mermaid_default"]}