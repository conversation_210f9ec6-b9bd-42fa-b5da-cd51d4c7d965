{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/twig/twig.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/twig/twig.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n  comments: {\n    blockComment: [\"{#\", \"#}\"]\n  },\n  brackets: [\n    [\"{#\", \"#}\"],\n    [\"{%\", \"%}\"],\n    [\"{{\", \"}}\"],\n    [\"(\", \")\"],\n    [\"[\", \"]\"],\n    // HTML\n    [\"<!--\", \"-->\"],\n    [\"<\", \">\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{# \", close: \" #}\" },\n    { open: \"{% \", close: \" %}\" },\n    { open: \"{{ \", close: \" }}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    // HTML\n    { open: \"<\", close: \">\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  ignoreCase: true,\n  keywords: [\n    // (opening) tags\n    \"apply\",\n    \"autoescape\",\n    \"block\",\n    \"deprecated\",\n    \"do\",\n    \"embed\",\n    \"extends\",\n    \"flush\",\n    \"for\",\n    \"from\",\n    \"if\",\n    \"import\",\n    \"include\",\n    \"macro\",\n    \"sandbox\",\n    \"set\",\n    \"use\",\n    \"verbatim\",\n    \"with\",\n    // closing tags\n    \"endapply\",\n    \"endautoescape\",\n    \"endblock\",\n    \"endembed\",\n    \"endfor\",\n    \"endif\",\n    \"endmacro\",\n    \"endsandbox\",\n    \"endset\",\n    \"endwith\",\n    // literals\n    \"true\",\n    \"false\"\n  ],\n  tokenizer: {\n    root: [\n      // whitespace\n      [/\\s+/],\n      // Twig Tag Delimiters\n      [/{#/, \"comment.twig\", \"@commentState\"],\n      [/{%[-~]?/, \"delimiter.twig\", \"@blockState\"],\n      [/{{[-~]?/, \"delimiter.twig\", \"@variableState\"],\n      // HTML\n      [/<!DOCTYPE/, \"metatag.html\", \"@doctype\"],\n      [/<!--/, \"comment.html\", \"@comment\"],\n      [/(<)((?:[\\w\\-]+:)?[\\w\\-]+)(\\s*)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"\", \"delimiter.html\"]],\n      [/(<)(script)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@script\" }]],\n      [/(<)(style)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@style\" }]],\n      [/(<)((?:[\\w\\-]+:)?[\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)((?:[\\w\\-]+:)?[\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/[^<{]+/]\n      // text\n    ],\n    /**\n     * Comment Tag Handling\n     */\n    commentState: [\n      [/#}/, \"comment.twig\", \"@pop\"],\n      [/./, \"comment.twig\"]\n    ],\n    /**\n     * Block Tag Handling\n     */\n    blockState: [\n      [/[-~]?%}/, \"delimiter.twig\", \"@pop\"],\n      // whitespace\n      [/\\s+/],\n      // verbatim\n      // Unlike other blocks, verbatim ehas its own state\n      // transition to ensure we mark its contents as strings.\n      [\n        /(verbatim)(\\s*)([-~]?%})/,\n        [\"keyword.twig\", \"\", { token: \"delimiter.twig\", next: \"@rawDataState\" }]\n      ],\n      { include: \"expression\" }\n    ],\n    rawDataState: [\n      // endverbatim\n      [\n        /({%[-~]?)(\\s*)(endverbatim)(\\s*)([-~]?%})/,\n        [\"delimiter.twig\", \"\", \"keyword.twig\", \"\", { token: \"delimiter.twig\", next: \"@popall\" }]\n      ],\n      [/./, \"string.twig\"]\n    ],\n    /**\n     * Variable Tag Handling\n     */\n    variableState: [[/[-~]?}}/, \"delimiter.twig\", \"@pop\"], { include: \"expression\" }],\n    stringState: [\n      // closing double quoted string\n      [/\"/, \"string.twig\", \"@pop\"],\n      // interpolation start\n      [/#{\\s*/, \"string.twig\", \"@interpolationState\"],\n      // string part\n      [/[^#\"\\\\]*(?:(?:\\\\.|#(?!\\{))[^#\"\\\\]*)*/, \"string.twig\"]\n    ],\n    interpolationState: [\n      // interpolation end\n      [/}/, \"string.twig\", \"@pop\"],\n      { include: \"expression\" }\n    ],\n    /**\n     * Expression Handling\n     */\n    expression: [\n      // whitespace\n      [/\\s+/],\n      // operators - math\n      [/\\+|-|\\/{1,2}|%|\\*{1,2}/, \"operators.twig\"],\n      // operators - logic\n      [/(and|or|not|b-and|b-xor|b-or)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - comparison (symbols)\n      [/==|!=|<|>|>=|<=/, \"operators.twig\"],\n      // operators - comparison (words)\n      [/(starts with|ends with|matches)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - containment\n      [/(in)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - test\n      [/(is)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - misc\n      [/\\||~|:|\\.{1,2}|\\?{1,2}/, \"operators.twig\"],\n      // names\n      [\n        /[^\\W\\d][\\w]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword.twig\",\n            \"@default\": \"variable.twig\"\n          }\n        }\n      ],\n      // numbers\n      [/\\d+(\\.\\d+)?/, \"number.twig\"],\n      // punctuation\n      [/\\(|\\)|\\[|\\]|{|}|,/, \"delimiter.twig\"],\n      // strings\n      [/\"([^#\"\\\\]*(?:\\\\.[^#\"\\\\]*)*)\"|\\'([^\\'\\\\]*(?:\\\\.[^\\'\\\\]*)*)\\'/, \"string.twig\"],\n      // opening double quoted string\n      [/\"/, \"string.twig\", \"@stringState\"],\n      // misc syntactic constructs\n      // These are not operators per se, but for the purposes of lexical analysis we\n      // can treat them as such.\n      // arrow functions\n      [/=>/, \"operators.twig\"],\n      // assignment\n      [/=/, \"operators.twig\"]\n    ],\n    /**\n     * HTML\n     */\n    doctype: [\n      [/[^>]+/, \"metatag.content.html\"],\n      [/>/, \"metatag.html\", \"@pop\"]\n    ],\n    comment: [\n      [/-->/, \"comment.html\", \"@pop\"],\n      [/[^-]+/, \"comment.content.html\"],\n      [/./, \"comment.content.html\"]\n    ],\n    otherTag: [\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [/[ \\t\\r\\n]+/]\n      // whitespace\n    ],\n    // -- BEGIN <script> tags handling\n    // After <script\n    script: [\n      [/type/, \"attribute.name.html\", \"@scriptAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(script\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <script ... type\n    scriptAfterType: [\n      [/=/, \"delimiter.html\", \"@scriptAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type =\n    scriptAfterTypeEquals: [\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type = $S2\n    scriptWithCustomType: [\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptEmbedded: [\n      [/<\\/script/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/[^<]+/, \"\"]\n    ],\n    // -- END <script> tags handling\n    // -- BEGIN <style> tags handling\n    // After <style\n    style: [\n      [/type/, \"attribute.name.html\", \"@styleAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(style\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <style ... type\n    styleAfterType: [\n      [/=/, \"delimiter.html\", \"@styleAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type =\n    styleAfterTypeEquals: [\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type = $S2\n    styleWithCustomType: [\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleEmbedded: [\n      [/<\\/style/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/[^<]+/, \"\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,aAAa;AAAA,EACb,UAAU;AAAA,IACR,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,MAAM,IAAI;AAAA,IACX,CAAC,MAAM,IAAI;AAAA,IACX,CAAC,MAAM,IAAI;AAAA,IACX,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA;AAAA,IAET,CAAC,QAAQ,KAAK;AAAA,IACd,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,IAC5B,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,IAC5B,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,IAC5B,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA;AAAA,IAExB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA;AAAA,IAER;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA;AAAA,MAEJ,CAAC,KAAK;AAAA;AAAA,MAEN,CAAC,MAAM,gBAAgB,eAAe;AAAA,MACtC,CAAC,WAAW,kBAAkB,aAAa;AAAA,MAC3C,CAAC,WAAW,kBAAkB,gBAAgB;AAAA;AAAA,MAE9C,CAAC,aAAa,gBAAgB,UAAU;AAAA,MACxC,CAAC,QAAQ,gBAAgB,UAAU;AAAA,MACnC,CAAC,uCAAuC,CAAC,kBAAkB,YAAY,IAAI,gBAAgB,CAAC;AAAA,MAC5F,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,UAAU,CAAC,CAAC;AAAA,MAC1E,CAAC,cAAc,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,SAAS,CAAC,CAAC;AAAA,MACxE,CAAC,6BAA6B,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,MAC1F,CAAC,+BAA+B,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,MAC5F,CAAC,KAAK,gBAAgB;AAAA,MACtB,CAAC,QAAQ;AAAA;AAAA,IAEX;AAAA;AAAA;AAAA;AAAA,IAIA,cAAc;AAAA,MACZ,CAAC,MAAM,gBAAgB,MAAM;AAAA,MAC7B,CAAC,KAAK,cAAc;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA,IAIA,YAAY;AAAA,MACV,CAAC,WAAW,kBAAkB,MAAM;AAAA;AAAA,MAEpC,CAAC,KAAK;AAAA;AAAA;AAAA;AAAA,MAIN;AAAA,QACE;AAAA,QACA,CAAC,gBAAgB,IAAI,EAAE,OAAO,kBAAkB,MAAM,gBAAgB,CAAC;AAAA,MACzE;AAAA,MACA,EAAE,SAAS,aAAa;AAAA,IAC1B;AAAA,IACA,cAAc;AAAA;AAAA,MAEZ;AAAA,QACE;AAAA,QACA,CAAC,kBAAkB,IAAI,gBAAgB,IAAI,EAAE,OAAO,kBAAkB,MAAM,UAAU,CAAC;AAAA,MACzF;AAAA,MACA,CAAC,KAAK,aAAa;AAAA,IACrB;AAAA;AAAA;AAAA;AAAA,IAIA,eAAe,CAAC,CAAC,WAAW,kBAAkB,MAAM,GAAG,EAAE,SAAS,aAAa,CAAC;AAAA,IAChF,aAAa;AAAA;AAAA,MAEX,CAAC,KAAK,eAAe,MAAM;AAAA;AAAA,MAE3B,CAAC,SAAS,eAAe,qBAAqB;AAAA;AAAA,MAE9C,CAAC,wCAAwC,aAAa;AAAA,IACxD;AAAA,IACA,oBAAoB;AAAA;AAAA,MAElB,CAAC,KAAK,eAAe,MAAM;AAAA,MAC3B,EAAE,SAAS,aAAa;AAAA,IAC1B;AAAA;AAAA;AAAA;AAAA,IAIA,YAAY;AAAA;AAAA,MAEV,CAAC,KAAK;AAAA;AAAA,MAEN,CAAC,0BAA0B,gBAAgB;AAAA;AAAA,MAE3C,CAAC,sCAAsC,CAAC,kBAAkB,EAAE,CAAC;AAAA;AAAA,MAE7D,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,MAEpC,CAAC,wCAAwC,CAAC,kBAAkB,EAAE,CAAC;AAAA;AAAA,MAE/D,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;AAAA;AAAA,MAEpC,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;AAAA;AAAA,MAEpC,CAAC,0BAA0B,gBAAgB;AAAA;AAAA,MAE3C;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,eAAe,aAAa;AAAA;AAAA,MAE7B,CAAC,qBAAqB,gBAAgB;AAAA;AAAA,MAEtC,CAAC,+DAA+D,aAAa;AAAA;AAAA,MAE7E,CAAC,KAAK,eAAe,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnC,CAAC,MAAM,gBAAgB;AAAA;AAAA,MAEvB,CAAC,KAAK,gBAAgB;AAAA,IACxB;AAAA;AAAA;AAAA;AAAA,IAIA,SAAS;AAAA,MACP,CAAC,SAAS,sBAAsB;AAAA,MAChC,CAAC,KAAK,gBAAgB,MAAM;AAAA,IAC9B;AAAA,IACA,SAAS;AAAA,MACP,CAAC,OAAO,gBAAgB,MAAM;AAAA,MAC9B,CAAC,SAAS,sBAAsB;AAAA,MAChC,CAAC,KAAK,sBAAsB;AAAA,IAC9B;AAAA,IACA,UAAU;AAAA,MACR,CAAC,QAAQ,kBAAkB,MAAM;AAAA,MACjC,CAAC,aAAa,sBAAsB;AAAA,MACpC,CAAC,aAAa,sBAAsB;AAAA,MACpC,CAAC,WAAW,qBAAqB;AAAA,MACjC,CAAC,KAAK,gBAAgB;AAAA,MACtB,CAAC,YAAY;AAAA;AAAA,IAEf;AAAA;AAAA;AAAA,IAGA,QAAQ;AAAA,MACN,CAAC,QAAQ,uBAAuB,kBAAkB;AAAA,MAClD,CAAC,aAAa,sBAAsB;AAAA,MACpC,CAAC,aAAa,sBAAsB;AAAA,MACpC,CAAC,WAAW,qBAAqB;AAAA,MACjC,CAAC,KAAK,gBAAgB;AAAA,MACtB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,YAAY;AAAA;AAAA,MAEb;AAAA,QACE;AAAA,QACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,MAC1E;AAAA,IACF;AAAA;AAAA,IAEA,iBAAiB;AAAA,MACf,CAAC,KAAK,kBAAkB,wBAAwB;AAAA,MAChD;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,YAAY;AAAA;AAAA,MAEb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACvD;AAAA;AAAA,IAEA,uBAAuB;AAAA,MACrB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,YAAY;AAAA;AAAA,MAEb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACvD;AAAA;AAAA,IAEA,sBAAsB;AAAA,MACpB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,aAAa,sBAAsB;AAAA,MACpC,CAAC,aAAa,sBAAsB;AAAA,MACpC,CAAC,WAAW,qBAAqB;AAAA,MACjC,CAAC,KAAK,gBAAgB;AAAA,MACtB,CAAC,YAAY;AAAA;AAAA,MAEb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACvD;AAAA,IACA,gBAAgB;AAAA,MACd,CAAC,aAAa,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,MACvE,CAAC,SAAS,EAAE;AAAA,IACd;AAAA;AAAA;AAAA;AAAA,IAIA,OAAO;AAAA,MACL,CAAC,QAAQ,uBAAuB,iBAAiB;AAAA,MACjD,CAAC,aAAa,sBAAsB;AAAA,MACpC,CAAC,aAAa,sBAAsB;AAAA,MACpC,CAAC,WAAW,qBAAqB;AAAA,MACjC,CAAC,KAAK,gBAAgB;AAAA,MACtB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,YAAY;AAAA;AAAA,MAEb;AAAA,QACE;AAAA,QACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,MAC1E;AAAA,IACF;AAAA;AAAA,IAEA,gBAAgB;AAAA,MACd,CAAC,KAAK,kBAAkB,uBAAuB;AAAA,MAC/C;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,YAAY;AAAA;AAAA,MAEb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACtD;AAAA;AAAA,IAEA,sBAAsB;AAAA,MACpB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,YAAY;AAAA;AAAA,MAEb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACtD;AAAA;AAAA,IAEA,qBAAqB;AAAA,MACnB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,CAAC,aAAa,sBAAsB;AAAA,MACpC,CAAC,aAAa,sBAAsB;AAAA,MACpC,CAAC,WAAW,qBAAqB;AAAA,MACjC,CAAC,KAAK,gBAAgB;AAAA,MACtB,CAAC,YAAY;AAAA;AAAA,MAEb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACtD;AAAA,IACA,eAAe;AAAA,MACb,CAAC,YAAY,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,MACtE,CAAC,SAAS,EAAE;AAAA,IACd;AAAA,EACF;AACF;", "names": []}