# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-emit"
description = "Enables the emit command without any pre-configured scope."
commands.allow = ["emit"]

[[permission]]
identifier = "deny-emit"
description = "Denies the emit command without any pre-configured scope."
commands.deny = ["emit"]
