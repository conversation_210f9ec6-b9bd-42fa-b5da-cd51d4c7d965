{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/chunk-RZ5BOZE2.mjs"], "sourcesContent": ["import {\n  __name,\n  configureSvgSize,\n  log\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/insertElementsForSize.js\nimport { select } from \"d3\";\nvar getDiagramElement = /* @__PURE__ */ __name((id, securityLevel) => {\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  return svg;\n}, \"getDiagramElement\");\n\n// src/rendering-util/setupViewPortForSVG.ts\nvar setupViewPortForSVG = /* @__PURE__ */ __name((svg, padding, cssDiagram, useMaxWidth) => {\n  svg.attr(\"class\", cssDiagram);\n  const { width, height, x, y } = calculateDimensionsWithPadding(svg, padding);\n  configureSvgSize(svg, height, width, useMaxWidth);\n  const viewBox = createViewBox(x, y, width, height, padding);\n  svg.attr(\"viewBox\", viewBox);\n  log.debug(`viewBox configured: ${viewBox} with padding: ${padding}`);\n}, \"setupViewPortForSVG\");\nvar calculateDimensionsWithPadding = /* @__PURE__ */ __name((svg, padding) => {\n  const bounds = svg.node()?.getBBox() || { width: 0, height: 0, x: 0, y: 0 };\n  return {\n    width: bounds.width + padding * 2,\n    height: bounds.height + padding * 2,\n    x: bounds.x,\n    y: bounds.y\n  };\n}, \"calculateDimensionsWithPadding\");\nvar createViewBox = /* @__PURE__ */ __name((x, y, width, height, padding) => {\n  return `${x - padding} ${y - padding} ${width} ${height}`;\n}, \"createViewBox\");\n\nexport {\n  getDiagramElement,\n  setupViewPortForSVG\n};\n"], "mappings": ";;;;;;;;AAQA,IAAI,oBAAoC,OAAO,CAAC,IAAI,kBAAkB;AACpE,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,eAAO,OAAO,EAAE;AAAA,EACnC;AACA,QAAM,OAAO,kBAAkB,YAAY,eAAO,eAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,IAAI,eAAO,MAAM;AACjH,QAAM,MAAM,KAAK,OAAO,QAAQ,EAAE,IAAI;AACtC,SAAO;AACT,GAAG,mBAAmB;AAGtB,IAAI,sBAAsC,OAAO,CAAC,KAAK,SAAS,YAAY,gBAAgB;AAC1F,MAAI,KAAK,SAAS,UAAU;AAC5B,QAAM,EAAE,OAAO,QAAQ,GAAG,EAAE,IAAI,+BAA+B,KAAK,OAAO;AAC3E,mBAAiB,KAAK,QAAQ,OAAO,WAAW;AAChD,QAAM,UAAU,cAAc,GAAG,GAAG,OAAO,QAAQ,OAAO;AAC1D,MAAI,KAAK,WAAW,OAAO;AAC3B,MAAI,MAAM,uBAAuB,OAAO,kBAAkB,OAAO,EAAE;AACrE,GAAG,qBAAqB;AACxB,IAAI,iCAAiD,OAAO,CAAC,KAAK,YAAY;AA3B9E;AA4BE,QAAM,WAAS,SAAI,KAAK,MAAT,mBAAY,cAAa,EAAE,OAAO,GAAG,QAAQ,GAAG,GAAG,GAAG,GAAG,EAAE;AAC1E,SAAO;AAAA,IACL,OAAO,OAAO,QAAQ,UAAU;AAAA,IAChC,QAAQ,OAAO,SAAS,UAAU;AAAA,IAClC,GAAG,OAAO;AAAA,IACV,GAAG,OAAO;AAAA,EACZ;AACF,GAAG,gCAAgC;AACnC,IAAI,gBAAgC,OAAO,CAAC,GAAG,GAAG,OAAO,QAAQ,YAAY;AAC3E,SAAO,GAAG,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,IAAI,MAAM;AACzD,GAAG,eAAe;", "names": []}