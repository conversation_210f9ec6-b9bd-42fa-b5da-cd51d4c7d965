{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 12427970797809321433, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 6849228626748195943], [3150220818285335163, "url", false, 8907747236312430643], [3191507132440681679, "serde_untagged", false, 6670383594668085889], [4071963112282141418, "serde_with", false, 5923415163062482985], [4899080583175475170, "semver", false, 16978511449633190971], [5986029879202738730, "log", false, 2351528888146949315], [6606131838865521726, "ctor", false, 17302679953152090055], [7170110829644101142, "json_patch", false, 6157681744129821401], [8319709847752024821, "uuid", false, 6776183863973640548], [9010263965687315507, "http", false, 9326335740251934562], [9451456094439810778, "regex", false, 11610437707374617284], [9556762810601084293, "brotli", false, 6255207127309063429], [9689903380558560274, "serde", false, 3755520848713314912], [10806645703491011684, "thiserror", false, 1866175186105377659], [11989259058781683633, "dunce", false, 11858794786811574169], [13625485746686963219, "anyhow", false, 6546084173510242191], [15367738274754116744, "serde_json", false, 12024906575928109641], [15609422047640926750, "toml", false, 11282779678063749469], [15622660310229662834, "walkdir", false, 4061966985145559829], [15932120279885307830, "memchr", false, 10218923838535464402], [17146114186171651583, "infer", false, 518981317603009313], [17155886227862585100, "glob", false, 2178336415492610584], [17186037756130803222, "phf", false, 5650946206721804938]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-c1fd5e056f9ee128\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}