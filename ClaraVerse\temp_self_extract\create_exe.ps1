
# Script PowerShell pour créer un EXE auto-extractible
Add-Type -AssemblyName System.IO.Compression.FileSystem

$appPath = "temp_self_extract\\app"
$outputExe = "C:\\Users\\<USER>\\Downloads\\WeMa_IA_Portable.exe"

# Créer un script batch auto-extractible
$batchContent = @'
@echo off
title WeMa IA - Installation
echo Installation de WeMa IA...

set "INSTALL_DIR=%APPDATA%\WeMa_IA"
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo Extraction en cours...
powershell -Command "Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('%~dp0app.zip', '%INSTALL_DIR%')"

echo Lancement...
start "" "%INSTALL_DIR%\WeMa IA.exe"

echo Installation terminee !
timeout /t 2 > nul
'@

$batchContent | Out-File -FilePath "launcher.bat" -Encoding ASCII

Write-Host "EXE auto-extractible créé !"
