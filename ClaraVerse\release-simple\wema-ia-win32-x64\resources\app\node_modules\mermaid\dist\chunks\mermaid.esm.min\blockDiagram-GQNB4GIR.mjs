import{a as Yt}from"./chunk-ZN7TASNU.mjs";import{a as Wt}from"./chunk-S67DUUA5.mjs";import{a as Ft}from"./chunk-LM6QDVU5.mjs";import{c as Mt,d as nt}from"./chunk-HESFG3RP.mjs";import{d as zt,m as tt,o as et}from"./chunk-YM3XIQPS.mjs";import"./chunk-TI4EEUUG.mjs";import{A as q,Da as Rt,F as bt,G as P,Ha as At,L as Nt,M as It,P as Ot,X as B,b as k,ga as C,l as Tt,m as Ct}from"./chunk-ZKYS2E5M.mjs";import{e as Pt}from"./chunk-5ZJXQJOJ.mjs";import"./chunk-YPUTD6PB.mjs";import"./chunk-6BY5RJGC.mjs";import{a as c}from"./chunk-GTKDMUJJ.mjs";var yt=function(){var e=c(function(T,y,d,f){for(d=d||{},f=T.length;f--;d[T[f]]=y);return d},"o"),t=[1,7],s=[1,13],n=[1,14],i=[1,15],r=[1,19],a=[1,16],l=[1,17],o=[1,18],u=[8,30],g=[8,21,28,29,30,31,32,40,44,47],x=[1,23],m=[1,24],b=[8,15,16,21,28,29,30,31,32,40,44,47],L=[8,15,16,21,27,28,29,30,31,32,40,44,47],_=[1,49],S={trace:c(function(){},"trace"),yy:{},symbols_:{error:2,spaceLines:3,SPACELINE:4,NL:5,separator:6,SPACE:7,EOF:8,start:9,BLOCK_DIAGRAM_KEY:10,document:11,stop:12,statement:13,link:14,LINK:15,START_LINK:16,LINK_LABEL:17,STR:18,nodeStatement:19,columnsStatement:20,SPACE_BLOCK:21,blockStatement:22,classDefStatement:23,cssClassStatement:24,styleStatement:25,node:26,SIZE:27,COLUMNS:28,"id-block":29,end:30,block:31,NODE_ID:32,nodeShapeNLabel:33,dirList:34,DIR:35,NODE_DSTART:36,NODE_DEND:37,BLOCK_ARROW_START:38,BLOCK_ARROW_END:39,classDef:40,CLASSDEF_ID:41,CLASSDEF_STYLEOPTS:42,DEFAULT:43,class:44,CLASSENTITY_IDS:45,STYLECLASS:46,style:47,STYLE_ENTITY_IDS:48,STYLE_DEFINITION_DATA:49,$accept:0,$end:1},terminals_:{2:"error",4:"SPACELINE",5:"NL",7:"SPACE",8:"EOF",10:"BLOCK_DIAGRAM_KEY",15:"LINK",16:"START_LINK",17:"LINK_LABEL",18:"STR",21:"SPACE_BLOCK",27:"SIZE",28:"COLUMNS",29:"id-block",30:"end",31:"block",32:"NODE_ID",35:"DIR",36:"NODE_DSTART",37:"NODE_DEND",38:"BLOCK_ARROW_START",39:"BLOCK_ARROW_END",40:"classDef",41:"CLASSDEF_ID",42:"CLASSDEF_STYLEOPTS",43:"DEFAULT",44:"class",45:"CLASSENTITY_IDS",46:"STYLECLASS",47:"style",48:"STYLE_ENTITY_IDS",49:"STYLE_DEFINITION_DATA"},productions_:[0,[3,1],[3,2],[3,2],[6,1],[6,1],[6,1],[9,3],[12,1],[12,1],[12,2],[12,2],[11,1],[11,2],[14,1],[14,4],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[19,3],[19,2],[19,1],[20,1],[22,4],[22,3],[26,1],[26,2],[34,1],[34,2],[33,3],[33,4],[23,3],[23,3],[24,3],[25,3]],performAction:c(function(y,d,f,w,E,h,Y){var p=h.length-1;switch(E){case 4:w.getLogger().debug("Rule: separator (NL) ");break;case 5:w.getLogger().debug("Rule: separator (Space) ");break;case 6:w.getLogger().debug("Rule: separator (EOF) ");break;case 7:w.getLogger().debug("Rule: hierarchy: ",h[p-1]),w.setHierarchy(h[p-1]);break;case 8:w.getLogger().debug("Stop NL ");break;case 9:w.getLogger().debug("Stop EOF ");break;case 10:w.getLogger().debug("Stop NL2 ");break;case 11:w.getLogger().debug("Stop EOF2 ");break;case 12:w.getLogger().debug("Rule: statement: ",h[p]),typeof h[p].length=="number"?this.$=h[p]:this.$=[h[p]];break;case 13:w.getLogger().debug("Rule: statement #2: ",h[p-1]),this.$=[h[p-1]].concat(h[p]);break;case 14:w.getLogger().debug("Rule: link: ",h[p],y),this.$={edgeTypeStr:h[p],label:""};break;case 15:w.getLogger().debug("Rule: LABEL link: ",h[p-3],h[p-1],h[p]),this.$={edgeTypeStr:h[p],label:h[p-1]};break;case 18:let R=parseInt(h[p]),Z=w.generateId();this.$={id:Z,type:"space",label:"",width:R,children:[]};break;case 23:w.getLogger().debug("Rule: (nodeStatement link node) ",h[p-2],h[p-1],h[p]," typestr: ",h[p-1].edgeTypeStr);let V=w.edgeStrToEdgeData(h[p-1].edgeTypeStr);this.$=[{id:h[p-2].id,label:h[p-2].label,type:h[p-2].type,directions:h[p-2].directions},{id:h[p-2].id+"-"+h[p].id,start:h[p-2].id,end:h[p].id,label:h[p-1].label,type:"edge",directions:h[p].directions,arrowTypeEnd:V,arrowTypeStart:"arrow_open"},{id:h[p].id,label:h[p].label,type:w.typeStr2Type(h[p].typeStr),directions:h[p].directions}];break;case 24:w.getLogger().debug("Rule: nodeStatement (abc88 node size) ",h[p-1],h[p]),this.$={id:h[p-1].id,label:h[p-1].label,type:w.typeStr2Type(h[p-1].typeStr),directions:h[p-1].directions,widthInColumns:parseInt(h[p],10)};break;case 25:w.getLogger().debug("Rule: nodeStatement (node) ",h[p]),this.$={id:h[p].id,label:h[p].label,type:w.typeStr2Type(h[p].typeStr),directions:h[p].directions,widthInColumns:1};break;case 26:w.getLogger().debug("APA123",this?this:"na"),w.getLogger().debug("COLUMNS: ",h[p]),this.$={type:"column-setting",columns:h[p]==="auto"?-1:parseInt(h[p])};break;case 27:w.getLogger().debug("Rule: id-block statement : ",h[p-2],h[p-1]);let Bt=w.generateId();this.$={...h[p-2],type:"composite",children:h[p-1]};break;case 28:w.getLogger().debug("Rule: blockStatement : ",h[p-2],h[p-1],h[p]);let at=w.generateId();this.$={id:at,type:"composite",label:"",children:h[p-1]};break;case 29:w.getLogger().debug("Rule: node (NODE_ID separator): ",h[p]),this.$={id:h[p]};break;case 30:w.getLogger().debug("Rule: node (NODE_ID nodeShapeNLabel separator): ",h[p-1],h[p]),this.$={id:h[p-1],label:h[p].label,typeStr:h[p].typeStr,directions:h[p].directions};break;case 31:w.getLogger().debug("Rule: dirList: ",h[p]),this.$=[h[p]];break;case 32:w.getLogger().debug("Rule: dirList: ",h[p-1],h[p]),this.$=[h[p-1]].concat(h[p]);break;case 33:w.getLogger().debug("Rule: nodeShapeNLabel: ",h[p-2],h[p-1],h[p]),this.$={typeStr:h[p-2]+h[p],label:h[p-1]};break;case 34:w.getLogger().debug("Rule: BLOCK_ARROW nodeShapeNLabel: ",h[p-3],h[p-2]," #3:",h[p-1],h[p]),this.$={typeStr:h[p-3]+h[p],label:h[p-2],directions:h[p-1]};break;case 35:case 36:this.$={type:"classDef",id:h[p-1].trim(),css:h[p].trim()};break;case 37:this.$={type:"applyClass",id:h[p-1].trim(),styleClass:h[p].trim()};break;case 38:this.$={type:"applyStyles",id:h[p-1].trim(),stylesStr:h[p].trim()};break}},"anonymous"),table:[{9:1,10:[1,2]},{1:[3]},{11:3,13:4,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:s,29:n,31:i,32:r,40:a,44:l,47:o},{8:[1,20]},e(u,[2,12],{13:4,19:5,20:6,22:8,23:9,24:10,25:11,26:12,11:21,21:t,28:s,29:n,31:i,32:r,40:a,44:l,47:o}),e(g,[2,16],{14:22,15:x,16:m}),e(g,[2,17]),e(g,[2,18]),e(g,[2,19]),e(g,[2,20]),e(g,[2,21]),e(g,[2,22]),e(b,[2,25],{27:[1,25]}),e(g,[2,26]),{19:26,26:12,32:r},{11:27,13:4,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:s,29:n,31:i,32:r,40:a,44:l,47:o},{41:[1,28],43:[1,29]},{45:[1,30]},{48:[1,31]},e(L,[2,29],{33:32,36:[1,33],38:[1,34]}),{1:[2,7]},e(u,[2,13]),{26:35,32:r},{32:[2,14]},{17:[1,36]},e(b,[2,24]),{11:37,13:4,14:22,15:x,16:m,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:s,29:n,31:i,32:r,40:a,44:l,47:o},{30:[1,38]},{42:[1,39]},{42:[1,40]},{46:[1,41]},{49:[1,42]},e(L,[2,30]),{18:[1,43]},{18:[1,44]},e(b,[2,23]),{18:[1,45]},{30:[1,46]},e(g,[2,28]),e(g,[2,35]),e(g,[2,36]),e(g,[2,37]),e(g,[2,38]),{37:[1,47]},{34:48,35:_},{15:[1,50]},e(g,[2,27]),e(L,[2,33]),{39:[1,51]},{34:52,35:_,39:[2,31]},{32:[2,15]},e(L,[2,34]),{39:[2,32]}],defaultActions:{20:[2,7],23:[2,14],50:[2,15],52:[2,32]},parseError:c(function(y,d){if(d.recoverable)this.trace(y);else{var f=new Error(y);throw f.hash=d,f}},"parseError"),parse:c(function(y){var d=this,f=[0],w=[],E=[null],h=[],Y=this.table,p="",R=0,Z=0,V=0,Bt=2,at=1,_e=h.slice.call(arguments,1),z=Object.create(this.lexer),J={yy:{}};for(var dt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,dt)&&(J.yy[dt]=this.yy[dt]);z.setInput(y,J.yy),J.yy.lexer=z,J.yy.parser=this,typeof z.yylloc>"u"&&(z.yylloc={});var ut=z.yylloc;h.push(ut);var De=z.options&&z.options.ranges;typeof J.yy.parseError=="function"?this.parseError=J.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Gr(H){f.length=f.length-2*H,E.length=E.length-H,h.length=h.length-H}c(Gr,"popStack");function Be(){var H;return H=w.pop()||z.lex()||at,typeof H!="number"&&(H instanceof Array&&(w=H,H=w.pop()),H=d.symbols_[H]||H),H}c(Be,"lex");for(var F,pt,Q,K,Zr,ft,$={},st,G,vt,it;;){if(Q=f[f.length-1],this.defaultActions[Q]?K=this.defaultActions[Q]:((F===null||typeof F>"u")&&(F=Be()),K=Y[Q]&&Y[Q][F]),typeof K>"u"||!K.length||!K[0]){var xt="";it=[];for(st in Y[Q])this.terminals_[st]&&st>Bt&&it.push("'"+this.terminals_[st]+"'");z.showPosition?xt="Parse error on line "+(R+1)+`:
`+z.showPosition()+`
Expecting `+it.join(", ")+", got '"+(this.terminals_[F]||F)+"'":xt="Parse error on line "+(R+1)+": Unexpected "+(F==at?"end of input":"'"+(this.terminals_[F]||F)+"'"),this.parseError(xt,{text:z.match,token:this.terminals_[F]||F,line:z.yylineno,loc:ut,expected:it})}if(K[0]instanceof Array&&K.length>1)throw new Error("Parse Error: multiple actions possible at state: "+Q+", token: "+F);switch(K[0]){case 1:f.push(F),E.push(z.yytext),h.push(z.yylloc),f.push(K[1]),F=null,pt?(F=pt,pt=null):(Z=z.yyleng,p=z.yytext,R=z.yylineno,ut=z.yylloc,V>0&&V--);break;case 2:if(G=this.productions_[K[1]][1],$.$=E[E.length-G],$._$={first_line:h[h.length-(G||1)].first_line,last_line:h[h.length-1].last_line,first_column:h[h.length-(G||1)].first_column,last_column:h[h.length-1].last_column},De&&($._$.range=[h[h.length-(G||1)].range[0],h[h.length-1].range[1]]),ft=this.performAction.apply($,[p,Z,R,J.yy,K[1],E,h].concat(_e)),typeof ft<"u")return ft;G&&(f=f.slice(0,-1*G*2),E=E.slice(0,-1*G),h=h.slice(0,-1*G)),f.push(this.productions_[K[1]][0]),E.push($.$),h.push($._$),vt=Y[f[f.length-2]][f[f.length-1]],f.push(vt);break;case 3:return!0}}return!0},"parse")},I=function(){var T={EOF:1,parseError:c(function(d,f){if(this.yy.parser)this.yy.parser.parseError(d,f);else throw new Error(d)},"parseError"),setInput:c(function(y,d){return this.yy=d||this.yy||{},this._input=y,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:c(function(){var y=this._input[0];this.yytext+=y,this.yyleng++,this.offset++,this.match+=y,this.matched+=y;var d=y.match(/(?:\r\n?|\n).*/g);return d?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),y},"input"),unput:c(function(y){var d=y.length,f=y.split(/(?:\r\n?|\n)/g);this._input=y+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-d),this.offset-=d;var w=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),f.length-1&&(this.yylineno-=f.length-1);var E=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:f?(f.length===w.length?this.yylloc.first_column:0)+w[w.length-f.length].length-f[0].length:this.yylloc.first_column-d},this.options.ranges&&(this.yylloc.range=[E[0],E[0]+this.yyleng-d]),this.yyleng=this.yytext.length,this},"unput"),more:c(function(){return this._more=!0,this},"more"),reject:c(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:c(function(y){this.unput(this.match.slice(y))},"less"),pastInput:c(function(){var y=this.matched.substr(0,this.matched.length-this.match.length);return(y.length>20?"...":"")+y.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:c(function(){var y=this.match;return y.length<20&&(y+=this._input.substr(0,20-y.length)),(y.substr(0,20)+(y.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:c(function(){var y=this.pastInput(),d=new Array(y.length+1).join("-");return y+this.upcomingInput()+`
`+d+"^"},"showPosition"),test_match:c(function(y,d){var f,w,E;if(this.options.backtrack_lexer&&(E={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(E.yylloc.range=this.yylloc.range.slice(0))),w=y[0].match(/(?:\r\n?|\n).*/g),w&&(this.yylineno+=w.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:w?w[w.length-1].length-w[w.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+y[0].length},this.yytext+=y[0],this.match+=y[0],this.matches=y,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(y[0].length),this.matched+=y[0],f=this.performAction.call(this,this.yy,this,d,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),f)return f;if(this._backtrack){for(var h in E)this[h]=E[h];return!1}return!1},"test_match"),next:c(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var y,d,f,w;this._more||(this.yytext="",this.match="");for(var E=this._currentRules(),h=0;h<E.length;h++)if(f=this._input.match(this.rules[E[h]]),f&&(!d||f[0].length>d[0].length)){if(d=f,w=h,this.options.backtrack_lexer){if(y=this.test_match(f,E[h]),y!==!1)return y;if(this._backtrack){d=!1;continue}else return!1}else if(!this.options.flex)break}return d?(y=this.test_match(d,E[w]),y!==!1?y:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:c(function(){var d=this.next();return d||this.lex()},"lex"),begin:c(function(d){this.conditionStack.push(d)},"begin"),popState:c(function(){var d=this.conditionStack.length-1;return d>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:c(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:c(function(d){return d=this.conditionStack.length-1-Math.abs(d||0),d>=0?this.conditionStack[d]:"INITIAL"},"topState"),pushState:c(function(d){this.begin(d)},"pushState"),stateStackSize:c(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:c(function(d,f,w,E){var h=E;switch(w){case 0:return 10;case 1:return d.getLogger().debug("Found space-block"),31;break;case 2:return d.getLogger().debug("Found nl-block"),31;break;case 3:return d.getLogger().debug("Found space-block"),29;break;case 4:d.getLogger().debug(".",f.yytext);break;case 5:d.getLogger().debug("_",f.yytext);break;case 6:return 5;case 7:return f.yytext=-1,28;break;case 8:return f.yytext=f.yytext.replace(/columns\s+/,""),d.getLogger().debug("COLUMNS (LEX)",f.yytext),28;break;case 9:this.pushState("md_string");break;case 10:return"MD_STR";case 11:this.popState();break;case 12:this.pushState("string");break;case 13:d.getLogger().debug("LEX: POPPING STR:",f.yytext),this.popState();break;case 14:return d.getLogger().debug("LEX: STR end:",f.yytext),"STR";break;case 15:return f.yytext=f.yytext.replace(/space\:/,""),d.getLogger().debug("SPACE NUM (LEX)",f.yytext),21;break;case 16:return f.yytext="1",d.getLogger().debug("COLUMNS (LEX)",f.yytext),21;break;case 17:return 43;case 18:return"LINKSTYLE";case 19:return"INTERPOLATE";case 20:return this.pushState("CLASSDEF"),40;break;case 21:return this.popState(),this.pushState("CLASSDEFID"),"DEFAULT_CLASSDEF_ID";break;case 22:return this.popState(),this.pushState("CLASSDEFID"),41;break;case 23:return this.popState(),42;break;case 24:return this.pushState("CLASS"),44;break;case 25:return this.popState(),this.pushState("CLASS_STYLE"),45;break;case 26:return this.popState(),46;break;case 27:return this.pushState("STYLE_STMNT"),47;break;case 28:return this.popState(),this.pushState("STYLE_DEFINITION"),48;break;case 29:return this.popState(),49;break;case 30:return this.pushState("acc_title"),"acc_title";break;case 31:return this.popState(),"acc_title_value";break;case 32:return this.pushState("acc_descr"),"acc_descr";break;case 33:return this.popState(),"acc_descr_value";break;case 34:this.pushState("acc_descr_multiline");break;case 35:this.popState();break;case 36:return"acc_descr_multiline_value";case 37:return 30;case 38:return this.popState(),d.getLogger().debug("Lex: (("),"NODE_DEND";break;case 39:return this.popState(),d.getLogger().debug("Lex: (("),"NODE_DEND";break;case 40:return this.popState(),d.getLogger().debug("Lex: ))"),"NODE_DEND";break;case 41:return this.popState(),d.getLogger().debug("Lex: (("),"NODE_DEND";break;case 42:return this.popState(),d.getLogger().debug("Lex: (("),"NODE_DEND";break;case 43:return this.popState(),d.getLogger().debug("Lex: (-"),"NODE_DEND";break;case 44:return this.popState(),d.getLogger().debug("Lex: -)"),"NODE_DEND";break;case 45:return this.popState(),d.getLogger().debug("Lex: (("),"NODE_DEND";break;case 46:return this.popState(),d.getLogger().debug("Lex: ]]"),"NODE_DEND";break;case 47:return this.popState(),d.getLogger().debug("Lex: ("),"NODE_DEND";break;case 48:return this.popState(),d.getLogger().debug("Lex: ])"),"NODE_DEND";break;case 49:return this.popState(),d.getLogger().debug("Lex: /]"),"NODE_DEND";break;case 50:return this.popState(),d.getLogger().debug("Lex: /]"),"NODE_DEND";break;case 51:return this.popState(),d.getLogger().debug("Lex: )]"),"NODE_DEND";break;case 52:return this.popState(),d.getLogger().debug("Lex: )"),"NODE_DEND";break;case 53:return this.popState(),d.getLogger().debug("Lex: ]>"),"NODE_DEND";break;case 54:return this.popState(),d.getLogger().debug("Lex: ]"),"NODE_DEND";break;case 55:return d.getLogger().debug("Lexa: -)"),this.pushState("NODE"),36;break;case 56:return d.getLogger().debug("Lexa: (-"),this.pushState("NODE"),36;break;case 57:return d.getLogger().debug("Lexa: ))"),this.pushState("NODE"),36;break;case 58:return d.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;break;case 59:return d.getLogger().debug("Lex: ((("),this.pushState("NODE"),36;break;case 60:return d.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;break;case 61:return d.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;break;case 62:return d.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;break;case 63:return d.getLogger().debug("Lexc: >"),this.pushState("NODE"),36;break;case 64:return d.getLogger().debug("Lexa: (["),this.pushState("NODE"),36;break;case 65:return d.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;break;case 66:return this.pushState("NODE"),36;break;case 67:return this.pushState("NODE"),36;break;case 68:return this.pushState("NODE"),36;break;case 69:return this.pushState("NODE"),36;break;case 70:return this.pushState("NODE"),36;break;case 71:return this.pushState("NODE"),36;break;case 72:return this.pushState("NODE"),36;break;case 73:return d.getLogger().debug("Lexa: ["),this.pushState("NODE"),36;break;case 74:return this.pushState("BLOCK_ARROW"),d.getLogger().debug("LEX ARR START"),38;break;case 75:return d.getLogger().debug("Lex: NODE_ID",f.yytext),32;break;case 76:return d.getLogger().debug("Lex: EOF",f.yytext),8;break;case 77:this.pushState("md_string");break;case 78:this.pushState("md_string");break;case 79:return"NODE_DESCR";case 80:this.popState();break;case 81:d.getLogger().debug("Lex: Starting string"),this.pushState("string");break;case 82:d.getLogger().debug("LEX ARR: Starting string"),this.pushState("string");break;case 83:return d.getLogger().debug("LEX: NODE_DESCR:",f.yytext),"NODE_DESCR";break;case 84:d.getLogger().debug("LEX POPPING"),this.popState();break;case 85:d.getLogger().debug("Lex: =>BAE"),this.pushState("ARROW_DIR");break;case 86:return f.yytext=f.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (right): dir:",f.yytext),"DIR";break;case 87:return f.yytext=f.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (left):",f.yytext),"DIR";break;case 88:return f.yytext=f.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (x):",f.yytext),"DIR";break;case 89:return f.yytext=f.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (y):",f.yytext),"DIR";break;case 90:return f.yytext=f.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (up):",f.yytext),"DIR";break;case 91:return f.yytext=f.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (down):",f.yytext),"DIR";break;case 92:return f.yytext="]>",d.getLogger().debug("Lex (ARROW_DIR end):",f.yytext),this.popState(),this.popState(),"BLOCK_ARROW_END";break;case 93:return d.getLogger().debug("Lex: LINK","#"+f.yytext+"#"),15;break;case 94:return d.getLogger().debug("Lex: LINK",f.yytext),15;break;case 95:return d.getLogger().debug("Lex: LINK",f.yytext),15;break;case 96:return d.getLogger().debug("Lex: LINK",f.yytext),15;break;case 97:return d.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;break;case 98:return d.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;break;case 99:return d.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;break;case 100:this.pushState("md_string");break;case 101:return d.getLogger().debug("Lex: Starting string"),this.pushState("string"),"LINK_LABEL";break;case 102:return this.popState(),d.getLogger().debug("Lex: LINK","#"+f.yytext+"#"),15;break;case 103:return this.popState(),d.getLogger().debug("Lex: LINK",f.yytext),15;break;case 104:return this.popState(),d.getLogger().debug("Lex: LINK",f.yytext),15;break;case 105:return d.getLogger().debug("Lex: COLON",f.yytext),f.yytext=f.yytext.slice(1),27;break}},"anonymous"),rules:[/^(?:block-beta\b)/,/^(?:block\s+)/,/^(?:block\n+)/,/^(?:block:)/,/^(?:[\s]+)/,/^(?:[\n]+)/,/^(?:((\u000D\u000A)|(\u000A)))/,/^(?:columns\s+auto\b)/,/^(?:columns\s+[\d]+)/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:space[:]\d+)/,/^(?:space\b)/,/^(?:default\b)/,/^(?:linkStyle\b)/,/^(?:interpolate\b)/,/^(?:classDef\s+)/,/^(?:DEFAULT\s+)/,/^(?:\w+\s+)/,/^(?:[^\n]*)/,/^(?:class\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:style\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:end\b\s*)/,/^(?:\(\(\()/,/^(?:\)\)\))/,/^(?:[\)]\))/,/^(?:\}\})/,/^(?:\})/,/^(?:\(-)/,/^(?:-\))/,/^(?:\(\()/,/^(?:\]\])/,/^(?:\()/,/^(?:\]\))/,/^(?:\\\])/,/^(?:\/\])/,/^(?:\)\])/,/^(?:[\)])/,/^(?:\]>)/,/^(?:[\]])/,/^(?:-\))/,/^(?:\(-)/,/^(?:\)\))/,/^(?:\))/,/^(?:\(\(\()/,/^(?:\(\()/,/^(?:\{\{)/,/^(?:\{)/,/^(?:>)/,/^(?:\(\[)/,/^(?:\()/,/^(?:\[\[)/,/^(?:\[\|)/,/^(?:\[\()/,/^(?:\)\)\))/,/^(?:\[\\)/,/^(?:\[\/)/,/^(?:\[\\)/,/^(?:\[)/,/^(?:<\[)/,/^(?:[^\(\[\n\-\)\{\}\s\<\>:]+)/,/^(?:$)/,/^(?:["][`])/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:\]>\s*\()/,/^(?:,?\s*right\s*)/,/^(?:,?\s*left\s*)/,/^(?:,?\s*x\s*)/,/^(?:,?\s*y\s*)/,/^(?:,?\s*up\s*)/,/^(?:,?\s*down\s*)/,/^(?:\)\s*)/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?:\s*~~[\~]+\s*)/,/^(?:\s*[xo<]?--\s*)/,/^(?:\s*[xo<]?==\s*)/,/^(?:\s*[xo<]?-\.\s*)/,/^(?:["][`])/,/^(?:["])/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?::\d+)/],conditions:{STYLE_DEFINITION:{rules:[29],inclusive:!1},STYLE_STMNT:{rules:[28],inclusive:!1},CLASSDEFID:{rules:[23],inclusive:!1},CLASSDEF:{rules:[21,22],inclusive:!1},CLASS_STYLE:{rules:[26],inclusive:!1},CLASS:{rules:[25],inclusive:!1},LLABEL:{rules:[100,101,102,103,104],inclusive:!1},ARROW_DIR:{rules:[86,87,88,89,90,91,92],inclusive:!1},BLOCK_ARROW:{rules:[77,82,85],inclusive:!1},NODE:{rules:[38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,78,81],inclusive:!1},md_string:{rules:[10,11,79,80],inclusive:!1},space:{rules:[],inclusive:!1},string:{rules:[13,14,83,84],inclusive:!1},acc_descr_multiline:{rules:[35,36],inclusive:!1},acc_descr:{rules:[33],inclusive:!1},acc_title:{rules:[31],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,12,15,16,17,18,19,20,24,27,30,32,34,37,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,93,94,95,96,97,98,99,105],inclusive:!0}}};return T}();S.lexer=I;function D(){this.yy={}}return c(D,"Parser"),D.prototype=S,S.Parser=D,new D}();yt.parser=yt;var Ht=yt;var X=new Map,kt=[],mt=new Map,jt="color",Kt="fill",Te="bgFill",Ut=",",Ce=B(),lt=new Map,Ne=c(e=>Nt.sanitizeText(e,Ce),"sanitizeText"),Ie=c(function(e,t=""){let s=lt.get(e);s||(s={id:e,styles:[],textStyles:[]},lt.set(e,s)),t?.split(Ut).forEach(n=>{let i=n.replace(/([^;]*);/,"$1").trim();if(RegExp(jt).exec(n)){let a=i.replace(Kt,Te).replace(jt,Kt);s.textStyles.push(a)}s.styles.push(i)})},"addStyleClass"),Oe=c(function(e,t=""){let s=X.get(e);t!=null&&(s.styles=t.split(Ut))},"addStyle2Node"),Re=c(function(e,t){e.split(",").forEach(function(s){let n=X.get(s);if(n===void 0){let i=s.trim();n={id:i,type:"na",children:[]},X.set(i,n)}n.classes||(n.classes=[]),n.classes.push(t)})},"setCssClass"),Vt=c((e,t)=>{let s=e.flat(),n=[];for(let i of s){if(i.label&&(i.label=Ne(i.label)),i.type==="classDef"){Ie(i.id,i.css);continue}if(i.type==="applyClass"){Re(i.id,i?.styleClass??"");continue}if(i.type==="applyStyles"){i?.stylesStr&&Oe(i.id,i?.stylesStr);continue}if(i.type==="column-setting")t.columns=i.columns??-1;else if(i.type==="edge"){let r=(mt.get(i.id)??0)+1;mt.set(i.id,r),i.id=r+"-"+i.id,kt.push(i)}else{i.label||(i.type==="composite"?i.label="":i.label=i.id);let r=X.get(i.id);if(r===void 0?X.set(i.id,i):(i.type!=="na"&&(r.type=i.type),i.label!==i.id&&(r.label=i.label)),i.children&&Vt(i.children,i),i.type==="space"){let a=i.width??1;for(let l=0;l<a;l++){let o=Pt(i);o.id=o.id+"-"+l,X.set(o.id,o),n.push(o)}}else r===void 0&&n.push(i)}}t.children=n},"populateBlockDatabase"),wt=[],rt={id:"root",type:"composite",children:[],columns:-1},Ae=c(()=>{k.debug("Clear called"),Ot(),rt={id:"root",type:"composite",children:[],columns:-1},X=new Map([["root",rt]]),wt=[],lt=new Map,kt=[],mt=new Map},"clear");function ze(e){switch(k.debug("typeStr2Type",e),e){case"[]":return"square";case"()":return k.debug("we have a round"),"round";case"(())":return"circle";case">]":return"rect_left_inv_arrow";case"{}":return"diamond";case"{{}}":return"hexagon";case"([])":return"stadium";case"[[]]":return"subroutine";case"[()]":return"cylinder";case"((()))":return"doublecircle";case"[//]":return"lean_right";case"[\\\\]":return"lean_left";case"[/\\]":return"trapezoid";case"[\\/]":return"inv_trapezoid";case"<[]>":return"block_arrow";default:return"na"}}c(ze,"typeStr2Type");function Me(e){switch(k.debug("typeStr2Type",e),e){case"==":return"thick";default:return"normal"}}c(Me,"edgeTypeStr2Type");function Fe(e){switch(e.trim()){case"--x":return"arrow_cross";case"--o":return"arrow_circle";default:return"arrow_point"}}c(Fe,"edgeStrToEdgeData");var Xt=0,We=c(()=>(Xt++,"id-"+Math.random().toString(36).substr(2,12)+"-"+Xt),"generateId"),Pe=c(e=>{rt.children=e,Vt(e,rt),wt=rt.children},"setHierarchy"),Ye=c(e=>{let t=X.get(e);return t?t.columns?t.columns:t.children?t.children.length:-1:-1},"getColumns"),He=c(()=>[...X.values()],"getBlocksFlat"),je=c(()=>wt||[],"getBlocks"),Ke=c(()=>kt,"getEdges"),Xe=c(e=>X.get(e),"getBlock"),Ue=c(e=>{X.set(e.id,e)},"setBlock"),Ve=c(()=>console,"getLogger"),Ge=c(function(){return lt},"getClasses"),Ze={getConfig:c(()=>q().block,"getConfig"),typeStr2Type:ze,edgeTypeStr2Type:Me,edgeStrToEdgeData:Fe,getLogger:Ve,getBlocksFlat:He,getBlocks:je,getEdges:Ke,setHierarchy:Pe,getBlock:Xe,setBlock:Ue,getColumns:Ye,getClasses:Ge,clear:Ae,generateId:We},Gt=Ze;var ot=c((e,t)=>{let s=Ct,n=s(e,"r"),i=s(e,"g"),r=s(e,"b");return Tt(n,i,r,t)},"fade"),qe=c(e=>`.label {
    font-family: ${e.fontFamily};
    color: ${e.nodeTextColor||e.textColor};
  }
  .cluster-label text {
    fill: ${e.titleColor};
  }
  .cluster-label span,p {
    color: ${e.titleColor};
  }



  .label text,span,p {
    fill: ${e.nodeTextColor||e.textColor};
    color: ${e.nodeTextColor||e.textColor};
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.mainBkg};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }
  .flowchart-label text {
    text-anchor: middle;
  }
  // .flowchart-label .text-outer-tspan {
  //   text-anchor: middle;
  // }
  // .flowchart-label .text-inner-tspan {
  //   text-anchor: start;
  // }

  .node .label {
    text-align: center;
  }
  .node.clickable {
    cursor: pointer;
  }

  .arrowheadPath {
    fill: ${e.arrowheadColor};
  }

  .edgePath .path {
    stroke: ${e.lineColor};
    stroke-width: 2.0px;
  }

  .flowchart-link {
    stroke: ${e.lineColor};
    fill: none;
  }

  .edgeLabel {
    background-color: ${e.edgeLabelBackground};
    rect {
      opacity: 0.5;
      background-color: ${e.edgeLabelBackground};
      fill: ${e.edgeLabelBackground};
    }
    text-align: center;
  }

  /* For html labels only */
  .labelBkg {
    background-color: ${ot(e.edgeLabelBackground,.5)};
    // background-color:
  }

  .node .cluster {
    // fill: ${ot(e.mainBkg,.5)};
    fill: ${ot(e.clusterBkg,.5)};
    stroke: ${ot(e.clusterBorder,.2)};
    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
    stroke-width: 1px;
  }

  .cluster text {
    fill: ${e.titleColor};
  }

  .cluster span,p {
    color: ${e.titleColor};
  }
  /* .cluster div {
    color: ${e.titleColor};
  } */

  div.mermaidTooltip {
    position: absolute;
    text-align: center;
    max-width: 200px;
    padding: 2px;
    font-family: ${e.fontFamily};
    font-size: 12px;
    background: ${e.tertiaryColor};
    border: 1px solid ${e.border2};
    border-radius: 2px;
    pointer-events: none;
    z-index: 100;
  }

  .flowchartTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${e.textColor};
  }
`,"getStyles"),Zt=qe;var Je=c((e,t,s,n)=>{t.forEach(i=>{lr[i](e,s,n)})},"insertMarkers"),Qe=c((e,t,s)=>{k.trace("Making markers for ",s),e.append("defs").append("marker").attr("id",s+"_"+t+"-extensionStart").attr("class","marker extension "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),e.append("defs").append("marker").attr("id",s+"_"+t+"-extensionEnd").attr("class","marker extension "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")},"extension"),$e=c((e,t,s)=>{e.append("defs").append("marker").attr("id",s+"_"+t+"-compositionStart").attr("class","marker composition "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",s+"_"+t+"-compositionEnd").attr("class","marker composition "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"composition"),tr=c((e,t,s)=>{e.append("defs").append("marker").attr("id",s+"_"+t+"-aggregationStart").attr("class","marker aggregation "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",s+"_"+t+"-aggregationEnd").attr("class","marker aggregation "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"aggregation"),er=c((e,t,s)=>{e.append("defs").append("marker").attr("id",s+"_"+t+"-dependencyStart").attr("class","marker dependency "+t).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",s+"_"+t+"-dependencyEnd").attr("class","marker dependency "+t).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"dependency"),rr=c((e,t,s)=>{e.append("defs").append("marker").attr("id",s+"_"+t+"-lollipopStart").attr("class","marker lollipop "+t).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),e.append("defs").append("marker").attr("id",s+"_"+t+"-lollipopEnd").attr("class","marker lollipop "+t).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)},"lollipop"),ar=c((e,t,s)=>{e.append("marker").attr("id",s+"_"+t+"-pointEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",6).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",s+"_"+t+"-pointStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"point"),sr=c((e,t,s)=>{e.append("marker").attr("id",s+"_"+t+"-circleEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",s+"_"+t+"-circleStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"circle"),ir=c((e,t,s)=>{e.append("marker").attr("id",s+"_"+t+"-crossEnd").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),e.append("marker").attr("id",s+"_"+t+"-crossStart").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")},"cross"),nr=c((e,t,s)=>{e.append("defs").append("marker").attr("id",s+"_"+t+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","strokeWidth").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"barb"),lr={extension:Qe,composition:$e,aggregation:tr,dependency:er,lollipop:rr,point:ar,circle:sr,cross:ir,barb:nr},qt=Je;var O=B()?.block?.padding??8;function or(e,t){if(e===0||!Number.isInteger(e))throw new Error("Columns must be an integer !== 0.");if(t<0||!Number.isInteger(t))throw new Error("Position must be a non-negative integer."+t);if(e<0)return{px:t,py:0};if(e===1)return{px:0,py:t};let s=t%e,n=Math.floor(t/e);return{px:s,py:n}}c(or,"calculateBlockPosition");var cr=c(e=>{let t=0,s=0;for(let n of e.children){let{width:i,height:r,x:a,y:l}=n.size??{width:0,height:0,x:0,y:0};k.debug("getMaxChildSize abc95 child:",n.id,"width:",i,"height:",r,"x:",a,"y:",l,n.type),n.type!=="space"&&(i>t&&(t=i/(e.widthInColumns??1)),r>s&&(s=r))}return{width:t,height:s}},"getMaxChildSize");function Lt(e,t,s=0,n=0){k.debug("setBlockSizes abc95 (start)",e.id,e?.size?.x,"block width =",e?.size,"sieblingWidth",s),e?.size?.width||(e.size={width:s,height:n,x:0,y:0});let i=0,r=0;if(e.children?.length>0){for(let b of e.children)Lt(b,t);let a=cr(e);i=a.width,r=a.height,k.debug("setBlockSizes abc95 maxWidth of",e.id,":s children is ",i,r);for(let b of e.children)b.size&&(k.debug(`abc95 Setting size of children of ${e.id} id=${b.id} ${i} ${r} ${JSON.stringify(b.size)}`),b.size.width=i*(b.widthInColumns??1)+O*((b.widthInColumns??1)-1),b.size.height=r,b.size.x=0,b.size.y=0,k.debug(`abc95 updating size of ${e.id} children child:${b.id} maxWidth:${i} maxHeight:${r}`));for(let b of e.children)Lt(b,t,i,r);let l=e.columns??-1,o=0;for(let b of e.children)o+=b.widthInColumns??1;let u=e.children.length;l>0&&l<o&&(u=l);let g=Math.ceil(o/u),x=u*(i+O)+O,m=g*(r+O)+O;if(x<s){k.debug(`Detected to small siebling: abc95 ${e.id} sieblingWidth ${s} sieblingHeight ${n} width ${x}`),x=s,m=n;let b=(s-u*O-O)/u,L=(n-g*O-O)/g;k.debug("Size indata abc88",e.id,"childWidth",b,"maxWidth",i),k.debug("Size indata abc88",e.id,"childHeight",L,"maxHeight",r),k.debug("Size indata abc88 xSize",u,"padding",O);for(let _ of e.children)_.size&&(_.size.width=b,_.size.height=L,_.size.x=0,_.size.y=0)}if(k.debug(`abc95 (finale calc) ${e.id} xSize ${u} ySize ${g} columns ${l}${e.children.length} width=${Math.max(x,e.size?.width||0)}`),x<(e?.size?.width||0)){x=e?.size?.width||0;let b=l>0?Math.min(e.children.length,l):e.children.length;if(b>0){let L=(x-b*O-O)/b;k.debug("abc95 (growing to fit) width",e.id,x,e.size?.width,L);for(let _ of e.children)_.size&&(_.size.width=L)}}e.size={width:x,height:m,x:0,y:0}}k.debug("setBlockSizes abc94 (done)",e.id,e?.size?.x,e?.size?.width,e?.size?.y,e?.size?.height)}c(Lt,"setBlockSizes");function Jt(e,t){k.debug(`abc85 layout blocks (=>layoutBlocks) ${e.id} x: ${e?.size?.x} y: ${e?.size?.y} width: ${e?.size?.width}`);let s=e.columns??-1;if(k.debug("layoutBlocks columns abc95",e.id,"=>",s,e),e.children&&e.children.length>0){let n=e?.children[0]?.size?.width??0,i=e.children.length*n+(e.children.length-1)*O;k.debug("widthOfChildren 88",i,"posX");let r=0;k.debug("abc91 block?.size?.x",e.id,e?.size?.x);let a=e?.size?.x?e?.size?.x+(-e?.size?.width/2||0):-O,l=0;for(let o of e.children){let u=e;if(!o.size)continue;let{width:g,height:x}=o.size,{px:m,py:b}=or(s,r);if(b!=l&&(l=b,a=e?.size?.x?e?.size?.x+(-e?.size?.width/2||0):-O,k.debug("New row in layout for block",e.id," and child ",o.id,l)),k.debug(`abc89 layout blocks (child) id: ${o.id} Pos: ${r} (px, py) ${m},${b} (${u?.size?.x},${u?.size?.y}) parent: ${u.id} width: ${g}${O}`),u.size){let L=g/2;o.size.x=a+O+L,k.debug(`abc91 layout blocks (calc) px, pyid:${o.id} startingPos=X${a} new startingPosX${o.size.x} ${L} padding=${O} width=${g} halfWidth=${L} => x:${o.size.x} y:${o.size.y} ${o.widthInColumns} (width * (child?.w || 1)) / 2 ${g*(o?.widthInColumns??1)/2}`),a=o.size.x+L,o.size.y=u.size.y-u.size.height/2+b*(x+O)+x/2+O,k.debug(`abc88 layout blocks (calc) px, pyid:${o.id}startingPosX${a}${O}${L}=>x:${o.size.x}y:${o.size.y}${o.widthInColumns}(width * (child?.w || 1)) / 2${g*(o?.widthInColumns??1)/2}`)}o.children&&Jt(o,t),r+=o?.widthInColumns??1,k.debug("abc88 columnsPos",o,r)}}k.debug(`layout blocks (<==layoutBlocks) ${e.id} x: ${e?.size?.x} y: ${e?.size?.y} width: ${e?.size?.width}`)}c(Jt,"layoutBlocks");function Qt(e,{minX:t,minY:s,maxX:n,maxY:i}={minX:0,minY:0,maxX:0,maxY:0}){if(e.size&&e.id!=="root"){let{x:r,y:a,width:l,height:o}=e.size;r-l/2<t&&(t=r-l/2),a-o/2<s&&(s=a-o/2),r+l/2>n&&(n=r+l/2),a+o/2>i&&(i=a+o/2)}if(e.children)for(let r of e.children)({minX:t,minY:s,maxX:n,maxY:i}=Qt(r,{minX:t,minY:s,maxX:n,maxY:i}));return{minX:t,minY:s,maxX:n,maxY:i}}c(Qt,"findBounds");function $t(e){let t=e.getBlock("root");if(!t)return;Lt(t,e,0,0),Jt(t,e),k.debug("getBlocks",JSON.stringify(t,null,2));let{minX:s,minY:n,maxX:i,maxY:r}=Qt(t),a=r-n,l=i-s;return{x:s,y:n,width:l,height:a}}c($t,"layout");function te(e,t){t&&e.attr("style",t)}c(te,"applyStyle");function hr(e){let t=C(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),s=t.append("xhtml:div"),n=e.label,i=e.isNode?"nodeLabel":"edgeLabel",r=s.append("span");return r.html(n),te(r,e.labelStyle),r.attr("class",i),te(s,e.labelStyle),s.style("display","inline-block"),s.style("white-space","nowrap"),s.attr("xmlns","http://www.w3.org/1999/xhtml"),t.node()}c(hr,"addHtmlLabel");var gr=c((e,t,s,n)=>{let i=e||"";if(typeof i=="object"&&(i=i[0]),P(B().flowchart.htmlLabels)){i=i.replace(/\\n|\n/g,"<br />"),k.debug("vertexText"+i);let r={isNode:n,label:Mt(et(i)),labelStyle:t.replace("fill:","color:")};return hr(r)}else{let r=document.createElementNS("http://www.w3.org/2000/svg","text");r.setAttribute("style",t.replace("color:","fill:"));let a=[];typeof i=="string"?a=i.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(i)?a=i:a=[];for(let l of a){let o=document.createElementNS("http://www.w3.org/2000/svg","tspan");o.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),o.setAttribute("dy","1em"),o.setAttribute("x","0"),s?o.setAttribute("class","title-row"):o.setAttribute("class","row"),o.textContent=l.trim(),r.appendChild(o)}return r}},"createLabel"),W=gr;var re=c((e,t,s,n,i)=>{t.arrowTypeStart&&ee(e,"start",t.arrowTypeStart,s,n,i),t.arrowTypeEnd&&ee(e,"end",t.arrowTypeEnd,s,n,i)},"addEdgeMarkers"),dr={arrow_cross:"cross",arrow_point:"point",arrow_barb:"barb",arrow_circle:"circle",aggregation:"aggregation",extension:"extension",composition:"composition",dependency:"dependency",lollipop:"lollipop"},ee=c((e,t,s,n,i,r)=>{let a=dr[s];if(!a){k.warn(`Unknown arrow type: ${s}`);return}let l=t==="start"?"Start":"End";e.attr(`marker-${t}`,`url(${n}#${i}_${r}-${a}${l})`)},"addEdgeMarker");var St={},M={};var se=c((e,t)=>{let s=B(),n=P(s.flowchart.htmlLabels),i=t.labelType==="markdown"?nt(e,t.label,{style:t.labelStyle,useHtmlLabels:n,addSvgBackground:!0},s):W(t.label,t.labelStyle),r=e.insert("g").attr("class","edgeLabel"),a=r.insert("g").attr("class","label");a.node().appendChild(i);let l=i.getBBox();if(n){let u=i.children[0],g=C(i);l=u.getBoundingClientRect(),g.attr("width",l.width),g.attr("height",l.height)}a.attr("transform","translate("+-l.width/2+", "+-l.height/2+")"),St[t.id]=r,t.width=l.width,t.height=l.height;let o;if(t.startLabelLeft){let u=W(t.startLabelLeft,t.labelStyle),g=e.insert("g").attr("class","edgeTerminals"),x=g.insert("g").attr("class","inner");o=x.node().appendChild(u);let m=u.getBBox();x.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"),M[t.id]||(M[t.id]={}),M[t.id].startLeft=g,ct(o,t.startLabelLeft)}if(t.startLabelRight){let u=W(t.startLabelRight,t.labelStyle),g=e.insert("g").attr("class","edgeTerminals"),x=g.insert("g").attr("class","inner");o=g.node().appendChild(u),x.node().appendChild(u);let m=u.getBBox();x.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"),M[t.id]||(M[t.id]={}),M[t.id].startRight=g,ct(o,t.startLabelRight)}if(t.endLabelLeft){let u=W(t.endLabelLeft,t.labelStyle),g=e.insert("g").attr("class","edgeTerminals"),x=g.insert("g").attr("class","inner");o=x.node().appendChild(u);let m=u.getBBox();x.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"),g.node().appendChild(u),M[t.id]||(M[t.id]={}),M[t.id].endLeft=g,ct(o,t.endLabelLeft)}if(t.endLabelRight){let u=W(t.endLabelRight,t.labelStyle),g=e.insert("g").attr("class","edgeTerminals"),x=g.insert("g").attr("class","inner");o=x.node().appendChild(u);let m=u.getBBox();x.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"),g.node().appendChild(u),M[t.id]||(M[t.id]={}),M[t.id].endRight=g,ct(o,t.endLabelRight)}return i},"insertEdgeLabel");function ct(e,t){B().flowchart.htmlLabels&&e&&(e.style.width=t.length*9+"px",e.style.height="12px")}c(ct,"setTerminalWidth");var ie=c((e,t)=>{k.debug("Moving label abc88 ",e.id,e.label,St[e.id],t);let s=t.updatedPath?t.updatedPath:t.originalPath,n=B(),{subGraphTitleTotalMargin:i}=Ft(n);if(e.label){let r=St[e.id],a=e.x,l=e.y;if(s){let o=tt.calcLabelPosition(s);k.debug("Moving label "+e.label+" from (",a,",",l,") to (",o.x,",",o.y,") abc88"),t.updatedPath&&(a=o.x,l=o.y)}r.attr("transform",`translate(${a}, ${l+i/2})`)}if(e.startLabelLeft){let r=M[e.id].startLeft,a=e.x,l=e.y;if(s){let o=tt.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_left",s);a=o.x,l=o.y}r.attr("transform",`translate(${a}, ${l})`)}if(e.startLabelRight){let r=M[e.id].startRight,a=e.x,l=e.y;if(s){let o=tt.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_right",s);a=o.x,l=o.y}r.attr("transform",`translate(${a}, ${l})`)}if(e.endLabelLeft){let r=M[e.id].endLeft,a=e.x,l=e.y;if(s){let o=tt.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_left",s);a=o.x,l=o.y}r.attr("transform",`translate(${a}, ${l})`)}if(e.endLabelRight){let r=M[e.id].endRight,a=e.x,l=e.y;if(s){let o=tt.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_right",s);a=o.x,l=o.y}r.attr("transform",`translate(${a}, ${l})`)}},"positionEdgeLabel"),ur=c((e,t)=>{let s=e.x,n=e.y,i=Math.abs(t.x-s),r=Math.abs(t.y-n),a=e.width/2,l=e.height/2;return i>=a||r>=l},"outsideNode"),pr=c((e,t,s)=>{k.debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(t)}
  insidePoint : ${JSON.stringify(s)}
  node        : x:${e.x} y:${e.y} w:${e.width} h:${e.height}`);let n=e.x,i=e.y,r=Math.abs(n-s.x),a=e.width/2,l=s.x<t.x?a-r:a+r,o=e.height/2,u=Math.abs(t.y-s.y),g=Math.abs(t.x-s.x);if(Math.abs(i-t.y)*a>Math.abs(n-t.x)*o){let x=s.y<t.y?t.y-o-i:i-o-t.y;l=g*x/u;let m={x:s.x<t.x?s.x+l:s.x-g+l,y:s.y<t.y?s.y+u-x:s.y-u+x};return l===0&&(m.x=t.x,m.y=t.y),g===0&&(m.x=t.x),u===0&&(m.y=t.y),k.debug(`abc89 topp/bott calc, Q ${u}, q ${x}, R ${g}, r ${l}`,m),m}else{s.x<t.x?l=t.x-a-n:l=n-a-t.x;let x=u*l/g,m=s.x<t.x?s.x+g-l:s.x-g+l,b=s.y<t.y?s.y+x:s.y-x;return k.debug(`sides calc abc89, Q ${u}, q ${x}, R ${g}, r ${l}`,{_x:m,_y:b}),l===0&&(m=t.x,b=t.y),g===0&&(m=t.x),u===0&&(b=t.y),{x:m,y:b}}},"intersection"),ae=c((e,t)=>{k.debug("abc88 cutPathAtIntersect",e,t);let s=[],n=e[0],i=!1;return e.forEach(r=>{if(!ur(t,r)&&!i){let a=pr(t,n,r),l=!1;s.forEach(o=>{l=l||o.x===a.x&&o.y===a.y}),s.some(o=>o.x===a.x&&o.y===a.y)||s.push(a),i=!0}else n=r,i||s.push(r)}),s},"cutPathAtIntersect"),ne=c(function(e,t,s,n,i,r,a){let l=s.points;k.debug("abc88 InsertEdge: edge=",s,"e=",t);let o=!1,u=r.node(t.v);var g=r.node(t.w);g?.intersect&&u?.intersect&&(l=l.slice(1,s.points.length-1),l.unshift(u.intersect(l[0])),l.push(g.intersect(l[l.length-1]))),s.toCluster&&(k.debug("to cluster abc88",n[s.toCluster]),l=ae(s.points,n[s.toCluster].node),o=!0),s.fromCluster&&(k.debug("from cluster abc88",n[s.fromCluster]),l=ae(l.reverse(),n[s.fromCluster].node).reverse(),o=!0);let x=l.filter(y=>!Number.isNaN(y.y)),m=At;s.curve&&(i==="graph"||i==="flowchart")&&(m=s.curve);let{x:b,y:L}=Wt(s),_=Rt().x(b).y(L).curve(m),S;switch(s.thickness){case"normal":S="edge-thickness-normal";break;case"thick":S="edge-thickness-thick";break;case"invisible":S="edge-thickness-thick";break;default:S=""}switch(s.pattern){case"solid":S+=" edge-pattern-solid";break;case"dotted":S+=" edge-pattern-dotted";break;case"dashed":S+=" edge-pattern-dashed";break}let I=e.append("path").attr("d",_(x)).attr("id",s.id).attr("class"," "+S+(s.classes?" "+s.classes:"")).attr("style",s.style),D="";(B().flowchart.arrowMarkerAbsolute||B().state.arrowMarkerAbsolute)&&(D=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,D=D.replace(/\(/g,"\\("),D=D.replace(/\)/g,"\\)")),re(I,s,D,a,i);let T={};return o&&(T.updatedPath=l),T.originalPath=s.points,T},"insertEdge");var fr=c(e=>{let t=new Set;for(let s of e)switch(s){case"x":t.add("right"),t.add("left");break;case"y":t.add("up"),t.add("down");break;default:t.add(s);break}return t},"expandAndDeduplicateDirections"),le=c((e,t,s)=>{let n=fr(e),i=2,r=t.height+2*s.padding,a=r/i,l=t.width+2*a+s.padding,o=s.padding/2;return n.has("right")&&n.has("left")&&n.has("up")&&n.has("down")?[{x:0,y:0},{x:a,y:0},{x:l/2,y:2*o},{x:l-a,y:0},{x:l,y:0},{x:l,y:-r/3},{x:l+2*o,y:-r/2},{x:l,y:-2*r/3},{x:l,y:-r},{x:l-a,y:-r},{x:l/2,y:-r-2*o},{x:a,y:-r},{x:0,y:-r},{x:0,y:-2*r/3},{x:-2*o,y:-r/2},{x:0,y:-r/3}]:n.has("right")&&n.has("left")&&n.has("up")?[{x:a,y:0},{x:l-a,y:0},{x:l,y:-r/2},{x:l-a,y:-r},{x:a,y:-r},{x:0,y:-r/2}]:n.has("right")&&n.has("left")&&n.has("down")?[{x:0,y:0},{x:a,y:-r},{x:l-a,y:-r},{x:l,y:0}]:n.has("right")&&n.has("up")&&n.has("down")?[{x:0,y:0},{x:l,y:-a},{x:l,y:-r+a},{x:0,y:-r}]:n.has("left")&&n.has("up")&&n.has("down")?[{x:l,y:0},{x:0,y:-a},{x:0,y:-r+a},{x:l,y:-r}]:n.has("right")&&n.has("left")?[{x:a,y:0},{x:a,y:-o},{x:l-a,y:-o},{x:l-a,y:0},{x:l,y:-r/2},{x:l-a,y:-r},{x:l-a,y:-r+o},{x:a,y:-r+o},{x:a,y:-r},{x:0,y:-r/2}]:n.has("up")&&n.has("down")?[{x:l/2,y:0},{x:0,y:-o},{x:a,y:-o},{x:a,y:-r+o},{x:0,y:-r+o},{x:l/2,y:-r},{x:l,y:-r+o},{x:l-a,y:-r+o},{x:l-a,y:-o},{x:l,y:-o}]:n.has("right")&&n.has("up")?[{x:0,y:0},{x:l,y:-a},{x:0,y:-r}]:n.has("right")&&n.has("down")?[{x:0,y:0},{x:l,y:0},{x:0,y:-r}]:n.has("left")&&n.has("up")?[{x:l,y:0},{x:0,y:-a},{x:l,y:-r}]:n.has("left")&&n.has("down")?[{x:l,y:0},{x:0,y:0},{x:l,y:-r}]:n.has("right")?[{x:a,y:-o},{x:a,y:-o},{x:l-a,y:-o},{x:l-a,y:0},{x:l,y:-r/2},{x:l-a,y:-r},{x:l-a,y:-r+o},{x:a,y:-r+o},{x:a,y:-r+o}]:n.has("left")?[{x:a,y:0},{x:a,y:-o},{x:l-a,y:-o},{x:l-a,y:-r+o},{x:a,y:-r+o},{x:a,y:-r},{x:0,y:-r/2}]:n.has("up")?[{x:a,y:-o},{x:a,y:-r+o},{x:0,y:-r+o},{x:l/2,y:-r},{x:l,y:-r+o},{x:l-a,y:-r+o},{x:l-a,y:-o}]:n.has("down")?[{x:l/2,y:0},{x:0,y:-o},{x:a,y:-o},{x:a,y:-r+o},{x:l-a,y:-r+o},{x:l-a,y:-o},{x:l,y:-o}]:[{x:0,y:0}]},"getArrowPoints");function xr(e,t){return e.intersect(t)}c(xr,"intersectNode");var oe=xr;function br(e,t,s,n){var i=e.x,r=e.y,a=i-n.x,l=r-n.y,o=Math.sqrt(t*t*l*l+s*s*a*a),u=Math.abs(t*s*a/o);n.x<i&&(u=-u);var g=Math.abs(t*s*l/o);return n.y<r&&(g=-g),{x:i+u,y:r+g}}c(br,"intersectEllipse");var ht=br;function yr(e,t,s){return ht(e,t,t,s)}c(yr,"intersectCircle");var ce=yr;function mr(e,t,s,n){var i,r,a,l,o,u,g,x,m,b,L,_,S,I,D;if(i=t.y-e.y,a=e.x-t.x,o=t.x*e.y-e.x*t.y,m=i*s.x+a*s.y+o,b=i*n.x+a*n.y+o,!(m!==0&&b!==0&&he(m,b))&&(r=n.y-s.y,l=s.x-n.x,u=n.x*s.y-s.x*n.y,g=r*e.x+l*e.y+u,x=r*t.x+l*t.y+u,!(g!==0&&x!==0&&he(g,x))&&(L=i*l-r*a,L!==0)))return _=Math.abs(L/2),S=a*u-l*o,I=S<0?(S-_)/L:(S+_)/L,S=r*o-i*u,D=S<0?(S-_)/L:(S+_)/L,{x:I,y:D}}c(mr,"intersectLine");function he(e,t){return e*t>0}c(he,"sameSign");var ge=mr;var de=kr;function kr(e,t,s){var n=e.x,i=e.y,r=[],a=Number.POSITIVE_INFINITY,l=Number.POSITIVE_INFINITY;typeof t.forEach=="function"?t.forEach(function(L){a=Math.min(a,L.x),l=Math.min(l,L.y)}):(a=Math.min(a,t.x),l=Math.min(l,t.y));for(var o=n-e.width/2-a,u=i-e.height/2-l,g=0;g<t.length;g++){var x=t[g],m=t[g<t.length-1?g+1:0],b=ge(e,s,{x:o+x.x,y:u+x.y},{x:o+m.x,y:u+m.y});b&&r.push(b)}return r.length?(r.length>1&&r.sort(function(L,_){var S=L.x-s.x,I=L.y-s.y,D=Math.sqrt(S*S+I*I),T=_.x-s.x,y=_.y-s.y,d=Math.sqrt(T*T+y*y);return D<d?-1:D===d?0:1}),r[0]):e}c(kr,"intersectPolygon");var wr=c((e,t)=>{var s=e.x,n=e.y,i=t.x-s,r=t.y-n,a=e.width/2,l=e.height/2,o,u;return Math.abs(r)*a>Math.abs(i)*l?(r<0&&(l=-l),o=r===0?0:l*i/r,u=l):(i<0&&(a=-a),o=a,u=i===0?0:a*r/i),{x:s+o,y:n+u}},"intersectRect"),ue=wr;var v={node:oe,circle:ce,ellipse:ht,polygon:de,rect:ue};var A=c(async(e,t,s,n)=>{let i=B(),r,a=t.useHtmlLabels||P(i.flowchart.htmlLabels);s?r=s:r="node default";let l=e.insert("g").attr("class",r).attr("id",t.domId||t.id),o=l.insert("g").attr("class","label").attr("style",t.labelStyle),u;t.labelText===void 0?u="":u=typeof t.labelText=="string"?t.labelText:t.labelText[0];let g=o.node(),x;t.labelType==="markdown"?x=nt(o,bt(et(u),i),{useHtmlLabels:a,width:t.width||i.flowchart.wrappingWidth,classes:"markdown-node-label"},i):x=g.appendChild(W(bt(et(u),i),t.labelStyle,!1,n));let m=x.getBBox(),b=t.padding/2;if(P(i.flowchart.htmlLabels)){let L=x.children[0],_=C(x),S=L.getElementsByTagName("img");if(S){let I=u.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...S].map(D=>new Promise(T=>{function y(){if(D.style.display="flex",D.style.flexDirection="column",I){let d=i.fontSize?i.fontSize:window.getComputedStyle(document.body).fontSize,w=parseInt(d,10)*5+"px";D.style.minWidth=w,D.style.maxWidth=w}else D.style.width="100%";T(D)}c(y,"setupImage"),setTimeout(()=>{D.complete&&y()}),D.addEventListener("error",y),D.addEventListener("load",y)})))}m=L.getBoundingClientRect(),_.attr("width",m.width),_.attr("height",m.height)}return a?o.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"):o.attr("transform","translate(0, "+-m.height/2+")"),t.centerLabel&&o.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"),o.insert("rect",":first-child"),{shapeSvg:l,bbox:m,halfPadding:b,label:o}},"labelHelper"),N=c((e,t)=>{let s=t.node().getBBox();e.width=s.width,e.height=s.height},"updateNodeBounds");function U(e,t,s,n){return e.insert("polygon",":first-child").attr("points",n.map(function(i){return i.x+","+i.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-t/2+","+s/2+")")}c(U,"insertPolygonShape");var Lr=c(async(e,t)=>{t.useHtmlLabels||B().flowchart.htmlLabels||(t.centerLabel=!0);let{shapeSvg:n,bbox:i,halfPadding:r}=await A(e,t,"node "+t.classes,!0);k.info("Classes = ",t.classes);let a=n.insert("rect",":first-child");return a.attr("rx",t.rx).attr("ry",t.ry).attr("x",-i.width/2-r).attr("y",-i.height/2-r).attr("width",i.width+t.padding).attr("height",i.height+t.padding),N(t,a),t.intersect=function(l){return v.rect(t,l)},n},"note"),pe=Lr;var fe=c(e=>e?" "+e:"","formatClass"),j=c((e,t)=>`${t||"node default"}${fe(e.classes)} ${fe(e.class)}`,"getClassesFromNode"),xe=c(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,j(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=i+r,l=[{x:a/2,y:0},{x:a,y:-a/2},{x:a/2,y:-a},{x:0,y:-a/2}];k.info("Question main (Circle)");let o=U(s,a,a,l);return o.attr("style",t.style),N(t,o),t.intersect=function(u){return k.warn("Intersect called"),v.polygon(t,l,u)},s},"question"),Sr=c((e,t)=>{let s=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),n=28,i=[{x:0,y:n/2},{x:n/2,y:0},{x:0,y:-n/2},{x:-n/2,y:0}];return s.insert("polygon",":first-child").attr("points",i.map(function(a){return a.x+","+a.y}).join(" ")).attr("class","state-start").attr("r",7).attr("width",28).attr("height",28),t.width=28,t.height=28,t.intersect=function(a){return v.circle(t,14,a)},s},"choice"),Er=c(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,j(t,void 0),!0),i=4,r=n.height+t.padding,a=r/i,l=n.width+2*a+t.padding,o=[{x:a,y:0},{x:l-a,y:0},{x:l,y:-r/2},{x:l-a,y:-r},{x:a,y:-r},{x:0,y:-r/2}],u=U(s,l,r,o);return u.attr("style",t.style),N(t,u),t.intersect=function(g){return v.polygon(t,o,g)},s},"hexagon"),_r=c(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,void 0,!0),i=2,r=n.height+2*t.padding,a=r/i,l=n.width+2*a+t.padding,o=le(t.directions,n,t),u=U(s,l,r,o);return u.attr("style",t.style),N(t,u),t.intersect=function(g){return v.polygon(t,o,g)},s},"block_arrow"),Dr=c(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,j(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:-r/2,y:0},{x:i,y:0},{x:i,y:-r},{x:-r/2,y:-r},{x:0,y:-r/2}];return U(s,i,r,a).attr("style",t.style),t.width=i+r,t.height=r,t.intersect=function(o){return v.polygon(t,a,o)},s},"rect_left_inv_arrow"),Br=c(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,j(t),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:-2*r/6,y:0},{x:i-r/6,y:0},{x:i+2*r/6,y:-r},{x:r/6,y:-r}],l=U(s,i,r,a);return l.attr("style",t.style),N(t,l),t.intersect=function(o){return v.polygon(t,a,o)},s},"lean_right"),vr=c(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,j(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:2*r/6,y:0},{x:i+r/6,y:0},{x:i-2*r/6,y:-r},{x:-r/6,y:-r}],l=U(s,i,r,a);return l.attr("style",t.style),N(t,l),t.intersect=function(o){return v.polygon(t,a,o)},s},"lean_left"),Tr=c(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,j(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:-2*r/6,y:0},{x:i+2*r/6,y:0},{x:i-r/6,y:-r},{x:r/6,y:-r}],l=U(s,i,r,a);return l.attr("style",t.style),N(t,l),t.intersect=function(o){return v.polygon(t,a,o)},s},"trapezoid"),Cr=c(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,j(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:r/6,y:0},{x:i-r/6,y:0},{x:i+2*r/6,y:-r},{x:-2*r/6,y:-r}],l=U(s,i,r,a);return l.attr("style",t.style),N(t,l),t.intersect=function(o){return v.polygon(t,a,o)},s},"inv_trapezoid"),Nr=c(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,j(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:0,y:0},{x:i+r/2,y:0},{x:i,y:-r/2},{x:i+r/2,y:-r},{x:0,y:-r}],l=U(s,i,r,a);return l.attr("style",t.style),N(t,l),t.intersect=function(o){return v.polygon(t,a,o)},s},"rect_right_inv_arrow"),Ir=c(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,j(t,void 0),!0),i=n.width+t.padding,r=i/2,a=r/(2.5+i/50),l=n.height+a+t.padding,o="M 0,"+a+" a "+r+","+a+" 0,0,0 "+i+" 0 a "+r+","+a+" 0,0,0 "+-i+" 0 l 0,"+l+" a "+r+","+a+" 0,0,0 "+i+" 0 l 0,"+-l,u=s.attr("label-offset-y",a).insert("path",":first-child").attr("style",t.style).attr("d",o).attr("transform","translate("+-i/2+","+-(l/2+a)+")");return N(t,u),t.intersect=function(g){let x=v.rect(t,g),m=x.x-t.x;if(r!=0&&(Math.abs(m)<t.width/2||Math.abs(m)==t.width/2&&Math.abs(x.y-t.y)>t.height/2-a)){let b=a*a*(1-m*m/(r*r));b!=0&&(b=Math.sqrt(b)),b=a-b,g.y-t.y>0&&(b=-b),x.y+=b}return x},s},"cylinder"),Or=c(async(e,t)=>{let{shapeSvg:s,bbox:n,halfPadding:i}=await A(e,t,"node "+t.classes+" "+t.class,!0),r=s.insert("rect",":first-child"),a=t.positioned?t.width:n.width+t.padding,l=t.positioned?t.height:n.height+t.padding,o=t.positioned?-a/2:-n.width/2-i,u=t.positioned?-l/2:-n.height/2-i;if(r.attr("class","basic label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",o).attr("y",u).attr("width",a).attr("height",l),t.props){let g=new Set(Object.keys(t.props));t.props.borders&&(Et(r,t.props.borders,a,l),g.delete("borders")),g.forEach(x=>{k.warn(`Unknown node property ${x}`)})}return N(t,r),t.intersect=function(g){return v.rect(t,g)},s},"rect"),Rr=c(async(e,t)=>{let{shapeSvg:s,bbox:n,halfPadding:i}=await A(e,t,"node "+t.classes,!0),r=s.insert("rect",":first-child"),a=t.positioned?t.width:n.width+t.padding,l=t.positioned?t.height:n.height+t.padding,o=t.positioned?-a/2:-n.width/2-i,u=t.positioned?-l/2:-n.height/2-i;if(r.attr("class","basic cluster composite label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",o).attr("y",u).attr("width",a).attr("height",l),t.props){let g=new Set(Object.keys(t.props));t.props.borders&&(Et(r,t.props.borders,a,l),g.delete("borders")),g.forEach(x=>{k.warn(`Unknown node property ${x}`)})}return N(t,r),t.intersect=function(g){return v.rect(t,g)},s},"composite"),Ar=c(async(e,t)=>{let{shapeSvg:s}=await A(e,t,"label",!0);k.trace("Classes = ",t.class);let n=s.insert("rect",":first-child"),i=0,r=0;if(n.attr("width",i).attr("height",r),s.attr("class","label edgeLabel"),t.props){let a=new Set(Object.keys(t.props));t.props.borders&&(Et(n,t.props.borders,i,r),a.delete("borders")),a.forEach(l=>{k.warn(`Unknown node property ${l}`)})}return N(t,n),t.intersect=function(a){return v.rect(t,a)},s},"labelRect");function Et(e,t,s,n){let i=[],r=c(l=>{i.push(l,0)},"addBorder"),a=c(l=>{i.push(0,l)},"skipBorder");t.includes("t")?(k.debug("add top border"),r(s)):a(s),t.includes("r")?(k.debug("add right border"),r(n)):a(n),t.includes("b")?(k.debug("add bottom border"),r(s)):a(s),t.includes("l")?(k.debug("add left border"),r(n)):a(n),e.attr("stroke-dasharray",i.join(" "))}c(Et,"applyNodePropertyBorders");var zr=c((e,t)=>{let s;t.classes?s="node "+t.classes:s="node default";let n=e.insert("g").attr("class",s).attr("id",t.domId||t.id),i=n.insert("rect",":first-child"),r=n.insert("line"),a=n.insert("g").attr("class","label"),l=t.labelText.flat?t.labelText.flat():t.labelText,o="";typeof l=="object"?o=l[0]:o=l,k.info("Label text abc79",o,l,typeof l=="object");let u=a.node().appendChild(W(o,t.labelStyle,!0,!0)),g={width:0,height:0};if(P(B().flowchart.htmlLabels)){let _=u.children[0],S=C(u);g=_.getBoundingClientRect(),S.attr("width",g.width),S.attr("height",g.height)}k.info("Text 2",l);let x=l.slice(1,l.length),m=u.getBBox(),b=a.node().appendChild(W(x.join?x.join("<br/>"):x,t.labelStyle,!0,!0));if(P(B().flowchart.htmlLabels)){let _=b.children[0],S=C(b);g=_.getBoundingClientRect(),S.attr("width",g.width),S.attr("height",g.height)}let L=t.padding/2;return C(b).attr("transform","translate( "+(g.width>m.width?0:(m.width-g.width)/2)+", "+(m.height+L+5)+")"),C(u).attr("transform","translate( "+(g.width<m.width?0:-(m.width-g.width)/2)+", 0)"),g=a.node().getBBox(),a.attr("transform","translate("+-g.width/2+", "+(-g.height/2-L+3)+")"),i.attr("class","outer title-state").attr("x",-g.width/2-L).attr("y",-g.height/2-L).attr("width",g.width+t.padding).attr("height",g.height+t.padding),r.attr("class","divider").attr("x1",-g.width/2-L).attr("x2",g.width/2+L).attr("y1",-g.height/2-L+m.height+L).attr("y2",-g.height/2-L+m.height+L),N(t,i),t.intersect=function(_){return v.rect(t,_)},n},"rectWithTitle"),Mr=c(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,j(t,void 0),!0),i=n.height+t.padding,r=n.width+i/4+t.padding,a=s.insert("rect",":first-child").attr("style",t.style).attr("rx",i/2).attr("ry",i/2).attr("x",-r/2).attr("y",-i/2).attr("width",r).attr("height",i);return N(t,a),t.intersect=function(l){return v.rect(t,l)},s},"stadium"),Fr=c(async(e,t)=>{let{shapeSvg:s,bbox:n,halfPadding:i}=await A(e,t,j(t,void 0),!0),r=s.insert("circle",":first-child");return r.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",n.width/2+i).attr("width",n.width+t.padding).attr("height",n.height+t.padding),k.info("Circle main"),N(t,r),t.intersect=function(a){return k.info("Circle intersect",t,n.width/2+i,a),v.circle(t,n.width/2+i,a)},s},"circle"),Wr=c(async(e,t)=>{let{shapeSvg:s,bbox:n,halfPadding:i}=await A(e,t,j(t,void 0),!0),r=5,a=s.insert("g",":first-child"),l=a.insert("circle"),o=a.insert("circle");return a.attr("class",t.class),l.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",n.width/2+i+r).attr("width",n.width+t.padding+r*2).attr("height",n.height+t.padding+r*2),o.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",n.width/2+i).attr("width",n.width+t.padding).attr("height",n.height+t.padding),k.info("DoubleCircle main"),N(t,l),t.intersect=function(u){return k.info("DoubleCircle intersect",t,n.width/2+i+r,u),v.circle(t,n.width/2+i+r,u)},s},"doublecircle"),Pr=c(async(e,t)=>{let{shapeSvg:s,bbox:n}=await A(e,t,j(t,void 0),!0),i=n.width+t.padding,r=n.height+t.padding,a=[{x:0,y:0},{x:i,y:0},{x:i,y:-r},{x:0,y:-r},{x:0,y:0},{x:-8,y:0},{x:i+8,y:0},{x:i+8,y:-r},{x:-8,y:-r},{x:-8,y:0}],l=U(s,i,r,a);return l.attr("style",t.style),N(t,l),t.intersect=function(o){return v.polygon(t,a,o)},s},"subroutine"),Yr=c((e,t)=>{let s=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),n=s.insert("circle",":first-child");return n.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),N(t,n),t.intersect=function(i){return v.circle(t,7,i)},s},"start"),be=c((e,t,s)=>{let n=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),i=70,r=10;s==="LR"&&(i=10,r=70);let a=n.append("rect").attr("x",-1*i/2).attr("y",-1*r/2).attr("width",i).attr("height",r).attr("class","fork-join");return N(t,a),t.height=t.height+t.padding/2,t.width=t.width+t.padding/2,t.intersect=function(l){return v.rect(t,l)},n},"forkJoin"),Hr=c((e,t)=>{let s=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),n=s.insert("circle",":first-child"),i=s.insert("circle",":first-child");return i.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),n.attr("class","state-end").attr("r",5).attr("width",10).attr("height",10),N(t,i),t.intersect=function(r){return v.circle(t,7,r)},s},"end"),jr=c((e,t)=>{let s=t.padding/2,n=4,i=8,r;t.classes?r="node "+t.classes:r="node default";let a=e.insert("g").attr("class",r).attr("id",t.domId||t.id),l=a.insert("rect",":first-child"),o=a.insert("line"),u=a.insert("line"),g=0,x=n,m=a.insert("g").attr("class","label"),b=0,L=t.classData.annotations?.[0],_=t.classData.annotations[0]?"\xAB"+t.classData.annotations[0]+"\xBB":"",S=m.node().appendChild(W(_,t.labelStyle,!0,!0)),I=S.getBBox();if(P(B().flowchart.htmlLabels)){let E=S.children[0],h=C(S);I=E.getBoundingClientRect(),h.attr("width",I.width),h.attr("height",I.height)}t.classData.annotations[0]&&(x+=I.height+n,g+=I.width);let D=t.classData.label;t.classData.type!==void 0&&t.classData.type!==""&&(B().flowchart.htmlLabels?D+="&lt;"+t.classData.type+"&gt;":D+="<"+t.classData.type+">");let T=m.node().appendChild(W(D,t.labelStyle,!0,!0));C(T).attr("class","classTitle");let y=T.getBBox();if(P(B().flowchart.htmlLabels)){let E=T.children[0],h=C(T);y=E.getBoundingClientRect(),h.attr("width",y.width),h.attr("height",y.height)}x+=y.height+n,y.width>g&&(g=y.width);let d=[];t.classData.members.forEach(E=>{let h=E.getDisplayDetails(),Y=h.displayText;B().flowchart.htmlLabels&&(Y=Y.replace(/</g,"&lt;").replace(/>/g,"&gt;"));let p=m.node().appendChild(W(Y,h.cssStyle?h.cssStyle:t.labelStyle,!0,!0)),R=p.getBBox();if(P(B().flowchart.htmlLabels)){let Z=p.children[0],V=C(p);R=Z.getBoundingClientRect(),V.attr("width",R.width),V.attr("height",R.height)}R.width>g&&(g=R.width),x+=R.height+n,d.push(p)}),x+=i;let f=[];if(t.classData.methods.forEach(E=>{let h=E.getDisplayDetails(),Y=h.displayText;B().flowchart.htmlLabels&&(Y=Y.replace(/</g,"&lt;").replace(/>/g,"&gt;"));let p=m.node().appendChild(W(Y,h.cssStyle?h.cssStyle:t.labelStyle,!0,!0)),R=p.getBBox();if(P(B().flowchart.htmlLabels)){let Z=p.children[0],V=C(p);R=Z.getBoundingClientRect(),V.attr("width",R.width),V.attr("height",R.height)}R.width>g&&(g=R.width),x+=R.height+n,f.push(p)}),x+=i,L){let E=(g-I.width)/2;C(S).attr("transform","translate( "+(-1*g/2+E)+", "+-1*x/2+")"),b=I.height+n}let w=(g-y.width)/2;return C(T).attr("transform","translate( "+(-1*g/2+w)+", "+(-1*x/2+b)+")"),b+=y.height+n,o.attr("class","divider").attr("x1",-g/2-s).attr("x2",g/2+s).attr("y1",-x/2-s+i+b).attr("y2",-x/2-s+i+b),b+=i,d.forEach(E=>{C(E).attr("transform","translate( "+-g/2+", "+(-1*x/2+b+i/2)+")");let h=E?.getBBox();b+=(h?.height??0)+n}),b+=i,u.attr("class","divider").attr("x1",-g/2-s).attr("x2",g/2+s).attr("y1",-x/2-s+i+b).attr("y2",-x/2-s+i+b),b+=i,f.forEach(E=>{C(E).attr("transform","translate( "+-g/2+", "+(-1*x/2+b)+")");let h=E?.getBBox();b+=(h?.height??0)+n}),l.attr("style",t.style).attr("class","outer title-state").attr("x",-g/2-s).attr("y",-(x/2)-s).attr("width",g+t.padding).attr("height",x+t.padding),N(t,l),t.intersect=function(E){return v.rect(t,E)},a},"class_box"),ye={rhombus:xe,composite:Rr,question:xe,rect:Or,labelRect:Ar,rectWithTitle:zr,choice:Sr,circle:Fr,doublecircle:Wr,stadium:Mr,hexagon:Er,block_arrow:_r,rect_left_inv_arrow:Dr,lean_right:Br,lean_left:vr,trapezoid:Tr,inv_trapezoid:Cr,rect_right_inv_arrow:Nr,cylinder:Ir,start:Yr,end:Hr,note:pe,subroutine:Pr,fork:be,join:be,class_box:jr},gt={},_t=c(async(e,t,s)=>{let n,i;if(t.link){let r;B().securityLevel==="sandbox"?r="_top":t.linkTarget&&(r=t.linkTarget||"_blank"),n=e.insert("svg:a").attr("xlink:href",t.link).attr("target",r),i=await ye[t.shape](n,t,s)}else i=await ye[t.shape](e,t,s),n=i;return t.tooltip&&i.attr("title",t.tooltip),t.class&&i.attr("class","node default "+t.class),gt[t.id]=n,t.haveCallback&&gt[t.id].attr("class",gt[t.id].attr("class")+" clickable"),n},"insertNode");var me=c(e=>{let t=gt[e.id];k.trace("Transforming node",e.diff,e,"translate("+(e.x-e.width/2-5)+", "+e.width/2+")");let s=8,n=e.diff||0;return e.clusterNode?t.attr("transform","translate("+(e.x+n-e.width/2)+", "+(e.y-e.height/2-s)+")"):t.attr("transform","translate("+e.x+", "+e.y+")"),n},"positionNode");function ke(e,t,s=!1){let n=e,i="default";(n?.classes?.length||0)>0&&(i=(n?.classes??[]).join(" ")),i=i+" flowchart-label";let r=0,a="",l;switch(n.type){case"round":r=5,a="rect";break;case"composite":r=0,a="composite",l=0;break;case"square":a="rect";break;case"diamond":a="question";break;case"hexagon":a="hexagon";break;case"block_arrow":a="block_arrow";break;case"odd":a="rect_left_inv_arrow";break;case"lean_right":a="lean_right";break;case"lean_left":a="lean_left";break;case"trapezoid":a="trapezoid";break;case"inv_trapezoid":a="inv_trapezoid";break;case"rect_left_inv_arrow":a="rect_left_inv_arrow";break;case"circle":a="circle";break;case"ellipse":a="ellipse";break;case"stadium":a="stadium";break;case"subroutine":a="subroutine";break;case"cylinder":a="cylinder";break;case"group":a="rect";break;case"doublecircle":a="doublecircle";break;default:a="rect"}let o=zt(n?.styles??[]),u=n.label,g=n.size??{width:0,height:0,x:0,y:0};return{labelStyle:o.labelStyle,shape:a,labelText:u,rx:r,ry:r,class:i,style:o.style,id:n.id,directions:n.directions,width:g.width,height:g.height,x:g.x,y:g.y,positioned:s,intersect:void 0,type:n.type,padding:l??q()?.block?.padding??0}}c(ke,"getNodeFromBlock");async function Kr(e,t,s){let n=ke(t,s,!1);if(n.type==="group")return;let i=q(),r=await _t(e,n,{config:i}),a=r.node().getBBox(),l=s.getBlock(n.id);l.size={width:a.width,height:a.height,x:0,y:0,node:r},s.setBlock(l),r.remove()}c(Kr,"calculateBlockSize");async function Xr(e,t,s){let n=ke(t,s,!0);if(s.getBlock(n.id).type!=="space"){let r=q();await _t(e,n,{config:r}),t.intersect=n?.intersect,me(n)}}c(Xr,"insertBlockPositioned");async function Dt(e,t,s,n){for(let i of t)await n(e,i,s),i.children&&await Dt(e,i.children,s,n)}c(Dt,"performOperations");async function we(e,t,s){await Dt(e,t,s,Kr)}c(we,"calculateBlockSizes");async function Le(e,t,s){await Dt(e,t,s,Xr)}c(Le,"insertBlocks");async function Se(e,t,s,n,i){let r=new Yt({multigraph:!0,compound:!0});r.setGraph({rankdir:"TB",nodesep:10,ranksep:10,marginx:8,marginy:8});for(let a of s)a.size&&r.setNode(a.id,{width:a.size.width,height:a.size.height,intersect:a.intersect});for(let a of t)if(a.start&&a.end){let l=n.getBlock(a.start),o=n.getBlock(a.end);if(l?.size&&o?.size){let u=l.size,g=o.size,x=[{x:u.x,y:u.y},{x:u.x+(g.x-u.x)/2,y:u.y+(g.y-u.y)/2},{x:g.x,y:g.y}];ne(e,{v:a.start,w:a.end,name:a.id},{...a,arrowTypeEnd:a.arrowTypeEnd,arrowTypeStart:a.arrowTypeStart,points:x,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"},void 0,"block",r,i),a.label&&(await se(e,{...a,label:a.label,labelStyle:"stroke: #333; stroke-width: 1.5px;fill:none;",arrowTypeEnd:a.arrowTypeEnd,arrowTypeStart:a.arrowTypeStart,points:x,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"}),ie({...a,x:x[1].x,y:x[1].y},{originalPath:x}))}}}c(Se,"insertEdges");var Ur=c(function(e,t){return t.db.getClasses()},"getClasses"),Vr=c(async function(e,t,s,n){let{securityLevel:i,block:r}=q(),a=n.db,l;i==="sandbox"&&(l=C("#i"+t));let o=i==="sandbox"?C(l.nodes()[0].contentDocument.body):C("body"),u=i==="sandbox"?o.select(`[id="${t}"]`):C(`[id="${t}"]`);qt(u,["point","circle","cross"],n.type,t);let x=a.getBlocks(),m=a.getBlocksFlat(),b=a.getEdges(),L=u.insert("g").attr("class","block");await we(L,x,a);let _=$t(a);if(await Le(L,x,a),await Se(L,b,m,a,t),_){let S=_,I=Math.max(1,Math.round(.125*(S.width/S.height))),D=S.height+I+10,T=S.width+10,{useMaxWidth:y}=r;It(u,D,T,!!y),k.debug("Here Bounds",_,S),u.attr("viewBox",`${S.x-5} ${S.y-5} ${S.width+10} ${S.height+10}`)}},"draw"),Ee={draw:Vr,getClasses:Ur};var qs={parser:Ht,db:Gt,renderer:Ee,styles:Zt};export{qs as diagram};
