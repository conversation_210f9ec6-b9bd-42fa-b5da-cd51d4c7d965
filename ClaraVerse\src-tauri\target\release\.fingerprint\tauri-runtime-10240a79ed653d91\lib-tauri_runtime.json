{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2040997289075261528, "path": 4231087634136942338, "deps": [[2671782512663819132, "tauri_utils", false, 15373204914832234626], [3150220818285335163, "url", false, 8907747236312430643], [4143744114649553716, "raw_window_handle", false, 9360050597165366007], [6089812615193535349, "build_script_build", false, 5582424082317112657], [7606335748176206944, "dpi", false, 17509326160235088657], [9010263965687315507, "http", false, 9326335740251934562], [9689903380558560274, "serde", false, 3755520848713314912], [10806645703491011684, "thiserror", false, 1866175186105377659], [14585479307175734061, "windows", false, 13145212145427509429], [15367738274754116744, "serde_json", false, 12024906575928109641], [16727543399706004146, "cookie", false, 4169221481605503526]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-10240a79ed653d91\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}