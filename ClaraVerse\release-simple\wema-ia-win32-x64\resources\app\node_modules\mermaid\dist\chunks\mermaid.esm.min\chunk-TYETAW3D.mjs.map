{"version": 3, "sources": ["../../../../parser/dist/mermaid-parser.core.mjs"], "sourcesContent": ["import {\n  GitGraphModule,\n  createGitGraphServices\n} from \"./chunks/mermaid-parser.core/chunk-2NYFTIL2.mjs\";\nimport {\n  InfoModule,\n  createInfoServices\n} from \"./chunks/mermaid-parser.core/chunk-EXZZNE6F.mjs\";\nimport {\n  PacketModule,\n  createPacketServices\n} from \"./chunks/mermaid-parser.core/chunk-V4Q32G6S.mjs\";\nimport {\n  PieModule,\n  createPieServices\n} from \"./chunks/mermaid-parser.core/chunk-ROXG7S4E.mjs\";\nimport {\n  ArchitectureModule,\n  createArchitectureServices\n} from \"./chunks/mermaid-parser.core/chunk-C4OEIS7N.mjs\";\nimport {\n  RadarModule,\n  createRadarServices\n} from \"./chunks/mermaid-parser.core/chunk-2O5ZK7RR.mjs\";\nimport {\n  AbstractMermaidTokenBuilder,\n  AbstractMermaidValueConverter,\n  Architecture,\n  ArchitectureGeneratedModule,\n  Branch,\n  Commit,\n  CommonTokenBuilder,\n  CommonValueConverter,\n  GitGraph,\n  GitGraphGeneratedModule,\n  Info,\n  InfoGeneratedModule,\n  Merge,\n  MermaidGeneratedSharedModule,\n  Packet,\n  PacketBlock,\n  PacketGeneratedModule,\n  Pie,\n  PieGeneratedModule,\n  PieSection,\n  Radar,\n  RadarGeneratedModule,\n  Statement,\n  __name,\n  isArchitecture,\n  isBranch,\n  isCommit,\n  isCommon,\n  isGitGraph,\n  isInfo,\n  isMerge,\n  isPacket,\n  isPacketBlock,\n  isPie,\n  isPieSection\n} from \"./chunks/mermaid-parser.core/chunk-7PKI6E2E.mjs\";\n\n// src/parse.ts\nvar parsers = {};\nvar initializers = {\n  info: /* @__PURE__ */ __name(async () => {\n    const { createInfoServices: createInfoServices2 } = await import(\"./chunks/mermaid-parser.core/info-4N47QTOZ.mjs\");\n    const parser = createInfoServices2().Info.parser.LangiumParser;\n    parsers.info = parser;\n  }, \"info\"),\n  packet: /* @__PURE__ */ __name(async () => {\n    const { createPacketServices: createPacketServices2 } = await import(\"./chunks/mermaid-parser.core/packet-KVYON367.mjs\");\n    const parser = createPacketServices2().Packet.parser.LangiumParser;\n    parsers.packet = parser;\n  }, \"packet\"),\n  pie: /* @__PURE__ */ __name(async () => {\n    const { createPieServices: createPieServices2 } = await import(\"./chunks/mermaid-parser.core/pie-R6RNRRYF.mjs\");\n    const parser = createPieServices2().Pie.parser.LangiumParser;\n    parsers.pie = parser;\n  }, \"pie\"),\n  architecture: /* @__PURE__ */ __name(async () => {\n    const { createArchitectureServices: createArchitectureServices2 } = await import(\"./chunks/mermaid-parser.core/architecture-4AB2E3PP.mjs\");\n    const parser = createArchitectureServices2().Architecture.parser.LangiumParser;\n    parsers.architecture = parser;\n  }, \"architecture\"),\n  gitGraph: /* @__PURE__ */ __name(async () => {\n    const { createGitGraphServices: createGitGraphServices2 } = await import(\"./chunks/mermaid-parser.core/gitGraph-O2Q2CXLX.mjs\");\n    const parser = createGitGraphServices2().GitGraph.parser.LangiumParser;\n    parsers.gitGraph = parser;\n  }, \"gitGraph\"),\n  radar: /* @__PURE__ */ __name(async () => {\n    const { createRadarServices: createRadarServices2 } = await import(\"./chunks/mermaid-parser.core/radar-MK3ICKWK.mjs\");\n    const parser = createRadarServices2().Radar.parser.LangiumParser;\n    parsers.radar = parser;\n  }, \"radar\")\n};\nasync function parse(diagramType, text) {\n  const initializer = initializers[diagramType];\n  if (!initializer) {\n    throw new Error(`Unknown diagram type: ${diagramType}`);\n  }\n  if (!parsers[diagramType]) {\n    await initializer();\n  }\n  const parser = parsers[diagramType];\n  const result = parser.parse(text);\n  if (result.lexerErrors.length > 0 || result.parserErrors.length > 0) {\n    throw new MermaidParseError(result);\n  }\n  return result.value;\n}\n__name(parse, \"parse\");\nvar MermaidParseError = class extends Error {\n  constructor(result) {\n    const lexerErrors = result.lexerErrors.map((err) => err.message).join(\"\\n\");\n    const parserErrors = result.parserErrors.map((err) => err.message).join(\"\\n\");\n    super(`Parsing failed: ${lexerErrors} ${parserErrors}`);\n    this.result = result;\n  }\n  static {\n    __name(this, \"MermaidParseError\");\n  }\n};\nexport {\n  AbstractMermaidTokenBuilder,\n  AbstractMermaidValueConverter,\n  Architecture,\n  ArchitectureGeneratedModule,\n  ArchitectureModule,\n  Branch,\n  Commit,\n  CommonTokenBuilder,\n  CommonValueConverter,\n  GitGraph,\n  GitGraphGeneratedModule,\n  GitGraphModule,\n  Info,\n  InfoGeneratedModule,\n  InfoModule,\n  Merge,\n  MermaidGeneratedSharedModule,\n  MermaidParseError,\n  Packet,\n  PacketBlock,\n  PacketGeneratedModule,\n  PacketModule,\n  Pie,\n  PieGeneratedModule,\n  PieModule,\n  PieSection,\n  Radar,\n  RadarGeneratedModule,\n  RadarModule,\n  Statement,\n  createArchitectureServices,\n  createGitGraphServices,\n  createInfoServices,\n  createPacketServices,\n  createPieServices,\n  createRadarServices,\n  isArchitecture,\n  isBranch,\n  isCommit,\n  isCommon,\n  isGitGraph,\n  isInfo,\n  isMerge,\n  isPacket,\n  isPacketBlock,\n  isPie,\n  isPieSection,\n  parse\n};\n"], "mappings": "kFA+DA,IAAIA,EAAU,CAAC,EACXC,EAAe,CACjB,KAAsBC,EAAO,SAAY,CACvC,GAAM,CAAE,mBAAoBC,CAAoB,EAAI,KAAM,QAAO,8BAAgD,EAC3GC,EAASD,EAAoB,EAAE,KAAK,OAAO,cACjDH,EAAQ,KAAOI,CACjB,EAAG,MAAM,EACT,OAAwBF,EAAO,SAAY,CACzC,GAAM,CAAE,qBAAsBG,CAAsB,EAAI,KAAM,QAAO,gCAAkD,EACjHD,EAASC,EAAsB,EAAE,OAAO,OAAO,cACrDL,EAAQ,OAASI,CACnB,EAAG,QAAQ,EACX,IAAqBF,EAAO,SAAY,CACtC,GAAM,CAAE,kBAAmBI,CAAmB,EAAI,KAAM,QAAO,6BAA+C,EACxGF,EAASE,EAAmB,EAAE,IAAI,OAAO,cAC/CN,EAAQ,IAAMI,CAChB,EAAG,KAAK,EACR,aAA8BF,EAAO,SAAY,CAC/C,GAAM,CAAE,2BAA4BK,CAA4B,EAAI,KAAM,QAAO,sCAAwD,EACnIH,EAASG,EAA4B,EAAE,aAAa,OAAO,cACjEP,EAAQ,aAAeI,CACzB,EAAG,cAAc,EACjB,SAA0BF,EAAO,SAAY,CAC3C,GAAM,CAAE,uBAAwBM,CAAwB,EAAI,KAAM,QAAO,kCAAoD,EACvHJ,EAASI,EAAwB,EAAE,SAAS,OAAO,cACzDR,EAAQ,SAAWI,CACrB,EAAG,UAAU,EACb,MAAuBF,EAAO,SAAY,CACxC,GAAM,CAAE,oBAAqBO,CAAqB,EAAI,KAAM,QAAO,+BAAiD,EAC9GL,EAASK,EAAqB,EAAE,MAAM,OAAO,cACnDT,EAAQ,MAAQI,CAClB,EAAG,OAAO,CACZ,EACA,eAAeM,EAAMC,EAAaC,EAAM,CACtC,IAAMC,EAAcZ,EAAaU,CAAW,EAC5C,GAAI,CAACE,EACH,MAAM,IAAI,MAAM,yBAAyBF,CAAW,EAAE,EAEnDX,EAAQW,CAAW,GACtB,MAAME,EAAY,EAGpB,IAAMC,EADSd,EAAQW,CAAW,EACZ,MAAMC,CAAI,EAChC,GAAIE,EAAO,YAAY,OAAS,GAAKA,EAAO,aAAa,OAAS,EAChE,MAAM,IAAIC,EAAkBD,CAAM,EAEpC,OAAOA,EAAO,KAChB,CAdeZ,EAAAQ,EAAA,SAefR,EAAOQ,EAAO,OAAO,EACrB,IAAIK,EAAoB,cAAc,KAAM,CAhH5C,MAgH4C,CAAAb,EAAA,0BAC1C,YAAYY,EAAQ,CAClB,IAAME,EAAcF,EAAO,YAAY,IAAKG,GAAQA,EAAI,OAAO,EAAE,KAAK;AAAA,CAAI,EACpEC,EAAeJ,EAAO,aAAa,IAAKG,GAAQA,EAAI,OAAO,EAAE,KAAK;AAAA,CAAI,EAC5E,MAAM,mBAAmBD,CAAW,IAAIE,CAAY,EAAE,EACtD,KAAK,OAASJ,CAChB,CACA,MAAO,CACLZ,EAAO,KAAM,mBAAmB,CAClC,CACF", "names": ["parsers", "initializers", "__name", "createInfoServices2", "parser", "createPacketServices2", "createPieServices2", "createArchitectureServices2", "createGitGraphServices2", "createRadarServices2", "parse", "diagramType", "text", "initializer", "result", "MermaidParseError", "lexerErrors", "err", "parserErrors"]}