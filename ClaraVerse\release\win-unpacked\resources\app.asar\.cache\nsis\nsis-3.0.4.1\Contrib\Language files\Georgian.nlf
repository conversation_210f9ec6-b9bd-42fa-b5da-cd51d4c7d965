﻿# Header, don't edit
NLF v6
# Language ID
1079
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
1200
# RTL - anything else than RTL means LTR
-
#Translation by <PERSON>
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
ჩატვირთვა $(^Name)
# ^UninstallCaption
$(^Name)–ის წაშლა
# ^LicenseSubCaption
: სალიცენზიო შეთანხმება
# ^ComponentsSubCaption
: ჩატვირთვის მონაცემები
# ^DirSubCaption
: საინსტალაციო ფოლდერი
# ^InstallingSubCaption
: ფაილების კოპირება
# ^CompletedSubCaption
: ოპერაცია დასრულებულია
# ^UnComponentsSubCaption
: წაშლის მონაცემები
# ^UnDirSubCaption
: წაშლის ფოულდერი
# ^ConfirmSubCaption
: თანხმობა
# ^UninstallingSubCaption
: ფაილების წაშლა
# ^UnCompletedSubCaption
: ოპერაცია დასრულებულია
# ^BackBtn
< &უკან
# ^NextBtn
&შემდეგ >
# ^AgreeBtn
ვეთანხმე&ბი
# ^AcceptBtn
&ვეთანხმები სალიცენზიო პირობებს
# ^DontAcceptBtn
&არ ვეთანხმები სალიცენზიო პირობებს
# ^InstallBtn
&ჩატვირთვა
# ^UninstallBtn
წაშ&ლა
# ^CancelBtn
უარი
# ^CloseBtn
&დახურვა
# ^BrowseBtn
დათ&ვალიერება...
# ^ShowDetailsBtn
&დეტალები...
# ^ClickNext
გასაგრძელებლად დააწკაპუნეთ ღილაკზე 'შემდეგ'.
# ^ClickInstall
დააწკაპუნეთ ღილაკზე 'ჩატვირთვა', პროგრამის ჩასატვირთად.
# ^ClickUninstall
დააწკაპუნეთ ღილაკზე 'წაშლა', პროგრამის წასაშლელად.
# ^Name
სახელი
# ^Completed
ჩაიტვირთა
# ^LicenseText
სანამ ჩაიტვირთება $(^NameDA) გაეცანით სალიცენზიო ხელშეკრულებას. თუ ეთანხმებით პირობებს დააწკაპუნეთ ღილაკზე 'თანხმობა'.
# ^LicenseTextCB
სანამ ჩაიტვირთება $(^NameDA) გაეცანით სალიცენზიო ხელშეკრულებას. თუ ეთანხმებით პირობებს მონიშნეთ ფანჯარა ქვემოთ. $_CLICK
# ^LicenseTextRB
სანამ ჩაიტვირთება $(^NameDA) გაეცანით სალიცენზიო ხელშეკრულებას. თუ ეთანხმებით პირობებს მონიშნეთ ქვემოთ მოცემული პირველი ვარიანტი. $_CLICK
# ^UnLicenseText
სანამ წაშლით $(^NameDA) გაეცანით სალიცენზიო ხელშეკრულებას. თუ ეთანხმებით პირობებს დააწკაპუნეთ ღილაკზე 'თანხმობა'.
# ^UnLicenseTextCB
სანამ წაშლით $(^NameDA) გაეცანით სალიცენზიო ხელშეკრულებას. თუ ეთანხმებით პირობებს მონიშნეთ ფანჯარა ქვემოთ. $_CLICK
# ^UnLicenseTextRB
სანამ წაშლით $(^NameDA) გაეცანით სალიცენზიო ხელშეკრულებას. თუ ეთანხმებით პირობებს მონიშნეთ ქვემოთ მოცემული პირველი ვარიანტი. $_CLICK
# ^Custom
სტანდარტული
# ^ComponentsText
აირჩიეთ ის კომპონენტები, რომლის ჩატვირთვაც გსურთ. $_CLICK
# ^ComponentsSubText1
აირჩიეთ ჩატვირთვის მეთოდი:
# ^ComponentsSubText2_NoInstTypes
ჩასატვირთად აირჩიეთ პროგრამის კომპონენტები:
# ^ComponentsSubText2
ან ჩასატვირთად აირჩიეთ პროგრამის დამატებითი კომპონენტები:
# ^UnComponentsText
აირჩიეთ ის კომპონენტები, რომლის წაშლაც გსურთ. $_CLICK
# ^UnComponentsSubText1
აირჩიეთ წაშლის მეთოდი:
# ^UnComponentsSubText2_NoInstTypes
წასაშლელად აირჩიეთ პროგრამის კომპონენტები:
# ^UnComponentsSubText2
ან წასაშლელად აირჩიეთ პროგრამის დამატებითი კომპონენტები:
# ^DirText
პროგრამა ჩაგიტვირთავთ $(^NameDA)–ის მითითებულ ფოლდერში. სხვა ადგილზე ჩასატვირთად დააწკაპუნეთ ღილაკზე 'დათვალიერება' და მიუთითეთ ადგილი. $_CLICK
# ^DirSubText
ჩატვირთვის ფოლდერი
# ^DirBrowseText
მითითეთ ფოლდერი სადაც უნდა ჩაიტვირთოს $(^NameDA):
# ^UnDirText
პროგრამა წაშლის $(^NameDA)–ის მითითებული ფოლდერიდან. სხვა ფოლდერიდან წასაშლელად დააწკაპუნეთ ღილაკზე 'დათვალიერება' და მიუთითეთ ადგილი. $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
მიუთითეთ ფოლდერი საიდანაც უნდა წაიშალოს $(^NameDA):
# ^SpaceAvailable
"Доступно на диске: "
# ^SpaceRequired
"Требуется на диске: "
# ^UninstallingText
პროგრამა $(^NameDA) წაიშლება თქვენი კომპიუტერიდან. $_CLICK
# ^UninstallingSubText
წაშლა:
# ^FileError
არ იხსნება ფაილები ჩასაწერად: \r\n\t"$0"\r\n'შეჩერება': შეჩერდეს ჩატვირთვა;\r\n"გამეორება": მცდელობის გამეორება;\r\n"გამოტოვება": ამ მოქმედების გამოტოვება.
# ^FileError_NoIgnore
არ იხსნება ფაილეი ჩასაწერად: \r\n\t"$0"\r\n'გამეორება': მცდელობის გამეორება;\r\n'უარი': ჩატვირთვის პროცესის შეწყვეტა.
# ^CantWrite
"არ იწერება: "
# ^CopyFailed
შეცდომა ჩაწერის დროს
# ^CopyTo
"კოპირება: "
# ^Registering
"რეგისტრირება: "
# ^Unregistering
"რეგისტრირებიდან მოხსნა: "
# ^SymbolNotFound
"ვერ მოიძებნა სიმბოლო: "
# ^CouldNotLoad
"ჩატვირთვა შეუძლებელია: "
# ^CreateFolder
"ფოლდერის შექმნა: "
# ^CreateShortcut
"იარლიყის შექმნა: "
# ^CreatedUninstaller
"წაშლის პროგრამის შექმნა: "
# ^Delete
"ფაილის წაშლა: "
# ^DeleteOnReboot
"წაიშლება კომპიუტერის გადატვირთვის დროს: "
# ^ErrorCreatingShortcut
"იარლიყის შექმნისას დაშვებულია შეცდომა: " 
# ^ErrorCreating
"შექმნისას დაშვებულია შეცდომა: "
# ^ErrorDecompressing
შეცდომა მონაცემების გახსნისას! შესაძლოა საინსტალაციო პროგრამაა დაზიანებული.
# ^ErrorRegistering
არ რეგისტრირდება (DLL)
# ^ExecShell
"ExecShell: " 
# ^Exec
"შესრულება: "
# ^Extract
"ამონაწერი: "
# ^ErrorWriting
"ამონაწერი: შეცდომაა დაშვებული ფაილის ჩაწერისას "
# ^InvalidOpcode
საინსტალაციო პროგრამა დაზიანებულია: კოდი არ არსებობს
# ^NoOLE
"OLE არ არის: " 
# ^OutputFolder
"ჩატვირთვის ფოლდერი: "
# ^RemoveFolder
"ფოლდერის წაშლა: "
# ^RenameOnReboot
"სახელის შეცვლა კომპიუტერის გადავირთვისას: "
# ^Rename
"სახელის შეცვლა: "
# ^Skipped
"გამოტოვა: "
# ^CopyDetails
მონაცემების ბუფერში კოპირება 
# ^LogInstall
ჩატვირთვის აღწერა
# byte
B
# kilo
 K
# mega
 M
# giga
 G
