{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "WeMa IA", "version": "0.1.2", "identifier": "com.wema-ia.app", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:5173", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "WeMa IA - Assistant IA Professionnel", "width": 1400, "height": 900, "resizable": true, "fullscreen": false, "decorations": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["../py_backend/**/*", "../start_backend.bat"]}}