{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 1369601567987815722, "path": 12619617251061245366, "deps": [[2671782512663819132, "tauri_utils", false, 11873741019147838691], [3060637413840920116, "proc_macro2", false, 17853383480193718097], [3150220818285335163, "url", false, 3792865789265056023], [4899080583175475170, "semver", false, 11369863174847741532], [4974441333307933176, "syn", false, 8086222198350423974], [7170110829644101142, "json_patch", false, 17880079348293135711], [7392050791754369441, "ico", false, 4335485218428939228], [8319709847752024821, "uuid", false, 11104418698036293082], [9556762810601084293, "brotli", false, 12523779811112487794], [9689903380558560274, "serde", false, 7922864411072909490], [9857275760291862238, "sha2", false, 3392166050006259196], [10806645703491011684, "thiserror", false, 10229662524248256106], [12687914511023397207, "png", false, 13503495494411234671], [13077212702700853852, "base64", false, 18051510364895857821], [15367738274754116744, "serde_json", false, 2122963044547910017], [15622660310229662834, "walkdir", false, 135640180713110039], [17990358020177143287, "quote", false, 11535381162450460229]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-08df8265f8c110b5\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}