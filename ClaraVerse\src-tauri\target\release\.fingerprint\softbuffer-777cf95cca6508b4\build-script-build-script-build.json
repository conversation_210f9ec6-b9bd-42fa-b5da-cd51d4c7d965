{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 5408242616063297496, "profile": 1369601567987815722, "path": 4060747357150126103, "deps": [[1884099982326826527, "cfg_aliases", false, 10251915684484313418]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\softbuffer-777cf95cca6508b4\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}