﻿# Header, don't edit
NLF v6
# Start editing here
# Language ID
1065
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
1256
# RTL - anything else than RTL means LTR
RTL
# Translation By FzerorubigD - <EMAIL> - Thanx to all people help me in forum.persiantools.com, <PERSON><PERSON><PERSON> <<EMAIL>>
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
نصب $(^Name) 
# ^UninstallCaption
حذف $(^Name) 
# ^LicenseSubCaption
: مجوز نصب
# ^ComponentsSubCaption
: گزینه‌های نصب
# ^DirSubCaption
: پوشه نصب
# ^InstallingSubCaption
: در حال نصب
# ^CompletedSubCaption
: پایان یافت
# ^UnComponentsSubCaption
: گزینه‌های حذف
# ^UnDirSubCaption
: پوشه‌ی حذف
# ^ConfirmSubCaption
: تأیید
# ^UninstallingSubCaption
: در حال حذف
# ^UnCompletedSubCaption
: پایان یافت
# ^BackBtn
&قبل 
# ^NextBtn
&بعد
# ^AgreeBtn
&موافقم
# ^AcceptBtn
من همه‌ی بندهای مجوز را قبول &دارم
# ^DontAcceptBtn
من بندهای مجوز را قبول &ندارم
# ^InstallBtn
&نصب
# ^UninstallBtn
&حذف
# ^CancelBtn
انصراف
# ^CloseBtn
&بستن
# ^BrowseBtn
&مرور...
# ^ShowDetailsBtn
نمایش جزئیات
# ^ClickNext
برای ادامه روی دکمه‌ی بعد کلیک کنید.
# ^ClickInstall
برای شروع نصب روی دکمه‌ی نصب کلیک کنید.
# ^ClickUninstall
برای شروع حذف روی دکمه‌ی حذف کلیک کنید.
# ^Name
نام
# ^Completed
پایان یافت
# ^LicenseText
لطفاً قبل از نصب $(^NameDA) متن مجوز را بخوانید. اگر همه‌ی بندهای آن را قبول دارید روی دکمه‌ی موافقم کلیک کنید.
# ^LicenseTextCB
لطفاً قبل از نصب $(^NameDA) متن مجوز را بخوانید. اگر همه‌ی بندهای آن را قبول دارید روی جعبه نشانه‌زنی زیر کلیک کنید. $_CLICK
# ^LicenseTextRB
لطفاً قبل از نصب $(^NameDA) متن مجوز را بخوانید. اگر همه‌ی بندهای آن را قبول دارید گزینه‌ی اول را انتخاب کنید. $_CLICK
# ^UnLicenseText
لطفاً قبل از حذف $(^NameDA) متن مجوز را بخوانید. اگر همه‌ی بندهای آن را قبول دارید روی دکمه‌ی موافقم کلیک کنید.
# ^UnLicenseTextCB
لطفاً قبل از حذف $(^NameDA) متن مجوز را بخوانید. اگر همه‌ی بندهای آن را قبول دارید روی جعبه نشانه‌زنی زیر کلیک کنید. $_CLICK
# ^UnLicenseTextRB
لطفاً قبل از حذف $(^NameDA) متن مجوز را بخوانید. اگر همه‌ی بندهای آن را قبول دارید گزینه‌ی اول را انتخاب کنید. $_CLICK
# ^Custom
سفارشی
# ^ComponentsText
کنار بخش‌هایی که می‌خواهید نصب شوند نشانه بزنید و نشانه بخش‌هایی را که نمی‌خواهید نصب شوند بردارید. $_CLICK
# ^ComponentsSubText1
نوع نصب را مشخص کنید: 
# ^ComponentsSubText2_NoInstTypes
بخش‌هایی را که می‌خواهید نصب شوند انتخاب کنید:
# ^ComponentsSubText2
یا، بخش‌های اختیاری را که می‌خواهید نصب شوند انتخاب کنید: 
# ^UnComponentsText
کنار بخش‌هایی که می‌خواهید حذف شوند نشانه بزنید و نشانه بخش‌هایی را که نمی‌خواهید حذف شوند بردارید. $_CLICK
# ^UnComponentsSubText1
نوع حذف را انتخاب کنید: 
# ^UnComponentsSubText2_NoInstTypes
بخش‌هایی را که می‌خواهید حذف شوند انتخاب کنید:
# ^UnComponentsSubText2
یا، بخش‌های اختیاری را که می‌خواهید حذف شوند انتخاب کنید: 
# ^DirText
برنامه نصب، $(^NameDA) را در پوشه‌ی زیر نصب خواهد کرد. برای نصب در پوشه‌ی دیگر روی دکمه مرور کلیک کنید و پوشه‌ی دیگری انتخاب کنید. $_CLICK
# ^DirSubText
پوشه‌ی مقصد
# ^DirBrowseText
انتخاب پوشه برای نصب $(^NameDA):
# ^UnDirText
برنامه نصب، $(^NameDA) را از پوشه‌ی زیر حذف خواهد کرد. برای نصب در پوشه‌ی دیگر روی دکمه مرور کلیک کنید و پوشه‌ی دیگری انتخاب کنید. $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
انتخاب پوشه برای حذف $(^NameDA):
# ^SpaceAvailable
"فضای موجود: "
# ^SpaceRequired
"فضای مورد نیاز: "
# ^UninstallingText
$(^NameDA) از پوشه‌ی زیر حذف خواهد شد. $_CLICK
# ^UninstallingSubText
حذف از: 
# ^FileError
خطا هنگام باز کردن پرونده برای نوشتن: \r\n\r\n$0\r\n\n برای توقف نصب روی Abort \r\n برای تلاش مجدد روی Retry \r\n و برای صرف‌نظر از این پرونده روی Ignore کلیک کنید.
# ^FileError_NoIgnore
خطا هنگام باز کردن پرونده برای نوشتن: \r\n\r\n$0\r\n\nبرای تلاش مجدد روی Retry\r\nو برای انصراف روی Cancel کلیک کنید.
# ^CantWrite
"نوشتن ممکن نیست: "
# ^CopyFailed
نسخه‌برداری ناموفق بود.
# ^CopyTo
"نسخه‌برداری در: "
# ^Registering
"در حال ثبت: "
# ^Unregistering
"در حال حذف ثبت: "
# ^SymbolNotFound
"علامت پیدا نشد: "
# ^CouldNotLoad
"بارگذاری ممکن نیست: "
# ^CreateFolder
"ایجاد پوشه: "
# ^CreateShortcut
"ایجاد میان‌بُر: "
# ^CreatedUninstaller
"حذف‌کننده ایجاد شد: "
# ^Delete
"حذف پرونده: "
# ^DeleteOnReboot
"حذف هنگام راه اندازی مجدد: "
# ^ErrorCreatingShortcut
"خطا هنگام ایجاد میان‌بُر: "
# ^ErrorCreating
"خطا هنگام ایجاد: "
# ^ErrorDecompressing
خطا هنگام باز کردن اطلاعات! نصب‌کننده خراب است؟
# ^ErrorRegistering
خطا هنگام ثبت DLL
# ^ExecShell
"پوسته اجرایی: "
# ^Exec
"اجرا: "
# ^Extract
"استخراج: "
# ^ErrorWriting
"استخراج: خطا هنگام نوشتن در پرونده"
# ^InvalidOpcode
نصب‌کننده خراب است: کد عملیاتی نامعتبر.
# ^NoOLE
"‏OLE وجود ندارد: "
# ^OutputFolder
"پوشه‌ی خروجی: "
# ^RemoveFolder
"حذف پوشه: "
# ^RenameOnReboot
"تغییر نام هنگام راه اندازی مجدد: "
# ^Rename
"تغییر نام: "
# ^Skipped
"چشم پوشی شد: "
# ^CopyDetails
نسخه‌برداری جزئیات در کلیپ‌برد
# ^LogInstall
ثبت روند نصب
# ^Byte
 بایت
# ^Kilo
 کیلو
# ^Mega
 مگا
# ^Giga
 گیگا
