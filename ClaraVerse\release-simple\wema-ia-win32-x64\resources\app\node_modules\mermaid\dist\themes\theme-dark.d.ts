export function getThemeVariables(userOverrides: any): Theme;
declare class Theme {
    background: string;
    primaryColor: string;
    secondaryColor: any;
    tertiaryColor: any;
    primaryBorderColor: any;
    secondaryBorderColor: any;
    tertiaryBorderColor: any;
    primaryTextColor: any;
    secondaryTextColor: any;
    tertiaryTextColor: any;
    lineColor: string;
    textColor: string;
    mainBkg: string;
    secondBkg: string;
    mainContrastColor: string;
    darkTextColor: any;
    border1: string;
    border2: any;
    arrowheadColor: string;
    fontFamily: string;
    fontSize: string;
    labelBackground: string;
    THEME_COLOR_LIMIT: number;
    nodeBkg: string;
    nodeBorder: string;
    clusterBkg: string;
    clusterBorder: string;
    defaultLinkColor: string;
    titleColor: string;
    edgeLabelBackground: string;
    actorBorder: string;
    actorBkg: string;
    actorTextColor: string;
    actorLineColor: string;
    signalColor: string;
    signalTextColor: string;
    labelBoxBkgColor: string;
    labelBoxBorderColor: string;
    labelTextColor: string;
    loopTextColor: string;
    noteBorderColor: string;
    noteBkgColor: string;
    noteTextColor: string;
    activationBorderColor: string;
    activationBkgColor: string;
    sequenceNumberColor: string;
    sectionBkgColor: any;
    altSectionBkgColor: string;
    sectionBkgColor2: string;
    excludeBkgColor: any;
    taskBorderColor: any;
    taskBkgColor: string;
    taskTextColor: string;
    taskTextLightColor: string;
    taskTextOutsideColor: string;
    taskTextClickableColor: string;
    activeTaskBorderColor: any;
    activeTaskBkgColor: string;
    gridColor: string;
    doneTaskBkgColor: string;
    doneTaskBorderColor: string;
    critBorderColor: string;
    critBkgColor: string;
    taskTextDarkColor: string;
    todayLineColor: string;
    personBorder: any;
    personBkg: string;
    archEdgeColor: string;
    archEdgeArrowColor: string;
    archEdgeWidth: string;
    archGroupBorderColor: any;
    archGroupBorderWidth: string;
    rowOdd: any;
    rowEven: any;
    labelColor: string;
    errorBkgColor: string;
    errorTextColor: string;
    updateColors(): void;
    transitionColor: any;
    transitionLabelColor: any;
    stateLabelColor: any;
    stateBkg: any;
    labelBackgroundColor: any;
    compositeBackground: any;
    altBackground: any;
    compositeTitleBackground: any;
    compositeBorder: any;
    innerEndBackground: any;
    specialStateColor: string | undefined;
    fillType0: string | undefined;
    fillType1: any;
    fillType2: any;
    fillType3: any;
    fillType4: any;
    fillType5: any;
    fillType6: any;
    fillType7: any;
    cScale1: any;
    cScale2: any;
    cScale3: any;
    cScale4: any;
    cScale5: any;
    cScale6: any;
    cScale7: any;
    cScale8: any;
    cScale9: any;
    cScale10: any;
    cScale11: any;
    cScale12: any;
    cScale0: any;
    scaleLabelColor: any;
    pieTitleTextSize: any;
    pieTitleTextColor: any;
    pieSectionTextSize: any;
    pieSectionTextColor: any;
    pieLegendTextSize: any;
    pieLegendTextColor: any;
    pieStrokeColor: any;
    pieStrokeWidth: any;
    pieOuterStrokeWidth: any;
    pieOuterStrokeColor: any;
    pieOpacity: any;
    quadrant1Fill: any;
    quadrant2Fill: any;
    quadrant3Fill: any;
    quadrant4Fill: any;
    quadrant1TextFill: any;
    quadrant2TextFill: any;
    quadrant3TextFill: any;
    quadrant4TextFill: any;
    quadrantPointFill: any;
    quadrantPointTextFill: any;
    quadrantXAxisTextFill: any;
    quadrantYAxisTextFill: any;
    quadrantInternalBorderStrokeFill: any;
    quadrantExternalBorderStrokeFill: any;
    quadrantTitleFill: any;
    xyChart: any;
    packet: {
        startByteColor: any;
        endByteColor: any;
        labelColor: any;
        titleColor: any;
        blockStrokeColor: any;
        blockFillColor: string;
    } | undefined;
    radar: any;
    classText: any;
    requirementBackground: any;
    requirementBorderColor: any;
    requirementBorderSize: any;
    requirementTextColor: any;
    relationColor: any;
    relationLabelBackground: any;
    relationLabelColor: any;
    git0: any;
    git1: any;
    git2: any;
    git3: any;
    git4: any;
    git5: any;
    git6: any;
    git7: any;
    gitInv0: any;
    gitInv1: any;
    gitInv2: any;
    gitInv3: any;
    gitInv4: any;
    gitInv5: any;
    gitInv6: any;
    gitInv7: any;
    gitBranchLabel0: any;
    gitBranchLabel1: any;
    gitBranchLabel2: any;
    gitBranchLabel3: any;
    gitBranchLabel4: any;
    gitBranchLabel5: any;
    gitBranchLabel6: any;
    gitBranchLabel7: any;
    tagLabelColor: any;
    tagLabelBackground: any;
    tagLabelBorder: any;
    tagLabelFontSize: any;
    commitLabelColor: any;
    commitLabelBackground: any;
    commitLabelFontSize: any;
    attributeBackgroundColorOdd: any;
    attributeBackgroundColorEven: any;
    calculate(overrides: any): void;
}
export {};
