{"version": 3, "sources": ["../../../../parser/dist/chunks/mermaid-parser.core/chunk-V4Q32G6S.mjs"], "sourcesContent": ["import {\n  AbstractMermaidTokenBuilder,\n  CommonValueConverter,\n  MermaidGeneratedSharedModule,\n  PacketGeneratedModule,\n  __name\n} from \"./chunk-7PKI6E2E.mjs\";\n\n// src/language/packet/module.ts\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject\n} from \"langium\";\n\n// src/language/packet/tokenBuilder.ts\nvar PacketTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"PacketTokenBuilder\");\n  }\n  constructor() {\n    super([\"packet-beta\"]);\n  }\n};\n\n// src/language/packet/module.ts\nvar PacketModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */ __name(() => new PacketTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */ __name(() => new CommonValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createPacketServices(context = EmptyFileSystem) {\n  const shared = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Packet = inject(\n    createDefaultCoreModule({ shared }),\n    PacketGeneratedModule,\n    PacketModule\n  );\n  shared.ServiceRegistry.register(Packet);\n  return { shared, Packet };\n}\n__name(createPacketServices, \"createPacketServices\");\n\nexport {\n  PacketModule,\n  createPacketServices\n};\n"], "mappings": "iJAiBA,IAAIA,EAAqB,cAAcC,CAA4B,CAjBnE,MAiBmE,CAAAC,EAAA,2BACjE,MAAO,CACLA,EAAO,KAAM,oBAAoB,CACnC,CACA,aAAc,CACZ,MAAM,CAAC,aAAa,CAAC,CACvB,CACF,EAGIC,EAAe,CACjB,OAAQ,CACN,aAA8BD,EAAO,IAAM,IAAIF,EAAsB,cAAc,EACnF,eAAgCE,EAAO,IAAM,IAAIE,EAAwB,gBAAgB,CAC3F,CACF,EACA,SAASC,EAAqBC,EAAUC,EAAiB,CACvD,IAAMC,EAASC,EACbC,EAA8BJ,CAAO,EACrCK,CACF,EACMC,EAASH,EACbI,EAAwB,CAAE,OAAAL,CAAO,CAAC,EAClCM,EACAX,CACF,EACA,OAAAK,EAAO,gBAAgB,SAASI,CAAM,EAC/B,CAAE,OAAAJ,EAAQ,OAAAI,CAAO,CAC1B,CAZSV,EAAAG,EAAA,wBAaTH,EAAOG,EAAsB,sBAAsB", "names": ["PacketTokenBuilder", "AbstractMermaidTokenBuilder", "__name", "PacketModule", "CommonValueConverter", "createPacketServices", "context", "EmptyFileSystem", "shared", "inject", "createDefaultSharedCoreModule", "MermaidGeneratedSharedModule", "Packet", "createDefaultCoreModule", "PacketGeneratedModule"]}