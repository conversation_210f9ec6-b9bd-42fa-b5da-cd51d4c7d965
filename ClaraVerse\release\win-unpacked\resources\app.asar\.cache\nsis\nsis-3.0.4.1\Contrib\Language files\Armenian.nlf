﻿# Header, don't edit
NLF v6
# Start editing here
# Language ID
1067
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
1200
# RTL - anything else than RTL means LTR
-
# Translation by <PERSON><PERSON> (<PERSON><PERSON>oh<PERSON><PERSON>@haysoft.org) 
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
Տեղակայել $(^Name)-ը
# ^UninstallCaption
Ջնջել $(^Name)-ը
# ^LicenseSubCaption
: Արտոնագրային համաձայնություն
# ^ComponentsSubCaption
: Տեղակայելու ընտրանքները
# ^DirSubCaption
: Տեղակայելու թղթապանակը
# ^InstallingSubCaption
: Ֆայլերը պատճենվում են
# ^CompletedSubCaption
: Գործողությունը ավարտվեց
# ^UnComponentsSubCaption
: Տեղակայելու ընտրությունը
# ^UnDirSubCaption
: Ջնջվող թղթապանակը
# ^ConfirmSubCaption
: Հաստատեք
# ^UninstallingSubCaption
: Ֆայլերը ջնջվում են
# ^UnCompletedSubCaption
: Գործողությունը ավարտվեց
# ^BackBtn
« &Նախորդը
# ^NextBtn
&Հաջորդը »
# ^AgreeBtn
Համաձայն& եմ
# ^AcceptBtn
Ես &ընդունում եմ համաձայնագրի պայմանները
# ^DontAcceptBtn
Ես &չեմ ընդունում համաձայնագրի պայմանները
# ^InstallBtn
&Տեղակայել
# ^UninstallBtn
Ջն&ջել
# ^CancelBtn
Չեղարկել
# ^CloseBtn
&Փակել
# ^BrowseBtn
Դ&իտել ...
# ^ShowDetailsBtn
&Մանրամասն...
# ^ClickNext
Շարունակելու համար սեղմեք 'Առաջ'։
# ^ClickInstall
Տեղակայելու համար սեղմեք 'Տեղակայել'։
# ^ClickUninstall
Ծրագիրը ջնջելու համար սեղմեք 'Ջնջել'։
# ^Name
Անունը
# ^Completed
Պատրաստ է
# ^LicenseText
$(^NameDA)-ը տեղակայելուց առաջ ծանոթացեք արտոնագրային համաձայնությանը։ Եթե ընդունում եք այն՝ սեղմեք 'Համաձայն եմ'։
# ^LicenseTextCB
$(^NameDA)-ը տեղակայելուց առաջ ծանոթացեք արտոնագրային համաձայնությանը։ Եթե ընդունում եք այն՝ դրեք նիշը ներքևում։ $_CLICK
# ^LicenseTextRB
$(^NameDA)-ը տեղակայելուց առաջ ծանոթացեք արտոնագրային համաձայնությանը։ Եթե ընդունում եք այն՝ ընտրեք ներքոնշյալներից առաջինը։ $_CLICK
# ^UnLicenseText
$(^NameDA)-ը ջնջելուց առաջ ծանոթացեք արտոնագրային համաձայնությանը։ Եթե ընդունում եք այն՝ սեղմեք 'Համաձայն եմ'։
# ^UnLicenseTextCB
$(^NameDA)-ը ջնջելուց առաջ ծանոթացեք արտոնագրային համաձայնությանը։ Եթե ընդունում եք այն՝ դրեք նիշը ներքևում։ $_CLICK
# ^UnLicenseTextRB
$(^NameDA)-ը ջնջելուց առաջ ծանոթացեք արտոնագրային համաձայնությանը։ Եթե ընդունում եք այն՝ ընտրեք ներքոնշյալներից առաջինը։ $_CLICK
# ^Custom
Հարմարեցված
# ^ComponentsText
Ընտրեք այն բաղադրիչները, որոնք ցանկանում եք տեղակայել։ $_CLICK
# ^ComponentsSubText1
Ընտրեք տեղակայելու եղանակը.
# ^ComponentsSubText2_NoInstTypes
Տեղակայելու համար ընտրեք բաղադրիչները.
# ^ComponentsSubText2
կամ ընտրեք լրացուցիչ բաղադրիչներ.
# ^UnComponentsText
Ջնջելու համար ընտրեք բաղադրիչները։ $_CLICK
# ^UnComponentsSubText1
Ընտրեք ջնջելու եղանակը.
# ^UnComponentsSubText2_NoInstTypes
Ընտրեք ջնջելու բաղադրիչները.
# ^UnComponentsSubText2
կամ ջնջելու համար ընտրեք լրացուցիչ բաղադրիչներ։
# ^DirText
Ծրագիրը կտեղակայի $(^NameDA)-ը նշված թղթապանակում։ Այլ թղթապանակում տեղակայելու համար սեղմեք 'Ընտրել' և ընտրեք այն։ $_CLICK
# ^DirSubText
Տեղակայելու թղթապանկը
# ^DirBrowseText
Նշեք $(^NameDA)-ի տեղակայելու թղթապանակը.
# ^UnDirText
Ծրագիրը կջնջի $(^NameDA)-ը նշված թղթապանակից։ Այլ թղթապանակից ջնջելու համար սեղմեք 'Ընտրել' և ընտրեք այն։ $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
Ընտրեք թղթապանակը, որից պետք է ջնջել $(^NameDA)-ը.
# ^SpaceAvailable
"Հասանելի է. "
# ^SpaceRequired
"Պահանջվում է. "
# ^UninstallingText
$(^NameDA) ծրագիրը կջնջվի Ձեր համակարգչից։ $_CLICK
# ^UninstallingSubText
Ջնջվում է՝
# ^FileError
Հնարավոր չէ բացել ֆայլը՝ գրանցելու համար։ \r\n\t"$0"\r\n'Դադարեցնել'՝ ընդհատել տեղակայումը,\r\n"Կրկնել"՝ կրկին փորձել,\r\n"Բաց թողնել"՝ բաց թողնել գործողությունը։
# ^FileError_NoIgnore
Հնարավոր չէ բացել ֆայլը՝ գրանցելու համար։ \r\n\t"$0"\r\n'Կրկնել'՝ կրկին փորձել,\r\n'Դադարեցնել'՝ ընդհատել տեղակայումը։
# ^CantWrite
"Հնարավոր չէ գրանցել "
# ^CopyFailed
Սխալ՝ պատճենելու ժամանակ
# ^CopyTo
"Պատճենել՝ "
# ^Registering
"Գրանցում. "
# ^Unregistering
"Վերագրանցում. "
# ^SymbolNotFound
"Հնարավոր չէ գտնել՝ "
# ^CouldNotLoad
"Հնարավոր չէ բացել. "
# ^CreateFolder
"Ստեղծվում է թղթապանակ "
# ^CreateShortcut
"Ստեղծվում են պիտակներ."
# ^CreatedUninstaller
"Ստեղծվում ջնջման ծրագիրը. "
# ^Delete
"Ֆայլերի ջնջում. "
# ^DeleteOnReboot
"Կջնջվի վերագործարկելուց հետո. "
# ^ErrorCreatingShortcut
"Սխալ՝ պիտակը ստեղծելիս. " 
# ^ErrorCreating
"Սխալ. "
# ^ErrorDecompressing
Սխալ՝ տվյալները բացելու ժամանակ։
# ^ErrorRegistering
Հնարավոր չէ գրանցել գրադարանը(DLL)
# ^ExecShell
"Ֆայլի կիրառում. " 
# ^Exec
"Կատարվում է. "
# ^Extract
"Հանում է. "
# ^ErrorWriting
"Ֆայլերը գրելու սխալ. "
# ^InvalidOpcode
Տեղակայիչը վնասված է.
# ^NoOLE
"Չկա OLE՝" 
# ^OutputFolder
"Տեղակայելու թղթապանակը. "
# ^RemoveFolder
"Թղթապանակի ջնջում. "
# ^RenameOnReboot
"Կանվանափոխվի վերագործարկելուց հետո. "
# ^Rename
"Անվանափոխում. "
# ^Skipped
"Բաց թողնած. "
# ^CopyDetails
Պատճենել տվյալները 
# ^LogInstall
Տեղակայման հաշվետվություն
# byte
բայթ
# kilo
 Կ
# mega
 Մ
# giga
 Գ