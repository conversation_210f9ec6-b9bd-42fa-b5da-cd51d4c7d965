
    Add-Type -AssemblyName System.Windows.Forms
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    
    [System.Windows.Forms.MessageBox]::Show("Installation de WeMa IA en cours...", "WeMa IA", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
    
    $installDir = "$env:APPDATA\\WeMa_IA"
    if (-not (Test-Path $installDir)) {
        New-Item -ItemType Directory -Path $installDir -Force | Out-Null
    }
    
    # Extraire l'application
    [System.IO.Compression.ZipFile]::ExtractToDirectory("wema_app.zip", $installDir)
    
    # Créer raccourci bureau
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut("$env:USERPROFILE\\Desktop\\WeMa IA.lnk")
    $Shortcut.TargetPath = "$installDir\\WeMa IA.exe"
    $Shortcut.Save()
    
    # Lancer l'application
    Start-Process "$installDir\\WeMa IA.exe"
    
    [System.Windows.Forms.MessageBox]::Show("WeMa IA installé et lancé avec succès !", "WeMa IA", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
    