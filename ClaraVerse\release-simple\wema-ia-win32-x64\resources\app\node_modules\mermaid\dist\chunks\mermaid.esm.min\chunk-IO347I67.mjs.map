{"version": 3, "sources": ["../../../src/rendering-util/insertElementsForSize.js", "../../../src/rendering-util/setupViewPortForSVG.ts"], "sourcesContent": ["import { select } from 'd3';\n\nexport const getDiagramElement = (id, securityLevel) => {\n  let sandboxElement;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? select(sandboxElement.nodes()[0].contentDocument.body)\n      : select('body');\n\n  const svg = root.select(`[id=\"${id}\"]`);\n\n  // Run the renderer. This is what draws the final graph.\n\n  return svg;\n};\n", "import { configureSvgSize } from '../setupGraphViewbox.js';\nimport type { SVG } from '../diagram-api/types.js';\nimport { log } from '../logger.js';\n\nexport const setupViewPortForSVG = (\n  svg: SVG,\n  padding: number,\n  cssDiagram: string,\n  useMaxWidth: boolean\n) => {\n  // Initialize the SVG element and set the diagram class\n  svg.attr('class', cssDiagram);\n\n  // Calculate the dimensions and position with padding\n  const { width, height, x, y } = calculateDimensionsWithPadding(svg, padding);\n\n  // Configure the size and aspect ratio of the SVG\n  configureSvgSize(svg, height, width, useMaxWidth);\n\n  // Update the viewBox to ensure all content is visible with padding\n  const viewBox = createViewBox(x, y, width, height, padding);\n  svg.attr('viewBox', viewBox);\n\n  // Log the viewBox configuration for debugging\n  log.debug(`viewBox configured: ${viewBox} with padding: ${padding}`);\n};\n\nconst calculateDimensionsWithPadding = (svg: SVG, padding: number) => {\n  const bounds = svg.node()?.getBBox() || { width: 0, height: 0, x: 0, y: 0 };\n  return {\n    width: bounds.width + padding * 2,\n    height: bounds.height + padding * 2,\n    x: bounds.x,\n    y: bounds.y,\n  };\n};\n\nconst createViewBox = (x: number, y: number, width: number, height: number, padding: number) => {\n  return `${x - padding} ${y - padding} ${width} ${height}`;\n};\n"], "mappings": "iGAEO,IAAMA,EAAoBC,EAAA,CAACC,EAAIC,IAAkB,CACtD,IAAIC,EACJ,OAAID,IAAkB,YACpBC,EAAiBC,EAAO,KAAOH,CAAE,IAGjCC,IAAkB,UACdE,EAAOD,EAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,EACrDC,EAAO,MAAM,GAEF,OAAO,QAAQH,CAAE,IAAI,CAKxC,EAfiC,qBCE1B,IAAMI,EAAsBC,EAAA,CACjCC,EACAC,EACAC,EACAC,IACG,CAEHH,EAAI,KAAK,QAASE,CAAU,EAG5B,GAAM,CAAE,MAAAE,EAAO,OAAAC,EAAQ,EAAAC,EAAG,EAAAC,CAAE,EAAIC,EAA+BR,EAAKC,CAAO,EAG3EQ,EAAiBT,EAAKK,EAAQD,EAAOD,CAAW,EAGhD,IAAMO,EAAUC,EAAcL,EAAGC,EAAGH,EAAOC,EAAQJ,CAAO,EAC1DD,EAAI,KAAK,UAAWU,CAAO,EAG3BE,EAAI,MAAM,uBAAuBF,CAAO,kBAAkBT,CAAO,EAAE,CACrE,EArBmC,uBAuB7BO,EAAiCT,EAAA,CAACC,EAAUC,IAAoB,CACpE,IAAMY,EAASb,EAAI,KAAK,GAAG,QAAQ,GAAK,CAAE,MAAO,EAAG,OAAQ,EAAG,EAAG,EAAG,EAAG,CAAE,EAC1E,MAAO,CACL,MAAOa,EAAO,MAAQZ,EAAU,EAChC,OAAQY,EAAO,OAASZ,EAAU,EAClC,EAAGY,EAAO,EACV,EAAGA,EAAO,CACZ,CACF,EARuC,kCAUjCF,EAAgBZ,EAAA,CAACO,EAAWC,EAAWH,EAAeC,EAAgBJ,IACnE,GAAGK,EAAIL,CAAO,IAAIM,EAAIN,CAAO,IAAIG,CAAK,IAAIC,CAAM,GADnC", "names": ["getDiagramElement", "__name", "id", "securityLevel", "sandboxElement", "select_default", "setupViewPortForSVG", "__name", "svg", "padding", "cssDiagram", "useMaxWidth", "width", "height", "x", "y", "calculateDimensionsWithPadding", "configureSvgSize", "viewBox", "createViewBox", "log", "bounds"]}