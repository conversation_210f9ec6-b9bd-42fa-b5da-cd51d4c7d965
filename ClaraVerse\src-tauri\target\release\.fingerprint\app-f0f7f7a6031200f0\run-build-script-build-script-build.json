{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 8010699718139807489], [14039947826026167952, "build_script_build", false, 12105191455322431167], [8324462083842905811, "build_script_build", false, 12107525688337279129]], "local": [{"RerunIfChanged": {"output": "release\\build\\app-f0f7f7a6031200f0\\output", "paths": ["tauri.conf.json", "capabilities", "..\\py_backend\\.env", "..\\py_backend\\.env.template", "..\\py_backend\\Dockerfile", "..\\py_backend\\Speech2Text.py", "..\\py_backend\\TTS_README.md", "..\\py_backend\\Text2Speech.py", "..\\py_backend\\__pycache__\\langfuse_service.cpython-312.pyc", "..\\py_backend\\__pycache__\\main.cpython-312.pyc", "..\\py_backend\\__pycache__\\network_config.cpython-312.pyc", "..\\py_backend\\__pycache__\\ocr_processor_premium.cpython-312.pyc", "..\\py_backend\\__pycache__\\ocr_service.cpython-312.pyc", "..\\py_backend\\__pycache__\\perfect_compressor.cpython-312.pyc", "..\\py_backend\\__pycache__\\rag_premium_service.cpython-312.pyc", "..\\py_backend\\auth_service.py", "..\\py_backend\\clara_backend.db", "..\\py_backend\\compression_routes.py", "..\\py_backend\\diffusers_api.py", "..\\py_backend\\documents.db", "..\\py_backend\\install_kokoro.py", "..\\py_backend\\langfuse_service.py", "..\\py_backend\\main.py", "..\\py_backend\\network_config.py", "..\\py_backend\\ocr_processor_premium.py", "..\\py_backend\\ocr_service.py", "..\\py_backend\\perfect_compressor.py", "..\\py_backend\\qdrant_storage\\.lock", "..\\py_backend\\qdrant_storage\\meta.json", "..\\py_backend\\requirements.txt", "..\\py_backend\\services\\__pycache__\\internet_search_service.cpython-312.pyc", "..\\py_backend\\services\\__pycache__\\startup_service.cpython-312.pyc", "..\\py_backend\\services\\__pycache__\\web_scraper_service.cpython-312.pyc", "..\\py_backend\\services\\internet_search_service.py", "..\\py_backend\\services\\startup_service.py", "..\\py_backend\\services\\web_scraper_service.py", "..\\py_backend\\start_server.py", "..\\py_backend\\test_compression_tool.py", "..\\py_backend\\test_non_streaming.py", "..\\py_backend\\test_optimizations.py", "..\\py_backend\\test_qdrant_temp\\.lock", "..\\py_backend\\test_qdrant_temp\\collection\\test_collection\\storage.sqlite", "..\\py_backend\\test_streaming_optimized.py", "..\\py_backend\\tools\\__pycache__\\internet_search_tools.cpython-312.pyc", "..\\py_backend\\tools\\internet_search_tools.py", "python-embed\\python.zip"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}