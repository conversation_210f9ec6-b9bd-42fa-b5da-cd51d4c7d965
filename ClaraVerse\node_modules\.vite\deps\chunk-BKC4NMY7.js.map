{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/chunk-AEK57VVT.mjs"], "sourcesContent": ["import {\n  getDiagramElement,\n  setupViewPortForSVG\n} from \"./chunk-RZ5BOZE2.mjs\";\nimport {\n  render\n} from \"./chunk-TYCBKAJE.mjs\";\nimport {\n  generateId,\n  utils_default\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  clear,\n  common_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/state/parser/stateDiagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 2], $V1 = [1, 3], $V2 = [1, 4], $V3 = [2, 4], $V4 = [1, 9], $V5 = [1, 11], $V6 = [1, 16], $V7 = [1, 17], $V8 = [1, 18], $V9 = [1, 19], $Va = [1, 32], $Vb = [1, 20], $Vc = [1, 21], $Vd = [1, 22], $Ve = [1, 23], $Vf = [1, 24], $Vg = [1, 26], $Vh = [1, 27], $Vi = [1, 28], $Vj = [1, 29], $Vk = [1, 30], $Vl = [1, 31], $Vm = [1, 34], $Vn = [1, 35], $Vo = [1, 36], $Vp = [1, 37], $Vq = [1, 33], $Vr = [1, 4, 5, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 42, 45, 48, 49, 50, 51, 54], $Vs = [1, 4, 5, 14, 15, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 42, 45, 48, 49, 50, 51, 54], $Vt = [4, 5, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 42, 45, 48, 49, 50, 51, 54];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"SPACE\": 4, \"NL\": 5, \"SD\": 6, \"document\": 7, \"line\": 8, \"statement\": 9, \"classDefStatement\": 10, \"styleStatement\": 11, \"cssClassStatement\": 12, \"idStatement\": 13, \"DESCR\": 14, \"-->\": 15, \"HIDE_EMPTY\": 16, \"scale\": 17, \"WIDTH\": 18, \"COMPOSIT_STATE\": 19, \"STRUCT_START\": 20, \"STRUCT_STOP\": 21, \"STATE_DESCR\": 22, \"AS\": 23, \"ID\": 24, \"FORK\": 25, \"JOIN\": 26, \"CHOICE\": 27, \"CONCURRENT\": 28, \"note\": 29, \"notePosition\": 30, \"NOTE_TEXT\": 31, \"direction\": 32, \"acc_title\": 33, \"acc_title_value\": 34, \"acc_descr\": 35, \"acc_descr_value\": 36, \"acc_descr_multiline_value\": 37, \"classDef\": 38, \"CLASSDEF_ID\": 39, \"CLASSDEF_STYLEOPTS\": 40, \"DEFAULT\": 41, \"style\": 42, \"STYLE_IDS\": 43, \"STYLEDEF_STYLEOPTS\": 44, \"class\": 45, \"CLASSENTITY_IDS\": 46, \"STYLECLASS\": 47, \"direction_tb\": 48, \"direction_bt\": 49, \"direction_rl\": 50, \"direction_lr\": 51, \"eol\": 52, \";\": 53, \"EDGE_STATE\": 54, \"STYLE_SEPARATOR\": 55, \"left_of\": 56, \"right_of\": 57, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"SPACE\", 5: \"NL\", 6: \"SD\", 14: \"DESCR\", 15: \"-->\", 16: \"HIDE_EMPTY\", 17: \"scale\", 18: \"WIDTH\", 19: \"COMPOSIT_STATE\", 20: \"STRUCT_START\", 21: \"STRUCT_STOP\", 22: \"STATE_DESCR\", 23: \"AS\", 24: \"ID\", 25: \"FORK\", 26: \"JOIN\", 27: \"CHOICE\", 28: \"CONCURRENT\", 29: \"note\", 31: \"NOTE_TEXT\", 33: \"acc_title\", 34: \"acc_title_value\", 35: \"acc_descr\", 36: \"acc_descr_value\", 37: \"acc_descr_multiline_value\", 38: \"classDef\", 39: \"CLASSDEF_ID\", 40: \"CLASSDEF_STYLEOPTS\", 41: \"DEFAULT\", 42: \"style\", 43: \"STYLE_IDS\", 44: \"STYLEDEF_STYLEOPTS\", 45: \"class\", 46: \"CLASSENTITY_IDS\", 47: \"STYLECLASS\", 48: \"direction_tb\", 49: \"direction_bt\", 50: \"direction_rl\", 51: \"direction_lr\", 53: \";\", 54: \"EDGE_STATE\", 55: \"STYLE_SEPARATOR\", 56: \"left_of\", 57: \"right_of\" },\n    productions_: [0, [3, 2], [3, 2], [3, 2], [7, 0], [7, 2], [8, 2], [8, 1], [8, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 2], [9, 3], [9, 4], [9, 1], [9, 2], [9, 1], [9, 4], [9, 3], [9, 6], [9, 1], [9, 1], [9, 1], [9, 1], [9, 4], [9, 4], [9, 1], [9, 2], [9, 2], [9, 1], [10, 3], [10, 3], [11, 3], [12, 3], [32, 1], [32, 1], [32, 1], [32, 1], [52, 1], [52, 1], [13, 1], [13, 1], [13, 3], [13, 3], [30, 1], [30, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 3:\n          yy.setRootDoc($$[$0]);\n          return $$[$0];\n          break;\n        case 4:\n          this.$ = [];\n          break;\n        case 5:\n          if ($$[$0] != \"nl\") {\n            $$[$0 - 1].push($$[$0]);\n            this.$ = $$[$0 - 1];\n          }\n          break;\n        case 6:\n        case 7:\n          this.$ = $$[$0];\n          break;\n        case 8:\n          this.$ = \"nl\";\n          break;\n        case 12:\n          this.$ = $$[$0];\n          break;\n        case 13:\n          const stateStmt = $$[$0 - 1];\n          stateStmt.description = yy.trimColon($$[$0]);\n          this.$ = stateStmt;\n          break;\n        case 14:\n          this.$ = { stmt: \"relation\", state1: $$[$0 - 2], state2: $$[$0] };\n          break;\n        case 15:\n          const relDescription = yy.trimColon($$[$0]);\n          this.$ = { stmt: \"relation\", state1: $$[$0 - 3], state2: $$[$0 - 1], description: relDescription };\n          break;\n        case 19:\n          this.$ = { stmt: \"state\", id: $$[$0 - 3], type: \"default\", description: \"\", doc: $$[$0 - 1] };\n          break;\n        case 20:\n          var id = $$[$0];\n          var description = $$[$0 - 2].trim();\n          if ($$[$0].match(\":\")) {\n            var parts = $$[$0].split(\":\");\n            id = parts[0];\n            description = [description, parts[1]];\n          }\n          this.$ = { stmt: \"state\", id, type: \"default\", description };\n          break;\n        case 21:\n          this.$ = { stmt: \"state\", id: $$[$0 - 3], type: \"default\", description: $$[$0 - 5], doc: $$[$0 - 1] };\n          break;\n        case 22:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"fork\" };\n          break;\n        case 23:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"join\" };\n          break;\n        case 24:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"choice\" };\n          break;\n        case 25:\n          this.$ = { stmt: \"state\", id: yy.getDividerId(), type: \"divider\" };\n          break;\n        case 26:\n          this.$ = { stmt: \"state\", id: $$[$0 - 1].trim(), note: { position: $$[$0 - 2].trim(), text: $$[$0].trim() } };\n          break;\n        case 29:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 30:\n        case 31:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 32:\n        case 33:\n          this.$ = { stmt: \"classDef\", id: $$[$0 - 1].trim(), classes: $$[$0].trim() };\n          break;\n        case 34:\n          this.$ = { stmt: \"style\", id: $$[$0 - 1].trim(), styleClass: $$[$0].trim() };\n          break;\n        case 35:\n          this.$ = { stmt: \"applyClass\", id: $$[$0 - 1].trim(), styleClass: $$[$0].trim() };\n          break;\n        case 36:\n          yy.setDirection(\"TB\");\n          this.$ = { stmt: \"dir\", value: \"TB\" };\n          break;\n        case 37:\n          yy.setDirection(\"BT\");\n          this.$ = { stmt: \"dir\", value: \"BT\" };\n          break;\n        case 38:\n          yy.setDirection(\"RL\");\n          this.$ = { stmt: \"dir\", value: \"RL\" };\n          break;\n        case 39:\n          yy.setDirection(\"LR\");\n          this.$ = { stmt: \"dir\", value: \"LR\" };\n          break;\n        case 42:\n        case 43:\n          this.$ = { stmt: \"state\", id: $$[$0].trim(), type: \"default\", description: \"\" };\n          break;\n        case 44:\n          this.$ = { stmt: \"state\", id: $$[$0 - 2].trim(), classes: [$$[$0].trim()], type: \"default\", description: \"\" };\n          break;\n        case 45:\n          this.$ = { stmt: \"state\", id: $$[$0 - 2].trim(), classes: [$$[$0].trim()], type: \"default\", description: \"\" };\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: $V0, 5: $V1, 6: $V2 }, { 1: [3] }, { 3: 5, 4: $V0, 5: $V1, 6: $V2 }, { 3: 6, 4: $V0, 5: $V1, 6: $V2 }, o([1, 4, 5, 16, 17, 19, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 42, 45, 48, 49, 50, 51, 54], $V3, { 7: 7 }), { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 3], 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 42: $Vk, 45: $Vl, 48: $Vm, 49: $Vn, 50: $Vo, 51: $Vp, 54: $Vq }, o($Vr, [2, 5]), { 9: 38, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 42: $Vk, 45: $Vl, 48: $Vm, 49: $Vn, 50: $Vo, 51: $Vp, 54: $Vq }, o($Vr, [2, 7]), o($Vr, [2, 8]), o($Vr, [2, 9]), o($Vr, [2, 10]), o($Vr, [2, 11]), o($Vr, [2, 12], { 14: [1, 39], 15: [1, 40] }), o($Vr, [2, 16]), { 18: [1, 41] }, o($Vr, [2, 18], { 20: [1, 42] }), { 23: [1, 43] }, o($Vr, [2, 22]), o($Vr, [2, 23]), o($Vr, [2, 24]), o($Vr, [2, 25]), { 30: 44, 31: [1, 45], 56: [1, 46], 57: [1, 47] }, o($Vr, [2, 28]), { 34: [1, 48] }, { 36: [1, 49] }, o($Vr, [2, 31]), { 39: [1, 50], 41: [1, 51] }, { 43: [1, 52] }, { 46: [1, 53] }, o($Vs, [2, 42], { 55: [1, 54] }), o($Vs, [2, 43], { 55: [1, 55] }), o($Vr, [2, 36]), o($Vr, [2, 37]), o($Vr, [2, 38]), o($Vr, [2, 39]), o($Vr, [2, 6]), o($Vr, [2, 13]), { 13: 56, 24: $Va, 54: $Vq }, o($Vr, [2, 17]), o($Vt, $V3, { 7: 57 }), { 24: [1, 58] }, { 24: [1, 59] }, { 23: [1, 60] }, { 24: [2, 46] }, { 24: [2, 47] }, o($Vr, [2, 29]), o($Vr, [2, 30]), { 40: [1, 61] }, { 40: [1, 62] }, { 44: [1, 63] }, { 47: [1, 64] }, { 24: [1, 65] }, { 24: [1, 66] }, o($Vr, [2, 14], { 14: [1, 67] }), { 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 21: [1, 68], 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 42: $Vk, 45: $Vl, 48: $Vm, 49: $Vn, 50: $Vo, 51: $Vp, 54: $Vq }, o($Vr, [2, 20], { 20: [1, 69] }), { 31: [1, 70] }, { 24: [1, 71] }, o($Vr, [2, 32]), o($Vr, [2, 33]), o($Vr, [2, 34]), o($Vr, [2, 35]), o($Vs, [2, 44]), o($Vs, [2, 45]), o($Vr, [2, 15]), o($Vr, [2, 19]), o($Vt, $V3, { 7: 72 }), o($Vr, [2, 26]), o($Vr, [2, 27]), { 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 21: [1, 73], 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 42: $Vk, 45: $Vl, 48: $Vm, 49: $Vn, 50: $Vo, 51: $Vp, 54: $Vq }, o($Vr, [2, 21])],\n    defaultActions: { 5: [2, 1], 6: [2, 2], 46: [2, 46], 47: [2, 47] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 41;\n            break;\n          case 1:\n            return 48;\n            break;\n          case 2:\n            return 49;\n            break;\n          case 3:\n            return 50;\n            break;\n          case 4:\n            return 51;\n            break;\n          case 5:\n            break;\n          case 6:\n            {\n            }\n            break;\n          case 7:\n            return 5;\n            break;\n          case 8:\n            break;\n          case 9:\n            break;\n          case 10:\n            break;\n          case 11:\n            break;\n          case 12:\n            this.pushState(\"SCALE\");\n            return 17;\n            break;\n          case 13:\n            return 18;\n            break;\n          case 14:\n            this.popState();\n            break;\n          case 15:\n            this.begin(\"acc_title\");\n            return 33;\n            break;\n          case 16:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 17:\n            this.begin(\"acc_descr\");\n            return 35;\n            break;\n          case 18:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 19:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 20:\n            this.popState();\n            break;\n          case 21:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 22:\n            this.pushState(\"CLASSDEF\");\n            return 38;\n            break;\n          case 23:\n            this.popState();\n            this.pushState(\"CLASSDEFID\");\n            return \"DEFAULT_CLASSDEF_ID\";\n            break;\n          case 24:\n            this.popState();\n            this.pushState(\"CLASSDEFID\");\n            return 39;\n            break;\n          case 25:\n            this.popState();\n            return 40;\n            break;\n          case 26:\n            this.pushState(\"CLASS\");\n            return 45;\n            break;\n          case 27:\n            this.popState();\n            this.pushState(\"CLASS_STYLE\");\n            return 46;\n            break;\n          case 28:\n            this.popState();\n            return 47;\n            break;\n          case 29:\n            this.pushState(\"STYLE\");\n            return 42;\n            break;\n          case 30:\n            this.popState();\n            this.pushState(\"STYLEDEF_STYLES\");\n            return 43;\n            break;\n          case 31:\n            this.popState();\n            return 44;\n            break;\n          case 32:\n            this.pushState(\"SCALE\");\n            return 17;\n            break;\n          case 33:\n            return 18;\n            break;\n          case 34:\n            this.popState();\n            break;\n          case 35:\n            this.pushState(\"STATE\");\n            break;\n          case 36:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 25;\n            break;\n          case 37:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 26;\n            break;\n          case 38:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -10).trim();\n            return 27;\n            break;\n          case 39:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 25;\n            break;\n          case 40:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 26;\n            break;\n          case 41:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -10).trim();\n            return 27;\n            break;\n          case 42:\n            return 48;\n            break;\n          case 43:\n            return 49;\n            break;\n          case 44:\n            return 50;\n            break;\n          case 45:\n            return 51;\n            break;\n          case 46:\n            this.pushState(\"STATE_STRING\");\n            break;\n          case 47:\n            this.pushState(\"STATE_ID\");\n            return \"AS\";\n            break;\n          case 48:\n            this.popState();\n            return \"ID\";\n            break;\n          case 49:\n            this.popState();\n            break;\n          case 50:\n            return \"STATE_DESCR\";\n            break;\n          case 51:\n            return 19;\n            break;\n          case 52:\n            this.popState();\n            break;\n          case 53:\n            this.popState();\n            this.pushState(\"struct\");\n            return 20;\n            break;\n          case 54:\n            break;\n          case 55:\n            this.popState();\n            return 21;\n            break;\n          case 56:\n            break;\n          case 57:\n            this.begin(\"NOTE\");\n            return 29;\n            break;\n          case 58:\n            this.popState();\n            this.pushState(\"NOTE_ID\");\n            return 56;\n            break;\n          case 59:\n            this.popState();\n            this.pushState(\"NOTE_ID\");\n            return 57;\n            break;\n          case 60:\n            this.popState();\n            this.pushState(\"FLOATING_NOTE\");\n            break;\n          case 61:\n            this.popState();\n            this.pushState(\"FLOATING_NOTE_ID\");\n            return \"AS\";\n            break;\n          case 62:\n            break;\n          case 63:\n            return \"NOTE_TEXT\";\n            break;\n          case 64:\n            this.popState();\n            return \"ID\";\n            break;\n          case 65:\n            this.popState();\n            this.pushState(\"NOTE_TEXT\");\n            return 24;\n            break;\n          case 66:\n            this.popState();\n            yy_.yytext = yy_.yytext.substr(2).trim();\n            return 31;\n            break;\n          case 67:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 31;\n            break;\n          case 68:\n            return 6;\n            break;\n          case 69:\n            return 6;\n            break;\n          case 70:\n            return 16;\n            break;\n          case 71:\n            return 54;\n            break;\n          case 72:\n            return 24;\n            break;\n          case 73:\n            yy_.yytext = yy_.yytext.trim();\n            return 14;\n            break;\n          case 74:\n            return 15;\n            break;\n          case 75:\n            return 28;\n            break;\n          case 76:\n            return 55;\n            break;\n          case 77:\n            return 5;\n            break;\n          case 78:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:default\\b)/i, /^(?:.*direction\\s+TB[^\\n]*)/i, /^(?:.*direction\\s+BT[^\\n]*)/i, /^(?:.*direction\\s+RL[^\\n]*)/i, /^(?:.*direction\\s+LR[^\\n]*)/i, /^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n]+)/i, /^(?:[\\s]+)/i, /^(?:((?!\\n)\\s)+)/i, /^(?:#[^\\n]*)/i, /^(?:%[^\\n]*)/i, /^(?:scale\\s+)/i, /^(?:\\d+)/i, /^(?:\\s+width\\b)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:classDef\\s+)/i, /^(?:DEFAULT\\s+)/i, /^(?:\\w+\\s+)/i, /^(?:[^\\n]*)/i, /^(?:class\\s+)/i, /^(?:(\\w+)+((,\\s*\\w+)*))/i, /^(?:[^\\n]*)/i, /^(?:style\\s+)/i, /^(?:[\\w,]+\\s+)/i, /^(?:[^\\n]*)/i, /^(?:scale\\s+)/i, /^(?:\\d+)/i, /^(?:\\s+width\\b)/i, /^(?:state\\s+)/i, /^(?:.*<<fork>>)/i, /^(?:.*<<join>>)/i, /^(?:.*<<choice>>)/i, /^(?:.*\\[\\[fork\\]\\])/i, /^(?:.*\\[\\[join\\]\\])/i, /^(?:.*\\[\\[choice\\]\\])/i, /^(?:.*direction\\s+TB[^\\n]*)/i, /^(?:.*direction\\s+BT[^\\n]*)/i, /^(?:.*direction\\s+RL[^\\n]*)/i, /^(?:.*direction\\s+LR[^\\n]*)/i, /^(?:[\"])/i, /^(?:\\s*as\\s+)/i, /^(?:[^\\n\\{]*)/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:[^\\n\\s\\{]+)/i, /^(?:\\n)/i, /^(?:\\{)/i, /^(?:%%(?!\\{)[^\\n]*)/i, /^(?:\\})/i, /^(?:[\\n])/i, /^(?:note\\s+)/i, /^(?:left of\\b)/i, /^(?:right of\\b)/i, /^(?:\")/i, /^(?:\\s*as\\s*)/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:[^\\n]*)/i, /^(?:\\s*[^:\\n\\s\\-]+)/i, /^(?:\\s*:[^:\\n;]+)/i, /^(?:[\\s\\S]*?end note\\b)/i, /^(?:stateDiagram\\s+)/i, /^(?:stateDiagram-v2\\s+)/i, /^(?:hide empty description\\b)/i, /^(?:\\[\\*\\])/i, /^(?:[^:\\n\\s\\-\\{]+)/i, /^(?:\\s*:[^:\\n;]+)/i, /^(?:-->)/i, /^(?:--)/i, /^(?::::)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"LINE\": { \"rules\": [9, 10], \"inclusive\": false }, \"struct\": { \"rules\": [9, 10, 22, 26, 29, 35, 42, 43, 44, 45, 54, 55, 56, 57, 71, 72, 73, 74, 75], \"inclusive\": false }, \"FLOATING_NOTE_ID\": { \"rules\": [64], \"inclusive\": false }, \"FLOATING_NOTE\": { \"rules\": [61, 62, 63], \"inclusive\": false }, \"NOTE_TEXT\": { \"rules\": [66, 67], \"inclusive\": false }, \"NOTE_ID\": { \"rules\": [65], \"inclusive\": false }, \"NOTE\": { \"rules\": [58, 59, 60], \"inclusive\": false }, \"STYLEDEF_STYLEOPTS\": { \"rules\": [], \"inclusive\": false }, \"STYLEDEF_STYLES\": { \"rules\": [31], \"inclusive\": false }, \"STYLE_IDS\": { \"rules\": [], \"inclusive\": false }, \"STYLE\": { \"rules\": [30], \"inclusive\": false }, \"CLASS_STYLE\": { \"rules\": [28], \"inclusive\": false }, \"CLASS\": { \"rules\": [27], \"inclusive\": false }, \"CLASSDEFID\": { \"rules\": [25], \"inclusive\": false }, \"CLASSDEF\": { \"rules\": [23, 24], \"inclusive\": false }, \"acc_descr_multiline\": { \"rules\": [20, 21], \"inclusive\": false }, \"acc_descr\": { \"rules\": [18], \"inclusive\": false }, \"acc_title\": { \"rules\": [16], \"inclusive\": false }, \"SCALE\": { \"rules\": [13, 14, 33, 34], \"inclusive\": false }, \"ALIAS\": { \"rules\": [], \"inclusive\": false }, \"STATE_ID\": { \"rules\": [48], \"inclusive\": false }, \"STATE_STRING\": { \"rules\": [49, 50], \"inclusive\": false }, \"FORK_STATE\": { \"rules\": [], \"inclusive\": false }, \"STATE\": { \"rules\": [9, 10, 36, 37, 38, 39, 40, 41, 46, 47, 51, 52, 53], \"inclusive\": false }, \"ID\": { \"rules\": [9, 10], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 15, 17, 19, 22, 26, 29, 32, 35, 53, 57, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar stateDiagram_default = parser;\n\n// src/diagrams/state/stateCommon.ts\nvar DEFAULT_DIAGRAM_DIRECTION = \"TB\";\nvar DEFAULT_NESTED_DOC_DIR = \"TB\";\nvar STMT_DIRECTION = \"dir\";\nvar STMT_STATE = \"state\";\nvar STMT_RELATION = \"relation\";\nvar STMT_CLASSDEF = \"classDef\";\nvar STMT_STYLEDEF = \"style\";\nvar STMT_APPLYCLASS = \"applyClass\";\nvar DEFAULT_STATE_TYPE = \"default\";\nvar DIVIDER_TYPE = \"divider\";\nvar G_EDGE_STYLE = \"fill:none\";\nvar G_EDGE_ARROWHEADSTYLE = \"fill: #333\";\nvar G_EDGE_LABELPOS = \"c\";\nvar G_EDGE_LABELTYPE = \"text\";\nvar G_EDGE_THICKNESS = \"normal\";\nvar SHAPE_STATE = \"rect\";\nvar SHAPE_STATE_WITH_DESC = \"rectWithTitle\";\nvar SHAPE_START = \"stateStart\";\nvar SHAPE_END = \"stateEnd\";\nvar SHAPE_DIVIDER = \"divider\";\nvar SHAPE_GROUP = \"roundedWithTitle\";\nvar SHAPE_NOTE = \"note\";\nvar SHAPE_NOTEGROUP = \"noteGroup\";\nvar CSS_DIAGRAM = \"statediagram\";\nvar CSS_STATE = \"state\";\nvar CSS_DIAGRAM_STATE = `${CSS_DIAGRAM}-${CSS_STATE}`;\nvar CSS_EDGE = \"transition\";\nvar CSS_NOTE = \"note\";\nvar CSS_NOTE_EDGE = \"note-edge\";\nvar CSS_EDGE_NOTE_EDGE = `${CSS_EDGE} ${CSS_NOTE_EDGE}`;\nvar CSS_DIAGRAM_NOTE = `${CSS_DIAGRAM}-${CSS_NOTE}`;\nvar CSS_CLUSTER = \"cluster\";\nvar CSS_DIAGRAM_CLUSTER = `${CSS_DIAGRAM}-${CSS_CLUSTER}`;\nvar CSS_CLUSTER_ALT = \"cluster-alt\";\nvar CSS_DIAGRAM_CLUSTER_ALT = `${CSS_DIAGRAM}-${CSS_CLUSTER_ALT}`;\nvar PARENT = \"parent\";\nvar NOTE = \"note\";\nvar DOMID_STATE = \"state\";\nvar DOMID_TYPE_SPACER = \"----\";\nvar NOTE_ID = `${DOMID_TYPE_SPACER}${NOTE}`;\nvar PARENT_ID = `${DOMID_TYPE_SPACER}${PARENT}`;\n\n// src/diagrams/state/stateRenderer-v3-unified.ts\nvar getDir = /* @__PURE__ */ __name((parsedItem, defaultDir = DEFAULT_NESTED_DOC_DIR) => {\n  if (!parsedItem.doc) {\n    return defaultDir;\n  }\n  let dir = defaultDir;\n  for (const parsedItemDoc of parsedItem.doc) {\n    if (parsedItemDoc.stmt === \"dir\") {\n      dir = parsedItemDoc.value;\n    }\n  }\n  return dir;\n}, \"getDir\");\nvar getClasses = /* @__PURE__ */ __name(function(text, diagramObj) {\n  return diagramObj.db.getClasses();\n}, \"getClasses\");\nvar draw = /* @__PURE__ */ __name(async function(text, id, _version, diag) {\n  log.info(\"REF0:\");\n  log.info(\"Drawing state diagram (v2)\", id);\n  const { securityLevel, state: conf, layout } = getConfig();\n  diag.db.extract(diag.db.getRootDocV2());\n  const data4Layout = diag.db.getData();\n  const svg = getDiagramElement(id, securityLevel);\n  data4Layout.type = diag.type;\n  data4Layout.layoutAlgorithm = layout;\n  data4Layout.nodeSpacing = conf?.nodeSpacing || 50;\n  data4Layout.rankSpacing = conf?.rankSpacing || 50;\n  data4Layout.markers = [\"barb\"];\n  data4Layout.diagramId = id;\n  await render(data4Layout, svg);\n  const padding = 8;\n  utils_default.insertTitle(\n    svg,\n    \"statediagramTitleText\",\n    conf?.titleTopMargin ?? 25,\n    diag.db.getDiagramTitle()\n  );\n  setupViewPortForSVG(svg, padding, CSS_DIAGRAM, conf?.useMaxWidth ?? true);\n}, \"draw\");\nvar stateRenderer_v3_unified_default = {\n  getClasses,\n  draw,\n  getDir\n};\n\n// src/diagrams/state/dataFetcher.js\nvar nodeDb = /* @__PURE__ */ new Map();\nvar graphItemCount = 0;\nfunction stateDomId(itemId = \"\", counter = 0, type = \"\", typeSpacer = DOMID_TYPE_SPACER) {\n  const typeStr = type !== null && type.length > 0 ? `${typeSpacer}${type}` : \"\";\n  return `${DOMID_STATE}-${itemId}${typeStr}-${counter}`;\n}\n__name(stateDomId, \"stateDomId\");\nvar setupDoc = /* @__PURE__ */ __name((parentParsedItem, doc, diagramStates, nodes, edges, altFlag, look, classes) => {\n  log.trace(\"items\", doc);\n  doc.forEach((item) => {\n    switch (item.stmt) {\n      case STMT_STATE:\n        dataFetcher(parentParsedItem, item, diagramStates, nodes, edges, altFlag, look, classes);\n        break;\n      case DEFAULT_STATE_TYPE:\n        dataFetcher(parentParsedItem, item, diagramStates, nodes, edges, altFlag, look, classes);\n        break;\n      case STMT_RELATION:\n        {\n          dataFetcher(\n            parentParsedItem,\n            item.state1,\n            diagramStates,\n            nodes,\n            edges,\n            altFlag,\n            look,\n            classes\n          );\n          dataFetcher(\n            parentParsedItem,\n            item.state2,\n            diagramStates,\n            nodes,\n            edges,\n            altFlag,\n            look,\n            classes\n          );\n          const edgeData = {\n            id: \"edge\" + graphItemCount,\n            start: item.state1.id,\n            end: item.state2.id,\n            arrowhead: \"normal\",\n            arrowTypeEnd: \"arrow_barb\",\n            style: G_EDGE_STYLE,\n            labelStyle: \"\",\n            label: common_default.sanitizeText(item.description, getConfig()),\n            arrowheadStyle: G_EDGE_ARROWHEADSTYLE,\n            labelpos: G_EDGE_LABELPOS,\n            labelType: G_EDGE_LABELTYPE,\n            thickness: G_EDGE_THICKNESS,\n            classes: CSS_EDGE,\n            look\n          };\n          edges.push(edgeData);\n          graphItemCount++;\n        }\n        break;\n    }\n  });\n}, \"setupDoc\");\nvar getDir2 = /* @__PURE__ */ __name((parsedItem, defaultDir = DEFAULT_NESTED_DOC_DIR) => {\n  let dir = defaultDir;\n  if (parsedItem.doc) {\n    for (const parsedItemDoc of parsedItem.doc) {\n      if (parsedItemDoc.stmt === \"dir\") {\n        dir = parsedItemDoc.value;\n      }\n    }\n  }\n  return dir;\n}, \"getDir\");\nfunction insertOrUpdateNode(nodes, nodeData, classes) {\n  if (!nodeData.id || nodeData.id === \"</join></fork>\" || nodeData.id === \"</choice>\") {\n    return;\n  }\n  if (nodeData.cssClasses) {\n    if (!Array.isArray(nodeData.cssCompiledStyles)) {\n      nodeData.cssCompiledStyles = [];\n    }\n    nodeData.cssClasses.split(\" \").forEach((cssClass) => {\n      if (classes.get(cssClass)) {\n        const classDef = classes.get(cssClass);\n        nodeData.cssCompiledStyles = [...nodeData.cssCompiledStyles, ...classDef.styles];\n      }\n    });\n  }\n  const existingNodeData = nodes.find((node) => node.id === nodeData.id);\n  if (existingNodeData) {\n    Object.assign(existingNodeData, nodeData);\n  } else {\n    nodes.push(nodeData);\n  }\n}\n__name(insertOrUpdateNode, \"insertOrUpdateNode\");\nfunction getClassesFromDbInfo(dbInfoItem) {\n  return dbInfoItem?.classes?.join(\" \") ?? \"\";\n}\n__name(getClassesFromDbInfo, \"getClassesFromDbInfo\");\nfunction getStylesFromDbInfo(dbInfoItem) {\n  return dbInfoItem?.styles ?? [];\n}\n__name(getStylesFromDbInfo, \"getStylesFromDbInfo\");\nvar dataFetcher = /* @__PURE__ */ __name((parent, parsedItem, diagramStates, nodes, edges, altFlag, look, classes) => {\n  const itemId = parsedItem.id;\n  const dbState = diagramStates.get(itemId);\n  const classStr = getClassesFromDbInfo(dbState);\n  const style = getStylesFromDbInfo(dbState);\n  log.info(\"dataFetcher parsedItem\", parsedItem, dbState, style);\n  if (itemId !== \"root\") {\n    let shape = SHAPE_STATE;\n    if (parsedItem.start === true) {\n      shape = SHAPE_START;\n    } else if (parsedItem.start === false) {\n      shape = SHAPE_END;\n    }\n    if (parsedItem.type !== DEFAULT_STATE_TYPE) {\n      shape = parsedItem.type;\n    }\n    if (!nodeDb.get(itemId)) {\n      nodeDb.set(itemId, {\n        id: itemId,\n        shape,\n        description: common_default.sanitizeText(itemId, getConfig()),\n        cssClasses: `${classStr} ${CSS_DIAGRAM_STATE}`,\n        cssStyles: style\n      });\n    }\n    const newNode = nodeDb.get(itemId);\n    if (parsedItem.description) {\n      if (Array.isArray(newNode.description)) {\n        newNode.shape = SHAPE_STATE_WITH_DESC;\n        newNode.description.push(parsedItem.description);\n      } else {\n        if (newNode.description?.length > 0) {\n          newNode.shape = SHAPE_STATE_WITH_DESC;\n          if (newNode.description === itemId) {\n            newNode.description = [parsedItem.description];\n          } else {\n            newNode.description = [newNode.description, parsedItem.description];\n          }\n        } else {\n          newNode.shape = SHAPE_STATE;\n          newNode.description = parsedItem.description;\n        }\n      }\n      newNode.description = common_default.sanitizeTextOrArray(newNode.description, getConfig());\n    }\n    if (newNode.description?.length === 1 && newNode.shape === SHAPE_STATE_WITH_DESC) {\n      if (newNode.type === \"group\") {\n        newNode.shape = SHAPE_GROUP;\n      } else {\n        newNode.shape = SHAPE_STATE;\n      }\n    }\n    if (!newNode.type && parsedItem.doc) {\n      log.info(\"Setting cluster for XCX\", itemId, getDir2(parsedItem));\n      newNode.type = \"group\";\n      newNode.isGroup = true;\n      newNode.dir = getDir2(parsedItem);\n      newNode.shape = parsedItem.type === DIVIDER_TYPE ? SHAPE_DIVIDER : SHAPE_GROUP;\n      newNode.cssClasses = `${newNode.cssClasses} ${CSS_DIAGRAM_CLUSTER} ${altFlag ? CSS_DIAGRAM_CLUSTER_ALT : \"\"}`;\n    }\n    const nodeData = {\n      labelStyle: \"\",\n      shape: newNode.shape,\n      label: newNode.description,\n      cssClasses: newNode.cssClasses,\n      cssCompiledStyles: [],\n      cssStyles: newNode.cssStyles,\n      id: itemId,\n      dir: newNode.dir,\n      domId: stateDomId(itemId, graphItemCount),\n      type: newNode.type,\n      isGroup: newNode.type === \"group\",\n      padding: 8,\n      rx: 10,\n      ry: 10,\n      look\n    };\n    if (nodeData.shape === SHAPE_DIVIDER) {\n      nodeData.label = \"\";\n    }\n    if (parent && parent.id !== \"root\") {\n      log.trace(\"Setting node \", itemId, \" to be child of its parent \", parent.id);\n      nodeData.parentId = parent.id;\n    }\n    nodeData.centerLabel = true;\n    if (parsedItem.note) {\n      const noteData = {\n        labelStyle: \"\",\n        shape: SHAPE_NOTE,\n        label: parsedItem.note.text,\n        cssClasses: CSS_DIAGRAM_NOTE,\n        // useHtmlLabels: false,\n        cssStyles: [],\n        cssCompilesStyles: [],\n        id: itemId + NOTE_ID + \"-\" + graphItemCount,\n        domId: stateDomId(itemId, graphItemCount, NOTE),\n        type: newNode.type,\n        isGroup: newNode.type === \"group\",\n        padding: getConfig().flowchart.padding,\n        look,\n        position: parsedItem.note.position\n      };\n      const parentNodeId = itemId + PARENT_ID;\n      const groupData = {\n        labelStyle: \"\",\n        shape: SHAPE_NOTEGROUP,\n        label: parsedItem.note.text,\n        cssClasses: newNode.cssClasses,\n        cssStyles: [],\n        id: itemId + PARENT_ID,\n        domId: stateDomId(itemId, graphItemCount, PARENT),\n        type: \"group\",\n        isGroup: true,\n        padding: 16,\n        //getConfig().flowchart.padding\n        look,\n        position: parsedItem.note.position\n      };\n      graphItemCount++;\n      groupData.id = parentNodeId;\n      noteData.parentId = parentNodeId;\n      insertOrUpdateNode(nodes, groupData, classes);\n      insertOrUpdateNode(nodes, noteData, classes);\n      insertOrUpdateNode(nodes, nodeData, classes);\n      let from = itemId;\n      let to = noteData.id;\n      if (parsedItem.note.position === \"left of\") {\n        from = noteData.id;\n        to = itemId;\n      }\n      edges.push({\n        id: from + \"-\" + to,\n        start: from,\n        end: to,\n        arrowhead: \"none\",\n        arrowTypeEnd: \"\",\n        style: G_EDGE_STYLE,\n        labelStyle: \"\",\n        classes: CSS_EDGE_NOTE_EDGE,\n        arrowheadStyle: G_EDGE_ARROWHEADSTYLE,\n        labelpos: G_EDGE_LABELPOS,\n        labelType: G_EDGE_LABELTYPE,\n        thickness: G_EDGE_THICKNESS,\n        look\n      });\n    } else {\n      insertOrUpdateNode(nodes, nodeData, classes);\n    }\n  }\n  if (parsedItem.doc) {\n    log.trace(\"Adding nodes children \");\n    setupDoc(parsedItem, parsedItem.doc, diagramStates, nodes, edges, !altFlag, look, classes);\n  }\n}, \"dataFetcher\");\nvar reset = /* @__PURE__ */ __name(() => {\n  nodeDb.clear();\n  graphItemCount = 0;\n}, \"reset\");\n\n// src/diagrams/state/stateDb.js\nvar START_NODE = \"[*]\";\nvar START_TYPE = \"start\";\nvar END_NODE = START_NODE;\nvar END_TYPE = \"end\";\nvar COLOR_KEYWORD = \"color\";\nvar FILL_KEYWORD = \"fill\";\nvar BG_FILL = \"bgFill\";\nvar STYLECLASS_SEP = \",\";\nfunction newClassesList() {\n  return /* @__PURE__ */ new Map();\n}\n__name(newClassesList, \"newClassesList\");\nvar newDoc = /* @__PURE__ */ __name(() => {\n  return {\n    /** @type {{ id1: string, id2: string, relationTitle: string }[]} */\n    relations: [],\n    states: /* @__PURE__ */ new Map(),\n    documents: {}\n  };\n}, \"newDoc\");\nvar clone = /* @__PURE__ */ __name((o) => JSON.parse(JSON.stringify(o)), \"clone\");\nvar StateDB = class {\n  static {\n    __name(this, \"StateDB\");\n  }\n  /**\n   * @param {1 | 2} version - v1 renderer or v2 renderer.\n   */\n  constructor(version) {\n    this.clear();\n    this.version = version;\n    this.setRootDoc = this.setRootDoc.bind(this);\n    this.getDividerId = this.getDividerId.bind(this);\n    this.setDirection = this.setDirection.bind(this);\n    this.trimColon = this.trimColon.bind(this);\n  }\n  /**\n   * @private\n   * @type {1 | 2}\n   */\n  version;\n  /**\n   * @private\n   * @type {Array}\n   */\n  nodes = [];\n  /**\n   * @private\n   * @type {Array}\n   */\n  edges = [];\n  /**\n   * @private\n   * @type {Array}\n   */\n  rootDoc = [];\n  /**\n   * @private\n   * @type {Map<string, any>}\n   */\n  classes = newClassesList();\n  // style classes defined by a classDef\n  /**\n   * @private\n   * @type {Object}\n   */\n  documents = {\n    root: newDoc()\n  };\n  /**\n   * @private\n   * @type {Object}\n   */\n  currentDocument = this.documents.root;\n  /**\n   * @private\n   * @type {number}\n   */\n  startEndCount = 0;\n  /**\n   * @private\n   * @type {number}\n   */\n  dividerCnt = 0;\n  static relationType = {\n    AGGREGATION: 0,\n    EXTENSION: 1,\n    COMPOSITION: 2,\n    DEPENDENCY: 3\n  };\n  setRootDoc(o) {\n    log.info(\"Setting root doc\", o);\n    this.rootDoc = o;\n    if (this.version === 1) {\n      this.extract(o);\n    } else {\n      this.extract(this.getRootDocV2());\n    }\n  }\n  getRootDoc() {\n    return this.rootDoc;\n  }\n  /**\n   * @private\n   * @param {Object} parent\n   * @param {Object} node\n   * @param {boolean} first\n   */\n  docTranslator(parent, node, first) {\n    if (node.stmt === STMT_RELATION) {\n      this.docTranslator(parent, node.state1, true);\n      this.docTranslator(parent, node.state2, false);\n    } else {\n      if (node.stmt === STMT_STATE) {\n        if (node.id === \"[*]\") {\n          node.id = first ? parent.id + \"_start\" : parent.id + \"_end\";\n          node.start = first;\n        } else {\n          node.id = node.id.trim();\n        }\n      }\n      if (node.doc) {\n        const doc = [];\n        let currentDoc = [];\n        let i;\n        for (i = 0; i < node.doc.length; i++) {\n          if (node.doc[i].type === DIVIDER_TYPE) {\n            const newNode = clone(node.doc[i]);\n            newNode.doc = clone(currentDoc);\n            doc.push(newNode);\n            currentDoc = [];\n          } else {\n            currentDoc.push(node.doc[i]);\n          }\n        }\n        if (doc.length > 0 && currentDoc.length > 0) {\n          const newNode = {\n            stmt: STMT_STATE,\n            id: generateId(),\n            type: \"divider\",\n            doc: clone(currentDoc)\n          };\n          doc.push(clone(newNode));\n          node.doc = doc;\n        }\n        node.doc.forEach((docNode) => this.docTranslator(node, docNode, true));\n      }\n    }\n  }\n  /**\n   * @private\n   */\n  getRootDocV2() {\n    this.docTranslator({ id: \"root\" }, { id: \"root\", doc: this.rootDoc }, true);\n    return { id: \"root\", doc: this.rootDoc };\n  }\n  /**\n   * Convert all of the statements (stmts) that were parsed into states and relationships.\n   * This is done because a state diagram may have nested sections,\n   * where each section is a 'document' and has its own set of statements.\n   * Ex: the section within a fork has its own statements, and incoming and outgoing statements\n   * refer to the fork as a whole (document).\n   * See the parser grammar:  the definition of a document is a document then a 'line', where a line can be a statement.\n   * This will push the statement into the list of statements for the current document.\n   * @private\n   * @param _doc\n   */\n  extract(_doc) {\n    let doc;\n    if (_doc.doc) {\n      doc = _doc.doc;\n    } else {\n      doc = _doc;\n    }\n    log.info(doc);\n    this.clear(true);\n    log.info(\"Extract initial document:\", doc);\n    doc.forEach((item) => {\n      log.warn(\"Statement\", item.stmt);\n      switch (item.stmt) {\n        case STMT_STATE:\n          this.addState(\n            item.id.trim(),\n            item.type,\n            item.doc,\n            item.description,\n            item.note,\n            item.classes,\n            item.styles,\n            item.textStyles\n          );\n          break;\n        case STMT_RELATION:\n          this.addRelation(item.state1, item.state2, item.description);\n          break;\n        case STMT_CLASSDEF:\n          this.addStyleClass(item.id.trim(), item.classes);\n          break;\n        case STMT_STYLEDEF:\n          {\n            const ids = item.id.trim().split(\",\");\n            const styles = item.styleClass.split(\",\");\n            ids.forEach((id) => {\n              let foundState = this.getState(id);\n              if (foundState === void 0) {\n                const trimmedId = id.trim();\n                this.addState(trimmedId);\n                foundState = this.getState(trimmedId);\n              }\n              foundState.styles = styles.map((s) => s.replace(/;/g, \"\")?.trim());\n            });\n          }\n          break;\n        case STMT_APPLYCLASS:\n          this.setCssClass(item.id.trim(), item.styleClass);\n          break;\n      }\n    });\n    const diagramStates = this.getStates();\n    const config = getConfig();\n    const look = config.look;\n    reset();\n    dataFetcher(\n      void 0,\n      this.getRootDocV2(),\n      diagramStates,\n      this.nodes,\n      this.edges,\n      true,\n      look,\n      this.classes\n    );\n    this.nodes.forEach((node) => {\n      if (Array.isArray(node.label)) {\n        node.description = node.label.slice(1);\n        if (node.isGroup && node.description.length > 0) {\n          throw new Error(\n            \"Group nodes can only have label. Remove the additional description for node [\" + node.id + \"]\"\n          );\n        }\n        node.label = node.label[0];\n      }\n    });\n  }\n  /**\n   * Function called by parser when a node definition has been found.\n   *\n   * @param {null | string} id\n   * @param {null | string} type\n   * @param {null | string} doc\n   * @param {null | string | string[]} descr - description for the state. Can be a string or a list or strings\n   * @param {null | string} note\n   * @param {null | string | string[]} classes - class styles to apply to this state. Can be a string (1 style) or an array of styles. If it's just 1 class, convert it to an array of that 1 class.\n   * @param {null | string | string[]} styles - styles to apply to this state. Can be a string (1 style) or an array of styles. If it's just 1 style, convert it to an array of that 1 style.\n   * @param {null | string | string[]} textStyles - text styles to apply to this state. Can be a string (1 text test) or an array of text styles. If it's just 1 text style, convert it to an array of that 1 text style.\n   */\n  addState(id, type = DEFAULT_STATE_TYPE, doc = null, descr = null, note = null, classes = null, styles = null, textStyles = null) {\n    const trimmedId = id?.trim();\n    if (!this.currentDocument.states.has(trimmedId)) {\n      log.info(\"Adding state \", trimmedId, descr);\n      this.currentDocument.states.set(trimmedId, {\n        id: trimmedId,\n        descriptions: [],\n        type,\n        doc,\n        note,\n        classes: [],\n        styles: [],\n        textStyles: []\n      });\n    } else {\n      if (!this.currentDocument.states.get(trimmedId).doc) {\n        this.currentDocument.states.get(trimmedId).doc = doc;\n      }\n      if (!this.currentDocument.states.get(trimmedId).type) {\n        this.currentDocument.states.get(trimmedId).type = type;\n      }\n    }\n    if (descr) {\n      log.info(\"Setting state description\", trimmedId, descr);\n      if (typeof descr === \"string\") {\n        this.addDescription(trimmedId, descr.trim());\n      }\n      if (typeof descr === \"object\") {\n        descr.forEach((des) => this.addDescription(trimmedId, des.trim()));\n      }\n    }\n    if (note) {\n      const doc2 = this.currentDocument.states.get(trimmedId);\n      doc2.note = note;\n      doc2.note.text = common_default.sanitizeText(doc2.note.text, getConfig());\n    }\n    if (classes) {\n      log.info(\"Setting state classes\", trimmedId, classes);\n      const classesList = typeof classes === \"string\" ? [classes] : classes;\n      classesList.forEach((cssClass) => this.setCssClass(trimmedId, cssClass.trim()));\n    }\n    if (styles) {\n      log.info(\"Setting state styles\", trimmedId, styles);\n      const stylesList = typeof styles === \"string\" ? [styles] : styles;\n      stylesList.forEach((style) => this.setStyle(trimmedId, style.trim()));\n    }\n    if (textStyles) {\n      log.info(\"Setting state styles\", trimmedId, styles);\n      const textStylesList = typeof textStyles === \"string\" ? [textStyles] : textStyles;\n      textStylesList.forEach((textStyle) => this.setTextStyle(trimmedId, textStyle.trim()));\n    }\n  }\n  clear(saveCommon) {\n    this.nodes = [];\n    this.edges = [];\n    this.documents = {\n      root: newDoc()\n    };\n    this.currentDocument = this.documents.root;\n    this.startEndCount = 0;\n    this.classes = newClassesList();\n    if (!saveCommon) {\n      clear();\n    }\n  }\n  getState(id) {\n    return this.currentDocument.states.get(id);\n  }\n  getStates() {\n    return this.currentDocument.states;\n  }\n  logDocuments() {\n    log.info(\"Documents = \", this.documents);\n  }\n  getRelations() {\n    return this.currentDocument.relations;\n  }\n  /**\n   * If the id is a start node ( [*] ), then return a new id constructed from\n   * the start node name and the current start node count.\n   * else return the given id\n   *\n   * @param {string} id\n   * @returns {string} - the id (original or constructed)\n   * @private\n   */\n  startIdIfNeeded(id = \"\") {\n    let fixedId = id;\n    if (id === START_NODE) {\n      this.startEndCount++;\n      fixedId = `${START_TYPE}${this.startEndCount}`;\n    }\n    return fixedId;\n  }\n  /**\n   * If the id is a start node ( [*] ), then return the start type ('start')\n   * else return the given type\n   *\n   * @param {string} id\n   * @param {string} type\n   * @returns {string} - the type that should be used\n   * @private\n   */\n  startTypeIfNeeded(id = \"\", type = DEFAULT_STATE_TYPE) {\n    return id === START_NODE ? START_TYPE : type;\n  }\n  /**\n   * If the id is an end node ( [*] ), then return a new id constructed from\n   * the end node name and the current start_end node count.\n   * else return the given id\n   *\n   * @param {string} id\n   * @returns {string} - the id (original or constructed)\n   * @private\n   */\n  endIdIfNeeded(id = \"\") {\n    let fixedId = id;\n    if (id === END_NODE) {\n      this.startEndCount++;\n      fixedId = `${END_TYPE}${this.startEndCount}`;\n    }\n    return fixedId;\n  }\n  /**\n   * If the id is an end node ( [*] ), then return the end type\n   * else return the given type\n   *\n   * @param {string} id\n   * @param {string} type\n   * @returns {string} - the type that should be used\n   * @private\n   */\n  endTypeIfNeeded(id = \"\", type = DEFAULT_STATE_TYPE) {\n    return id === END_NODE ? END_TYPE : type;\n  }\n  /**\n   *\n   * @param item1\n   * @param item2\n   * @param relationTitle\n   */\n  addRelationObjs(item1, item2, relationTitle) {\n    let id1 = this.startIdIfNeeded(item1.id.trim());\n    let type1 = this.startTypeIfNeeded(item1.id.trim(), item1.type);\n    let id2 = this.startIdIfNeeded(item2.id.trim());\n    let type2 = this.startTypeIfNeeded(item2.id.trim(), item2.type);\n    this.addState(\n      id1,\n      type1,\n      item1.doc,\n      item1.description,\n      item1.note,\n      item1.classes,\n      item1.styles,\n      item1.textStyles\n    );\n    this.addState(\n      id2,\n      type2,\n      item2.doc,\n      item2.description,\n      item2.note,\n      item2.classes,\n      item2.styles,\n      item2.textStyles\n    );\n    this.currentDocument.relations.push({\n      id1,\n      id2,\n      relationTitle: common_default.sanitizeText(relationTitle, getConfig())\n    });\n  }\n  /**\n   * Add a relation between two items.  The items may be full objects or just the string id of a state.\n   *\n   * @param {string | object} item1\n   * @param {string | object} item2\n   * @param {string} title\n   */\n  addRelation(item1, item2, title) {\n    if (typeof item1 === \"object\") {\n      this.addRelationObjs(item1, item2, title);\n    } else {\n      const id1 = this.startIdIfNeeded(item1.trim());\n      const type1 = this.startTypeIfNeeded(item1);\n      const id2 = this.endIdIfNeeded(item2.trim());\n      const type2 = this.endTypeIfNeeded(item2);\n      this.addState(id1, type1);\n      this.addState(id2, type2);\n      this.currentDocument.relations.push({\n        id1,\n        id2,\n        title: common_default.sanitizeText(title, getConfig())\n      });\n    }\n  }\n  addDescription(id, descr) {\n    const theState = this.currentDocument.states.get(id);\n    const _descr = descr.startsWith(\":\") ? descr.replace(\":\", \"\").trim() : descr;\n    theState.descriptions.push(common_default.sanitizeText(_descr, getConfig()));\n  }\n  cleanupLabel(label) {\n    if (label.substring(0, 1) === \":\") {\n      return label.substr(2).trim();\n    } else {\n      return label.trim();\n    }\n  }\n  getDividerId() {\n    this.dividerCnt++;\n    return \"divider-id-\" + this.dividerCnt;\n  }\n  /**\n   * Called when the parser comes across a (style) class definition\n   * @example classDef my-style fill:#f96;\n   *\n   * @param {string} id - the id of this (style) class\n   * @param  {string | null} styleAttributes - the string with 1 or more style attributes (each separated by a comma)\n   */\n  addStyleClass(id, styleAttributes = \"\") {\n    if (!this.classes.has(id)) {\n      this.classes.set(id, { id, styles: [], textStyles: [] });\n    }\n    const foundClass = this.classes.get(id);\n    if (styleAttributes !== void 0 && styleAttributes !== null) {\n      styleAttributes.split(STYLECLASS_SEP).forEach((attrib) => {\n        const fixedAttrib = attrib.replace(/([^;]*);/, \"$1\").trim();\n        if (RegExp(COLOR_KEYWORD).exec(attrib)) {\n          const newStyle1 = fixedAttrib.replace(FILL_KEYWORD, BG_FILL);\n          const newStyle2 = newStyle1.replace(COLOR_KEYWORD, FILL_KEYWORD);\n          foundClass.textStyles.push(newStyle2);\n        }\n        foundClass.styles.push(fixedAttrib);\n      });\n    }\n  }\n  /**\n   * Return all of the style classes\n   * @returns {{} | any | classes}\n   */\n  getClasses() {\n    return this.classes;\n  }\n  /**\n   * Add a (style) class or css class to a state with the given id.\n   * If the state isn't already in the list of known states, add it.\n   * Might be called by parser when a style class or CSS class should be applied to a state\n   *\n   * @param {string | string[]} itemIds The id or a list of ids of the item(s) to apply the css class to\n   * @param {string} cssClassName CSS class name\n   */\n  setCssClass(itemIds, cssClassName) {\n    itemIds.split(\",\").forEach((id) => {\n      let foundState = this.getState(id);\n      if (foundState === void 0) {\n        const trimmedId = id.trim();\n        this.addState(trimmedId);\n        foundState = this.getState(trimmedId);\n      }\n      foundState.classes.push(cssClassName);\n    });\n  }\n  /**\n   * Add a style to a state with the given id.\n   * @example style stateId fill:#f9f,stroke:#333,stroke-width:4px\n   *   where 'style' is the keyword\n   *   stateId is the id of a state\n   *   the rest of the string is the styleText (all of the attributes to be applied to the state)\n   *\n   * @param itemId The id of item to apply the style to\n   * @param styleText - the text of the attributes for the style\n   */\n  setStyle(itemId, styleText) {\n    const item = this.getState(itemId);\n    if (item !== void 0) {\n      item.styles.push(styleText);\n    }\n  }\n  /**\n   * Add a text style to a state with the given id\n   *\n   * @param itemId The id of item to apply the css class to\n   * @param cssClassName CSS class name\n   */\n  setTextStyle(itemId, cssClassName) {\n    const item = this.getState(itemId);\n    if (item !== void 0) {\n      item.textStyles.push(cssClassName);\n    }\n  }\n  /**\n   * Finds the direction statement in the root document.\n   * @private\n   * @returns {{ value: string } | undefined} - the direction statement if present\n   */\n  getDirectionStatement() {\n    return this.rootDoc.find((doc) => doc.stmt === STMT_DIRECTION);\n  }\n  getDirection() {\n    return this.getDirectionStatement()?.value ?? DEFAULT_DIAGRAM_DIRECTION;\n  }\n  setDirection(dir) {\n    const doc = this.getDirectionStatement();\n    if (doc) {\n      doc.value = dir;\n    } else {\n      this.rootDoc.unshift({ stmt: STMT_DIRECTION, value: dir });\n    }\n  }\n  trimColon(str) {\n    return str && str[0] === \":\" ? str.substr(1).trim() : str.trim();\n  }\n  getData() {\n    const config = getConfig();\n    return {\n      nodes: this.nodes,\n      edges: this.edges,\n      other: {},\n      config,\n      direction: getDir(this.getRootDocV2())\n    };\n  }\n  getConfig() {\n    return getConfig().state;\n  }\n  getAccTitle = getAccTitle;\n  setAccTitle = setAccTitle;\n  getAccDescription = getAccDescription;\n  setAccDescription = setAccDescription;\n  setDiagramTitle = setDiagramTitle;\n  getDiagramTitle = getDiagramTitle;\n};\n\n// src/diagrams/state/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `\ndefs #statediagram-barbEnd {\n    fill: ${options.transitionColor};\n    stroke: ${options.transitionColor};\n  }\ng.stateGroup text {\n  fill: ${options.nodeBorder};\n  stroke: none;\n  font-size: 10px;\n}\ng.stateGroup text {\n  fill: ${options.textColor};\n  stroke: none;\n  font-size: 10px;\n\n}\ng.stateGroup .state-title {\n  font-weight: bolder;\n  fill: ${options.stateLabelColor};\n}\n\ng.stateGroup rect {\n  fill: ${options.mainBkg};\n  stroke: ${options.nodeBorder};\n}\n\ng.stateGroup line {\n  stroke: ${options.lineColor};\n  stroke-width: 1;\n}\n\n.transition {\n  stroke: ${options.transitionColor};\n  stroke-width: 1;\n  fill: none;\n}\n\n.stateGroup .composit {\n  fill: ${options.background};\n  border-bottom: 1px\n}\n\n.stateGroup .alt-composit {\n  fill: #e0e0e0;\n  border-bottom: 1px\n}\n\n.state-note {\n  stroke: ${options.noteBorderColor};\n  fill: ${options.noteBkgColor};\n\n  text {\n    fill: ${options.noteTextColor};\n    stroke: none;\n    font-size: 10px;\n  }\n}\n\n.stateLabel .box {\n  stroke: none;\n  stroke-width: 0;\n  fill: ${options.mainBkg};\n  opacity: 0.5;\n}\n\n.edgeLabel .label rect {\n  fill: ${options.labelBackgroundColor};\n  opacity: 0.5;\n}\n.edgeLabel {\n  background-color: ${options.edgeLabelBackground};\n  p {\n    background-color: ${options.edgeLabelBackground};\n  }\n  rect {\n    opacity: 0.5;\n    background-color: ${options.edgeLabelBackground};\n    fill: ${options.edgeLabelBackground};\n  }\n  text-align: center;\n}\n.edgeLabel .label text {\n  fill: ${options.transitionLabelColor || options.tertiaryTextColor};\n}\n.label div .edgeLabel {\n  color: ${options.transitionLabelColor || options.tertiaryTextColor};\n}\n\n.stateLabel text {\n  fill: ${options.stateLabelColor};\n  font-size: 10px;\n  font-weight: bold;\n}\n\n.node circle.state-start {\n  fill: ${options.specialStateColor};\n  stroke: ${options.specialStateColor};\n}\n\n.node .fork-join {\n  fill: ${options.specialStateColor};\n  stroke: ${options.specialStateColor};\n}\n\n.node circle.state-end {\n  fill: ${options.innerEndBackground};\n  stroke: ${options.background};\n  stroke-width: 1.5\n}\n.end-state-inner {\n  fill: ${options.compositeBackground || options.background};\n  // stroke: ${options.background};\n  stroke-width: 1.5\n}\n\n.node rect {\n  fill: ${options.stateBkg || options.mainBkg};\n  stroke: ${options.stateBorder || options.nodeBorder};\n  stroke-width: 1px;\n}\n.node polygon {\n  fill: ${options.mainBkg};\n  stroke: ${options.stateBorder || options.nodeBorder};;\n  stroke-width: 1px;\n}\n#statediagram-barbEnd {\n  fill: ${options.lineColor};\n}\n\n.statediagram-cluster rect {\n  fill: ${options.compositeTitleBackground};\n  stroke: ${options.stateBorder || options.nodeBorder};\n  stroke-width: 1px;\n}\n\n.cluster-label, .nodeLabel {\n  color: ${options.stateLabelColor};\n  // line-height: 1;\n}\n\n.statediagram-cluster rect.outer {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state .divider {\n  stroke: ${options.stateBorder || options.nodeBorder};\n}\n\n.statediagram-state .title-state {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-cluster.statediagram-cluster .inner {\n  fill: ${options.compositeBackground || options.background};\n}\n.statediagram-cluster.statediagram-cluster-alt .inner {\n  fill: ${options.altBackground ? options.altBackground : \"#efefef\"};\n}\n\n.statediagram-cluster .inner {\n  rx:0;\n  ry:0;\n}\n\n.statediagram-state rect.basic {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state rect.divider {\n  stroke-dasharray: 10,10;\n  fill: ${options.altBackground ? options.altBackground : \"#efefef\"};\n}\n\n.note-edge {\n  stroke-dasharray: 5;\n}\n\n.statediagram-note rect {\n  fill: ${options.noteBkgColor};\n  stroke: ${options.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n.statediagram-note rect {\n  fill: ${options.noteBkgColor};\n  stroke: ${options.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n\n.statediagram-note text {\n  fill: ${options.noteTextColor};\n}\n\n.statediagram-note .nodeLabel {\n  color: ${options.noteTextColor};\n}\n.statediagram .edgeLabel {\n  color: red; // ${options.noteTextColor};\n}\n\n#dependencyStart, #dependencyEnd {\n  fill: ${options.lineColor};\n  stroke: ${options.lineColor};\n  stroke-width: 1;\n}\n\n.statediagramTitleText {\n  text-anchor: middle;\n  font-size: 18px;\n  fill: ${options.textColor};\n}\n`, \"getStyles\");\nvar styles_default = getStyles;\n\nexport {\n  stateDiagram_default,\n  stateRenderer_v3_unified_default,\n  StateDB,\n  styles_default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,IAAI,SAAS,WAAW;AACtB,MAAI,IAAoB,OAAO,SAAS,GAAG,GAAG,IAAI,GAAG;AACnD,SAAK,KAAK,MAAM,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG,EAAE,CAAC,CAAC,IAAI,EAAG;AACrD,WAAO;AAAA,EACT,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AACttB,MAAI,UAAU;AAAA,IACZ,OAAuB,OAAO,SAAS,QAAQ;AAAA,IAC/C,GAAG,OAAO;AAAA,IACV,IAAI,CAAC;AAAA,IACL,UAAU,EAAE,SAAS,GAAG,SAAS,GAAG,SAAS,GAAG,MAAM,GAAG,MAAM,GAAG,YAAY,GAAG,QAAQ,GAAG,aAAa,GAAG,qBAAqB,IAAI,kBAAkB,IAAI,qBAAqB,IAAI,eAAe,IAAI,SAAS,IAAI,OAAO,IAAI,cAAc,IAAI,SAAS,IAAI,SAAS,IAAI,kBAAkB,IAAI,gBAAgB,IAAI,eAAe,IAAI,eAAe,IAAI,MAAM,IAAI,MAAM,IAAI,QAAQ,IAAI,QAAQ,IAAI,UAAU,IAAI,cAAc,IAAI,QAAQ,IAAI,gBAAgB,IAAI,aAAa,IAAI,aAAa,IAAI,aAAa,IAAI,mBAAmB,IAAI,aAAa,IAAI,mBAAmB,IAAI,6BAA6B,IAAI,YAAY,IAAI,eAAe,IAAI,sBAAsB,IAAI,WAAW,IAAI,SAAS,IAAI,aAAa,IAAI,sBAAsB,IAAI,SAAS,IAAI,mBAAmB,IAAI,cAAc,IAAI,gBAAgB,IAAI,gBAAgB,IAAI,gBAAgB,IAAI,gBAAgB,IAAI,OAAO,IAAI,KAAK,IAAI,cAAc,IAAI,mBAAmB,IAAI,WAAW,IAAI,YAAY,IAAI,WAAW,GAAG,QAAQ,EAAE;AAAA,IACx9B,YAAY,EAAE,GAAG,SAAS,GAAG,SAAS,GAAG,MAAM,GAAG,MAAM,IAAI,SAAS,IAAI,OAAO,IAAI,cAAc,IAAI,SAAS,IAAI,SAAS,IAAI,kBAAkB,IAAI,gBAAgB,IAAI,eAAe,IAAI,eAAe,IAAI,MAAM,IAAI,MAAM,IAAI,QAAQ,IAAI,QAAQ,IAAI,UAAU,IAAI,cAAc,IAAI,QAAQ,IAAI,aAAa,IAAI,aAAa,IAAI,mBAAmB,IAAI,aAAa,IAAI,mBAAmB,IAAI,6BAA6B,IAAI,YAAY,IAAI,eAAe,IAAI,sBAAsB,IAAI,WAAW,IAAI,SAAS,IAAI,aAAa,IAAI,sBAAsB,IAAI,SAAS,IAAI,mBAAmB,IAAI,cAAc,IAAI,gBAAgB,IAAI,gBAAgB,IAAI,gBAAgB,IAAI,gBAAgB,IAAI,KAAK,IAAI,cAAc,IAAI,mBAAmB,IAAI,WAAW,IAAI,WAAW;AAAA,IAChwB,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAAA,IACxZ,eAA+B,OAAO,SAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAS,IAAI,IAAI;AACtG,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACf,KAAK;AACH,aAAG,WAAW,GAAG,EAAE,CAAC;AACpB,iBAAO,GAAG,EAAE;AACZ;AAAA,QACF,KAAK;AACH,eAAK,IAAI,CAAC;AACV;AAAA,QACF,KAAK;AACH,cAAI,GAAG,EAAE,KAAK,MAAM;AAClB,eAAG,KAAK,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC;AACtB,iBAAK,IAAI,GAAG,KAAK,CAAC;AAAA,UACpB;AACA;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,eAAK,IAAI;AACT;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,gBAAM,YAAY,GAAG,KAAK,CAAC;AAC3B,oBAAU,cAAc,GAAG,UAAU,GAAG,EAAE,CAAC;AAC3C,eAAK,IAAI;AACT;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,YAAY,QAAQ,GAAG,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAE;AAChE;AAAA,QACF,KAAK;AACH,gBAAM,iBAAiB,GAAG,UAAU,GAAG,EAAE,CAAC;AAC1C,eAAK,IAAI,EAAE,MAAM,YAAY,QAAQ,GAAG,KAAK,CAAC,GAAG,QAAQ,GAAG,KAAK,CAAC,GAAG,aAAa,eAAe;AACjG;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,WAAW,aAAa,IAAI,KAAK,GAAG,KAAK,CAAC,EAAE;AAC5F;AAAA,QACF,KAAK;AACH,cAAI,KAAK,GAAG,EAAE;AACd,cAAI,cAAc,GAAG,KAAK,CAAC,EAAE,KAAK;AAClC,cAAI,GAAG,EAAE,EAAE,MAAM,GAAG,GAAG;AACrB,gBAAI,QAAQ,GAAG,EAAE,EAAE,MAAM,GAAG;AAC5B,iBAAK,MAAM,CAAC;AACZ,0BAAc,CAAC,aAAa,MAAM,CAAC,CAAC;AAAA,UACtC;AACA,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,MAAM,WAAW,YAAY;AAC3D;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,WAAW,aAAa,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,EAAE;AACpG;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,EAAE,GAAG,MAAM,OAAO;AACnD;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,EAAE,GAAG,MAAM,OAAO;AACnD;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,EAAE,GAAG,MAAM,SAAS;AACrD;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,aAAa,GAAG,MAAM,UAAU;AACjE;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,EAAE,KAAK,GAAG,MAAM,EAAE,UAAU,GAAG,KAAK,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,EAAE,EAAE,KAAK,EAAE,EAAE;AAC5G;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,EAAE,EAAE,KAAK;AACrB,aAAG,YAAY,KAAK,CAAC;AACrB;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,eAAK,IAAI,GAAG,EAAE,EAAE,KAAK;AACrB,aAAG,kBAAkB,KAAK,CAAC;AAC3B;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,YAAY,IAAI,GAAG,KAAK,CAAC,EAAE,KAAK,GAAG,SAAS,GAAG,EAAE,EAAE,KAAK,EAAE;AAC3E;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,EAAE,KAAK,GAAG,YAAY,GAAG,EAAE,EAAE,KAAK,EAAE;AAC3E;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,cAAc,IAAI,GAAG,KAAK,CAAC,EAAE,KAAK,GAAG,YAAY,GAAG,EAAE,EAAE,KAAK,EAAE;AAChF;AAAA,QACF,KAAK;AACH,aAAG,aAAa,IAAI;AACpB,eAAK,IAAI,EAAE,MAAM,OAAO,OAAO,KAAK;AACpC;AAAA,QACF,KAAK;AACH,aAAG,aAAa,IAAI;AACpB,eAAK,IAAI,EAAE,MAAM,OAAO,OAAO,KAAK;AACpC;AAAA,QACF,KAAK;AACH,aAAG,aAAa,IAAI;AACpB,eAAK,IAAI,EAAE,MAAM,OAAO,OAAO,KAAK;AACpC;AAAA,QACF,KAAK;AACH,aAAG,aAAa,IAAI;AACpB,eAAK,IAAI,EAAE,MAAM,OAAO,OAAO,KAAK;AACpC;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,EAAE,EAAE,KAAK,GAAG,MAAM,WAAW,aAAa,GAAG;AAC9E;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,EAAE,KAAK,GAAG,SAAS,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,GAAG,MAAM,WAAW,aAAa,GAAG;AAC5G;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,EAAE,KAAK,GAAG,SAAS,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,GAAG,MAAM,WAAW,aAAa,GAAG;AAC5G;AAAA,MACJ;AAAA,IACF,GAAG,WAAW;AAAA,IACd,OAAO,CAAC,EAAE,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,EAAE,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IACrhF,gBAAgB,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE;AAAA,IACjE,YAA4B,OAAO,SAAS,WAAW,KAAK,MAAM;AAChE,UAAI,KAAK,aAAa;AACpB,aAAK,MAAM,GAAG;AAAA,MAChB,OAAO;AACL,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACR;AAAA,IACF,GAAG,YAAY;AAAA,IACf,OAAuB,OAAO,SAAS,MAAM,OAAO;AAClD,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAI,SAAS,OAAO,OAAO,KAAK,KAAK;AACrC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAAS,KAAK,KAAK,IAAI;AACrB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AACpD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QAC/B;AAAA,MACF;AACA,aAAO,SAAS,OAAO,YAAY,EAAE;AACrC,kBAAY,GAAG,QAAQ;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAO,OAAO,UAAU,aAAa;AACvC,eAAO,SAAS,CAAC;AAAA,MACnB;AACA,UAAI,QAAQ,OAAO;AACnB,aAAO,KAAK,KAAK;AACjB,UAAI,SAAS,OAAO,WAAW,OAAO,QAAQ;AAC9C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACnD,aAAK,aAAa,YAAY,GAAG;AAAA,MACnC,OAAO;AACL,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAChD;AACA,eAAS,SAAS,GAAG;AACnB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MAClC;AACA,aAAO,UAAU,UAAU;AAC3B,eAAS,MAAM;AACb,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK;AACxC,YAAI,OAAO,UAAU,UAAU;AAC7B,cAAI,iBAAiB,OAAO;AAC1B,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACrB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QAClC;AACA,eAAO;AAAA,MACT;AACA,aAAO,KAAK,KAAK;AACjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACX,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC9B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACpC,OAAO;AACL,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACnD,qBAAS,IAAI;AAAA,UACf;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAC9C;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AACjE,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACtB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AACpC,uBAAS,KAAK,MAAM,KAAK,WAAW,CAAC,IAAI,GAAG;AAAA,YAC9C;AAAA,UACF;AACA,cAAI,OAAO,cAAc;AACvB,qBAAS,0BAA0B,WAAW,KAAK,QAAQ,OAAO,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAa,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAC9K,OAAO;AACL,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAO,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACrJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACtB,MAAM,OAAO;AAAA,YACb,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAM,OAAO;AAAA,YACb,KAAK;AAAA,YACL;AAAA,UACF,CAAC;AAAA,QACH;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACnD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACpG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACjB,KAAK;AACH,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAK,OAAO,MAAM;AACzB,mBAAO,KAAK,OAAO,MAAM;AACzB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACnB,uBAAS,OAAO;AAChB,uBAAS,OAAO;AAChB,yBAAW,OAAO;AAClB,sBAAQ,OAAO;AACf,kBAAI,aAAa,GAAG;AAClB;AAAA,cACF;AAAA,YACF,OAAO;AACL,uBAAS;AACT,+BAAiB;AAAA,YACnB;AACA;AAAA,UACF,KAAK;AACH,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACT,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YACzC;AACA,gBAAI,QAAQ;AACV,oBAAM,GAAG,QAAQ;AAAA,gBACf,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACnC;AAAA,YACF;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAClC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACF,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC5B,qBAAO;AAAA,YACT;AACA,gBAAI,KAAK;AACP,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACnC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACF,KAAK;AACH,mBAAO;AAAA,QACX;AAAA,MACF;AACA,aAAO;AAAA,IACT,GAAG,OAAO;AAAA,EACZ;AACA,MAAI,QAAwB,WAAW;AACrC,QAAI,SAAS;AAAA,MACX,KAAK;AAAA,MACL,YAA4B,OAAO,SAAS,WAAW,KAAK,MAAM;AAChE,YAAI,KAAK,GAAG,QAAQ;AAClB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACrC,OAAO;AACL,gBAAM,IAAI,MAAM,GAAG;AAAA,QACrB;AAAA,MACF,GAAG,YAAY;AAAA;AAAA,MAEf,UAA0B,OAAO,SAAS,OAAO,IAAI;AACnD,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACZ,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACf;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,GAAG,CAAC;AAAA,QAC3B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACT,GAAG,UAAU;AAAA;AAAA,MAEb,OAAuB,OAAO,WAAW;AACvC,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACT,eAAK;AACL,eAAK,OAAO;AAAA,QACd,OAAO;AACL,eAAK,OAAO;AAAA,QACd;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,MAAM,CAAC;AAAA,QACrB;AACA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACT,GAAG,OAAO;AAAA;AAAA,MAEV,OAAuB,OAAO,SAAS,IAAI;AACzC,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AACpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAC5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAC7D,YAAI,MAAM,SAAS,GAAG;AACpB,eAAK,YAAY,MAAM,SAAS;AAAA,QAClC;AACA,YAAI,IAAI,KAAK,OAAO;AACpB,aAAK,SAAS;AAAA,UACZ,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SAAS,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAAK,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS,KAAK,OAAO,eAAe;AAAA,QAC1L;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACrD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACT,GAAG,OAAO;AAAA;AAAA,MAEV,MAAsB,OAAO,WAAW;AACtC,aAAK,QAAQ;AACb,eAAO;AAAA,MACT,GAAG,MAAM;AAAA;AAAA,MAET,QAAwB,OAAO,WAAW;AACxC,YAAI,KAAK,QAAQ,iBAAiB;AAChC,eAAK,aAAa;AAAA,QACpB,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAChO,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACb,CAAC;AAAA,QACH;AACA,eAAO;AAAA,MACT,GAAG,QAAQ;AAAA;AAAA,MAEX,MAAsB,OAAO,SAAS,GAAG;AACvC,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAChC,GAAG,MAAM;AAAA;AAAA,MAET,WAA2B,OAAO,WAAW;AAC3C,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAQ,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAAG,WAAW;AAAA;AAAA,MAEd,eAA+B,OAAO,WAAW;AAC/C,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AACpB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAK,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAG,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MACjF,GAAG,eAAe;AAAA;AAAA,MAElB,cAA8B,OAAO,WAAW;AAC9C,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACjD,GAAG,cAAc;AAAA;AAAA,MAEjB,YAA4B,OAAO,SAAS,OAAO,cAAc;AAC/D,YAAI,OAAO,OAAO;AAClB,YAAI,KAAK,QAAQ,iBAAiB;AAChC,mBAAS;AAAA,YACP,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACN,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC3B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACb;AACA,cAAI,KAAK,QAAQ,QAAQ;AACvB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACjD;AAAA,QACF;AACA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACT,eAAK,YAAY,MAAM;AAAA,QACzB;AACA,aAAK,SAAS;AAAA,UACZ,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QAAQ,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAAS,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QAC/I;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAC9D;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC5B,eAAK,OAAO;AAAA,QACd;AACA,YAAI,OAAO;AACT,iBAAO;AAAA,QACT,WAAW,KAAK,YAAY;AAC1B,mBAAS,KAAK,QAAQ;AACpB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACpB;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,GAAG,YAAY;AAAA;AAAA,MAEf,MAAsB,OAAO,WAAW;AACtC,YAAI,KAAK,MAAM;AACb,iBAAO,KAAK;AAAA,QACd;AACA,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,OAAO;AAAA,QACd;AACA,YAAI,OAAO,OAAO,WAAW;AAC7B,YAAI,CAAC,KAAK,OAAO;AACf,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACf;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAClE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAChC,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACnB,uBAAO;AAAA,cACT,WAAW,KAAK,YAAY;AAC1B,wBAAQ;AACR;AAAA,cACF,OAAO;AACL,uBAAO;AAAA,cACT;AAAA,YACF,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC7B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,OAAO;AACT,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACnB,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,WAAW,IAAI;AACtB,iBAAO,KAAK;AAAA,QACd,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACtH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF,GAAG,MAAM;AAAA;AAAA,MAET,KAAqB,OAAO,SAAS,MAAM;AACzC,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACL,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,KAAK,IAAI;AAAA,QAClB;AAAA,MACF,GAAG,KAAK;AAAA;AAAA,MAER,OAAuB,OAAO,SAAS,MAAM,WAAW;AACtD,aAAK,eAAe,KAAK,SAAS;AAAA,MACpC,GAAG,OAAO;AAAA;AAAA,MAEV,UAA0B,OAAO,SAAS,WAAW;AACnD,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACT,iBAAO,KAAK,eAAe,IAAI;AAAA,QACjC,OAAO;AACL,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B;AAAA,MACF,GAAG,UAAU;AAAA;AAAA,MAEb,eAA+B,OAAO,SAAS,gBAAgB;AAC7D,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACrF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAC9E,OAAO;AACL,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACpC;AAAA,MACF,GAAG,eAAe;AAAA;AAAA,MAElB,UAA0B,OAAO,SAAS,SAAS,GAAG;AACpD,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACV,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,GAAG,UAAU;AAAA;AAAA,MAEb,WAA2B,OAAO,SAAS,UAAU,WAAW;AAC9D,aAAK,MAAM,SAAS;AAAA,MACtB,GAAG,WAAW;AAAA;AAAA,MAEd,gBAAgC,OAAO,SAAS,iBAAiB;AAC/D,eAAO,KAAK,eAAe;AAAA,MAC7B,GAAG,gBAAgB;AAAA,MACnB,SAAS,EAAE,oBAAoB,KAAK;AAAA,MACpC,eAA+B,OAAO,SAAS,UAAU,IAAI,KAAK,2BAA2B,UAAU;AACrG,YAAI,UAAU;AACd,gBAAQ,2BAA2B;AAAA,UACjC,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH;AAAA,YACA;AACA;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,OAAO;AACtB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,WAAW;AACtB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,WAAW;AACtB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,qBAAqB;AAChC;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,UAAU;AACzB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,iBAAK,UAAU,YAAY;AAC3B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,iBAAK,UAAU,YAAY;AAC3B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,OAAO;AACtB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,iBAAK,UAAU,aAAa;AAC5B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,OAAO;AACtB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,iBAAK,UAAU,iBAAiB;AAChC,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,OAAO;AACtB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,OAAO;AACtB;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,gBAAI,SAAS,IAAI,OAAO,MAAM,GAAG,EAAE,EAAE,KAAK;AAC1C,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,gBAAI,SAAS,IAAI,OAAO,MAAM,GAAG,EAAE,EAAE,KAAK;AAC1C,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,gBAAI,SAAS,IAAI,OAAO,MAAM,GAAG,GAAG,EAAE,KAAK;AAC3C,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,gBAAI,SAAS,IAAI,OAAO,MAAM,GAAG,EAAE,EAAE,KAAK;AAC1C,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,gBAAI,SAAS,IAAI,OAAO,MAAM,GAAG,EAAE,EAAE,KAAK;AAC1C,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,gBAAI,SAAS,IAAI,OAAO,MAAM,GAAG,GAAG,EAAE,KAAK;AAC3C,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,cAAc;AAC7B;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,UAAU;AACzB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,iBAAK,UAAU,QAAQ;AACvB,mBAAO;AACP;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,MAAM;AACjB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,iBAAK,UAAU,SAAS;AACxB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,iBAAK,UAAU,SAAS;AACxB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,iBAAK,UAAU,eAAe;AAC9B;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,iBAAK,UAAU,kBAAkB;AACjC,mBAAO;AACP;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,iBAAK,UAAU,WAAW;AAC1B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,gBAAI,SAAS,IAAI,OAAO,OAAO,CAAC,EAAE,KAAK;AACvC,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,gBAAI,SAAS,IAAI,OAAO,MAAM,GAAG,EAAE,EAAE,KAAK;AAC1C,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,gBAAI,SAAS,IAAI,OAAO,KAAK;AAC7B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,QACJ;AAAA,MACF,GAAG,WAAW;AAAA,MACd,OAAO,CAAC,mBAAmB,gCAAgC,gCAAgC,gCAAgC,gCAAgC,wBAAwB,uBAAuB,eAAe,eAAe,qBAAqB,iBAAiB,iBAAiB,kBAAkB,aAAa,oBAAoB,yBAAyB,yBAAyB,yBAAyB,yBAAyB,0BAA0B,cAAc,gBAAgB,qBAAqB,oBAAoB,gBAAgB,gBAAgB,kBAAkB,4BAA4B,gBAAgB,kBAAkB,mBAAmB,gBAAgB,kBAAkB,aAAa,oBAAoB,kBAAkB,oBAAoB,oBAAoB,sBAAsB,wBAAwB,wBAAwB,0BAA0B,gCAAgC,gCAAgC,gCAAgC,gCAAgC,aAAa,kBAAkB,kBAAkB,aAAa,eAAe,oBAAoB,YAAY,YAAY,wBAAwB,YAAY,cAAc,iBAAiB,mBAAmB,oBAAoB,WAAW,kBAAkB,aAAa,eAAe,gBAAgB,wBAAwB,sBAAsB,4BAA4B,yBAAyB,4BAA4B,kCAAkC,gBAAgB,uBAAuB,sBAAsB,aAAa,YAAY,aAAa,WAAW,SAAS;AAAA,MAC3jD,YAAY,EAAE,QAAQ,EAAE,SAAS,CAAC,GAAG,EAAE,GAAG,aAAa,MAAM,GAAG,UAAU,EAAE,SAAS,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,oBAAoB,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAM,GAAG,iBAAiB,EAAE,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,aAAa,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,WAAW,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAM,GAAG,QAAQ,EAAE,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,sBAAsB,EAAE,SAAS,CAAC,GAAG,aAAa,MAAM,GAAG,mBAAmB,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAM,GAAG,aAAa,EAAE,SAAS,CAAC,GAAG,aAAa,MAAM,GAAG,SAAS,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAM,GAAG,eAAe,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAM,GAAG,SAAS,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAM,GAAG,cAAc,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAM,GAAG,YAAY,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,uBAAuB,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,aAAa,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAM,GAAG,aAAa,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAM,GAAG,SAAS,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,SAAS,EAAE,SAAS,CAAC,GAAG,aAAa,MAAM,GAAG,YAAY,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAM,GAAG,gBAAgB,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,cAAc,EAAE,SAAS,CAAC,GAAG,aAAa,MAAM,GAAG,SAAS,EAAE,SAAS,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,MAAM,EAAE,SAAS,CAAC,GAAG,EAAE,GAAG,aAAa,MAAM,GAAG,WAAW,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,KAAK,EAAE;AAAA,IACtmD;AACA,WAAO;AAAA,EACT,EAAE;AACF,UAAQ,QAAQ;AAChB,WAAS,SAAS;AAChB,SAAK,KAAK,CAAC;AAAA,EACb;AACA,SAAO,QAAQ,QAAQ;AACvB,SAAO,YAAY;AACnB,UAAQ,SAAS;AACjB,SAAO,IAAI,OAAO;AACpB,EAAE;AACF,OAAO,SAAS;AAChB,IAAI,uBAAuB;AAG3B,IAAI,4BAA4B;AAChC,IAAI,yBAAyB;AAC7B,IAAI,iBAAiB;AACrB,IAAI,aAAa;AACjB,IAAI,gBAAgB;AACpB,IAAI,gBAAgB;AACpB,IAAI,gBAAgB;AACpB,IAAI,kBAAkB;AACtB,IAAI,qBAAqB;AACzB,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,wBAAwB;AAC5B,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,mBAAmB;AACvB,IAAI,cAAc;AAClB,IAAI,wBAAwB;AAC5B,IAAI,cAAc;AAClB,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,IAAI,kBAAkB;AACtB,IAAI,cAAc;AAClB,IAAI,YAAY;AAChB,IAAI,oBAAoB,GAAG,WAAW,IAAI,SAAS;AACnD,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,gBAAgB;AACpB,IAAI,qBAAqB,GAAG,QAAQ,IAAI,aAAa;AACrD,IAAI,mBAAmB,GAAG,WAAW,IAAI,QAAQ;AACjD,IAAI,cAAc;AAClB,IAAI,sBAAsB,GAAG,WAAW,IAAI,WAAW;AACvD,IAAI,kBAAkB;AACtB,IAAI,0BAA0B,GAAG,WAAW,IAAI,eAAe;AAC/D,IAAI,SAAS;AACb,IAAI,OAAO;AACX,IAAI,cAAc;AAClB,IAAI,oBAAoB;AACxB,IAAI,UAAU,GAAG,iBAAiB,GAAG,IAAI;AACzC,IAAI,YAAY,GAAG,iBAAiB,GAAG,MAAM;AAG7C,IAAI,SAAyB,OAAO,CAAC,YAAY,aAAa,2BAA2B;AACvF,MAAI,CAAC,WAAW,KAAK;AACnB,WAAO;AAAA,EACT;AACA,MAAI,MAAM;AACV,aAAW,iBAAiB,WAAW,KAAK;AAC1C,QAAI,cAAc,SAAS,OAAO;AAChC,YAAM,cAAc;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AACT,GAAG,QAAQ;AACX,IAAI,aAA6B,OAAO,SAAS,MAAM,YAAY;AACjE,SAAO,WAAW,GAAG,WAAW;AAClC,GAAG,YAAY;AACf,IAAI,OAAuB,OAAO,eAAe,MAAM,IAAI,UAAU,MAAM;AACzE,MAAI,KAAK,OAAO;AAChB,MAAI,KAAK,8BAA8B,EAAE;AACzC,QAAM,EAAE,eAAe,OAAO,MAAM,OAAO,IAAI,WAAU;AACzD,OAAK,GAAG,QAAQ,KAAK,GAAG,aAAa,CAAC;AACtC,QAAM,cAAc,KAAK,GAAG,QAAQ;AACpC,QAAM,MAAM,kBAAkB,IAAI,aAAa;AAC/C,cAAY,OAAO,KAAK;AACxB,cAAY,kBAAkB;AAC9B,cAAY,eAAc,6BAAM,gBAAe;AAC/C,cAAY,eAAc,6BAAM,gBAAe;AAC/C,cAAY,UAAU,CAAC,MAAM;AAC7B,cAAY,YAAY;AACxB,QAAM,OAAO,aAAa,GAAG;AAC7B,QAAM,UAAU;AAChB,gBAAc;AAAA,IACZ;AAAA,IACA;AAAA,KACA,6BAAM,mBAAkB;AAAA,IACxB,KAAK,GAAG,gBAAgB;AAAA,EAC1B;AACA,sBAAoB,KAAK,SAAS,cAAa,6BAAM,gBAAe,IAAI;AAC1E,GAAG,MAAM;AACT,IAAI,mCAAmC;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AACF;AAGA,IAAI,SAAyB,oBAAI,IAAI;AACrC,IAAI,iBAAiB;AACrB,SAAS,WAAW,SAAS,IAAI,UAAU,GAAG,OAAO,IAAI,aAAa,mBAAmB;AACvF,QAAM,UAAU,SAAS,QAAQ,KAAK,SAAS,IAAI,GAAG,UAAU,GAAG,IAAI,KAAK;AAC5E,SAAO,GAAG,WAAW,IAAI,MAAM,GAAG,OAAO,IAAI,OAAO;AACtD;AACA,OAAO,YAAY,YAAY;AAC/B,IAAI,WAA2B,OAAO,CAAC,kBAAkB,KAAK,eAAe,OAAO,OAAO,SAAS,MAAM,YAAY;AACpH,MAAI,MAAM,SAAS,GAAG;AACtB,MAAI,QAAQ,CAAC,SAAS;AACpB,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK;AACH,oBAAY,kBAAkB,MAAM,eAAe,OAAO,OAAO,SAAS,MAAM,OAAO;AACvF;AAAA,MACF,KAAK;AACH,oBAAY,kBAAkB,MAAM,eAAe,OAAO,OAAO,SAAS,MAAM,OAAO;AACvF;AAAA,MACF,KAAK;AACH;AACE;AAAA,YACE;AAAA,YACA,KAAK;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA;AAAA,YACE;AAAA,YACA,KAAK;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,gBAAM,WAAW;AAAA,YACf,IAAI,SAAS;AAAA,YACb,OAAO,KAAK,OAAO;AAAA,YACnB,KAAK,KAAK,OAAO;AAAA,YACjB,WAAW;AAAA,YACX,cAAc;AAAA,YACd,OAAO;AAAA,YACP,YAAY;AAAA,YACZ,OAAO,eAAe,aAAa,KAAK,aAAa,WAAU,CAAC;AAAA,YAChE,gBAAgB;AAAA,YAChB,UAAU;AAAA,YACV,WAAW;AAAA,YACX,WAAW;AAAA,YACX,SAAS;AAAA,YACT;AAAA,UACF;AACA,gBAAM,KAAK,QAAQ;AACnB;AAAA,QACF;AACA;AAAA,IACJ;AAAA,EACF,CAAC;AACH,GAAG,UAAU;AACb,IAAI,UAA0B,OAAO,CAAC,YAAY,aAAa,2BAA2B;AACxF,MAAI,MAAM;AACV,MAAI,WAAW,KAAK;AAClB,eAAW,iBAAiB,WAAW,KAAK;AAC1C,UAAI,cAAc,SAAS,OAAO;AAChC,cAAM,cAAc;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT,GAAG,QAAQ;AACX,SAAS,mBAAmB,OAAO,UAAU,SAAS;AACpD,MAAI,CAAC,SAAS,MAAM,SAAS,OAAO,oBAAoB,SAAS,OAAO,aAAa;AACnF;AAAA,EACF;AACA,MAAI,SAAS,YAAY;AACvB,QAAI,CAAC,MAAM,QAAQ,SAAS,iBAAiB,GAAG;AAC9C,eAAS,oBAAoB,CAAC;AAAA,IAChC;AACA,aAAS,WAAW,MAAM,GAAG,EAAE,QAAQ,CAAC,aAAa;AACnD,UAAI,QAAQ,IAAI,QAAQ,GAAG;AACzB,cAAM,WAAW,QAAQ,IAAI,QAAQ;AACrC,iBAAS,oBAAoB,CAAC,GAAG,SAAS,mBAAmB,GAAG,SAAS,MAAM;AAAA,MACjF;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,mBAAmB,MAAM,KAAK,CAAC,SAAS,KAAK,OAAO,SAAS,EAAE;AACrE,MAAI,kBAAkB;AACpB,WAAO,OAAO,kBAAkB,QAAQ;AAAA,EAC1C,OAAO;AACL,UAAM,KAAK,QAAQ;AAAA,EACrB;AACF;AACA,OAAO,oBAAoB,oBAAoB;AAC/C,SAAS,qBAAqB,YAAY;AAtjC1C,MAAAA;AAujCE,WAAOA,MAAA,yCAAY,YAAZ,gBAAAA,IAAqB,KAAK,SAAQ;AAC3C;AACA,OAAO,sBAAsB,sBAAsB;AACnD,SAAS,oBAAoB,YAAY;AACvC,UAAO,yCAAY,WAAU,CAAC;AAChC;AACA,OAAO,qBAAqB,qBAAqB;AACjD,IAAI,cAA8B,OAAO,CAAC,QAAQ,YAAY,eAAe,OAAO,OAAO,SAAS,MAAM,YAAY;AA9jCtH,MAAAA,KAAA;AA+jCE,QAAM,SAAS,WAAW;AAC1B,QAAM,UAAU,cAAc,IAAI,MAAM;AACxC,QAAM,WAAW,qBAAqB,OAAO;AAC7C,QAAM,QAAQ,oBAAoB,OAAO;AACzC,MAAI,KAAK,0BAA0B,YAAY,SAAS,KAAK;AAC7D,MAAI,WAAW,QAAQ;AACrB,QAAI,QAAQ;AACZ,QAAI,WAAW,UAAU,MAAM;AAC7B,cAAQ;AAAA,IACV,WAAW,WAAW,UAAU,OAAO;AACrC,cAAQ;AAAA,IACV;AACA,QAAI,WAAW,SAAS,oBAAoB;AAC1C,cAAQ,WAAW;AAAA,IACrB;AACA,QAAI,CAAC,OAAO,IAAI,MAAM,GAAG;AACvB,aAAO,IAAI,QAAQ;AAAA,QACjB,IAAI;AAAA,QACJ;AAAA,QACA,aAAa,eAAe,aAAa,QAAQ,WAAU,CAAC;AAAA,QAC5D,YAAY,GAAG,QAAQ,IAAI,iBAAiB;AAAA,QAC5C,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AACA,UAAM,UAAU,OAAO,IAAI,MAAM;AACjC,QAAI,WAAW,aAAa;AAC1B,UAAI,MAAM,QAAQ,QAAQ,WAAW,GAAG;AACtC,gBAAQ,QAAQ;AAChB,gBAAQ,YAAY,KAAK,WAAW,WAAW;AAAA,MACjD,OAAO;AACL,cAAIA,MAAA,QAAQ,gBAAR,gBAAAA,IAAqB,UAAS,GAAG;AACnC,kBAAQ,QAAQ;AAChB,cAAI,QAAQ,gBAAgB,QAAQ;AAClC,oBAAQ,cAAc,CAAC,WAAW,WAAW;AAAA,UAC/C,OAAO;AACL,oBAAQ,cAAc,CAAC,QAAQ,aAAa,WAAW,WAAW;AAAA,UACpE;AAAA,QACF,OAAO;AACL,kBAAQ,QAAQ;AAChB,kBAAQ,cAAc,WAAW;AAAA,QACnC;AAAA,MACF;AACA,cAAQ,cAAc,eAAe,oBAAoB,QAAQ,aAAa,WAAU,CAAC;AAAA,IAC3F;AACA,UAAI,aAAQ,gBAAR,mBAAqB,YAAW,KAAK,QAAQ,UAAU,uBAAuB;AAChF,UAAI,QAAQ,SAAS,SAAS;AAC5B,gBAAQ,QAAQ;AAAA,MAClB,OAAO;AACL,gBAAQ,QAAQ;AAAA,MAClB;AAAA,IACF;AACA,QAAI,CAAC,QAAQ,QAAQ,WAAW,KAAK;AACnC,UAAI,KAAK,2BAA2B,QAAQ,QAAQ,UAAU,CAAC;AAC/D,cAAQ,OAAO;AACf,cAAQ,UAAU;AAClB,cAAQ,MAAM,QAAQ,UAAU;AAChC,cAAQ,QAAQ,WAAW,SAAS,eAAe,gBAAgB;AACnE,cAAQ,aAAa,GAAG,QAAQ,UAAU,IAAI,mBAAmB,IAAI,UAAU,0BAA0B,EAAE;AAAA,IAC7G;AACA,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,OAAO,QAAQ;AAAA,MACf,OAAO,QAAQ;AAAA,MACf,YAAY,QAAQ;AAAA,MACpB,mBAAmB,CAAC;AAAA,MACpB,WAAW,QAAQ;AAAA,MACnB,IAAI;AAAA,MACJ,KAAK,QAAQ;AAAA,MACb,OAAO,WAAW,QAAQ,cAAc;AAAA,MACxC,MAAM,QAAQ;AAAA,MACd,SAAS,QAAQ,SAAS;AAAA,MAC1B,SAAS;AAAA,MACT,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ;AAAA,IACF;AACA,QAAI,SAAS,UAAU,eAAe;AACpC,eAAS,QAAQ;AAAA,IACnB;AACA,QAAI,UAAU,OAAO,OAAO,QAAQ;AAClC,UAAI,MAAM,iBAAiB,QAAQ,+BAA+B,OAAO,EAAE;AAC3E,eAAS,WAAW,OAAO;AAAA,IAC7B;AACA,aAAS,cAAc;AACvB,QAAI,WAAW,MAAM;AACnB,YAAM,WAAW;AAAA,QACf,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,OAAO,WAAW,KAAK;AAAA,QACvB,YAAY;AAAA;AAAA,QAEZ,WAAW,CAAC;AAAA,QACZ,mBAAmB,CAAC;AAAA,QACpB,IAAI,SAAS,UAAU,MAAM;AAAA,QAC7B,OAAO,WAAW,QAAQ,gBAAgB,IAAI;AAAA,QAC9C,MAAM,QAAQ;AAAA,QACd,SAAS,QAAQ,SAAS;AAAA,QAC1B,SAAS,WAAU,EAAE,UAAU;AAAA,QAC/B;AAAA,QACA,UAAU,WAAW,KAAK;AAAA,MAC5B;AACA,YAAM,eAAe,SAAS;AAC9B,YAAM,YAAY;AAAA,QAChB,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,OAAO,WAAW,KAAK;AAAA,QACvB,YAAY,QAAQ;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,IAAI,SAAS;AAAA,QACb,OAAO,WAAW,QAAQ,gBAAgB,MAAM;AAAA,QAChD,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA;AAAA,QAET;AAAA,QACA,UAAU,WAAW,KAAK;AAAA,MAC5B;AACA;AACA,gBAAU,KAAK;AACf,eAAS,WAAW;AACpB,yBAAmB,OAAO,WAAW,OAAO;AAC5C,yBAAmB,OAAO,UAAU,OAAO;AAC3C,yBAAmB,OAAO,UAAU,OAAO;AAC3C,UAAI,OAAO;AACX,UAAI,KAAK,SAAS;AAClB,UAAI,WAAW,KAAK,aAAa,WAAW;AAC1C,eAAO,SAAS;AAChB,aAAK;AAAA,MACP;AACA,YAAM,KAAK;AAAA,QACT,IAAI,OAAO,MAAM;AAAA,QACjB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,QACX,cAAc;AAAA,QACd,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,yBAAmB,OAAO,UAAU,OAAO;AAAA,IAC7C;AAAA,EACF;AACA,MAAI,WAAW,KAAK;AAClB,QAAI,MAAM,wBAAwB;AAClC,aAAS,YAAY,WAAW,KAAK,eAAe,OAAO,OAAO,CAAC,SAAS,MAAM,OAAO;AAAA,EAC3F;AACF,GAAG,aAAa;AAChB,IAAI,QAAwB,OAAO,MAAM;AACvC,SAAO,MAAM;AACb,mBAAiB;AACnB,GAAG,OAAO;AAGV,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,IAAI,UAAU;AACd,IAAI,iBAAiB;AACrB,SAAS,iBAAiB;AACxB,SAAuB,oBAAI,IAAI;AACjC;AACA,OAAO,gBAAgB,gBAAgB;AACvC,IAAI,SAAyB,OAAO,MAAM;AACxC,SAAO;AAAA;AAAA,IAEL,WAAW,CAAC;AAAA,IACZ,QAAwB,oBAAI,IAAI;AAAA,IAChC,WAAW,CAAC;AAAA,EACd;AACF,GAAG,QAAQ;AACX,IAAI,QAAwB,OAAO,CAAC,MAAM,KAAK,MAAM,KAAK,UAAU,CAAC,CAAC,GAAG,OAAO;AAlvChF;AAmvCA,IAAI,WAAU,WAAM;AAAA;AAAA;AAAA;AAAA,EAOlB,YAAY,SAAS;AAYrB;AAAA;AAAA;AAAA;AAAA;AAKA;AAAA;AAAA;AAAA;AAAA,iCAAQ,CAAC;AAKT;AAAA;AAAA;AAAA;AAAA,iCAAQ,CAAC;AAKT;AAAA;AAAA;AAAA;AAAA,mCAAU,CAAC;AAKX;AAAA;AAAA;AAAA;AAAA,mCAAU,eAAe;AAMzB;AAAA;AAAA;AAAA;AAAA;AAAA,qCAAY;AAAA,MACV,MAAM,OAAO;AAAA,IACf;AAKA;AAAA;AAAA;AAAA;AAAA,2CAAkB,KAAK,UAAU;AAKjC;AAAA;AAAA;AAAA;AAAA,yCAAgB;AAKhB;AAAA;AAAA;AAAA;AAAA,sCAAa;AAkfb,uCAAc;AACd,uCAAc;AACd,6CAAoB;AACpB,6CAAoB;AACpB,2CAAkB;AAClB,2CAAkB;AA7iBhB,SAAK,MAAM;AACX,SAAK,UAAU;AACf,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AAAA,EAC3C;AAAA,EAuDA,WAAW,GAAG;AACZ,QAAI,KAAK,oBAAoB,CAAC;AAC9B,SAAK,UAAU;AACf,QAAI,KAAK,YAAY,GAAG;AACtB,WAAK,QAAQ,CAAC;AAAA,IAChB,OAAO;AACL,WAAK,QAAQ,KAAK,aAAa,CAAC;AAAA,IAClC;AAAA,EACF;AAAA,EACA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,QAAQ,MAAM,OAAO;AACjC,QAAI,KAAK,SAAS,eAAe;AAC/B,WAAK,cAAc,QAAQ,KAAK,QAAQ,IAAI;AAC5C,WAAK,cAAc,QAAQ,KAAK,QAAQ,KAAK;AAAA,IAC/C,OAAO;AACL,UAAI,KAAK,SAAS,YAAY;AAC5B,YAAI,KAAK,OAAO,OAAO;AACrB,eAAK,KAAK,QAAQ,OAAO,KAAK,WAAW,OAAO,KAAK;AACrD,eAAK,QAAQ;AAAA,QACf,OAAO;AACL,eAAK,KAAK,KAAK,GAAG,KAAK;AAAA,QACzB;AAAA,MACF;AACA,UAAI,KAAK,KAAK;AACZ,cAAM,MAAM,CAAC;AACb,YAAI,aAAa,CAAC;AAClB,YAAI;AACJ,aAAK,IAAI,GAAG,IAAI,KAAK,IAAI,QAAQ,KAAK;AACpC,cAAI,KAAK,IAAI,CAAC,EAAE,SAAS,cAAc;AACrC,kBAAM,UAAU,MAAM,KAAK,IAAI,CAAC,CAAC;AACjC,oBAAQ,MAAM,MAAM,UAAU;AAC9B,gBAAI,KAAK,OAAO;AAChB,yBAAa,CAAC;AAAA,UAChB,OAAO;AACL,uBAAW,KAAK,KAAK,IAAI,CAAC,CAAC;AAAA,UAC7B;AAAA,QACF;AACA,YAAI,IAAI,SAAS,KAAK,WAAW,SAAS,GAAG;AAC3C,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,YACN,IAAI,WAAW;AAAA,YACf,MAAM;AAAA,YACN,KAAK,MAAM,UAAU;AAAA,UACvB;AACA,cAAI,KAAK,MAAM,OAAO,CAAC;AACvB,eAAK,MAAM;AAAA,QACb;AACA,aAAK,IAAI,QAAQ,CAAC,YAAY,KAAK,cAAc,MAAM,SAAS,IAAI,CAAC;AAAA,MACvE;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,SAAK,cAAc,EAAE,IAAI,OAAO,GAAG,EAAE,IAAI,QAAQ,KAAK,KAAK,QAAQ,GAAG,IAAI;AAC1E,WAAO,EAAE,IAAI,QAAQ,KAAK,KAAK,QAAQ;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,QAAQ,MAAM;AACZ,QAAI;AACJ,QAAI,KAAK,KAAK;AACZ,YAAM,KAAK;AAAA,IACb,OAAO;AACL,YAAM;AAAA,IACR;AACA,QAAI,KAAK,GAAG;AACZ,SAAK,MAAM,IAAI;AACf,QAAI,KAAK,6BAA6B,GAAG;AACzC,QAAI,QAAQ,CAAC,SAAS;AACpB,UAAI,KAAK,aAAa,KAAK,IAAI;AAC/B,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AACH,eAAK;AAAA,YACH,KAAK,GAAG,KAAK;AAAA,YACb,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,UACP;AACA;AAAA,QACF,KAAK;AACH,eAAK,YAAY,KAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW;AAC3D;AAAA,QACF,KAAK;AACH,eAAK,cAAc,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO;AAC/C;AAAA,QACF,KAAK;AACH;AACE,kBAAM,MAAM,KAAK,GAAG,KAAK,EAAE,MAAM,GAAG;AACpC,kBAAM,SAAS,KAAK,WAAW,MAAM,GAAG;AACxC,gBAAI,QAAQ,CAAC,OAAO;AAClB,kBAAI,aAAa,KAAK,SAAS,EAAE;AACjC,kBAAI,eAAe,QAAQ;AACzB,sBAAM,YAAY,GAAG,KAAK;AAC1B,qBAAK,SAAS,SAAS;AACvB,6BAAa,KAAK,SAAS,SAAS;AAAA,cACtC;AACA,yBAAW,SAAS,OAAO,IAAI,CAAC,MAAG;AA/6CjD,oBAAAA;AA+6CoD,wBAAAA,MAAA,EAAE,QAAQ,MAAM,EAAE,MAAlB,gBAAAA,IAAqB;AAAA,eAAM;AAAA,YACnE,CAAC;AAAA,UACH;AACA;AAAA,QACF,KAAK;AACH,eAAK,YAAY,KAAK,GAAG,KAAK,GAAG,KAAK,UAAU;AAChD;AAAA,MACJ;AAAA,IACF,CAAC;AACD,UAAM,gBAAgB,KAAK,UAAU;AACrC,UAAM,SAAS,WAAU;AACzB,UAAM,OAAO,OAAO;AACpB,UAAM;AACN;AAAA,MACE;AAAA,MACA,KAAK,aAAa;AAAA,MAClB;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACP;AACA,SAAK,MAAM,QAAQ,CAAC,SAAS;AAC3B,UAAI,MAAM,QAAQ,KAAK,KAAK,GAAG;AAC7B,aAAK,cAAc,KAAK,MAAM,MAAM,CAAC;AACrC,YAAI,KAAK,WAAW,KAAK,YAAY,SAAS,GAAG;AAC/C,gBAAM,IAAI;AAAA,YACR,kFAAkF,KAAK,KAAK;AAAA,UAC9F;AAAA,QACF;AACA,aAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,SAAS,IAAI,OAAO,oBAAoB,MAAM,MAAM,QAAQ,MAAM,OAAO,MAAM,UAAU,MAAM,SAAS,MAAM,aAAa,MAAM;AAC/H,UAAM,YAAY,yBAAI;AACtB,QAAI,CAAC,KAAK,gBAAgB,OAAO,IAAI,SAAS,GAAG;AAC/C,UAAI,KAAK,iBAAiB,WAAW,KAAK;AAC1C,WAAK,gBAAgB,OAAO,IAAI,WAAW;AAAA,QACzC,IAAI;AAAA,QACJ,cAAc,CAAC;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS,CAAC;AAAA,QACV,QAAQ,CAAC;AAAA,QACT,YAAY,CAAC;AAAA,MACf,CAAC;AAAA,IACH,OAAO;AACL,UAAI,CAAC,KAAK,gBAAgB,OAAO,IAAI,SAAS,EAAE,KAAK;AACnD,aAAK,gBAAgB,OAAO,IAAI,SAAS,EAAE,MAAM;AAAA,MACnD;AACA,UAAI,CAAC,KAAK,gBAAgB,OAAO,IAAI,SAAS,EAAE,MAAM;AACpD,aAAK,gBAAgB,OAAO,IAAI,SAAS,EAAE,OAAO;AAAA,MACpD;AAAA,IACF;AACA,QAAI,OAAO;AACT,UAAI,KAAK,6BAA6B,WAAW,KAAK;AACtD,UAAI,OAAO,UAAU,UAAU;AAC7B,aAAK,eAAe,WAAW,MAAM,KAAK,CAAC;AAAA,MAC7C;AACA,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,QAAQ,CAAC,QAAQ,KAAK,eAAe,WAAW,IAAI,KAAK,CAAC,CAAC;AAAA,MACnE;AAAA,IACF;AACA,QAAI,MAAM;AACR,YAAM,OAAO,KAAK,gBAAgB,OAAO,IAAI,SAAS;AACtD,WAAK,OAAO;AACZ,WAAK,KAAK,OAAO,eAAe,aAAa,KAAK,KAAK,MAAM,WAAU,CAAC;AAAA,IAC1E;AACA,QAAI,SAAS;AACX,UAAI,KAAK,yBAAyB,WAAW,OAAO;AACpD,YAAM,cAAc,OAAO,YAAY,WAAW,CAAC,OAAO,IAAI;AAC9D,kBAAY,QAAQ,CAAC,aAAa,KAAK,YAAY,WAAW,SAAS,KAAK,CAAC,CAAC;AAAA,IAChF;AACA,QAAI,QAAQ;AACV,UAAI,KAAK,wBAAwB,WAAW,MAAM;AAClD,YAAM,aAAa,OAAO,WAAW,WAAW,CAAC,MAAM,IAAI;AAC3D,iBAAW,QAAQ,CAAC,UAAU,KAAK,SAAS,WAAW,MAAM,KAAK,CAAC,CAAC;AAAA,IACtE;AACA,QAAI,YAAY;AACd,UAAI,KAAK,wBAAwB,WAAW,MAAM;AAClD,YAAM,iBAAiB,OAAO,eAAe,WAAW,CAAC,UAAU,IAAI;AACvE,qBAAe,QAAQ,CAAC,cAAc,KAAK,aAAa,WAAW,UAAU,KAAK,CAAC,CAAC;AAAA,IACtF;AAAA,EACF;AAAA,EACA,MAAM,YAAY;AAChB,SAAK,QAAQ,CAAC;AACd,SAAK,QAAQ,CAAC;AACd,SAAK,YAAY;AAAA,MACf,MAAM,OAAO;AAAA,IACf;AACA,SAAK,kBAAkB,KAAK,UAAU;AACtC,SAAK,gBAAgB;AACrB,SAAK,UAAU,eAAe;AAC9B,QAAI,CAAC,YAAY;AACf,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,SAAS,IAAI;AACX,WAAO,KAAK,gBAAgB,OAAO,IAAI,EAAE;AAAA,EAC3C;AAAA,EACA,YAAY;AACV,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA,EACA,eAAe;AACb,QAAI,KAAK,gBAAgB,KAAK,SAAS;AAAA,EACzC;AAAA,EACA,eAAe;AACb,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,gBAAgB,KAAK,IAAI;AACvB,QAAI,UAAU;AACd,QAAI,OAAO,YAAY;AACrB,WAAK;AACL,gBAAU,GAAG,UAAU,GAAG,KAAK,aAAa;AAAA,IAC9C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,kBAAkB,KAAK,IAAI,OAAO,oBAAoB;AACpD,WAAO,OAAO,aAAa,aAAa;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,cAAc,KAAK,IAAI;AACrB,QAAI,UAAU;AACd,QAAI,OAAO,UAAU;AACnB,WAAK;AACL,gBAAU,GAAG,QAAQ,GAAG,KAAK,aAAa;AAAA,IAC5C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,gBAAgB,KAAK,IAAI,OAAO,oBAAoB;AAClD,WAAO,OAAO,WAAW,WAAW;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,OAAO,OAAO,eAAe;AAC3C,QAAI,MAAM,KAAK,gBAAgB,MAAM,GAAG,KAAK,CAAC;AAC9C,QAAI,QAAQ,KAAK,kBAAkB,MAAM,GAAG,KAAK,GAAG,MAAM,IAAI;AAC9D,QAAI,MAAM,KAAK,gBAAgB,MAAM,GAAG,KAAK,CAAC;AAC9C,QAAI,QAAQ,KAAK,kBAAkB,MAAM,GAAG,KAAK,GAAG,MAAM,IAAI;AAC9D,SAAK;AAAA,MACH;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AACA,SAAK;AAAA,MACH;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AACA,SAAK,gBAAgB,UAAU,KAAK;AAAA,MAClC;AAAA,MACA;AAAA,MACA,eAAe,eAAe,aAAa,eAAe,WAAU,CAAC;AAAA,IACvE,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,OAAO,OAAO,OAAO;AAC/B,QAAI,OAAO,UAAU,UAAU;AAC7B,WAAK,gBAAgB,OAAO,OAAO,KAAK;AAAA,IAC1C,OAAO;AACL,YAAM,MAAM,KAAK,gBAAgB,MAAM,KAAK,CAAC;AAC7C,YAAM,QAAQ,KAAK,kBAAkB,KAAK;AAC1C,YAAM,MAAM,KAAK,cAAc,MAAM,KAAK,CAAC;AAC3C,YAAM,QAAQ,KAAK,gBAAgB,KAAK;AACxC,WAAK,SAAS,KAAK,KAAK;AACxB,WAAK,SAAS,KAAK,KAAK;AACxB,WAAK,gBAAgB,UAAU,KAAK;AAAA,QAClC;AAAA,QACA;AAAA,QACA,OAAO,eAAe,aAAa,OAAO,WAAU,CAAC;AAAA,MACvD,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,eAAe,IAAI,OAAO;AACxB,UAAM,WAAW,KAAK,gBAAgB,OAAO,IAAI,EAAE;AACnD,UAAM,SAAS,MAAM,WAAW,GAAG,IAAI,MAAM,QAAQ,KAAK,EAAE,EAAE,KAAK,IAAI;AACvE,aAAS,aAAa,KAAK,eAAe,aAAa,QAAQ,WAAU,CAAC,CAAC;AAAA,EAC7E;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,MAAM,UAAU,GAAG,CAAC,MAAM,KAAK;AACjC,aAAO,MAAM,OAAO,CAAC,EAAE,KAAK;AAAA,IAC9B,OAAO;AACL,aAAO,MAAM,KAAK;AAAA,IACpB;AAAA,EACF;AAAA,EACA,eAAe;AACb,SAAK;AACL,WAAO,gBAAgB,KAAK;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,IAAI,kBAAkB,IAAI;AACtC,QAAI,CAAC,KAAK,QAAQ,IAAI,EAAE,GAAG;AACzB,WAAK,QAAQ,IAAI,IAAI,EAAE,IAAI,QAAQ,CAAC,GAAG,YAAY,CAAC,EAAE,CAAC;AAAA,IACzD;AACA,UAAM,aAAa,KAAK,QAAQ,IAAI,EAAE;AACtC,QAAI,oBAAoB,UAAU,oBAAoB,MAAM;AAC1D,sBAAgB,MAAM,cAAc,EAAE,QAAQ,CAAC,WAAW;AACxD,cAAM,cAAc,OAAO,QAAQ,YAAY,IAAI,EAAE,KAAK;AAC1D,YAAI,OAAO,aAAa,EAAE,KAAK,MAAM,GAAG;AACtC,gBAAM,YAAY,YAAY,QAAQ,cAAc,OAAO;AAC3D,gBAAM,YAAY,UAAU,QAAQ,eAAe,YAAY;AAC/D,qBAAW,WAAW,KAAK,SAAS;AAAA,QACtC;AACA,mBAAW,OAAO,KAAK,WAAW;AAAA,MACpC,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,SAAS,cAAc;AACjC,YAAQ,MAAM,GAAG,EAAE,QAAQ,CAAC,OAAO;AACjC,UAAI,aAAa,KAAK,SAAS,EAAE;AACjC,UAAI,eAAe,QAAQ;AACzB,cAAM,YAAY,GAAG,KAAK;AAC1B,aAAK,SAAS,SAAS;AACvB,qBAAa,KAAK,SAAS,SAAS;AAAA,MACtC;AACA,iBAAW,QAAQ,KAAK,YAAY;AAAA,IACtC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,SAAS,QAAQ,WAAW;AAC1B,UAAM,OAAO,KAAK,SAAS,MAAM;AACjC,QAAI,SAAS,QAAQ;AACnB,WAAK,OAAO,KAAK,SAAS;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,QAAQ,cAAc;AACjC,UAAM,OAAO,KAAK,SAAS,MAAM;AACjC,QAAI,SAAS,QAAQ;AACnB,WAAK,WAAW,KAAK,YAAY;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB;AACtB,WAAO,KAAK,QAAQ,KAAK,CAAC,QAAQ,IAAI,SAAS,cAAc;AAAA,EAC/D;AAAA,EACA,eAAe;AAxwDjB,QAAAA;AAywDI,aAAOA,MAAA,KAAK,sBAAsB,MAA3B,gBAAAA,IAA8B,UAAS;AAAA,EAChD;AAAA,EACA,aAAa,KAAK;AAChB,UAAM,MAAM,KAAK,sBAAsB;AACvC,QAAI,KAAK;AACP,UAAI,QAAQ;AAAA,IACd,OAAO;AACL,WAAK,QAAQ,QAAQ,EAAE,MAAM,gBAAgB,OAAO,IAAI,CAAC;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,UAAU,KAAK;AACb,WAAO,OAAO,IAAI,CAAC,MAAM,MAAM,IAAI,OAAO,CAAC,EAAE,KAAK,IAAI,IAAI,KAAK;AAAA,EACjE;AAAA,EACA,UAAU;AACR,UAAM,SAAS,WAAU;AACzB,WAAO;AAAA,MACL,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,MACZ,OAAO,CAAC;AAAA,MACR;AAAA,MACA,WAAW,OAAO,KAAK,aAAa,CAAC;AAAA,IACvC;AAAA,EACF;AAAA,EACA,YAAY;AACV,WAAO,WAAU,EAAE;AAAA,EACrB;AAOF,GApjBI,OAAO,IAAM,SAAS,GA6DxB,cA/DY,IA+DL,gBAAe;AAAA,EACpB,aAAa;AAAA,EACb,WAAW;AAAA,EACX,aAAa;AAAA,EACb,YAAY;AACd,IApEY;AAyjBd,IAAI,YAA4B,OAAO,CAAC,YAAY;AAAA;AAAA,YAExC,QAAQ,eAAe;AAAA,cACrB,QAAQ,eAAe;AAAA;AAAA;AAAA,UAG3B,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,UAKlB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOjB,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA,UAIvB,QAAQ,OAAO;AAAA,YACb,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,YAIlB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,YAKjB,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMzB,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAUhB,QAAQ,eAAe;AAAA,UACzB,QAAQ,YAAY;AAAA;AAAA;AAAA,YAGlB,QAAQ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UASvB,QAAQ,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,UAKf,QAAQ,oBAAoB;AAAA;AAAA;AAAA;AAAA,sBAIhB,QAAQ,mBAAmB;AAAA;AAAA,wBAEzB,QAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA,wBAI3B,QAAQ,mBAAmB;AAAA,YACvC,QAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,UAK7B,QAAQ,wBAAwB,QAAQ,iBAAiB;AAAA;AAAA;AAAA,WAGxD,QAAQ,wBAAwB,QAAQ,iBAAiB;AAAA;AAAA;AAAA;AAAA,UAI1D,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMvB,QAAQ,iBAAiB;AAAA,YACvB,QAAQ,iBAAiB;AAAA;AAAA;AAAA;AAAA,UAI3B,QAAQ,iBAAiB;AAAA,YACvB,QAAQ,iBAAiB;AAAA;AAAA;AAAA;AAAA,UAI3B,QAAQ,kBAAkB;AAAA,YACxB,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,UAIpB,QAAQ,uBAAuB,QAAQ,UAAU;AAAA,eAC5C,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,UAKvB,QAAQ,YAAY,QAAQ,OAAO;AAAA,YACjC,QAAQ,eAAe,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,UAI3C,QAAQ,OAAO;AAAA,YACb,QAAQ,eAAe,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,UAI3C,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA,UAIjB,QAAQ,wBAAwB;AAAA,YAC9B,QAAQ,eAAe,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,WAK1C,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAStB,QAAQ,eAAe,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQ3C,QAAQ,uBAAuB,QAAQ,UAAU;AAAA;AAAA;AAAA,UAGjD,QAAQ,gBAAgB,QAAQ,gBAAgB,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAczD,QAAQ,gBAAgB,QAAQ,gBAAgB,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQzD,QAAQ,YAAY;AAAA,YAClB,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMzB,QAAQ,YAAY;AAAA,YAClB,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOzB,QAAQ,aAAa;AAAA;AAAA;AAAA;AAAA,WAIpB,QAAQ,aAAa;AAAA;AAAA;AAAA,mBAGb,QAAQ,aAAa;AAAA;AAAA;AAAA;AAAA,UAI9B,QAAQ,SAAS;AAAA,YACf,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOnB,QAAQ,SAAS;AAAA;AAAA,GAExB,WAAW;AACd,IAAI,iBAAiB;", "names": ["_a"]}