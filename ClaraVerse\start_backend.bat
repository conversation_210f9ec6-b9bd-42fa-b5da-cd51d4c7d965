@echo off
:: WeMa IA Backend Starter
:: <PERSON><PERSON><PERSON><PERSON> le backend Python avec toutes les fonctionnalités

echo 🚀 WeMa IA - Démarrage du backend...

:: Aller dans le répertoire du backend
cd /d "%~dp0py_backend"

:: Vérifier si Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERREUR: Python n'est pas installé ou pas dans le PATH
    echo Veuillez installer Python depuis https://python.org
    pause
    exit /b 1
)

:: Installer les dépendances si nécessaire (première fois)
if not exist ".deps_installed" (
    echo 📦 Installation des dépendances Python...
    python -m pip install --upgrade pip
    python -m pip install -r requirements.txt
    if errorlevel 0 (
        echo. > .deps_installed
        echo ✅ Dépendances installées avec succès
    ) else (
        echo ❌ Erreur lors de l'installation des dépendances
        pause
        exit /b 1
    )
)

:: Démarrer le backend Python
echo ✅ Démarrage du serveur backend...
python main.py

:: <PERSON><PERSON><PERSON> la fenêtre ouverte en cas d'erreur
if errorlevel 1 (
    echo ❌ Erreur lors du démarrage du backend
    pause
)
