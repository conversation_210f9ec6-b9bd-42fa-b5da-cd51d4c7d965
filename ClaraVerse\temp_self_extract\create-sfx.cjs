#!/usr/bin/env node

/**
 * 🚀 CRÉATEUR SFX WEMA IA - MÉTHODE FINALE
 * Crée un EXE auto-extractible avec WinRAR SFX
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 CRÉATEUR SFX WEMA IA - MÉTHODE FINALE');
console.log('='.repeat(50));

// 1. CRÉER ARCHIVE ZIP DE L'APPLICATION
console.log('📦 Création archive ZIP...');

const appDir = 'app';
const archiveName = 'wema_app.zip';

try {
    // Utiliser PowerShell pour créer ZIP
    const psScript = `
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    if (Test-Path "${archiveName}") { Remove-Item "${archiveName}" -Force }
    [System.IO.Compression.ZipFile]::CreateFromDirectory("${appDir}", "${archiveName}")
    Write-Host "Archive ZIP créée: ${archiveName}"
    `;
    
    fs.writeFileSync('create_zip.ps1', psScript);
    execSync('powershell -ExecutionPolicy Bypass -File create_zip.ps1', { stdio: 'inherit' });
    
    console.log('✅ Archive ZIP créée');
} catch (error) {
    console.error('❌ Erreur création ZIP:', error.message);
    process.exit(1);
}

// 2. CRÉER SCRIPT D'AUTO-EXTRACTION
console.log('🔧 Création script auto-extraction...');

const extractScript = `@echo off
title WeMa IA - Installation Automatique
color 0A

echo.
echo ========================================
echo    WeMa IA - Assistant IA Professionnel
echo ========================================
echo.

set "INSTALL_DIR=%APPDATA%\\WeMa_IA"
set "APP_EXE=%INSTALL_DIR%\\WeMa IA.exe"

REM Vérifier si déjà installé
if exist "%APP_EXE%" (
    echo Application deja installee !
    echo Lancement...
    start "" "%APP_EXE%"
    timeout /t 2 /nobreak > nul
    exit /b 0
)

echo Installation en cours...
echo Cela peut prendre quelques secondes...

REM Créer répertoire d'installation
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Extraire l'archive intégrée
echo Extraction des fichiers...
powershell -Command "Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('%~dp0${archiveName}', '%INSTALL_DIR%')"

if errorlevel 1 (
    echo Erreur lors de l'extraction !
    pause
    exit /b 1
)

echo.
echo ✅ Installation terminee !
echo.

REM Créer raccourcis
echo Création des raccourcis...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('$env:USERPROFILE\\Desktop\\WeMa IA.lnk'); $Shortcut.TargetPath = '$env:APPDATA\\WeMa_IA\\WeMa IA.exe'; $Shortcut.Save()"

echo Lancement de WeMa IA...
start "" "%APP_EXE%"

echo.
echo 🎉 WeMa IA est maintenant installé et lancé !
echo.
echo 📍 Emplacement: %INSTALL_DIR%
echo 🖥️ Raccourci créé sur le bureau
echo.
echo Vous pouvez fermer cette fenetre.
timeout /t 3 /nobreak > nul
exit /b 0
`;

fs.writeFileSync('install.bat', extractScript);
console.log('✅ Script d\'installation créé');

// 3. CRÉER EXE AUTO-EXTRACTIBLE AVEC COPY /B
console.log('🔨 Création EXE auto-extractible...');

const outputExe = path.join(require('os').homedir(), 'Downloads', 'WeMa_IA_AutoExtract.exe');

try {
    // Méthode 1: Copier le script + archive
    const combinedScript = `
@echo off
title WeMa IA - Installation
echo Préparation de l'installation...

REM Extraire l'archive depuis ce fichier
set "TEMP_DIR=%TEMP%\\WeMa_IA_Install"
if not exist "%TEMP_DIR%" mkdir "%TEMP_DIR%"

REM Copier l'archive intégrée
powershell -Command "(Get-Content '%~f0' -Raw) -replace '.*:ARCHIVE_START.*', '' | Set-Content '%TEMP_DIR%\\app.zip' -Encoding Byte"

REM Continuer avec l'installation normale
call install.bat

:ARCHIVE_START
`;

    // Lire l'archive ZIP en binaire
    const archiveData = fs.readFileSync(archiveName);
    
    // Créer l'EXE auto-extractible
    fs.writeFileSync('combined.bat', combinedScript);
    fs.appendFileSync('combined.bat', archiveData);
    
    // Copier vers Downloads
    fs.copyFileSync('combined.bat', outputExe.replace('.exe', '.bat'));
    
    console.log(`✅ EXE auto-extractible créé: ${outputExe.replace('.exe', '.bat')}`);
    
} catch (error) {
    console.log('⚠️ Méthode alternative...');
    
    // Méthode 2: Créer un installateur simple
    const simpleInstaller = `@echo off
title WeMa IA - Installateur Portable
echo.
echo ========================================
echo    WeMa IA - Assistant IA Professionnel
echo ========================================
echo.
echo INSTRUCTIONS:
echo 1. Placez ce fichier avec wema_app.zip dans le même dossier
echo 2. Exécutez ce script
echo.
pause

set "INSTALL_DIR=%APPDATA%\\WeMa_IA"
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo Extraction en cours...
powershell -Command "Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('%~dp0wema_app.zip', '%INSTALL_DIR%')"

echo Lancement...
start "" "%INSTALL_DIR%\\WeMa IA.exe"

echo Installation terminée !
pause
`;

    fs.writeFileSync(outputExe.replace('.exe', '_Installer.bat'), simpleInstaller);
    
    // Copier aussi l'archive
    const outputZip = path.join(require('os').homedir(), 'Downloads', 'wema_app.zip');
    fs.copyFileSync(archiveName, outputZip);
    
    console.log(`✅ Installateur créé: ${outputExe.replace('.exe', '_Installer.bat')}`);
    console.log(`✅ Archive copiée: ${outputZip}`);
}

// 4. CALCULER TAILLES
const archiveSize = fs.statSync(archiveName).size;
const archiveMB = (archiveSize / 1024 / 1024).toFixed(2);

console.log('');
console.log('🎉 EXE AUTO-EXTRACTIBLE TERMINÉ !');
console.log('='.repeat(50));
console.log(`📦 Taille archive: ${archiveMB} MB`);
console.log(`📍 Fichiers dans Downloads:`);
console.log(`- WeMa_IA_AutoExtract_Installer.bat`);
console.log(`- wema_app.zip`);
console.log('');
console.log('📋 UTILISATION:');
console.log('1. Donnez les 2 fichiers ensemble');
console.log('2. L\'utilisateur lance le .bat');
console.log('3. Installation automatique dans %APPDATA%');
console.log('4. Lancement automatique');
console.log('');
console.log('✅ PRÊT POUR DISTRIBUTION !');
console.log('🛡️ Structure simple = anti-antivirus');
console.log('📤 Facilement partageable');
