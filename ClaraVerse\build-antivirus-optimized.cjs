#!/usr/bin/env node

/**
 * 🛡️ BUILD ANTI-ANTIVIRUS OPTIMISÉ WEMA IA
 * Utilise la méthode qui marche + optimisations anti-antivirus
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🛡️ BUILD ANTI-ANTIVIRUS OPTIMISÉ WEMA IA');
console.log('Méthode: npm run electron:build-win + optimisations');
console.log('='.repeat(60));

// 1. NETTOYAGE COMPLET
console.log('🧹 Nettoyage complet...');
try {
    if (fs.existsSync('release')) {
        execSync('rmdir /s /q release', { stdio: 'inherit' });
    }
    if (fs.existsSync('dist')) {
        execSync('rmdir /s /q dist', { stdio: 'inherit' });
    }
} catch (e) {
    console.log('⚠️ Nettoyage partiel');
}

// 2. OPTIMISATIONS ANTI-ANTIVIRUS TEMPORAIRES
console.log('🛡️ Application optimisations anti-antivirus...');

const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const originalConfig = JSON.stringify(packageJson.build, null, 2);

// Optimisations anti-antivirus
packageJson.build.compression = "store"; // Pas de compression = moins suspect
packageJson.build.nsis.warningsAsErrors = false;
packageJson.build.nsis.displayLanguageSelector = false;
packageJson.build.nsis.deleteAppDataOnUninstall = false; // Moins agressif
packageJson.build.win.requestedExecutionLevel = "asInvoker"; // Pas de droits admin
packageJson.build.win.signAndEditExecutable = false;
packageJson.build.win.verifyUpdateCodeSignature = false;

// Sauvegarder config temporaire
fs.writeFileSync('package.json.backup', fs.readFileSync('package.json', 'utf8'));
fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));

console.log('✅ Optimisations appliquées');

// 3. BUILD AVEC LA MÉTHODE QUI MARCHE
console.log('🏗️ Build avec méthode éprouvée...');

try {
    // Exactement la même commande qui marche
    execSync('npm run electron:build-win', { 
        stdio: 'inherit',
        timeout: 300000 // 5 minutes
    });
    console.log('✅ Build terminé avec succès');
} catch (error) {
    console.error('❌ Erreur build:', error.message);
    
    // Restaurer config originale
    fs.writeFileSync('package.json', fs.readFileSync('package.json.backup', 'utf8'));
    fs.unlinkSync('package.json.backup');
    process.exit(1);
}

// 4. RESTAURER CONFIG ORIGINALE
console.log('🔄 Restauration configuration...');
fs.writeFileSync('package.json', fs.readFileSync('package.json.backup', 'utf8'));
fs.unlinkSync('package.json.backup');

// 5. ANALYSE DU RÉSULTAT
console.log('🔍 Analyse du résultat...');

const releaseDir = 'release';
if (fs.existsSync(releaseDir)) {
    const files = fs.readdirSync(releaseDir);
    console.log('📁 Contenu release:');
    
    for (const file of files) {
        const filePath = path.join(releaseDir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isFile()) {
            const sizeMB = (stats.size / 1024 / 1024).toFixed(2);
            console.log(`  📄 ${file}: ${sizeMB} MB`);
            
            if (file.endsWith('.exe')) {
                // Copier l'EXE vers Downloads avec nom optimisé
                const downloadsPath = path.join(require('os').homedir(), 'Downloads', `${file.replace('.exe', '')}_AntiVirus_${sizeMB}MB.exe`);
                try {
                    fs.copyFileSync(filePath, downloadsPath);
                    console.log(`  ✅ Copié vers: ${downloadsPath}`);
                } catch (e) {
                    console.log('  ⚠️ Impossible de copier vers Downloads');
                }
            }
        } else if (stats.isDirectory()) {
            const dirSize = getFolderSize(filePath);
            const dirSizeMB = (dirSize / 1024 / 1024).toFixed(2);
            console.log(`  📁 ${file}/: ${dirSizeMB} MB`);
            
            if (file === 'win-unpacked') {
                // Créer version portable optimisée
                const portableDir = path.join(require('os').homedir(), 'Downloads', `WeMa_IA_Portable_${dirSizeMB}MB`);
                try {
                    if (fs.existsSync(portableDir)) {
                        execSync(`rmdir /s /q "${portableDir}"`, { stdio: 'ignore' });
                    }
                    copyRecursive(filePath, portableDir);
                    
                    // Créer script de lancement portable
                    const launcherScript = `@echo off
title WeMa IA - Assistant IA Professionnel
echo.
echo ========================================
echo    WeMa IA - Assistant IA Professionnel
echo ========================================
echo.
echo Lancement de l'application...
start "" "%~dp0WeMa IA.exe"
echo Application lancee !
`;
                    fs.writeFileSync(path.join(portableDir, 'Lancer WeMa IA.bat'), launcherScript);
                    
                    console.log(`  ✅ Version portable: ${portableDir}`);
                } catch (e) {
                    console.log('  ⚠️ Erreur création portable');
                }
            }
        }
    }
}

console.log('');
console.log('🎉 BUILD ANTI-ANTIVIRUS TERMINÉ !');
console.log('='.repeat(60));
console.log('📦 RÉSULTATS:');
console.log('');
console.log('🎯 DISTRIBUTION OPTIONS:');
console.log('');
console.log('1️⃣ **EXE INSTALLATEUR** (RECOMMANDÉ):');
console.log('   - Fichier: WeMa IA Setup 0.1.2.exe');
console.log('   - Usage: Double-clic → Installation automatique');
console.log('   - Avantage: Un seul fichier, professionnel');
console.log('');
console.log('2️⃣ **DOSSIER PORTABLE**:');
console.log('   - Dossier: WeMa_IA_Portable_XXXmb/');
console.log('   - Usage: Copier dossier → Lancer WeMa IA.exe');
console.log('   - Avantage: Pas d\'installation, plus léger');
console.log('');
console.log('🛡️ OPTIMISATIONS ANTI-ANTIVIRUS APPLIQUÉES:');
console.log('✅ Compression réduite (store)');
console.log('✅ Pas de signature forcée');
console.log('✅ Pas de droits administrateur');
console.log('✅ Configuration NSIS optimisée');
console.log('');
console.log('📤 PRÊT POUR DISTRIBUTION !');

// Fonctions utilitaires
function copyRecursive(src, dest) {
    const stats = fs.statSync(src);
    if (stats.isDirectory()) {
        if (!fs.existsSync(dest)) {
            fs.mkdirSync(dest, { recursive: true });
        }
        const files = fs.readdirSync(src);
        for (const file of files) {
            copyRecursive(path.join(src, file), path.join(dest, file));
        }
    } else {
        fs.copyFileSync(src, dest);
    }
}

function getFolderSize(folderPath) {
    let totalSize = 0;
    
    function calculateSize(currentPath) {
        const stats = fs.statSync(currentPath);
        if (stats.isDirectory()) {
            const files = fs.readdirSync(currentPath);
            for (const file of files) {
                calculateSize(path.join(currentPath, file));
            }
        } else {
            totalSize += stats.size;
        }
    }
    
    calculateSize(folderPath);
    return totalSize;
}
