{"version": 3, "sources": ["../../../src/utils/lineWithOffset.ts"], "sourcesContent": ["import type { EdgeData, Point } from '../types.js';\n\n// We need to draw the lines a bit shorter to avoid drawing\n// under any transparent markers.\n// The offsets are calculated from the markers' dimensions.\nconst markerOffsets = {\n  aggregation: 18,\n  extension: 18,\n  composition: 18,\n  dependency: 6,\n  lollipop: 13.5,\n  arrow_point: 4,\n} as const;\n\n/**\n * Calculate the deltas and angle between two points\n * @param point1 - First point\n * @param point2 - Second point\n * @returns The angle, deltaX and deltaY\n */\nfunction calculateDeltaAndAngle(\n  point1?: Point | [number, number],\n  point2?: Point | [number, number]\n): { angle: number; deltaX: number; deltaY: number } {\n  if (point1 === undefined || point2 === undefined) {\n    return { angle: 0, deltaX: 0, deltaY: 0 };\n  }\n  point1 = pointTransformer(point1);\n  point2 = pointTransformer(point2);\n  const [x1, y1] = [point1.x, point1.y];\n  const [x2, y2] = [point2.x, point2.y];\n  const deltaX = x2 - x1;\n  const deltaY = y2 - y1;\n  return { angle: Math.atan(deltaY / deltaX), deltaX, deltaY };\n}\n\nconst pointTransformer = (data: Point | [number, number]) => {\n  if (Array.isArray(data)) {\n    return { x: data[0], y: data[1] };\n  }\n  return data;\n};\n\nexport const getLineFunctionsWithOffset = (\n  edge: Pick<EdgeData, 'arrowTypeStart' | 'arrowTypeEnd'>\n) => {\n  return {\n    x: function (\n      this: void,\n      d: Point | [number, number],\n      i: number,\n      data: (Point | [number, number])[]\n    ) {\n      let offset = 0;\n      const DIRECTION =\n        pointTransformer(data[0]).x < pointTransformer(data[data.length - 1]).x ? 'left' : 'right';\n      if (i === 0 && Object.hasOwn(markerOffsets, edge.arrowTypeStart)) {\n        const { angle, deltaX } = calculateDeltaAndAngle(data[0], data[1]);\n        offset =\n          markerOffsets[edge.arrowTypeStart as keyof typeof markerOffsets] *\n          Math.cos(angle) *\n          (deltaX >= 0 ? 1 : -1);\n      } else if (i === data.length - 1 && Object.hasOwn(markerOffsets, edge.arrowTypeEnd)) {\n        const { angle, deltaX } = calculateDeltaAndAngle(\n          data[data.length - 1],\n          data[data.length - 2]\n        );\n        offset =\n          markerOffsets[edge.arrowTypeEnd as keyof typeof markerOffsets] *\n          Math.cos(angle) *\n          (deltaX >= 0 ? 1 : -1);\n      }\n\n      const differenceToEnd = Math.abs(\n        pointTransformer(d).x - pointTransformer(data[data.length - 1]).x\n      );\n      const differenceInYEnd = Math.abs(\n        pointTransformer(d).y - pointTransformer(data[data.length - 1]).y\n      );\n      const differenceToStart = Math.abs(pointTransformer(d).x - pointTransformer(data[0]).x);\n      const differenceInYStart = Math.abs(pointTransformer(d).y - pointTransformer(data[0]).y);\n      const startMarkerHeight = markerOffsets[edge.arrowTypeStart as keyof typeof markerOffsets];\n      const endMarkerHeight = markerOffsets[edge.arrowTypeEnd as keyof typeof markerOffsets];\n      const extraRoom = 1;\n\n      // Adjust the offset if the difference is smaller than the marker height\n      if (\n        differenceToEnd < endMarkerHeight &&\n        differenceToEnd > 0 &&\n        differenceInYEnd < endMarkerHeight\n      ) {\n        let adjustment = endMarkerHeight + extraRoom - differenceToEnd;\n        adjustment *= DIRECTION === 'right' ? -1 : 1;\n        // Adjust the offset by the amount needed to fit the marker\n        offset -= adjustment;\n      }\n\n      if (\n        differenceToStart < startMarkerHeight &&\n        differenceToStart > 0 &&\n        differenceInYStart < startMarkerHeight\n      ) {\n        let adjustment = startMarkerHeight + extraRoom - differenceToStart;\n        adjustment *= DIRECTION === 'right' ? -1 : 1;\n        offset += adjustment;\n      }\n\n      return pointTransformer(d).x + offset;\n    },\n    y: function (\n      this: void,\n      d: Point | [number, number],\n      i: number,\n      data: (Point | [number, number])[]\n    ) {\n      let offset = 0;\n      const DIRECTION =\n        pointTransformer(data[0]).y < pointTransformer(data[data.length - 1]).y ? 'down' : 'up';\n      if (i === 0 && Object.hasOwn(markerOffsets, edge.arrowTypeStart)) {\n        const { angle, deltaY } = calculateDeltaAndAngle(data[0], data[1]);\n        offset =\n          markerOffsets[edge.arrowTypeStart as keyof typeof markerOffsets] *\n          Math.abs(Math.sin(angle)) *\n          (deltaY >= 0 ? 1 : -1);\n      } else if (i === data.length - 1 && Object.hasOwn(markerOffsets, edge.arrowTypeEnd)) {\n        const { angle, deltaY } = calculateDeltaAndAngle(\n          data[data.length - 1],\n          data[data.length - 2]\n        );\n        offset =\n          markerOffsets[edge.arrowTypeEnd as keyof typeof markerOffsets] *\n          Math.abs(Math.sin(angle)) *\n          (deltaY >= 0 ? 1 : -1);\n      }\n\n      const differenceToEnd = Math.abs(\n        pointTransformer(d).y - pointTransformer(data[data.length - 1]).y\n      );\n      const differenceInXEnd = Math.abs(\n        pointTransformer(d).x - pointTransformer(data[data.length - 1]).x\n      );\n      const differenceToStart = Math.abs(pointTransformer(d).y - pointTransformer(data[0]).y);\n      const differenceInXStart = Math.abs(pointTransformer(d).x - pointTransformer(data[0]).x);\n      const startMarkerHeight = markerOffsets[edge.arrowTypeStart as keyof typeof markerOffsets];\n      const endMarkerHeight = markerOffsets[edge.arrowTypeEnd as keyof typeof markerOffsets];\n      const extraRoom = 1;\n\n      // Adjust the offset if the difference is smaller than the marker height\n      if (\n        differenceToEnd < endMarkerHeight &&\n        differenceToEnd > 0 &&\n        differenceInXEnd < endMarkerHeight\n      ) {\n        let adjustment = endMarkerHeight + extraRoom - differenceToEnd;\n        adjustment *= DIRECTION === 'up' ? -1 : 1;\n        // Adjust the offset by the amount needed to fit the marker\n        offset -= adjustment;\n      }\n\n      if (\n        differenceToStart < startMarkerHeight &&\n        differenceToStart > 0 &&\n        differenceInXStart < startMarkerHeight\n      ) {\n        let adjustment = startMarkerHeight + extraRoom - differenceToStart;\n        adjustment *= DIRECTION === 'up' ? -1 : 1;\n        offset += adjustment;\n      }\n      return pointTransformer(d).y + offset;\n    },\n  };\n};\n\nif (import.meta.vitest) {\n  const { it, expect, describe } = import.meta.vitest;\n  describe('calculateDeltaAndAngle', () => {\n    it('should calculate the angle and deltas between two points', () => {\n      expect(calculateDeltaAndAngle([0, 0], [0, 1])).toStrictEqual({\n        angle: 1.5707963267948966,\n        deltaX: 0,\n        deltaY: 1,\n      });\n      expect(calculateDeltaAndAngle([1, 0], [0, -1])).toStrictEqual({\n        angle: 0.7853981633974483,\n        deltaX: -1,\n        deltaY: -1,\n      });\n      expect(calculateDeltaAndAngle({ x: 1, y: 0 }, [0, -1])).toStrictEqual({\n        angle: 0.7853981633974483,\n        deltaX: -1,\n        deltaY: -1,\n      });\n      expect(calculateDeltaAndAngle({ x: 1, y: 0 }, { x: 1, y: 0 })).toStrictEqual({\n        angle: NaN,\n        deltaX: 0,\n        deltaY: 0,\n      });\n    });\n\n    it('should calculate the angle and deltas if one point in undefined', () => {\n      expect(calculateDeltaAndAngle(undefined, [0, 1])).toStrictEqual({\n        angle: 0,\n        deltaX: 0,\n        deltaY: 0,\n      });\n      expect(calculateDeltaAndAngle([0, 1], undefined)).toStrictEqual({\n        angle: 0,\n        deltaX: 0,\n        deltaY: 0,\n      });\n    });\n  });\n}\n"], "mappings": "yCAKA,IAAMA,EAAgB,CACpB,YAAa,GACb,UAAW,GACX,YAAa,GACb,WAAY,EACZ,SAAU,KACV,YAAa,CACf,EAQA,SAASC,EACPC,EACAC,EACmD,CACnD,GAAID,IAAW,QAAaC,IAAW,OACrC,MAAO,CAAE,MAAO,EAAG,OAAQ,EAAG,OAAQ,CAAE,EAE1CD,EAASE,EAAiBF,CAAM,EAChCC,EAASC,EAAiBD,CAAM,EAChC,GAAM,CAACE,EAAIC,CAAE,EAAI,CAACJ,EAAO,EAAGA,EAAO,CAAC,EAC9B,CAACK,EAAIC,CAAE,EAAI,CAACL,EAAO,EAAGA,EAAO,CAAC,EAC9BM,EAASF,EAAKF,EACdK,EAASF,EAAKF,EACpB,MAAO,CAAE,MAAO,KAAK,KAAKI,EAASD,CAAM,EAAG,OAAAA,EAAQ,OAAAC,CAAO,CAC7D,CAdSC,EAAAV,EAAA,0BAgBT,IAAMG,EAAmBO,EAACC,GACpB,MAAM,QAAQA,CAAI,EACb,CAAE,EAAGA,EAAK,CAAC,EAAG,EAAGA,EAAK,CAAC,CAAE,EAE3BA,EAJgB,oBAOZC,EAA6BF,EACxCG,IAEO,CACL,EAAGH,EAAA,SAEDI,EACAC,EACAJ,EACA,CACA,IAAIK,EAAS,EACPC,EACJd,EAAiBQ,EAAK,CAAC,CAAC,EAAE,EAAIR,EAAiBQ,EAAKA,EAAK,OAAS,CAAC,CAAC,EAAE,EAAI,OAAS,QACrF,GAAII,IAAM,GAAK,OAAO,OAAOhB,EAAec,EAAK,cAAc,EAAG,CAChE,GAAM,CAAE,MAAAK,EAAO,OAAAV,CAAO,EAAIR,EAAuBW,EAAK,CAAC,EAAGA,EAAK,CAAC,CAAC,EACjEK,EACEjB,EAAcc,EAAK,cAA4C,EAC/D,KAAK,IAAIK,CAAK,GACbV,GAAU,EAAI,EAAI,GACvB,SAAWO,IAAMJ,EAAK,OAAS,GAAK,OAAO,OAAOZ,EAAec,EAAK,YAAY,EAAG,CACnF,GAAM,CAAE,MAAAK,EAAO,OAAAV,CAAO,EAAIR,EACxBW,EAAKA,EAAK,OAAS,CAAC,EACpBA,EAAKA,EAAK,OAAS,CAAC,CACtB,EACAK,EACEjB,EAAcc,EAAK,YAA0C,EAC7D,KAAK,IAAIK,CAAK,GACbV,GAAU,EAAI,EAAI,GACvB,CAEA,IAAMW,EAAkB,KAAK,IAC3BhB,EAAiBW,CAAC,EAAE,EAAIX,EAAiBQ,EAAKA,EAAK,OAAS,CAAC,CAAC,EAAE,CAClE,EACMS,EAAmB,KAAK,IAC5BjB,EAAiBW,CAAC,EAAE,EAAIX,EAAiBQ,EAAKA,EAAK,OAAS,CAAC,CAAC,EAAE,CAClE,EACMU,EAAoB,KAAK,IAAIlB,EAAiBW,CAAC,EAAE,EAAIX,EAAiBQ,EAAK,CAAC,CAAC,EAAE,CAAC,EAChFW,EAAqB,KAAK,IAAInB,EAAiBW,CAAC,EAAE,EAAIX,EAAiBQ,EAAK,CAAC,CAAC,EAAE,CAAC,EACjFY,EAAoBxB,EAAcc,EAAK,cAA4C,EACnFW,EAAkBzB,EAAcc,EAAK,YAA0C,EAC/EY,EAAY,EAGlB,GACEN,EAAkBK,GAClBL,EAAkB,GAClBC,EAAmBI,EACnB,CACA,IAAIE,EAAaF,EAAkBC,EAAYN,EAC/CO,GAAcT,IAAc,QAAU,GAAK,EAE3CD,GAAUU,CACZ,CAEA,GACEL,EAAoBE,GACpBF,EAAoB,GACpBC,EAAqBC,EACrB,CACA,IAAIG,EAAaH,EAAoBE,EAAYJ,EACjDK,GAAcT,IAAc,QAAU,GAAK,EAC3CD,GAAUU,CACZ,CAEA,OAAOvB,EAAiBW,CAAC,EAAE,EAAIE,CACjC,EA7DG,KA8DH,EAAGN,EAAA,SAEDI,EACAC,EACAJ,EACA,CACA,IAAIK,EAAS,EACPC,EACJd,EAAiBQ,EAAK,CAAC,CAAC,EAAE,EAAIR,EAAiBQ,EAAKA,EAAK,OAAS,CAAC,CAAC,EAAE,EAAI,OAAS,KACrF,GAAII,IAAM,GAAK,OAAO,OAAOhB,EAAec,EAAK,cAAc,EAAG,CAChE,GAAM,CAAE,MAAAK,EAAO,OAAAT,CAAO,EAAIT,EAAuBW,EAAK,CAAC,EAAGA,EAAK,CAAC,CAAC,EACjEK,EACEjB,EAAcc,EAAK,cAA4C,EAC/D,KAAK,IAAI,KAAK,IAAIK,CAAK,CAAC,GACvBT,GAAU,EAAI,EAAI,GACvB,SAAWM,IAAMJ,EAAK,OAAS,GAAK,OAAO,OAAOZ,EAAec,EAAK,YAAY,EAAG,CACnF,GAAM,CAAE,MAAAK,EAAO,OAAAT,CAAO,EAAIT,EACxBW,EAAKA,EAAK,OAAS,CAAC,EACpBA,EAAKA,EAAK,OAAS,CAAC,CACtB,EACAK,EACEjB,EAAcc,EAAK,YAA0C,EAC7D,KAAK,IAAI,KAAK,IAAIK,CAAK,CAAC,GACvBT,GAAU,EAAI,EAAI,GACvB,CAEA,IAAMU,EAAkB,KAAK,IAC3BhB,EAAiBW,CAAC,EAAE,EAAIX,EAAiBQ,EAAKA,EAAK,OAAS,CAAC,CAAC,EAAE,CAClE,EACMgB,EAAmB,KAAK,IAC5BxB,EAAiBW,CAAC,EAAE,EAAIX,EAAiBQ,EAAKA,EAAK,OAAS,CAAC,CAAC,EAAE,CAClE,EACMU,EAAoB,KAAK,IAAIlB,EAAiBW,CAAC,EAAE,EAAIX,EAAiBQ,EAAK,CAAC,CAAC,EAAE,CAAC,EAChFiB,EAAqB,KAAK,IAAIzB,EAAiBW,CAAC,EAAE,EAAIX,EAAiBQ,EAAK,CAAC,CAAC,EAAE,CAAC,EACjFY,EAAoBxB,EAAcc,EAAK,cAA4C,EACnFW,EAAkBzB,EAAcc,EAAK,YAA0C,EAC/EY,EAAY,EAGlB,GACEN,EAAkBK,GAClBL,EAAkB,GAClBQ,EAAmBH,EACnB,CACA,IAAIE,EAAaF,EAAkBC,EAAYN,EAC/CO,GAAcT,IAAc,KAAO,GAAK,EAExCD,GAAUU,CACZ,CAEA,GACEL,EAAoBE,GACpBF,EAAoB,GACpBO,EAAqBL,EACrB,CACA,IAAIG,EAAaH,EAAoBE,EAAYJ,EACjDK,GAAcT,IAAc,KAAO,GAAK,EACxCD,GAAUU,CACZ,CACA,OAAOvB,EAAiBW,CAAC,EAAE,EAAIE,CACjC,EA5DG,IA6DL,GA/HwC", "names": ["markerOffsets", "calculateDeltaAndAngle", "point1", "point2", "pointTransformer", "x1", "y1", "x2", "y2", "deltaX", "deltaY", "__name", "data", "getLineFunctionsWithOffset", "edge", "d", "i", "offset", "DIRECTION", "angle", "differenceToEnd", "differenceInYEnd", "differenceToStart", "differenceInYStart", "startMarkerHeight", "endMarkerHeight", "extraRoom", "adjustment", "differenceInXEnd", "differenceInXStart"]}