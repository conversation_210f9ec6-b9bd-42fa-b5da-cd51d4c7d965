#!/usr/bin/env node

/**
 * 📦 CRÉATEUR PORTABLE MANUEL - ANTI-ANTIVIRUS
 * Crée un dossier portable léger manuellement
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('📦 CRÉATEUR PORTABLE MANUEL - ANTI-ANTIVIRUS');
console.log('='.repeat(50));

// 1. CRÉER STRUCTURE PORTABLE
const portableDir = path.join(require('os').homedir(), 'Downloads', 'WeMa_IA_Portable_Light');

console.log('📁 Création structure portable...');

// Supprimer l'ancien
if (fs.existsSync(portableDir)) {
    try {
        execSync(`rmdir /s /q "${portableDir}"`, { stdio: 'inherit' });
    } catch (e) {
        console.log('⚠️ Nettoyage partiel');
    }
}

// Créer la structure
fs.mkdirSync(portableDir, { recursive: true });
fs.mkdirSync(path.join(portableDir, 'Data'), { recursive: true });
fs.mkdirSync(path.join(portableDir, 'Logs'), { recursive: true });
fs.mkdirSync(path.join(portableDir, 'resources'), { recursive: true });

// 2. COPIER ELECTRON MINIMAL
console.log('⚡ Copie Electron minimal...');

// Télécharger Electron portable si nécessaire
const electronVersion = '37.2.1';
const electronDir = path.join(portableDir, 'electron');

// Pour l'instant, utilisons les fichiers du build existant si disponible
const sourceWinUnpacked = 'release/win-unpacked';
if (fs.existsSync(sourceWinUnpacked)) {
    console.log('📋 Copie depuis build existant...');
    
    // Copier les DLL et fichiers système essentiels
    const essentialFiles = [
        'chrome_100_percent.pak',
        'chrome_200_percent.pak',
        'icudtl.dat',
        'resources.pak',
        'v8_context_snapshot.bin',
        'vk_swiftshader_icd.json',
        'vulkan-1.dll',
        'd3dcompiler_47.dll',
        'libEGL.dll',
        'libGLESv2.dll',
        'ffmpeg.dll'
    ];
    
    for (const file of essentialFiles) {
        const srcPath = path.join(sourceWinUnpacked, file);
        const destPath = path.join(portableDir, file);
        if (fs.existsSync(srcPath)) {
            fs.copyFileSync(srcPath, destPath);
            console.log(`✅ ${file}`);
        }
    }
    
    // Copier les dossiers essentiels
    const essentialDirs = ['locales', 'swiftshader'];
    for (const dir of essentialDirs) {
        const srcPath = path.join(sourceWinUnpacked, dir);
        const destPath = path.join(portableDir, dir);
        if (fs.existsSync(srcPath)) {
            copyRecursive(srcPath, destPath);
            console.log(`✅ ${dir}/`);
        }
    }
}

// 3. CRÉER APP MINIMAL
console.log('📱 Création app minimal...');

// Créer un package.json minimal
const minimalPackage = {
    "name": "wema-ia",
    "version": "0.1.2",
    "description": "WeMa IA - Assistant IA Professionnel",
    "main": "electron.cjs"
};

fs.writeFileSync(path.join(portableDir, 'resources', 'package.json'), JSON.stringify(minimalPackage, null, 2));

// Créer un electron.cjs minimal
const minimalElectron = `
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const { spawn } = require('child_process');

let mainWindow;
let backendProcess;

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js')
        },
        icon: path.join(__dirname, 'wema-icon-256.ico'),
        title: 'WeMa IA'
    });

    // Charger l'interface depuis le backend
    mainWindow.loadURL('http://localhost:5001');
    
    mainWindow.on('closed', () => {
        mainWindow = null;
        if (backendProcess) {
            backendProcess.kill();
        }
    });
}

function startBackend() {
    const backendPath = path.join(__dirname, 'py_backend', 'main.py');
    backendProcess = spawn('python', [backendPath], {
        cwd: path.join(__dirname, 'py_backend')
    });
    
    backendProcess.stdout.on('data', (data) => {
        console.log('Backend:', data.toString());
    });
}

app.whenReady().then(() => {
    startBackend();
    setTimeout(createWindow, 3000); // Attendre que le backend démarre
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});
`;

fs.writeFileSync(path.join(portableDir, 'resources', 'electron.cjs'), minimalElectron);

// 4. COPIER BACKEND PYTHON
console.log('🐍 Copie backend Python...');
const backendSrc = 'py_backend';
const backendDest = path.join(portableDir, 'resources', 'py_backend');

if (fs.existsSync(backendSrc)) {
    copyRecursive(backendSrc, backendDest);
    console.log('✅ Backend Python copié');
}

// 5. COPIER LOGOS
console.log('🎨 Copie logos...');
const logos = ['wema-logo-light.png', 'wema-logo-dark.png', 'wema-icon-256.ico'];
for (const logo of logos) {
    const srcPath = path.join('public', logo);
    const destPath = path.join(portableDir, 'resources', logo);
    if (fs.existsSync(srcPath)) {
        fs.copyFileSync(srcPath, destPath);
        console.log(`✅ ${logo}`);
    }
}

// 6. CRÉER EXÉCUTABLE
console.log('🚀 Création exécutable...');

// Copier l'EXE Electron et le renommer
const electronExe = path.join(sourceWinUnpacked, 'WeMa IA.exe');
const portableExe = path.join(portableDir, 'WeMa IA.exe');

if (fs.existsSync(electronExe)) {
    fs.copyFileSync(electronExe, portableExe);
    console.log('✅ EXE copié');
}

// 7. CRÉER SCRIPTS DE LANCEMENT
console.log('📝 Création scripts...');

const launcherBat = `@echo off
title WeMa IA - Assistant IA Professionnel
echo.
echo ========================================
echo    WeMa IA - Assistant IA Professionnel  
echo ========================================
echo.
echo Demarrage...

REM Définir variables d'environnement
set WEMA_DATA_DIR=%~dp0Data
set WEMA_LOGS_DIR=%~dp0Logs

REM Lancer l'application
start "" "%~dp0WeMa IA.exe"

echo Application lancee !
timeout /t 2 /nobreak > nul
`;

fs.writeFileSync(path.join(portableDir, 'Lancer WeMa IA.bat'), launcherBat);

// 8. CALCULER TAILLE
const totalSize = getFolderSize(portableDir);
const sizeMB = (totalSize / 1024 / 1024).toFixed(2);

console.log('');
console.log('🎉 PORTABLE MANUEL CRÉÉ !');
console.log('='.repeat(50));
console.log(`📍 Emplacement: ${portableDir}`);
console.log(`📦 Taille: ${sizeMB} MB`);
console.log('');
console.log('✅ PRÊT POUR DISTRIBUTION !');

if (sizeMB < 100) {
    console.log('🏆 EXCELLENT: Ultra-léger !');
} else if (sizeMB < 300) {
    console.log('✅ PARFAIT: Taille idéale !');
} else {
    console.log('⚠️ Acceptable mais encore optimisable');
}

// Fonctions utilitaires
function copyRecursive(src, dest) {
    const stats = fs.statSync(src);
    if (stats.isDirectory()) {
        if (!fs.existsSync(dest)) {
            fs.mkdirSync(dest, { recursive: true });
        }
        const files = fs.readdirSync(src);
        for (const file of files) {
            copyRecursive(path.join(src, file), path.join(dest, file));
        }
    } else {
        fs.copyFileSync(src, dest);
    }
}

function getFolderSize(folderPath) {
    let totalSize = 0;
    
    function calculateSize(currentPath) {
        const stats = fs.statSync(currentPath);
        if (stats.isDirectory()) {
            const files = fs.readdirSync(currentPath);
            for (const file of files) {
                calculateSize(path.join(currentPath, file));
            }
        } else {
            totalSize += stats.size;
        }
    }
    
    calculateSize(folderPath);
    return totalSize;
}
