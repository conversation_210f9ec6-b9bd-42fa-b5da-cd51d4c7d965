{"version": 3, "sources": ["../../../../parser/dist/chunks/mermaid-parser.core/chunk-EXZZNE6F.mjs"], "sourcesContent": ["import {\n  AbstractMermaidTokenBuilder,\n  CommonValueConverter,\n  InfoGeneratedModule,\n  MermaidGeneratedSharedModule,\n  __name\n} from \"./chunk-7PKI6E2E.mjs\";\n\n// src/language/info/module.ts\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject\n} from \"langium\";\n\n// src/language/info/tokenBuilder.ts\nvar InfoTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"InfoTokenBuilder\");\n  }\n  constructor() {\n    super([\"info\", \"showInfo\"]);\n  }\n};\n\n// src/language/info/module.ts\nvar InfoModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */ __name(() => new InfoTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */ __name(() => new CommonValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createInfoServices(context = EmptyFileSystem) {\n  const shared = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Info = inject(\n    createDefaultCoreModule({ shared }),\n    InfoGeneratedModule,\n    InfoModule\n  );\n  shared.ServiceRegistry.register(Info);\n  return { shared, Info };\n}\n__name(createInfoServices, \"createInfoServices\");\n\nexport {\n  InfoModule,\n  createInfoServices\n};\n"], "mappings": "4IAiBA,IAAIA,EAAmB,cAAcC,CAA4B,CAjBjE,MAiBiE,CAAAC,EAAA,yBAC/D,MAAO,CACLA,EAAO,KAAM,kBAAkB,CACjC,CACA,aAAc,CACZ,MAAM,CAAC,OAAQ,UAAU,CAAC,CAC5B,CACF,EAGIC,EAAa,CACf,OAAQ,CACN,aAA8BD,EAAO,IAAM,IAAIF,EAAoB,cAAc,EACjF,eAAgCE,EAAO,IAAM,IAAIE,EAAwB,gBAAgB,CAC3F,CACF,EACA,SAASC,EAAmBC,EAAUC,EAAiB,CACrD,IAAMC,EAASC,EACbC,EAA8BJ,CAAO,EACrCK,CACF,EACMC,EAAOH,EACXI,EAAwB,CAAE,OAAAL,CAAO,CAAC,EAClCM,EACAX,CACF,EACA,OAAAK,EAAO,gBAAgB,SAASI,CAAI,EAC7B,CAAE,OAAAJ,EAAQ,KAAAI,CAAK,CACxB,CAZSV,EAAAG,EAAA,sBAaTH,EAAOG,EAAoB,oBAAoB", "names": ["InfoTokenBuilder", "AbstractMermaidTokenBuilder", "__name", "InfoModule", "CommonValueConverter", "createInfoServices", "context", "EmptyFileSystem", "shared", "inject", "createDefaultSharedCoreModule", "MermaidGeneratedSharedModule", "Info", "createDefaultCoreModule", "InfoGeneratedModule"]}