{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/stateDiagram-DGXRK772.mjs"], "sourcesContent": ["import {\n  StateDB,\n  stateDiagram_default,\n  styles_default\n} from \"./chunk-AEK57VVT.mjs\";\nimport \"./chunk-RZ5BOZE2.mjs\";\nimport \"./chunk-TYCBKAJE.mjs\";\nimport \"./chunk-IIMUDSI4.mjs\";\nimport \"./chunk-VV3M67IP.mjs\";\nimport \"./chunk-HRU6DDCH.mjs\";\nimport \"./chunk-K557N5IZ.mjs\";\nimport \"./chunk-H2D2JQ3I.mjs\";\nimport \"./chunk-C3MQ5ANM.mjs\";\nimport {\n  utils_default\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  common_default,\n  configureSvgSize,\n  getConfig2 as getConfig,\n  log\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/state/stateRenderer.js\nimport { select } from \"d3\";\nimport { layout as dagreLayout } from \"dagre-d3-es/src/dagre/index.js\";\nimport * as graphlib from \"dagre-d3-es/src/graphlib/index.js\";\n\n// src/diagrams/state/shapes.js\nimport { line, curveBasis } from \"d3\";\n\n// src/diagrams/state/id-cache.js\nvar idCache = {};\nvar set = /* @__PURE__ */ __name((key, val) => {\n  idCache[key] = val;\n}, \"set\");\nvar get = /* @__PURE__ */ __name((k) => idCache[k], \"get\");\nvar keys = /* @__PURE__ */ __name(() => Object.keys(idCache), \"keys\");\nvar size = /* @__PURE__ */ __name(() => keys().length, \"size\");\nvar id_cache_default = {\n  get,\n  set,\n  keys,\n  size\n};\n\n// src/diagrams/state/shapes.js\nvar drawStartState = /* @__PURE__ */ __name((g) => g.append(\"circle\").attr(\"class\", \"start-state\").attr(\"r\", getConfig().state.sizeUnit).attr(\"cx\", getConfig().state.padding + getConfig().state.sizeUnit).attr(\"cy\", getConfig().state.padding + getConfig().state.sizeUnit), \"drawStartState\");\nvar drawDivider = /* @__PURE__ */ __name((g) => g.append(\"line\").style(\"stroke\", \"grey\").style(\"stroke-dasharray\", \"3\").attr(\"x1\", getConfig().state.textHeight).attr(\"class\", \"divider\").attr(\"x2\", getConfig().state.textHeight * 2).attr(\"y1\", 0).attr(\"y2\", 0), \"drawDivider\");\nvar drawSimpleState = /* @__PURE__ */ __name((g, stateDef) => {\n  const state = g.append(\"text\").attr(\"x\", 2 * getConfig().state.padding).attr(\"y\", getConfig().state.textHeight + 2 * getConfig().state.padding).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.id);\n  const classBox = state.node().getBBox();\n  g.insert(\"rect\", \":first-child\").attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding).attr(\"width\", classBox.width + 2 * getConfig().state.padding).attr(\"height\", classBox.height + 2 * getConfig().state.padding).attr(\"rx\", getConfig().state.radius);\n  return state;\n}, \"drawSimpleState\");\nvar drawDescrState = /* @__PURE__ */ __name((g, stateDef) => {\n  const addTspan = /* @__PURE__ */ __name(function(textEl, txt, isFirst2) {\n    const tSpan = textEl.append(\"tspan\").attr(\"x\", 2 * getConfig().state.padding).text(txt);\n    if (!isFirst2) {\n      tSpan.attr(\"dy\", getConfig().state.textHeight);\n    }\n  }, \"addTspan\");\n  const title = g.append(\"text\").attr(\"x\", 2 * getConfig().state.padding).attr(\"y\", getConfig().state.textHeight + 1.3 * getConfig().state.padding).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.descriptions[0]);\n  const titleBox = title.node().getBBox();\n  const titleHeight = titleBox.height;\n  const description = g.append(\"text\").attr(\"x\", getConfig().state.padding).attr(\n    \"y\",\n    titleHeight + getConfig().state.padding * 0.4 + getConfig().state.dividerMargin + getConfig().state.textHeight\n  ).attr(\"class\", \"state-description\");\n  let isFirst = true;\n  let isSecond = true;\n  stateDef.descriptions.forEach(function(descr) {\n    if (!isFirst) {\n      addTspan(description, descr, isSecond);\n      isSecond = false;\n    }\n    isFirst = false;\n  });\n  const descrLine = g.append(\"line\").attr(\"x1\", getConfig().state.padding).attr(\"y1\", getConfig().state.padding + titleHeight + getConfig().state.dividerMargin / 2).attr(\"y2\", getConfig().state.padding + titleHeight + getConfig().state.dividerMargin / 2).attr(\"class\", \"descr-divider\");\n  const descrBox = description.node().getBBox();\n  const width = Math.max(descrBox.width, titleBox.width);\n  descrLine.attr(\"x2\", width + 3 * getConfig().state.padding);\n  g.insert(\"rect\", \":first-child\").attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding).attr(\"width\", width + 2 * getConfig().state.padding).attr(\"height\", descrBox.height + titleHeight + 2 * getConfig().state.padding).attr(\"rx\", getConfig().state.radius);\n  return g;\n}, \"drawDescrState\");\nvar addTitleAndBox = /* @__PURE__ */ __name((g, stateDef, altBkg) => {\n  const pad = getConfig().state.padding;\n  const dblPad = 2 * getConfig().state.padding;\n  const orgBox = g.node().getBBox();\n  const orgWidth = orgBox.width;\n  const orgX = orgBox.x;\n  const title = g.append(\"text\").attr(\"x\", 0).attr(\"y\", getConfig().state.titleShift).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.id);\n  const titleBox = title.node().getBBox();\n  const titleWidth = titleBox.width + dblPad;\n  let width = Math.max(titleWidth, orgWidth);\n  if (width === orgWidth) {\n    width = width + dblPad;\n  }\n  let startX;\n  const graphBox = g.node().getBBox();\n  if (stateDef.doc) {\n  }\n  startX = orgX - pad;\n  if (titleWidth > orgWidth) {\n    startX = (orgWidth - width) / 2 + pad;\n  }\n  if (Math.abs(orgX - graphBox.x) < pad && titleWidth > orgWidth) {\n    startX = orgX - (titleWidth - orgWidth) / 2;\n  }\n  const lineY = 1 - getConfig().state.textHeight;\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\"y\", lineY).attr(\"class\", altBkg ? \"alt-composit\" : \"composit\").attr(\"width\", width).attr(\n    \"height\",\n    graphBox.height + getConfig().state.textHeight + getConfig().state.titleShift + 1\n  ).attr(\"rx\", \"0\");\n  title.attr(\"x\", startX + pad);\n  if (titleWidth <= orgWidth) {\n    title.attr(\"x\", orgX + (width - dblPad) / 2 - titleWidth / 2 + pad);\n  }\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\n    \"y\",\n    getConfig().state.titleShift - getConfig().state.textHeight - getConfig().state.padding\n  ).attr(\"width\", width).attr(\"height\", getConfig().state.textHeight * 3).attr(\"rx\", getConfig().state.radius);\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\n    \"y\",\n    getConfig().state.titleShift - getConfig().state.textHeight - getConfig().state.padding\n  ).attr(\"width\", width).attr(\"height\", graphBox.height + 3 + 2 * getConfig().state.textHeight).attr(\"rx\", getConfig().state.radius);\n  return g;\n}, \"addTitleAndBox\");\nvar drawEndState = /* @__PURE__ */ __name((g) => {\n  g.append(\"circle\").attr(\"class\", \"end-state-outer\").attr(\"r\", getConfig().state.sizeUnit + getConfig().state.miniPadding).attr(\n    \"cx\",\n    getConfig().state.padding + getConfig().state.sizeUnit + getConfig().state.miniPadding\n  ).attr(\n    \"cy\",\n    getConfig().state.padding + getConfig().state.sizeUnit + getConfig().state.miniPadding\n  );\n  return g.append(\"circle\").attr(\"class\", \"end-state-inner\").attr(\"r\", getConfig().state.sizeUnit).attr(\"cx\", getConfig().state.padding + getConfig().state.sizeUnit + 2).attr(\"cy\", getConfig().state.padding + getConfig().state.sizeUnit + 2);\n}, \"drawEndState\");\nvar drawForkJoinState = /* @__PURE__ */ __name((g, stateDef) => {\n  let width = getConfig().state.forkWidth;\n  let height = getConfig().state.forkHeight;\n  if (stateDef.parentId) {\n    let tmp = width;\n    width = height;\n    height = tmp;\n  }\n  return g.append(\"rect\").style(\"stroke\", \"black\").style(\"fill\", \"black\").attr(\"width\", width).attr(\"height\", height).attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding);\n}, \"drawForkJoinState\");\nvar _drawLongText = /* @__PURE__ */ __name((_text, x, y, g) => {\n  let textHeight = 0;\n  const textElem = g.append(\"text\");\n  textElem.style(\"text-anchor\", \"start\");\n  textElem.attr(\"class\", \"noteText\");\n  let text = _text.replace(/\\r\\n/g, \"<br/>\");\n  text = text.replace(/\\n/g, \"<br/>\");\n  const lines = text.split(common_default.lineBreakRegex);\n  let tHeight = 1.25 * getConfig().state.noteMargin;\n  for (const line2 of lines) {\n    const txt = line2.trim();\n    if (txt.length > 0) {\n      const span = textElem.append(\"tspan\");\n      span.text(txt);\n      if (tHeight === 0) {\n        const textBounds = span.node().getBBox();\n        tHeight += textBounds.height;\n      }\n      textHeight += tHeight;\n      span.attr(\"x\", x + getConfig().state.noteMargin);\n      span.attr(\"y\", y + textHeight + 1.25 * getConfig().state.noteMargin);\n    }\n  }\n  return { textWidth: textElem.node().getBBox().width, textHeight };\n}, \"_drawLongText\");\nvar drawNote = /* @__PURE__ */ __name((text, g) => {\n  g.attr(\"class\", \"state-note\");\n  const note = g.append(\"rect\").attr(\"x\", 0).attr(\"y\", getConfig().state.padding);\n  const rectElem = g.append(\"g\");\n  const { textWidth, textHeight } = _drawLongText(text, 0, 0, rectElem);\n  note.attr(\"height\", textHeight + 2 * getConfig().state.noteMargin);\n  note.attr(\"width\", textWidth + getConfig().state.noteMargin * 2);\n  return note;\n}, \"drawNote\");\nvar drawState = /* @__PURE__ */ __name(function(elem, stateDef) {\n  const id = stateDef.id;\n  const stateInfo = {\n    id,\n    label: stateDef.id,\n    width: 0,\n    height: 0\n  };\n  const g = elem.append(\"g\").attr(\"id\", id).attr(\"class\", \"stateGroup\");\n  if (stateDef.type === \"start\") {\n    drawStartState(g);\n  }\n  if (stateDef.type === \"end\") {\n    drawEndState(g);\n  }\n  if (stateDef.type === \"fork\" || stateDef.type === \"join\") {\n    drawForkJoinState(g, stateDef);\n  }\n  if (stateDef.type === \"note\") {\n    drawNote(stateDef.note.text, g);\n  }\n  if (stateDef.type === \"divider\") {\n    drawDivider(g);\n  }\n  if (stateDef.type === \"default\" && stateDef.descriptions.length === 0) {\n    drawSimpleState(g, stateDef);\n  }\n  if (stateDef.type === \"default\" && stateDef.descriptions.length > 0) {\n    drawDescrState(g, stateDef);\n  }\n  const stateBox = g.node().getBBox();\n  stateInfo.width = stateBox.width + 2 * getConfig().state.padding;\n  stateInfo.height = stateBox.height + 2 * getConfig().state.padding;\n  id_cache_default.set(id, stateInfo);\n  return stateInfo;\n}, \"drawState\");\nvar edgeCount = 0;\nvar drawEdge = /* @__PURE__ */ __name(function(elem, path, relation) {\n  const getRelationType = /* @__PURE__ */ __name(function(type) {\n    switch (type) {\n      case StateDB.relationType.AGGREGATION:\n        return \"aggregation\";\n      case StateDB.relationType.EXTENSION:\n        return \"extension\";\n      case StateDB.relationType.COMPOSITION:\n        return \"composition\";\n      case StateDB.relationType.DEPENDENCY:\n        return \"dependency\";\n    }\n  }, \"getRelationType\");\n  path.points = path.points.filter((p) => !Number.isNaN(p.y));\n  const lineData = path.points;\n  const lineFunction = line().x(function(d) {\n    return d.x;\n  }).y(function(d) {\n    return d.y;\n  }).curve(curveBasis);\n  const svgPath = elem.append(\"path\").attr(\"d\", lineFunction(lineData)).attr(\"id\", \"edge\" + edgeCount).attr(\"class\", \"transition\");\n  let url = \"\";\n  if (getConfig().state.arrowMarkerAbsolute) {\n    url = window.location.protocol + \"//\" + window.location.host + window.location.pathname + window.location.search;\n    url = url.replace(/\\(/g, \"\\\\(\");\n    url = url.replace(/\\)/g, \"\\\\)\");\n  }\n  svgPath.attr(\n    \"marker-end\",\n    \"url(\" + url + \"#\" + getRelationType(StateDB.relationType.DEPENDENCY) + \"End)\"\n  );\n  if (relation.title !== void 0) {\n    const label = elem.append(\"g\").attr(\"class\", \"stateLabel\");\n    const { x, y } = utils_default.calcLabelPosition(path.points);\n    const rows = common_default.getRows(relation.title);\n    let titleHeight = 0;\n    const titleRows = [];\n    let maxWidth = 0;\n    let minX = 0;\n    for (let i = 0; i <= rows.length; i++) {\n      const title = label.append(\"text\").attr(\"text-anchor\", \"middle\").text(rows[i]).attr(\"x\", x).attr(\"y\", y + titleHeight);\n      const boundsTmp = title.node().getBBox();\n      maxWidth = Math.max(maxWidth, boundsTmp.width);\n      minX = Math.min(minX, boundsTmp.x);\n      log.info(boundsTmp.x, x, y + titleHeight);\n      if (titleHeight === 0) {\n        const titleBox = title.node().getBBox();\n        titleHeight = titleBox.height;\n        log.info(\"Title height\", titleHeight, y);\n      }\n      titleRows.push(title);\n    }\n    let boxHeight = titleHeight * rows.length;\n    if (rows.length > 1) {\n      const heightAdj = (rows.length - 1) * titleHeight * 0.5;\n      titleRows.forEach((title, i) => title.attr(\"y\", y + i * titleHeight - heightAdj));\n      boxHeight = titleHeight * rows.length;\n    }\n    const bounds = label.node().getBBox();\n    label.insert(\"rect\", \":first-child\").attr(\"class\", \"box\").attr(\"x\", x - maxWidth / 2 - getConfig().state.padding / 2).attr(\"y\", y - boxHeight / 2 - getConfig().state.padding / 2 - 3.5).attr(\"width\", maxWidth + getConfig().state.padding).attr(\"height\", boxHeight + getConfig().state.padding);\n    log.info(bounds);\n  }\n  edgeCount++;\n}, \"drawEdge\");\n\n// src/diagrams/state/stateRenderer.js\nvar conf;\nvar transformationLog = {};\nvar setConf = /* @__PURE__ */ __name(function() {\n}, \"setConf\");\nvar insertMarkers = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"dependencyEnd\").attr(\"refX\", 19).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 19,7 L9,13 L14,7 L9,1 Z\");\n}, \"insertMarkers\");\nvar draw = /* @__PURE__ */ __name(function(text, id, _version, diagObj) {\n  conf = getConfig().state;\n  const securityLevel = getConfig().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const doc = securityLevel === \"sandbox\" ? sandboxElement.nodes()[0].contentDocument : document;\n  log.debug(\"Rendering diagram \" + text);\n  const diagram2 = root.select(`[id='${id}']`);\n  insertMarkers(diagram2);\n  const rootDoc = diagObj.db.getRootDoc();\n  renderDoc(rootDoc, diagram2, void 0, false, root, doc, diagObj);\n  const padding = conf.padding;\n  const bounds = diagram2.node().getBBox();\n  const width = bounds.width + padding * 2;\n  const height = bounds.height + padding * 2;\n  const svgWidth = width * 1.75;\n  configureSvgSize(diagram2, height, svgWidth, conf.useMaxWidth);\n  diagram2.attr(\n    \"viewBox\",\n    `${bounds.x - conf.padding}  ${bounds.y - conf.padding} ` + width + \" \" + height\n  );\n}, \"draw\");\nvar getLabelWidth = /* @__PURE__ */ __name((text) => {\n  return text ? text.length * conf.fontSizeFactor : 1;\n}, \"getLabelWidth\");\nvar renderDoc = /* @__PURE__ */ __name((doc, diagram2, parentId, altBkg, root, domDocument, diagObj) => {\n  const graph = new graphlib.Graph({\n    compound: true,\n    multigraph: true\n  });\n  let i;\n  let edgeFreeDoc = true;\n  for (i = 0; i < doc.length; i++) {\n    if (doc[i].stmt === \"relation\") {\n      edgeFreeDoc = false;\n      break;\n    }\n  }\n  if (parentId) {\n    graph.setGraph({\n      rankdir: \"LR\",\n      multigraph: true,\n      compound: true,\n      // acyclicer: 'greedy',\n      ranker: \"tight-tree\",\n      ranksep: edgeFreeDoc ? 1 : conf.edgeLengthFactor,\n      nodeSep: edgeFreeDoc ? 1 : 50,\n      isMultiGraph: true\n      // ranksep: 5,\n      // nodesep: 1\n    });\n  } else {\n    graph.setGraph({\n      rankdir: \"TB\",\n      multigraph: true,\n      compound: true,\n      // isCompound: true,\n      // acyclicer: 'greedy',\n      // ranker: 'longest-path'\n      ranksep: edgeFreeDoc ? 1 : conf.edgeLengthFactor,\n      nodeSep: edgeFreeDoc ? 1 : 50,\n      ranker: \"tight-tree\",\n      // ranker: 'network-simplex'\n      isMultiGraph: true\n    });\n  }\n  graph.setDefaultEdgeLabel(function() {\n    return {};\n  });\n  const states = diagObj.db.getStates();\n  const relations = diagObj.db.getRelations();\n  const keys2 = Object.keys(states);\n  let first = true;\n  for (const key of keys2) {\n    const stateDef = states[key];\n    if (parentId) {\n      stateDef.parentId = parentId;\n    }\n    let node;\n    if (stateDef.doc) {\n      let sub = diagram2.append(\"g\").attr(\"id\", stateDef.id).attr(\"class\", \"stateGroup\");\n      node = renderDoc(stateDef.doc, sub, stateDef.id, !altBkg, root, domDocument, diagObj);\n      if (first) {\n        sub = addTitleAndBox(sub, stateDef, altBkg);\n        let boxBounds = sub.node().getBBox();\n        node.width = boxBounds.width;\n        node.height = boxBounds.height + conf.padding / 2;\n        transformationLog[stateDef.id] = { y: conf.compositTitleSize };\n      } else {\n        let boxBounds = sub.node().getBBox();\n        node.width = boxBounds.width;\n        node.height = boxBounds.height;\n      }\n    } else {\n      node = drawState(diagram2, stateDef, graph);\n    }\n    if (stateDef.note) {\n      const noteDef = {\n        descriptions: [],\n        id: stateDef.id + \"-note\",\n        note: stateDef.note,\n        type: \"note\"\n      };\n      const note = drawState(diagram2, noteDef, graph);\n      if (stateDef.note.position === \"left of\") {\n        graph.setNode(node.id + \"-note\", note);\n        graph.setNode(node.id, node);\n      } else {\n        graph.setNode(node.id, node);\n        graph.setNode(node.id + \"-note\", note);\n      }\n      graph.setParent(node.id, node.id + \"-group\");\n      graph.setParent(node.id + \"-note\", node.id + \"-group\");\n    } else {\n      graph.setNode(node.id, node);\n    }\n  }\n  log.debug(\"Count=\", graph.nodeCount(), graph);\n  let cnt = 0;\n  relations.forEach(function(relation) {\n    cnt++;\n    log.debug(\"Setting edge\", relation);\n    graph.setEdge(\n      relation.id1,\n      relation.id2,\n      {\n        relation,\n        width: getLabelWidth(relation.title),\n        height: conf.labelHeight * common_default.getRows(relation.title).length,\n        labelpos: \"c\"\n      },\n      \"id\" + cnt\n    );\n  });\n  dagreLayout(graph);\n  log.debug(\"Graph after layout\", graph.nodes());\n  const svgElem = diagram2.node();\n  graph.nodes().forEach(function(v) {\n    if (v !== void 0 && graph.node(v) !== void 0) {\n      log.warn(\"Node \" + v + \": \" + JSON.stringify(graph.node(v)));\n      root.select(\"#\" + svgElem.id + \" #\" + v).attr(\n        \"transform\",\n        \"translate(\" + (graph.node(v).x - graph.node(v).width / 2) + \",\" + (graph.node(v).y + (transformationLog[v] ? transformationLog[v].y : 0) - graph.node(v).height / 2) + \" )\"\n      );\n      root.select(\"#\" + svgElem.id + \" #\" + v).attr(\"data-x-shift\", graph.node(v).x - graph.node(v).width / 2);\n      const dividers = domDocument.querySelectorAll(\"#\" + svgElem.id + \" #\" + v + \" .divider\");\n      dividers.forEach((divider) => {\n        const parent = divider.parentElement;\n        let pWidth = 0;\n        let pShift = 0;\n        if (parent) {\n          if (parent.parentElement) {\n            pWidth = parent.parentElement.getBBox().width;\n          }\n          pShift = parseInt(parent.getAttribute(\"data-x-shift\"), 10);\n          if (Number.isNaN(pShift)) {\n            pShift = 0;\n          }\n        }\n        divider.setAttribute(\"x1\", 0 - pShift + 8);\n        divider.setAttribute(\"x2\", pWidth - pShift - 8);\n      });\n    } else {\n      log.debug(\"No Node \" + v + \": \" + JSON.stringify(graph.node(v)));\n    }\n  });\n  let stateBox = svgElem.getBBox();\n  graph.edges().forEach(function(e) {\n    if (e !== void 0 && graph.edge(e) !== void 0) {\n      log.debug(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(graph.edge(e)));\n      drawEdge(diagram2, graph.edge(e), graph.edge(e).relation);\n    }\n  });\n  stateBox = svgElem.getBBox();\n  const stateInfo = {\n    id: parentId ? parentId : \"root\",\n    label: parentId ? parentId : \"root\",\n    width: 0,\n    height: 0\n  };\n  stateInfo.width = stateBox.width + 2 * conf.padding;\n  stateInfo.height = stateBox.height + 2 * conf.padding;\n  log.debug(\"Doc rendered\", stateInfo, graph);\n  return stateInfo;\n}, \"renderDoc\");\nvar stateRenderer_default = {\n  setConf,\n  draw\n};\n\n// src/diagrams/state/stateDiagram.ts\nvar diagram = {\n  parser: stateDiagram_default,\n  get db() {\n    return new StateDB(1);\n  },\n  renderer: stateRenderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name((cnf) => {\n    if (!cnf.state) {\n      cnf.state = {};\n    }\n    cnf.state.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,IAAI,UAAU,CAAC;AACf,IAAI,MAAsB,OAAO,CAAC,KAAK,QAAQ;AAC7C,UAAQ,GAAG,IAAI;AACjB,GAAG,KAAK;AACR,IAAI,MAAsB,OAAO,CAAC,MAAM,QAAQ,CAAC,GAAG,KAAK;AACzD,IAAI,OAAuB,OAAO,MAAM,OAAO,KAAK,OAAO,GAAG,MAAM;AACpE,IAAI,OAAuB,OAAO,MAAM,KAAK,EAAE,QAAQ,MAAM;AAC7D,IAAI,mBAAmB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGA,IAAI,iBAAiC,OAAO,CAAC,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,SAAS,aAAa,EAAE,KAAK,KAAK,WAAU,EAAE,MAAM,QAAQ,EAAE,KAAK,MAAM,WAAU,EAAE,MAAM,UAAU,WAAU,EAAE,MAAM,QAAQ,EAAE,KAAK,MAAM,WAAU,EAAE,MAAM,UAAU,WAAU,EAAE,MAAM,QAAQ,GAAG,gBAAgB;AAChS,IAAI,cAA8B,OAAO,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE,MAAM,UAAU,MAAM,EAAE,MAAM,oBAAoB,GAAG,EAAE,KAAK,MAAM,WAAU,EAAE,MAAM,UAAU,EAAE,KAAK,SAAS,SAAS,EAAE,KAAK,MAAM,WAAU,EAAE,MAAM,aAAa,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,GAAG,aAAa;AACjR,IAAI,kBAAkC,OAAO,CAAC,GAAG,aAAa;AAC5D,QAAM,QAAQ,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,IAAI,WAAU,EAAE,MAAM,OAAO,EAAE,KAAK,KAAK,WAAU,EAAE,MAAM,aAAa,IAAI,WAAU,EAAE,MAAM,OAAO,EAAE,KAAK,aAAa,WAAU,EAAE,MAAM,QAAQ,EAAE,KAAK,SAAS,aAAa,EAAE,KAAK,SAAS,EAAE;AAC3O,QAAM,WAAW,MAAM,KAAK,EAAE,QAAQ;AACtC,IAAE,OAAO,QAAQ,cAAc,EAAE,KAAK,KAAK,WAAU,EAAE,MAAM,OAAO,EAAE,KAAK,KAAK,WAAU,EAAE,MAAM,OAAO,EAAE,KAAK,SAAS,SAAS,QAAQ,IAAI,WAAU,EAAE,MAAM,OAAO,EAAE,KAAK,UAAU,SAAS,SAAS,IAAI,WAAU,EAAE,MAAM,OAAO,EAAE,KAAK,MAAM,WAAU,EAAE,MAAM,MAAM;AAC5Q,SAAO;AACT,GAAG,iBAAiB;AACpB,IAAI,iBAAiC,OAAO,CAAC,GAAG,aAAa;AAC3D,QAAM,WAA2B,OAAO,SAAS,QAAQ,KAAK,UAAU;AACtE,UAAM,QAAQ,OAAO,OAAO,OAAO,EAAE,KAAK,KAAK,IAAI,WAAU,EAAE,MAAM,OAAO,EAAE,KAAK,GAAG;AACtF,QAAI,CAAC,UAAU;AACb,YAAM,KAAK,MAAM,WAAU,EAAE,MAAM,UAAU;AAAA,IAC/C;AAAA,EACF,GAAG,UAAU;AACb,QAAM,QAAQ,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,IAAI,WAAU,EAAE,MAAM,OAAO,EAAE,KAAK,KAAK,WAAU,EAAE,MAAM,aAAa,MAAM,WAAU,EAAE,MAAM,OAAO,EAAE,KAAK,aAAa,WAAU,EAAE,MAAM,QAAQ,EAAE,KAAK,SAAS,aAAa,EAAE,KAAK,SAAS,aAAa,CAAC,CAAC;AAC1P,QAAM,WAAW,MAAM,KAAK,EAAE,QAAQ;AACtC,QAAM,cAAc,SAAS;AAC7B,QAAM,cAAc,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,WAAU,EAAE,MAAM,OAAO,EAAE;AAAA,IACxE;AAAA,IACA,cAAc,WAAU,EAAE,MAAM,UAAU,MAAM,WAAU,EAAE,MAAM,gBAAgB,WAAU,EAAE,MAAM;AAAA,EACtG,EAAE,KAAK,SAAS,mBAAmB;AACnC,MAAI,UAAU;AACd,MAAI,WAAW;AACf,WAAS,aAAa,QAAQ,SAAS,OAAO;AAC5C,QAAI,CAAC,SAAS;AACZ,eAAS,aAAa,OAAO,QAAQ;AACrC,iBAAW;AAAA,IACb;AACA,cAAU;AAAA,EACZ,CAAC;AACD,QAAM,YAAY,EAAE,OAAO,MAAM,EAAE,KAAK,MAAM,WAAU,EAAE,MAAM,OAAO,EAAE,KAAK,MAAM,WAAU,EAAE,MAAM,UAAU,cAAc,WAAU,EAAE,MAAM,gBAAgB,CAAC,EAAE,KAAK,MAAM,WAAU,EAAE,MAAM,UAAU,cAAc,WAAU,EAAE,MAAM,gBAAgB,CAAC,EAAE,KAAK,SAAS,eAAe;AAC1R,QAAM,WAAW,YAAY,KAAK,EAAE,QAAQ;AAC5C,QAAM,QAAQ,KAAK,IAAI,SAAS,OAAO,SAAS,KAAK;AACrD,YAAU,KAAK,MAAM,QAAQ,IAAI,WAAU,EAAE,MAAM,OAAO;AAC1D,IAAE,OAAO,QAAQ,cAAc,EAAE,KAAK,KAAK,WAAU,EAAE,MAAM,OAAO,EAAE,KAAK,KAAK,WAAU,EAAE,MAAM,OAAO,EAAE,KAAK,SAAS,QAAQ,IAAI,WAAU,EAAE,MAAM,OAAO,EAAE,KAAK,UAAU,SAAS,SAAS,cAAc,IAAI,WAAU,EAAE,MAAM,OAAO,EAAE,KAAK,MAAM,WAAU,EAAE,MAAM,MAAM;AACjR,SAAO;AACT,GAAG,gBAAgB;AACnB,IAAI,iBAAiC,OAAO,CAAC,GAAG,UAAU,WAAW;AACnE,QAAM,MAAM,WAAU,EAAE,MAAM;AAC9B,QAAM,SAAS,IAAI,WAAU,EAAE,MAAM;AACrC,QAAM,SAAS,EAAE,KAAK,EAAE,QAAQ;AAChC,QAAM,WAAW,OAAO;AACxB,QAAM,OAAO,OAAO;AACpB,QAAM,QAAQ,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,WAAU,EAAE,MAAM,UAAU,EAAE,KAAK,aAAa,WAAU,EAAE,MAAM,QAAQ,EAAE,KAAK,SAAS,aAAa,EAAE,KAAK,SAAS,EAAE;AAC/K,QAAM,WAAW,MAAM,KAAK,EAAE,QAAQ;AACtC,QAAM,aAAa,SAAS,QAAQ;AACpC,MAAI,QAAQ,KAAK,IAAI,YAAY,QAAQ;AACzC,MAAI,UAAU,UAAU;AACtB,YAAQ,QAAQ;AAAA,EAClB;AACA,MAAI;AACJ,QAAM,WAAW,EAAE,KAAK,EAAE,QAAQ;AAClC,MAAI,SAAS,KAAK;AAAA,EAClB;AACA,WAAS,OAAO;AAChB,MAAI,aAAa,UAAU;AACzB,cAAU,WAAW,SAAS,IAAI;AAAA,EACpC;AACA,MAAI,KAAK,IAAI,OAAO,SAAS,CAAC,IAAI,OAAO,aAAa,UAAU;AAC9D,aAAS,QAAQ,aAAa,YAAY;AAAA,EAC5C;AACA,QAAM,QAAQ,IAAI,WAAU,EAAE,MAAM;AACpC,IAAE,OAAO,QAAQ,cAAc,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,KAAK,EAAE,KAAK,SAAS,SAAS,iBAAiB,UAAU,EAAE,KAAK,SAAS,KAAK,EAAE;AAAA,IAC3I;AAAA,IACA,SAAS,SAAS,WAAU,EAAE,MAAM,aAAa,WAAU,EAAE,MAAM,aAAa;AAAA,EAClF,EAAE,KAAK,MAAM,GAAG;AAChB,QAAM,KAAK,KAAK,SAAS,GAAG;AAC5B,MAAI,cAAc,UAAU;AAC1B,UAAM,KAAK,KAAK,QAAQ,QAAQ,UAAU,IAAI,aAAa,IAAI,GAAG;AAAA,EACpE;AACA,IAAE,OAAO,QAAQ,cAAc,EAAE,KAAK,KAAK,MAAM,EAAE;AAAA,IACjD;AAAA,IACA,WAAU,EAAE,MAAM,aAAa,WAAU,EAAE,MAAM,aAAa,WAAU,EAAE,MAAM;AAAA,EAClF,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,WAAU,EAAE,MAAM,aAAa,CAAC,EAAE,KAAK,MAAM,WAAU,EAAE,MAAM,MAAM;AAC3G,IAAE,OAAO,QAAQ,cAAc,EAAE,KAAK,KAAK,MAAM,EAAE;AAAA,IACjD;AAAA,IACA,WAAU,EAAE,MAAM,aAAa,WAAU,EAAE,MAAM,aAAa,WAAU,EAAE,MAAM;AAAA,EAClF,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,SAAS,SAAS,IAAI,IAAI,WAAU,EAAE,MAAM,UAAU,EAAE,KAAK,MAAM,WAAU,EAAE,MAAM,MAAM;AACjI,SAAO;AACT,GAAG,gBAAgB;AACnB,IAAI,eAA+B,OAAO,CAAC,MAAM;AAC/C,IAAE,OAAO,QAAQ,EAAE,KAAK,SAAS,iBAAiB,EAAE,KAAK,KAAK,WAAU,EAAE,MAAM,WAAW,WAAU,EAAE,MAAM,WAAW,EAAE;AAAA,IACxH;AAAA,IACA,WAAU,EAAE,MAAM,UAAU,WAAU,EAAE,MAAM,WAAW,WAAU,EAAE,MAAM;AAAA,EAC7E,EAAE;AAAA,IACA;AAAA,IACA,WAAU,EAAE,MAAM,UAAU,WAAU,EAAE,MAAM,WAAW,WAAU,EAAE,MAAM;AAAA,EAC7E;AACA,SAAO,EAAE,OAAO,QAAQ,EAAE,KAAK,SAAS,iBAAiB,EAAE,KAAK,KAAK,WAAU,EAAE,MAAM,QAAQ,EAAE,KAAK,MAAM,WAAU,EAAE,MAAM,UAAU,WAAU,EAAE,MAAM,WAAW,CAAC,EAAE,KAAK,MAAM,WAAU,EAAE,MAAM,UAAU,WAAU,EAAE,MAAM,WAAW,CAAC;AAC/O,GAAG,cAAc;AACjB,IAAI,oBAAoC,OAAO,CAAC,GAAG,aAAa;AAC9D,MAAI,QAAQ,WAAU,EAAE,MAAM;AAC9B,MAAI,SAAS,WAAU,EAAE,MAAM;AAC/B,MAAI,SAAS,UAAU;AACrB,QAAI,MAAM;AACV,YAAQ;AACR,aAAS;AAAA,EACX;AACA,SAAO,EAAE,OAAO,MAAM,EAAE,MAAM,UAAU,OAAO,EAAE,MAAM,QAAQ,OAAO,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,KAAK,WAAU,EAAE,MAAM,OAAO,EAAE,KAAK,KAAK,WAAU,EAAE,MAAM,OAAO;AAC9L,GAAG,mBAAmB;AACtB,IAAI,gBAAgC,OAAO,CAAC,OAAO,GAAG,GAAG,MAAM;AAC7D,MAAI,aAAa;AACjB,QAAM,WAAW,EAAE,OAAO,MAAM;AAChC,WAAS,MAAM,eAAe,OAAO;AACrC,WAAS,KAAK,SAAS,UAAU;AACjC,MAAI,OAAO,MAAM,QAAQ,SAAS,OAAO;AACzC,SAAO,KAAK,QAAQ,OAAO,OAAO;AAClC,QAAM,QAAQ,KAAK,MAAM,eAAe,cAAc;AACtD,MAAI,UAAU,OAAO,WAAU,EAAE,MAAM;AACvC,aAAW,SAAS,OAAO;AACzB,UAAM,MAAM,MAAM,KAAK;AACvB,QAAI,IAAI,SAAS,GAAG;AAClB,YAAM,OAAO,SAAS,OAAO,OAAO;AACpC,WAAK,KAAK,GAAG;AACb,UAAI,YAAY,GAAG;AACjB,cAAM,aAAa,KAAK,KAAK,EAAE,QAAQ;AACvC,mBAAW,WAAW;AAAA,MACxB;AACA,oBAAc;AACd,WAAK,KAAK,KAAK,IAAI,WAAU,EAAE,MAAM,UAAU;AAC/C,WAAK,KAAK,KAAK,IAAI,aAAa,OAAO,WAAU,EAAE,MAAM,UAAU;AAAA,IACrE;AAAA,EACF;AACA,SAAO,EAAE,WAAW,SAAS,KAAK,EAAE,QAAQ,EAAE,OAAO,WAAW;AAClE,GAAG,eAAe;AAClB,IAAI,WAA2B,OAAO,CAAC,MAAM,MAAM;AACjD,IAAE,KAAK,SAAS,YAAY;AAC5B,QAAM,OAAO,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,WAAU,EAAE,MAAM,OAAO;AAC9E,QAAM,WAAW,EAAE,OAAO,GAAG;AAC7B,QAAM,EAAE,WAAW,WAAW,IAAI,cAAc,MAAM,GAAG,GAAG,QAAQ;AACpE,OAAK,KAAK,UAAU,aAAa,IAAI,WAAU,EAAE,MAAM,UAAU;AACjE,OAAK,KAAK,SAAS,YAAY,WAAU,EAAE,MAAM,aAAa,CAAC;AAC/D,SAAO;AACT,GAAG,UAAU;AACb,IAAI,YAA4B,OAAO,SAAS,MAAM,UAAU;AAC9D,QAAM,KAAK,SAAS;AACpB,QAAM,YAAY;AAAA,IAChB;AAAA,IACA,OAAO,SAAS;AAAA,IAChB,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACA,QAAM,IAAI,KAAK,OAAO,GAAG,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,SAAS,YAAY;AACpE,MAAI,SAAS,SAAS,SAAS;AAC7B,mBAAe,CAAC;AAAA,EAClB;AACA,MAAI,SAAS,SAAS,OAAO;AAC3B,iBAAa,CAAC;AAAA,EAChB;AACA,MAAI,SAAS,SAAS,UAAU,SAAS,SAAS,QAAQ;AACxD,sBAAkB,GAAG,QAAQ;AAAA,EAC/B;AACA,MAAI,SAAS,SAAS,QAAQ;AAC5B,aAAS,SAAS,KAAK,MAAM,CAAC;AAAA,EAChC;AACA,MAAI,SAAS,SAAS,WAAW;AAC/B,gBAAY,CAAC;AAAA,EACf;AACA,MAAI,SAAS,SAAS,aAAa,SAAS,aAAa,WAAW,GAAG;AACrE,oBAAgB,GAAG,QAAQ;AAAA,EAC7B;AACA,MAAI,SAAS,SAAS,aAAa,SAAS,aAAa,SAAS,GAAG;AACnE,mBAAe,GAAG,QAAQ;AAAA,EAC5B;AACA,QAAM,WAAW,EAAE,KAAK,EAAE,QAAQ;AAClC,YAAU,QAAQ,SAAS,QAAQ,IAAI,WAAU,EAAE,MAAM;AACzD,YAAU,SAAS,SAAS,SAAS,IAAI,WAAU,EAAE,MAAM;AAC3D,mBAAiB,IAAI,IAAI,SAAS;AAClC,SAAO;AACT,GAAG,WAAW;AACd,IAAI,YAAY;AAChB,IAAI,WAA2B,OAAO,SAAS,MAAM,MAAM,UAAU;AACnE,QAAM,kBAAkC,OAAO,SAAS,MAAM;AAC5D,YAAQ,MAAM;AAAA,MACZ,KAAK,QAAQ,aAAa;AACxB,eAAO;AAAA,MACT,KAAK,QAAQ,aAAa;AACxB,eAAO;AAAA,MACT,KAAK,QAAQ,aAAa;AACxB,eAAO;AAAA,MACT,KAAK,QAAQ,aAAa;AACxB,eAAO;AAAA,IACX;AAAA,EACF,GAAG,iBAAiB;AACpB,OAAK,SAAS,KAAK,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,MAAM,EAAE,CAAC,CAAC;AAC1D,QAAM,WAAW,KAAK;AACtB,QAAM,eAAe,aAAK,EAAE,EAAE,SAAS,GAAG;AACxC,WAAO,EAAE;AAAA,EACX,CAAC,EAAE,EAAE,SAAS,GAAG;AACf,WAAO,EAAE;AAAA,EACX,CAAC,EAAE,MAAM,aAAU;AACnB,QAAM,UAAU,KAAK,OAAO,MAAM,EAAE,KAAK,KAAK,aAAa,QAAQ,CAAC,EAAE,KAAK,MAAM,SAAS,SAAS,EAAE,KAAK,SAAS,YAAY;AAC/H,MAAI,MAAM;AACV,MAAI,WAAU,EAAE,MAAM,qBAAqB;AACzC,UAAM,OAAO,SAAS,WAAW,OAAO,OAAO,SAAS,OAAO,OAAO,SAAS,WAAW,OAAO,SAAS;AAC1G,UAAM,IAAI,QAAQ,OAAO,KAAK;AAC9B,UAAM,IAAI,QAAQ,OAAO,KAAK;AAAA,EAChC;AACA,UAAQ;AAAA,IACN;AAAA,IACA,SAAS,MAAM,MAAM,gBAAgB,QAAQ,aAAa,UAAU,IAAI;AAAA,EAC1E;AACA,MAAI,SAAS,UAAU,QAAQ;AAC7B,UAAM,QAAQ,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,YAAY;AACzD,UAAM,EAAE,GAAG,EAAE,IAAI,cAAc,kBAAkB,KAAK,MAAM;AAC5D,UAAM,OAAO,eAAe,QAAQ,SAAS,KAAK;AAClD,QAAI,cAAc;AAClB,UAAM,YAAY,CAAC;AACnB,QAAI,WAAW;AACf,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,KAAK;AACrC,YAAM,QAAQ,MAAM,OAAO,MAAM,EAAE,KAAK,eAAe,QAAQ,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,IAAI,WAAW;AACrH,YAAM,YAAY,MAAM,KAAK,EAAE,QAAQ;AACvC,iBAAW,KAAK,IAAI,UAAU,UAAU,KAAK;AAC7C,aAAO,KAAK,IAAI,MAAM,UAAU,CAAC;AACjC,UAAI,KAAK,UAAU,GAAG,GAAG,IAAI,WAAW;AACxC,UAAI,gBAAgB,GAAG;AACrB,cAAM,WAAW,MAAM,KAAK,EAAE,QAAQ;AACtC,sBAAc,SAAS;AACvB,YAAI,KAAK,gBAAgB,aAAa,CAAC;AAAA,MACzC;AACA,gBAAU,KAAK,KAAK;AAAA,IACtB;AACA,QAAI,YAAY,cAAc,KAAK;AACnC,QAAI,KAAK,SAAS,GAAG;AACnB,YAAM,aAAa,KAAK,SAAS,KAAK,cAAc;AACpD,gBAAU,QAAQ,CAAC,OAAO,MAAM,MAAM,KAAK,KAAK,IAAI,IAAI,cAAc,SAAS,CAAC;AAChF,kBAAY,cAAc,KAAK;AAAA,IACjC;AACA,UAAM,SAAS,MAAM,KAAK,EAAE,QAAQ;AACpC,UAAM,OAAO,QAAQ,cAAc,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,KAAK,IAAI,WAAW,IAAI,WAAU,EAAE,MAAM,UAAU,CAAC,EAAE,KAAK,KAAK,IAAI,YAAY,IAAI,WAAU,EAAE,MAAM,UAAU,IAAI,GAAG,EAAE,KAAK,SAAS,WAAW,WAAU,EAAE,MAAM,OAAO,EAAE,KAAK,UAAU,YAAY,WAAU,EAAE,MAAM,OAAO;AACjS,QAAI,KAAK,MAAM;AAAA,EACjB;AACA;AACF,GAAG,UAAU;AAGb,IAAI;AACJ,IAAI,oBAAoB,CAAC;AACzB,IAAI,UAA0B,OAAO,WAAW;AAChD,GAAG,SAAS;AACZ,IAAI,gBAAgC,OAAO,SAAS,MAAM;AACxD,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,eAAe,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,2BAA2B;AAChO,GAAG,eAAe;AAClB,IAAI,OAAuB,OAAO,SAAS,MAAM,IAAI,UAAU,SAAS;AACtE,SAAO,WAAU,EAAE;AACnB,QAAM,gBAAgB,WAAU,EAAE;AAClC,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,eAAO,OAAO,EAAE;AAAA,EACnC;AACA,QAAM,OAAO,kBAAkB,YAAY,eAAO,eAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,IAAI,eAAO,MAAM;AACjH,QAAM,MAAM,kBAAkB,YAAY,eAAe,MAAM,EAAE,CAAC,EAAE,kBAAkB;AACtF,MAAI,MAAM,uBAAuB,IAAI;AACrC,QAAM,WAAW,KAAK,OAAO,QAAQ,EAAE,IAAI;AAC3C,gBAAc,QAAQ;AACtB,QAAM,UAAU,QAAQ,GAAG,WAAW;AACtC,YAAU,SAAS,UAAU,QAAQ,OAAO,MAAM,KAAK,OAAO;AAC9D,QAAM,UAAU,KAAK;AACrB,QAAM,SAAS,SAAS,KAAK,EAAE,QAAQ;AACvC,QAAM,QAAQ,OAAO,QAAQ,UAAU;AACvC,QAAM,SAAS,OAAO,SAAS,UAAU;AACzC,QAAM,WAAW,QAAQ;AACzB,mBAAiB,UAAU,QAAQ,UAAU,KAAK,WAAW;AAC7D,WAAS;AAAA,IACP;AAAA,IACA,GAAG,OAAO,IAAI,KAAK,OAAO,KAAK,OAAO,IAAI,KAAK,OAAO,MAAM,QAAQ,MAAM;AAAA,EAC5E;AACF,GAAG,MAAM;AACT,IAAI,gBAAgC,OAAO,CAAC,SAAS;AACnD,SAAO,OAAO,KAAK,SAAS,KAAK,iBAAiB;AACpD,GAAG,eAAe;AAClB,IAAI,YAA4B,OAAO,CAAC,KAAK,UAAU,UAAU,QAAQ,MAAM,aAAa,YAAY;AACtG,QAAM,QAAQ,IAAa,MAAM;AAAA,IAC/B,UAAU;AAAA,IACV,YAAY;AAAA,EACd,CAAC;AACD,MAAI;AACJ,MAAI,cAAc;AAClB,OAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAC/B,QAAI,IAAI,CAAC,EAAE,SAAS,YAAY;AAC9B,oBAAc;AACd;AAAA,IACF;AAAA,EACF;AACA,MAAI,UAAU;AACZ,UAAM,SAAS;AAAA,MACb,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA,MAEV,QAAQ;AAAA,MACR,SAAS,cAAc,IAAI,KAAK;AAAA,MAChC,SAAS,cAAc,IAAI;AAAA,MAC3B,cAAc;AAAA;AAAA;AAAA,IAGhB,CAAC;AAAA,EACH,OAAO;AACL,UAAM,SAAS;AAAA,MACb,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA,MAIV,SAAS,cAAc,IAAI,KAAK;AAAA,MAChC,SAAS,cAAc,IAAI;AAAA,MAC3B,QAAQ;AAAA;AAAA,MAER,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AACA,QAAM,oBAAoB,WAAW;AACnC,WAAO,CAAC;AAAA,EACV,CAAC;AACD,QAAM,SAAS,QAAQ,GAAG,UAAU;AACpC,QAAM,YAAY,QAAQ,GAAG,aAAa;AAC1C,QAAM,QAAQ,OAAO,KAAK,MAAM;AAChC,MAAI,QAAQ;AACZ,aAAW,OAAO,OAAO;AACvB,UAAM,WAAW,OAAO,GAAG;AAC3B,QAAI,UAAU;AACZ,eAAS,WAAW;AAAA,IACtB;AACA,QAAI;AACJ,QAAI,SAAS,KAAK;AAChB,UAAI,MAAM,SAAS,OAAO,GAAG,EAAE,KAAK,MAAM,SAAS,EAAE,EAAE,KAAK,SAAS,YAAY;AACjF,aAAO,UAAU,SAAS,KAAK,KAAK,SAAS,IAAI,CAAC,QAAQ,MAAM,aAAa,OAAO;AACpF,UAAI,OAAO;AACT,cAAM,eAAe,KAAK,UAAU,MAAM;AAC1C,YAAI,YAAY,IAAI,KAAK,EAAE,QAAQ;AACnC,aAAK,QAAQ,UAAU;AACvB,aAAK,SAAS,UAAU,SAAS,KAAK,UAAU;AAChD,0BAAkB,SAAS,EAAE,IAAI,EAAE,GAAG,KAAK,kBAAkB;AAAA,MAC/D,OAAO;AACL,YAAI,YAAY,IAAI,KAAK,EAAE,QAAQ;AACnC,aAAK,QAAQ,UAAU;AACvB,aAAK,SAAS,UAAU;AAAA,MAC1B;AAAA,IACF,OAAO;AACL,aAAO,UAAU,UAAU,UAAU,KAAK;AAAA,IAC5C;AACA,QAAI,SAAS,MAAM;AACjB,YAAM,UAAU;AAAA,QACd,cAAc,CAAC;AAAA,QACf,IAAI,SAAS,KAAK;AAAA,QAClB,MAAM,SAAS;AAAA,QACf,MAAM;AAAA,MACR;AACA,YAAM,OAAO,UAAU,UAAU,SAAS,KAAK;AAC/C,UAAI,SAAS,KAAK,aAAa,WAAW;AACxC,cAAM,QAAQ,KAAK,KAAK,SAAS,IAAI;AACrC,cAAM,QAAQ,KAAK,IAAI,IAAI;AAAA,MAC7B,OAAO;AACL,cAAM,QAAQ,KAAK,IAAI,IAAI;AAC3B,cAAM,QAAQ,KAAK,KAAK,SAAS,IAAI;AAAA,MACvC;AACA,YAAM,UAAU,KAAK,IAAI,KAAK,KAAK,QAAQ;AAC3C,YAAM,UAAU,KAAK,KAAK,SAAS,KAAK,KAAK,QAAQ;AAAA,IACvD,OAAO;AACL,YAAM,QAAQ,KAAK,IAAI,IAAI;AAAA,IAC7B;AAAA,EACF;AACA,MAAI,MAAM,UAAU,MAAM,UAAU,GAAG,KAAK;AAC5C,MAAI,MAAM;AACV,YAAU,QAAQ,SAAS,UAAU;AACnC;AACA,QAAI,MAAM,gBAAgB,QAAQ;AAClC,UAAM;AAAA,MACJ,SAAS;AAAA,MACT,SAAS;AAAA,MACT;AAAA,QACE;AAAA,QACA,OAAO,cAAc,SAAS,KAAK;AAAA,QACnC,QAAQ,KAAK,cAAc,eAAe,QAAQ,SAAS,KAAK,EAAE;AAAA,QAClE,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,SAAY,KAAK;AACjB,MAAI,MAAM,sBAAsB,MAAM,MAAM,CAAC;AAC7C,QAAM,UAAU,SAAS,KAAK;AAC9B,QAAM,MAAM,EAAE,QAAQ,SAAS,GAAG;AAChC,QAAI,MAAM,UAAU,MAAM,KAAK,CAAC,MAAM,QAAQ;AAC5C,UAAI,KAAK,UAAU,IAAI,OAAO,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC,CAAC;AAC3D,WAAK,OAAO,MAAM,QAAQ,KAAK,OAAO,CAAC,EAAE;AAAA,QACvC;AAAA,QACA,gBAAgB,MAAM,KAAK,CAAC,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE,QAAQ,KAAK,OAAO,MAAM,KAAK,CAAC,EAAE,KAAK,kBAAkB,CAAC,IAAI,kBAAkB,CAAC,EAAE,IAAI,KAAK,MAAM,KAAK,CAAC,EAAE,SAAS,KAAK;AAAA,MAC1K;AACA,WAAK,OAAO,MAAM,QAAQ,KAAK,OAAO,CAAC,EAAE,KAAK,gBAAgB,MAAM,KAAK,CAAC,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE,QAAQ,CAAC;AACvG,YAAM,WAAW,YAAY,iBAAiB,MAAM,QAAQ,KAAK,OAAO,IAAI,WAAW;AACvF,eAAS,QAAQ,CAAC,YAAY;AAC5B,cAAM,SAAS,QAAQ;AACvB,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,QAAQ;AACV,cAAI,OAAO,eAAe;AACxB,qBAAS,OAAO,cAAc,QAAQ,EAAE;AAAA,UAC1C;AACA,mBAAS,SAAS,OAAO,aAAa,cAAc,GAAG,EAAE;AACzD,cAAI,OAAO,MAAM,MAAM,GAAG;AACxB,qBAAS;AAAA,UACX;AAAA,QACF;AACA,gBAAQ,aAAa,MAAM,IAAI,SAAS,CAAC;AACzC,gBAAQ,aAAa,MAAM,SAAS,SAAS,CAAC;AAAA,MAChD,CAAC;AAAA,IACH,OAAO;AACL,UAAI,MAAM,aAAa,IAAI,OAAO,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC,CAAC;AAAA,IACjE;AAAA,EACF,CAAC;AACD,MAAI,WAAW,QAAQ,QAAQ;AAC/B,QAAM,MAAM,EAAE,QAAQ,SAAS,GAAG;AAChC,QAAI,MAAM,UAAU,MAAM,KAAK,CAAC,MAAM,QAAQ;AAC5C,UAAI,MAAM,UAAU,EAAE,IAAI,SAAS,EAAE,IAAI,OAAO,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC,CAAC;AAC7E,eAAS,UAAU,MAAM,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC,EAAE,QAAQ;AAAA,IAC1D;AAAA,EACF,CAAC;AACD,aAAW,QAAQ,QAAQ;AAC3B,QAAM,YAAY;AAAA,IAChB,IAAI,WAAW,WAAW;AAAA,IAC1B,OAAO,WAAW,WAAW;AAAA,IAC7B,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACA,YAAU,QAAQ,SAAS,QAAQ,IAAI,KAAK;AAC5C,YAAU,SAAS,SAAS,SAAS,IAAI,KAAK;AAC9C,MAAI,MAAM,gBAAgB,WAAW,KAAK;AAC1C,SAAO;AACT,GAAG,WAAW;AACd,IAAI,wBAAwB;AAAA,EAC1B;AAAA,EACA;AACF;AAGA,IAAI,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,IAAI,KAAK;AACP,WAAO,IAAI,QAAQ,CAAC;AAAA,EACtB;AAAA,EACA,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,MAAsB,OAAO,CAAC,QAAQ;AACpC,QAAI,CAAC,IAAI,OAAO;AACd,UAAI,QAAQ,CAAC;AAAA,IACf;AACA,QAAI,MAAM,sBAAsB,IAAI;AAAA,EACtC,GAAG,MAAM;AACX;", "names": []}