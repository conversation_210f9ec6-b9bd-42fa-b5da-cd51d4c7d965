directories:
  output: release
  buildResources: assets
appId: com.wema-ia.app
productName: WeMa IA
files:
  - filter:
      - dist/**/*
      - public/electron.cjs
      - public/preload.js
      - public/wema-icon-256.ico
      - public/wema-logo-light.png
      - public/wema-logo-dark.png
      - package.json
      - '!clara_interpreter'
      - '!clara_interpreter/**/*'
      - '!**/clara_interpreter'
      - '!**/clara_interpreter/**/*'
      - '!**/**/venv/**/*'
      - '!**/venv/**/*'
      - '!venv'
      - '!venv/**/*'
      - '!screenshots/**/*'
      - '!documentation/**/*'
      - '!mock/**/*'
      - '!mascot/**/*'
      - '!*.log'
      - '!*.cache'
      - '!dist-*/**/*'
      - '!release/**/*'
asarUnpack:
  - '!**/canvas/**/*'
extraResources:
  - from: py_backend
    to: py_backend
    filter:
      - '**/*'
      - '!**/__pycache__/**'
      - '!**/*.pyc'
      - '!**/*.log'
      - '!**/venv/**/*'
      - '!**/test_*'
      - '!**/*_test.py'
publish:
  provider: github
  owner: badboysm890
  repo: WeMa-IA
  releaseType: release
  private: false
mac:
  category: public.app-category.developer-tools
  icon: assets/icons/mac/icon.icns
  hardenedRuntime: true
  gatekeeperAssess: false
  entitlements: build/entitlements.mac.plist
  entitlementsInherit: build/entitlements.mac.plist
  notarize: true
  target:
    - target: default
      arch:
        - arm64
  darkModeSupport: true
  artifactName: ${productName}-${version}-arm64.${ext}
  type: distribution
  files:
    - '!clara_interpreter/**/*'
    - '!**/**/venv/**/*'
    - '!**/venv/**/*'
    - '!venv'
    - '!venv/**/*'
win:
  target:
    - nsis
  icon: public/wema-icon-256.ico
  extraResources:
    - from: py_backend
      to: py_backend
      filter:
        - '**/*'
        - '!**/__pycache__/**'
        - '!**/*.pyc'
  extraFiles: []
  files:
    - '!clara_interpreter/**/*'
    - '!**/**/venv/**/*'
    - '!**/venv/**/*'
    - '!venv'
    - '!venv/**/*'
linux:
  target:
    - deb
    - AppImage
  category: Development
  icon: assets/icons/png
  desktop:
    entry:
      StartupNotify: 'false'
      Encoding: UTF-8
      MimeType: x-scheme-handler/wema-ia
  artifactName: ${productName}-${version}.${ext}
  files:
    - dist/**/*
    - public/electron.cjs
    - '!clara_interpreter/**/*'
    - '!**/**/venv/**/*'
    - '!**/venv/**/*'
    - '!venv'
    - '!venv/**/*'
    - '!src/**/*'
    - '!node_modules/**/*'
    - '!**/*.map'
    - '!**/*.ts'
    - '!**/*.tsx'
    - '!dist/assets/monaco-editor*'
    - '!dist/assets/mermaid*'
    - '!dist/assets/katex*'
    - '!dist/assets/cytoscape*'
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  include: installer.nsh
  installerIcon: assets/icons/win/icon.ico
  uninstallerIcon: assets/icons/win/icon.ico
  installerHeaderIcon: assets/icons/win/icon.ico
  createStartMenuShortcut: true
  shortcutName: WeMa IA
  license: LICENSE
  deleteAppDataOnUninstall: true
  displayLanguageSelector: false
  artifactName: ${productName} Setup ${version}.${ext}
  unicode: true
  differentialPackage: false
npmRebuild: false
electronVersion: 37.2.1
