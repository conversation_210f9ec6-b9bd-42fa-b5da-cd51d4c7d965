import{a as ct}from"./chunk-MBJCTAW2.mjs";import{a as It}from"./chunk-MEBTFSOL.mjs";import{d as At}from"./chunk-HESFG3RP.mjs";import{k as Ot}from"./chunk-YM3XIQPS.mjs";import"./chunk-TI4EEUUG.mjs";import{F as tt,N as Dt,X as et,b as Q,ga as Nt,n as mt,o as Lt,p as Tt,s as q}from"./chunk-ZKYS2E5M.mjs";import"./chunk-6BY5RJGC.mjs";import{a as L,b as lt,e as Ut}from"./chunk-GTKDMUJJ.mjs";var pt=lt((rt,ft)=>{"use strict";L(function(I,o){typeof rt=="object"&&typeof ft=="object"?ft.exports=o():typeof define=="function"&&define.amd?define([],o):typeof rt=="object"?rt.layoutBase=o():I.layoutBase=o()},"webpackUniversalModuleDefinition")(rt,function(){return function(E){var I={};function o(i){if(I[i])return I[i].exports;var e=I[i]={i,l:!1,exports:{}};return E[i].call(e.exports,e,e.exports,o),e.l=!0,e.exports}return L(o,"__webpack_require__"),o.m=E,o.c=I,o.i=function(i){return i},o.d=function(i,e,t){o.o(i,e)||Object.defineProperty(i,e,{configurable:!1,enumerable:!0,get:t})},o.n=function(i){var e=i&&i.__esModule?L(function(){return i.default},"getDefault"):L(function(){return i},"getModuleExports");return o.d(e,"a",e),e},o.o=function(i,e){return Object.prototype.hasOwnProperty.call(i,e)},o.p="",o(o.s=26)}([function(E,I,o){"use strict";function i(){}L(i,"LayoutConstants"),i.QUALITY=1,i.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,i.DEFAULT_INCREMENTAL=!1,i.DEFAULT_ANIMATION_ON_LAYOUT=!0,i.DEFAULT_ANIMATION_DURING_LAYOUT=!1,i.DEFAULT_ANIMATION_PERIOD=50,i.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,i.DEFAULT_GRAPH_MARGIN=15,i.NODE_DIMENSIONS_INCLUDE_LABELS=!1,i.SIMPLE_NODE_SIZE=40,i.SIMPLE_NODE_HALF_SIZE=i.SIMPLE_NODE_SIZE/2,i.EMPTY_COMPOUND_NODE_SIZE=40,i.MIN_EDGE_LENGTH=1,i.WORLD_BOUNDARY=1e6,i.INITIAL_WORLD_BOUNDARY=i.WORLD_BOUNDARY/1e3,i.WORLD_CENTER_X=1200,i.WORLD_CENTER_Y=900,E.exports=i},function(E,I,o){"use strict";var i=o(2),e=o(8),t=o(9);function r(f,a,d){i.call(this,d),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=d,this.bendpoints=[],this.source=f,this.target=a}L(r,"LEdge"),r.prototype=Object.create(i.prototype);for(var l in i)r[l]=i[l];r.prototype.getSource=function(){return this.source},r.prototype.getTarget=function(){return this.target},r.prototype.isInterGraph=function(){return this.isInterGraph},r.prototype.getLength=function(){return this.length},r.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},r.prototype.getBendpoints=function(){return this.bendpoints},r.prototype.getLca=function(){return this.lca},r.prototype.getSourceInLca=function(){return this.sourceInLca},r.prototype.getTargetInLca=function(){return this.targetInLca},r.prototype.getOtherEnd=function(f){if(this.source===f)return this.target;if(this.target===f)return this.source;throw"Node is not incident with this edge"},r.prototype.getOtherEndInGraph=function(f,a){for(var d=this.getOtherEnd(f),s=a.getGraphManager().getRoot();;){if(d.getOwner()==a)return d;if(d.getOwner()==s)break;d=d.getOwner().getParent()}return null},r.prototype.updateLength=function(){var f=new Array(4);this.isOverlapingSourceAndTarget=e.getIntersection(this.target.getRect(),this.source.getRect(),f),this.isOverlapingSourceAndTarget||(this.lengthX=f[0]-f[2],this.lengthY=f[1]-f[3],Math.abs(this.lengthX)<1&&(this.lengthX=t.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=t.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},r.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=t.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=t.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},E.exports=r},function(E,I,o){"use strict";function i(e){this.vGraphObject=e}L(i,"LGraphObject"),E.exports=i},function(E,I,o){"use strict";var i=o(2),e=o(10),t=o(13),r=o(0),l=o(16),f=o(4);function a(s,c,u,v){u==null&&v==null&&(v=c),i.call(this,v),s.graphManager!=null&&(s=s.graphManager),this.estimatedSize=e.MIN_VALUE,this.inclusionTreeDepth=e.MAX_VALUE,this.vGraphObject=v,this.edges=[],this.graphManager=s,u!=null&&c!=null?this.rect=new t(c.x,c.y,u.width,u.height):this.rect=new t}L(a,"LNode"),a.prototype=Object.create(i.prototype);for(var d in i)a[d]=i[d];a.prototype.getEdges=function(){return this.edges},a.prototype.getChild=function(){return this.child},a.prototype.getOwner=function(){return this.owner},a.prototype.getWidth=function(){return this.rect.width},a.prototype.setWidth=function(s){this.rect.width=s},a.prototype.getHeight=function(){return this.rect.height},a.prototype.setHeight=function(s){this.rect.height=s},a.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},a.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},a.prototype.getCenter=function(){return new f(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},a.prototype.getLocation=function(){return new f(this.rect.x,this.rect.y)},a.prototype.getRect=function(){return this.rect},a.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},a.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},a.prototype.setRect=function(s,c){this.rect.x=s.x,this.rect.y=s.y,this.rect.width=c.width,this.rect.height=c.height},a.prototype.setCenter=function(s,c){this.rect.x=s-this.rect.width/2,this.rect.y=c-this.rect.height/2},a.prototype.setLocation=function(s,c){this.rect.x=s,this.rect.y=c},a.prototype.moveBy=function(s,c){this.rect.x+=s,this.rect.y+=c},a.prototype.getEdgeListToNode=function(s){var c=[],u,v=this;return v.edges.forEach(function(m){if(m.target==s){if(m.source!=v)throw"Incorrect edge source!";c.push(m)}}),c},a.prototype.getEdgesBetween=function(s){var c=[],u,v=this;return v.edges.forEach(function(m){if(!(m.source==v||m.target==v))throw"Incorrect edge source and/or target";(m.target==s||m.source==s)&&c.push(m)}),c},a.prototype.getNeighborsList=function(){var s=new Set,c=this;return c.edges.forEach(function(u){if(u.source==c)s.add(u.target);else{if(u.target!=c)throw"Incorrect incidency!";s.add(u.source)}}),s},a.prototype.withChildren=function(){var s=new Set,c,u;if(s.add(this),this.child!=null)for(var v=this.child.getNodes(),m=0;m<v.length;m++)c=v[m],u=c.withChildren(),u.forEach(function(T){s.add(T)});return s},a.prototype.getNoOfChildren=function(){var s=0,c;if(this.child==null)s=1;else for(var u=this.child.getNodes(),v=0;v<u.length;v++)c=u[v],s+=c.getNoOfChildren();return s==0&&(s=1),s},a.prototype.getEstimatedSize=function(){if(this.estimatedSize==e.MIN_VALUE)throw"assert failed";return this.estimatedSize},a.prototype.calcEstimatedSize=function(){return this.child==null?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},a.prototype.scatter=function(){var s,c,u=-r.INITIAL_WORLD_BOUNDARY,v=r.INITIAL_WORLD_BOUNDARY;s=r.WORLD_CENTER_X+l.nextDouble()*(v-u)+u;var m=-r.INITIAL_WORLD_BOUNDARY,T=r.INITIAL_WORLD_BOUNDARY;c=r.WORLD_CENTER_Y+l.nextDouble()*(T-m)+m,this.rect.x=s,this.rect.y=c},a.prototype.updateBounds=function(){if(this.getChild()==null)throw"assert failed";if(this.getChild().getNodes().length!=0){var s=this.getChild();if(s.updateBounds(!0),this.rect.x=s.getLeft(),this.rect.y=s.getTop(),this.setWidth(s.getRight()-s.getLeft()),this.setHeight(s.getBottom()-s.getTop()),r.NODE_DIMENSIONS_INCLUDE_LABELS){var c=s.getRight()-s.getLeft(),u=s.getBottom()-s.getTop();this.labelWidth>c&&(this.rect.x-=(this.labelWidth-c)/2,this.setWidth(this.labelWidth)),this.labelHeight>u&&(this.labelPos=="center"?this.rect.y-=(this.labelHeight-u)/2:this.labelPos=="top"&&(this.rect.y-=this.labelHeight-u),this.setHeight(this.labelHeight))}}},a.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==e.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},a.prototype.transform=function(s){var c=this.rect.x;c>r.WORLD_BOUNDARY?c=r.WORLD_BOUNDARY:c<-r.WORLD_BOUNDARY&&(c=-r.WORLD_BOUNDARY);var u=this.rect.y;u>r.WORLD_BOUNDARY?u=r.WORLD_BOUNDARY:u<-r.WORLD_BOUNDARY&&(u=-r.WORLD_BOUNDARY);var v=new f(c,u),m=s.inverseTransformPoint(v);this.setLocation(m.x,m.y)},a.prototype.getLeft=function(){return this.rect.x},a.prototype.getRight=function(){return this.rect.x+this.rect.width},a.prototype.getTop=function(){return this.rect.y},a.prototype.getBottom=function(){return this.rect.y+this.rect.height},a.prototype.getParent=function(){return this.owner==null?null:this.owner.getParent()},E.exports=a},function(E,I,o){"use strict";function i(e,t){e==null&&t==null?(this.x=0,this.y=0):(this.x=e,this.y=t)}L(i,"PointD"),i.prototype.getX=function(){return this.x},i.prototype.getY=function(){return this.y},i.prototype.setX=function(e){this.x=e},i.prototype.setY=function(e){this.y=e},i.prototype.getDifference=function(e){return new DimensionD(this.x-e.x,this.y-e.y)},i.prototype.getCopy=function(){return new i(this.x,this.y)},i.prototype.translate=function(e){return this.x+=e.width,this.y+=e.height,this},E.exports=i},function(E,I,o){"use strict";var i=o(2),e=o(10),t=o(0),r=o(6),l=o(3),f=o(1),a=o(13),d=o(12),s=o(11);function c(v,m,T){i.call(this,T),this.estimatedSize=e.MIN_VALUE,this.margin=t.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=v,m!=null&&m instanceof r?this.graphManager=m:m!=null&&m instanceof Layout&&(this.graphManager=m.graphManager)}L(c,"LGraph"),c.prototype=Object.create(i.prototype);for(var u in i)c[u]=i[u];c.prototype.getNodes=function(){return this.nodes},c.prototype.getEdges=function(){return this.edges},c.prototype.getGraphManager=function(){return this.graphManager},c.prototype.getParent=function(){return this.parent},c.prototype.getLeft=function(){return this.left},c.prototype.getRight=function(){return this.right},c.prototype.getTop=function(){return this.top},c.prototype.getBottom=function(){return this.bottom},c.prototype.isConnected=function(){return this.isConnected},c.prototype.add=function(v,m,T){if(m==null&&T==null){var y=v;if(this.graphManager==null)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(y)>-1)throw"Node already in graph!";return y.owner=this,this.getNodes().push(y),y}else{var A=v;if(!(this.getNodes().indexOf(m)>-1&&this.getNodes().indexOf(T)>-1))throw"Source or target not in graph!";if(!(m.owner==T.owner&&m.owner==this))throw"Both owners must be this graph!";return m.owner!=T.owner?null:(A.source=m,A.target=T,A.isInterGraph=!1,this.getEdges().push(A),m.edges.push(A),T!=m&&T.edges.push(A),A)}},c.prototype.remove=function(v){var m=v;if(v instanceof l){if(m==null)throw"Node is null!";if(!(m.owner!=null&&m.owner==this))throw"Owner graph is invalid!";if(this.graphManager==null)throw"Owner graph manager is invalid!";for(var T=m.edges.slice(),y,A=T.length,O=0;O<A;O++)y=T[O],y.isInterGraph?this.graphManager.remove(y):y.source.owner.remove(y);var R=this.nodes.indexOf(m);if(R==-1)throw"Node not in owner node list!";this.nodes.splice(R,1)}else if(v instanceof f){var y=v;if(y==null)throw"Edge is null!";if(!(y.source!=null&&y.target!=null))throw"Source and/or target is null!";if(!(y.source.owner!=null&&y.target.owner!=null&&y.source.owner==this&&y.target.owner==this))throw"Source and/or target owner is invalid!";var n=y.source.edges.indexOf(y),h=y.target.edges.indexOf(y);if(!(n>-1&&h>-1))throw"Source and/or target doesn't know this edge!";y.source.edges.splice(n,1),y.target!=y.source&&y.target.edges.splice(h,1);var R=y.source.owner.getEdges().indexOf(y);if(R==-1)throw"Not in owner's edge list!";y.source.owner.getEdges().splice(R,1)}},c.prototype.updateLeftTop=function(){for(var v=e.MAX_VALUE,m=e.MAX_VALUE,T,y,A,O=this.getNodes(),R=O.length,n=0;n<R;n++){var h=O[n];T=h.getTop(),y=h.getLeft(),v>T&&(v=T),m>y&&(m=y)}return v==e.MAX_VALUE?null:(O[0].getParent().paddingLeft!=null?A=O[0].getParent().paddingLeft:A=this.margin,this.left=m-A,this.top=v-A,new d(this.left,this.top))},c.prototype.updateBounds=function(v){for(var m=e.MAX_VALUE,T=-e.MAX_VALUE,y=e.MAX_VALUE,A=-e.MAX_VALUE,O,R,n,h,g,p=this.nodes,D=p.length,N=0;N<D;N++){var M=p[N];v&&M.child!=null&&M.updateBounds(),O=M.getLeft(),R=M.getRight(),n=M.getTop(),h=M.getBottom(),m>O&&(m=O),T<R&&(T=R),y>n&&(y=n),A<h&&(A=h)}var C=new a(m,y,T-m,A-y);m==e.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),p[0].getParent().paddingLeft!=null?g=p[0].getParent().paddingLeft:g=this.margin,this.left=C.x-g,this.right=C.x+C.width+g,this.top=C.y-g,this.bottom=C.y+C.height+g},c.calculateBounds=function(v){for(var m=e.MAX_VALUE,T=-e.MAX_VALUE,y=e.MAX_VALUE,A=-e.MAX_VALUE,O,R,n,h,g=v.length,p=0;p<g;p++){var D=v[p];O=D.getLeft(),R=D.getRight(),n=D.getTop(),h=D.getBottom(),m>O&&(m=O),T<R&&(T=R),y>n&&(y=n),A<h&&(A=h)}var N=new a(m,y,T-m,A-y);return N},c.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},c.prototype.getEstimatedSize=function(){if(this.estimatedSize==e.MIN_VALUE)throw"assert failed";return this.estimatedSize},c.prototype.calcEstimatedSize=function(){for(var v=0,m=this.nodes,T=m.length,y=0;y<T;y++){var A=m[y];v+=A.calcEstimatedSize()}return v==0?this.estimatedSize=t.EMPTY_COMPOUND_NODE_SIZE:this.estimatedSize=v/Math.sqrt(this.nodes.length),this.estimatedSize},c.prototype.updateConnected=function(){var v=this;if(this.nodes.length==0){this.isConnected=!0;return}var m=new s,T=new Set,y=this.nodes[0],A,O,R=y.withChildren();for(R.forEach(function(N){m.push(N),T.add(N)});m.length!==0;){y=m.shift(),A=y.getEdges();for(var n=A.length,h=0;h<n;h++){var g=A[h];if(O=g.getOtherEndInGraph(y,this),O!=null&&!T.has(O)){var p=O.withChildren();p.forEach(function(N){m.push(N),T.add(N)})}}}if(this.isConnected=!1,T.size>=this.nodes.length){var D=0;T.forEach(function(N){N.owner==v&&D++}),D==this.nodes.length&&(this.isConnected=!0)}},E.exports=c},function(E,I,o){"use strict";var i,e=o(1);function t(r){i=o(5),this.layout=r,this.graphs=[],this.edges=[]}L(t,"LGraphManager"),t.prototype.addRoot=function(){var r=this.layout.newGraph(),l=this.layout.newNode(null),f=this.add(r,l);return this.setRootGraph(f),this.rootGraph},t.prototype.add=function(r,l,f,a,d){if(f==null&&a==null&&d==null){if(r==null)throw"Graph is null!";if(l==null)throw"Parent node is null!";if(this.graphs.indexOf(r)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(r),r.parent!=null)throw"Already has a parent!";if(l.child!=null)throw"Already has a child!";return r.parent=l,l.child=r,r}else{d=f,a=l,f=r;var s=a.getOwner(),c=d.getOwner();if(!(s!=null&&s.getGraphManager()==this))throw"Source not in this graph mgr!";if(!(c!=null&&c.getGraphManager()==this))throw"Target not in this graph mgr!";if(s==c)return f.isInterGraph=!1,s.add(f,a,d);if(f.isInterGraph=!0,f.source=a,f.target=d,this.edges.indexOf(f)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(f),!(f.source!=null&&f.target!=null))throw"Edge source and/or target is null!";if(!(f.source.edges.indexOf(f)==-1&&f.target.edges.indexOf(f)==-1))throw"Edge already in source and/or target incidency list!";return f.source.edges.push(f),f.target.edges.push(f),f}},t.prototype.remove=function(r){if(r instanceof i){var l=r;if(l.getGraphManager()!=this)throw"Graph not in this graph mgr";if(!(l==this.rootGraph||l.parent!=null&&l.parent.graphManager==this))throw"Invalid parent node!";var f=[];f=f.concat(l.getEdges());for(var a,d=f.length,s=0;s<d;s++)a=f[s],l.remove(a);var c=[];c=c.concat(l.getNodes());var u;d=c.length;for(var s=0;s<d;s++)u=c[s],l.remove(u);l==this.rootGraph&&this.setRootGraph(null);var v=this.graphs.indexOf(l);this.graphs.splice(v,1),l.parent=null}else if(r instanceof e){if(a=r,a==null)throw"Edge is null!";if(!a.isInterGraph)throw"Not an inter-graph edge!";if(!(a.source!=null&&a.target!=null))throw"Source and/or target is null!";if(!(a.source.edges.indexOf(a)!=-1&&a.target.edges.indexOf(a)!=-1))throw"Source and/or target doesn't know this edge!";var v=a.source.edges.indexOf(a);if(a.source.edges.splice(v,1),v=a.target.edges.indexOf(a),a.target.edges.splice(v,1),!(a.source.owner!=null&&a.source.owner.getGraphManager()!=null))throw"Edge owner graph or owner graph manager is null!";if(a.source.owner.getGraphManager().edges.indexOf(a)==-1)throw"Not in owner graph manager's edge list!";var v=a.source.owner.getGraphManager().edges.indexOf(a);a.source.owner.getGraphManager().edges.splice(v,1)}},t.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},t.prototype.getGraphs=function(){return this.graphs},t.prototype.getAllNodes=function(){if(this.allNodes==null){for(var r=[],l=this.getGraphs(),f=l.length,a=0;a<f;a++)r=r.concat(l[a].getNodes());this.allNodes=r}return this.allNodes},t.prototype.resetAllNodes=function(){this.allNodes=null},t.prototype.resetAllEdges=function(){this.allEdges=null},t.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},t.prototype.getAllEdges=function(){if(this.allEdges==null){for(var r=[],l=this.getGraphs(),f=l.length,a=0;a<l.length;a++)r=r.concat(l[a].getEdges());r=r.concat(this.edges),this.allEdges=r}return this.allEdges},t.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},t.prototype.setAllNodesToApplyGravitation=function(r){if(this.allNodesToApplyGravitation!=null)throw"assert failed";this.allNodesToApplyGravitation=r},t.prototype.getRoot=function(){return this.rootGraph},t.prototype.setRootGraph=function(r){if(r.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=r,r.parent==null&&(r.parent=this.layout.newNode("Root node"))},t.prototype.getLayout=function(){return this.layout},t.prototype.isOneAncestorOfOther=function(r,l){if(!(r!=null&&l!=null))throw"assert failed";if(r==l)return!0;var f=r.getOwner(),a;do{if(a=f.getParent(),a==null)break;if(a==l)return!0;if(f=a.getOwner(),f==null)break}while(!0);f=l.getOwner();do{if(a=f.getParent(),a==null)break;if(a==r)return!0;if(f=a.getOwner(),f==null)break}while(!0);return!1},t.prototype.calcLowestCommonAncestors=function(){for(var r,l,f,a,d,s=this.getAllEdges(),c=s.length,u=0;u<c;u++){if(r=s[u],l=r.source,f=r.target,r.lca=null,r.sourceInLca=l,r.targetInLca=f,l==f){r.lca=l.getOwner();continue}for(a=l.getOwner();r.lca==null;){for(r.targetInLca=f,d=f.getOwner();r.lca==null;){if(d==a){r.lca=d;break}if(d==this.rootGraph)break;if(r.lca!=null)throw"assert failed";r.targetInLca=d.getParent(),d=r.targetInLca.getOwner()}if(a==this.rootGraph)break;r.lca==null&&(r.sourceInLca=a.getParent(),a=r.sourceInLca.getOwner())}if(r.lca==null)throw"assert failed"}},t.prototype.calcLowestCommonAncestor=function(r,l){if(r==l)return r.getOwner();var f=r.getOwner();do{if(f==null)break;var a=l.getOwner();do{if(a==null)break;if(a==f)return a;a=a.getParent().getOwner()}while(!0);f=f.getParent().getOwner()}while(!0);return f},t.prototype.calcInclusionTreeDepths=function(r,l){r==null&&l==null&&(r=this.rootGraph,l=1);for(var f,a=r.getNodes(),d=a.length,s=0;s<d;s++)f=a[s],f.inclusionTreeDepth=l,f.child!=null&&this.calcInclusionTreeDepths(f.child,l+1)},t.prototype.includesInvalidEdge=function(){for(var r,l=this.edges.length,f=0;f<l;f++)if(r=this.edges[f],this.isOneAncestorOfOther(r.source,r.target))return!0;return!1},E.exports=t},function(E,I,o){"use strict";var i=o(0);function e(){}L(e,"FDLayoutConstants");for(var t in i)e[t]=i[t];e.MAX_ITERATIONS=2500,e.DEFAULT_EDGE_LENGTH=50,e.DEFAULT_SPRING_STRENGTH=.45,e.DEFAULT_REPULSION_STRENGTH=4500,e.DEFAULT_GRAVITY_STRENGTH=.4,e.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,e.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,e.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,e.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,e.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,e.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,e.COOLING_ADAPTATION_FACTOR=.33,e.ADAPTATION_LOWER_NODE_LIMIT=1e3,e.ADAPTATION_UPPER_NODE_LIMIT=5e3,e.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,e.MAX_NODE_DISPLACEMENT=e.MAX_NODE_DISPLACEMENT_INCREMENTAL*3,e.MIN_REPULSION_DIST=e.DEFAULT_EDGE_LENGTH/10,e.CONVERGENCE_CHECK_PERIOD=100,e.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,e.MIN_EDGE_LENGTH=1,e.GRID_CALCULATION_CHECK_PERIOD=10,E.exports=e},function(E,I,o){"use strict";var i=o(12);function e(){}L(e,"IGeometry"),e.calcSeparationAmount=function(t,r,l,f){if(!t.intersects(r))throw"assert failed";var a=new Array(2);this.decideDirectionsForOverlappingNodes(t,r,a),l[0]=Math.min(t.getRight(),r.getRight())-Math.max(t.x,r.x),l[1]=Math.min(t.getBottom(),r.getBottom())-Math.max(t.y,r.y),t.getX()<=r.getX()&&t.getRight()>=r.getRight()?l[0]+=Math.min(r.getX()-t.getX(),t.getRight()-r.getRight()):r.getX()<=t.getX()&&r.getRight()>=t.getRight()&&(l[0]+=Math.min(t.getX()-r.getX(),r.getRight()-t.getRight())),t.getY()<=r.getY()&&t.getBottom()>=r.getBottom()?l[1]+=Math.min(r.getY()-t.getY(),t.getBottom()-r.getBottom()):r.getY()<=t.getY()&&r.getBottom()>=t.getBottom()&&(l[1]+=Math.min(t.getY()-r.getY(),r.getBottom()-t.getBottom()));var d=Math.abs((r.getCenterY()-t.getCenterY())/(r.getCenterX()-t.getCenterX()));r.getCenterY()===t.getCenterY()&&r.getCenterX()===t.getCenterX()&&(d=1);var s=d*l[0],c=l[1]/d;l[0]<c?c=l[0]:s=l[1],l[0]=-1*a[0]*(c/2+f),l[1]=-1*a[1]*(s/2+f)},e.decideDirectionsForOverlappingNodes=function(t,r,l){t.getCenterX()<r.getCenterX()?l[0]=-1:l[0]=1,t.getCenterY()<r.getCenterY()?l[1]=-1:l[1]=1},e.getIntersection2=function(t,r,l){var f=t.getCenterX(),a=t.getCenterY(),d=r.getCenterX(),s=r.getCenterY();if(t.intersects(r))return l[0]=f,l[1]=a,l[2]=d,l[3]=s,!0;var c=t.getX(),u=t.getY(),v=t.getRight(),m=t.getX(),T=t.getBottom(),y=t.getRight(),A=t.getWidthHalf(),O=t.getHeightHalf(),R=r.getX(),n=r.getY(),h=r.getRight(),g=r.getX(),p=r.getBottom(),D=r.getRight(),N=r.getWidthHalf(),M=r.getHeightHalf(),C=!1,w=!1;if(f===d){if(a>s)return l[0]=f,l[1]=u,l[2]=d,l[3]=p,!1;if(a<s)return l[0]=f,l[1]=T,l[2]=d,l[3]=n,!1}else if(a===s){if(f>d)return l[0]=c,l[1]=a,l[2]=h,l[3]=s,!1;if(f<d)return l[0]=v,l[1]=a,l[2]=R,l[3]=s,!1}else{var P=t.height/t.width,U=r.height/r.width,F=(s-a)/(d-f),x=void 0,G=void 0,S=void 0,_=void 0,X=void 0,b=void 0;if(-P===F?f>d?(l[0]=m,l[1]=T,C=!0):(l[0]=v,l[1]=u,C=!0):P===F&&(f>d?(l[0]=c,l[1]=u,C=!0):(l[0]=y,l[1]=T,C=!0)),-U===F?d>f?(l[2]=g,l[3]=p,w=!0):(l[2]=h,l[3]=n,w=!0):U===F&&(d>f?(l[2]=R,l[3]=n,w=!0):(l[2]=D,l[3]=p,w=!0)),C&&w)return!1;if(f>d?a>s?(x=this.getCardinalDirection(P,F,4),G=this.getCardinalDirection(U,F,2)):(x=this.getCardinalDirection(-P,F,3),G=this.getCardinalDirection(-U,F,1)):a>s?(x=this.getCardinalDirection(-P,F,1),G=this.getCardinalDirection(-U,F,3)):(x=this.getCardinalDirection(P,F,2),G=this.getCardinalDirection(U,F,4)),!C)switch(x){case 1:_=u,S=f+-O/F,l[0]=S,l[1]=_;break;case 2:S=y,_=a+A*F,l[0]=S,l[1]=_;break;case 3:_=T,S=f+O/F,l[0]=S,l[1]=_;break;case 4:S=m,_=a+-A*F,l[0]=S,l[1]=_;break}if(!w)switch(G){case 1:b=n,X=d+-M/F,l[2]=X,l[3]=b;break;case 2:X=D,b=s+N*F,l[2]=X,l[3]=b;break;case 3:b=p,X=d+M/F,l[2]=X,l[3]=b;break;case 4:X=g,b=s+-N*F,l[2]=X,l[3]=b;break}}return!1},e.getCardinalDirection=function(t,r,l){return t>r?l:1+l%4},e.getIntersection=function(t,r,l,f){if(f==null)return this.getIntersection2(t,r,l);var a=t.x,d=t.y,s=r.x,c=r.y,u=l.x,v=l.y,m=f.x,T=f.y,y=void 0,A=void 0,O=void 0,R=void 0,n=void 0,h=void 0,g=void 0,p=void 0,D=void 0;return O=c-d,n=a-s,g=s*d-a*c,R=T-v,h=u-m,p=m*v-u*T,D=O*h-R*n,D===0?null:(y=(n*p-h*g)/D,A=(R*g-O*p)/D,new i(y,A))},e.angleOfVector=function(t,r,l,f){var a=void 0;return t!==l?(a=Math.atan((f-r)/(l-t)),l<t?a+=Math.PI:f<r&&(a+=this.TWO_PI)):f<r?a=this.ONE_AND_HALF_PI:a=this.HALF_PI,a},e.doIntersect=function(t,r,l,f){var a=t.x,d=t.y,s=r.x,c=r.y,u=l.x,v=l.y,m=f.x,T=f.y,y=(s-a)*(T-v)-(m-u)*(c-d);if(y===0)return!1;var A=((T-v)*(m-a)+(u-m)*(T-d))/y,O=((d-c)*(m-a)+(s-a)*(T-d))/y;return 0<A&&A<1&&0<O&&O<1},e.HALF_PI=.5*Math.PI,e.ONE_AND_HALF_PI=1.5*Math.PI,e.TWO_PI=2*Math.PI,e.THREE_PI=3*Math.PI,E.exports=e},function(E,I,o){"use strict";function i(){}L(i,"IMath"),i.sign=function(e){return e>0?1:e<0?-1:0},i.floor=function(e){return e<0?Math.ceil(e):Math.floor(e)},i.ceil=function(e){return e<0?Math.floor(e):Math.ceil(e)},E.exports=i},function(E,I,o){"use strict";function i(){}L(i,"Integer"),i.MAX_VALUE=2147483647,i.MIN_VALUE=-2147483648,E.exports=i},function(E,I,o){"use strict";var i=function(){function a(d,s){for(var c=0;c<s.length;c++){var u=s[c];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(d,u.key,u)}}return L(a,"defineProperties"),function(d,s,c){return s&&a(d.prototype,s),c&&a(d,c),d}}();function e(a,d){if(!(a instanceof d))throw new TypeError("Cannot call a class as a function")}L(e,"_classCallCheck");var t=L(function(d){return{value:d,next:null,prev:null}},"nodeFrom"),r=L(function(d,s,c,u){return d!==null?d.next=s:u.head=s,c!==null?c.prev=s:u.tail=s,s.prev=d,s.next=c,u.length++,s},"add"),l=L(function(d,s){var c=d.prev,u=d.next;return c!==null?c.next=u:s.head=u,u!==null?u.prev=c:s.tail=c,d.prev=d.next=null,s.length--,d},"_remove"),f=function(){function a(d){var s=this;e(this,a),this.length=0,this.head=null,this.tail=null,d?.forEach(function(c){return s.push(c)})}return L(a,"LinkedList"),i(a,[{key:"size",value:L(function(){return this.length},"size")},{key:"insertBefore",value:L(function(s,c){return r(c.prev,t(s),c,this)},"insertBefore")},{key:"insertAfter",value:L(function(s,c){return r(c,t(s),c.next,this)},"insertAfter")},{key:"insertNodeBefore",value:L(function(s,c){return r(c.prev,s,c,this)},"insertNodeBefore")},{key:"insertNodeAfter",value:L(function(s,c){return r(c,s,c.next,this)},"insertNodeAfter")},{key:"push",value:L(function(s){return r(this.tail,t(s),null,this)},"push")},{key:"unshift",value:L(function(s){return r(null,t(s),this.head,this)},"unshift")},{key:"remove",value:L(function(s){return l(s,this)},"remove")},{key:"pop",value:L(function(){return l(this.tail,this).value},"pop")},{key:"popNode",value:L(function(){return l(this.tail,this)},"popNode")},{key:"shift",value:L(function(){return l(this.head,this).value},"shift")},{key:"shiftNode",value:L(function(){return l(this.head,this)},"shiftNode")},{key:"get_object_at",value:L(function(s){if(s<=this.length()){for(var c=1,u=this.head;c<s;)u=u.next,c++;return u.value}},"get_object_at")},{key:"set_object_at",value:L(function(s,c){if(s<=this.length()){for(var u=1,v=this.head;u<s;)v=v.next,u++;v.value=c}},"set_object_at")}]),a}();E.exports=f},function(E,I,o){"use strict";function i(e,t,r){this.x=null,this.y=null,e==null&&t==null&&r==null?(this.x=0,this.y=0):typeof e=="number"&&typeof t=="number"&&r==null?(this.x=e,this.y=t):e.constructor.name=="Point"&&t==null&&r==null&&(r=e,this.x=r.x,this.y=r.y)}L(i,"Point"),i.prototype.getX=function(){return this.x},i.prototype.getY=function(){return this.y},i.prototype.getLocation=function(){return new i(this.x,this.y)},i.prototype.setLocation=function(e,t,r){e.constructor.name=="Point"&&t==null&&r==null?(r=e,this.setLocation(r.x,r.y)):typeof e=="number"&&typeof t=="number"&&r==null&&(parseInt(e)==e&&parseInt(t)==t?this.move(e,t):(this.x=Math.floor(e+.5),this.y=Math.floor(t+.5)))},i.prototype.move=function(e,t){this.x=e,this.y=t},i.prototype.translate=function(e,t){this.x+=e,this.y+=t},i.prototype.equals=function(e){if(e.constructor.name=="Point"){var t=e;return this.x==t.x&&this.y==t.y}return this==e},i.prototype.toString=function(){return new i().constructor.name+"[x="+this.x+",y="+this.y+"]"},E.exports=i},function(E,I,o){"use strict";function i(e,t,r,l){this.x=0,this.y=0,this.width=0,this.height=0,e!=null&&t!=null&&r!=null&&l!=null&&(this.x=e,this.y=t,this.width=r,this.height=l)}L(i,"RectangleD"),i.prototype.getX=function(){return this.x},i.prototype.setX=function(e){this.x=e},i.prototype.getY=function(){return this.y},i.prototype.setY=function(e){this.y=e},i.prototype.getWidth=function(){return this.width},i.prototype.setWidth=function(e){this.width=e},i.prototype.getHeight=function(){return this.height},i.prototype.setHeight=function(e){this.height=e},i.prototype.getRight=function(){return this.x+this.width},i.prototype.getBottom=function(){return this.y+this.height},i.prototype.intersects=function(e){return!(this.getRight()<e.x||this.getBottom()<e.y||e.getRight()<this.x||e.getBottom()<this.y)},i.prototype.getCenterX=function(){return this.x+this.width/2},i.prototype.getMinX=function(){return this.getX()},i.prototype.getMaxX=function(){return this.getX()+this.width},i.prototype.getCenterY=function(){return this.y+this.height/2},i.prototype.getMinY=function(){return this.getY()},i.prototype.getMaxY=function(){return this.getY()+this.height},i.prototype.getWidthHalf=function(){return this.width/2},i.prototype.getHeightHalf=function(){return this.height/2},E.exports=i},function(E,I,o){"use strict";var i=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};function e(){}L(e,"UniqueIDGeneretor"),e.lastID=0,e.createID=function(t){return e.isPrimitive(t)?t:(t.uniqueID!=null||(t.uniqueID=e.getString(),e.lastID++),t.uniqueID)},e.getString=function(t){return t==null&&(t=e.lastID),"Object#"+t},e.isPrimitive=function(t){var r=typeof t>"u"?"undefined":i(t);return t==null||r!="object"&&r!="function"},E.exports=e},function(E,I,o){"use strict";function i(u){if(Array.isArray(u)){for(var v=0,m=Array(u.length);v<u.length;v++)m[v]=u[v];return m}else return Array.from(u)}L(i,"_toConsumableArray");var e=o(0),t=o(6),r=o(3),l=o(1),f=o(5),a=o(4),d=o(17),s=o(27);function c(u){s.call(this),this.layoutQuality=e.QUALITY,this.createBendsAsNeeded=e.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=e.DEFAULT_INCREMENTAL,this.animationOnLayout=e.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=e.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=e.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=e.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new t(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,u!=null&&(this.isRemoteUse=u)}L(c,"Layout"),c.RANDOM_SEED=1,c.prototype=Object.create(s.prototype),c.prototype.getGraphManager=function(){return this.graphManager},c.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},c.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},c.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},c.prototype.newGraphManager=function(){var u=new t(this);return this.graphManager=u,u},c.prototype.newGraph=function(u){return new f(null,this.graphManager,u)},c.prototype.newNode=function(u){return new r(this.graphManager,u)},c.prototype.newEdge=function(u){return new l(null,null,u)},c.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()},c.prototype.runLayout=function(){this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters();var u;return this.checkLayoutSuccess()?u=!1:u=this.layout(),e.ANIMATE==="during"?!1:(u&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,u)},c.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},c.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var u,v=this.graphManager.getAllEdges(),m=0;m<v.length;m++)u=v[m];for(var T,y=this.graphManager.getRoot().getNodes(),m=0;m<y.length;m++)T=y[m];this.update(this.graphManager.getRoot())}},c.prototype.update=function(u){if(u==null)this.update2();else if(u instanceof r){var v=u;if(v.getChild()!=null)for(var m=v.getChild().getNodes(),T=0;T<m.length;T++)update(m[T]);if(v.vGraphObject!=null){var y=v.vGraphObject;y.update(v)}}else if(u instanceof l){var A=u;if(A.vGraphObject!=null){var O=A.vGraphObject;O.update(A)}}else if(u instanceof f){var R=u;if(R.vGraphObject!=null){var n=R.vGraphObject;n.update(R)}}},c.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=e.QUALITY,this.animationDuringLayout=e.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=e.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=e.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=e.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=e.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=e.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},c.prototype.transform=function(u){if(u==null)this.transform(new a(0,0));else{var v=new d,m=this.graphManager.getRoot().updateLeftTop();if(m!=null){v.setWorldOrgX(u.x),v.setWorldOrgY(u.y),v.setDeviceOrgX(m.x),v.setDeviceOrgY(m.y);for(var T=this.getAllNodes(),y,A=0;A<T.length;A++)y=T[A],y.transform(v)}}},c.prototype.positionNodesRandomly=function(u){if(u==null)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var v,m,T=u.getNodes(),y=0;y<T.length;y++)v=T[y],m=v.getChild(),m==null||m.getNodes().length==0?v.scatter():(this.positionNodesRandomly(m),v.updateBounds())},c.prototype.getFlatForest=function(){for(var u=[],v=!0,m=this.graphManager.getRoot().getNodes(),T=!0,y=0;y<m.length;y++)m[y].getChild()!=null&&(T=!1);if(!T)return u;var A=new Set,O=[],R=new Map,n=[];for(n=n.concat(m);n.length>0&&v;){for(O.push(n[0]);O.length>0&&v;){var h=O[0];O.splice(0,1),A.add(h);for(var g=h.getEdges(),y=0;y<g.length;y++){var p=g[y].getOtherEnd(h);if(R.get(h)!=p)if(!A.has(p))O.push(p),R.set(p,h);else{v=!1;break}}}if(!v)u=[];else{var D=[].concat(i(A));u.push(D);for(var y=0;y<D.length;y++){var N=D[y],M=n.indexOf(N);M>-1&&n.splice(M,1)}A=new Set,R=new Map}}return u},c.prototype.createDummyNodesForBendpoints=function(u){for(var v=[],m=u.source,T=this.graphManager.calcLowestCommonAncestor(u.source,u.target),y=0;y<u.bendpoints.length;y++){var A=this.newNode(null);A.setRect(new Point(0,0),new Dimension(1,1)),T.add(A);var O=this.newEdge(null);this.graphManager.add(O,m,A),v.add(A),m=A}var O=this.newEdge(null);return this.graphManager.add(O,m,u.target),this.edgeToDummyNodes.set(u,v),u.isInterGraph()?this.graphManager.remove(u):T.remove(u),v},c.prototype.createBendpointsFromDummyNodes=function(){var u=[];u=u.concat(this.graphManager.getAllEdges()),u=[].concat(i(this.edgeToDummyNodes.keys())).concat(u);for(var v=0;v<u.length;v++){var m=u[v];if(m.bendpoints.length>0){for(var T=this.edgeToDummyNodes.get(m),y=0;y<T.length;y++){var A=T[y],O=new a(A.getCenterX(),A.getCenterY()),R=m.bendpoints.get(y);R.x=O.x,R.y=O.y,A.getOwner().remove(A)}this.graphManager.add(m,m.source,m.target)}}},c.transform=function(u,v,m,T){if(m!=null&&T!=null){var y=v;if(u<=50){var A=v/m;y-=(v-A)/50*(50-u)}else{var O=v*T;y+=(O-v)/50*(u-50)}return y}else{var R,n;return u<=50?(R=9*v/500,n=v/10):(R=9*v/50,n=-8*v),R*u+n}},c.findCenterOfTree=function(u){var v=[];v=v.concat(u);var m=[],T=new Map,y=!1,A=null;(v.length==1||v.length==2)&&(y=!0,A=v[0]);for(var O=0;O<v.length;O++){var R=v[O],n=R.getNeighborsList().size;T.set(R,R.getNeighborsList().size),n==1&&m.push(R)}var h=[];for(h=h.concat(m);!y;){var g=[];g=g.concat(h),h=[];for(var O=0;O<v.length;O++){var R=v[O],p=v.indexOf(R);p>=0&&v.splice(p,1);var D=R.getNeighborsList();D.forEach(function(C){if(m.indexOf(C)<0){var w=T.get(C),P=w-1;P==1&&h.push(C),T.set(C,P)}})}m=m.concat(h),(v.length==1||v.length==2)&&(y=!0,A=v[0])}return A},c.prototype.setGraphManager=function(u){this.graphManager=u},E.exports=c},function(E,I,o){"use strict";function i(){}L(i,"RandomSeed"),i.seed=1,i.x=0,i.nextDouble=function(){return i.x=Math.sin(i.seed++)*1e4,i.x-Math.floor(i.x)},E.exports=i},function(E,I,o){"use strict";var i=o(4);function e(t,r){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}L(e,"Transform"),e.prototype.getWorldOrgX=function(){return this.lworldOrgX},e.prototype.setWorldOrgX=function(t){this.lworldOrgX=t},e.prototype.getWorldOrgY=function(){return this.lworldOrgY},e.prototype.setWorldOrgY=function(t){this.lworldOrgY=t},e.prototype.getWorldExtX=function(){return this.lworldExtX},e.prototype.setWorldExtX=function(t){this.lworldExtX=t},e.prototype.getWorldExtY=function(){return this.lworldExtY},e.prototype.setWorldExtY=function(t){this.lworldExtY=t},e.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},e.prototype.setDeviceOrgX=function(t){this.ldeviceOrgX=t},e.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},e.prototype.setDeviceOrgY=function(t){this.ldeviceOrgY=t},e.prototype.getDeviceExtX=function(){return this.ldeviceExtX},e.prototype.setDeviceExtX=function(t){this.ldeviceExtX=t},e.prototype.getDeviceExtY=function(){return this.ldeviceExtY},e.prototype.setDeviceExtY=function(t){this.ldeviceExtY=t},e.prototype.transformX=function(t){var r=0,l=this.lworldExtX;return l!=0&&(r=this.ldeviceOrgX+(t-this.lworldOrgX)*this.ldeviceExtX/l),r},e.prototype.transformY=function(t){var r=0,l=this.lworldExtY;return l!=0&&(r=this.ldeviceOrgY+(t-this.lworldOrgY)*this.ldeviceExtY/l),r},e.prototype.inverseTransformX=function(t){var r=0,l=this.ldeviceExtX;return l!=0&&(r=this.lworldOrgX+(t-this.ldeviceOrgX)*this.lworldExtX/l),r},e.prototype.inverseTransformY=function(t){var r=0,l=this.ldeviceExtY;return l!=0&&(r=this.lworldOrgY+(t-this.ldeviceOrgY)*this.lworldExtY/l),r},e.prototype.inverseTransformPoint=function(t){var r=new i(this.inverseTransformX(t.x),this.inverseTransformY(t.y));return r},E.exports=e},function(E,I,o){"use strict";function i(s){if(Array.isArray(s)){for(var c=0,u=Array(s.length);c<s.length;c++)u[c]=s[c];return u}else return Array.from(s)}L(i,"_toConsumableArray");var e=o(15),t=o(7),r=o(0),l=o(8),f=o(9);function a(){e.call(this),this.useSmartIdealEdgeLengthCalculation=t.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.idealEdgeLength=t.DEFAULT_EDGE_LENGTH,this.springConstant=t.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=t.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=t.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=t.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=t.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=t.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*t.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=t.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=t.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=t.MAX_ITERATIONS}L(a,"FDLayout"),a.prototype=Object.create(e.prototype);for(var d in e)a[d]=e[d];a.prototype.initParameters=function(){e.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=t.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},a.prototype.calcIdealEdgeLengths=function(){for(var s,c,u,v,m,T,y=this.getGraphManager().getAllEdges(),A=0;A<y.length;A++)s=y[A],s.idealLength=this.idealEdgeLength,s.isInterGraph&&(u=s.getSource(),v=s.getTarget(),m=s.getSourceInLca().getEstimatedSize(),T=s.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(s.idealLength+=m+T-2*r.SIMPLE_NODE_SIZE),c=s.getLca().getInclusionTreeDepth(),s.idealLength+=t.DEFAULT_EDGE_LENGTH*t.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(u.getInclusionTreeDepth()+v.getInclusionTreeDepth()-2*c))},a.prototype.initSpringEmbedder=function(){var s=this.getAllNodes().length;this.incremental?(s>t.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*t.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(s-t.ADAPTATION_LOWER_NODE_LIMIT)/(t.ADAPTATION_UPPER_NODE_LIMIT-t.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-t.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=t.MAX_NODE_DISPLACEMENT_INCREMENTAL):(s>t.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(t.COOLING_ADAPTATION_FACTOR,1-(s-t.ADAPTATION_LOWER_NODE_LIMIT)/(t.ADAPTATION_UPPER_NODE_LIMIT-t.ADAPTATION_LOWER_NODE_LIMIT)*(1-t.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=t.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(this.getAllNodes().length*5,this.maxIterations),this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},a.prototype.calcSpringForces=function(){for(var s=this.getAllEdges(),c,u=0;u<s.length;u++)c=s[u],this.calcSpringForce(c,c.idealLength)},a.prototype.calcRepulsionForces=function(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,u,v,m,T,y=this.getAllNodes(),A;if(this.useFRGridVariant)for(this.totalIterations%t.GRID_CALCULATION_CHECK_PERIOD==1&&s&&this.updateGrid(),A=new Set,u=0;u<y.length;u++)m=y[u],this.calculateRepulsionForceOfANode(m,A,s,c),A.add(m);else for(u=0;u<y.length;u++)for(m=y[u],v=u+1;v<y.length;v++)T=y[v],m.getOwner()==T.getOwner()&&this.calcRepulsionForce(m,T)},a.prototype.calcGravitationalForces=function(){for(var s,c=this.getAllNodesToApplyGravitation(),u=0;u<c.length;u++)s=c[u],this.calcGravitationalForce(s)},a.prototype.moveNodes=function(){for(var s=this.getAllNodes(),c,u=0;u<s.length;u++)c=s[u],c.move()},a.prototype.calcSpringForce=function(s,c){var u=s.getSource(),v=s.getTarget(),m,T,y,A;if(this.uniformLeafNodeSizes&&u.getChild()==null&&v.getChild()==null)s.updateLengthSimple();else if(s.updateLength(),s.isOverlapingSourceAndTarget)return;m=s.getLength(),m!=0&&(T=this.springConstant*(m-c),y=T*(s.lengthX/m),A=T*(s.lengthY/m),u.springForceX+=y,u.springForceY+=A,v.springForceX-=y,v.springForceY-=A)},a.prototype.calcRepulsionForce=function(s,c){var u=s.getRect(),v=c.getRect(),m=new Array(2),T=new Array(4),y,A,O,R,n,h,g;if(u.intersects(v)){l.calcSeparationAmount(u,v,m,t.DEFAULT_EDGE_LENGTH/2),h=2*m[0],g=2*m[1];var p=s.noOfChildren*c.noOfChildren/(s.noOfChildren+c.noOfChildren);s.repulsionForceX-=p*h,s.repulsionForceY-=p*g,c.repulsionForceX+=p*h,c.repulsionForceY+=p*g}else this.uniformLeafNodeSizes&&s.getChild()==null&&c.getChild()==null?(y=v.getCenterX()-u.getCenterX(),A=v.getCenterY()-u.getCenterY()):(l.getIntersection(u,v,T),y=T[2]-T[0],A=T[3]-T[1]),Math.abs(y)<t.MIN_REPULSION_DIST&&(y=f.sign(y)*t.MIN_REPULSION_DIST),Math.abs(A)<t.MIN_REPULSION_DIST&&(A=f.sign(A)*t.MIN_REPULSION_DIST),O=y*y+A*A,R=Math.sqrt(O),n=this.repulsionConstant*s.noOfChildren*c.noOfChildren/O,h=n*y/R,g=n*A/R,s.repulsionForceX-=h,s.repulsionForceY-=g,c.repulsionForceX+=h,c.repulsionForceY+=g},a.prototype.calcGravitationalForce=function(s){var c,u,v,m,T,y,A,O;c=s.getOwner(),u=(c.getRight()+c.getLeft())/2,v=(c.getTop()+c.getBottom())/2,m=s.getCenterX()-u,T=s.getCenterY()-v,y=Math.abs(m)+s.getWidth()/2,A=Math.abs(T)+s.getHeight()/2,s.getOwner()==this.graphManager.getRoot()?(O=c.getEstimatedSize()*this.gravityRangeFactor,(y>O||A>O)&&(s.gravitationForceX=-this.gravityConstant*m,s.gravitationForceY=-this.gravityConstant*T)):(O=c.getEstimatedSize()*this.compoundGravityRangeFactor,(y>O||A>O)&&(s.gravitationForceX=-this.gravityConstant*m*this.compoundGravityConstant,s.gravitationForceY=-this.gravityConstant*T*this.compoundGravityConstant))},a.prototype.isConverged=function(){var s,c=!1;return this.totalIterations>this.maxIterations/3&&(c=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),s=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,s||c},a.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},a.prototype.calcNoOfChildrenForAllNodes=function(){for(var s,c=this.graphManager.getAllNodes(),u=0;u<c.length;u++)s=c[u],s.noOfChildren=s.getNoOfChildren()},a.prototype.calcGrid=function(s){var c=0,u=0;c=parseInt(Math.ceil((s.getRight()-s.getLeft())/this.repulsionRange)),u=parseInt(Math.ceil((s.getBottom()-s.getTop())/this.repulsionRange));for(var v=new Array(c),m=0;m<c;m++)v[m]=new Array(u);for(var m=0;m<c;m++)for(var T=0;T<u;T++)v[m][T]=new Array;return v},a.prototype.addNodeToGrid=function(s,c,u){var v=0,m=0,T=0,y=0;v=parseInt(Math.floor((s.getRect().x-c)/this.repulsionRange)),m=parseInt(Math.floor((s.getRect().width+s.getRect().x-c)/this.repulsionRange)),T=parseInt(Math.floor((s.getRect().y-u)/this.repulsionRange)),y=parseInt(Math.floor((s.getRect().height+s.getRect().y-u)/this.repulsionRange));for(var A=v;A<=m;A++)for(var O=T;O<=y;O++)this.grid[A][O].push(s),s.setGridCoordinates(v,m,T,y)},a.prototype.updateGrid=function(){var s,c,u=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),s=0;s<u.length;s++)c=u[s],this.addNodeToGrid(c,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},a.prototype.calculateRepulsionForceOfANode=function(s,c,u,v){if(this.totalIterations%t.GRID_CALCULATION_CHECK_PERIOD==1&&u||v){var m=new Set;s.surrounding=new Array;for(var T,y=this.grid,A=s.startX-1;A<s.finishX+2;A++)for(var O=s.startY-1;O<s.finishY+2;O++)if(!(A<0||O<0||A>=y.length||O>=y[0].length)){for(var R=0;R<y[A][O].length;R++)if(T=y[A][O][R],!(s.getOwner()!=T.getOwner()||s==T)&&!c.has(T)&&!m.has(T)){var n=Math.abs(s.getCenterX()-T.getCenterX())-(s.getWidth()/2+T.getWidth()/2),h=Math.abs(s.getCenterY()-T.getCenterY())-(s.getHeight()/2+T.getHeight()/2);n<=this.repulsionRange&&h<=this.repulsionRange&&m.add(T)}}s.surrounding=[].concat(i(m))}for(A=0;A<s.surrounding.length;A++)this.calcRepulsionForce(s,s.surrounding[A])},a.prototype.calcRepulsionRange=function(){return 0},E.exports=a},function(E,I,o){"use strict";var i=o(1),e=o(7);function t(l,f,a){i.call(this,l,f,a),this.idealLength=e.DEFAULT_EDGE_LENGTH}L(t,"FDLayoutEdge"),t.prototype=Object.create(i.prototype);for(var r in i)t[r]=i[r];E.exports=t},function(E,I,o){"use strict";var i=o(3);function e(r,l,f,a){i.call(this,r,l,f,a),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}L(e,"FDLayoutNode"),e.prototype=Object.create(i.prototype);for(var t in i)e[t]=i[t];e.prototype.setGridCoordinates=function(r,l,f,a){this.startX=r,this.finishX=l,this.startY=f,this.finishY=a},E.exports=e},function(E,I,o){"use strict";function i(e,t){this.width=0,this.height=0,e!==null&&t!==null&&(this.height=t,this.width=e)}L(i,"DimensionD"),i.prototype.getWidth=function(){return this.width},i.prototype.setWidth=function(e){this.width=e},i.prototype.getHeight=function(){return this.height},i.prototype.setHeight=function(e){this.height=e},E.exports=i},function(E,I,o){"use strict";var i=o(14);function e(){this.map={},this.keys=[]}L(e,"HashMap"),e.prototype.put=function(t,r){var l=i.createID(t);this.contains(l)||(this.map[l]=r,this.keys.push(t))},e.prototype.contains=function(t){var r=i.createID(t);return this.map[t]!=null},e.prototype.get=function(t){var r=i.createID(t);return this.map[r]},e.prototype.keySet=function(){return this.keys},E.exports=e},function(E,I,o){"use strict";var i=o(14);function e(){this.set={}}L(e,"HashSet"),e.prototype.add=function(t){var r=i.createID(t);this.contains(r)||(this.set[r]=t)},e.prototype.remove=function(t){delete this.set[i.createID(t)]},e.prototype.clear=function(){this.set={}},e.prototype.contains=function(t){return this.set[i.createID(t)]==t},e.prototype.isEmpty=function(){return this.size()===0},e.prototype.size=function(){return Object.keys(this.set).length},e.prototype.addAllTo=function(t){for(var r=Object.keys(this.set),l=r.length,f=0;f<l;f++)t.push(this.set[r[f]])},e.prototype.size=function(){return Object.keys(this.set).length},e.prototype.addAll=function(t){for(var r=t.length,l=0;l<r;l++){var f=t[l];this.add(f)}},E.exports=e},function(E,I,o){"use strict";var i=function(){function l(f,a){for(var d=0;d<a.length;d++){var s=a[d];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(f,s.key,s)}}return L(l,"defineProperties"),function(f,a,d){return a&&l(f.prototype,a),d&&l(f,d),f}}();function e(l,f){if(!(l instanceof f))throw new TypeError("Cannot call a class as a function")}L(e,"_classCallCheck");var t=o(11),r=function(){function l(f,a){e(this,l),(a!==null||a!==void 0)&&(this.compareFunction=this._defaultCompareFunction);var d=void 0;f instanceof t?d=f.size():d=f.length,this._quicksort(f,0,d-1)}return L(l,"Quicksort"),i(l,[{key:"_quicksort",value:L(function(a,d,s){if(d<s){var c=this._partition(a,d,s);this._quicksort(a,d,c),this._quicksort(a,c+1,s)}},"_quicksort")},{key:"_partition",value:L(function(a,d,s){for(var c=this._get(a,d),u=d,v=s;;){for(;this.compareFunction(c,this._get(a,v));)v--;for(;this.compareFunction(this._get(a,u),c);)u++;if(u<v)this._swap(a,u,v),u++,v--;else return v}},"_partition")},{key:"_get",value:L(function(a,d){return a instanceof t?a.get_object_at(d):a[d]},"_get")},{key:"_set",value:L(function(a,d,s){a instanceof t?a.set_object_at(d,s):a[d]=s},"_set")},{key:"_swap",value:L(function(a,d,s){var c=this._get(a,d);this._set(a,d,this._get(a,s)),this._set(a,s,c)},"_swap")},{key:"_defaultCompareFunction",value:L(function(a,d){return d>a},"_defaultCompareFunction")}]),l}();E.exports=r},function(E,I,o){"use strict";var i=function(){function r(l,f){for(var a=0;a<f.length;a++){var d=f[a];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(l,d.key,d)}}return L(r,"defineProperties"),function(l,f,a){return f&&r(l.prototype,f),a&&r(l,a),l}}();function e(r,l){if(!(r instanceof l))throw new TypeError("Cannot call a class as a function")}L(e,"_classCallCheck");var t=function(){function r(l,f){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,d=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1,s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1;e(this,r),this.sequence1=l,this.sequence2=f,this.match_score=a,this.mismatch_penalty=d,this.gap_penalty=s,this.iMax=l.length+1,this.jMax=f.length+1,this.grid=new Array(this.iMax);for(var c=0;c<this.iMax;c++){this.grid[c]=new Array(this.jMax);for(var u=0;u<this.jMax;u++)this.grid[c][u]=0}this.tracebackGrid=new Array(this.iMax);for(var v=0;v<this.iMax;v++){this.tracebackGrid[v]=new Array(this.jMax);for(var m=0;m<this.jMax;m++)this.tracebackGrid[v][m]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return L(r,"NeedlemanWunsch"),i(r,[{key:"getScore",value:L(function(){return this.score},"getScore")},{key:"getAlignments",value:L(function(){return this.alignments},"getAlignments")},{key:"computeGrids",value:L(function(){for(var f=1;f<this.jMax;f++)this.grid[0][f]=this.grid[0][f-1]+this.gap_penalty,this.tracebackGrid[0][f]=[!1,!1,!0];for(var a=1;a<this.iMax;a++)this.grid[a][0]=this.grid[a-1][0]+this.gap_penalty,this.tracebackGrid[a][0]=[!1,!0,!1];for(var d=1;d<this.iMax;d++)for(var s=1;s<this.jMax;s++){var c=void 0;this.sequence1[d-1]===this.sequence2[s-1]?c=this.grid[d-1][s-1]+this.match_score:c=this.grid[d-1][s-1]+this.mismatch_penalty;var u=this.grid[d-1][s]+this.gap_penalty,v=this.grid[d][s-1]+this.gap_penalty,m=[c,u,v],T=this.arrayAllMaxIndexes(m);this.grid[d][s]=m[T[0]],this.tracebackGrid[d][s]=[T.includes(0),T.includes(1),T.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]},"computeGrids")},{key:"alignmentTraceback",value:L(function(){var f=[];for(f.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});f[0];){var a=f[0],d=this.tracebackGrid[a.pos[0]][a.pos[1]];d[0]&&f.push({pos:[a.pos[0]-1,a.pos[1]-1],seq1:this.sequence1[a.pos[0]-1]+a.seq1,seq2:this.sequence2[a.pos[1]-1]+a.seq2}),d[1]&&f.push({pos:[a.pos[0]-1,a.pos[1]],seq1:this.sequence1[a.pos[0]-1]+a.seq1,seq2:"-"+a.seq2}),d[2]&&f.push({pos:[a.pos[0],a.pos[1]-1],seq1:"-"+a.seq1,seq2:this.sequence2[a.pos[1]-1]+a.seq2}),a.pos[0]===0&&a.pos[1]===0&&this.alignments.push({sequence1:a.seq1,sequence2:a.seq2}),f.shift()}return this.alignments},"alignmentTraceback")},{key:"getAllIndexes",value:L(function(f,a){for(var d=[],s=-1;(s=f.indexOf(a,s+1))!==-1;)d.push(s);return d},"getAllIndexes")},{key:"arrayAllMaxIndexes",value:L(function(f){return this.getAllIndexes(f,Math.max.apply(null,f))},"arrayAllMaxIndexes")}]),r}();E.exports=t},function(E,I,o){"use strict";var i=L(function(){},"layoutBase");i.FDLayout=o(18),i.FDLayoutConstants=o(7),i.FDLayoutEdge=o(19),i.FDLayoutNode=o(20),i.DimensionD=o(21),i.HashMap=o(22),i.HashSet=o(23),i.IGeometry=o(8),i.IMath=o(9),i.Integer=o(10),i.Point=o(12),i.PointD=o(4),i.RandomSeed=o(16),i.RectangleD=o(13),i.Transform=o(17),i.UniqueIDGeneretor=o(14),i.Quicksort=o(24),i.LinkedList=o(11),i.LGraphObject=o(2),i.LGraph=o(5),i.LEdge=o(1),i.LGraphManager=o(6),i.LNode=o(3),i.Layout=o(15),i.LayoutConstants=o(0),i.NeedlemanWunsch=o(25),E.exports=i},function(E,I,o){"use strict";function i(){this.listeners=[]}L(i,"Emitter");var e=i.prototype;e.addListener=function(t,r){this.listeners.push({event:t,callback:r})},e.removeListener=function(t,r){for(var l=this.listeners.length;l>=0;l--){var f=this.listeners[l];f.event===t&&f.callback===r&&this.listeners.splice(l,1)}},e.emit=function(t,r){for(var l=0;l<this.listeners.length;l++){var f=this.listeners[l];t===f.event&&f.callback(r)}},E.exports=i}])})});var vt=lt((it,dt)=>{"use strict";L(function(I,o){typeof it=="object"&&typeof dt=="object"?dt.exports=o(pt()):typeof define=="function"&&define.amd?define(["layout-base"],o):typeof it=="object"?it.coseBase=o(pt()):I.coseBase=o(I.layoutBase)},"webpackUniversalModuleDefinition")(it,function(E){return function(I){var o={};function i(e){if(o[e])return o[e].exports;var t=o[e]={i:e,l:!1,exports:{}};return I[e].call(t.exports,t,t.exports,i),t.l=!0,t.exports}return L(i,"__webpack_require__"),i.m=I,i.c=o,i.i=function(e){return e},i.d=function(e,t,r){i.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:r})},i.n=function(e){var t=e&&e.__esModule?L(function(){return e.default},"getDefault"):L(function(){return e},"getModuleExports");return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=7)}([function(I,o){I.exports=E},function(I,o,i){"use strict";var e=i(0).FDLayoutConstants;function t(){}L(t,"CoSEConstants");for(var r in e)t[r]=e[r];t.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,t.DEFAULT_RADIAL_SEPARATION=e.DEFAULT_EDGE_LENGTH,t.DEFAULT_COMPONENT_SEPERATION=60,t.TILE=!0,t.TILING_PADDING_VERTICAL=10,t.TILING_PADDING_HORIZONTAL=10,t.TREE_REDUCTION_ON_INCREMENTAL=!1,I.exports=t},function(I,o,i){"use strict";var e=i(0).FDLayoutEdge;function t(l,f,a){e.call(this,l,f,a)}L(t,"CoSEEdge"),t.prototype=Object.create(e.prototype);for(var r in e)t[r]=e[r];I.exports=t},function(I,o,i){"use strict";var e=i(0).LGraph;function t(l,f,a){e.call(this,l,f,a)}L(t,"CoSEGraph"),t.prototype=Object.create(e.prototype);for(var r in e)t[r]=e[r];I.exports=t},function(I,o,i){"use strict";var e=i(0).LGraphManager;function t(l){e.call(this,l)}L(t,"CoSEGraphManager"),t.prototype=Object.create(e.prototype);for(var r in e)t[r]=e[r];I.exports=t},function(I,o,i){"use strict";var e=i(0).FDLayoutNode,t=i(0).IMath;function r(f,a,d,s){e.call(this,f,a,d,s)}L(r,"CoSENode"),r.prototype=Object.create(e.prototype);for(var l in e)r[l]=e[l];r.prototype.move=function(){var f=this.graphManager.getLayout();this.displacementX=f.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY=f.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren,Math.abs(this.displacementX)>f.coolingFactor*f.maxNodeDisplacement&&(this.displacementX=f.coolingFactor*f.maxNodeDisplacement*t.sign(this.displacementX)),Math.abs(this.displacementY)>f.coolingFactor*f.maxNodeDisplacement&&(this.displacementY=f.coolingFactor*f.maxNodeDisplacement*t.sign(this.displacementY)),this.child==null?this.moveBy(this.displacementX,this.displacementY):this.child.getNodes().length==0?this.moveBy(this.displacementX,this.displacementY):this.propogateDisplacementToChildren(this.displacementX,this.displacementY),f.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},r.prototype.propogateDisplacementToChildren=function(f,a){for(var d=this.getChild().getNodes(),s,c=0;c<d.length;c++)s=d[c],s.getChild()==null?(s.moveBy(f,a),s.displacementX+=f,s.displacementY+=a):s.propogateDisplacementToChildren(f,a)},r.prototype.setPred1=function(f){this.pred1=f},r.prototype.getPred1=function(){return pred1},r.prototype.getPred2=function(){return pred2},r.prototype.setNext=function(f){this.next=f},r.prototype.getNext=function(){return next},r.prototype.setProcessed=function(f){this.processed=f},r.prototype.isProcessed=function(){return processed},I.exports=r},function(I,o,i){"use strict";var e=i(0).FDLayout,t=i(4),r=i(3),l=i(5),f=i(2),a=i(1),d=i(0).FDLayoutConstants,s=i(0).LayoutConstants,c=i(0).Point,u=i(0).PointD,v=i(0).Layout,m=i(0).Integer,T=i(0).IGeometry,y=i(0).LGraph,A=i(0).Transform;function O(){e.call(this),this.toBeTiled={}}L(O,"CoSELayout"),O.prototype=Object.create(e.prototype);for(var R in e)O[R]=e[R];O.prototype.newGraphManager=function(){var n=new t(this);return this.graphManager=n,n},O.prototype.newGraph=function(n){return new r(null,this.graphManager,n)},O.prototype.newNode=function(n){return new l(this.graphManager,n)},O.prototype.newEdge=function(n){return new f(null,null,n)},O.prototype.initParameters=function(){e.prototype.initParameters.call(this,arguments),this.isSubLayout||(a.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=a.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=a.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.springConstant=d.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=d.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=d.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=d.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=d.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=d.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1,this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/d.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=d.CONVERGENCE_CHECK_PERIOD/this.maxIterations,this.coolingAdjuster=1)},O.prototype.layout=function(){var n=s.DEFAULT_CREATE_BENDS_AS_NEEDED;return n&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},O.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental){if(a.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var h=new Set(this.getAllNodes()),g=this.nodesWithGravity.filter(function(N){return h.has(N)});this.graphManager.setAllNodesToApplyGravitation(g)}}else{var n=this.getFlatForest();if(n.length>0)this.positionNodesRadially(n);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var h=new Set(this.getAllNodes()),g=this.nodesWithGravity.filter(function(p){return h.has(p)});this.graphManager.setAllNodesToApplyGravitation(g),this.positionNodesRandomly()}}return this.initSpringEmbedder(),this.runSpringEmbedder(),!0},O.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished)if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;if(this.totalIterations%d.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged())if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;this.coolingCycle++,this.layoutQuality==0?this.coolingAdjuster=this.coolingCycle:this.layoutQuality==1&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var n=new Set(this.getAllNodes()),h=this.nodesWithGravity.filter(function(D){return n.has(D)});this.graphManager.setAllNodesToApplyGravitation(h),this.graphManager.updateBounds(),this.updateGrid(),this.coolingFactor=d.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),this.coolingFactor=d.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var g=!this.isTreeGrowing&&!this.isGrowthFinished,p=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(g,p),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},O.prototype.getPositionsData=function(){for(var n=this.graphManager.getAllNodes(),h={},g=0;g<n.length;g++){var p=n[g].rect,D=n[g].id;h[D]={id:D,x:p.getCenterX(),y:p.getCenterY(),w:p.width,h:p.height}}return h},O.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var n=!1;if(d.ANIMATE==="during")this.emit("layoutstarted");else{for(;!n;)n=this.tick();this.graphManager.updateBounds()}},O.prototype.calculateNodesToApplyGravitationTo=function(){var n=[],h,g=this.graphManager.getGraphs(),p=g.length,D;for(D=0;D<p;D++)h=g[D],h.updateConnected(),h.isConnected||(n=n.concat(h.getNodes()));return n},O.prototype.createBendpoints=function(){var n=[];n=n.concat(this.graphManager.getAllEdges());var h=new Set,g;for(g=0;g<n.length;g++){var p=n[g];if(!h.has(p)){var D=p.getSource(),N=p.getTarget();if(D==N)p.getBendpoints().push(new u),p.getBendpoints().push(new u),this.createDummyNodesForBendpoints(p),h.add(p);else{var M=[];if(M=M.concat(D.getEdgeListToNode(N)),M=M.concat(N.getEdgeListToNode(D)),!h.has(M[0])){if(M.length>1){var C;for(C=0;C<M.length;C++){var w=M[C];w.getBendpoints().push(new u),this.createDummyNodesForBendpoints(w)}}M.forEach(function(P){h.add(P)})}}}if(h.size==n.length)break}},O.prototype.positionNodesRadially=function(n){for(var h=new c(0,0),g=Math.ceil(Math.sqrt(n.length)),p=0,D=0,N=0,M=new u(0,0),C=0;C<n.length;C++){C%g==0&&(N=0,D=p,C!=0&&(D+=a.DEFAULT_COMPONENT_SEPERATION),p=0);var w=n[C],P=v.findCenterOfTree(w);h.x=N,h.y=D,M=O.radialLayout(w,P,h),M.y>p&&(p=Math.floor(M.y)),N=Math.floor(M.x+a.DEFAULT_COMPONENT_SEPERATION)}this.transform(new u(s.WORLD_CENTER_X-M.x/2,s.WORLD_CENTER_Y-M.y/2))},O.radialLayout=function(n,h,g){var p=Math.max(this.maxDiagonalInTree(n),a.DEFAULT_RADIAL_SEPARATION);O.branchRadialLayout(h,null,0,359,0,p);var D=y.calculateBounds(n),N=new A;N.setDeviceOrgX(D.getMinX()),N.setDeviceOrgY(D.getMinY()),N.setWorldOrgX(g.x),N.setWorldOrgY(g.y);for(var M=0;M<n.length;M++){var C=n[M];C.transform(N)}var w=new u(D.getMaxX(),D.getMaxY());return N.inverseTransformPoint(w)},O.branchRadialLayout=function(n,h,g,p,D,N){var M=(p-g+1)/2;M<0&&(M+=180);var C=(M+g)%360,w=C*T.TWO_PI/360,P=Math.cos(w),U=D*Math.cos(w),F=D*Math.sin(w);n.setCenter(U,F);var x=[];x=x.concat(n.getEdges());var G=x.length;h!=null&&G--;for(var S=0,_=x.length,X,b=n.getEdgesBetween(h);b.length>1;){var H=b[0];b.splice(0,1);var z=x.indexOf(H);z>=0&&x.splice(z,1),_--,G--}h!=null?X=(x.indexOf(b[0])+1)%_:X=0;for(var B=Math.abs(p-g)/G,Y=X;S!=G;Y=++Y%_){var K=x[Y].getOtherEnd(n);if(K!=h){var j=(g+S*B)%360,$=(j+B)%360;O.branchRadialLayout(K,n,j,$,D+N,N),S++}}},O.maxDiagonalInTree=function(n){for(var h=m.MIN_VALUE,g=0;g<n.length;g++){var p=n[g],D=p.getDiagonal();D>h&&(h=D)}return h},O.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},O.prototype.groupZeroDegreeMembers=function(){var n=this,h={};this.memberGroups={},this.idToDummyNode={};for(var g=[],p=this.graphManager.getAllNodes(),D=0;D<p.length;D++){var N=p[D],M=N.getParent();this.getNodeDegreeWithChildren(N)===0&&(M.id==null||!this.getToBeTiled(M))&&g.push(N)}for(var D=0;D<g.length;D++){var N=g[D],C=N.getParent().id;typeof h[C]>"u"&&(h[C]=[]),h[C]=h[C].concat(N)}Object.keys(h).forEach(function(w){if(h[w].length>1){var P="DummyCompound_"+w;n.memberGroups[P]=h[w];var U=h[w][0].getParent(),F=new l(n.graphManager);F.id=P,F.paddingLeft=U.paddingLeft||0,F.paddingRight=U.paddingRight||0,F.paddingBottom=U.paddingBottom||0,F.paddingTop=U.paddingTop||0,n.idToDummyNode[P]=F;var x=n.getGraphManager().add(n.newGraph(),F),G=U.getChild();G.add(F);for(var S=0;S<h[w].length;S++){var _=h[w][S];G.remove(_),x.add(_)}}})},O.prototype.clearCompounds=function(){var n={},h={};this.performDFSOnCompounds();for(var g=0;g<this.compoundOrder.length;g++)h[this.compoundOrder[g].id]=this.compoundOrder[g],n[this.compoundOrder[g].id]=[].concat(this.compoundOrder[g].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[g].getChild()),this.compoundOrder[g].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(n,h)},O.prototype.clearZeroDegreeMembers=function(){var n=this,h=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(g){var p=n.idToDummyNode[g];h[g]=n.tileNodes(n.memberGroups[g],p.paddingLeft+p.paddingRight),p.rect.width=h[g].width,p.rect.height=h[g].height})},O.prototype.repopulateCompounds=function(){for(var n=this.compoundOrder.length-1;n>=0;n--){var h=this.compoundOrder[n],g=h.id,p=h.paddingLeft,D=h.paddingTop;this.adjustLocations(this.tiledMemberPack[g],h.rect.x,h.rect.y,p,D)}},O.prototype.repopulateZeroDegreeMembers=function(){var n=this,h=this.tiledZeroDegreePack;Object.keys(h).forEach(function(g){var p=n.idToDummyNode[g],D=p.paddingLeft,N=p.paddingTop;n.adjustLocations(h[g],p.rect.x,p.rect.y,D,N)})},O.prototype.getToBeTiled=function(n){var h=n.id;if(this.toBeTiled[h]!=null)return this.toBeTiled[h];var g=n.getChild();if(g==null)return this.toBeTiled[h]=!1,!1;for(var p=g.getNodes(),D=0;D<p.length;D++){var N=p[D];if(this.getNodeDegree(N)>0)return this.toBeTiled[h]=!1,!1;if(N.getChild()==null){this.toBeTiled[N.id]=!1;continue}if(!this.getToBeTiled(N))return this.toBeTiled[h]=!1,!1}return this.toBeTiled[h]=!0,!0},O.prototype.getNodeDegree=function(n){for(var h=n.id,g=n.getEdges(),p=0,D=0;D<g.length;D++){var N=g[D];N.getSource().id!==N.getTarget().id&&(p=p+1)}return p},O.prototype.getNodeDegreeWithChildren=function(n){var h=this.getNodeDegree(n);if(n.getChild()==null)return h;for(var g=n.getChild().getNodes(),p=0;p<g.length;p++){var D=g[p];h+=this.getNodeDegreeWithChildren(D)}return h},O.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},O.prototype.fillCompexOrderByDFS=function(n){for(var h=0;h<n.length;h++){var g=n[h];g.getChild()!=null&&this.fillCompexOrderByDFS(g.getChild().getNodes()),this.getToBeTiled(g)&&this.compoundOrder.push(g)}},O.prototype.adjustLocations=function(n,h,g,p,D){h+=p,g+=D;for(var N=h,M=0;M<n.rows.length;M++){var C=n.rows[M];h=N;for(var w=0,P=0;P<C.length;P++){var U=C[P];U.rect.x=h,U.rect.y=g,h+=U.rect.width+n.horizontalPadding,U.rect.height>w&&(w=U.rect.height)}g+=w+n.verticalPadding}},O.prototype.tileCompoundMembers=function(n,h){var g=this;this.tiledMemberPack=[],Object.keys(n).forEach(function(p){var D=h[p];g.tiledMemberPack[p]=g.tileNodes(n[p],D.paddingLeft+D.paddingRight),D.rect.width=g.tiledMemberPack[p].width,D.rect.height=g.tiledMemberPack[p].height})},O.prototype.tileNodes=function(n,h){var g=a.TILING_PADDING_VERTICAL,p=a.TILING_PADDING_HORIZONTAL,D={rows:[],rowWidth:[],rowHeight:[],width:0,height:h,verticalPadding:g,horizontalPadding:p};n.sort(function(C,w){return C.rect.width*C.rect.height>w.rect.width*w.rect.height?-1:C.rect.width*C.rect.height<w.rect.width*w.rect.height?1:0});for(var N=0;N<n.length;N++){var M=n[N];D.rows.length==0?this.insertNodeToRow(D,M,0,h):this.canAddHorizontal(D,M.rect.width,M.rect.height)?this.insertNodeToRow(D,M,this.getShortestRowIndex(D),h):this.insertNodeToRow(D,M,D.rows.length,h),this.shiftToLastRow(D)}return D},O.prototype.insertNodeToRow=function(n,h,g,p){var D=p;if(g==n.rows.length){var N=[];n.rows.push(N),n.rowWidth.push(D),n.rowHeight.push(0)}var M=n.rowWidth[g]+h.rect.width;n.rows[g].length>0&&(M+=n.horizontalPadding),n.rowWidth[g]=M,n.width<M&&(n.width=M);var C=h.rect.height;g>0&&(C+=n.verticalPadding);var w=0;C>n.rowHeight[g]&&(w=n.rowHeight[g],n.rowHeight[g]=C,w=n.rowHeight[g]-w),n.height+=w,n.rows[g].push(h)},O.prototype.getShortestRowIndex=function(n){for(var h=-1,g=Number.MAX_VALUE,p=0;p<n.rows.length;p++)n.rowWidth[p]<g&&(h=p,g=n.rowWidth[p]);return h},O.prototype.getLongestRowIndex=function(n){for(var h=-1,g=Number.MIN_VALUE,p=0;p<n.rows.length;p++)n.rowWidth[p]>g&&(h=p,g=n.rowWidth[p]);return h},O.prototype.canAddHorizontal=function(n,h,g){var p=this.getShortestRowIndex(n);if(p<0)return!0;var D=n.rowWidth[p];if(D+n.horizontalPadding+h<=n.width)return!0;var N=0;n.rowHeight[p]<g&&p>0&&(N=g+n.verticalPadding-n.rowHeight[p]);var M;n.width-D>=h+n.horizontalPadding?M=(n.height+N)/(D+h+n.horizontalPadding):M=(n.height+N)/n.width,N=g+n.verticalPadding;var C;return n.width<h?C=(n.height+N)/h:C=(n.height+N)/n.width,C<1&&(C=1/C),M<1&&(M=1/M),M<C},O.prototype.shiftToLastRow=function(n){var h=this.getLongestRowIndex(n),g=n.rowWidth.length-1,p=n.rows[h],D=p[p.length-1],N=D.width+n.horizontalPadding;if(n.width-n.rowWidth[g]>N&&h!=g){p.splice(-1,1),n.rows[g].push(D),n.rowWidth[h]=n.rowWidth[h]-N,n.rowWidth[g]=n.rowWidth[g]+N,n.width=n.rowWidth[instance.getLongestRowIndex(n)];for(var M=Number.MIN_VALUE,C=0;C<p.length;C++)p[C].height>M&&(M=p[C].height);h>0&&(M+=n.verticalPadding);var w=n.rowHeight[h]+n.rowHeight[g];n.rowHeight[h]=M,n.rowHeight[g]<D.height+n.verticalPadding&&(n.rowHeight[g]=D.height+n.verticalPadding);var P=n.rowHeight[h]+n.rowHeight[g];n.height+=P-w,this.shiftToLastRow(n)}},O.prototype.tilingPreLayout=function(){a.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},O.prototype.tilingPostLayout=function(){a.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},O.prototype.reduceTrees=function(){for(var n=[],h=!0,g;h;){var p=this.graphManager.getAllNodes(),D=[];h=!1;for(var N=0;N<p.length;N++)g=p[N],g.getEdges().length==1&&!g.getEdges()[0].isInterGraph&&g.getChild()==null&&(D.push([g,g.getEdges()[0],g.getOwner()]),h=!0);if(h==!0){for(var M=[],C=0;C<D.length;C++)D[C][0].getEdges().length==1&&(M.push(D[C]),D[C][0].getOwner().remove(D[C][0]));n.push(M),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=n},O.prototype.growTree=function(n){for(var h=n.length,g=n[h-1],p,D=0;D<g.length;D++)p=g[D],this.findPlaceforPrunedNode(p),p[2].add(p[0]),p[2].add(p[1],p[1].source,p[1].target);n.splice(n.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},O.prototype.findPlaceforPrunedNode=function(n){var h,g,p=n[0];p==n[1].source?g=n[1].target:g=n[1].source;var D=g.startX,N=g.finishX,M=g.startY,C=g.finishY,w=0,P=0,U=0,F=0,x=[w,U,P,F];if(M>0)for(var G=D;G<=N;G++)x[0]+=this.grid[G][M-1].length+this.grid[G][M].length-1;if(N<this.grid.length-1)for(var G=M;G<=C;G++)x[1]+=this.grid[N+1][G].length+this.grid[N][G].length-1;if(C<this.grid[0].length-1)for(var G=D;G<=N;G++)x[2]+=this.grid[G][C+1].length+this.grid[G][C].length-1;if(D>0)for(var G=M;G<=C;G++)x[3]+=this.grid[D-1][G].length+this.grid[D][G].length-1;for(var S=m.MAX_VALUE,_,X,b=0;b<x.length;b++)x[b]<S?(S=x[b],_=1,X=b):x[b]==S&&_++;if(_==3&&S==0)x[0]==0&&x[1]==0&&x[2]==0?h=1:x[0]==0&&x[1]==0&&x[3]==0?h=0:x[0]==0&&x[2]==0&&x[3]==0?h=3:x[1]==0&&x[2]==0&&x[3]==0&&(h=2);else if(_==2&&S==0){var H=Math.floor(Math.random()*2);x[0]==0&&x[1]==0?H==0?h=0:h=1:x[0]==0&&x[2]==0?H==0?h=0:h=2:x[0]==0&&x[3]==0?H==0?h=0:h=3:x[1]==0&&x[2]==0?H==0?h=1:h=2:x[1]==0&&x[3]==0?H==0?h=1:h=3:H==0?h=2:h=3}else if(_==4&&S==0){var H=Math.floor(Math.random()*4);h=H}else h=X;h==0?p.setCenter(g.getCenterX(),g.getCenterY()-g.getHeight()/2-d.DEFAULT_EDGE_LENGTH-p.getHeight()/2):h==1?p.setCenter(g.getCenterX()+g.getWidth()/2+d.DEFAULT_EDGE_LENGTH+p.getWidth()/2,g.getCenterY()):h==2?p.setCenter(g.getCenterX(),g.getCenterY()+g.getHeight()/2+d.DEFAULT_EDGE_LENGTH+p.getHeight()/2):p.setCenter(g.getCenterX()-g.getWidth()/2-d.DEFAULT_EDGE_LENGTH-p.getWidth()/2,g.getCenterY())},I.exports=O},function(I,o,i){"use strict";var e={};e.layoutBase=i(0),e.CoSEConstants=i(1),e.CoSEEdge=i(2),e.CoSEGraph=i(3),e.CoSEGraphManager=i(4),e.CoSELayout=i(6),e.CoSENode=i(5),I.exports=e}])})});var xt=lt((nt,yt)=>{"use strict";L(function(I,o){typeof nt=="object"&&typeof yt=="object"?yt.exports=o(vt()):typeof define=="function"&&define.amd?define(["cose-base"],o):typeof nt=="object"?nt.cytoscapeCoseBilkent=o(vt()):I.cytoscapeCoseBilkent=o(I.coseBase)},"webpackUniversalModuleDefinition")(nt,function(E){return function(I){var o={};function i(e){if(o[e])return o[e].exports;var t=o[e]={i:e,l:!1,exports:{}};return I[e].call(t.exports,t,t.exports,i),t.l=!0,t.exports}return L(i,"__webpack_require__"),i.m=I,i.c=o,i.i=function(e){return e},i.d=function(e,t,r){i.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:r})},i.n=function(e){var t=e&&e.__esModule?L(function(){return e.default},"getDefault"):L(function(){return e},"getModuleExports");return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=1)}([function(I,o){I.exports=E},function(I,o,i){"use strict";var e=i(0).layoutBase.LayoutConstants,t=i(0).layoutBase.FDLayoutConstants,r=i(0).CoSEConstants,l=i(0).CoSELayout,f=i(0).CoSENode,a=i(0).layoutBase.PointD,d=i(0).layoutBase.DimensionD,s={ready:L(function(){},"ready"),stop:L(function(){},"stop"),quality:"default",nodeDimensionsIncludeLabels:!1,refresh:30,fit:!0,padding:10,randomize:!0,nodeRepulsion:4500,idealEdgeLength:50,edgeElasticity:.45,nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,animate:"end",animationDuration:500,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.5};function c(T,y){var A={};for(var O in T)A[O]=T[O];for(var O in y)A[O]=y[O];return A}L(c,"extend");function u(T){this.options=c(s,T),v(this.options)}L(u,"_CoSELayout");var v=L(function(y){y.nodeRepulsion!=null&&(r.DEFAULT_REPULSION_STRENGTH=t.DEFAULT_REPULSION_STRENGTH=y.nodeRepulsion),y.idealEdgeLength!=null&&(r.DEFAULT_EDGE_LENGTH=t.DEFAULT_EDGE_LENGTH=y.idealEdgeLength),y.edgeElasticity!=null&&(r.DEFAULT_SPRING_STRENGTH=t.DEFAULT_SPRING_STRENGTH=y.edgeElasticity),y.nestingFactor!=null&&(r.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=t.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=y.nestingFactor),y.gravity!=null&&(r.DEFAULT_GRAVITY_STRENGTH=t.DEFAULT_GRAVITY_STRENGTH=y.gravity),y.numIter!=null&&(r.MAX_ITERATIONS=t.MAX_ITERATIONS=y.numIter),y.gravityRange!=null&&(r.DEFAULT_GRAVITY_RANGE_FACTOR=t.DEFAULT_GRAVITY_RANGE_FACTOR=y.gravityRange),y.gravityCompound!=null&&(r.DEFAULT_COMPOUND_GRAVITY_STRENGTH=t.DEFAULT_COMPOUND_GRAVITY_STRENGTH=y.gravityCompound),y.gravityRangeCompound!=null&&(r.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=t.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=y.gravityRangeCompound),y.initialEnergyOnIncremental!=null&&(r.DEFAULT_COOLING_FACTOR_INCREMENTAL=t.DEFAULT_COOLING_FACTOR_INCREMENTAL=y.initialEnergyOnIncremental),y.quality=="draft"?e.QUALITY=0:y.quality=="proof"?e.QUALITY=2:e.QUALITY=1,r.NODE_DIMENSIONS_INCLUDE_LABELS=t.NODE_DIMENSIONS_INCLUDE_LABELS=e.NODE_DIMENSIONS_INCLUDE_LABELS=y.nodeDimensionsIncludeLabels,r.DEFAULT_INCREMENTAL=t.DEFAULT_INCREMENTAL=e.DEFAULT_INCREMENTAL=!y.randomize,r.ANIMATE=t.ANIMATE=e.ANIMATE=y.animate,r.TILE=y.tile,r.TILING_PADDING_VERTICAL=typeof y.tilingPaddingVertical=="function"?y.tilingPaddingVertical.call():y.tilingPaddingVertical,r.TILING_PADDING_HORIZONTAL=typeof y.tilingPaddingHorizontal=="function"?y.tilingPaddingHorizontal.call():y.tilingPaddingHorizontal},"getUserOptions");u.prototype.run=function(){var T,y,A=this.options,O=this.idToLNode={},R=this.layout=new l,n=this;n.stopped=!1,this.cy=this.options.cy,this.cy.trigger({type:"layoutstart",layout:this});var h=R.newGraphManager();this.gm=h;var g=this.options.eles.nodes(),p=this.options.eles.edges();this.root=h.addRoot(),this.processChildrenList(this.root,this.getTopMostNodes(g),R);for(var D=0;D<p.length;D++){var N=p[D],M=this.idToLNode[N.data("source")],C=this.idToLNode[N.data("target")];if(M!==C&&M.getEdgesBetween(C).length==0){var w=h.add(R.newEdge(),M,C);w.id=N.id()}}var P=L(function(x,G){typeof x=="number"&&(x=G);var S=x.data("id"),_=n.idToLNode[S];return{x:_.getRect().getCenterX(),y:_.getRect().getCenterY()}},"getPositions"),U=L(function F(){for(var x=L(function(){A.fit&&A.cy.fit(A.eles,A.padding),T||(T=!0,n.cy.one("layoutready",A.ready),n.cy.trigger({type:"layoutready",layout:n}))},"afterReposition"),G=n.options.refresh,S,_=0;_<G&&!S;_++)S=n.stopped||n.layout.tick();if(S){R.checkLayoutSuccess()&&!R.isSubLayout&&R.doPostLayout(),R.tilingPostLayout&&R.tilingPostLayout(),R.isLayoutFinished=!0,n.options.eles.nodes().positions(P),x(),n.cy.one("layoutstop",n.options.stop),n.cy.trigger({type:"layoutstop",layout:n}),y&&cancelAnimationFrame(y),T=!1;return}var X=n.layout.getPositionsData();A.eles.nodes().positions(function(b,H){if(typeof b=="number"&&(b=H),!b.isParent()){for(var z=b.id(),B=X[z],Y=b;B==null&&(B=X[Y.data("parent")]||X["DummyCompound_"+Y.data("parent")],X[z]=B,Y=Y.parent()[0],Y!=null););return B!=null?{x:B.x,y:B.y}:{x:b.position("x"),y:b.position("y")}}}),x(),y=requestAnimationFrame(F)},"iterateAnimated");return R.addListener("layoutstarted",function(){n.options.animate==="during"&&(y=requestAnimationFrame(U))}),R.runLayout(),this.options.animate!=="during"&&(n.options.eles.nodes().not(":parent").layoutPositions(n,n.options,P),T=!1),this},u.prototype.getTopMostNodes=function(T){for(var y={},A=0;A<T.length;A++)y[T[A].id()]=!0;var O=T.filter(function(R,n){typeof R=="number"&&(R=n);for(var h=R.parent()[0];h!=null;){if(y[h.id()])return!1;h=h.parent()[0]}return!0});return O},u.prototype.processChildrenList=function(T,y,A){for(var O=y.length,R=0;R<O;R++){var n=y[R],h=n.children(),g,p=n.layoutDimensions({nodeDimensionsIncludeLabels:this.options.nodeDimensionsIncludeLabels});if(n.outerWidth()!=null&&n.outerHeight()!=null?g=T.add(new f(A.graphManager,new a(n.position("x")-p.w/2,n.position("y")-p.h/2),new d(parseFloat(p.w),parseFloat(p.h)))):g=T.add(new f(this.graphManager)),g.id=n.data("id"),g.paddingLeft=parseInt(n.css("padding")),g.paddingTop=parseInt(n.css("padding")),g.paddingRight=parseInt(n.css("padding")),g.paddingBottom=parseInt(n.css("padding")),this.options.nodeDimensionsIncludeLabels&&n.isParent()){var D=n.boundingBox({includeLabels:!0,includeNodes:!1}).w,N=n.boundingBox({includeLabels:!0,includeNodes:!1}).h,M=n.css("text-halign");g.labelWidth=D,g.labelHeight=N,g.labelPos=M}if(this.idToLNode[n.data("id")]=g,isNaN(g.rect.x)&&(g.rect.x=0),isNaN(g.rect.y)&&(g.rect.y=0),h!=null&&h.length>0){var C;C=A.getGraphManager().add(A.newGraph(),g),this.processChildrenList(C,h,A)}}},u.prototype.stop=function(){return this.stopped=!0,this};var m=L(function(y){y("layout","cose-bilkent",u)},"register");typeof cytoscape<"u"&&m(cytoscape),I.exports=m}])})});var gt=function(){var E=L(function(R,n,h,g){for(h=h||{},g=R.length;g--;h[R[g]]=n);return h},"o"),I=[1,4],o=[1,13],i=[1,12],e=[1,15],t=[1,16],r=[1,20],l=[1,19],f=[6,7,8],a=[1,26],d=[1,24],s=[1,25],c=[6,7,11],u=[1,6,13,15,16,19,22],v=[1,33],m=[1,34],T=[1,6,7,11,13,15,16,19,22],y={trace:L(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,MINDMAP:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,ICON:15,CLASS:16,nodeWithId:17,nodeWithoutId:18,NODE_DSTART:19,NODE_DESCR:20,NODE_DEND:21,NODE_ID:22,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"MINDMAP",11:"EOF",13:"SPACELIST",15:"ICON",16:"CLASS",19:"NODE_DSTART",20:"NODE_DESCR",21:"NODE_DEND",22:"NODE_ID"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,2],[12,2],[12,2],[12,1],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[18,3],[17,1],[17,4]],performAction:L(function(n,h,g,p,D,N,M){var C=N.length-1;switch(D){case 6:case 7:return p;case 8:p.getLogger().trace("Stop NL ");break;case 9:p.getLogger().trace("Stop EOF ");break;case 11:p.getLogger().trace("Stop NL2 ");break;case 12:p.getLogger().trace("Stop EOF2 ");break;case 15:p.getLogger().info("Node: ",N[C].id),p.addNode(N[C-1].length,N[C].id,N[C].descr,N[C].type);break;case 16:p.getLogger().trace("Icon: ",N[C]),p.decorateNode({icon:N[C]});break;case 17:case 21:p.decorateNode({class:N[C]});break;case 18:p.getLogger().trace("SPACELIST");break;case 19:p.getLogger().trace("Node: ",N[C].id),p.addNode(0,N[C].id,N[C].descr,N[C].type);break;case 20:p.decorateNode({icon:N[C]});break;case 25:p.getLogger().trace("node found ..",N[C-2]),this.$={id:N[C-1],descr:N[C-1],type:p.getType(N[C-2],N[C])};break;case 26:this.$={id:N[C],descr:N[C],type:p.nodeType.DEFAULT};break;case 27:p.getLogger().trace("node found ..",N[C-3]),this.$={id:N[C-3],descr:N[C-1],type:p.getType(N[C-2],N[C])};break}},"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],8:I},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:I},{6:o,7:[1,10],9:9,12:11,13:i,14:14,15:e,16:t,17:17,18:18,19:r,22:l},E(f,[2,3]),{1:[2,2]},E(f,[2,4]),E(f,[2,5]),{1:[2,6],6:o,12:21,13:i,14:14,15:e,16:t,17:17,18:18,19:r,22:l},{6:o,9:22,12:11,13:i,14:14,15:e,16:t,17:17,18:18,19:r,22:l},{6:a,7:d,10:23,11:s},E(c,[2,22],{17:17,18:18,14:27,15:[1,28],16:[1,29],19:r,22:l}),E(c,[2,18]),E(c,[2,19]),E(c,[2,20]),E(c,[2,21]),E(c,[2,23]),E(c,[2,24]),E(c,[2,26],{19:[1,30]}),{20:[1,31]},{6:a,7:d,10:32,11:s},{1:[2,7],6:o,12:21,13:i,14:14,15:e,16:t,17:17,18:18,19:r,22:l},E(u,[2,14],{7:v,11:m}),E(T,[2,8]),E(T,[2,9]),E(T,[2,10]),E(c,[2,15]),E(c,[2,16]),E(c,[2,17]),{20:[1,35]},{21:[1,36]},E(u,[2,13],{7:v,11:m}),E(T,[2,11]),E(T,[2,12]),{21:[1,37]},E(c,[2,25]),E(c,[2,27])],defaultActions:{2:[2,1],6:[2,2]},parseError:L(function(n,h){if(h.recoverable)this.trace(n);else{var g=new Error(n);throw g.hash=h,g}},"parseError"),parse:L(function(n){var h=this,g=[0],p=[],D=[null],N=[],M=this.table,C="",w=0,P=0,U=0,F=2,x=1,G=N.slice.call(arguments,1),S=Object.create(this.lexer),_={yy:{}};for(var X in this.yy)Object.prototype.hasOwnProperty.call(this.yy,X)&&(_.yy[X]=this.yy[X]);S.setInput(n,_.yy),_.yy.lexer=S,_.yy.parser=this,typeof S.yylloc>"u"&&(S.yylloc={});var b=S.yylloc;N.push(b);var H=S.options&&S.options.ranges;typeof _.yy.parseError=="function"?this.parseError=_.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function z(W){g.length=g.length-2*W,D.length=D.length-W,N.length=N.length-W}L(z,"popStack");function B(){var W;return W=p.pop()||S.lex()||x,typeof W!="number"&&(W instanceof Array&&(p=W,W=p.pop()),W=h.symbols_[W]||W),W}L(B,"lex");for(var Y,K,j,$,ge,ot,J={},st,Z,Et,at;;){if(j=g[g.length-1],this.defaultActions[j]?$=this.defaultActions[j]:((Y===null||typeof Y>"u")&&(Y=B()),$=M[j]&&M[j][Y]),typeof $>"u"||!$.length||!$[0]){var ht="";at=[];for(st in M[j])this.terminals_[st]&&st>F&&at.push("'"+this.terminals_[st]+"'");S.showPosition?ht="Parse error on line "+(w+1)+`:
`+S.showPosition()+`
Expecting `+at.join(", ")+", got '"+(this.terminals_[Y]||Y)+"'":ht="Parse error on line "+(w+1)+": Unexpected "+(Y==x?"end of input":"'"+(this.terminals_[Y]||Y)+"'"),this.parseError(ht,{text:S.match,token:this.terminals_[Y]||Y,line:S.yylineno,loc:b,expected:at})}if($[0]instanceof Array&&$.length>1)throw new Error("Parse Error: multiple actions possible at state: "+j+", token: "+Y);switch($[0]){case 1:g.push(Y),D.push(S.yytext),N.push(S.yylloc),g.push($[1]),Y=null,K?(Y=K,K=null):(P=S.yyleng,C=S.yytext,w=S.yylineno,b=S.yylloc,U>0&&U--);break;case 2:if(Z=this.productions_[$[1]][1],J.$=D[D.length-Z],J._$={first_line:N[N.length-(Z||1)].first_line,last_line:N[N.length-1].last_line,first_column:N[N.length-(Z||1)].first_column,last_column:N[N.length-1].last_column},H&&(J._$.range=[N[N.length-(Z||1)].range[0],N[N.length-1].range[1]]),ot=this.performAction.apply(J,[C,P,w,_.yy,$[1],D,N].concat(G)),typeof ot<"u")return ot;Z&&(g=g.slice(0,-1*Z*2),D=D.slice(0,-1*Z),N=N.slice(0,-1*Z)),g.push(this.productions_[$[1]][0]),D.push(J.$),N.push(J._$),Et=M[g[g.length-2]][g[g.length-1]],g.push(Et);break;case 3:return!0}}return!0},"parse")},A=function(){var R={EOF:1,parseError:L(function(h,g){if(this.yy.parser)this.yy.parser.parseError(h,g);else throw new Error(h)},"parseError"),setInput:L(function(n,h){return this.yy=h||this.yy||{},this._input=n,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:L(function(){var n=this._input[0];this.yytext+=n,this.yyleng++,this.offset++,this.match+=n,this.matched+=n;var h=n.match(/(?:\r\n?|\n).*/g);return h?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),n},"input"),unput:L(function(n){var h=n.length,g=n.split(/(?:\r\n?|\n)/g);this._input=n+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-h),this.offset-=h;var p=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),g.length-1&&(this.yylineno-=g.length-1);var D=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:g?(g.length===p.length?this.yylloc.first_column:0)+p[p.length-g.length].length-g[0].length:this.yylloc.first_column-h},this.options.ranges&&(this.yylloc.range=[D[0],D[0]+this.yyleng-h]),this.yyleng=this.yytext.length,this},"unput"),more:L(function(){return this._more=!0,this},"more"),reject:L(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:L(function(n){this.unput(this.match.slice(n))},"less"),pastInput:L(function(){var n=this.matched.substr(0,this.matched.length-this.match.length);return(n.length>20?"...":"")+n.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:L(function(){var n=this.match;return n.length<20&&(n+=this._input.substr(0,20-n.length)),(n.substr(0,20)+(n.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:L(function(){var n=this.pastInput(),h=new Array(n.length+1).join("-");return n+this.upcomingInput()+`
`+h+"^"},"showPosition"),test_match:L(function(n,h){var g,p,D;if(this.options.backtrack_lexer&&(D={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(D.yylloc.range=this.yylloc.range.slice(0))),p=n[0].match(/(?:\r\n?|\n).*/g),p&&(this.yylineno+=p.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:p?p[p.length-1].length-p[p.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+n[0].length},this.yytext+=n[0],this.match+=n[0],this.matches=n,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(n[0].length),this.matched+=n[0],g=this.performAction.call(this,this.yy,this,h,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),g)return g;if(this._backtrack){for(var N in D)this[N]=D[N];return!1}return!1},"test_match"),next:L(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var n,h,g,p;this._more||(this.yytext="",this.match="");for(var D=this._currentRules(),N=0;N<D.length;N++)if(g=this._input.match(this.rules[D[N]]),g&&(!h||g[0].length>h[0].length)){if(h=g,p=N,this.options.backtrack_lexer){if(n=this.test_match(g,D[N]),n!==!1)return n;if(this._backtrack){h=!1;continue}else return!1}else if(!this.options.flex)break}return h?(n=this.test_match(h,D[p]),n!==!1?n:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:L(function(){var h=this.next();return h||this.lex()},"lex"),begin:L(function(h){this.conditionStack.push(h)},"begin"),popState:L(function(){var h=this.conditionStack.length-1;return h>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:L(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:L(function(h){return h=this.conditionStack.length-1-Math.abs(h||0),h>=0?this.conditionStack[h]:"INITIAL"},"topState"),pushState:L(function(h){this.begin(h)},"pushState"),stateStackSize:L(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:L(function(h,g,p,D){var N=D;switch(p){case 0:return h.getLogger().trace("Found comment",g.yytext),6;break;case 1:return 8;case 2:this.begin("CLASS");break;case 3:return this.popState(),16;break;case 4:this.popState();break;case 5:h.getLogger().trace("Begin icon"),this.begin("ICON");break;case 6:return h.getLogger().trace("SPACELINE"),6;break;case 7:return 7;case 8:return 15;case 9:h.getLogger().trace("end icon"),this.popState();break;case 10:return h.getLogger().trace("Exploding node"),this.begin("NODE"),19;break;case 11:return h.getLogger().trace("Cloud"),this.begin("NODE"),19;break;case 12:return h.getLogger().trace("Explosion Bang"),this.begin("NODE"),19;break;case 13:return h.getLogger().trace("Cloud Bang"),this.begin("NODE"),19;break;case 14:return this.begin("NODE"),19;break;case 15:return this.begin("NODE"),19;break;case 16:return this.begin("NODE"),19;break;case 17:return this.begin("NODE"),19;break;case 18:return 13;case 19:return 22;case 20:return 11;case 21:this.begin("NSTR2");break;case 22:return"NODE_DESCR";case 23:this.popState();break;case 24:h.getLogger().trace("Starting NSTR"),this.begin("NSTR");break;case 25:return h.getLogger().trace("description:",g.yytext),"NODE_DESCR";break;case 26:this.popState();break;case 27:return this.popState(),h.getLogger().trace("node end ))"),"NODE_DEND";break;case 28:return this.popState(),h.getLogger().trace("node end )"),"NODE_DEND";break;case 29:return this.popState(),h.getLogger().trace("node end ...",g.yytext),"NODE_DEND";break;case 30:return this.popState(),h.getLogger().trace("node end (("),"NODE_DEND";break;case 31:return this.popState(),h.getLogger().trace("node end (-"),"NODE_DEND";break;case 32:return this.popState(),h.getLogger().trace("node end (-"),"NODE_DEND";break;case 33:return this.popState(),h.getLogger().trace("node end (("),"NODE_DEND";break;case 34:return this.popState(),h.getLogger().trace("node end (("),"NODE_DEND";break;case 35:return h.getLogger().trace("Long description:",g.yytext),20;break;case 36:return h.getLogger().trace("Long description:",g.yytext),20;break}},"anonymous"),rules:[/^(?:\s*%%.*)/i,/^(?:mindmap\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\)\{\}]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{CLASS:{rules:[3,4],inclusive:!1},ICON:{rules:[8,9],inclusive:!1},NSTR2:{rules:[22,23],inclusive:!1},NSTR:{rules:[25,26],inclusive:!1},NODE:{rules:[21,24,27,28,29,30,31,32,33,34,35,36],inclusive:!1},INITIAL:{rules:[0,1,2,5,6,7,10,11,12,13,14,15,16,17,18,19,20],inclusive:!0}}};return R}();y.lexer=A;function O(){this.yy={}}return L(O,"Parser"),O.prototype=y,y.Parser=O,new O}();gt.parser=gt;var Ct=gt;var V=[],Mt=0,ut={},Yt=L(()=>{V=[],Mt=0,ut={}},"clear"),Xt=L(function(E){for(let I=V.length-1;I>=0;I--)if(V[I].level<E)return V[I];return null},"getParent"),kt=L(()=>V.length>0?V[0]:null,"getMindmap"),Ht=L((E,I,o,i)=>{Q.info("addNode",E,I,o,i);let e=et(),t=e.mindmap?.padding??q.mindmap.padding;switch(i){case k.ROUNDED_RECT:case k.RECT:case k.HEXAGON:t*=2}let r={id:Mt++,nodeId:tt(I,e),level:E,descr:tt(o,e),type:i,children:[],width:e.mindmap?.maxNodeWidth??q.mindmap.maxNodeWidth,padding:t},l=Xt(E);if(l)l.children.push(r),V.push(r);else if(V.length===0)V.push(r);else throw new Error('There can be only one root. No parent could be found for ("'+r.descr+'")')},"addNode"),k={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6},$t=L((E,I)=>{switch(Q.debug("In get type",E,I),E){case"[":return k.RECT;case"(":return I===")"?k.ROUNDED_RECT:k.CLOUD;case"((":return k.CIRCLE;case")":return k.CLOUD;case"))":return k.BANG;case"{{":return k.HEXAGON;default:return k.DEFAULT}},"getType"),Wt=L((E,I)=>{ut[E]=I},"setElementForId"),Bt=L(E=>{if(!E)return;let I=et(),o=V[V.length-1];E.icon&&(o.icon=tt(E.icon,I)),E.class&&(o.class=tt(E.class,I))},"decorateNode"),Vt=L(E=>{switch(E){case k.DEFAULT:return"no-border";case k.RECT:return"rect";case k.ROUNDED_RECT:return"rounded-rect";case k.CIRCLE:return"circle";case k.CLOUD:return"cloud";case k.BANG:return"bang";case k.HEXAGON:return"hexgon";default:return"no-border"}},"type2Str"),jt=L(()=>Q,"getLogger"),Zt=L(E=>ut[E],"getElementById"),Qt={clear:Yt,addNode:Ht,getMindmap:kt,nodeType:k,getType:$t,setElementForId:Wt,decorateNode:Bt,type2Str:Vt,getLogger:jt,getElementById:Zt},Rt=Qt;var Gt=Ut(xt(),1);var zt=12,Kt=L(function(E,I,o,i){I.append("path").attr("id","node-"+o.id).attr("class","node-bkg node-"+E.type2Str(o.type)).attr("d",`M0 ${o.height-5} v${-o.height+2*5} q0,-5 5,-5 h${o.width-2*5} q5,0 5,5 v${o.height-5} H0 Z`),I.append("line").attr("class","node-line-"+i).attr("x1",0).attr("y1",o.height).attr("x2",o.width).attr("y2",o.height)},"defaultBkg"),Jt=L(function(E,I,o){I.append("rect").attr("id","node-"+o.id).attr("class","node-bkg node-"+E.type2Str(o.type)).attr("height",o.height).attr("width",o.width)},"rectBkg"),qt=L(function(E,I,o){let i=o.width,e=o.height,t=.15*i,r=.25*i,l=.35*i,f=.2*i;I.append("path").attr("id","node-"+o.id).attr("class","node-bkg node-"+E.type2Str(o.type)).attr("d",`M0 0 a${t},${t} 0 0,1 ${i*.25},${-1*i*.1}
      a${l},${l} 1 0,1 ${i*.4},${-1*i*.1}
      a${r},${r} 1 0,1 ${i*.35},${1*i*.2}

      a${t},${t} 1 0,1 ${i*.15},${1*e*.35}
      a${f},${f} 1 0,1 ${-1*i*.15},${1*e*.65}

      a${r},${t} 1 0,1 ${-1*i*.25},${i*.15}
      a${l},${l} 1 0,1 ${-1*i*.5},0
      a${t},${t} 1 0,1 ${-1*i*.25},${-1*i*.15}

      a${t},${t} 1 0,1 ${-1*i*.1},${-1*e*.35}
      a${f},${f} 1 0,1 ${i*.1},${-1*e*.65}

    H0 V0 Z`)},"cloudBkg"),te=L(function(E,I,o){let i=o.width,e=o.height,t=.15*i;I.append("path").attr("id","node-"+o.id).attr("class","node-bkg node-"+E.type2Str(o.type)).attr("d",`M0 0 a${t},${t} 1 0,0 ${i*.25},${-1*e*.1}
      a${t},${t} 1 0,0 ${i*.25},0
      a${t},${t} 1 0,0 ${i*.25},0
      a${t},${t} 1 0,0 ${i*.25},${1*e*.1}

      a${t},${t} 1 0,0 ${i*.15},${1*e*.33}
      a${t*.8},${t*.8} 1 0,0 0,${1*e*.34}
      a${t},${t} 1 0,0 ${-1*i*.15},${1*e*.33}

      a${t},${t} 1 0,0 ${-1*i*.25},${e*.15}
      a${t},${t} 1 0,0 ${-1*i*.25},0
      a${t},${t} 1 0,0 ${-1*i*.25},0
      a${t},${t} 1 0,0 ${-1*i*.25},${-1*e*.15}

      a${t},${t} 1 0,0 ${-1*i*.1},${-1*e*.33}
      a${t*.8},${t*.8} 1 0,0 0,${-1*e*.34}
      a${t},${t} 1 0,0 ${i*.1},${-1*e*.33}

    H0 V0 Z`)},"bangBkg"),ee=L(function(E,I,o){I.append("circle").attr("id","node-"+o.id).attr("class","node-bkg node-"+E.type2Str(o.type)).attr("r",o.width/2)},"circleBkg");function re(E,I,o,i,e){return E.insert("polygon",":first-child").attr("points",i.map(function(t){return t.x+","+t.y}).join(" ")).attr("transform","translate("+(e.width-I)/2+", "+o+")")}L(re,"insertPolygonShape");var ie=L(function(E,I,o){let i=o.height,t=i/4,r=o.width-o.padding+2*t,l=[{x:t,y:0},{x:r-t,y:0},{x:r,y:-i/2},{x:r-t,y:-i},{x:t,y:-i},{x:0,y:-i/2}];re(I,r,i,l,o)},"hexagonBkg"),ne=L(function(E,I,o){I.append("rect").attr("id","node-"+o.id).attr("class","node-bkg node-"+E.type2Str(o.type)).attr("height",o.height).attr("rx",o.padding).attr("ry",o.padding).attr("width",o.width)},"roundedRectBkg"),wt=L(async function(E,I,o,i,e){let t=e.htmlLabels,r=i%(zt-1),l=I.append("g");o.section=r;let f="section-"+r;r<0&&(f+=" section-root"),l.attr("class",(o.class?o.class+" ":"")+"mindmap-node "+f);let a=l.append("g"),d=l.append("g"),s=o.descr.replace(/(<br\/*>)/g,`
`);await At(d,s,{useHtmlLabels:t,width:o.width,classes:"mindmap-node-label"},e),t||d.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle");let c=d.node().getBBox(),[u]=Ot(e.fontSize);if(o.height=c.height+u*1.1*.5+o.padding,o.width=c.width+2*o.padding,o.icon)if(o.type===E.nodeType.CIRCLE)o.height+=50,o.width+=50,l.append("foreignObject").attr("height","50px").attr("width",o.width).attr("style","text-align: center;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+r+" "+o.icon),d.attr("transform","translate("+o.width/2+", "+(o.height/2-1.5*o.padding)+")");else{o.width+=50;let v=o.height;o.height=Math.max(v,60);let m=Math.abs(o.height-v);l.append("foreignObject").attr("width","60px").attr("height",o.height).attr("style","text-align: center;margin-top:"+m/2+"px;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+r+" "+o.icon),d.attr("transform","translate("+(25+o.width/2)+", "+(m/2+o.padding/2)+")")}else if(t){let v=(o.width-c.width)/2,m=(o.height-c.height)/2;d.attr("transform","translate("+v+", "+m+")")}else{let v=o.width/2,m=o.padding/2;d.attr("transform","translate("+v+", "+m+")")}switch(o.type){case E.nodeType.DEFAULT:Kt(E,a,o,r);break;case E.nodeType.ROUNDED_RECT:ne(E,a,o,r);break;case E.nodeType.RECT:Jt(E,a,o,r);break;case E.nodeType.CIRCLE:a.attr("transform","translate("+o.width/2+", "+ +o.height/2+")"),ee(E,a,o,r);break;case E.nodeType.CLOUD:qt(E,a,o,r);break;case E.nodeType.BANG:te(E,a,o,r);break;case E.nodeType.HEXAGON:ie(E,a,o,r);break}return E.setElementForId(o.id,l),o.height},"drawNode"),St=L(function(E,I){let o=E.getElementById(I.id),i=I.x||0,e=I.y||0;o.attr("transform","translate("+i+","+e+")")},"positionNode");ct.use(Gt.default);async function Ft(E,I,o,i,e){await wt(E,I,o,i,e),o.children&&await Promise.all(o.children.map((t,r)=>Ft(E,I,t,i<0?r:i,e)))}L(Ft,"drawNodes");function se(E,I){I.edges().map((o,i)=>{let e=o.data();if(o[0]._private.bodyBounds){let t=o[0]._private.rscratch;Q.trace("Edge: ",i,e),E.insert("path").attr("d",`M ${t.startX},${t.startY} L ${t.midX},${t.midY} L${t.endX},${t.endY} `).attr("class","edge section-edge-"+e.section+" edge-depth-"+e.depth)}})}L(se,"drawEdges");function _t(E,I,o,i){I.add({group:"nodes",data:{id:E.id.toString(),labelText:E.descr,height:E.height,width:E.width,level:i,nodeId:E.id,padding:E.padding,type:E.type},position:{x:E.x,y:E.y}}),E.children&&E.children.forEach(e=>{_t(e,I,o,i+1),I.add({group:"edges",data:{id:`${E.id}_${e.id}`,source:E.id,target:e.id,depth:i,section:e.section}})})}L(_t,"addNodes");function ae(E,I){return new Promise(o=>{let i=Nt("body").append("div").attr("id","cy").attr("style","display:none"),e=ct({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"bezier"}}]});i.remove(),_t(E,e,I,0),e.nodes().forEach(function(t){t.layoutDimensions=()=>{let r=t.data();return{w:r.width,h:r.height}}}),e.layout({name:"cose-bilkent",quality:"proof",styleEnabled:!1,animate:!1}).run(),e.ready(t=>{Q.info("Ready",t),o(e)})})}L(ae,"layoutMindmap");function oe(E,I){I.nodes().map((o,i)=>{let e=o.data();e.x=o.position().x,e.y=o.position().y,St(E,e);let t=E.getElementById(e.nodeId);Q.info("Id:",i,"Position: (",o.position().x,", ",o.position().y,")",e),t.attr("transform",`translate(${o.position().x-e.width/2}, ${o.position().y-e.height/2})`),t.attr("attr",`apa-${i})`)})}L(oe,"positionNodes");var he=L(async(E,I,o,i)=>{Q.debug(`Rendering mindmap diagram
`+E);let e=i.db,t=e.getMindmap();if(!t)return;let r=et();r.htmlLabels=!1;let l=It(I),f=l.append("g");f.attr("class","mindmap-edges");let a=l.append("g");a.attr("class","mindmap-nodes"),await Ft(e,a,t,-1,r);let d=await ae(t,r);se(f,d),oe(e,d),Dt(void 0,l,r.mindmap?.padding??q.mindmap.padding,r.mindmap?.useMaxWidth??q.mindmap.useMaxWidth)},"draw"),bt={draw:he};var le=L(E=>{let I="";for(let o=0;o<E.THEME_COLOR_LIMIT;o++)E["lineColor"+o]=E["lineColor"+o]||E["cScaleInv"+o],mt(E["lineColor"+o])?E["lineColor"+o]=Lt(E["lineColor"+o],20):E["lineColor"+o]=Tt(E["lineColor"+o],20);for(let o=0;o<E.THEME_COLOR_LIMIT;o++){let i=""+(17-3*o);I+=`
    .section-${o-1} rect, .section-${o-1} path, .section-${o-1} circle, .section-${o-1} polygon, .section-${o-1} path  {
      fill: ${E["cScale"+o]};
    }
    .section-${o-1} text {
     fill: ${E["cScaleLabel"+o]};
    }
    .node-icon-${o-1} {
      font-size: 40px;
      color: ${E["cScaleLabel"+o]};
    }
    .section-edge-${o-1}{
      stroke: ${E["cScale"+o]};
    }
    .edge-depth-${o-1}{
      stroke-width: ${i};
    }
    .section-${o-1} line {
      stroke: ${E["cScaleInv"+o]} ;
      stroke-width: 3;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }
    `}return I},"genSections"),ce=L(E=>`
  .edge {
    stroke-width: 3;
  }
  ${le(E)}
  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {
    fill: ${E.git0};
  }
  .section-root text {
    fill: ${E.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .mindmap-node-label {
    dy: 1em;
    alignment-baseline: middle;
    text-anchor: middle;
    dominant-baseline: middle;
    text-align: center;
  }
`,"getStyles"),Pt=ce;var We={db:Rt,renderer:bt,parser:Ct,styles:Pt};export{We as diagram};
