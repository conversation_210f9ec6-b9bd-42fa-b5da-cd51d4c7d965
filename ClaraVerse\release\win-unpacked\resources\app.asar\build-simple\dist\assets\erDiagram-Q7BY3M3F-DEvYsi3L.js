import{g as Dt,s as wt}from"./chunk-RZ5BOZE2-D-3lwpbo.js";import{_ as u,b as Vt,a as Lt,s as Mt,g as Bt,v as Ft,x as Yt,c as tt,l as D,B as Pt,A as zt,D as Gt,F as Kt,q as Zt,r as Ut,d as jt,u as Wt}from"./index-DegskO7r.js";import{c as Qt}from"./channel-BMsVgUgU.js";import"./vendor-BEryHLmj.js";import"./pdfjs-CcP0jMWS.js";var dt=function(){var s=u(function(R,n,a,c){for(a=a||{},c=R.length;c--;a[R[c]]=n);return a},"o"),i=[6,8,10,22,24,26,28,33,34,35,36,37,40,43,44,50],h=[1,10],d=[1,11],o=[1,12],l=[1,13],f=[1,20],_=[1,21],E=[1,22],V=[1,23],Z=[1,24],S=[1,19],et=[1,25],U=[1,26],T=[1,18],L=[1,33],st=[1,34],it=[1,35],rt=[1,36],nt=[1,37],pt=[6,8,10,13,15,17,20,21,22,24,26,28,33,34,35,36,37,40,43,44,50,63,64,65,66,67],O=[1,42],A=[1,43],M=[1,52],B=[40,50,68,69],F=[1,63],Y=[1,61],N=[1,58],P=[1,62],z=[1,64],j=[6,8,10,13,17,22,24,26,28,33,34,35,36,37,40,41,42,43,44,48,49,50,63,64,65,66,67],yt=[63,64,65,66,67],ft=[1,81],_t=[1,80],gt=[1,78],bt=[1,79],mt=[6,10,42,47],v=[6,10,13,41,42,47,48,49],W=[1,89],Q=[1,88],X=[1,87],G=[19,56],Et=[1,98],kt=[1,97],at=[19,56,58,60],ct={trace:u(function(){},"trace"),yy:{},symbols_:{error:2,start:3,ER_DIAGRAM:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NEWLINE:10,entityName:11,relSpec:12,COLON:13,role:14,STYLE_SEPARATOR:15,idList:16,BLOCK_START:17,attributes:18,BLOCK_STOP:19,SQS:20,SQE:21,title:22,title_value:23,acc_title:24,acc_title_value:25,acc_descr:26,acc_descr_value:27,acc_descr_multiline_value:28,direction:29,classDefStatement:30,classStatement:31,styleStatement:32,direction_tb:33,direction_bt:34,direction_rl:35,direction_lr:36,CLASSDEF:37,stylesOpt:38,separator:39,UNICODE_TEXT:40,STYLE_TEXT:41,COMMA:42,CLASS:43,STYLE:44,style:45,styleComponent:46,SEMI:47,NUM:48,BRKT:49,ENTITY_NAME:50,attribute:51,attributeType:52,attributeName:53,attributeKeyTypeList:54,attributeComment:55,ATTRIBUTE_WORD:56,attributeKeyType:57,",":58,ATTRIBUTE_KEY:59,COMMENT:60,cardinality:61,relType:62,ZERO_OR_ONE:63,ZERO_OR_MORE:64,ONE_OR_MORE:65,ONLY_ONE:66,MD_PARENT:67,NON_IDENTIFYING:68,IDENTIFYING:69,WORD:70,$accept:0,$end:1},terminals_:{2:"error",4:"ER_DIAGRAM",6:"EOF",8:"SPACE",10:"NEWLINE",13:"COLON",15:"STYLE_SEPARATOR",17:"BLOCK_START",19:"BLOCK_STOP",20:"SQS",21:"SQE",22:"title",23:"title_value",24:"acc_title",25:"acc_title_value",26:"acc_descr",27:"acc_descr_value",28:"acc_descr_multiline_value",33:"direction_tb",34:"direction_bt",35:"direction_rl",36:"direction_lr",37:"CLASSDEF",40:"UNICODE_TEXT",41:"STYLE_TEXT",42:"COMMA",43:"CLASS",44:"STYLE",47:"SEMI",48:"NUM",49:"BRKT",50:"ENTITY_NAME",56:"ATTRIBUTE_WORD",58:",",59:"ATTRIBUTE_KEY",60:"COMMENT",63:"ZERO_OR_ONE",64:"ZERO_OR_MORE",65:"ONE_OR_MORE",66:"ONLY_ONE",67:"MD_PARENT",68:"NON_IDENTIFYING",69:"IDENTIFYING",70:"WORD"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[9,5],[9,9],[9,7],[9,7],[9,4],[9,6],[9,3],[9,5],[9,1],[9,3],[9,7],[9,9],[9,6],[9,8],[9,4],[9,6],[9,2],[9,2],[9,2],[9,1],[9,1],[9,1],[9,1],[9,1],[29,1],[29,1],[29,1],[29,1],[30,4],[16,1],[16,1],[16,3],[16,3],[31,3],[32,4],[38,1],[38,3],[45,1],[45,2],[39,1],[39,1],[39,1],[46,1],[46,1],[46,1],[46,1],[11,1],[11,1],[18,1],[18,2],[51,2],[51,3],[51,3],[51,4],[52,1],[53,1],[54,1],[54,3],[57,1],[55,1],[12,3],[61,1],[61,1],[61,1],[61,1],[61,1],[62,1],[62,1],[14,1],[14,1],[14,1]],performAction:u(function(n,a,c,r,p,t,K){var e=t.length-1;switch(p){case 1:break;case 2:this.$=[];break;case 3:t[e-1].push(t[e]),this.$=t[e-1];break;case 4:case 5:this.$=t[e];break;case 6:case 7:this.$=[];break;case 8:r.addEntity(t[e-4]),r.addEntity(t[e-2]),r.addRelationship(t[e-4],t[e],t[e-2],t[e-3]);break;case 9:r.addEntity(t[e-8]),r.addEntity(t[e-4]),r.addRelationship(t[e-8],t[e],t[e-4],t[e-5]),r.setClass([t[e-8]],t[e-6]),r.setClass([t[e-4]],t[e-2]);break;case 10:r.addEntity(t[e-6]),r.addEntity(t[e-2]),r.addRelationship(t[e-6],t[e],t[e-2],t[e-3]),r.setClass([t[e-6]],t[e-4]);break;case 11:r.addEntity(t[e-6]),r.addEntity(t[e-4]),r.addRelationship(t[e-6],t[e],t[e-4],t[e-5]),r.setClass([t[e-4]],t[e-2]);break;case 12:r.addEntity(t[e-3]),r.addAttributes(t[e-3],t[e-1]);break;case 13:r.addEntity(t[e-5]),r.addAttributes(t[e-5],t[e-1]),r.setClass([t[e-5]],t[e-3]);break;case 14:r.addEntity(t[e-2]);break;case 15:r.addEntity(t[e-4]),r.setClass([t[e-4]],t[e-2]);break;case 16:r.addEntity(t[e]);break;case 17:r.addEntity(t[e-2]),r.setClass([t[e-2]],t[e]);break;case 18:r.addEntity(t[e-6],t[e-4]),r.addAttributes(t[e-6],t[e-1]);break;case 19:r.addEntity(t[e-8],t[e-6]),r.addAttributes(t[e-8],t[e-1]),r.setClass([t[e-8]],t[e-3]);break;case 20:r.addEntity(t[e-5],t[e-3]);break;case 21:r.addEntity(t[e-7],t[e-5]),r.setClass([t[e-7]],t[e-2]);break;case 22:r.addEntity(t[e-3],t[e-1]);break;case 23:r.addEntity(t[e-5],t[e-3]),r.setClass([t[e-5]],t[e]);break;case 24:case 25:this.$=t[e].trim(),r.setAccTitle(this.$);break;case 26:case 27:this.$=t[e].trim(),r.setAccDescription(this.$);break;case 32:r.setDirection("TB");break;case 33:r.setDirection("BT");break;case 34:r.setDirection("RL");break;case 35:r.setDirection("LR");break;case 36:this.$=t[e-3],r.addClass(t[e-2],t[e-1]);break;case 37:case 38:case 56:case 64:this.$=[t[e]];break;case 39:case 40:this.$=t[e-2].concat([t[e]]);break;case 41:this.$=t[e-2],r.setClass(t[e-1],t[e]);break;case 42:this.$=t[e-3],r.addCssStyles(t[e-2],t[e-1]);break;case 43:this.$=[t[e]];break;case 44:t[e-2].push(t[e]),this.$=t[e-2];break;case 46:this.$=t[e-1]+t[e];break;case 54:case 76:case 77:this.$=t[e].replace(/"/g,"");break;case 55:case 78:this.$=t[e];break;case 57:t[e].push(t[e-1]),this.$=t[e];break;case 58:this.$={type:t[e-1],name:t[e]};break;case 59:this.$={type:t[e-2],name:t[e-1],keys:t[e]};break;case 60:this.$={type:t[e-2],name:t[e-1],comment:t[e]};break;case 61:this.$={type:t[e-3],name:t[e-2],keys:t[e-1],comment:t[e]};break;case 62:case 63:case 66:this.$=t[e];break;case 65:t[e-2].push(t[e]),this.$=t[e-2];break;case 67:this.$=t[e].replace(/"/g,"");break;case 68:this.$={cardA:t[e],relType:t[e-1],cardB:t[e-2]};break;case 69:this.$=r.Cardinality.ZERO_OR_ONE;break;case 70:this.$=r.Cardinality.ZERO_OR_MORE;break;case 71:this.$=r.Cardinality.ONE_OR_MORE;break;case 72:this.$=r.Cardinality.ONLY_ONE;break;case 73:this.$=r.Cardinality.MD_PARENT;break;case 74:this.$=r.Identification.NON_IDENTIFYING;break;case 75:this.$=r.Identification.IDENTIFYING;break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},s(i,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:9,22:h,24:d,26:o,28:l,29:14,30:15,31:16,32:17,33:f,34:_,35:E,36:V,37:Z,40:S,43:et,44:U,50:T},s(i,[2,7],{1:[2,1]}),s(i,[2,3]),{9:27,11:9,22:h,24:d,26:o,28:l,29:14,30:15,31:16,32:17,33:f,34:_,35:E,36:V,37:Z,40:S,43:et,44:U,50:T},s(i,[2,5]),s(i,[2,6]),s(i,[2,16],{12:28,61:32,15:[1,29],17:[1,30],20:[1,31],63:L,64:st,65:it,66:rt,67:nt}),{23:[1,38]},{25:[1,39]},{27:[1,40]},s(i,[2,27]),s(i,[2,28]),s(i,[2,29]),s(i,[2,30]),s(i,[2,31]),s(pt,[2,54]),s(pt,[2,55]),s(i,[2,32]),s(i,[2,33]),s(i,[2,34]),s(i,[2,35]),{16:41,40:O,41:A},{16:44,40:O,41:A},{16:45,40:O,41:A},s(i,[2,4]),{11:46,40:S,50:T},{16:47,40:O,41:A},{18:48,19:[1,49],51:50,52:51,56:M},{11:53,40:S,50:T},{62:54,68:[1,55],69:[1,56]},s(B,[2,69]),s(B,[2,70]),s(B,[2,71]),s(B,[2,72]),s(B,[2,73]),s(i,[2,24]),s(i,[2,25]),s(i,[2,26]),{13:F,38:57,41:Y,42:N,45:59,46:60,48:P,49:z},s(j,[2,37]),s(j,[2,38]),{16:65,40:O,41:A,42:N},{13:F,38:66,41:Y,42:N,45:59,46:60,48:P,49:z},{13:[1,67],15:[1,68]},s(i,[2,17],{61:32,12:69,17:[1,70],42:N,63:L,64:st,65:it,66:rt,67:nt}),{19:[1,71]},s(i,[2,14]),{18:72,19:[2,56],51:50,52:51,56:M},{53:73,56:[1,74]},{56:[2,62]},{21:[1,75]},{61:76,63:L,64:st,65:it,66:rt,67:nt},s(yt,[2,74]),s(yt,[2,75]),{6:ft,10:_t,39:77,42:gt,47:bt},{40:[1,82],41:[1,83]},s(mt,[2,43],{46:84,13:F,41:Y,48:P,49:z}),s(v,[2,45]),s(v,[2,50]),s(v,[2,51]),s(v,[2,52]),s(v,[2,53]),s(i,[2,41],{42:N}),{6:ft,10:_t,39:85,42:gt,47:bt},{14:86,40:W,50:Q,70:X},{16:90,40:O,41:A},{11:91,40:S,50:T},{18:92,19:[1,93],51:50,52:51,56:M},s(i,[2,12]),{19:[2,57]},s(G,[2,58],{54:94,55:95,57:96,59:Et,60:kt}),s([19,56,59,60],[2,63]),s(i,[2,22],{15:[1,100],17:[1,99]}),s([40,50],[2,68]),s(i,[2,36]),{13:F,41:Y,45:101,46:60,48:P,49:z},s(i,[2,47]),s(i,[2,48]),s(i,[2,49]),s(j,[2,39]),s(j,[2,40]),s(v,[2,46]),s(i,[2,42]),s(i,[2,8]),s(i,[2,76]),s(i,[2,77]),s(i,[2,78]),{13:[1,102],42:N},{13:[1,104],15:[1,103]},{19:[1,105]},s(i,[2,15]),s(G,[2,59],{55:106,58:[1,107],60:kt}),s(G,[2,60]),s(at,[2,64]),s(G,[2,67]),s(at,[2,66]),{18:108,19:[1,109],51:50,52:51,56:M},{16:110,40:O,41:A},s(mt,[2,44],{46:84,13:F,41:Y,48:P,49:z}),{14:111,40:W,50:Q,70:X},{16:112,40:O,41:A},{14:113,40:W,50:Q,70:X},s(i,[2,13]),s(G,[2,61]),{57:114,59:Et},{19:[1,115]},s(i,[2,20]),s(i,[2,23],{17:[1,116],42:N}),s(i,[2,11]),{13:[1,117],42:N},s(i,[2,10]),s(at,[2,65]),s(i,[2,18]),{18:118,19:[1,119],51:50,52:51,56:M},{14:120,40:W,50:Q,70:X},{19:[1,121]},s(i,[2,21]),s(i,[2,9]),s(i,[2,19])],defaultActions:{52:[2,62],72:[2,57]},parseError:u(function(n,a){if(a.recoverable)this.trace(n);else{var c=new Error(n);throw c.hash=a,c}},"parseError"),parse:u(function(n){var a=this,c=[0],r=[],p=[null],t=[],K=this.table,e="",H=0,St=0,xt=2,Tt=1,It=t.slice.call(arguments,1),y=Object.create(this.lexer),x={yy:{}};for(var lt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,lt)&&(x.yy[lt]=this.yy[lt]);y.setInput(n,x.yy),x.yy.lexer=y,x.yy.parser=this,typeof y.yylloc>"u"&&(y.yylloc={});var ot=y.yylloc;t.push(ot);var vt=y.options&&y.options.ranges;typeof x.yy.parseError=="function"?this.parseError=x.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Ct(b){c.length=c.length-2*b,p.length=p.length-b,t.length=t.length-b}u(Ct,"popStack");function Ot(){var b;return b=r.pop()||y.lex()||Tt,typeof b!="number"&&(b instanceof Array&&(r=b,b=r.pop()),b=a.symbols_[b]||b),b}u(Ot,"lex");for(var g,I,m,ht,C={},J,k,At,$;;){if(I=c[c.length-1],this.defaultActions[I]?m=this.defaultActions[I]:((g===null||typeof g>"u")&&(g=Ot()),m=K[I]&&K[I][g]),typeof m>"u"||!m.length||!m[0]){var ut="";$=[];for(J in K[I])this.terminals_[J]&&J>xt&&$.push("'"+this.terminals_[J]+"'");y.showPosition?ut="Parse error on line "+(H+1)+`:
`+y.showPosition()+`
Expecting `+$.join(", ")+", got '"+(this.terminals_[g]||g)+"'":ut="Parse error on line "+(H+1)+": Unexpected "+(g==Tt?"end of input":"'"+(this.terminals_[g]||g)+"'"),this.parseError(ut,{text:y.match,token:this.terminals_[g]||g,line:y.yylineno,loc:ot,expected:$})}if(m[0]instanceof Array&&m.length>1)throw new Error("Parse Error: multiple actions possible at state: "+I+", token: "+g);switch(m[0]){case 1:c.push(g),p.push(y.yytext),t.push(y.yylloc),c.push(m[1]),g=null,St=y.yyleng,e=y.yytext,H=y.yylineno,ot=y.yylloc;break;case 2:if(k=this.productions_[m[1]][1],C.$=p[p.length-k],C._$={first_line:t[t.length-(k||1)].first_line,last_line:t[t.length-1].last_line,first_column:t[t.length-(k||1)].first_column,last_column:t[t.length-1].last_column},vt&&(C._$.range=[t[t.length-(k||1)].range[0],t[t.length-1].range[1]]),ht=this.performAction.apply(C,[e,St,H,x.yy,m[1],p,t].concat(It)),typeof ht<"u")return ht;k&&(c=c.slice(0,-1*k*2),p=p.slice(0,-1*k),t=t.slice(0,-1*k)),c.push(this.productions_[m[1]][0]),p.push(C.$),t.push(C._$),At=K[c[c.length-2]][c[c.length-1]],c.push(At);break;case 3:return!0}}return!0},"parse")},Rt=function(){var R={EOF:1,parseError:u(function(a,c){if(this.yy.parser)this.yy.parser.parseError(a,c);else throw new Error(a)},"parseError"),setInput:u(function(n,a){return this.yy=a||this.yy||{},this._input=n,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:u(function(){var n=this._input[0];this.yytext+=n,this.yyleng++,this.offset++,this.match+=n,this.matched+=n;var a=n.match(/(?:\r\n?|\n).*/g);return a?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),n},"input"),unput:u(function(n){var a=n.length,c=n.split(/(?:\r\n?|\n)/g);this._input=n+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-a),this.offset-=a;var r=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),c.length-1&&(this.yylineno-=c.length-1);var p=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:c?(c.length===r.length?this.yylloc.first_column:0)+r[r.length-c.length].length-c[0].length:this.yylloc.first_column-a},this.options.ranges&&(this.yylloc.range=[p[0],p[0]+this.yyleng-a]),this.yyleng=this.yytext.length,this},"unput"),more:u(function(){return this._more=!0,this},"more"),reject:u(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:u(function(n){this.unput(this.match.slice(n))},"less"),pastInput:u(function(){var n=this.matched.substr(0,this.matched.length-this.match.length);return(n.length>20?"...":"")+n.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:u(function(){var n=this.match;return n.length<20&&(n+=this._input.substr(0,20-n.length)),(n.substr(0,20)+(n.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:u(function(){var n=this.pastInput(),a=new Array(n.length+1).join("-");return n+this.upcomingInput()+`
`+a+"^"},"showPosition"),test_match:u(function(n,a){var c,r,p;if(this.options.backtrack_lexer&&(p={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(p.yylloc.range=this.yylloc.range.slice(0))),r=n[0].match(/(?:\r\n?|\n).*/g),r&&(this.yylineno+=r.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:r?r[r.length-1].length-r[r.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+n[0].length},this.yytext+=n[0],this.match+=n[0],this.matches=n,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(n[0].length),this.matched+=n[0],c=this.performAction.call(this,this.yy,this,a,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),c)return c;if(this._backtrack){for(var t in p)this[t]=p[t];return!1}return!1},"test_match"),next:u(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var n,a,c,r;this._more||(this.yytext="",this.match="");for(var p=this._currentRules(),t=0;t<p.length;t++)if(c=this._input.match(this.rules[p[t]]),c&&(!a||c[0].length>a[0].length)){if(a=c,r=t,this.options.backtrack_lexer){if(n=this.test_match(c,p[t]),n!==!1)return n;if(this._backtrack){a=!1;continue}else return!1}else if(!this.options.flex)break}return a?(n=this.test_match(a,p[r]),n!==!1?n:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:u(function(){var a=this.next();return a||this.lex()},"lex"),begin:u(function(a){this.conditionStack.push(a)},"begin"),popState:u(function(){var a=this.conditionStack.length-1;return a>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:u(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:u(function(a){return a=this.conditionStack.length-1-Math.abs(a||0),a>=0?this.conditionStack[a]:"INITIAL"},"topState"),pushState:u(function(a){this.begin(a)},"pushState"),stateStackSize:u(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:u(function(a,c,r,p){switch(r){case 0:return this.begin("acc_title"),24;case 1:return this.popState(),"acc_title_value";case 2:return this.begin("acc_descr"),26;case 3:return this.popState(),"acc_descr_value";case 4:this.begin("acc_descr_multiline");break;case 5:this.popState();break;case 6:return"acc_descr_multiline_value";case 7:return 33;case 8:return 34;case 9:return 35;case 10:return 36;case 11:return 10;case 12:break;case 13:return 8;case 14:return 50;case 15:return 70;case 16:return 4;case 17:return this.begin("block"),17;case 18:return 49;case 19:return 49;case 20:return 42;case 21:return 15;case 22:return 13;case 23:break;case 24:return 59;case 25:return 56;case 26:return 56;case 27:return 60;case 28:break;case 29:return this.popState(),19;case 30:return c.yytext[0];case 31:return 20;case 32:return 21;case 33:return this.begin("style"),44;case 34:return this.popState(),10;case 35:break;case 36:return 13;case 37:return 42;case 38:return 49;case 39:return this.begin("style"),37;case 40:return 43;case 41:return 63;case 42:return 65;case 43:return 65;case 44:return 65;case 45:return 63;case 46:return 63;case 47:return 64;case 48:return 64;case 49:return 64;case 50:return 64;case 51:return 64;case 52:return 65;case 53:return 64;case 54:return 65;case 55:return 66;case 56:return 66;case 57:return 66;case 58:return 66;case 59:return 63;case 60:return 64;case 61:return 65;case 62:return 67;case 63:return 68;case 64:return 69;case 65:return 69;case 66:return 68;case 67:return 68;case 68:return 68;case 69:return 41;case 70:return 47;case 71:return 40;case 72:return 48;case 73:return c.yytext[0];case 74:return 6}},"anonymous"),rules:[/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:.*direction\s+TB[^\n]*)/i,/^(?:.*direction\s+BT[^\n]*)/i,/^(?:.*direction\s+RL[^\n]*)/i,/^(?:.*direction\s+LR[^\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:[\s]+)/i,/^(?:"[^"%\r\n\v\b\\]+")/i,/^(?:"[^"]*")/i,/^(?:erDiagram\b)/i,/^(?:\{)/i,/^(?:#)/i,/^(?:#)/i,/^(?:,)/i,/^(?::::)/i,/^(?::)/i,/^(?:\s+)/i,/^(?:\b((?:PK)|(?:FK)|(?:UK))\b)/i,/^(?:([^\s]*)[~].*[~]([^\s]*))/i,/^(?:([\*A-Za-z_\u00C0-\uFFFF][A-Za-z0-9\-\_\[\]\(\)\u00C0-\uFFFF\*]*))/i,/^(?:"[^"]*")/i,/^(?:[\n]+)/i,/^(?:\})/i,/^(?:.)/i,/^(?:\[)/i,/^(?:\])/i,/^(?:style\b)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?::)/i,/^(?:,)/i,/^(?:#)/i,/^(?:classDef\b)/i,/^(?:class\b)/i,/^(?:one or zero\b)/i,/^(?:one or more\b)/i,/^(?:one or many\b)/i,/^(?:1\+)/i,/^(?:\|o\b)/i,/^(?:zero or one\b)/i,/^(?:zero or more\b)/i,/^(?:zero or many\b)/i,/^(?:0\+)/i,/^(?:\}o\b)/i,/^(?:many\(0\))/i,/^(?:many\(1\))/i,/^(?:many\b)/i,/^(?:\}\|)/i,/^(?:one\b)/i,/^(?:only one\b)/i,/^(?:1\b)/i,/^(?:\|\|)/i,/^(?:o\|)/i,/^(?:o\{)/i,/^(?:\|\{)/i,/^(?:\s*u\b)/i,/^(?:\.\.)/i,/^(?:--)/i,/^(?:to\b)/i,/^(?:optionally to\b)/i,/^(?:\.-)/i,/^(?:-\.)/i,/^(?:([^\x00-\x7F]|\w|-|\*)+)/i,/^(?:;)/i,/^(?:([^\x00-\x7F]|\w|-|\*)+)/i,/^(?:[0-9])/i,/^(?:.)/i,/^(?:$)/i],conditions:{style:{rules:[34,35,36,37,38,69,70],inclusive:!1},acc_descr_multiline:{rules:[5,6],inclusive:!1},acc_descr:{rules:[3],inclusive:!1},acc_title:{rules:[1],inclusive:!1},block:{rules:[23,24,25,26,27,28,29,30],inclusive:!1},INITIAL:{rules:[0,2,4,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,31,32,33,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,71,72,73,74],inclusive:!0}}};return R}();ct.lexer=Rt;function q(){this.yy={}}return u(q,"Parser"),q.prototype=ct,ct.Parser=q,new q}();dt.parser=dt;var Xt=dt,w,qt=(w=class{constructor(){this.entities=new Map,this.relationships=[],this.classes=new Map,this.direction="TB",this.Cardinality={ZERO_OR_ONE:"ZERO_OR_ONE",ZERO_OR_MORE:"ZERO_OR_MORE",ONE_OR_MORE:"ONE_OR_MORE",ONLY_ONE:"ONLY_ONE",MD_PARENT:"MD_PARENT"},this.Identification={NON_IDENTIFYING:"NON_IDENTIFYING",IDENTIFYING:"IDENTIFYING"},this.setAccTitle=Vt,this.getAccTitle=Lt,this.setAccDescription=Mt,this.getAccDescription=Bt,this.setDiagramTitle=Ft,this.getDiagramTitle=Yt,this.getConfig=u(()=>tt().er,"getConfig"),this.clear(),this.addEntity=this.addEntity.bind(this),this.addAttributes=this.addAttributes.bind(this),this.addRelationship=this.addRelationship.bind(this),this.setDirection=this.setDirection.bind(this),this.addCssStyles=this.addCssStyles.bind(this),this.addClass=this.addClass.bind(this),this.setClass=this.setClass.bind(this),this.setAccTitle=this.setAccTitle.bind(this),this.setAccDescription=this.setAccDescription.bind(this)}addEntity(i,h=""){var d;return this.entities.has(i)?!((d=this.entities.get(i))!=null&&d.alias)&&h&&(this.entities.get(i).alias=h,D.info(`Add alias '${h}' to entity '${i}'`)):(this.entities.set(i,{id:`entity-${i}-${this.entities.size}`,label:i,attributes:[],alias:h,shape:"erBox",look:tt().look??"default",cssClasses:"default",cssStyles:[]}),D.info("Added new entity :",i)),this.entities.get(i)}getEntity(i){return this.entities.get(i)}getEntities(){return this.entities}getClasses(){return this.classes}addAttributes(i,h){const d=this.addEntity(i);let o;for(o=h.length-1;o>=0;o--)h[o].keys||(h[o].keys=[]),h[o].comment||(h[o].comment=""),d.attributes.push(h[o]),D.debug("Added attribute ",h[o].name)}addRelationship(i,h,d,o){const l=this.entities.get(i),f=this.entities.get(d);if(!l||!f)return;const _={entityA:l.id,roleA:h,entityB:f.id,relSpec:o};this.relationships.push(_),D.debug("Added new relationship :",_)}getRelationships(){return this.relationships}getDirection(){return this.direction}setDirection(i){this.direction=i}getCompiledStyles(i){let h=[];for(const d of i){const o=this.classes.get(d);o!=null&&o.styles&&(h=[...h,...o.styles??[]].map(l=>l.trim())),o!=null&&o.textStyles&&(h=[...h,...o.textStyles??[]].map(l=>l.trim()))}return h}addCssStyles(i,h){for(const d of i){const o=this.entities.get(d);if(!h||!o)return;for(const l of h)o.cssStyles.push(l)}}addClass(i,h){i.forEach(d=>{let o=this.classes.get(d);o===void 0&&(o={id:d,styles:[],textStyles:[]},this.classes.set(d,o)),h&&h.forEach(function(l){if(/color/.exec(l)){const f=l.replace("fill","bgFill");o.textStyles.push(f)}o.styles.push(l)})})}setClass(i,h){for(const d of i){const o=this.entities.get(d);if(o)for(const l of h)o.cssClasses+=" "+l}}clear(){this.entities=new Map,this.classes=new Map,this.relationships=[],Pt()}getData(){const i=[],h=[],d=tt();for(const l of this.entities.keys()){const f=this.entities.get(l);f&&(f.cssCompiledStyles=this.getCompiledStyles(f.cssClasses.split(" ")),i.push(f))}let o=0;for(const l of this.relationships){const f={id:zt(l.entityA,l.entityB,{prefix:"id",counter:o++}),type:"normal",curve:"basis",start:l.entityA,end:l.entityB,label:l.roleA,labelpos:"c",thickness:"normal",classes:"relationshipLine",arrowTypeStart:l.relSpec.cardB.toLowerCase(),arrowTypeEnd:l.relSpec.cardA.toLowerCase(),pattern:l.relSpec.relType=="IDENTIFYING"?"solid":"dashed",look:d.look};h.push(f)}return{nodes:i,edges:h,other:{},config:d,direction:"TB"}}},u(w,"ErDB"),w),Nt={};Kt(Nt,{draw:()=>Ht});var Ht=u(async function(s,i,h,d){D.info("REF0:"),D.info("Drawing er diagram (unified)",i);const{securityLevel:o,er:l,layout:f}=tt(),_=d.db.getData(),E=Dt(i,o);_.type=d.type,_.layoutAlgorithm=Zt(f),_.config.flowchart.nodeSpacing=(l==null?void 0:l.nodeSpacing)||140,_.config.flowchart.rankSpacing=(l==null?void 0:l.rankSpacing)||80,_.direction=d.db.getDirection(),_.markers=["only_one","zero_or_one","one_or_more","zero_or_more"],_.diagramId=i,await Ut(_,E),_.layoutAlgorithm==="elk"&&E.select(".edges").lower();const V=E.selectAll('[id*="-background"]');Array.from(V).length>0&&V.each(function(){const S=jt(this),U=S.attr("id").replace("-background",""),T=E.select(`#${CSS.escape(U)}`);if(!T.empty()){const L=T.attr("transform");S.attr("transform",L)}});const Z=8;Wt.insertTitle(E,"erDiagramTitleText",(l==null?void 0:l.titleTopMargin)??25,d.db.getDiagramTitle()),wt(E,Z,"erDiagram",(l==null?void 0:l.useMaxWidth)??!0)},"draw"),Jt=u((s,i)=>{const h=Qt,d=h(s,"r"),o=h(s,"g"),l=h(s,"b");return Gt(d,o,l,i)},"fade"),$t=u(s=>`
  .entityBox {
    fill: ${s.mainBkg};
    stroke: ${s.nodeBorder};
  }

  .relationshipLabelBox {
    fill: ${s.tertiaryColor};
    opacity: 0.7;
    background-color: ${s.tertiaryColor};
      rect {
        opacity: 0.5;
      }
  }

  .labelBkg {
    background-color: ${Jt(s.tertiaryColor,.5)};
  }

  .edgeLabel .label {
    fill: ${s.nodeBorder};
    font-size: 14px;
  }

  .label {
    font-family: ${s.fontFamily};
    color: ${s.nodeTextColor||s.textColor};
  }

  .edge-pattern-dashed {
    stroke-dasharray: 8,8;
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon
  {
    fill: ${s.mainBkg};
    stroke: ${s.nodeBorder};
    stroke-width: 1px;
  }

  .relationshipLine {
    stroke: ${s.lineColor};
    stroke-width: 1;
    fill: none;
  }

  .marker {
    fill: none !important;
    stroke: ${s.lineColor} !important;
    stroke-width: 1;
  }
`,"getStyles"),te=$t,ae={parser:Xt,get db(){return new qt},renderer:Nt,styles:te};export{ae as diagram};
