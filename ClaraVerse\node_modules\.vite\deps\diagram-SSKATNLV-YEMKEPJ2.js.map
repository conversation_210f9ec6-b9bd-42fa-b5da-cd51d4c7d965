{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/diagram-SSKATNLV.mjs"], "sourcesContent": ["import {\n  populateCommonDb\n} from \"./chunk-4BMEZGHF.mjs\";\nimport {\n  cleanAndMerge\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-7B677QYD.mjs\";\nimport {\n  __name,\n  clear,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getDiagramTitle,\n  getThemeVariables,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/radar/db.ts\nvar defaultOptions = {\n  showLegend: true,\n  ticks: 5,\n  max: null,\n  min: 0,\n  graticule: \"circle\"\n};\nvar defaultRadarData = {\n  axes: [],\n  curves: [],\n  options: defaultOptions\n};\nvar data = structuredClone(defaultRadarData);\nvar DEFAULT_RADAR_CONFIG = defaultConfig_default.radar;\nvar getConfig2 = /* @__PURE__ */ __name(() => {\n  const config = cleanAndMerge({\n    ...DEFAULT_RADAR_CONFIG,\n    ...getConfig().radar\n  });\n  return config;\n}, \"getConfig\");\nvar getAxes = /* @__PURE__ */ __name(() => data.axes, \"getAxes\");\nvar getCurves = /* @__PURE__ */ __name(() => data.curves, \"getCurves\");\nvar getOptions = /* @__PURE__ */ __name(() => data.options, \"getOptions\");\nvar setAxes = /* @__PURE__ */ __name((axes) => {\n  data.axes = axes.map((axis) => {\n    return {\n      name: axis.name,\n      label: axis.label ?? axis.name\n    };\n  });\n}, \"setAxes\");\nvar setCurves = /* @__PURE__ */ __name((curves) => {\n  data.curves = curves.map((curve) => {\n    return {\n      name: curve.name,\n      label: curve.label ?? curve.name,\n      entries: computeCurveEntries(curve.entries)\n    };\n  });\n}, \"setCurves\");\nvar computeCurveEntries = /* @__PURE__ */ __name((entries) => {\n  if (entries[0].axis == void 0) {\n    return entries.map((entry) => entry.value);\n  }\n  const axes = getAxes();\n  if (axes.length === 0) {\n    throw new Error(\"Axes must be populated before curves for reference entries\");\n  }\n  return axes.map((axis) => {\n    const entry = entries.find((entry2) => entry2.axis?.$refText === axis.name);\n    if (entry === void 0) {\n      throw new Error(\"Missing entry for axis \" + axis.label);\n    }\n    return entry.value;\n  });\n}, \"computeCurveEntries\");\nvar setOptions = /* @__PURE__ */ __name((options) => {\n  const optionMap = options.reduce(\n    (acc, option) => {\n      acc[option.name] = option;\n      return acc;\n    },\n    {}\n  );\n  data.options = {\n    showLegend: optionMap.showLegend?.value ?? defaultOptions.showLegend,\n    ticks: optionMap.ticks?.value ?? defaultOptions.ticks,\n    max: optionMap.max?.value ?? defaultOptions.max,\n    min: optionMap.min?.value ?? defaultOptions.min,\n    graticule: optionMap.graticule?.value ?? defaultOptions.graticule\n  };\n}, \"setOptions\");\nvar clear2 = /* @__PURE__ */ __name(() => {\n  clear();\n  data = structuredClone(defaultRadarData);\n}, \"clear\");\nvar db = {\n  getAxes,\n  getCurves,\n  getOptions,\n  setAxes,\n  setCurves,\n  setOptions,\n  getConfig: getConfig2,\n  clear: clear2,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription\n};\n\n// src/diagrams/radar/parser.ts\nimport { parse } from \"@mermaid-js/parser\";\nvar populate = /* @__PURE__ */ __name((ast) => {\n  populateCommonDb(ast, db);\n  const { axes, curves, options } = ast;\n  db.setAxes(axes);\n  db.setCurves(curves);\n  db.setOptions(options);\n}, \"populate\");\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"radar\", input);\n    log.debug(ast);\n    populate(ast);\n  }, \"parse\")\n};\n\n// src/diagrams/radar/renderer.ts\nvar draw = /* @__PURE__ */ __name((_text, id, _version, diagram2) => {\n  const db2 = diagram2.db;\n  const axes = db2.getAxes();\n  const curves = db2.getCurves();\n  const options = db2.getOptions();\n  const config = db2.getConfig();\n  const title = db2.getDiagramTitle();\n  const svg = selectSvgElement(id);\n  const g = drawFrame(svg, config);\n  const maxValue = options.max ?? Math.max(...curves.map((curve) => Math.max(...curve.entries)));\n  const minValue = options.min;\n  const radius = Math.min(config.width, config.height) / 2;\n  drawGraticule(g, axes, radius, options.ticks, options.graticule);\n  drawAxes(g, axes, radius, config);\n  drawCurves(g, axes, curves, minValue, maxValue, options.graticule, config);\n  drawLegend(g, curves, options.showLegend, config);\n  g.append(\"text\").attr(\"class\", \"radarTitle\").text(title).attr(\"x\", 0).attr(\"y\", -config.height / 2 - config.marginTop);\n}, \"draw\");\nvar drawFrame = /* @__PURE__ */ __name((svg, config) => {\n  const totalWidth = config.width + config.marginLeft + config.marginRight;\n  const totalHeight = config.height + config.marginTop + config.marginBottom;\n  const center = {\n    x: config.marginLeft + config.width / 2,\n    y: config.marginTop + config.height / 2\n  };\n  svg.attr(\"viewbox\", `0 0 ${totalWidth} ${totalHeight}`).attr(\"width\", totalWidth).attr(\"height\", totalHeight);\n  return svg.append(\"g\").attr(\"transform\", `translate(${center.x}, ${center.y})`);\n}, \"drawFrame\");\nvar drawGraticule = /* @__PURE__ */ __name((g, axes, radius, ticks, graticule) => {\n  if (graticule === \"circle\") {\n    for (let i = 0; i < ticks; i++) {\n      const r = radius * (i + 1) / ticks;\n      g.append(\"circle\").attr(\"r\", r).attr(\"class\", \"radarGraticule\");\n    }\n  } else if (graticule === \"polygon\") {\n    const numAxes = axes.length;\n    for (let i = 0; i < ticks; i++) {\n      const r = radius * (i + 1) / ticks;\n      const points = axes.map((_, j) => {\n        const angle = 2 * j * Math.PI / numAxes - Math.PI / 2;\n        const x = r * Math.cos(angle);\n        const y = r * Math.sin(angle);\n        return `${x},${y}`;\n      }).join(\" \");\n      g.append(\"polygon\").attr(\"points\", points).attr(\"class\", \"radarGraticule\");\n    }\n  }\n}, \"drawGraticule\");\nvar drawAxes = /* @__PURE__ */ __name((g, axes, radius, config) => {\n  const numAxes = axes.length;\n  for (let i = 0; i < numAxes; i++) {\n    const label = axes[i].label;\n    const angle = 2 * i * Math.PI / numAxes - Math.PI / 2;\n    g.append(\"line\").attr(\"x1\", 0).attr(\"y1\", 0).attr(\"x2\", radius * config.axisScaleFactor * Math.cos(angle)).attr(\"y2\", radius * config.axisScaleFactor * Math.sin(angle)).attr(\"class\", \"radarAxisLine\");\n    g.append(\"text\").text(label).attr(\"x\", radius * config.axisLabelFactor * Math.cos(angle)).attr(\"y\", radius * config.axisLabelFactor * Math.sin(angle)).attr(\"class\", \"radarAxisLabel\");\n  }\n}, \"drawAxes\");\nfunction drawCurves(g, axes, curves, minValue, maxValue, graticule, config) {\n  const numAxes = axes.length;\n  const radius = Math.min(config.width, config.height) / 2;\n  curves.forEach((curve, index) => {\n    if (curve.entries.length !== numAxes) {\n      return;\n    }\n    const points = curve.entries.map((entry, i) => {\n      const angle = 2 * Math.PI * i / numAxes - Math.PI / 2;\n      const r = relativeRadius(entry, minValue, maxValue, radius);\n      const x = r * Math.cos(angle);\n      const y = r * Math.sin(angle);\n      return { x, y };\n    });\n    if (graticule === \"circle\") {\n      g.append(\"path\").attr(\"d\", closedRoundCurve(points, config.curveTension)).attr(\"class\", `radarCurve-${index}`);\n    } else if (graticule === \"polygon\") {\n      g.append(\"polygon\").attr(\"points\", points.map((p) => `${p.x},${p.y}`).join(\" \")).attr(\"class\", `radarCurve-${index}`);\n    }\n  });\n}\n__name(drawCurves, \"drawCurves\");\nfunction relativeRadius(value, minValue, maxValue, radius) {\n  const clippedValue = Math.min(Math.max(value, minValue), maxValue);\n  return radius * (clippedValue - minValue) / (maxValue - minValue);\n}\n__name(relativeRadius, \"relativeRadius\");\nfunction closedRoundCurve(points, tension) {\n  const numPoints = points.length;\n  let d = `M${points[0].x},${points[0].y}`;\n  for (let i = 0; i < numPoints; i++) {\n    const p0 = points[(i - 1 + numPoints) % numPoints];\n    const p1 = points[i];\n    const p2 = points[(i + 1) % numPoints];\n    const p3 = points[(i + 2) % numPoints];\n    const cp1 = {\n      x: p1.x + (p2.x - p0.x) * tension,\n      y: p1.y + (p2.y - p0.y) * tension\n    };\n    const cp2 = {\n      x: p2.x - (p3.x - p1.x) * tension,\n      y: p2.y - (p3.y - p1.y) * tension\n    };\n    d += ` C${cp1.x},${cp1.y} ${cp2.x},${cp2.y} ${p2.x},${p2.y}`;\n  }\n  return `${d} Z`;\n}\n__name(closedRoundCurve, \"closedRoundCurve\");\nfunction drawLegend(g, curves, showLegend, config) {\n  if (!showLegend) {\n    return;\n  }\n  const legendX = (config.width / 2 + config.marginRight) * 3 / 4;\n  const legendY = -(config.height / 2 + config.marginTop) * 3 / 4;\n  const lineHeight = 20;\n  curves.forEach((curve, index) => {\n    const itemGroup = g.append(\"g\").attr(\"transform\", `translate(${legendX}, ${legendY + index * lineHeight})`);\n    itemGroup.append(\"rect\").attr(\"width\", 12).attr(\"height\", 12).attr(\"class\", `radarLegendBox-${index}`);\n    itemGroup.append(\"text\").attr(\"x\", 16).attr(\"y\", 0).attr(\"class\", \"radarLegendText\").text(curve.label);\n  });\n}\n__name(drawLegend, \"drawLegend\");\nvar renderer = { draw };\n\n// src/diagrams/radar/styles.ts\nvar genIndexStyles = /* @__PURE__ */ __name((themeVariables, radarOptions) => {\n  let sections = \"\";\n  for (let i = 0; i < themeVariables.THEME_COLOR_LIMIT; i++) {\n    const indexColor = themeVariables[`cScale${i}`];\n    sections += `\n\t\t.radarCurve-${i} {\n\t\t\tcolor: ${indexColor};\n\t\t\tfill: ${indexColor};\n\t\t\tfill-opacity: ${radarOptions.curveOpacity};\n\t\t\tstroke: ${indexColor};\n\t\t\tstroke-width: ${radarOptions.curveStrokeWidth};\n\t\t}\n\t\t.radarLegendBox-${i} {\n\t\t\tfill: ${indexColor};\n\t\t\tfill-opacity: ${radarOptions.curveOpacity};\n\t\t\tstroke: ${indexColor};\n\t\t}\n\t\t`;\n  }\n  return sections;\n}, \"genIndexStyles\");\nvar buildRadarStyleOptions = /* @__PURE__ */ __name((radar) => {\n  const defaultThemeVariables = getThemeVariables();\n  const currentConfig = getConfig();\n  const themeVariables = cleanAndMerge(defaultThemeVariables, currentConfig.themeVariables);\n  const radarOptions = cleanAndMerge(themeVariables.radar, radar);\n  return { themeVariables, radarOptions };\n}, \"buildRadarStyleOptions\");\nvar styles = /* @__PURE__ */ __name(({ radar } = {}) => {\n  const { themeVariables, radarOptions } = buildRadarStyleOptions(radar);\n  return `\n\t.radarTitle {\n\t\tfont-size: ${themeVariables.fontSize};\n\t\tcolor: ${themeVariables.titleColor};\n\t\tdominant-baseline: hanging;\n\t\ttext-anchor: middle;\n\t}\n\t.radarAxisLine {\n\t\tstroke: ${radarOptions.axisColor};\n\t\tstroke-width: ${radarOptions.axisStrokeWidth};\n\t}\n\t.radarAxisLabel {\n\t\tdominant-baseline: middle;\n\t\ttext-anchor: middle;\n\t\tfont-size: ${radarOptions.axisLabelFontSize}px;\n\t\tcolor: ${radarOptions.axisColor};\n\t}\n\t.radarGraticule {\n\t\tfill: ${radarOptions.graticuleColor};\n\t\tfill-opacity: ${radarOptions.graticuleOpacity};\n\t\tstroke: ${radarOptions.graticuleColor};\n\t\tstroke-width: ${radarOptions.graticuleStrokeWidth};\n\t}\n\t.radarLegendText {\n\t\ttext-anchor: start;\n\t\tfont-size: ${radarOptions.legendFontSize}px;\n\t\tdominant-baseline: hanging;\n\t}\n\t${genIndexStyles(themeVariables, radarOptions)}\n\t`;\n}, \"styles\");\n\n// src/diagrams/radar/diagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer,\n  styles\n};\nexport {\n  diagram\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,IAAI,iBAAiB;AAAA,EACnB,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,KAAK;AAAA,EACL,KAAK;AAAA,EACL,WAAW;AACb;AACA,IAAI,mBAAmB;AAAA,EACrB,MAAM,CAAC;AAAA,EACP,QAAQ,CAAC;AAAA,EACT,SAAS;AACX;AACA,IAAI,OAAO,gBAAgB,gBAAgB;AAC3C,IAAI,uBAAuB,sBAAsB;AACjD,IAAI,aAA6B,OAAO,MAAM;AAC5C,QAAM,SAAS,cAAc;AAAA,IAC3B,GAAG;AAAA,IACH,GAAG,UAAU,EAAE;AAAA,EACjB,CAAC;AACD,SAAO;AACT,GAAG,WAAW;AACd,IAAI,UAA0B,OAAO,MAAM,KAAK,MAAM,SAAS;AAC/D,IAAI,YAA4B,OAAO,MAAM,KAAK,QAAQ,WAAW;AACrE,IAAI,aAA6B,OAAO,MAAM,KAAK,SAAS,YAAY;AACxE,IAAI,UAA0B,OAAO,CAAC,SAAS;AAC7C,OAAK,OAAO,KAAK,IAAI,CAAC,SAAS;AAC7B,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,OAAO,KAAK,SAAS,KAAK;AAAA,IAC5B;AAAA,EACF,CAAC;AACH,GAAG,SAAS;AACZ,IAAI,YAA4B,OAAO,CAAC,WAAW;AACjD,OAAK,SAAS,OAAO,IAAI,CAAC,UAAU;AAClC,WAAO;AAAA,MACL,MAAM,MAAM;AAAA,MACZ,OAAO,MAAM,SAAS,MAAM;AAAA,MAC5B,SAAS,oBAAoB,MAAM,OAAO;AAAA,IAC5C;AAAA,EACF,CAAC;AACH,GAAG,WAAW;AACd,IAAI,sBAAsC,OAAO,CAAC,YAAY;AAC5D,MAAI,QAAQ,CAAC,EAAE,QAAQ,QAAQ;AAC7B,WAAO,QAAQ,IAAI,CAAC,UAAU,MAAM,KAAK;AAAA,EAC3C;AACA,QAAM,OAAO,QAAQ;AACrB,MAAI,KAAK,WAAW,GAAG;AACrB,UAAM,IAAI,MAAM,4DAA4D;AAAA,EAC9E;AACA,SAAO,KAAK,IAAI,CAAC,SAAS;AACxB,UAAM,QAAQ,QAAQ,KAAK,CAAC,WAAQ;AA3ExC;AA2E2C,2BAAO,SAAP,mBAAa,cAAa,KAAK;AAAA,KAAI;AAC1E,QAAI,UAAU,QAAQ;AACpB,YAAM,IAAI,MAAM,4BAA4B,KAAK,KAAK;AAAA,IACxD;AACA,WAAO,MAAM;AAAA,EACf,CAAC;AACH,GAAG,qBAAqB;AACxB,IAAI,aAA6B,OAAO,CAAC,YAAY;AAlFrD;AAmFE,QAAM,YAAY,QAAQ;AAAA,IACxB,CAAC,KAAK,WAAW;AACf,UAAI,OAAO,IAAI,IAAI;AACnB,aAAO;AAAA,IACT;AAAA,IACA,CAAC;AAAA,EACH;AACA,OAAK,UAAU;AAAA,IACb,cAAY,eAAU,eAAV,mBAAsB,UAAS,eAAe;AAAA,IAC1D,SAAO,eAAU,UAAV,mBAAiB,UAAS,eAAe;AAAA,IAChD,OAAK,eAAU,QAAV,mBAAe,UAAS,eAAe;AAAA,IAC5C,OAAK,eAAU,QAAV,mBAAe,UAAS,eAAe;AAAA,IAC5C,aAAW,eAAU,cAAV,mBAAqB,UAAS,eAAe;AAAA,EAC1D;AACF,GAAG,YAAY;AACf,IAAI,SAAyB,OAAO,MAAM;AACxC,QAAM;AACN,SAAO,gBAAgB,gBAAgB;AACzC,GAAG,OAAO;AACV,IAAI,KAAK;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAIA,IAAI,WAA2B,OAAO,CAAC,QAAQ;AAC7C,mBAAiB,KAAK,EAAE;AACxB,QAAM,EAAE,MAAM,QAAQ,QAAQ,IAAI;AAClC,KAAG,QAAQ,IAAI;AACf,KAAG,UAAU,MAAM;AACnB,KAAG,WAAW,OAAO;AACvB,GAAG,UAAU;AACb,IAAI,SAAS;AAAA,EACX,OAAuB,OAAO,OAAO,UAAU;AAC7C,UAAM,MAAM,MAAM,MAAM,SAAS,KAAK;AACtC,QAAI,MAAM,GAAG;AACb,aAAS,GAAG;AAAA,EACd,GAAG,OAAO;AACZ;AAGA,IAAI,OAAuB,OAAO,CAAC,OAAO,IAAI,UAAU,aAAa;AACnE,QAAM,MAAM,SAAS;AACrB,QAAM,OAAO,IAAI,QAAQ;AACzB,QAAM,SAAS,IAAI,UAAU;AAC7B,QAAM,UAAU,IAAI,WAAW;AAC/B,QAAM,SAAS,IAAI,UAAU;AAC7B,QAAM,QAAQ,IAAI,gBAAgB;AAClC,QAAM,MAAM,iBAAiB,EAAE;AAC/B,QAAM,IAAI,UAAU,KAAK,MAAM;AAC/B,QAAM,WAAW,QAAQ,OAAO,KAAK,IAAI,GAAG,OAAO,IAAI,CAAC,UAAU,KAAK,IAAI,GAAG,MAAM,OAAO,CAAC,CAAC;AAC7F,QAAM,WAAW,QAAQ;AACzB,QAAM,SAAS,KAAK,IAAI,OAAO,OAAO,OAAO,MAAM,IAAI;AACvD,gBAAc,GAAG,MAAM,QAAQ,QAAQ,OAAO,QAAQ,SAAS;AAC/D,WAAS,GAAG,MAAM,QAAQ,MAAM;AAChC,aAAW,GAAG,MAAM,QAAQ,UAAU,UAAU,QAAQ,WAAW,MAAM;AACzE,aAAW,GAAG,QAAQ,QAAQ,YAAY,MAAM;AAChD,IAAE,OAAO,MAAM,EAAE,KAAK,SAAS,YAAY,EAAE,KAAK,KAAK,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,OAAO,SAAS,IAAI,OAAO,SAAS;AACvH,GAAG,MAAM;AACT,IAAI,YAA4B,OAAO,CAAC,KAAK,WAAW;AACtD,QAAM,aAAa,OAAO,QAAQ,OAAO,aAAa,OAAO;AAC7D,QAAM,cAAc,OAAO,SAAS,OAAO,YAAY,OAAO;AAC9D,QAAM,SAAS;AAAA,IACb,GAAG,OAAO,aAAa,OAAO,QAAQ;AAAA,IACtC,GAAG,OAAO,YAAY,OAAO,SAAS;AAAA,EACxC;AACA,MAAI,KAAK,WAAW,OAAO,UAAU,IAAI,WAAW,EAAE,EAAE,KAAK,SAAS,UAAU,EAAE,KAAK,UAAU,WAAW;AAC5G,SAAO,IAAI,OAAO,GAAG,EAAE,KAAK,aAAa,aAAa,OAAO,CAAC,KAAK,OAAO,CAAC,GAAG;AAChF,GAAG,WAAW;AACd,IAAI,gBAAgC,OAAO,CAAC,GAAG,MAAM,QAAQ,OAAO,cAAc;AAChF,MAAI,cAAc,UAAU;AAC1B,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,YAAM,IAAI,UAAU,IAAI,KAAK;AAC7B,QAAE,OAAO,QAAQ,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,gBAAgB;AAAA,IAChE;AAAA,EACF,WAAW,cAAc,WAAW;AAClC,UAAM,UAAU,KAAK;AACrB,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,YAAM,IAAI,UAAU,IAAI,KAAK;AAC7B,YAAM,SAAS,KAAK,IAAI,CAAC,GAAG,MAAM;AAChC,cAAM,QAAQ,IAAI,IAAI,KAAK,KAAK,UAAU,KAAK,KAAK;AACpD,cAAM,IAAI,IAAI,KAAK,IAAI,KAAK;AAC5B,cAAM,IAAI,IAAI,KAAK,IAAI,KAAK;AAC5B,eAAO,GAAG,CAAC,IAAI,CAAC;AAAA,MAClB,CAAC,EAAE,KAAK,GAAG;AACX,QAAE,OAAO,SAAS,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,SAAS,gBAAgB;AAAA,IAC3E;AAAA,EACF;AACF,GAAG,eAAe;AAClB,IAAI,WAA2B,OAAO,CAAC,GAAG,MAAM,QAAQ,WAAW;AACjE,QAAM,UAAU,KAAK;AACrB,WAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,UAAM,QAAQ,KAAK,CAAC,EAAE;AACtB,UAAM,QAAQ,IAAI,IAAI,KAAK,KAAK,UAAU,KAAK,KAAK;AACpD,MAAE,OAAO,MAAM,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,SAAS,OAAO,kBAAkB,KAAK,IAAI,KAAK,CAAC,EAAE,KAAK,MAAM,SAAS,OAAO,kBAAkB,KAAK,IAAI,KAAK,CAAC,EAAE,KAAK,SAAS,eAAe;AACtM,MAAE,OAAO,MAAM,EAAE,KAAK,KAAK,EAAE,KAAK,KAAK,SAAS,OAAO,kBAAkB,KAAK,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK,SAAS,OAAO,kBAAkB,KAAK,IAAI,KAAK,CAAC,EAAE,KAAK,SAAS,gBAAgB;AAAA,EACvL;AACF,GAAG,UAAU;AACb,SAAS,WAAW,GAAG,MAAM,QAAQ,UAAU,UAAU,WAAW,QAAQ;AAC1E,QAAM,UAAU,KAAK;AACrB,QAAM,SAAS,KAAK,IAAI,OAAO,OAAO,OAAO,MAAM,IAAI;AACvD,SAAO,QAAQ,CAAC,OAAO,UAAU;AAC/B,QAAI,MAAM,QAAQ,WAAW,SAAS;AACpC;AAAA,IACF;AACA,UAAM,SAAS,MAAM,QAAQ,IAAI,CAAC,OAAO,MAAM;AAC7C,YAAM,QAAQ,IAAI,KAAK,KAAK,IAAI,UAAU,KAAK,KAAK;AACpD,YAAM,IAAI,eAAe,OAAO,UAAU,UAAU,MAAM;AAC1D,YAAM,IAAI,IAAI,KAAK,IAAI,KAAK;AAC5B,YAAM,IAAI,IAAI,KAAK,IAAI,KAAK;AAC5B,aAAO,EAAE,GAAG,EAAE;AAAA,IAChB,CAAC;AACD,QAAI,cAAc,UAAU;AAC1B,QAAE,OAAO,MAAM,EAAE,KAAK,KAAK,iBAAiB,QAAQ,OAAO,YAAY,CAAC,EAAE,KAAK,SAAS,cAAc,KAAK,EAAE;AAAA,IAC/G,WAAW,cAAc,WAAW;AAClC,QAAE,OAAO,SAAS,EAAE,KAAK,UAAU,OAAO,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,SAAS,cAAc,KAAK,EAAE;AAAA,IACtH;AAAA,EACF,CAAC;AACH;AACA,OAAO,YAAY,YAAY;AAC/B,SAAS,eAAe,OAAO,UAAU,UAAU,QAAQ;AACzD,QAAM,eAAe,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ,GAAG,QAAQ;AACjE,SAAO,UAAU,eAAe,aAAa,WAAW;AAC1D;AACA,OAAO,gBAAgB,gBAAgB;AACvC,SAAS,iBAAiB,QAAQ,SAAS;AACzC,QAAM,YAAY,OAAO;AACzB,MAAI,IAAI,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC;AACtC,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,UAAM,KAAK,QAAQ,IAAI,IAAI,aAAa,SAAS;AACjD,UAAM,KAAK,OAAO,CAAC;AACnB,UAAM,KAAK,QAAQ,IAAI,KAAK,SAAS;AACrC,UAAM,KAAK,QAAQ,IAAI,KAAK,SAAS;AACrC,UAAM,MAAM;AAAA,MACV,GAAG,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK;AAAA,MAC1B,GAAG,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK;AAAA,IAC5B;AACA,UAAM,MAAM;AAAA,MACV,GAAG,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK;AAAA,MAC1B,GAAG,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK;AAAA,IAC5B;AACA,SAAK,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,EAC5D;AACA,SAAO,GAAG,CAAC;AACb;AACA,OAAO,kBAAkB,kBAAkB;AAC3C,SAAS,WAAW,GAAG,QAAQ,YAAY,QAAQ;AACjD,MAAI,CAAC,YAAY;AACf;AAAA,EACF;AACA,QAAM,WAAW,OAAO,QAAQ,IAAI,OAAO,eAAe,IAAI;AAC9D,QAAM,UAAU,EAAE,OAAO,SAAS,IAAI,OAAO,aAAa,IAAI;AAC9D,QAAM,aAAa;AACnB,SAAO,QAAQ,CAAC,OAAO,UAAU;AAC/B,UAAM,YAAY,EAAE,OAAO,GAAG,EAAE,KAAK,aAAa,aAAa,OAAO,KAAK,UAAU,QAAQ,UAAU,GAAG;AAC1G,cAAU,OAAO,MAAM,EAAE,KAAK,SAAS,EAAE,EAAE,KAAK,UAAU,EAAE,EAAE,KAAK,SAAS,kBAAkB,KAAK,EAAE;AACrG,cAAU,OAAO,MAAM,EAAE,KAAK,KAAK,EAAE,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,iBAAiB,EAAE,KAAK,MAAM,KAAK;AAAA,EACvG,CAAC;AACH;AACA,OAAO,YAAY,YAAY;AAC/B,IAAI,WAAW,EAAE,KAAK;AAGtB,IAAI,iBAAiC,OAAO,CAAC,gBAAgB,iBAAiB;AAC5E,MAAI,WAAW;AACf,WAAS,IAAI,GAAG,IAAI,eAAe,mBAAmB,KAAK;AACzD,UAAM,aAAa,eAAe,SAAS,CAAC,EAAE;AAC9C,gBAAY;AAAA,gBACA,CAAC;AAAA,YACL,UAAU;AAAA,WACX,UAAU;AAAA,mBACF,aAAa,YAAY;AAAA,aAC/B,UAAU;AAAA,mBACJ,aAAa,gBAAgB;AAAA;AAAA,oBAE5B,CAAC;AAAA,WACV,UAAU;AAAA,mBACF,aAAa,YAAY;AAAA,aAC/B,UAAU;AAAA;AAAA;AAAA,EAGrB;AACA,SAAO;AACT,GAAG,gBAAgB;AACnB,IAAI,yBAAyC,OAAO,CAAC,UAAU;AAC7D,QAAM,wBAAwB,mBAAkB;AAChD,QAAM,gBAAgB,UAAU;AAChC,QAAM,iBAAiB,cAAc,uBAAuB,cAAc,cAAc;AACxF,QAAM,eAAe,cAAc,eAAe,OAAO,KAAK;AAC9D,SAAO,EAAE,gBAAgB,aAAa;AACxC,GAAG,wBAAwB;AAC3B,IAAI,SAAyB,OAAO,CAAC,EAAE,MAAM,IAAI,CAAC,MAAM;AACtD,QAAM,EAAE,gBAAgB,aAAa,IAAI,uBAAuB,KAAK;AACrE,SAAO;AAAA;AAAA,eAEM,eAAe,QAAQ;AAAA,WAC3B,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,YAKxB,aAAa,SAAS;AAAA,kBAChB,aAAa,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,eAK/B,aAAa,iBAAiB;AAAA,WAClC,aAAa,SAAS;AAAA;AAAA;AAAA,UAGvB,aAAa,cAAc;AAAA,kBACnB,aAAa,gBAAgB;AAAA,YACnC,aAAa,cAAc;AAAA,kBACrB,aAAa,oBAAoB;AAAA;AAAA;AAAA;AAAA,eAIpC,aAAa,cAAc;AAAA;AAAA;AAAA,GAGvC,eAAe,gBAAgB,YAAY,CAAC;AAAA;AAE/C,GAAG,QAAQ;AAGX,IAAI,UAAU;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}