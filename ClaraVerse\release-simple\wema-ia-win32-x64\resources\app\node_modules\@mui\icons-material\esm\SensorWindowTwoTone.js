"use client";

import createSvgIcon from "./utils/createSvgIcon.js";
import { jsx as _jsx } from "react/jsx-runtime";
export default createSvgIcon([/*#__PURE__*/_jsx("path", {
  d: "M18 4v7h-4v-1h-4v1H6V4zM6 20v-7h12v7z",
  opacity: ".3"
}, "0"), /*#__PURE__*/_jsx("path", {
  d: "M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2m0 2v7h-4v-1h-4v1H6V4zM6 20v-7h12v7z"
}, "1")], 'SensorWindowTwoTone');