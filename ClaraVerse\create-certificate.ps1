# 🛡️ CRÉER CERTIFICAT SELF-SIGNED POUR WEMA IA
# Objectif: Signer l'EXE pour éviter Bkav Pro

Write-Host "🛡️ CRÉATION CERTIFICAT SELF-SIGNED" -ForegroundColor Green
Write-Host "Objectif: Signer WeMa IA pour éviter faux positifs" -ForegroundColor Yellow
Write-Host "=" * 60

# Paramètres du certificat
$certName = "WeMa IA Team"
$certSubject = "CN=WeMa IA Team, O=WeMa IA, C=FR"
$certStore = "Cert:\CurrentUser\My"
$pfxPassword = "WeMaIA2025!"

Write-Host "📋 Paramètres:" -ForegroundColor Cyan
Write-Host "  Nom: $certName"
Write-Host "  Sujet: $certSubject"
Write-Host "  Magasin: $certStore"

# Créer le certificat
Write-Host "🔧 Création du certificat..." -ForegroundColor Yellow

try {
    $cert = New-SelfSignedCertificate `
        -Subject $certSubject `
        -Type CodeSigningCert `
        -KeyUsage DigitalSignature `
        -FriendlyName "WeMa IA Code Signing Certificate" `
        -CertStoreLocation $certStore `
        -KeyLength 2048 `
        -Provider "Microsoft Enhanced RSA and AES Cryptographic Provider" `
        -KeyExportPolicy Exportable `
        -KeySpec Signature `
        -HashAlgorithm SHA256 `
        -NotAfter (Get-Date).AddYears(3)

    Write-Host "✅ Certificat créé avec succès!" -ForegroundColor Green
    Write-Host "📋 Thumbprint: $($cert.Thumbprint)" -ForegroundColor Cyan
    
    # Exporter en PFX
    $pfxPath = ".\wema-ia-cert.pfx"
    $securePassword = ConvertTo-SecureString -String $pfxPassword -Force -AsPlainText
    
    Export-PfxCertificate -Cert $cert -FilePath $pfxPath -Password $securePassword
    Write-Host "📁 Certificat exporté: $pfxPath" -ForegroundColor Green
    
    # Ajouter au magasin Trusted Root (pour éviter les warnings)
    Write-Host "🔧 Ajout au magasin Trusted Root..." -ForegroundColor Yellow
    $rootStore = Get-Item "Cert:\CurrentUser\Root"
    $rootStore.Open("ReadWrite")
    $rootStore.Add($cert)
    $rootStore.Close()
    
    Write-Host "✅ Certificat ajouté au magasin Trusted Root" -ForegroundColor Green
    
    # Informations pour la signature
    Write-Host ""
    Write-Host "🎯 INFORMATIONS POUR SIGNATURE:" -ForegroundColor Magenta
    Write-Host "  Fichier PFX: $pfxPath"
    Write-Host "  Mot de passe: $pfxPassword"
    Write-Host "  Thumbprint: $($cert.Thumbprint)"
    
    # Créer le fichier de config pour electron-builder
    $builderConfig = @"
{
  "win": {
    "certificateFile": "wema-ia-cert.pfx",
    "certificatePassword": "$pfxPassword",
    "signingHashAlgorithms": ["sha256"],
    "signAndEditExecutable": true,
    "signDlls": true
  }
}
"@
    
    $builderConfig | Out-File -FilePath "signing-config.json" -Encoding UTF8
    Write-Host "📁 Config electron-builder créée: signing-config.json" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "🎉 CERTIFICAT PRÊT POUR SIGNATURE!" -ForegroundColor Green
    Write-Host "Prochaine étape: Modifier package.json pour utiliser le certificat" -ForegroundColor Yellow
    
} catch {
    Write-Host "❌ Erreur lors de la création du certificat:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    exit 1
}
