
    $nsisUrl = "https://sourceforge.net/projects/nsis/files/NSIS%203/3.09/nsis-3.09-setup.exe/download"
    $nsisInstaller = "$env:TEMP\\nsis-setup.exe"
    
    Write-Host "Téléchargement NSIS..."
    Invoke-WebRequest -Uri $nsisUrl -OutFile $nsisInstaller -UseBasicParsing
    
    Write-Host "Installation NSIS..."
    Start-Process $nsisInstaller -ArgumentList "/S" -Wait
    
    Write-Host "NSIS installé !"
    