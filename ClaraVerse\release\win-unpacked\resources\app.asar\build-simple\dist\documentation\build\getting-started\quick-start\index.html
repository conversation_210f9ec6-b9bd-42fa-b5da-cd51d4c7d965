<!doctype html>
<html lang="en" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-getting-started/quick-start" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.8.0">
<title data-rh="true">Quick Start Guide | Clara Documentation</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://clara-docs.example.com/img/clara-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://clara-docs.example.com/img/clara-social-card.jpg"><meta data-rh="true" property="og:url" content="https://clara-docs.example.com/getting-started/quick-start"><meta data-rh="true" property="og:locale" content="en"><meta data-rh="true" name="docusaurus_locale" content="en"><meta data-rh="true" name="docsearch:language" content="en"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="Quick Start Guide | Clara Documentation"><meta data-rh="true" name="description" content="Get up and running with Clara in minutes"><meta data-rh="true" property="og:description" content="Get up and running with Clara in minutes"><link data-rh="true" rel="icon" href="/img/favicon.ico"><link data-rh="true" rel="canonical" href="https://clara-docs.example.com/getting-started/quick-start"><link data-rh="true" rel="alternate" href="https://clara-docs.example.com/getting-started/quick-start" hreflang="en"><link data-rh="true" rel="alternate" href="https://clara-docs.example.com/getting-started/quick-start" hreflang="x-default"><script data-rh="true" type="application/ld+json">{"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Quick Start Guide","item":"https://clara-docs.example.com/getting-started/quick-start"}]}</script><link rel="stylesheet" href="/assets/css/styles.d4e08c71.css">
<script src="/assets/js/runtime~main.63bcd324.js" defer="defer"></script>
<script src="/assets/js/main.a25a12dc.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<svg xmlns="http://www.w3.org/2000/svg" style="display: none;"><defs>
<symbol id="theme-svg-external-link" viewBox="0 0 24 24"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"/></symbol>
</defs></svg>
<script>!function(){var t=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();document.documentElement.setAttribute("data-theme",t||(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light")),document.documentElement.setAttribute("data-theme-choice",t||"system")}(),function(){try{const c=new URLSearchParams(window.location.search).entries();for(var[t,e]of c)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/img/logo.svg"><div role="region" aria-label="Skip to main content"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">Skip to main content</a></div><nav aria-label="Main" class="theme-layout-navbar navbar navbar--fixed-top"><div class="navbar__inner"><div class="theme-layout-navbar-left navbar__items"><button aria-label="Toggle navigation bar" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/"><div class="navbar__logo"><img src="/img/logo.svg" alt="Clara Logo" class="themedComponent_mlkZ themedComponent--light_NVdE"><img src="/img/logo.svg" alt="Clara Logo" class="themedComponent_mlkZ themedComponent--dark_xIcU"></div><b class="navbar__title text--truncate">Clara</b></a><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/">Documentation</a></div><div class="theme-layout-navbar-right navbar__items navbar__items--right"><a href="https://github.com/your-org/clara" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">GitHub<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a><div class="toggle_vylO colorModeToggle_DEke"><button class="clean-btn toggleButton_gllP toggleButtonDisabled_aARS" type="button" disabled="" title="system mode" aria-label="Switch between dark and light mode (currently system mode)"><svg viewBox="0 0 24 24" width="24" height="24" aria-hidden="true" class="toggleIcon_g3eP lightToggleIcon_pyhR"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" aria-hidden="true" class="toggleIcon_g3eP darkToggleIcon_wfgR"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" aria-hidden="true" class="toggleIcon_g3eP systemToggleIcon_QzmC"><path fill="currentColor" d="m12 21c4.971 0 9-4.029 9-9s-4.029-9-9-9-9 4.029-9 9 4.029 9 9 9zm4.95-13.95c1.313 1.313 2.05 3.093 2.05 4.95s-0.738 3.637-2.05 4.95c-1.313 1.313-3.093 2.05-4.95 2.05v-14c1.857 0 3.637 0.737 4.95 2.05z"></path></svg></button></div><div class="navbarSearchContainer_Bca1"></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="theme-layout-main main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="Docs sidebar" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/">Clara Documentation</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" href="/getting-started/introduction">Getting Started</a></div><ul class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/getting-started/introduction">Introduction to Clara</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/getting-started/installation">Installation</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/getting-started/quick-start">Quick Start Guide</a></li></ul></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="Breadcrumbs"><ul class="breadcrumbs"><li class="breadcrumbs__item"><a aria-label="Home page" class="breadcrumbs__link" href="/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">Getting Started</span></li><li class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link">Quick Start Guide</span></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">On this page</button></div><div class="theme-doc-markdown markdown"><header><h1>🚀 Quick Start Guide</h1></header>
<p>Get Clara up and running in just a few minutes! This guide will walk you through the essentials to start using Clara&#x27;s powerful AI-powered workspace.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="prerequisites">Prerequisites<a href="#prerequisites" class="hash-link" aria-label="Direct link to Prerequisites" title="Direct link to Prerequisites">​</a></h2>
<p>Before you begin, make sure you have:</p>
<ul>
<li><strong>Node.js</strong> (version 18 or higher)</li>
<li><strong>A modern web browser</strong> (Chrome, Firefox, Safari, Edge)</li>
<li><strong>An AI provider API key</strong> (OpenAI, Anthropic, or local Ollama)</li>
</ul>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="installation">Installation<a href="#installation" class="hash-link" aria-label="Direct link to Installation" title="Direct link to Installation">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="option-1-download-release-recommended">Option 1: Download Release (Recommended)<a href="#option-1-download-release-recommended" class="hash-link" aria-label="Direct link to Option 1: Download Release (Recommended)" title="Direct link to Option 1: Download Release (Recommended)">​</a></h3>
<ol>
<li>Visit the <a href="https://github.com/your-org/clara/releases" target="_blank" rel="noopener noreferrer">Clara Releases</a> page</li>
<li>Download the latest version for your platform:<!-- -->
<ul>
<li><strong>Windows</strong>: <code>Clara-Setup-x.x.x.exe</code></li>
<li><strong>macOS</strong>: <code>Clara-x.x.x.dmg</code></li>
<li><strong>Linux</strong>: <code>Clara-x.x.x.AppImage</code></li>
</ul>
</li>
<li>Install and launch Clara</li>
</ol>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="option-2-build-from-source">Option 2: Build from Source<a href="#option-2-build-from-source" class="hash-link" aria-label="Direct link to Option 2: Build from Source" title="Direct link to Option 2: Build from Source">​</a></h3>
<div class="language-bash codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-bash codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token comment" style="color:#999988;font-style:italic"># Clone the repository</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token function" style="color:#d73a49">git</span><span class="token plain"> clone https://github.com/your-org/clara.git</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token builtin class-name">cd</span><span class="token plain"> clara</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic"># Install dependencies</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token function" style="color:#d73a49">npm</span><span class="token plain"> </span><span class="token function" style="color:#d73a49">install</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic"># Start development server</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token function" style="color:#d73a49">npm</span><span class="token plain"> run dev</span><br></span></code></pre></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="first-launch-setup">First Launch Setup<a href="#first-launch-setup" class="hash-link" aria-label="Direct link to First Launch Setup" title="Direct link to First Launch Setup">​</a></h2>
<p>When you first open Clara, you&#x27;ll be guided through a quick onboarding process:</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="1-welcome-screen">1. Welcome Screen<a href="#1-welcome-screen" class="hash-link" aria-label="Direct link to 1. Welcome Screen" title="Direct link to 1. Welcome Screen">​</a></h3>
<ul>
<li>Enter your name for personalization</li>
<li>Choose your preferred theme (Light/Dark)</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="2-ai-provider-configuration">2. AI Provider Configuration<a href="#2-ai-provider-configuration" class="hash-link" aria-label="Direct link to 2. AI Provider Configuration" title="Direct link to 2. AI Provider Configuration">​</a></h3>
<p>Choose and configure your AI provider:</p>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="openai-setup">OpenAI Setup<a href="#openai-setup" class="hash-link" aria-label="Direct link to OpenAI Setup" title="Direct link to OpenAI Setup">​</a></h4>
<div class="language-bash codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-bash codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token comment" style="color:#999988;font-style:italic"># Required: Your OpenAI API key</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token assign-left variable" style="color:#36acaa">API_KEY</span><span class="token operator" style="color:#393A34">=</span><span class="token plain">sk-your-openai-api-key-here</span><br></span></code></pre></div></div>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="anthropic-setup">Anthropic Setup<a href="#anthropic-setup" class="hash-link" aria-label="Direct link to Anthropic Setup" title="Direct link to Anthropic Setup">​</a></h4>
<div class="language-bash codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-bash codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token comment" style="color:#999988;font-style:italic"># Required: Your Anthropic API key  </span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token assign-left variable" style="color:#36acaa">API_KEY</span><span class="token operator" style="color:#393A34">=</span><span class="token plain">sk-ant-your-anthropic-api-key-here</span><br></span></code></pre></div></div>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="ollama-setup-local">Ollama Setup (Local)<a href="#ollama-setup-local" class="hash-link" aria-label="Direct link to Ollama Setup (Local)" title="Direct link to Ollama Setup (Local)">​</a></h4>
<div class="language-bash codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-bash codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token comment" style="color:#999988;font-style:italic"># Install Ollama first</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token function" style="color:#d73a49">curl</span><span class="token plain"> </span><span class="token parameter variable" style="color:#36acaa">-fsSL</span><span class="token plain"> https://ollama.ai/install.sh </span><span class="token operator" style="color:#393A34">|</span><span class="token plain"> </span><span class="token function" style="color:#d73a49">sh</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic"># Pull a model</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">ollama pull llama2</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic"># Clara will auto-detect local Ollama</span><br></span></code></pre></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="3-verify-installation">3. Verify Installation<a href="#3-verify-installation" class="hash-link" aria-label="Direct link to 3. Verify Installation" title="Direct link to 3. Verify Installation">​</a></h3>
<p>After setup, test your installation:</p>
<ol>
<li><strong>Open Clara Assistant</strong> - Click the chat icon in the sidebar</li>
<li><strong>Send a test message</strong> - Try: &quot;Hello Clara, tell me about your features&quot;</li>
<li><strong>Check response</strong> - You should get a helpful response about Clara&#x27;s capabilities</li>
</ol>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="essential-first-steps">Essential First Steps<a href="#essential-first-steps" class="hash-link" aria-label="Direct link to Essential First Steps" title="Direct link to Essential First Steps">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="-try-the-ai-assistant">🤖 Try the AI Assistant<a href="#-try-the-ai-assistant" class="hash-link" aria-label="Direct link to 🤖 Try the AI Assistant" title="Direct link to 🤖 Try the AI Assistant">​</a></h3>
<ol>
<li>
<p><strong>Access the Assistant</strong>:</p>
<ul>
<li>Click the <strong>Bot</strong> icon in the sidebar</li>
<li>Or use the keyboard shortcut <code>Ctrl+Shift+A</code> (Windows/Linux) or <code>Cmd+Shift+A</code> (macOS)</li>
</ul>
</li>
<li>
<p><strong>Start a Conversation</strong>:</p>
<div class="language-text codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-text codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">Hello Clara! What can you help me with today?</span><br></span></code></pre></div></div>
</li>
<li>
<p><strong>Explore Capabilities</strong>:</p>
<ul>
<li>Ask questions about any topic</li>
<li>Request code assistance</li>
<li>Get help with writing and analysis</li>
<li>Use tools and integrations</li>
</ul>
</li>
</ol>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="-generate-your-first-image">🎨 Generate Your First Image<a href="#-generate-your-first-image" class="hash-link" aria-label="Direct link to 🎨 Generate Your First Image" title="Direct link to 🎨 Generate Your First Image">​</a></h3>
<ol>
<li>
<p><strong>Open Image Generation</strong>:</p>
<ul>
<li>Click <strong>Image</strong> in the sidebar</li>
<li>Or navigate to the Image Generation tab</li>
</ul>
</li>
<li>
<p><strong>Create an Image</strong>:</p>
<div class="language-text codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-text codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">A serene Japanese garden with cherry blossoms in full bloom, </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">soft pink petals falling, peaceful zen atmosphere, </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">high quality, detailed</span><br></span></code></pre></div></div>
</li>
<li>
<p><strong>Customize Settings</strong>:</p>
<ul>
<li>Adjust image size and quality</li>
<li>Try different art styles</li>
<li>Experiment with prompts</li>
</ul>
</li>
</ol>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="-build-your-first-agent">⚡ Build Your First Agent<a href="#-build-your-first-agent" class="hash-link" aria-label="Direct link to ⚡ Build Your First Agent" title="Direct link to ⚡ Build Your First Agent">​</a></h3>
<ol>
<li>
<p><strong>Access Agent Studio</strong>:</p>
<ul>
<li>Click <strong>Agents</strong> in the sidebar</li>
<li>Start with the &quot;Create New Agent&quot; button</li>
</ul>
</li>
<li>
<p><strong>Use the Visual Builder</strong>:</p>
<ul>
<li>Drag and drop nodes to create workflows</li>
<li>Connect inputs and outputs</li>
<li>Configure node parameters</li>
</ul>
</li>
<li>
<p><strong>Test Your Agent</strong>:</p>
<ul>
<li>Use the built-in testing tools</li>
<li>Debug workflows step by step</li>
<li>Export and share your agents</li>
</ul>
</li>
</ol>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="quick-tips-for-success">Quick Tips for Success<a href="#quick-tips-for-success" class="hash-link" aria-label="Direct link to Quick Tips for Success" title="Direct link to Quick Tips for Success">​</a></h2>
<div class="theme-admonition theme-admonition-tip admonition_xJq3 alert alert--success"><div class="admonitionHeading_Gvgb"><span class="admonitionIcon_Rf37"><svg viewBox="0 0 12 16"><path fill-rule="evenodd" d="M6.5 0C3.48 0 1 2.19 1 5c0 .92.55 2.25 1 3 1.34 2.25 1.78 2.78 2 4v1h5v-1c.22-1.22.66-1.75 2-4 .45-.75 1-2.08 1-3 0-2.81-2.48-5-5.5-5zm3.64 7.48c-.25.44-.47.8-.67 1.11-.86 1.41-1.25 2.06-1.45 3.23-.02.05-.02.11-.02.17H5c0-.06 0-.13-.02-.17-.2-1.17-.59-1.83-1.45-3.23-.2-.31-.42-.67-.67-1.11C2.44 6.78 2 5.65 2 5c0-2.2 2.02-4 4.5-4 1.22 0 2.36.42 3.22 1.19C10.55 2.94 11 3.94 11 5c0 .66-.44 1.78-.86 2.48zM4 14h5c-.23 1.14-1.3 2-2.5 2s-2.27-.86-2.5-2z"></path></svg></span>Pro Tips</div><div class="admonitionContent_BuS1"><ul>
<li><strong>Explore the Dashboard</strong>: Get familiar with the overview and quick actions</li>
<li><strong>Check Settings</strong>: Customize Clara to match your workflow preferences</li>
<li><strong>Use Keyboard Shortcuts</strong>: Speed up your workflow with built-in shortcuts</li>
<li><strong>Save Frequently</strong>: Your work is automatically saved, but manual saves are good practice</li>
</ul></div></div>
<div class="theme-admonition theme-admonition-warning admonition_xJq3 alert alert--warning"><div class="admonitionHeading_Gvgb"><span class="admonitionIcon_Rf37"><svg viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8.893 1.5c-.183-.31-.52-.5-.887-.5s-.703.19-.886.5L.138 13.499a.98.98 0 0 0 0 1.001c.193.31.53.501.886.501h13.964c.367 0 .704-.19.877-.5a1.03 1.03 0 0 0 .01-1.002L8.893 1.5zm.133 11.497H6.987v-2.003h2.039v2.003zm0-3.004H6.987V5.987h2.039v4.006z"></path></svg></span>Common Issues</div><div class="admonitionContent_BuS1"><ul>
<li><strong>API Keys</strong>: Make sure your AI provider API keys are valid and have sufficient credits</li>
<li><strong>Network</strong>: Clara requires internet connectivity for cloud AI providers</li>
<li><strong>Memory</strong>: For local models (Ollama), ensure you have sufficient RAM</li>
</ul></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="next-steps">Next Steps<a href="#next-steps" class="hash-link" aria-label="Direct link to Next Steps" title="Direct link to Next Steps">​</a></h2>
<p>Now that Clara is running, explore these features:</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="-configuration">🔧 <strong>Configuration</strong><a href="#-configuration" class="hash-link" aria-label="Direct link to -configuration" title="Direct link to -configuration">​</a></h3>
<p>Customize Clara&#x27;s behavior, themes, and integrations through the Settings page</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="-learn-more">📖 <strong>Learn More</strong><a href="#-learn-more" class="hash-link" aria-label="Direct link to -learn-more" title="Direct link to -learn-more">​</a></h3>
<p>Read the <a href="/getting-started/introduction">Introduction Guide</a> to understand Clara&#x27;s full capabilities</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="-installation-details">📚 <strong>Installation Details</strong><a href="#-installation-details" class="hash-link" aria-label="Direct link to -installation-details" title="Direct link to -installation-details">​</a></h3>
<p>Check the complete <a href="/getting-started/installation">Installation Guide</a> for advanced setup options</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="getting-help">Getting Help<a href="#getting-help" class="hash-link" aria-label="Direct link to Getting Help" title="Direct link to Getting Help">​</a></h2>
<p>If you run into any issues:</p>
<ul>
<li><strong>Documentation</strong>: Browse our comprehensive guides</li>
<li><strong>GitHub Issues</strong>: Report bugs or request features</li>
<li><strong>Community</strong>: Join discussions and get help from other users</li>
</ul>
<hr>
<p><strong>Congratulations!</strong> 🎉 You&#x27;re now ready to explore Clara&#x27;s full potential. The AI-powered workspace is at your fingertips!</p></div></article><nav class="docusaurus-mt-lg pagination-nav" aria-label="Docs pages"><a class="pagination-nav__link pagination-nav__link--prev" href="/getting-started/installation"><div class="pagination-nav__sublabel">Previous</div><div class="pagination-nav__label">Installation</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#prerequisites" class="table-of-contents__link toc-highlight">Prerequisites</a></li><li><a href="#installation" class="table-of-contents__link toc-highlight">Installation</a><ul><li><a href="#option-1-download-release-recommended" class="table-of-contents__link toc-highlight">Option 1: Download Release (Recommended)</a></li><li><a href="#option-2-build-from-source" class="table-of-contents__link toc-highlight">Option 2: Build from Source</a></li></ul></li><li><a href="#first-launch-setup" class="table-of-contents__link toc-highlight">First Launch Setup</a><ul><li><a href="#1-welcome-screen" class="table-of-contents__link toc-highlight">1. Welcome Screen</a></li><li><a href="#2-ai-provider-configuration" class="table-of-contents__link toc-highlight">2. AI Provider Configuration</a></li><li><a href="#3-verify-installation" class="table-of-contents__link toc-highlight">3. Verify Installation</a></li></ul></li><li><a href="#essential-first-steps" class="table-of-contents__link toc-highlight">Essential First Steps</a><ul><li><a href="#-try-the-ai-assistant" class="table-of-contents__link toc-highlight">🤖 Try the AI Assistant</a></li><li><a href="#-generate-your-first-image" class="table-of-contents__link toc-highlight">🎨 Generate Your First Image</a></li><li><a href="#-build-your-first-agent" class="table-of-contents__link toc-highlight">⚡ Build Your First Agent</a></li></ul></li><li><a href="#quick-tips-for-success" class="table-of-contents__link toc-highlight">Quick Tips for Success</a></li><li><a href="#next-steps" class="table-of-contents__link toc-highlight">Next Steps</a><ul><li><a href="#-configuration" class="table-of-contents__link toc-highlight">🔧 <strong>Configuration</strong></a></li><li><a href="#-learn-more" class="table-of-contents__link toc-highlight">📖 <strong>Learn More</strong></a></li><li><a href="#-installation-details" class="table-of-contents__link toc-highlight">📚 <strong>Installation Details</strong></a></li></ul></li><li><a href="#getting-help" class="table-of-contents__link toc-highlight">Getting Help</a></li></ul></div></div></div></div></main></div></div></div><footer class="theme-layout-footer footer footer--dark"><div class="container container-fluid"><div class="row footer__links"><div class="theme-layout-footer-column col footer__col"><div class="footer__title">Documentation</div><ul class="footer__items clean-list"><li class="footer__item"><a class="footer__link-item" href="/getting-started/introduction">Getting Started</a></li><li class="footer__item"><a class="footer__link-item" href="/getting-started/installation">Installation</a></li><li class="footer__item"><a class="footer__link-item" href="/getting-started/quick-start">Quick Start</a></li></ul></div><div class="theme-layout-footer-column col footer__col"><div class="footer__title">Clara</div><ul class="footer__items clean-list"><li class="footer__item"><a href="https://github.com/your-org/clara" target="_blank" rel="noopener noreferrer" class="footer__link-item">GitHub<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a></li></ul></div></div><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 Clara. Built with ❤️ and Docusaurus.</div></div></div></footer></div>
</body>
</html>