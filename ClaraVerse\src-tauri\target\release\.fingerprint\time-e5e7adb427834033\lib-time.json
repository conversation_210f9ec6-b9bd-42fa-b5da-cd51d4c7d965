{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"formatting\", \"local-offset\", \"macros\", \"parsing\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"formatting\", \"large-dates\", \"local-offset\", \"macros\", \"parsing\", \"quickcheck\", \"rand\", \"serde\", \"serde-human-readable\", \"serde-well-known\", \"std\", \"wasm-bindgen\"]", "target": 3713843603798095488, "profile": 14356718205376624770, "path": 7783011213072121854, "deps": [[253581978874359338, "deranged", false, 2667404053946752575], [724804171976944018, "num_conv", false, 10963769019856478301], [1509944293013079861, "time_macros", false, 9938770392902680830], [5901133744777009488, "powerfmt", false, 12958335821393572066], [7695812897323945497, "itoa", false, 16164747779741859888], [9886904983647127192, "time_core", false, 8600852529221888536]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\time-e5e7adb427834033\\dep-lib-time", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}