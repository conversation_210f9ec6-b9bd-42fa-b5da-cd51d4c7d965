{"version": 3, "sources": ["../../@iconify/utils/lib/icon/defaults.mjs", "../../@iconify/utils/lib/customisations/defaults.mjs", "../../@iconify/utils/lib/icon/name.mjs", "../../@iconify/utils/lib/icon/transformations.mjs", "../../@iconify/utils/lib/icon/merge.mjs", "../../@iconify/utils/lib/icon-set/tree.mjs", "../../@iconify/utils/lib/icon-set/get-icon.mjs", "../../@iconify/utils/lib/icon-set/validate-basic.mjs", "../../@iconify/utils/lib/icon-set/get-icons.mjs", "../../@iconify/utils/lib/svg/size.mjs", "../../@iconify/utils/lib/svg/defs.mjs", "../../@iconify/utils/lib/svg/build.mjs", "../../@iconify/utils/lib/svg/id.mjs", "../../@iconify/utils/lib/svg/html.mjs", "../../@iconify/utils/lib/colors/keywords.mjs", "../../@iconify/utils/lib/css/icons.mjs", "../../@iconify/utils/lib/loader/custom.mjs", "../../@iconify/utils/lib/loader/modern.mjs", "../../@iconify/utils/lib/loader/loader.mjs", "../../@iconify/utils/lib/emoji/format.mjs", "../../@iconify/utils/lib/index.mjs", "../../mermaid/dist/chunks/mermaid.core/chunk-H2D2JQ3I.mjs"], "sourcesContent": ["const defaultIconDimensions = Object.freeze(\n  {\n    left: 0,\n    top: 0,\n    width: 16,\n    height: 16\n  }\n);\nconst defaultIconTransformations = Object.freeze({\n  rotate: 0,\n  vFlip: false,\n  hFlip: false\n});\nconst defaultIconProps = Object.freeze({\n  ...defaultIconDimensions,\n  ...defaultIconTransformations\n});\nconst defaultExtendedIconProps = Object.freeze({\n  ...defaultIconProps,\n  body: \"\",\n  hidden: false\n});\n\nexport { defaultExtendedIconProps, defaultIconDimensions, defaultIconProps, defaultIconTransformations };\n", "import { defaultIconTransformations } from '../icon/defaults.mjs';\n\nconst defaultIconSizeCustomisations = Object.freeze({\n  width: null,\n  height: null\n});\nconst defaultIconCustomisations = Object.freeze({\n  // Dimensions\n  ...defaultIconSizeCustomisations,\n  // Transformations\n  ...defaultIconTransformations\n});\n\nexport { defaultIconCustomisations, defaultIconSizeCustomisations };\n", "const matchIconName = /^[a-z0-9]+(-[a-z0-9]+)*$/;\nconst stringToIcon = (value, validate, allowSimpleName, provider = \"\") => {\n  const colonSeparated = value.split(\":\");\n  if (value.slice(0, 1) === \"@\") {\n    if (colonSeparated.length < 2 || colonSeparated.length > 3) {\n      return null;\n    }\n    provider = colonSeparated.shift().slice(1);\n  }\n  if (colonSeparated.length > 3 || !colonSeparated.length) {\n    return null;\n  }\n  if (colonSeparated.length > 1) {\n    const name2 = colonSeparated.pop();\n    const prefix = colonSeparated.pop();\n    const result = {\n      // Allow provider without '@': \"provider:prefix:name\"\n      provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,\n      prefix,\n      name: name2\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  const name = colonSeparated[0];\n  const dashSeparated = name.split(\"-\");\n  if (dashSeparated.length > 1) {\n    const result = {\n      provider,\n      prefix: dashSeparated.shift(),\n      name: dashSeparated.join(\"-\")\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  if (allowSimpleName && provider === \"\") {\n    const result = {\n      provider,\n      prefix: \"\",\n      name\n    };\n    return validate && !validateIconName(result, allowSimpleName) ? null : result;\n  }\n  return null;\n};\nconst validateIconName = (icon, allowSimpleName) => {\n  if (!icon) {\n    return false;\n  }\n  return !!// Check prefix: cannot be empty, unless allowSimpleName is enabled\n  // Check name: cannot be empty\n  ((allowSimpleName && icon.prefix === \"\" || !!icon.prefix) && !!icon.name);\n};\n\nexport { matchIconName, stringToIcon, validateIconName };\n", "function mergeIconTransformations(obj1, obj2) {\n  const result = {};\n  if (!obj1.hFlip !== !obj2.hFlip) {\n    result.hFlip = true;\n  }\n  if (!obj1.vFlip !== !obj2.vFlip) {\n    result.vFlip = true;\n  }\n  const rotate = ((obj1.rotate || 0) + (obj2.rotate || 0)) % 4;\n  if (rotate) {\n    result.rotate = rotate;\n  }\n  return result;\n}\n\nexport { mergeIconTransformations };\n", "import { defaultExtendedIconProps, defaultIconTransformations } from './defaults.mjs';\nimport { mergeIconTransformations } from './transformations.mjs';\n\nfunction mergeIconData(parent, child) {\n  const result = mergeIconTransformations(parent, child);\n  for (const key in defaultExtendedIconProps) {\n    if (key in defaultIconTransformations) {\n      if (key in parent && !(key in result)) {\n        result[key] = defaultIconTransformations[key];\n      }\n    } else if (key in child) {\n      result[key] = child[key];\n    } else if (key in parent) {\n      result[key] = parent[key];\n    }\n  }\n  return result;\n}\n\nexport { mergeIconData };\n", "function getIconsTree(data, names) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  const resolved = /* @__PURE__ */ Object.create(null);\n  function resolve(name) {\n    if (icons[name]) {\n      return resolved[name] = [];\n    }\n    if (!(name in resolved)) {\n      resolved[name] = null;\n      const parent = aliases[name] && aliases[name].parent;\n      const value = parent && resolve(parent);\n      if (value) {\n        resolved[name] = [parent].concat(value);\n      }\n    }\n    return resolved[name];\n  }\n  (names || Object.keys(icons).concat(Object.keys(aliases))).forEach(resolve);\n  return resolved;\n}\n\nexport { getIconsTree };\n", "import { mergeIconData } from '../icon/merge.mjs';\nimport { getIconsTree } from './tree.mjs';\nimport '../icon/defaults.mjs';\nimport '../icon/transformations.mjs';\n\nfunction internalGetIconData(data, name, tree) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  let currentProps = {};\n  function parse(name2) {\n    currentProps = mergeIconData(\n      icons[name2] || aliases[name2],\n      currentProps\n    );\n  }\n  parse(name);\n  tree.forEach(parse);\n  return mergeIconData(data, currentProps);\n}\nfunction getIconData(data, name) {\n  if (data.icons[name]) {\n    return internalGetIconData(data, name, []);\n  }\n  const tree = getIconsTree(data, [name])[name];\n  return tree ? internalGetIconData(data, name, tree) : null;\n}\n\nexport { getIconData, internalGetIconData };\n", "import { defaultIconDimensions, defaultExtendedIconProps } from '../icon/defaults.mjs';\n\nconst optionalPropertyDefaults = {\n  provider: \"\",\n  aliases: {},\n  not_found: {},\n  ...defaultIconDimensions\n};\nfunction checkOptionalProps(item, defaults) {\n  for (const prop in defaults) {\n    if (prop in item && typeof item[prop] !== typeof defaults[prop]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction quicklyValidateIconSet(obj) {\n  if (typeof obj !== \"object\" || obj === null) {\n    return null;\n  }\n  const data = obj;\n  if (typeof data.prefix !== \"string\" || !obj.icons || typeof obj.icons !== \"object\") {\n    return null;\n  }\n  if (!checkOptionalProps(obj, optionalPropertyDefaults)) {\n    return null;\n  }\n  const icons = data.icons;\n  for (const name in icons) {\n    const icon = icons[name];\n    if (\n      // Name cannot be empty\n      !name || // Must have body\n      typeof icon.body !== \"string\" || // Check other props\n      !checkOptionalProps(\n        icon,\n        defaultExtendedIconProps\n      )\n    ) {\n      return null;\n    }\n  }\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  for (const name in aliases) {\n    const icon = aliases[name];\n    const parent = icon.parent;\n    if (\n      // Name cannot be empty\n      !name || // Parent must be set and point to existing icon\n      typeof parent !== \"string\" || !icons[parent] && !aliases[parent] || // Check other props\n      !checkOptionalProps(\n        icon,\n        defaultExtendedIconProps\n      )\n    ) {\n      return null;\n    }\n  }\n  return data;\n}\n\nexport { quicklyValidateIconSet };\n", "import { defaultIconDimensions } from '../icon/defaults.mjs';\nimport { getIconsTree } from './tree.mjs';\n\nconst propsToCopy = Object.keys(defaultIconDimensions).concat([\n  \"provider\"\n]);\nfunction getIcons(data, names, not_found) {\n  const icons = /* @__PURE__ */ Object.create(null);\n  const aliases = /* @__PURE__ */ Object.create(null);\n  const result = {\n    prefix: data.prefix,\n    icons\n  };\n  const sourceIcons = data.icons;\n  const sourceAliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  if (data.lastModified) {\n    result.lastModified = data.lastModified;\n  }\n  const tree = getIconsTree(data, names);\n  let empty = true;\n  for (const name in tree) {\n    if (!tree[name]) {\n      if (not_found && names.indexOf(name) !== -1) {\n        (result.not_found || (result.not_found = [])).push(name);\n      }\n    } else if (sourceIcons[name]) {\n      icons[name] = {\n        ...sourceIcons[name]\n      };\n      empty = false;\n    } else {\n      aliases[name] = {\n        ...sourceAliases[name]\n      };\n      result.aliases = aliases;\n    }\n  }\n  propsToCopy.forEach((attr) => {\n    if (attr in data) {\n      result[attr] = data[attr];\n    }\n  });\n  return empty && not_found !== true ? null : result;\n}\n\nexport { getIcons, propsToCopy };\n", "const unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;\nconst unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;\nfunction calculateSize(size, ratio, precision) {\n  if (ratio === 1) {\n    return size;\n  }\n  precision = precision || 100;\n  if (typeof size === \"number\") {\n    return Math.ceil(size * ratio * precision) / precision;\n  }\n  if (typeof size !== \"string\") {\n    return size;\n  }\n  const oldParts = size.split(unitsSplit);\n  if (oldParts === null || !oldParts.length) {\n    return size;\n  }\n  const newParts = [];\n  let code = oldParts.shift();\n  let isNumber = unitsTest.test(code);\n  while (true) {\n    if (isNumber) {\n      const num = parseFloat(code);\n      if (isNaN(num)) {\n        newParts.push(code);\n      } else {\n        newParts.push(Math.ceil(num * ratio * precision) / precision);\n      }\n    } else {\n      newParts.push(code);\n    }\n    code = oldParts.shift();\n    if (code === void 0) {\n      return newParts.join(\"\");\n    }\n    isNumber = !isNumber;\n  }\n}\n\nexport { calculateSize };\n", "function splitSVGDefs(content, tag = \"defs\") {\n  let defs = \"\";\n  const index = content.indexOf(\"<\" + tag);\n  while (index >= 0) {\n    const start = content.indexOf(\">\", index);\n    const end = content.indexOf(\"</\" + tag);\n    if (start === -1 || end === -1) {\n      break;\n    }\n    const endEnd = content.indexOf(\">\", end);\n    if (endEnd === -1) {\n      break;\n    }\n    defs += content.slice(start + 1, end).trim();\n    content = content.slice(0, index).trim() + content.slice(endEnd + 1);\n  }\n  return {\n    defs,\n    content\n  };\n}\nfunction mergeDefsAndContent(defs, content) {\n  return defs ? \"<defs>\" + defs + \"</defs>\" + content : content;\n}\nfunction wrapSVGContent(body, start, end) {\n  const split = splitSVGDefs(body);\n  return mergeDefsAndContent(split.defs, start + split.content + end);\n}\n\nexport { mergeDefsAndContent, splitSVGDefs, wrapSVGContent };\n", "import { defaultIconProps } from '../icon/defaults.mjs';\nimport { defaultIconCustomisations } from '../customisations/defaults.mjs';\nimport { calculateSize } from './size.mjs';\nimport { wrapSVGContent } from './defs.mjs';\n\nconst isUnsetKeyword = (value) => value === \"unset\" || value === \"undefined\" || value === \"none\";\nfunction iconToSVG(icon, customisations) {\n  const fullIcon = {\n    ...defaultIconProps,\n    ...icon\n  };\n  const fullCustomisations = {\n    ...defaultIconCustomisations,\n    ...customisations\n  };\n  const box = {\n    left: fullIcon.left,\n    top: fullIcon.top,\n    width: fullIcon.width,\n    height: fullIcon.height\n  };\n  let body = fullIcon.body;\n  [fullIcon, fullCustomisations].forEach((props) => {\n    const transformations = [];\n    const hFlip = props.hFlip;\n    const vFlip = props.vFlip;\n    let rotation = props.rotate;\n    if (hFlip) {\n      if (vFlip) {\n        rotation += 2;\n      } else {\n        transformations.push(\n          \"translate(\" + (box.width + box.left).toString() + \" \" + (0 - box.top).toString() + \")\"\n        );\n        transformations.push(\"scale(-1 1)\");\n        box.top = box.left = 0;\n      }\n    } else if (vFlip) {\n      transformations.push(\n        \"translate(\" + (0 - box.left).toString() + \" \" + (box.height + box.top).toString() + \")\"\n      );\n      transformations.push(\"scale(1 -1)\");\n      box.top = box.left = 0;\n    }\n    let tempValue;\n    if (rotation < 0) {\n      rotation -= Math.floor(rotation / 4) * 4;\n    }\n    rotation = rotation % 4;\n    switch (rotation) {\n      case 1:\n        tempValue = box.height / 2 + box.top;\n        transformations.unshift(\n          \"rotate(90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n      case 2:\n        transformations.unshift(\n          \"rotate(180 \" + (box.width / 2 + box.left).toString() + \" \" + (box.height / 2 + box.top).toString() + \")\"\n        );\n        break;\n      case 3:\n        tempValue = box.width / 2 + box.left;\n        transformations.unshift(\n          \"rotate(-90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n    }\n    if (rotation % 2 === 1) {\n      if (box.left !== box.top) {\n        tempValue = box.left;\n        box.left = box.top;\n        box.top = tempValue;\n      }\n      if (box.width !== box.height) {\n        tempValue = box.width;\n        box.width = box.height;\n        box.height = tempValue;\n      }\n    }\n    if (transformations.length) {\n      body = wrapSVGContent(\n        body,\n        '<g transform=\"' + transformations.join(\" \") + '\">',\n        \"</g>\"\n      );\n    }\n  });\n  const customisationsWidth = fullCustomisations.width;\n  const customisationsHeight = fullCustomisations.height;\n  const boxWidth = box.width;\n  const boxHeight = box.height;\n  let width;\n  let height;\n  if (customisationsWidth === null) {\n    height = customisationsHeight === null ? \"1em\" : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n    width = calculateSize(height, boxWidth / boxHeight);\n  } else {\n    width = customisationsWidth === \"auto\" ? boxWidth : customisationsWidth;\n    height = customisationsHeight === null ? calculateSize(width, boxHeight / boxWidth) : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n  }\n  const attributes = {};\n  const setAttr = (prop, value) => {\n    if (!isUnsetKeyword(value)) {\n      attributes[prop] = value.toString();\n    }\n  };\n  setAttr(\"width\", width);\n  setAttr(\"height\", height);\n  const viewBox = [box.left, box.top, boxWidth, boxHeight];\n  attributes.viewBox = viewBox.join(\" \");\n  return {\n    attributes,\n    viewBox,\n    body\n  };\n}\n\nexport { iconToSVG, isUnsetKeyword };\n", "const regex = /\\sid=\"(\\S+)\"/g;\nconst randomPrefix = \"IconifyId\" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);\nlet counter = 0;\nfunction replaceIDs(body, prefix = randomPrefix) {\n  const ids = [];\n  let match;\n  while (match = regex.exec(body)) {\n    ids.push(match[1]);\n  }\n  if (!ids.length) {\n    return body;\n  }\n  const suffix = \"suffix\" + (Math.random() * 16777216 | Date.now()).toString(16);\n  ids.forEach((id) => {\n    const newID = typeof prefix === \"function\" ? prefix(id) : prefix + (counter++).toString();\n    const escapedID = id.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n    body = body.replace(\n      // Allowed characters before id: [#;\"]\n      // Allowed characters after id: [)\"], .[a-z]\n      new RegExp('([#;\"])(' + escapedID + ')([\")]|\\\\.[a-z])', \"g\"),\n      \"$1\" + newID + suffix + \"$3\"\n    );\n  });\n  body = body.replace(new RegExp(suffix, \"g\"), \"\");\n  return body;\n}\n\nexport { replaceIDs };\n", "function iconToHTML(body, attributes) {\n  let renderAttribsHTML = body.indexOf(\"xlink:\") === -1 ? \"\" : ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"';\n  for (const attr in attributes) {\n    renderAttribsHTML += \" \" + attr + '=\"' + attributes[attr] + '\"';\n  }\n  return '<svg xmlns=\"http://www.w3.org/2000/svg\"' + renderAttribsHTML + \">\" + body + \"</svg>\";\n}\n\nexport { iconToHTML };\n", "const colorKeywords = {\n  transparent: {\n    type: \"transparent\"\n  },\n  none: {\n    type: \"none\"\n  },\n  currentcolor: {\n    type: \"current\"\n  }\n};\nfunction add(keyword, colors) {\n  const type = \"rgb\";\n  const r = colors[0];\n  const length = colors.length;\n  colorKeywords[keyword] = {\n    type,\n    r,\n    g: length > 1 ? colors[1] : r,\n    b: length > 2 ? colors[2] : r,\n    alpha: length > 3 ? colors[3] : 1\n  };\n}\nadd(\"silver\", [192]);\nadd(\"gray\", [128]);\nadd(\"white\", [255]);\nadd(\"maroon\", [128, 0, 0]);\nadd(\"red\", [255, 0, 0]);\nadd(\"purple\", [128, 0]);\nadd(\"fuchsia\", [255, 0]);\nadd(\"green\", [0, 128]);\nadd(\"lime\", [0, 255]);\nadd(\"olive\", [128, 128, 0]);\nadd(\"yellow\", [255, 255, 0]);\nadd(\"navy\", [0, 0, 128]);\nadd(\"blue\", [0, 0, 255]);\nadd(\"teal\", [0, 128, 128]);\nadd(\"aqua\", [0, 255, 255]);\nadd(\"aliceblue\", [240, 248, 255]);\nadd(\"antiquewhite\", [250, 235, 215]);\nadd(\"aqua\", [0, 255, 255]);\nadd(\"aquamarine\", [127, 255, 212]);\nadd(\"azure\", [240, 255, 255]);\nadd(\"beige\", [245, 245, 220]);\nadd(\"bisque\", [255, 228, 196]);\nadd(\"black\", [0]);\nadd(\"blanchedalmond\", [255, 235, 205]);\nadd(\"blue\", [0, 0, 255]);\nadd(\"blueviolet\", [138, 43, 226]);\nadd(\"brown\", [165, 42, 42]);\nadd(\"burlywood\", [222, 184, 135]);\nadd(\"cadetblue\", [95, 158, 160]);\nadd(\"chartreuse\", [127, 255, 0]);\nadd(\"chocolate\", [210, 105, 30]);\nadd(\"coral\", [255, 127, 80]);\nadd(\"cornflowerblue\", [100, 149, 237]);\nadd(\"cornsilk\", [255, 248, 220]);\nadd(\"crimson\", [220, 20, 60]);\nadd(\"cyan\", [0, 255, 255]);\nadd(\"darkblue\", [0, 0, 139]);\nadd(\"darkcyan\", [0, 139, 139]);\nadd(\"darkgoldenrod\", [184, 134, 11]);\nadd(\"darkgray\", [169]);\nadd(\"darkgreen\", [0, 100]);\nadd(\"darkgrey\", [169]);\nadd(\"darkkhaki\", [189, 183, 107]);\nadd(\"darkmagenta\", [139, 0]);\nadd(\"darkolivegreen\", [85, 107, 47]);\nadd(\"darkorange\", [255, 140, 0]);\nadd(\"darkorchid\", [153, 50, 204]);\nadd(\"darkred\", [139, 0, 0]);\nadd(\"darksalmon\", [233, 150, 122]);\nadd(\"darkseagreen\", [143, 188]);\nadd(\"darkslateblue\", [72, 61, 139]);\nadd(\"darkslategray\", [47, 79, 79]);\nadd(\"darkslategrey\", [47, 79, 79]);\nadd(\"darkturquoise\", [0, 206, 209]);\nadd(\"darkviolet\", [148, 0, 211]);\nadd(\"deeppink\", [255, 20, 147]);\nadd(\"deepskyblue\", [0, 191, 255]);\nadd(\"dimgray\", [105]);\nadd(\"dimgrey\", [105]);\nadd(\"dodgerblue\", [30, 144, 255]);\nadd(\"firebrick\", [178, 34, 34]);\nadd(\"floralwhite\", [255, 250, 240]);\nadd(\"forestgreen\", [34, 139]);\nadd(\"fuchsia\", [255, 0]);\nadd(\"gainsboro\", [220]);\nadd(\"ghostwhite\", [248, 248, 255]);\nadd(\"gold\", [255, 215, 0]);\nadd(\"goldenrod\", [218, 165, 32]);\nadd(\"gray\", [128]);\nadd(\"green\", [0, 128]);\nadd(\"greenyellow\", [173, 255, 47]);\nadd(\"grey\", [128]);\nadd(\"honeydew\", [240, 255]);\nadd(\"hotpink\", [255, 105, 180]);\nadd(\"indianred\", [205, 92, 92]);\nadd(\"indigo\", [75, 0, 130]);\nadd(\"ivory\", [255, 255, 240]);\nadd(\"khaki\", [240, 230, 140]);\nadd(\"lavender\", [230, 230, 250]);\nadd(\"lavenderblush\", [255, 240, 245]);\nadd(\"lawngreen\", [124, 252, 0]);\nadd(\"lemonchiffon\", [255, 250, 205]);\nadd(\"lightblue\", [173, 216, 230]);\nadd(\"lightcoral\", [240, 128, 128]);\nadd(\"lightcyan\", [224, 255, 255]);\nadd(\"lightgoldenrodyellow\", [250, 250, 210]);\nadd(\"lightgray\", [211]);\nadd(\"lightgreen\", [144, 238]);\nadd(\"lightgrey\", [211]);\nadd(\"lightpink\", [255, 182, 193]);\nadd(\"lightsalmon\", [255, 160, 122]);\nadd(\"lightseagreen\", [32, 178, 170]);\nadd(\"lightskyblue\", [135, 206, 250]);\nadd(\"lightslategray\", [119, 136, 153]);\nadd(\"lightslategrey\", [119, 136, 153]);\nadd(\"lightsteelblue\", [176, 196, 222]);\nadd(\"lightyellow\", [255, 255, 224]);\nadd(\"lime\", [0, 255]);\nadd(\"limegreen\", [50, 205]);\nadd(\"linen\", [250, 240, 230]);\nadd(\"magenta\", [255, 0]);\nadd(\"maroon\", [128, 0, 0]);\nadd(\"mediumaquamarine\", [102, 205, 170]);\nadd(\"mediumblue\", [0, 0, 205]);\nadd(\"mediumorchid\", [186, 85, 211]);\nadd(\"mediumpurple\", [147, 112, 219]);\nadd(\"mediumseagreen\", [60, 179, 113]);\nadd(\"mediumslateblue\", [123, 104, 238]);\nadd(\"mediumspringgreen\", [0, 250, 154]);\nadd(\"mediumturquoise\", [72, 209, 204]);\nadd(\"mediumvioletred\", [199, 21, 133]);\nadd(\"midnightblue\", [25, 25, 112]);\nadd(\"mintcream\", [245, 255, 250]);\nadd(\"mistyrose\", [255, 228, 225]);\nadd(\"moccasin\", [255, 228, 181]);\nadd(\"navajowhite\", [255, 222, 173]);\nadd(\"navy\", [0, 0, 128]);\nadd(\"oldlace\", [253, 245, 230]);\nadd(\"olive\", [128, 128, 0]);\nadd(\"olivedrab\", [107, 142, 35]);\nadd(\"orange\", [255, 165, 0]);\nadd(\"orangered\", [255, 69, 0]);\nadd(\"orchid\", [218, 112, 214]);\nadd(\"palegoldenrod\", [238, 232, 170]);\nadd(\"palegreen\", [152, 251]);\nadd(\"paleturquoise\", [175, 238, 238]);\nadd(\"palevioletred\", [219, 112, 147]);\nadd(\"papayawhip\", [255, 239, 213]);\nadd(\"peachpuff\", [255, 218, 185]);\nadd(\"peru\", [205, 133, 63]);\nadd(\"pink\", [255, 192, 203]);\nadd(\"plum\", [221, 160]);\nadd(\"powderblue\", [176, 224, 230]);\nadd(\"purple\", [128, 0]);\nadd(\"rebeccapurple\", [102, 51, 153]);\nadd(\"red\", [255, 0, 0]);\nadd(\"rosybrown\", [188, 143, 143]);\nadd(\"royalblue\", [65, 105, 225]);\nadd(\"saddlebrown\", [139, 69, 19]);\nadd(\"salmon\", [250, 128, 114]);\nadd(\"sandybrown\", [244, 164, 96]);\nadd(\"seagreen\", [46, 139, 87]);\nadd(\"seashell\", [255, 245, 238]);\nadd(\"sienna\", [160, 82, 45]);\nadd(\"silver\", [192]);\nadd(\"skyblue\", [135, 206, 235]);\nadd(\"slateblue\", [106, 90, 205]);\nadd(\"slategray\", [112, 128, 144]);\nadd(\"slategrey\", [112, 128, 144]);\nadd(\"snow\", [255, 250, 250]);\nadd(\"springgreen\", [0, 255, 127]);\nadd(\"steelblue\", [70, 130, 180]);\nadd(\"tan\", [210, 180, 140]);\nadd(\"teal\", [0, 128, 128]);\nadd(\"thistle\", [216, 191]);\nadd(\"tomato\", [255, 99, 71]);\nadd(\"turquoise\", [64, 224, 208]);\nadd(\"violet\", [238, 130]);\nadd(\"wheat\", [245, 222, 179]);\nadd(\"white\", [255]);\nadd(\"whitesmoke\", [245]);\nadd(\"yellow\", [255, 255, 0]);\nadd(\"yellowgreen\", [154, 205, 50]);\n\nexport { colorKeywords };\n", "import { getIconData } from '../icon-set/get-icon.mjs';\nimport { defaultIconProps } from '../icon/defaults.mjs';\nimport { getCommonCSSRules, generateItemCSSRules, generateItemContent } from './common.mjs';\nimport { formatCSS } from './format.mjs';\nimport '../icon/merge.mjs';\nimport '../icon/transformations.mjs';\nimport '../icon-set/tree.mjs';\nimport '../svg/html.mjs';\nimport '../svg/size.mjs';\nimport '../svg/url.mjs';\nimport '../icon/square.mjs';\nimport '../svg/build.mjs';\nimport '../customisations/defaults.mjs';\nimport '../svg/defs.mjs';\n\nconst commonSelector = \".icon--{prefix}\";\nconst iconSelector = \".icon--{prefix}--{name}\";\nconst contentSelector = \".icon--{prefix}--{name}::after\";\nconst defaultSelectors = {\n  commonSelector,\n  iconSelector,\n  overrideSelector: commonSelector + iconSelector\n};\nfunction getIconsCSSData(iconSet, names, options = {}) {\n  const css = [];\n  const errors = [];\n  const palette = options.color ? true : void 0;\n  let mode = options.mode || typeof palette === \"boolean\" && (palette ? \"background\" : \"mask\");\n  if (!mode) {\n    for (let i = 0; i < names.length; i++) {\n      const name = names[i];\n      const icon = getIconData(iconSet, name);\n      if (icon) {\n        const body = options.customise ? options.customise(icon.body, name) : icon.body;\n        mode = body.includes(\"currentColor\") ? \"mask\" : \"background\";\n        break;\n      }\n    }\n    if (!mode) {\n      mode = \"mask\";\n      errors.push(\n        \"/* cannot detect icon mode: not set in options and icon set is missing info, rendering as \" + mode + \" */\"\n      );\n    }\n  }\n  let varName = options.varName;\n  if (varName === void 0 && mode === \"mask\") {\n    varName = \"svg\";\n  }\n  const newOptions = {\n    ...options,\n    // Override mode and varName\n    mode,\n    varName\n  };\n  const { commonSelector: commonSelector2, iconSelector: iconSelector2, overrideSelector } = newOptions.iconSelector ? newOptions : defaultSelectors;\n  const iconSelectorWithPrefix = iconSelector2.replace(\n    /{prefix}/g,\n    iconSet.prefix\n  );\n  const commonRules = {\n    ...options.rules,\n    ...getCommonCSSRules(newOptions)\n  };\n  const hasCommonRules = commonSelector2 && commonSelector2 !== iconSelector2;\n  const commonSelectors = /* @__PURE__ */ new Set();\n  if (hasCommonRules) {\n    css.push({\n      selector: commonSelector2.replace(/{prefix}/g, iconSet.prefix),\n      rules: commonRules\n    });\n  }\n  for (let i = 0; i < names.length; i++) {\n    const name = names[i];\n    const iconData = getIconData(iconSet, name);\n    if (!iconData) {\n      errors.push(\"/* Could not find icon: \" + name + \" */\");\n      continue;\n    }\n    const body = options.customise ? options.customise(iconData.body, name) : iconData.body;\n    const rules = generateItemCSSRules(\n      {\n        ...defaultIconProps,\n        ...iconData,\n        body\n      },\n      newOptions\n    );\n    let requiresOverride = false;\n    if (hasCommonRules && overrideSelector) {\n      for (const key in rules) {\n        if (key in commonRules) {\n          requiresOverride = true;\n        }\n      }\n    }\n    const selector = (requiresOverride && overrideSelector ? overrideSelector.replace(/{prefix}/g, iconSet.prefix) : iconSelectorWithPrefix).replace(/{name}/g, name);\n    css.push({\n      selector,\n      rules\n    });\n    if (!hasCommonRules) {\n      commonSelectors.add(selector);\n    }\n  }\n  const result = {\n    css,\n    errors\n  };\n  if (!hasCommonRules && commonSelectors.size) {\n    const selector = Array.from(commonSelectors).join(\n      newOptions.format === \"compressed\" ? \",\" : \", \"\n    );\n    result.common = {\n      selector,\n      rules: commonRules\n    };\n  }\n  return result;\n}\nfunction getIconsCSS(iconSet, names, options = {}) {\n  const { css, errors, common } = getIconsCSSData(iconSet, names, options);\n  if (common) {\n    if (css.length === 1 && css[0].selector === common.selector) {\n      css[0].rules = {\n        // Common first, override later\n        ...common.rules,\n        ...css[0].rules\n      };\n    } else {\n      css.unshift(common);\n    }\n  }\n  return formatCSS(css, options.format) + (errors.length ? \"\\n\" + errors.join(\"\\n\") + \"\\n\" : \"\");\n}\nfunction getIconsContentCSS(iconSet, names, options) {\n  const errors = [];\n  const css = [];\n  const iconSelectorWithPrefix = (options.iconSelector ?? contentSelector).replace(/{prefix}/g, iconSet.prefix);\n  for (let i = 0; i < names.length; i++) {\n    const name = names[i];\n    const iconData = getIconData(iconSet, name);\n    if (!iconData) {\n      errors.push(\"/* Could not find icon: \" + name + \" */\");\n      continue;\n    }\n    const body = options.customise ? options.customise(iconData.body, name) : iconData.body;\n    const content = generateItemContent(\n      {\n        ...defaultIconProps,\n        ...iconData,\n        body\n      },\n      options\n    );\n    const selector = iconSelectorWithPrefix.replace(/{name}/g, name);\n    css.push({\n      selector,\n      rules: {\n        ...options.rules,\n        content\n      }\n    });\n  }\n  return formatCSS(css, options.format) + (errors.length ? \"\\n\" + errors.join(\"\\n\") + \"\\n\" : \"\");\n}\n\nexport { getIconsCSS, getIconsCSSData, getIconsContentCSS };\n", "import createDebugger from 'debug';\nimport { mergeIconProps } from './utils.mjs';\nimport { trimSVG } from '../svg/trim.mjs';\nimport '../svg/build.mjs';\nimport '../icon/defaults.mjs';\nimport '../customisations/defaults.mjs';\nimport '../svg/size.mjs';\nimport '../svg/defs.mjs';\n\nconst debug = createDebugger(\"@iconify-loader:custom\");\nasync function getCustomIcon(custom, collection, icon, options) {\n  let result;\n  debug(`${collection}:${icon}`);\n  try {\n    if (typeof custom === \"function\") {\n      result = await custom(icon);\n    } else {\n      const inline = custom[icon];\n      result = typeof inline === \"function\" ? await inline() : inline;\n    }\n  } catch (err) {\n    console.warn(\n      `Failed to load custom icon \"${icon}\" in \"${collection}\":`,\n      err\n    );\n    return;\n  }\n  if (result) {\n    const cleanupIdx = result.indexOf(\"<svg\");\n    if (cleanupIdx > 0)\n      result = result.slice(cleanupIdx);\n    const { transform } = options?.customizations ?? {};\n    result = typeof transform === \"function\" ? await transform(result, collection, icon) : result;\n    if (!result.startsWith(\"<svg\")) {\n      console.warn(\n        `Custom icon \"${icon}\" in \"${collection}\" is not a valid SVG`\n      );\n      return result;\n    }\n    return await mergeIconProps(\n      options?.customizations?.trimCustomSvg === true ? trimSVG(result) : result,\n      collection,\n      icon,\n      options,\n      void 0\n    );\n  }\n}\n\nexport { getCustomIcon };\n", "import { iconToSVG, isUnsetKeyword } from '../svg/build.mjs';\nimport { getIconData } from '../icon-set/get-icon.mjs';\nimport { calculateSize } from '../svg/size.mjs';\nimport { mergeIconProps } from './utils.mjs';\nimport createDebugger from 'debug';\nimport { defaultIconCustomisations } from '../customisations/defaults.mjs';\nimport '../icon/defaults.mjs';\nimport '../svg/defs.mjs';\nimport '../icon/merge.mjs';\nimport '../icon/transformations.mjs';\nimport '../icon-set/tree.mjs';\n\nconst debug = createDebugger(\"@iconify-loader:icon\");\nasync function searchForIcon(iconSet, collection, ids, options) {\n  let iconData;\n  const { customize } = options?.customizations ?? {};\n  for (const id of ids) {\n    iconData = getIconData(iconSet, id);\n    if (iconData) {\n      debug(`${collection}:${id}`);\n      let defaultCustomizations = {\n        ...defaultIconCustomisations\n      };\n      if (typeof customize === \"function\") {\n        iconData = Object.assign({}, iconData);\n        defaultCustomizations = customize(\n          defaultCustomizations,\n          iconData,\n          `${collection}:${id}`\n        ) ?? defaultCustomizations;\n      }\n      const {\n        attributes: { width, height, ...restAttributes },\n        body\n      } = iconToSVG(iconData, defaultCustomizations);\n      const scale = options?.scale;\n      return await mergeIconProps(\n        // DON'T remove space on <svg >\n        `<svg >${body}</svg>`,\n        collection,\n        id,\n        options,\n        () => {\n          return { ...restAttributes };\n        },\n        (props) => {\n          const check = (prop, defaultValue) => {\n            const propValue = props[prop];\n            let value;\n            if (!isUnsetKeyword(propValue)) {\n              if (propValue) {\n                return;\n              }\n              if (typeof scale === \"number\") {\n                if (scale) {\n                  value = calculateSize(\n                    // Base on result from iconToSVG() or 1em\n                    defaultValue ?? \"1em\",\n                    scale\n                  );\n                }\n              } else {\n                value = defaultValue;\n              }\n            }\n            if (!value) {\n              delete props[prop];\n            } else {\n              props[prop] = value;\n            }\n          };\n          check(\"width\", width);\n          check(\"height\", height);\n        }\n      );\n    }\n  }\n}\n\nexport { searchForIcon };\n", "import { getCustomIcon } from './custom.mjs';\nimport { searchForIcon } from './modern.mjs';\nimport 'debug';\nimport './utils.mjs';\nimport '../svg/build.mjs';\nimport '../icon/defaults.mjs';\nimport '../customisations/defaults.mjs';\nimport '../svg/size.mjs';\nimport '../svg/defs.mjs';\nimport '../svg/trim.mjs';\nimport '../icon-set/get-icon.mjs';\nimport '../icon/merge.mjs';\nimport '../icon/transformations.mjs';\nimport '../icon-set/tree.mjs';\n\nconst loadIcon = async (collection, icon, options) => {\n  const custom = options?.customCollections?.[collection];\n  if (custom) {\n    if (typeof custom === \"function\") {\n      let result;\n      try {\n        result = await custom(icon);\n      } catch (err) {\n        console.warn(\n          `Failed to load custom icon \"${icon}\" in \"${collection}\":`,\n          err\n        );\n        return;\n      }\n      if (result) {\n        if (typeof result === \"string\") {\n          return await getCustomIcon(\n            () => result,\n            collection,\n            icon,\n            options\n          );\n        }\n        if (\"icons\" in result) {\n          const ids = [\n            icon,\n            icon.replace(/([a-z])([A-Z])/g, \"$1-$2\").toLowerCase(),\n            icon.replace(/([a-z])(\\d+)/g, \"$1-$2\")\n          ];\n          return await searchForIcon(\n            result,\n            collection,\n            ids,\n            options\n          );\n        }\n      }\n    } else {\n      return await getCustomIcon(custom, collection, icon, options);\n    }\n  }\n};\n\nexport { loadIcon };\n", "import { convertEmojiSequenceToUTF16, convertEmojiSequenceToUTF32 } from './convert.mjs';\nimport './data.mjs';\n\nconst defaultUnicodeOptions = {\n  prefix: \"\",\n  separator: \"\",\n  case: \"lower\",\n  format: \"utf-32\",\n  add0: false,\n  throwOnError: true\n};\nfunction convert(sequence, options) {\n  const prefix = options.prefix;\n  const func = options.case === \"upper\" ? \"toUpperCase\" : \"toLowerCase\";\n  const cleanSequence = options.format === \"utf-16\" ? convertEmojiSequenceToUTF16(sequence) : convertEmojiSequenceToUTF32(sequence, options.throwOnError);\n  return cleanSequence.map((code) => {\n    let str = code.toString(16);\n    if (options.add0 && str.length < 4) {\n      str = \"0\".repeat(4 - str.length) + str;\n    }\n    return prefix + str[func]();\n  }).join(options.separator);\n}\nfunction getEmojiUnicodeString(code, options = {}) {\n  return convert([code], {\n    ...defaultUnicodeOptions,\n    ...options\n  });\n}\nconst defaultSequenceOptions = {\n  ...defaultUnicodeOptions,\n  separator: \"-\"\n};\nfunction getEmojiSequenceString(sequence, options = {}) {\n  return convert(sequence, {\n    ...defaultSequenceOptions,\n    ...options\n  });\n}\nfunction getEmojiSequenceKeyword(sequence) {\n  return sequence.map((code) => code.toString(16)).join(\"-\");\n}\n\nexport { getEmojiSequenceKeyword, getEmojiSequenceString, getEmojiUnicodeString };\n", "export { defaultIconCustomisations, defaultIconSizeCustomisations } from './customisations/defaults.mjs';\nexport { mergeCustomisations } from './customisations/merge.mjs';\nexport { toBoolean } from './customisations/bool.mjs';\nexport { flipFromString } from './customisations/flip.mjs';\nexport { rotateFromString } from './customisations/rotate.mjs';\nexport { matchIconName, stringToIcon, validateIconName } from './icon/name.mjs';\nexport { mergeIconData } from './icon/merge.mjs';\nexport { mergeIconTransformations } from './icon/transformations.mjs';\nexport { defaultExtendedIconProps, defaultIconDimensions, defaultIconProps, defaultIconTransformations } from './icon/defaults.mjs';\nexport { makeIconSquare } from './icon/square.mjs';\nexport { getIconsTree } from './icon-set/tree.mjs';\nexport { parseIconSet, parseIconSetAsync } from './icon-set/parse.mjs';\nexport { validateIconSet } from './icon-set/validate.mjs';\nexport { quicklyValidateIconSet } from './icon-set/validate-basic.mjs';\nexport { expandIconSet } from './icon-set/expand.mjs';\nexport { minifyIconSet } from './icon-set/minify.mjs';\nexport { getIcons } from './icon-set/get-icons.mjs';\nexport { getIconData } from './icon-set/get-icon.mjs';\nexport { convertIconSetInfo } from './icon-set/convert-info.mjs';\nexport { iconToSVG } from './svg/build.mjs';\nexport { mergeDefsAndContent, splitSVGDefs, wrapSVGContent } from './svg/defs.mjs';\nexport { replaceIDs } from './svg/id.mjs';\nexport { calculateSize } from './svg/size.mjs';\nexport { encodeSvgForCss } from './svg/encode-svg-for-css.mjs';\nexport { trimSVG } from './svg/trim.mjs';\nexport { prettifySVG } from './svg/pretty.mjs';\nexport { iconToHTML } from './svg/html.mjs';\nexport { svgToData, svgToURL } from './svg/url.mjs';\nexport { cleanUpInnerHTML } from './svg/inner-html.mjs';\nexport { getSVGViewBox } from './svg/viewbox.mjs';\nexport { buildParsedSVG, convertParsedSVG, parseSVGContent } from './svg/parse.mjs';\nexport { colorKeywords } from './colors/keywords.mjs';\nexport { colorToString, compareColors, stringToColor } from './colors/index.mjs';\nexport { getIconCSS, getIconContentCSS } from './css/icon.mjs';\nexport { getIconsCSS, getIconsContentCSS } from './css/icons.mjs';\nexport { mergeIconProps } from './loader/utils.mjs';\nexport { getCustomIcon } from './loader/custom.mjs';\nexport { searchForIcon } from './loader/modern.mjs';\nexport { loadIcon } from './loader/loader.mjs';\nexport { getEmojiSequenceFromString, getUnqualifiedEmojiSequence } from './emoji/cleanup.mjs';\nexport { convertEmojiSequenceToUTF16, convertEmojiSequenceToUTF32, getEmojiCodePoint, getEmojiUnicode, isUTF32SplitNumber, mergeUTF32Numbers, splitUTF32Number } from './emoji/convert.mjs';\nexport { getEmojiSequenceKeyword, getEmojiSequenceString, getEmojiUnicodeString } from './emoji/format.mjs';\nexport { parseEmojiTestFile } from './emoji/test/parse.mjs';\nexport { getQualifiedEmojiVariations } from './emoji/test/variations.mjs';\nexport { findMissingEmojis } from './emoji/test/missing.mjs';\nexport { createOptimisedRegex, createOptimisedRegexForEmojiSequences } from './emoji/regex/create.mjs';\nexport { prepareEmojiForIconSet, prepareEmojiForIconsList } from './emoji/parse.mjs';\nexport { findAndReplaceEmojisInText } from './emoji/replace/replace.mjs';\nexport { camelToKebab, camelize, pascalize, snakelize } from './misc/strings.mjs';\nexport { commonObjectProps, compareObjects, unmergeObjects } from './misc/objects.mjs';\nexport { sanitiseTitleAttribute } from './misc/title.mjs';\nimport './css/common.mjs';\nimport './css/format.mjs';\nimport 'debug';\nimport './emoji/data.mjs';\nimport './emoji/test/components.mjs';\nimport './emoji/regex/tree.mjs';\nimport './emoji/regex/base.mjs';\nimport './emoji/regex/numbers.mjs';\nimport './emoji/regex/similar.mjs';\nimport './emoji/test/similar.mjs';\nimport './emoji/test/name.mjs';\nimport './emoji/test/tree.mjs';\nimport './emoji/replace/find.mjs';\n", "import {\n  __name,\n  log\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/icons.ts\nimport { getIconData, iconToHTML, iconToSVG, replaceIDs, stringToIcon } from \"@iconify/utils\";\nvar unknownIcon = {\n  body: '<g><rect width=\"80\" height=\"80\" style=\"fill: #087ebf; stroke-width: 0px;\"/><text transform=\"translate(21.16 64.67)\" style=\"fill: #fff; font-family: ArialMT, Arial; font-size: 67.75px;\"><tspan x=\"0\" y=\"0\">?</tspan></text></g>',\n  height: 80,\n  width: 80\n};\nvar iconsStore = /* @__PURE__ */ new Map();\nvar loaderStore = /* @__PURE__ */ new Map();\nvar registerIconPacks = /* @__PURE__ */ __name((iconLoaders) => {\n  for (const iconLoader of iconLoaders) {\n    if (!iconLoader.name) {\n      throw new Error(\n        'Invalid icon loader. Must have a \"name\" property with non-empty string value.'\n      );\n    }\n    log.debug(\"Registering icon pack:\", iconLoader.name);\n    if (\"loader\" in iconLoader) {\n      loaderStore.set(iconLoader.name, iconLoader.loader);\n    } else if (\"icons\" in iconLoader) {\n      iconsStore.set(iconLoader.name, iconLoader.icons);\n    } else {\n      log.error(\"Invalid icon loader:\", iconLoader);\n      throw new Error('Invalid icon loader. Must have either \"icons\" or \"loader\" property.');\n    }\n  }\n}, \"registerIconPacks\");\nvar getRegisteredIconData = /* @__PURE__ */ __name(async (iconName, fallbackPrefix) => {\n  const data = stringToIcon(iconName, true, fallbackPrefix !== void 0);\n  if (!data) {\n    throw new Error(`Invalid icon name: ${iconName}`);\n  }\n  const prefix = data.prefix || fallbackPrefix;\n  if (!prefix) {\n    throw new Error(`Icon name must contain a prefix: ${iconName}`);\n  }\n  let icons = iconsStore.get(prefix);\n  if (!icons) {\n    const loader = loaderStore.get(prefix);\n    if (!loader) {\n      throw new Error(`Icon set not found: ${data.prefix}`);\n    }\n    try {\n      const loaded = await loader();\n      icons = { ...loaded, prefix };\n      iconsStore.set(prefix, icons);\n    } catch (e) {\n      log.error(e);\n      throw new Error(`Failed to load icon set: ${data.prefix}`);\n    }\n  }\n  const iconData = getIconData(icons, data.name);\n  if (!iconData) {\n    throw new Error(`Icon not found: ${iconName}`);\n  }\n  return iconData;\n}, \"getRegisteredIconData\");\nvar getIconSVG = /* @__PURE__ */ __name(async (iconName, customisations) => {\n  let iconData;\n  try {\n    iconData = await getRegisteredIconData(iconName, customisations?.fallbackPrefix);\n  } catch (e) {\n    log.error(e);\n    iconData = unknownIcon;\n  }\n  const renderData = iconToSVG(iconData, customisations);\n  const svg = iconToHTML(replaceIDs(renderData.body), renderData.attributes);\n  return svg;\n}, \"getIconSVG\");\n\nexport {\n  unknownIcon,\n  registerIconPacks,\n  getIconSVG\n};\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAM,wBAAwB,OAAO;AAAA,EACnC;AAAA,IACE,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACF;AACA,IAAM,6BAA6B,OAAO,OAAO;AAAA,EAC/C,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AACT,CAAC;AACD,IAAM,mBAAmB,OAAO,OAAO;AAAA,EACrC,GAAG;AAAA,EACH,GAAG;AACL,CAAC;AACD,IAAM,2BAA2B,OAAO,OAAO;AAAA,EAC7C,GAAG;AAAA,EACH,MAAM;AAAA,EACN,QAAQ;AACV,CAAC;;;ACnBD,IAAM,gCAAgC,OAAO,OAAO;AAAA,EAClD,OAAO;AAAA,EACP,QAAQ;AACV,CAAC;AACD,IAAM,4BAA4B,OAAO,OAAO;AAAA;AAAA,EAE9C,GAAG;AAAA;AAAA,EAEH,GAAG;AACL,CAAC;;;ACVD,IAAM,eAAe,CAAC,OAAO,UAAU,iBAAiB,WAAW,OAAO;AACxE,QAAM,iBAAiB,MAAM,MAAM,GAAG;AACtC,MAAI,MAAM,MAAM,GAAG,CAAC,MAAM,KAAK;AAC7B,QAAI,eAAe,SAAS,KAAK,eAAe,SAAS,GAAG;AAC1D,aAAO;AAAA,IACT;AACA,eAAW,eAAe,MAAM,EAAE,MAAM,CAAC;AAAA,EAC3C;AACA,MAAI,eAAe,SAAS,KAAK,CAAC,eAAe,QAAQ;AACvD,WAAO;AAAA,EACT;AACA,MAAI,eAAe,SAAS,GAAG;AAC7B,UAAM,QAAQ,eAAe,IAAI;AACjC,UAAM,SAAS,eAAe,IAAI;AAClC,UAAM,SAAS;AAAA;AAAA,MAEb,UAAU,eAAe,SAAS,IAAI,eAAe,CAAC,IAAI;AAAA,MAC1D;AAAA,MACA,MAAM;AAAA,IACR;AACA,WAAO,YAAY,CAAC,iBAAiB,MAAM,IAAI,OAAO;AAAA,EACxD;AACA,QAAM,OAAO,eAAe,CAAC;AAC7B,QAAM,gBAAgB,KAAK,MAAM,GAAG;AACpC,MAAI,cAAc,SAAS,GAAG;AAC5B,UAAM,SAAS;AAAA,MACb;AAAA,MACA,QAAQ,cAAc,MAAM;AAAA,MAC5B,MAAM,cAAc,KAAK,GAAG;AAAA,IAC9B;AACA,WAAO,YAAY,CAAC,iBAAiB,MAAM,IAAI,OAAO;AAAA,EACxD;AACA,MAAI,mBAAmB,aAAa,IAAI;AACtC,UAAM,SAAS;AAAA,MACb;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,IACF;AACA,WAAO,YAAY,CAAC,iBAAiB,QAAQ,eAAe,IAAI,OAAO;AAAA,EACzE;AACA,SAAO;AACT;AACA,IAAM,mBAAmB,CAAC,MAAM,oBAAoB;AAClD,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,SAAO,CAAC;AAAA;AAAA,IAEN,mBAAmB,KAAK,WAAW,MAAM,CAAC,CAAC,KAAK,WAAW,CAAC,CAAC,KAAK;AACtE;;;AClDA,SAAS,yBAAyB,MAAM,MAAM;AAC5C,QAAM,SAAS,CAAC;AAChB,MAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO;AAC/B,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO;AAC/B,WAAO,QAAQ;AAAA,EACjB;AACA,QAAM,WAAW,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM;AAC3D,MAAI,QAAQ;AACV,WAAO,SAAS;AAAA,EAClB;AACA,SAAO;AACT;;;ACVA,SAAS,cAAc,QAAQ,OAAO;AACpC,QAAM,SAAS,yBAAyB,QAAQ,KAAK;AACrD,aAAW,OAAO,0BAA0B;AAC1C,QAAI,OAAO,4BAA4B;AACrC,UAAI,OAAO,UAAU,EAAE,OAAO,SAAS;AACrC,eAAO,GAAG,IAAI,2BAA2B,GAAG;AAAA,MAC9C;AAAA,IACF,WAAW,OAAO,OAAO;AACvB,aAAO,GAAG,IAAI,MAAM,GAAG;AAAA,IACzB,WAAW,OAAO,QAAQ;AACxB,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;;;ACjBA,SAAS,aAAa,MAAM,OAAO;AACjC,QAAM,QAAQ,KAAK;AACnB,QAAM,UAAU,KAAK,WAA2B,uBAAO,OAAO,IAAI;AAClE,QAAM,WAA2B,uBAAO,OAAO,IAAI;AACnD,WAAS,QAAQ,MAAM;AACrB,QAAI,MAAM,IAAI,GAAG;AACf,aAAO,SAAS,IAAI,IAAI,CAAC;AAAA,IAC3B;AACA,QAAI,EAAE,QAAQ,WAAW;AACvB,eAAS,IAAI,IAAI;AACjB,YAAM,SAAS,QAAQ,IAAI,KAAK,QAAQ,IAAI,EAAE;AAC9C,YAAM,QAAQ,UAAU,QAAQ,MAAM;AACtC,UAAI,OAAO;AACT,iBAAS,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,KAAK;AAAA,MACxC;AAAA,IACF;AACA,WAAO,SAAS,IAAI;AAAA,EACtB;AACA,GAAC,SAAS,OAAO,KAAK,KAAK,EAAE,OAAO,OAAO,KAAK,OAAO,CAAC,GAAG,QAAQ,OAAO;AAC1E,SAAO;AACT;;;ACfA,SAAS,oBAAoB,MAAM,MAAM,MAAM;AAC7C,QAAM,QAAQ,KAAK;AACnB,QAAM,UAAU,KAAK,WAA2B,uBAAO,OAAO,IAAI;AAClE,MAAI,eAAe,CAAC;AACpB,WAAS,MAAM,OAAO;AACpB,mBAAe;AAAA,MACb,MAAM,KAAK,KAAK,QAAQ,KAAK;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AACA,QAAM,IAAI;AACV,OAAK,QAAQ,KAAK;AAClB,SAAO,cAAc,MAAM,YAAY;AACzC;AACA,SAAS,YAAY,MAAM,MAAM;AAC/B,MAAI,KAAK,MAAM,IAAI,GAAG;AACpB,WAAO,oBAAoB,MAAM,MAAM,CAAC,CAAC;AAAA,EAC3C;AACA,QAAM,OAAO,aAAa,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI;AAC5C,SAAO,OAAO,oBAAoB,MAAM,MAAM,IAAI,IAAI;AACxD;;;ACvBA,IAAM,2BAA2B;AAAA,EAC/B,UAAU;AAAA,EACV,SAAS,CAAC;AAAA,EACV,WAAW,CAAC;AAAA,EACZ,GAAG;AACL;;;ACJA,IAAM,cAAc,OAAO,KAAK,qBAAqB,EAAE,OAAO;AAAA,EAC5D;AACF,CAAC;;;ACLD,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,SAAS,cAAc,MAAM,OAAO,WAAW;AAC7C,MAAI,UAAU,GAAG;AACf,WAAO;AAAA,EACT;AACA,cAAY,aAAa;AACzB,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,KAAK,KAAK,OAAO,QAAQ,SAAS,IAAI;AAAA,EAC/C;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,QAAM,WAAW,KAAK,MAAM,UAAU;AACtC,MAAI,aAAa,QAAQ,CAAC,SAAS,QAAQ;AACzC,WAAO;AAAA,EACT;AACA,QAAM,WAAW,CAAC;AAClB,MAAI,OAAO,SAAS,MAAM;AAC1B,MAAI,WAAW,UAAU,KAAK,IAAI;AAClC,SAAO,MAAM;AACX,QAAI,UAAU;AACZ,YAAM,MAAM,WAAW,IAAI;AAC3B,UAAI,MAAM,GAAG,GAAG;AACd,iBAAS,KAAK,IAAI;AAAA,MACpB,OAAO;AACL,iBAAS,KAAK,KAAK,KAAK,MAAM,QAAQ,SAAS,IAAI,SAAS;AAAA,MAC9D;AAAA,IACF,OAAO;AACL,eAAS,KAAK,IAAI;AAAA,IACpB;AACA,WAAO,SAAS,MAAM;AACtB,QAAI,SAAS,QAAQ;AACnB,aAAO,SAAS,KAAK,EAAE;AAAA,IACzB;AACA,eAAW,CAAC;AAAA,EACd;AACF;;;ACrCA,SAAS,aAAa,SAAS,MAAM,QAAQ;AAC3C,MAAI,OAAO;AACX,QAAM,QAAQ,QAAQ,QAAQ,MAAM,GAAG;AACvC,SAAO,SAAS,GAAG;AACjB,UAAM,QAAQ,QAAQ,QAAQ,KAAK,KAAK;AACxC,UAAM,MAAM,QAAQ,QAAQ,OAAO,GAAG;AACtC,QAAI,UAAU,MAAM,QAAQ,IAAI;AAC9B;AAAA,IACF;AACA,UAAM,SAAS,QAAQ,QAAQ,KAAK,GAAG;AACvC,QAAI,WAAW,IAAI;AACjB;AAAA,IACF;AACA,YAAQ,QAAQ,MAAM,QAAQ,GAAG,GAAG,EAAE,KAAK;AAC3C,cAAU,QAAQ,MAAM,GAAG,KAAK,EAAE,KAAK,IAAI,QAAQ,MAAM,SAAS,CAAC;AAAA,EACrE;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,oBAAoB,MAAM,SAAS;AAC1C,SAAO,OAAO,WAAW,OAAO,YAAY,UAAU;AACxD;AACA,SAAS,eAAe,MAAM,OAAO,KAAK;AACxC,QAAM,QAAQ,aAAa,IAAI;AAC/B,SAAO,oBAAoB,MAAM,MAAM,QAAQ,MAAM,UAAU,GAAG;AACpE;;;ACtBA,IAAM,iBAAiB,CAAC,UAAU,UAAU,WAAW,UAAU,eAAe,UAAU;AAC1F,SAAS,UAAU,MAAM,gBAAgB;AACvC,QAAM,WAAW;AAAA,IACf,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,qBAAqB;AAAA,IACzB,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,MAAM;AAAA,IACV,MAAM,SAAS;AAAA,IACf,KAAK,SAAS;AAAA,IACd,OAAO,SAAS;AAAA,IAChB,QAAQ,SAAS;AAAA,EACnB;AACA,MAAI,OAAO,SAAS;AACpB,GAAC,UAAU,kBAAkB,EAAE,QAAQ,CAAC,UAAU;AAChD,UAAM,kBAAkB,CAAC;AACzB,UAAM,QAAQ,MAAM;AACpB,UAAM,QAAQ,MAAM;AACpB,QAAI,WAAW,MAAM;AACrB,QAAI,OAAO;AACT,UAAI,OAAO;AACT,oBAAY;AAAA,MACd,OAAO;AACL,wBAAgB;AAAA,UACd,gBAAgB,IAAI,QAAQ,IAAI,MAAM,SAAS,IAAI,OAAO,IAAI,IAAI,KAAK,SAAS,IAAI;AAAA,QACtF;AACA,wBAAgB,KAAK,aAAa;AAClC,YAAI,MAAM,IAAI,OAAO;AAAA,MACvB;AAAA,IACF,WAAW,OAAO;AAChB,sBAAgB;AAAA,QACd,gBAAgB,IAAI,IAAI,MAAM,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,KAAK,SAAS,IAAI;AAAA,MACvF;AACA,sBAAgB,KAAK,aAAa;AAClC,UAAI,MAAM,IAAI,OAAO;AAAA,IACvB;AACA,QAAI;AACJ,QAAI,WAAW,GAAG;AAChB,kBAAY,KAAK,MAAM,WAAW,CAAC,IAAI;AAAA,IACzC;AACA,eAAW,WAAW;AACtB,YAAQ,UAAU;AAAA,MAChB,KAAK;AACH,oBAAY,IAAI,SAAS,IAAI,IAAI;AACjC,wBAAgB;AAAA,UACd,eAAe,UAAU,SAAS,IAAI,MAAM,UAAU,SAAS,IAAI;AAAA,QACrE;AACA;AAAA,MACF,KAAK;AACH,wBAAgB;AAAA,UACd,iBAAiB,IAAI,QAAQ,IAAI,IAAI,MAAM,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,IAAI,KAAK,SAAS,IAAI;AAAA,QACxG;AACA;AAAA,MACF,KAAK;AACH,oBAAY,IAAI,QAAQ,IAAI,IAAI;AAChC,wBAAgB;AAAA,UACd,gBAAgB,UAAU,SAAS,IAAI,MAAM,UAAU,SAAS,IAAI;AAAA,QACtE;AACA;AAAA,IACJ;AACA,QAAI,WAAW,MAAM,GAAG;AACtB,UAAI,IAAI,SAAS,IAAI,KAAK;AACxB,oBAAY,IAAI;AAChB,YAAI,OAAO,IAAI;AACf,YAAI,MAAM;AAAA,MACZ;AACA,UAAI,IAAI,UAAU,IAAI,QAAQ;AAC5B,oBAAY,IAAI;AAChB,YAAI,QAAQ,IAAI;AAChB,YAAI,SAAS;AAAA,MACf;AAAA,IACF;AACA,QAAI,gBAAgB,QAAQ;AAC1B,aAAO;AAAA,QACL;AAAA,QACA,mBAAmB,gBAAgB,KAAK,GAAG,IAAI;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,sBAAsB,mBAAmB;AAC/C,QAAM,uBAAuB,mBAAmB;AAChD,QAAM,WAAW,IAAI;AACrB,QAAM,YAAY,IAAI;AACtB,MAAI;AACJ,MAAI;AACJ,MAAI,wBAAwB,MAAM;AAChC,aAAS,yBAAyB,OAAO,QAAQ,yBAAyB,SAAS,YAAY;AAC/F,YAAQ,cAAc,QAAQ,WAAW,SAAS;AAAA,EACpD,OAAO;AACL,YAAQ,wBAAwB,SAAS,WAAW;AACpD,aAAS,yBAAyB,OAAO,cAAc,OAAO,YAAY,QAAQ,IAAI,yBAAyB,SAAS,YAAY;AAAA,EACtI;AACA,QAAM,aAAa,CAAC;AACpB,QAAM,UAAU,CAAC,MAAM,UAAU;AAC/B,QAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,iBAAW,IAAI,IAAI,MAAM,SAAS;AAAA,IACpC;AAAA,EACF;AACA,UAAQ,SAAS,KAAK;AACtB,UAAQ,UAAU,MAAM;AACxB,QAAM,UAAU,CAAC,IAAI,MAAM,IAAI,KAAK,UAAU,SAAS;AACvD,aAAW,UAAU,QAAQ,KAAK,GAAG;AACrC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACpHA,IAAM,QAAQ;AACd,IAAM,eAAe,cAAc,KAAK,IAAI,EAAE,SAAS,EAAE,KAAK,KAAK,OAAO,IAAI,WAAW,GAAG,SAAS,EAAE;AACvG,IAAI,UAAU;AACd,SAAS,WAAW,MAAM,SAAS,cAAc;AAC/C,QAAM,MAAM,CAAC;AACb,MAAI;AACJ,SAAO,QAAQ,MAAM,KAAK,IAAI,GAAG;AAC/B,QAAI,KAAK,MAAM,CAAC,CAAC;AAAA,EACnB;AACA,MAAI,CAAC,IAAI,QAAQ;AACf,WAAO;AAAA,EACT;AACA,QAAM,SAAS,YAAY,KAAK,OAAO,IAAI,WAAW,KAAK,IAAI,GAAG,SAAS,EAAE;AAC7E,MAAI,QAAQ,CAAC,OAAO;AAClB,UAAM,QAAQ,OAAO,WAAW,aAAa,OAAO,EAAE,IAAI,UAAU,WAAW,SAAS;AACxF,UAAM,YAAY,GAAG,QAAQ,uBAAuB,MAAM;AAC1D,WAAO,KAAK;AAAA;AAAA;AAAA,MAGV,IAAI,OAAO,aAAa,YAAY,oBAAoB,GAAG;AAAA,MAC3D,OAAO,QAAQ,SAAS;AAAA,IAC1B;AAAA,EACF,CAAC;AACD,SAAO,KAAK,QAAQ,IAAI,OAAO,QAAQ,GAAG,GAAG,EAAE;AAC/C,SAAO;AACT;;;ACzBA,SAAS,WAAW,MAAM,YAAY;AACpC,MAAI,oBAAoB,KAAK,QAAQ,QAAQ,MAAM,KAAK,KAAK;AAC7D,aAAW,QAAQ,YAAY;AAC7B,yBAAqB,MAAM,OAAO,OAAO,WAAW,IAAI,IAAI;AAAA,EAC9D;AACA,SAAO,4CAA4C,oBAAoB,MAAM,OAAO;AACtF;;;ACNA,IAAM,gBAAgB;AAAA,EACpB,aAAa;AAAA,IACX,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AACF;AACA,SAAS,IAAI,SAAS,QAAQ;AAC5B,QAAM,OAAO;AACb,QAAM,IAAI,OAAO,CAAC;AAClB,QAAM,SAAS,OAAO;AACtB,gBAAc,OAAO,IAAI;AAAA,IACvB;AAAA,IACA;AAAA,IACA,GAAG,SAAS,IAAI,OAAO,CAAC,IAAI;AAAA,IAC5B,GAAG,SAAS,IAAI,OAAO,CAAC,IAAI;AAAA,IAC5B,OAAO,SAAS,IAAI,OAAO,CAAC,IAAI;AAAA,EAClC;AACF;AACA,IAAI,UAAU,CAAC,GAAG,CAAC;AACnB,IAAI,QAAQ,CAAC,GAAG,CAAC;AACjB,IAAI,SAAS,CAAC,GAAG,CAAC;AAClB,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;AACzB,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;AACtB,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;AACtB,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;AACvB,IAAI,SAAS,CAAC,GAAG,GAAG,CAAC;AACrB,IAAI,QAAQ,CAAC,GAAG,GAAG,CAAC;AACpB,IAAI,SAAS,CAAC,KAAK,KAAK,CAAC,CAAC;AAC1B,IAAI,UAAU,CAAC,KAAK,KAAK,CAAC,CAAC;AAC3B,IAAI,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC;AACvB,IAAI,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC;AACvB,IAAI,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;AACzB,IAAI,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;AACzB,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,gBAAgB,CAAC,KAAK,KAAK,GAAG,CAAC;AACnC,IAAI,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;AACzB,IAAI,cAAc,CAAC,KAAK,KAAK,GAAG,CAAC;AACjC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC;AAC5B,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC;AAC5B,IAAI,UAAU,CAAC,KAAK,KAAK,GAAG,CAAC;AAC7B,IAAI,SAAS,CAAC,CAAC,CAAC;AAChB,IAAI,kBAAkB,CAAC,KAAK,KAAK,GAAG,CAAC;AACrC,IAAI,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC;AACvB,IAAI,cAAc,CAAC,KAAK,IAAI,GAAG,CAAC;AAChC,IAAI,SAAS,CAAC,KAAK,IAAI,EAAE,CAAC;AAC1B,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,aAAa,CAAC,IAAI,KAAK,GAAG,CAAC;AAC/B,IAAI,cAAc,CAAC,KAAK,KAAK,CAAC,CAAC;AAC/B,IAAI,aAAa,CAAC,KAAK,KAAK,EAAE,CAAC;AAC/B,IAAI,SAAS,CAAC,KAAK,KAAK,EAAE,CAAC;AAC3B,IAAI,kBAAkB,CAAC,KAAK,KAAK,GAAG,CAAC;AACrC,IAAI,YAAY,CAAC,KAAK,KAAK,GAAG,CAAC;AAC/B,IAAI,WAAW,CAAC,KAAK,IAAI,EAAE,CAAC;AAC5B,IAAI,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;AACzB,IAAI,YAAY,CAAC,GAAG,GAAG,GAAG,CAAC;AAC3B,IAAI,YAAY,CAAC,GAAG,KAAK,GAAG,CAAC;AAC7B,IAAI,iBAAiB,CAAC,KAAK,KAAK,EAAE,CAAC;AACnC,IAAI,YAAY,CAAC,GAAG,CAAC;AACrB,IAAI,aAAa,CAAC,GAAG,GAAG,CAAC;AACzB,IAAI,YAAY,CAAC,GAAG,CAAC;AACrB,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;AAC3B,IAAI,kBAAkB,CAAC,IAAI,KAAK,EAAE,CAAC;AACnC,IAAI,cAAc,CAAC,KAAK,KAAK,CAAC,CAAC;AAC/B,IAAI,cAAc,CAAC,KAAK,IAAI,GAAG,CAAC;AAChC,IAAI,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC;AAC1B,IAAI,cAAc,CAAC,KAAK,KAAK,GAAG,CAAC;AACjC,IAAI,gBAAgB,CAAC,KAAK,GAAG,CAAC;AAC9B,IAAI,iBAAiB,CAAC,IAAI,IAAI,GAAG,CAAC;AAClC,IAAI,iBAAiB,CAAC,IAAI,IAAI,EAAE,CAAC;AACjC,IAAI,iBAAiB,CAAC,IAAI,IAAI,EAAE,CAAC;AACjC,IAAI,iBAAiB,CAAC,GAAG,KAAK,GAAG,CAAC;AAClC,IAAI,cAAc,CAAC,KAAK,GAAG,GAAG,CAAC;AAC/B,IAAI,YAAY,CAAC,KAAK,IAAI,GAAG,CAAC;AAC9B,IAAI,eAAe,CAAC,GAAG,KAAK,GAAG,CAAC;AAChC,IAAI,WAAW,CAAC,GAAG,CAAC;AACpB,IAAI,WAAW,CAAC,GAAG,CAAC;AACpB,IAAI,cAAc,CAAC,IAAI,KAAK,GAAG,CAAC;AAChC,IAAI,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC;AAC9B,IAAI,eAAe,CAAC,KAAK,KAAK,GAAG,CAAC;AAClC,IAAI,eAAe,CAAC,IAAI,GAAG,CAAC;AAC5B,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;AACvB,IAAI,aAAa,CAAC,GAAG,CAAC;AACtB,IAAI,cAAc,CAAC,KAAK,KAAK,GAAG,CAAC;AACjC,IAAI,QAAQ,CAAC,KAAK,KAAK,CAAC,CAAC;AACzB,IAAI,aAAa,CAAC,KAAK,KAAK,EAAE,CAAC;AAC/B,IAAI,QAAQ,CAAC,GAAG,CAAC;AACjB,IAAI,SAAS,CAAC,GAAG,GAAG,CAAC;AACrB,IAAI,eAAe,CAAC,KAAK,KAAK,EAAE,CAAC;AACjC,IAAI,QAAQ,CAAC,GAAG,CAAC;AACjB,IAAI,YAAY,CAAC,KAAK,GAAG,CAAC;AAC1B,IAAI,WAAW,CAAC,KAAK,KAAK,GAAG,CAAC;AAC9B,IAAI,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC;AAC9B,IAAI,UAAU,CAAC,IAAI,GAAG,GAAG,CAAC;AAC1B,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC;AAC5B,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC;AAC5B,IAAI,YAAY,CAAC,KAAK,KAAK,GAAG,CAAC;AAC/B,IAAI,iBAAiB,CAAC,KAAK,KAAK,GAAG,CAAC;AACpC,IAAI,aAAa,CAAC,KAAK,KAAK,CAAC,CAAC;AAC9B,IAAI,gBAAgB,CAAC,KAAK,KAAK,GAAG,CAAC;AACnC,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,cAAc,CAAC,KAAK,KAAK,GAAG,CAAC;AACjC,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,wBAAwB,CAAC,KAAK,KAAK,GAAG,CAAC;AAC3C,IAAI,aAAa,CAAC,GAAG,CAAC;AACtB,IAAI,cAAc,CAAC,KAAK,GAAG,CAAC;AAC5B,IAAI,aAAa,CAAC,GAAG,CAAC;AACtB,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,eAAe,CAAC,KAAK,KAAK,GAAG,CAAC;AAClC,IAAI,iBAAiB,CAAC,IAAI,KAAK,GAAG,CAAC;AACnC,IAAI,gBAAgB,CAAC,KAAK,KAAK,GAAG,CAAC;AACnC,IAAI,kBAAkB,CAAC,KAAK,KAAK,GAAG,CAAC;AACrC,IAAI,kBAAkB,CAAC,KAAK,KAAK,GAAG,CAAC;AACrC,IAAI,kBAAkB,CAAC,KAAK,KAAK,GAAG,CAAC;AACrC,IAAI,eAAe,CAAC,KAAK,KAAK,GAAG,CAAC;AAClC,IAAI,QAAQ,CAAC,GAAG,GAAG,CAAC;AACpB,IAAI,aAAa,CAAC,IAAI,GAAG,CAAC;AAC1B,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC;AAC5B,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;AACvB,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;AACzB,IAAI,oBAAoB,CAAC,KAAK,KAAK,GAAG,CAAC;AACvC,IAAI,cAAc,CAAC,GAAG,GAAG,GAAG,CAAC;AAC7B,IAAI,gBAAgB,CAAC,KAAK,IAAI,GAAG,CAAC;AAClC,IAAI,gBAAgB,CAAC,KAAK,KAAK,GAAG,CAAC;AACnC,IAAI,kBAAkB,CAAC,IAAI,KAAK,GAAG,CAAC;AACpC,IAAI,mBAAmB,CAAC,KAAK,KAAK,GAAG,CAAC;AACtC,IAAI,qBAAqB,CAAC,GAAG,KAAK,GAAG,CAAC;AACtC,IAAI,mBAAmB,CAAC,IAAI,KAAK,GAAG,CAAC;AACrC,IAAI,mBAAmB,CAAC,KAAK,IAAI,GAAG,CAAC;AACrC,IAAI,gBAAgB,CAAC,IAAI,IAAI,GAAG,CAAC;AACjC,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,YAAY,CAAC,KAAK,KAAK,GAAG,CAAC;AAC/B,IAAI,eAAe,CAAC,KAAK,KAAK,GAAG,CAAC;AAClC,IAAI,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC;AACvB,IAAI,WAAW,CAAC,KAAK,KAAK,GAAG,CAAC;AAC9B,IAAI,SAAS,CAAC,KAAK,KAAK,CAAC,CAAC;AAC1B,IAAI,aAAa,CAAC,KAAK,KAAK,EAAE,CAAC;AAC/B,IAAI,UAAU,CAAC,KAAK,KAAK,CAAC,CAAC;AAC3B,IAAI,aAAa,CAAC,KAAK,IAAI,CAAC,CAAC;AAC7B,IAAI,UAAU,CAAC,KAAK,KAAK,GAAG,CAAC;AAC7B,IAAI,iBAAiB,CAAC,KAAK,KAAK,GAAG,CAAC;AACpC,IAAI,aAAa,CAAC,KAAK,GAAG,CAAC;AAC3B,IAAI,iBAAiB,CAAC,KAAK,KAAK,GAAG,CAAC;AACpC,IAAI,iBAAiB,CAAC,KAAK,KAAK,GAAG,CAAC;AACpC,IAAI,cAAc,CAAC,KAAK,KAAK,GAAG,CAAC;AACjC,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,QAAQ,CAAC,KAAK,KAAK,EAAE,CAAC;AAC1B,IAAI,QAAQ,CAAC,KAAK,KAAK,GAAG,CAAC;AAC3B,IAAI,QAAQ,CAAC,KAAK,GAAG,CAAC;AACtB,IAAI,cAAc,CAAC,KAAK,KAAK,GAAG,CAAC;AACjC,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;AACtB,IAAI,iBAAiB,CAAC,KAAK,IAAI,GAAG,CAAC;AACnC,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;AACtB,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,aAAa,CAAC,IAAI,KAAK,GAAG,CAAC;AAC/B,IAAI,eAAe,CAAC,KAAK,IAAI,EAAE,CAAC;AAChC,IAAI,UAAU,CAAC,KAAK,KAAK,GAAG,CAAC;AAC7B,IAAI,cAAc,CAAC,KAAK,KAAK,EAAE,CAAC;AAChC,IAAI,YAAY,CAAC,IAAI,KAAK,EAAE,CAAC;AAC7B,IAAI,YAAY,CAAC,KAAK,KAAK,GAAG,CAAC;AAC/B,IAAI,UAAU,CAAC,KAAK,IAAI,EAAE,CAAC;AAC3B,IAAI,UAAU,CAAC,GAAG,CAAC;AACnB,IAAI,WAAW,CAAC,KAAK,KAAK,GAAG,CAAC;AAC9B,IAAI,aAAa,CAAC,KAAK,IAAI,GAAG,CAAC;AAC/B,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,QAAQ,CAAC,KAAK,KAAK,GAAG,CAAC;AAC3B,IAAI,eAAe,CAAC,GAAG,KAAK,GAAG,CAAC;AAChC,IAAI,aAAa,CAAC,IAAI,KAAK,GAAG,CAAC;AAC/B,IAAI,OAAO,CAAC,KAAK,KAAK,GAAG,CAAC;AAC1B,IAAI,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;AACzB,IAAI,WAAW,CAAC,KAAK,GAAG,CAAC;AACzB,IAAI,UAAU,CAAC,KAAK,IAAI,EAAE,CAAC;AAC3B,IAAI,aAAa,CAAC,IAAI,KAAK,GAAG,CAAC;AAC/B,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC;AACxB,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC;AAC5B,IAAI,SAAS,CAAC,GAAG,CAAC;AAClB,IAAI,cAAc,CAAC,GAAG,CAAC;AACvB,IAAI,UAAU,CAAC,KAAK,KAAK,CAAC,CAAC;AAC3B,IAAI,eAAe,CAAC,KAAK,KAAK,EAAE,CAAC;;;AC1KjC,IAAM,iBAAiB;AACvB,IAAM,eAAe;AAErB,IAAM,mBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA,kBAAkB,iBAAiB;AACrC;;;ACtBA,mBAA2B;AAS3B,IAAM,YAAQ,aAAAA,SAAe,wBAAwB;;;ACLrD,IAAAC,gBAA2B;AAQ3B,IAAMC,aAAQ,cAAAC,SAAe,sBAAsB;;;ACVnD,IAAAC,gBAAO;;;ACCP,IAAM,wBAAwB;AAAA,EAC5B,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,cAAc;AAChB;AAmBA,IAAM,yBAAyB;AAAA,EAC7B,GAAG;AAAA,EACH,WAAW;AACb;;;ACqBA,IAAAC,gBAAO;;;AC9CP,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,aAA6B,oBAAI,IAAI;AACzC,IAAI,cAA8B,oBAAI,IAAI;AAC1C,IAAI,oBAAoC,OAAO,CAAC,gBAAgB;AAC9D,aAAW,cAAc,aAAa;AACpC,QAAI,CAAC,WAAW,MAAM;AACpB,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM,0BAA0B,WAAW,IAAI;AACnD,QAAI,YAAY,YAAY;AAC1B,kBAAY,IAAI,WAAW,MAAM,WAAW,MAAM;AAAA,IACpD,WAAW,WAAW,YAAY;AAChC,iBAAW,IAAI,WAAW,MAAM,WAAW,KAAK;AAAA,IAClD,OAAO;AACL,UAAI,MAAM,wBAAwB,UAAU;AAC5C,YAAM,IAAI,MAAM,qEAAqE;AAAA,IACvF;AAAA,EACF;AACF,GAAG,mBAAmB;AACtB,IAAI,wBAAwC,OAAO,OAAO,UAAU,mBAAmB;AACrF,QAAM,OAAO,aAAa,UAAU,MAAM,mBAAmB,MAAM;AACnE,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,sBAAsB,QAAQ,EAAE;AAAA,EAClD;AACA,QAAM,SAAS,KAAK,UAAU;AAC9B,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,MAAM,oCAAoC,QAAQ,EAAE;AAAA,EAChE;AACA,MAAI,QAAQ,WAAW,IAAI,MAAM;AACjC,MAAI,CAAC,OAAO;AACV,UAAM,SAAS,YAAY,IAAI,MAAM;AACrC,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,MAAM,uBAAuB,KAAK,MAAM,EAAE;AAAA,IACtD;AACA,QAAI;AACF,YAAM,SAAS,MAAM,OAAO;AAC5B,cAAQ,EAAE,GAAG,QAAQ,OAAO;AAC5B,iBAAW,IAAI,QAAQ,KAAK;AAAA,IAC9B,SAAS,GAAG;AACV,UAAI,MAAM,CAAC;AACX,YAAM,IAAI,MAAM,4BAA4B,KAAK,MAAM,EAAE;AAAA,IAC3D;AAAA,EACF;AACA,QAAM,WAAW,YAAY,OAAO,KAAK,IAAI;AAC7C,MAAI,CAAC,UAAU;AACb,UAAM,IAAI,MAAM,mBAAmB,QAAQ,EAAE;AAAA,EAC/C;AACA,SAAO;AACT,GAAG,uBAAuB;AAC1B,IAAI,aAA6B,OAAO,OAAO,UAAU,mBAAmB;AAC1E,MAAI;AACJ,MAAI;AACF,eAAW,MAAM,sBAAsB,UAAU,iDAAgB,cAAc;AAAA,EACjF,SAAS,GAAG;AACV,QAAI,MAAM,CAAC;AACX,eAAW;AAAA,EACb;AACA,QAAM,aAAa,UAAU,UAAU,cAAc;AACrD,QAAM,MAAM,WAAW,WAAW,WAAW,IAAI,GAAG,WAAW,UAAU;AACzE,SAAO;AACT,GAAG,YAAY;", "names": ["createDebugger", "import_debug", "debug", "createDebugger", "import_debug", "import_debug"]}