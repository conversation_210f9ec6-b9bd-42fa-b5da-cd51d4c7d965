<?if $(sys.B<PERSON><PERSON>AR<PERSON>)="x86"?>
    <?define Win64 = "no" ?>
    <?define PlatformProgramFilesFolder = "ProgramFilesFolder" ?>
<?elseif $(sys.BUILDARCH)="x64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?elseif $(sys.BUILDARCH)="arm64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?else?>
    <?error Unsupported value of sys.BUILDARCH=$(sys.BUILDARCH)?>
<?endif?>

<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Product
            Id="*"
            Name="WeMa IA"
            UpgradeCode="7adcaf1a-334a-54b2-baee-a32f3344ceea"
            Language="!(loc.TauriLanguage)"
            Manufacturer="wema-ia"
            Version="0.1.2">

        <Package Id="*"
                 Keywords="Installer"
                 InstallerVersion="450"
                 Languages="0"
                 Compressed="yes"
                 InstallScope="perMachine"
                 SummaryCodepage="!(loc.TauriCodepage)"/>

        <!-- https://docs.microsoft.com/en-us/windows/win32/msi/reinstallmode -->
        <!-- reinstall all files; rewrite all registry entries; reinstall all shortcuts -->
        <Property Id="REINSTALLMODE" Value="amus" />

        <!-- Auto launch app after installation, useful for passive mode which usually used in updates -->
        <Property Id="AUTOLAUNCHAPP" Secure="yes" />
        <!-- Property to forward cli args to the launched app to not lose those of the pre-update instance -->
        <Property Id="LAUNCHAPPARGS" Secure="yes" />

            <MajorUpgrade Schedule="afterInstallInitialize" AllowDowngrades="yes" />

        <InstallExecuteSequence>
            <RemoveShortcuts>Installed AND NOT UPGRADINGPRODUCTCODE</RemoveShortcuts>
        </InstallExecuteSequence>

        <Media Id="1" Cabinet="app.cab" EmbedCab="yes" />


        <Icon Id="ProductIcon" SourceFile="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\resources\icon.ico"/>
        <Property Id="ARPPRODUCTICON" Value="ProductIcon" />
        <Property Id="ARPNOREPAIR" Value="yes" Secure="yes" />      <!-- Remove repair -->
        <SetProperty Id="ARPNOMODIFY" Value="1" After="InstallValidate" Sequence="execute"/>


        <Property Id="INSTALLDIR">
          <!-- First attempt: Search for "InstallDir" -->
          <RegistrySearch Id="PrevInstallDirWithName" Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="InstallDir" Type="raw" />

          <!-- Second attempt: If the first fails, search for the default key value (this is how the nsis installer currently stores the path) -->
          <RegistrySearch Id="PrevInstallDirNoName" Root="HKCU" Key="Software\wema-ia\WeMa IA" Type="raw" />
        </Property>

        <!-- launch app checkbox -->
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOXTEXT" Value="!(loc.LaunchApp)" />
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOX" Value="1"/>
        <CustomAction Id="LaunchApplication" Impersonate="yes" FileKey="Path" ExeCommand="[LAUNCHAPPARGS]" Return="asyncNoWait" />

        <UI>
            <!-- launch app checkbox -->
            <Publish Dialog="ExitDialog" Control="Finish" Event="DoAction" Value="LaunchApplication">WIXUI_EXITDIALOGOPTIONALCHECKBOX = 1 and NOT Installed</Publish>

            <Property Id="WIXUI_INSTALLDIR" Value="INSTALLDIR" />

            <!-- Skip license dialog -->
            <Publish Dialog="WelcomeDlg"
                     Control="Next"
                     Event="NewDialog"
                     Value="InstallDirDlg"
                     Order="2">1</Publish>
            <Publish Dialog="InstallDirDlg"
                     Control="Back"
                     Event="NewDialog"
                     Value="WelcomeDlg"
                     Order="2">1</Publish>
        </UI>

        <UIRef Id="WixUI_InstallDir" />

        <Directory Id="TARGETDIR" Name="SourceDir">
            <Directory Id="DesktopFolder" Name="Desktop">
                <Component Id="ApplicationShortcutDesktop" Guid="*">
                    <Shortcut Id="ApplicationDesktopShortcut" Name="WeMa IA" Description="Runs WeMa IA" Target="[!Path]" WorkingDirectory="INSTALLDIR" />
                    <RemoveFolder Id="DesktopFolder" On="uninstall" />
                    <RegistryValue Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="Desktop Shortcut" Type="integer" Value="1" KeyPath="yes" />
                </Component>
            </Directory>
            <Directory Id="$(var.PlatformProgramFilesFolder)" Name="PFiles">
                <Directory Id="INSTALLDIR" Name="WeMa IA"/>
            </Directory>
            <Directory Id="ProgramMenuFolder">
                <Directory Id="ApplicationProgramsFolder" Name="WeMa IA"/>
            </Directory>
        </Directory>

        <DirectoryRef Id="INSTALLDIR">
            <Component Id="RegistryEntries" Guid="*">
                <RegistryKey Root="HKCU" Key="Software\wema-ia\WeMa IA">
                    <RegistryValue Name="InstallDir" Type="string" Value="[INSTALLDIR]" KeyPath="yes" />
                </RegistryKey>
                <!-- Change the Root to HKCU for perUser installations -->
</Component>
            <Component Id="Path" Guid="3b783432-6e5b-5f62-a8d8-17ab766c4f33" Win64="$(var.Win64)">
                <File Id="Path" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\app.exe" KeyPath="yes" Checksum="yes"/>
</Component>
            <Directory Id="I70ad28e78d4c4edab1487015f0d1f42c" Name="_up_"><Directory Id="Ifd0fa1445985490d98bf4c5406fcbb58" Name="py_backend"><Component Id="Ib847beaf886446e9bd09be8393add6ec" Guid="9cf3d394-d3b5-414a-91e1-0d4d6f0ecf0b" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ib847beaf886446e9bd09be8393add6ec" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\.env" /></Component><Component Id="Idecdc9437ee0459999fcc224b27c2b6c" Guid="196a4ae6-0aeb-4373-9e95-2bfef20aa511" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Idecdc9437ee0459999fcc224b27c2b6c" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\.env.template" /></Component><Component Id="I19b645772a1146f9a9ef31ca79c8c471" Guid="43d1848c-fffe-40af-8ded-cf9681d9eadc" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I19b645772a1146f9a9ef31ca79c8c471" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Dockerfile" /></Component><Component Id="Ia035c46e85e541ca93d8bec11f14cbb9" Guid="4b8be539-7216-487e-9c2b-ea0de101b50e" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ia035c46e85e541ca93d8bec11f14cbb9" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Speech2Text.py" /></Component><Component Id="I36b3cab15a244152b7b0ac63f3675132" Guid="964cb203-8068-418c-bcc8-9ca9b3613b97" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I36b3cab15a244152b7b0ac63f3675132" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\TTS_README.md" /></Component><Component Id="I26fb23efc83049269a97b628c4163bb3" Guid="71e6dd55-3b37-468c-aea1-63badea1ba8c" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I26fb23efc83049269a97b628c4163bb3" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Text2Speech.py" /></Component><Component Id="I0270928c5afc49498513c7f092622bd7" Guid="90017c8e-f9ac-412d-9e89-545bbb36f4db" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I0270928c5afc49498513c7f092622bd7" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\auth_service.py" /></Component><Component Id="I929bfe379a59495fae814edec89389b8" Guid="0603b208-030b-4285-9bea-934d580a3eba" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I929bfe379a59495fae814edec89389b8" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\clara_backend.db" /></Component><Component Id="I04fdd46bbc1e4dba89c5fe83467bee46" Guid="8d15a18a-7aa9-4e25-bf14-f9b8fc335a80" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I04fdd46bbc1e4dba89c5fe83467bee46" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\compression_routes.py" /></Component><Component Id="I148287bb45e347c7bdfe85173c322046" Guid="e1dd51c3-0395-4c86-8333-bb34541bab8a" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I148287bb45e347c7bdfe85173c322046" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\diffusers_api.py" /></Component><Component Id="I979ea4a978fa45cca5926a36ee75882c" Guid="b3f27777-db99-4545-9c02-1b1876f14ca8" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I979ea4a978fa45cca5926a36ee75882c" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\documents.db" /></Component><Component Id="I10583b72639e49c7b2fce190e597554e" Guid="e1c16edc-9584-4f81-9298-017ebe2f15ca" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I10583b72639e49c7b2fce190e597554e" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\install_kokoro.py" /></Component><Component Id="I60e795864b4c44b88d35c6e3c87113ec" Guid="e843b399-b490-4176-b18a-be2c6baea2fb" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I60e795864b4c44b88d35c6e3c87113ec" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\langfuse_service.py" /></Component><Component Id="I5407b34473e74a6d926a3ccf663e5a5a" Guid="d5686653-9414-4024-ae8f-696fba1ba09b" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I5407b34473e74a6d926a3ccf663e5a5a" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\main.py" /></Component><Component Id="Ia372574a03ae4ca5b82ef3b754637c81" Guid="e8b2db55-b108-4512-b5ef-8a264e3d5f30" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ia372574a03ae4ca5b82ef3b754637c81" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\network_config.py" /></Component><Component Id="I8d6623f562834dc9982729edf1b96585" Guid="ba473e31-7879-4bbe-bd2a-6894a4394e90" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I8d6623f562834dc9982729edf1b96585" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\ocr_processor_premium.py" /></Component><Component Id="I52caa82848ce41d2a7ccf9d56ebd0916" Guid="fb9b38cf-531f-479f-880b-6ec2392ef28f" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I52caa82848ce41d2a7ccf9d56ebd0916" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\ocr_service.py" /></Component><Component Id="I67d4ae89ae1a423caae3e2110bef071e" Guid="91fdab5e-73f4-487b-9d87-0f9b989b2446" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I67d4ae89ae1a423caae3e2110bef071e" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\perfect_compressor.py" /></Component><Component Id="I80e16660f01b4321b306f76493803768" Guid="93cb93be-0274-4891-86ac-6d5fc3fbc473" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I80e16660f01b4321b306f76493803768" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\requirements.txt" /></Component><Component Id="I6af9b79cdbac414984fcd47aeffd2789" Guid="bffac27e-ba6b-4f88-9d05-2b22d3f7781a" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I6af9b79cdbac414984fcd47aeffd2789" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\start_server.py" /></Component><Component Id="Ice6eaa42a0e1460ca18dea1c90cbd68a" Guid="ac1a89b6-9c74-4681-abc7-d56e93345459" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ice6eaa42a0e1460ca18dea1c90cbd68a" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_compression_tool.py" /></Component><Component Id="I928f2ad6d76f44978d0783968039362a" Guid="2090f0bc-7bf9-461c-9998-2a7805c082ab" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I928f2ad6d76f44978d0783968039362a" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_non_streaming.py" /></Component><Component Id="I34bd41af818b46ae8a948b886e32c777" Guid="34c0f1e8-1cba-462b-90cb-dc49692b4a74" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I34bd41af818b46ae8a948b886e32c777" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_optimizations.py" /></Component><Component Id="I520f9a57ec464b279f83649edcf5225c" Guid="99962852-7cd5-43b6-a971-3594c6701e09" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I520f9a57ec464b279f83649edcf5225c" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_streaming_optimized.py" /></Component><Directory Id="Ia0eae3615fa4456c9aa0db1b7711ba65" Name="__pycache__"><Component Id="I223471d2c4f24c0aaf14ae1631a44d23" Guid="6d1ee34f-1c03-471a-b908-8ec29cdf87c8" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I223471d2c4f24c0aaf14ae1631a44d23" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\langfuse_service.cpython-312.pyc" /></Component><Component Id="I69e5d976094e4b5eac765b04fa5cde94" Guid="fe4e3c25-fbf1-44ad-b1bb-ceb58eb29a54" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I69e5d976094e4b5eac765b04fa5cde94" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\main.cpython-312.pyc" /></Component><Component Id="I2b2ff9592e66463db2c31e51c7fb2d44" Guid="99f37372-71e5-446c-ab94-c7412bb786e5" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I2b2ff9592e66463db2c31e51c7fb2d44" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\network_config.cpython-312.pyc" /></Component><Component Id="I991e2ebf24e445e29bb8612235e6cdf1" Guid="856b6585-38fc-4715-b2a8-1ee2b82ce352" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I991e2ebf24e445e29bb8612235e6cdf1" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\ocr_processor_premium.cpython-312.pyc" /></Component><Component Id="I0d63393a2ff94ce7b924282eddf379c4" Guid="cb4b026a-2c7b-470d-8329-7a001bbcb027" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I0d63393a2ff94ce7b924282eddf379c4" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\ocr_service.cpython-312.pyc" /></Component><Component Id="Ifc2921c94e254a34844af3f59f37bfd0" Guid="5e003c78-06c7-42cd-bbeb-a5a3e41ebb86" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ifc2921c94e254a34844af3f59f37bfd0" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\perfect_compressor.cpython-312.pyc" /></Component><Component Id="I05f26abaf33a41878b24ba7e161fbe58" Guid="7d530eeb-6d29-4f4e-9ed3-fc6ca88cd0a7" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I05f26abaf33a41878b24ba7e161fbe58" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\rag_premium_service.cpython-312.pyc" /></Component></Directory><Directory Id="I41e9695770654e1cac6130203b3342c7" Name="qdrant_storage"><Component Id="I30bcb79325f040158632dfa202f920be" Guid="768a559f-ca94-4728-afd3-61fc94c18c22" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I30bcb79325f040158632dfa202f920be" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\qdrant_storage\.lock" /></Component><Component Id="I2d820082c5a74517aeb36eb8b35d6897" Guid="ba7b493d-8a68-4b59-a862-2ae3139bcb6e" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I2d820082c5a74517aeb36eb8b35d6897" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\qdrant_storage\meta.json" /></Component></Directory><Directory Id="I22b176a42e944be09f7a09d0fc97d526" Name="services"><Component Id="I75c6fe4d0d4b45fd9cd63e734d31d51f" Guid="da9c02b7-fce0-4573-b215-99bc05789494" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I75c6fe4d0d4b45fd9cd63e734d31d51f" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\internet_search_service.py" /></Component><Component Id="Iff6a57b151f34eaf8ed2f29b0e5fa037" Guid="710c655b-9a0e-4ef1-9bfd-810192aef277" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iff6a57b151f34eaf8ed2f29b0e5fa037" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\startup_service.py" /></Component><Component Id="I4d1065822fc74b64b97adb3abb589041" Guid="62254ecb-55fe-4a2f-a863-baecaaf1b132" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I4d1065822fc74b64b97adb3abb589041" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\web_scraper_service.py" /></Component><Directory Id="I0024e00fc9f749f0985c089a84c3f77a" Name="__pycache__"><Component Id="I6b3a9d38742b4a5d8cf8514631686bed" Guid="18e4f8e4-fafc-4d37-b2b9-327463ef6415" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I6b3a9d38742b4a5d8cf8514631686bed" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\internet_search_service.cpython-312.pyc" /></Component><Component Id="Ie12c214546d74ddab5e98eae8f2e755b" Guid="7adf9ac2-9251-490e-b462-e1a7d9385941" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ie12c214546d74ddab5e98eae8f2e755b" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\startup_service.cpython-312.pyc" /></Component><Component Id="Ic651976d10eb4b358b2b08fc376bccb0" Guid="0b8ee9fe-da29-4f42-836e-e4cd421b724a" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ic651976d10eb4b358b2b08fc376bccb0" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\web_scraper_service.cpython-312.pyc" /></Component></Directory></Directory><Directory Id="I247a5919dccc416086802eec09085f84" Name="test_qdrant_temp"><Component Id="I27e8b3d9b1814c4f9bdd19d5eda0ee8c" Guid="363c3261-5218-452f-b306-88676bdf1c2d" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I27e8b3d9b1814c4f9bdd19d5eda0ee8c" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_qdrant_temp\.lock" /></Component><Directory Id="Ibe5407d99ad3482eb0855a49c691fb5f" Name="collection"><Directory Id="I70bf787c65784342bad0561d76618a52" Name="test_collection"><Component Id="I8207dbfa4b154721b5d3e1fcccc336ba" Guid="8db17a88-248c-4164-b241-f028c0eae704" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I8207dbfa4b154721b5d3e1fcccc336ba" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_qdrant_temp\collection\test_collection\storage.sqlite" /></Component></Directory></Directory></Directory><Directory Id="I37a2c9f88a8942fc999d404a730acb61" Name="tools"><Component Id="I219f4b886cb54d20b6fd44260ed267ce" Guid="5cfc44d6-119a-4f79-9974-478c5c6e5d16" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I219f4b886cb54d20b6fd44260ed267ce" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\tools\internet_search_tools.py" /></Component><Directory Id="I66102e9d0a5b4b339519b9c85fd02c49" Name="__pycache__"><Component Id="Idf1acd60de4d4dd88f66f12640634bfe" Guid="4f93682f-f8e8-4908-9513-ad235aa12e8a" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Idf1acd60de4d4dd88f66f12640634bfe" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\tools\__pycache__\internet_search_tools.cpython-312.pyc" /></Component></Directory></Directory></Directory></Directory><Directory Id="If0f166cb1c6e43c1abc26c2de3bcf93e" Name="python-embed"><Component Id="I2663c02227d444fa9da0851730dd0144" Guid="57192b79-4e5f-41fa-823b-996f9fe95d22" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I2663c02227d444fa9da0851730dd0144" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\python-embed\python.zip" /></Component></Directory>
            <Component Id="CMP_UninstallShortcut" Guid="*">

                <Shortcut Id="UninstallShortcut"
						  Name="Uninstall WeMa IA"
						  Description="Uninstalls WeMa IA"
						  Target="[System64Folder]msiexec.exe"
						  Arguments="/x [ProductCode]" />

				<RemoveFolder Id="INSTALLDIR"
							  On="uninstall" />

				<RegistryValue Root="HKCU"
							   Key="Software\wema-ia\WeMa IA"
							   Name="Uninstaller Shortcut"
							   Type="integer"
							   Value="1"
							   KeyPath="yes" />
            </Component>
        </DirectoryRef>

        <DirectoryRef Id="ApplicationProgramsFolder">
            <Component Id="ApplicationShortcut" Guid="*">
                <Shortcut Id="ApplicationStartMenuShortcut"
                    Name="WeMa IA"
                    Description="Runs WeMa IA"
                    Target="[!Path]"
                    Icon="ProductIcon"
                    WorkingDirectory="INSTALLDIR">
                    <ShortcutProperty Key="System.AppUserModel.ID" Value="com.wema-ia.app"/>
                </Shortcut>
                <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall"/>
                <RegistryValue Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="Start Menu Shortcut" Type="integer" Value="1" KeyPath="yes"/>
           </Component>
        </DirectoryRef>

<Feature
                Id="MainProgram"
                Title="Application"
                Description="!(loc.InstallAppFeature)"
                Level="1"
                ConfigurableDirectory="INSTALLDIR"
                AllowAdvertise="no"
                Display="expand"
                Absent="disallow">

            <ComponentRef Id="RegistryEntries"/>

<ComponentRef Id="Ib847beaf886446e9bd09be8393add6ec"/>
<ComponentRef Id="Idecdc9437ee0459999fcc224b27c2b6c"/>
<ComponentRef Id="I19b645772a1146f9a9ef31ca79c8c471"/>
<ComponentRef Id="Ia035c46e85e541ca93d8bec11f14cbb9"/>
<ComponentRef Id="I36b3cab15a244152b7b0ac63f3675132"/>
<ComponentRef Id="I26fb23efc83049269a97b628c4163bb3"/>
<ComponentRef Id="I0270928c5afc49498513c7f092622bd7"/>
<ComponentRef Id="I929bfe379a59495fae814edec89389b8"/>
<ComponentRef Id="I04fdd46bbc1e4dba89c5fe83467bee46"/>
<ComponentRef Id="I148287bb45e347c7bdfe85173c322046"/>
<ComponentRef Id="I979ea4a978fa45cca5926a36ee75882c"/>
<ComponentRef Id="I10583b72639e49c7b2fce190e597554e"/>
<ComponentRef Id="I60e795864b4c44b88d35c6e3c87113ec"/>
<ComponentRef Id="I5407b34473e74a6d926a3ccf663e5a5a"/>
<ComponentRef Id="Ia372574a03ae4ca5b82ef3b754637c81"/>
<ComponentRef Id="I8d6623f562834dc9982729edf1b96585"/>
<ComponentRef Id="I52caa82848ce41d2a7ccf9d56ebd0916"/>
<ComponentRef Id="I67d4ae89ae1a423caae3e2110bef071e"/>
<ComponentRef Id="I80e16660f01b4321b306f76493803768"/>
<ComponentRef Id="I6af9b79cdbac414984fcd47aeffd2789"/>
<ComponentRef Id="Ice6eaa42a0e1460ca18dea1c90cbd68a"/>
<ComponentRef Id="I928f2ad6d76f44978d0783968039362a"/>
<ComponentRef Id="I34bd41af818b46ae8a948b886e32c777"/>
<ComponentRef Id="I520f9a57ec464b279f83649edcf5225c"/>
<ComponentRef Id="I223471d2c4f24c0aaf14ae1631a44d23"/>
<ComponentRef Id="I69e5d976094e4b5eac765b04fa5cde94"/>
<ComponentRef Id="I2b2ff9592e66463db2c31e51c7fb2d44"/>
<ComponentRef Id="I991e2ebf24e445e29bb8612235e6cdf1"/>
<ComponentRef Id="I0d63393a2ff94ce7b924282eddf379c4"/>
<ComponentRef Id="Ifc2921c94e254a34844af3f59f37bfd0"/>
<ComponentRef Id="I05f26abaf33a41878b24ba7e161fbe58"/>
<ComponentRef Id="I30bcb79325f040158632dfa202f920be"/>
<ComponentRef Id="I2d820082c5a74517aeb36eb8b35d6897"/>
<ComponentRef Id="I75c6fe4d0d4b45fd9cd63e734d31d51f"/>
<ComponentRef Id="Iff6a57b151f34eaf8ed2f29b0e5fa037"/>
<ComponentRef Id="I4d1065822fc74b64b97adb3abb589041"/>
<ComponentRef Id="I6b3a9d38742b4a5d8cf8514631686bed"/>
<ComponentRef Id="Ie12c214546d74ddab5e98eae8f2e755b"/>
<ComponentRef Id="Ic651976d10eb4b358b2b08fc376bccb0"/>
<ComponentRef Id="I27e8b3d9b1814c4f9bdd19d5eda0ee8c"/>
<ComponentRef Id="I8207dbfa4b154721b5d3e1fcccc336ba"/>
<ComponentRef Id="I219f4b886cb54d20b6fd44260ed267ce"/>
<ComponentRef Id="Idf1acd60de4d4dd88f66f12640634bfe"/>
<ComponentRef Id="I2663c02227d444fa9da0851730dd0144"/>

            <Feature Id="ShortcutsFeature"
                Title="Shortcuts"
                Level="1">
                <ComponentRef Id="Path"/>
                <ComponentRef Id="CMP_UninstallShortcut" />
                <ComponentRef Id="ApplicationShortcut" />
                <ComponentRef Id="ApplicationShortcutDesktop" />
            </Feature>

            <Feature
                Id="Environment"
                Title="PATH Environment Variable"
                Description="!(loc.PathEnvVarFeature)"
                Level="1"
                Absent="allow">
            <ComponentRef Id="Path"/>
</Feature>
        </Feature>

        <Feature Id="External" AllowAdvertise="no" Absent="disallow">
</Feature>

        <!-- WebView2 -->
        <Property Id="WVRTINSTALLED">
            <RegistrySearch Id="WVRTInstalledSystem" Root="HKLM" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw" Win64="no" />
            <RegistrySearch Id="WVRTInstalledUser" Root="HKCU" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw"/>
        </Property>

        <CustomAction Id='DownloadAndInvokeBootstrapper' Directory="INSTALLDIR" Execute="deferred" ExeCommand='powershell.exe -NoProfile -windowstyle hidden try [\{] [\[]Net.ServicePointManager[\]]::SecurityProtocol = [\[]Net.SecurityProtocolType[\]]::Tls12 [\}] catch [\{][\}]; Invoke-WebRequest -Uri "https://go.microsoft.com/fwlink/p/?LinkId=2124703" -OutFile "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" ; Start-Process -FilePath "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" -ArgumentList (&apos;/silent&apos;, &apos;/install&apos;) -Wait' Return='check'/>
        <InstallExecuteSequence>
            <Custom Action='DownloadAndInvokeBootstrapper' Before='InstallFinalize'>
                <![CDATA[NOT(REMOVE OR WVRTINSTALLED)]]>
            </Custom>
        </InstallExecuteSequence>

        <!-- Embedded webview bootstrapper mode -->

        <!-- Embedded offline installer -->



        <InstallExecuteSequence>
          <Custom Action="LaunchApplication" After="InstallFinalize">AUTOLAUNCHAPP AND NOT Installed</Custom>
        </InstallExecuteSequence>

        <SetProperty Id="ARPINSTALLLOCATION" Value="[INSTALLDIR]" After="CostFinalize"/>
    </Product>
</Wix>
