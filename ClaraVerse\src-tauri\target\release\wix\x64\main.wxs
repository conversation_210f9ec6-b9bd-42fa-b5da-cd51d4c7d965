<?if $(sys.B<PERSON><PERSON>AR<PERSON>)="x86"?>
    <?define Win64 = "no" ?>
    <?define PlatformProgramFilesFolder = "ProgramFilesFolder" ?>
<?elseif $(sys.BUILDARCH)="x64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?elseif $(sys.BUILDARCH)="arm64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?else?>
    <?error Unsupported value of sys.BUILDARCH=$(sys.BUILDARCH)?>
<?endif?>

<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Product
            Id="*"
            Name="WeMa IA"
            UpgradeCode="7adcaf1a-334a-54b2-baee-a32f3344ceea"
            Language="!(loc.TauriLanguage)"
            Manufacturer="wema-ia"
            Version="0.1.2">

        <Package Id="*"
                 Keywords="Installer"
                 InstallerVersion="450"
                 Languages="0"
                 Compressed="yes"
                 InstallScope="perMachine"
                 SummaryCodepage="!(loc.TauriCodepage)"/>

        <!-- https://docs.microsoft.com/en-us/windows/win32/msi/reinstallmode -->
        <!-- reinstall all files; rewrite all registry entries; reinstall all shortcuts -->
        <Property Id="REINSTALLMODE" Value="amus" />

        <!-- Auto launch app after installation, useful for passive mode which usually used in updates -->
        <Property Id="AUTOLAUNCHAPP" Secure="yes" />
        <!-- Property to forward cli args to the launched app to not lose those of the pre-update instance -->
        <Property Id="LAUNCHAPPARGS" Secure="yes" />

            <MajorUpgrade Schedule="afterInstallInitialize" AllowDowngrades="yes" />

        <InstallExecuteSequence>
            <RemoveShortcuts>Installed AND NOT UPGRADINGPRODUCTCODE</RemoveShortcuts>
        </InstallExecuteSequence>

        <Media Id="1" Cabinet="app.cab" EmbedCab="yes" />


        <Icon Id="ProductIcon" SourceFile="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\resources\icon.ico"/>
        <Property Id="ARPPRODUCTICON" Value="ProductIcon" />
        <Property Id="ARPNOREPAIR" Value="yes" Secure="yes" />      <!-- Remove repair -->
        <SetProperty Id="ARPNOMODIFY" Value="1" After="InstallValidate" Sequence="execute"/>


        <Property Id="INSTALLDIR">
          <!-- First attempt: Search for "InstallDir" -->
          <RegistrySearch Id="PrevInstallDirWithName" Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="InstallDir" Type="raw" />

          <!-- Second attempt: If the first fails, search for the default key value (this is how the nsis installer currently stores the path) -->
          <RegistrySearch Id="PrevInstallDirNoName" Root="HKCU" Key="Software\wema-ia\WeMa IA" Type="raw" />
        </Property>

        <!-- launch app checkbox -->
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOXTEXT" Value="!(loc.LaunchApp)" />
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOX" Value="1"/>
        <CustomAction Id="LaunchApplication" Impersonate="yes" FileKey="Path" ExeCommand="[LAUNCHAPPARGS]" Return="asyncNoWait" />

        <UI>
            <!-- launch app checkbox -->
            <Publish Dialog="ExitDialog" Control="Finish" Event="DoAction" Value="LaunchApplication">WIXUI_EXITDIALOGOPTIONALCHECKBOX = 1 and NOT Installed</Publish>

            <Property Id="WIXUI_INSTALLDIR" Value="INSTALLDIR" />

            <!-- Skip license dialog -->
            <Publish Dialog="WelcomeDlg"
                     Control="Next"
                     Event="NewDialog"
                     Value="InstallDirDlg"
                     Order="2">1</Publish>
            <Publish Dialog="InstallDirDlg"
                     Control="Back"
                     Event="NewDialog"
                     Value="WelcomeDlg"
                     Order="2">1</Publish>
        </UI>

        <UIRef Id="WixUI_InstallDir" />

        <Directory Id="TARGETDIR" Name="SourceDir">
            <Directory Id="DesktopFolder" Name="Desktop">
                <Component Id="ApplicationShortcutDesktop" Guid="*">
                    <Shortcut Id="ApplicationDesktopShortcut" Name="WeMa IA" Description="Runs WeMa IA" Target="[!Path]" WorkingDirectory="INSTALLDIR" />
                    <RemoveFolder Id="DesktopFolder" On="uninstall" />
                    <RegistryValue Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="Desktop Shortcut" Type="integer" Value="1" KeyPath="yes" />
                </Component>
            </Directory>
            <Directory Id="$(var.PlatformProgramFilesFolder)" Name="PFiles">
                <Directory Id="INSTALLDIR" Name="WeMa IA"/>
            </Directory>
            <Directory Id="ProgramMenuFolder">
                <Directory Id="ApplicationProgramsFolder" Name="WeMa IA"/>
            </Directory>
        </Directory>

        <DirectoryRef Id="INSTALLDIR">
            <Component Id="RegistryEntries" Guid="*">
                <RegistryKey Root="HKCU" Key="Software\wema-ia\WeMa IA">
                    <RegistryValue Name="InstallDir" Type="string" Value="[INSTALLDIR]" KeyPath="yes" />
                </RegistryKey>
                <!-- Change the Root to HKCU for perUser installations -->
</Component>
            <Component Id="Path" Guid="3b783432-6e5b-5f62-a8d8-17ab766c4f33" Win64="$(var.Win64)">
                <File Id="Path" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\app.exe" KeyPath="yes" Checksum="yes"/>
</Component>
            <Directory Id="Ic31cb2854df44298b946d2fa88e5403d" Name="_up_"><Directory Id="I13cebd2638034019844f8d862e5d9874" Name="py_backend"><Component Id="Iff8717b16beb4d9da1827a4e14b17b33" Guid="69dd67ae-2c40-4386-9f1c-1d91e349009e" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iff8717b16beb4d9da1827a4e14b17b33" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\.env" /></Component><Component Id="I592615c529e54bc1bce2067afd4e44ab" Guid="2d42756b-5c19-4a48-b148-b93fc0595253" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I592615c529e54bc1bce2067afd4e44ab" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\.env.template" /></Component><Component Id="I2d4a9a70d22041088d96228ba923b4b6" Guid="534a1055-375e-4b37-9c59-cfe229fb6674" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I2d4a9a70d22041088d96228ba923b4b6" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Dockerfile" /></Component><Component Id="I15f0f748a009400eba4e959d48a84f88" Guid="f4fc4e65-4d33-4d67-9e2a-2d91e080a2cf" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I15f0f748a009400eba4e959d48a84f88" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Speech2Text.py" /></Component><Component Id="I0409cf9a105141eda3a9dad5a11259fa" Guid="bdd67916-ee91-4275-9b13-74281fe06f8a" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I0409cf9a105141eda3a9dad5a11259fa" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\TTS_README.md" /></Component><Component Id="I6af7bc859d1c4bec975c84d884847ce7" Guid="09ed59ab-63d6-477c-a121-829135b36ae9" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I6af7bc859d1c4bec975c84d884847ce7" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Text2Speech.py" /></Component><Component Id="Iae10eca7ccdb4d0e9ba8092491c66f88" Guid="1ba05aa5-cde5-43c7-a4ea-5f40c29f4ae2" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iae10eca7ccdb4d0e9ba8092491c66f88" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\auth_service.py" /></Component><Component Id="If949312377ee4efaa5dc11dffe3b2d14" Guid="4fd0a1f2-be88-497c-bf81-1db43cc350f0" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_If949312377ee4efaa5dc11dffe3b2d14" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\compression_routes.py" /></Component><Component Id="I3d1a4cdb355243b2a9bd7e657fca2bea" Guid="ce509f53-05a5-46e7-bcfe-d2818e4e9fa0" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I3d1a4cdb355243b2a9bd7e657fca2bea" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\diffusers_api.py" /></Component><Component Id="I347328b2275443d2ba3acac5ca5a491e" Guid="a06c3215-9c0c-410c-a756-cff1aa6262a0" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I347328b2275443d2ba3acac5ca5a491e" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\documents.db" /></Component><Component Id="I481f31ce646e451387c96d913beff6ac" Guid="d7eda025-b5b9-45e6-921f-03e0ec8097d0" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I481f31ce646e451387c96d913beff6ac" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\install_kokoro.py" /></Component><Component Id="Ibf146eecd92a489588a170a68e00c23d" Guid="79836a1e-143b-468c-8299-4abe521f0bde" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ibf146eecd92a489588a170a68e00c23d" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\langfuse_service.py" /></Component><Component Id="I60d0a866fc4a4839afba9ae2ffdb0291" Guid="89d329ac-c8c2-4876-9ab9-1337b21a35d7" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I60d0a866fc4a4839afba9ae2ffdb0291" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\main.py" /></Component><Component Id="Ic8acf3e9446849f4a2350da7e9515ee3" Guid="edf72589-e69c-4dcf-b088-782278a661ce" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ic8acf3e9446849f4a2350da7e9515ee3" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\network_config.py" /></Component><Component Id="I145679144d09407c8cee5452d402f37c" Guid="66fe68d1-aa54-4d6c-a466-966042815e66" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I145679144d09407c8cee5452d402f37c" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\ocr_processor_premium.py" /></Component><Component Id="I7349b98e05cb47b89d28719feaaf37fb" Guid="2ce3b848-8554-4eef-8af0-a7baa81f2378" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I7349b98e05cb47b89d28719feaaf37fb" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\ocr_service.py" /></Component><Component Id="I29967d76be4547daa24da65e0539c5c7" Guid="7b383c5d-9a2a-481d-a62a-743db1b3dd95" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I29967d76be4547daa24da65e0539c5c7" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\perfect_compressor.py" /></Component><Component Id="I40219e42d1034870a1bf3c6f82dffc44" Guid="d0cba702-f981-43f0-a28b-f79f033796fc" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I40219e42d1034870a1bf3c6f82dffc44" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\requirements.txt" /></Component><Component Id="I64e0f23e820a4e77a44e777b32287547" Guid="4d969728-1785-4b89-a568-63ac84bf06ae" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I64e0f23e820a4e77a44e777b32287547" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\start_server.py" /></Component><Component Id="I0f7389d7e864440fb4dcdbc3b43ba588" Guid="31629a61-7847-434c-94f6-6bd6d722cf59" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I0f7389d7e864440fb4dcdbc3b43ba588" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_compression_tool.py" /></Component><Component Id="Idde87adc64ed43e989b69d4bc7f6daa3" Guid="c00394a6-a638-4b93-84ae-e5826bea9eb9" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Idde87adc64ed43e989b69d4bc7f6daa3" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_non_streaming.py" /></Component><Component Id="Ie220a92af6f14da38bc5dddcca37dbb7" Guid="42eb6bab-32c5-477a-b04b-faa15aee40c4" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ie220a92af6f14da38bc5dddcca37dbb7" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_optimizations.py" /></Component><Component Id="Ib631557f30c243cca64a8920cb36ebc6" Guid="10b285f2-f976-4b60-8902-0adcf8745493" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ib631557f30c243cca64a8920cb36ebc6" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_streaming_optimized.py" /></Component><Directory Id="Ia58850c01786473691486d9d0fead14c" Name="__pycache__"><Component Id="Ic28a8276d2d844e4a39c4b40f9f27c59" Guid="0a7f8ef9-1a1f-45d3-812e-379937ff12a7" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ic28a8276d2d844e4a39c4b40f9f27c59" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\langfuse_service.cpython-312.pyc" /></Component><Component Id="If6fda497ecfc46e4a27049dbb2a8e0e2" Guid="93a8b738-0c7d-4a1c-94d9-aa8663f3242f" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_If6fda497ecfc46e4a27049dbb2a8e0e2" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\main.cpython-312.pyc" /></Component><Component Id="I278625f3e61d469aae270ae3bbace749" Guid="2c037592-86ed-4311-8264-132547dd169a" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I278625f3e61d469aae270ae3bbace749" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\network_config.cpython-312.pyc" /></Component><Component Id="I4e98b18dda264cb380e24a327ee0e9b5" Guid="723de052-2b38-40a0-85f4-34530bb58630" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I4e98b18dda264cb380e24a327ee0e9b5" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\ocr_processor_premium.cpython-312.pyc" /></Component><Component Id="Ic578b4bf22f04acea3c60ab62cccc493" Guid="03c3a179-4f2c-455a-bcde-91fec41f540b" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ic578b4bf22f04acea3c60ab62cccc493" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\ocr_service.cpython-312.pyc" /></Component><Component Id="I8fc5c64e7f5a44f399eb4c40d6388ad8" Guid="10b173af-cbb8-4ac7-bb9e-3e5448d1ab8d" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I8fc5c64e7f5a44f399eb4c40d6388ad8" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\perfect_compressor.cpython-312.pyc" /></Component><Component Id="I75a9f57cca304c3ba3f2360707e83e97" Guid="0589ba10-dfd5-442b-a05b-b1e3a7ffd580" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I75a9f57cca304c3ba3f2360707e83e97" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\rag_premium_service.cpython-312.pyc" /></Component></Directory><Directory Id="I77275cc1227a48d8b6eaa208ff179bbe" Name="qdrant_storage"><Component Id="Ib1ad2173cf6443f6bc90c31456dd7ac9" Guid="a5d85b93-c92e-4459-9d15-1d28e2784e95" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ib1ad2173cf6443f6bc90c31456dd7ac9" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\qdrant_storage\.lock" /></Component><Component Id="I842e52dc9a674e57984c4d9c92774b39" Guid="1a10dd0b-f5ea-4a8e-8475-f690a5ed27d9" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I842e52dc9a674e57984c4d9c92774b39" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\qdrant_storage\meta.json" /></Component></Directory><Directory Id="Id4e4f646319c481f9af902a21bfb9309" Name="services"><Component Id="I1f47ce229eda4ebaac6dba53703e7911" Guid="fe78ffd8-355c-49c3-8efc-489531e81829" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I1f47ce229eda4ebaac6dba53703e7911" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\internet_search_service.py" /></Component><Component Id="I408b12c7c85d4e7084447511f9e3d268" Guid="12ed25c2-b3dd-494c-9e10-db2554462fc8" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I408b12c7c85d4e7084447511f9e3d268" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\startup_service.py" /></Component><Component Id="I647a28af0656482caccbcbde2ebd1891" Guid="fda76c97-d9ce-4741-84f9-06fbe6ee0abc" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I647a28af0656482caccbcbde2ebd1891" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\web_scraper_service.py" /></Component><Directory Id="I447fd5fe11c740c09764f71fc08edc57" Name="__pycache__"><Component Id="I7cb1f2456deb40148df204239bd8fa8a" Guid="9ffbc1c9-3afa-4391-93df-108f3370db02" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I7cb1f2456deb40148df204239bd8fa8a" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\internet_search_service.cpython-312.pyc" /></Component><Component Id="I670cd3a129994b25bdec3d60d49b1971" Guid="10534741-af91-4bc2-86db-5735187b4838" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I670cd3a129994b25bdec3d60d49b1971" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\startup_service.cpython-312.pyc" /></Component><Component Id="I27922fdacab94d85a1114440ba649e58" Guid="609a5429-13a4-4bf4-b1b5-abc39d03208c" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I27922fdacab94d85a1114440ba649e58" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\web_scraper_service.cpython-312.pyc" /></Component></Directory></Directory><Directory Id="Icf050421e7b546fc83870ac418035656" Name="test_qdrant_temp"><Component Id="Ic52c834b50e24452ab5c71eab116da20" Guid="4e4fa223-97f8-447b-850e-303402eb4061" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ic52c834b50e24452ab5c71eab116da20" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_qdrant_temp\.lock" /></Component><Directory Id="I59cc5a54126a4b2e89c29d6ed4bab2b4" Name="collection"><Directory Id="Ie7fc5f68b2a0459f9fea2df1ff44e24d" Name="test_collection"><Component Id="Ia5247ebcf3c14a268f1f36f2438e88b4" Guid="24fd4c42-6e6f-4266-9f49-b94c35e06ecc" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ia5247ebcf3c14a268f1f36f2438e88b4" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_qdrant_temp\collection\test_collection\storage.sqlite" /></Component></Directory></Directory></Directory><Directory Id="Id3a2f0480a3f40c2abd2fe6e7cb66fed" Name="tools"><Component Id="I285cc51ca4194f19b2b7fdad0eed920a" Guid="f4f50404-b9d2-4228-b338-2f90c16f0c39" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I285cc51ca4194f19b2b7fdad0eed920a" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\tools\internet_search_tools.py" /></Component><Directory Id="Ie150bb2f5f3942b19f1e681d4d8a5344" Name="__pycache__"><Component Id="I683da011aca2408da16b769bbe2853a4" Guid="f196fd99-cb8e-4e17-afae-1852dbc94d36" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I683da011aca2408da16b769bbe2853a4" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\tools\__pycache__\internet_search_tools.cpython-312.pyc" /></Component></Directory></Directory></Directory></Directory>
            <Component Id="CMP_UninstallShortcut" Guid="*">

                <Shortcut Id="UninstallShortcut"
						  Name="Uninstall WeMa IA"
						  Description="Uninstalls WeMa IA"
						  Target="[System64Folder]msiexec.exe"
						  Arguments="/x [ProductCode]" />

				<RemoveFolder Id="INSTALLDIR"
							  On="uninstall" />

				<RegistryValue Root="HKCU"
							   Key="Software\wema-ia\WeMa IA"
							   Name="Uninstaller Shortcut"
							   Type="integer"
							   Value="1"
							   KeyPath="yes" />
            </Component>
        </DirectoryRef>

        <DirectoryRef Id="ApplicationProgramsFolder">
            <Component Id="ApplicationShortcut" Guid="*">
                <Shortcut Id="ApplicationStartMenuShortcut"
                    Name="WeMa IA"
                    Description="Runs WeMa IA"
                    Target="[!Path]"
                    Icon="ProductIcon"
                    WorkingDirectory="INSTALLDIR">
                    <ShortcutProperty Key="System.AppUserModel.ID" Value="com.wema-ia.app"/>
                </Shortcut>
                <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall"/>
                <RegistryValue Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="Start Menu Shortcut" Type="integer" Value="1" KeyPath="yes"/>
           </Component>
        </DirectoryRef>

<Feature
                Id="MainProgram"
                Title="Application"
                Description="!(loc.InstallAppFeature)"
                Level="1"
                ConfigurableDirectory="INSTALLDIR"
                AllowAdvertise="no"
                Display="expand"
                Absent="disallow">

            <ComponentRef Id="RegistryEntries"/>

<ComponentRef Id="Iff8717b16beb4d9da1827a4e14b17b33"/>
<ComponentRef Id="I592615c529e54bc1bce2067afd4e44ab"/>
<ComponentRef Id="I2d4a9a70d22041088d96228ba923b4b6"/>
<ComponentRef Id="I15f0f748a009400eba4e959d48a84f88"/>
<ComponentRef Id="I0409cf9a105141eda3a9dad5a11259fa"/>
<ComponentRef Id="I6af7bc859d1c4bec975c84d884847ce7"/>
<ComponentRef Id="Iae10eca7ccdb4d0e9ba8092491c66f88"/>
<ComponentRef Id="If949312377ee4efaa5dc11dffe3b2d14"/>
<ComponentRef Id="I3d1a4cdb355243b2a9bd7e657fca2bea"/>
<ComponentRef Id="I347328b2275443d2ba3acac5ca5a491e"/>
<ComponentRef Id="I481f31ce646e451387c96d913beff6ac"/>
<ComponentRef Id="Ibf146eecd92a489588a170a68e00c23d"/>
<ComponentRef Id="I60d0a866fc4a4839afba9ae2ffdb0291"/>
<ComponentRef Id="Ic8acf3e9446849f4a2350da7e9515ee3"/>
<ComponentRef Id="I145679144d09407c8cee5452d402f37c"/>
<ComponentRef Id="I7349b98e05cb47b89d28719feaaf37fb"/>
<ComponentRef Id="I29967d76be4547daa24da65e0539c5c7"/>
<ComponentRef Id="I40219e42d1034870a1bf3c6f82dffc44"/>
<ComponentRef Id="I64e0f23e820a4e77a44e777b32287547"/>
<ComponentRef Id="I0f7389d7e864440fb4dcdbc3b43ba588"/>
<ComponentRef Id="Idde87adc64ed43e989b69d4bc7f6daa3"/>
<ComponentRef Id="Ie220a92af6f14da38bc5dddcca37dbb7"/>
<ComponentRef Id="Ib631557f30c243cca64a8920cb36ebc6"/>
<ComponentRef Id="Ic28a8276d2d844e4a39c4b40f9f27c59"/>
<ComponentRef Id="If6fda497ecfc46e4a27049dbb2a8e0e2"/>
<ComponentRef Id="I278625f3e61d469aae270ae3bbace749"/>
<ComponentRef Id="I4e98b18dda264cb380e24a327ee0e9b5"/>
<ComponentRef Id="Ic578b4bf22f04acea3c60ab62cccc493"/>
<ComponentRef Id="I8fc5c64e7f5a44f399eb4c40d6388ad8"/>
<ComponentRef Id="I75a9f57cca304c3ba3f2360707e83e97"/>
<ComponentRef Id="Ib1ad2173cf6443f6bc90c31456dd7ac9"/>
<ComponentRef Id="I842e52dc9a674e57984c4d9c92774b39"/>
<ComponentRef Id="I1f47ce229eda4ebaac6dba53703e7911"/>
<ComponentRef Id="I408b12c7c85d4e7084447511f9e3d268"/>
<ComponentRef Id="I647a28af0656482caccbcbde2ebd1891"/>
<ComponentRef Id="I7cb1f2456deb40148df204239bd8fa8a"/>
<ComponentRef Id="I670cd3a129994b25bdec3d60d49b1971"/>
<ComponentRef Id="I27922fdacab94d85a1114440ba649e58"/>
<ComponentRef Id="Ic52c834b50e24452ab5c71eab116da20"/>
<ComponentRef Id="Ia5247ebcf3c14a268f1f36f2438e88b4"/>
<ComponentRef Id="I285cc51ca4194f19b2b7fdad0eed920a"/>
<ComponentRef Id="I683da011aca2408da16b769bbe2853a4"/>

            <Feature Id="ShortcutsFeature"
                Title="Shortcuts"
                Level="1">
                <ComponentRef Id="Path"/>
                <ComponentRef Id="CMP_UninstallShortcut" />
                <ComponentRef Id="ApplicationShortcut" />
                <ComponentRef Id="ApplicationShortcutDesktop" />
            </Feature>

            <Feature
                Id="Environment"
                Title="PATH Environment Variable"
                Description="!(loc.PathEnvVarFeature)"
                Level="1"
                Absent="allow">
            <ComponentRef Id="Path"/>
</Feature>
        </Feature>

        <Feature Id="External" AllowAdvertise="no" Absent="disallow">
</Feature>

        <!-- WebView2 -->
        <Property Id="WVRTINSTALLED">
            <RegistrySearch Id="WVRTInstalledSystem" Root="HKLM" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw" Win64="no" />
            <RegistrySearch Id="WVRTInstalledUser" Root="HKCU" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw"/>
        </Property>

        <CustomAction Id='DownloadAndInvokeBootstrapper' Directory="INSTALLDIR" Execute="deferred" ExeCommand='powershell.exe -NoProfile -windowstyle hidden try [\{] [\[]Net.ServicePointManager[\]]::SecurityProtocol = [\[]Net.SecurityProtocolType[\]]::Tls12 [\}] catch [\{][\}]; Invoke-WebRequest -Uri "https://go.microsoft.com/fwlink/p/?LinkId=2124703" -OutFile "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" ; Start-Process -FilePath "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" -ArgumentList (&apos;/silent&apos;, &apos;/install&apos;) -Wait' Return='check'/>
        <InstallExecuteSequence>
            <Custom Action='DownloadAndInvokeBootstrapper' Before='InstallFinalize'>
                <![CDATA[NOT(REMOVE OR WVRTINSTALLED)]]>
            </Custom>
        </InstallExecuteSequence>

        <!-- Embedded webview bootstrapper mode -->

        <!-- Embedded offline installer -->



        <InstallExecuteSequence>
          <Custom Action="LaunchApplication" After="InstallFinalize">AUTOLAUNCHAPP AND NOT Installed</Custom>
        </InstallExecuteSequence>

        <SetProperty Id="ARPINSTALLLOCATION" Value="[INSTALLDIR]" After="CostFinalize"/>
    </Product>
</Wix>
