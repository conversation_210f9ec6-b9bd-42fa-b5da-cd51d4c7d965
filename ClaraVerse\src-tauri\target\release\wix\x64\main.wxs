<?if $(sys.B<PERSON><PERSON>AR<PERSON>)="x86"?>
    <?define Win64 = "no" ?>
    <?define PlatformProgramFilesFolder = "ProgramFilesFolder" ?>
<?elseif $(sys.BUILDARCH)="x64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?elseif $(sys.BUILDARCH)="arm64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?else?>
    <?error Unsupported value of sys.BUILDARCH=$(sys.BUILDARCH)?>
<?endif?>

<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Product
            Id="*"
            Name="WeMa IA"
            UpgradeCode="7adcaf1a-334a-54b2-baee-a32f3344ceea"
            Language="!(loc.TauriLanguage)"
            Manufacturer="wema-ia"
            Version="0.1.2">

        <Package Id="*"
                 Keywords="Installer"
                 InstallerVersion="450"
                 Languages="0"
                 Compressed="yes"
                 InstallScope="perMachine"
                 SummaryCodepage="!(loc.TauriCodepage)"/>

        <!-- https://docs.microsoft.com/en-us/windows/win32/msi/reinstallmode -->
        <!-- reinstall all files; rewrite all registry entries; reinstall all shortcuts -->
        <Property Id="REINSTALLMODE" Value="amus" />

        <!-- Auto launch app after installation, useful for passive mode which usually used in updates -->
        <Property Id="AUTOLAUNCHAPP" Secure="yes" />
        <!-- Property to forward cli args to the launched app to not lose those of the pre-update instance -->
        <Property Id="LAUNCHAPPARGS" Secure="yes" />

            <MajorUpgrade Schedule="afterInstallInitialize" AllowDowngrades="yes" />

        <InstallExecuteSequence>
            <RemoveShortcuts>Installed AND NOT UPGRADINGPRODUCTCODE</RemoveShortcuts>
        </InstallExecuteSequence>

        <Media Id="1" Cabinet="app.cab" EmbedCab="yes" />


        <Icon Id="ProductIcon" SourceFile="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\resources\icon.ico"/>
        <Property Id="ARPPRODUCTICON" Value="ProductIcon" />
        <Property Id="ARPNOREPAIR" Value="yes" Secure="yes" />      <!-- Remove repair -->
        <SetProperty Id="ARPNOMODIFY" Value="1" After="InstallValidate" Sequence="execute"/>


        <Property Id="INSTALLDIR">
          <!-- First attempt: Search for "InstallDir" -->
          <RegistrySearch Id="PrevInstallDirWithName" Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="InstallDir" Type="raw" />

          <!-- Second attempt: If the first fails, search for the default key value (this is how the nsis installer currently stores the path) -->
          <RegistrySearch Id="PrevInstallDirNoName" Root="HKCU" Key="Software\wema-ia\WeMa IA" Type="raw" />
        </Property>

        <!-- launch app checkbox -->
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOXTEXT" Value="!(loc.LaunchApp)" />
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOX" Value="1"/>
        <CustomAction Id="LaunchApplication" Impersonate="yes" FileKey="Path" ExeCommand="[LAUNCHAPPARGS]" Return="asyncNoWait" />

        <UI>
            <!-- launch app checkbox -->
            <Publish Dialog="ExitDialog" Control="Finish" Event="DoAction" Value="LaunchApplication">WIXUI_EXITDIALOGOPTIONALCHECKBOX = 1 and NOT Installed</Publish>

            <Property Id="WIXUI_INSTALLDIR" Value="INSTALLDIR" />

            <!-- Skip license dialog -->
            <Publish Dialog="WelcomeDlg"
                     Control="Next"
                     Event="NewDialog"
                     Value="InstallDirDlg"
                     Order="2">1</Publish>
            <Publish Dialog="InstallDirDlg"
                     Control="Back"
                     Event="NewDialog"
                     Value="WelcomeDlg"
                     Order="2">1</Publish>
        </UI>

        <UIRef Id="WixUI_InstallDir" />

        <Directory Id="TARGETDIR" Name="SourceDir">
            <Directory Id="DesktopFolder" Name="Desktop">
                <Component Id="ApplicationShortcutDesktop" Guid="*">
                    <Shortcut Id="ApplicationDesktopShortcut" Name="WeMa IA" Description="Runs WeMa IA" Target="[!Path]" WorkingDirectory="INSTALLDIR" />
                    <RemoveFolder Id="DesktopFolder" On="uninstall" />
                    <RegistryValue Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="Desktop Shortcut" Type="integer" Value="1" KeyPath="yes" />
                </Component>
            </Directory>
            <Directory Id="$(var.PlatformProgramFilesFolder)" Name="PFiles">
                <Directory Id="INSTALLDIR" Name="WeMa IA"/>
            </Directory>
            <Directory Id="ProgramMenuFolder">
                <Directory Id="ApplicationProgramsFolder" Name="WeMa IA"/>
            </Directory>
        </Directory>

        <DirectoryRef Id="INSTALLDIR">
            <Component Id="RegistryEntries" Guid="*">
                <RegistryKey Root="HKCU" Key="Software\wema-ia\WeMa IA">
                    <RegistryValue Name="InstallDir" Type="string" Value="[INSTALLDIR]" KeyPath="yes" />
                </RegistryKey>
                <!-- Change the Root to HKCU for perUser installations -->
</Component>
            <Component Id="Path" Guid="3b783432-6e5b-5f62-a8d8-17ab766c4f33" Win64="$(var.Win64)">
                <File Id="Path" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\app.exe" KeyPath="yes" Checksum="yes"/>
</Component>
            <Directory Id="I290a6490e8a2449086df869386f95ba2" Name="_up_"><Directory Id="I507558ec345248c798928d0b3f0f7d33" Name="py_backend"><Component Id="I2dc095f0de4c4e6ca1e914d8fcee9148" Guid="c839c6cf-5a49-4412-bb6d-0af4297e12eb" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I2dc095f0de4c4e6ca1e914d8fcee9148" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\.env" /></Component><Component Id="I35012fb3522d4051b23bf96cdb52fd6b" Guid="5703165b-bd14-434a-8819-139c5a3cf3c5" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I35012fb3522d4051b23bf96cdb52fd6b" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\.env.template" /></Component><Component Id="Ia74ef34374044c1a8657c34bb3e89be8" Guid="e38225a1-445f-4577-9e4a-25cadfd419d7" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ia74ef34374044c1a8657c34bb3e89be8" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Dockerfile" /></Component><Component Id="I231b7d891d0b497a9811a6946c47a4b6" Guid="8ac02d17-fb49-46ac-84d8-a871a0bd2587" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I231b7d891d0b497a9811a6946c47a4b6" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Speech2Text.py" /></Component><Component Id="I02395db624e1460096be74d077f0a214" Guid="35b68643-7cd5-4232-b6e9-c2f5ff9ba719" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I02395db624e1460096be74d077f0a214" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\TTS_README.md" /></Component><Component Id="Ic18658dfd29d45bd99a4dfb4d51141d2" Guid="c543f88d-7ba8-44bb-a489-e5d2090204d0" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ic18658dfd29d45bd99a4dfb4d51141d2" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Text2Speech.py" /></Component><Component Id="Ib35afaf72a734f859b29b3f7aabbf1c4" Guid="81bec9a5-1190-41e1-9835-256911e689fd" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ib35afaf72a734f859b29b3f7aabbf1c4" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\auth_service.py" /></Component><Component Id="I470562c6573d4f979d05945c0fb762f1" Guid="7cb96bcb-9cd5-4d40-af23-a36a290c99ac" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I470562c6573d4f979d05945c0fb762f1" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\compression_routes.py" /></Component><Component Id="I79229b990a634dba82a2dcc12399fff4" Guid="ef6030de-4622-468b-be40-43dc76100c28" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I79229b990a634dba82a2dcc12399fff4" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\diffusers_api.py" /></Component><Component Id="Ic4c0805a8ec54645922f029a525ca771" Guid="7c795260-aec0-49bd-9c1d-4631b76a8361" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ic4c0805a8ec54645922f029a525ca771" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\documents.db" /></Component><Component Id="I061a25a7990b47968dd6fe77e5e6dc8b" Guid="820b2c94-872b-4bd1-9367-a6dc86f49fde" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I061a25a7990b47968dd6fe77e5e6dc8b" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\install_kokoro.py" /></Component><Component Id="I6ff15e74032847e0bae3249493261081" Guid="f8348f57-8590-43e3-b096-b5d1eb08832a" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I6ff15e74032847e0bae3249493261081" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\langfuse_service.py" /></Component><Component Id="I498fdc7ec236453a9b415537b43f13a8" Guid="576355fd-05ef-4b69-bb8e-45526363bbd7" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I498fdc7ec236453a9b415537b43f13a8" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\main.py" /></Component><Component Id="Ie6e1cc26881c4c25b5c018419681cc9a" Guid="d6bba62a-f3a1-470d-ac32-87e28ee32629" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ie6e1cc26881c4c25b5c018419681cc9a" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\network_config.py" /></Component><Component Id="I4149503be5534fe0967c07827a1a0ea7" Guid="a05708e6-ac58-4ce5-959e-1fb1506e71c3" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I4149503be5534fe0967c07827a1a0ea7" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\ocr_processor_premium.py" /></Component><Component Id="I09a4f3bef29742319255193e2fc5f604" Guid="c9ebb427-cfc1-407c-8633-9bbd3d4dbd12" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I09a4f3bef29742319255193e2fc5f604" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\ocr_service.py" /></Component><Component Id="I1ca1902cd8254a7c835e5cb424741e5e" Guid="3452dc5d-8294-4257-bbdf-b5dd46444d2c" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I1ca1902cd8254a7c835e5cb424741e5e" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\perfect_compressor.py" /></Component><Component Id="I7d5367e6e80a4ee8aef68d3fe0a21022" Guid="5831446a-f580-4da8-b67a-0fa9ce0f8a35" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I7d5367e6e80a4ee8aef68d3fe0a21022" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\requirements.txt" /></Component><Component Id="I2b6b3014e27045e78d5aafff85d4a3d2" Guid="3a761d16-cd43-4a1a-88b3-65addfc13c11" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I2b6b3014e27045e78d5aafff85d4a3d2" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\start_server.py" /></Component><Component Id="Ic702c0abd34a44e7a64c5c8299b70faa" Guid="ff7091e3-7248-4947-9cd8-55ca905d04e2" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ic702c0abd34a44e7a64c5c8299b70faa" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_compression_tool.py" /></Component><Component Id="I2f12c716d87a4cd38c181d44170087cc" Guid="920cd4c6-2b84-4ecd-834e-68670f630a15" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I2f12c716d87a4cd38c181d44170087cc" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_non_streaming.py" /></Component><Component Id="Ic96e5ba6e13e462195cc5ec74342c853" Guid="b80977d4-**************-575cd4f86475" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ic96e5ba6e13e462195cc5ec74342c853" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_optimizations.py" /></Component><Component Id="Ie163c0f2c5c34a308d685387aa74ae41" Guid="3f8a98cd-8fdc-47ae-abec-7cd72c4bd85f" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ie163c0f2c5c34a308d685387aa74ae41" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_streaming_optimized.py" /></Component><Directory Id="Ie65683dc5aa548a6a384f931399c9d56" Name="__pycache__"><Component Id="Iad522ec5680343fda31cbe3b12546370" Guid="2f95d0ab-9c0f-43c7-8137-e6296346a038" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iad522ec5680343fda31cbe3b12546370" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\langfuse_service.cpython-312.pyc" /></Component><Component Id="I66e4548eb6e34ce2aaff34f4d143d7ce" Guid="17b7c4e2-8c94-4142-9d00-09a377e35326" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I66e4548eb6e34ce2aaff34f4d143d7ce" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\main.cpython-312.pyc" /></Component><Component Id="I7c19eec119df4522acc0b069ad298e91" Guid="760551b6-7546-47d3-a1f6-838cc22f9c0d" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I7c19eec119df4522acc0b069ad298e91" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\network_config.cpython-312.pyc" /></Component><Component Id="Ic0c1b6bd24d4483aa19a408cd7b27a8e" Guid="23bb5de1-d764-4744-b3ee-ebd30c2a3fc8" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ic0c1b6bd24d4483aa19a408cd7b27a8e" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\ocr_processor_premium.cpython-312.pyc" /></Component><Component Id="Ifc565b5799fe49bf994e1016c8bc0552" Guid="e72225a7-d890-4ff8-b528-24ba49c7ac4c" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ifc565b5799fe49bf994e1016c8bc0552" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\ocr_service.cpython-312.pyc" /></Component><Component Id="I1c3db327973b486db3959b2224746118" Guid="12334773-a1fa-4b91-af6d-277a2cc29f38" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I1c3db327973b486db3959b2224746118" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\perfect_compressor.cpython-312.pyc" /></Component><Component Id="I5ca21735c6c241b1b2c82aef26b9c94b" Guid="6427bb32-c268-492e-a2b4-052717b5e6eb" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I5ca21735c6c241b1b2c82aef26b9c94b" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\rag_premium_service.cpython-312.pyc" /></Component></Directory><Directory Id="Ia1d1962f192d4d87abe961d577e690fa" Name="qdrant_storage"><Component Id="Iad5420ad32a2461588c15f27cbd643d1" Guid="a01a1e4b-e85d-419c-ab15-abbfeb31ac86" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iad5420ad32a2461588c15f27cbd643d1" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\qdrant_storage\.lock" /></Component><Component Id="I5be77a97390c413789ab3dc6b38d2b8c" Guid="847a31a0-1857-4468-ac1c-cba02b5515cd" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I5be77a97390c413789ab3dc6b38d2b8c" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\qdrant_storage\meta.json" /></Component></Directory><Directory Id="Ib9eca4fd4a2344bea6738bf76cc9c184" Name="services"><Component Id="I32df3b7d0dac4529b027d14b0033eb1a" Guid="36890f3f-98a7-477c-91b5-cbc9247592b1" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I32df3b7d0dac4529b027d14b0033eb1a" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\internet_search_service.py" /></Component><Component Id="I463e42a27d50450aab558c1cf5e0d219" Guid="e71461f2-8e13-4542-a94e-7816fe0faa8e" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I463e42a27d50450aab558c1cf5e0d219" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\startup_service.py" /></Component><Component Id="I72ec6aa785eb4e68abfcc0ad5e2e4b99" Guid="1a606cff-8235-47dd-ad5e-8be28231dfa6" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I72ec6aa785eb4e68abfcc0ad5e2e4b99" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\web_scraper_service.py" /></Component><Directory Id="I190f8ed04d68449da2eea07e9d466b0c" Name="__pycache__"><Component Id="I9b08c53219fa4b6e959909d8798d4a9d" Guid="3a8c7e99-a43c-4095-841b-4bfcaea15cd9" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I9b08c53219fa4b6e959909d8798d4a9d" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\internet_search_service.cpython-312.pyc" /></Component><Component Id="If3e6f9e627e74afb865b9c5d1223a0c9" Guid="9218417d-8272-40b9-b3b7-cb0e03815050" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_If3e6f9e627e74afb865b9c5d1223a0c9" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\startup_service.cpython-312.pyc" /></Component><Component Id="I7f3c09102f1c4378ab39e274fc244c6a" Guid="7df0df36-2183-474e-9fbd-1f4b435d4add" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I7f3c09102f1c4378ab39e274fc244c6a" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\web_scraper_service.cpython-312.pyc" /></Component></Directory></Directory><Directory Id="I712dc3bf054e47fb8b7e8ba593e8db3c" Name="test_qdrant_temp"><Component Id="Iec598a412391445789c646d655620691" Guid="cd4da99f-0451-46d2-97da-ee26fae48af8" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iec598a412391445789c646d655620691" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_qdrant_temp\.lock" /></Component><Directory Id="Ieecf0179e7884d96a5ff28c3cea6d448" Name="collection"><Directory Id="Icaec82d3d35d4e8385ba6f7866635357" Name="test_collection"><Component Id="I99a0c3377b074f0585d2df46c85e951c" Guid="814b140a-d904-401b-bc2b-e5a5f8cd8c42" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I99a0c3377b074f0585d2df46c85e951c" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_qdrant_temp\collection\test_collection\storage.sqlite" /></Component></Directory></Directory></Directory><Directory Id="Ieb5e822da9f6496ab3d7ac335e2207ad" Name="tools"><Component Id="I019e705c53f7401db4016aa4042958bc" Guid="ceda1414-5f79-47e7-b7c7-fe4511db4639" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I019e705c53f7401db4016aa4042958bc" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\tools\internet_search_tools.py" /></Component><Directory Id="Ia2b51dbc196c4cd4bd5697834da71524" Name="__pycache__"><Component Id="I0920d737588b462dad295c9dbf1079c2" Guid="2b651e63-68ad-4fcb-ab6c-672ca04c78be" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I0920d737588b462dad295c9dbf1079c2" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\tools\__pycache__\internet_search_tools.cpython-312.pyc" /></Component></Directory></Directory></Directory></Directory>
            <Component Id="CMP_UninstallShortcut" Guid="*">

                <Shortcut Id="UninstallShortcut"
						  Name="Uninstall WeMa IA"
						  Description="Uninstalls WeMa IA"
						  Target="[System64Folder]msiexec.exe"
						  Arguments="/x [ProductCode]" />

				<RemoveFolder Id="INSTALLDIR"
							  On="uninstall" />

				<RegistryValue Root="HKCU"
							   Key="Software\wema-ia\WeMa IA"
							   Name="Uninstaller Shortcut"
							   Type="integer"
							   Value="1"
							   KeyPath="yes" />
            </Component>
        </DirectoryRef>

        <DirectoryRef Id="ApplicationProgramsFolder">
            <Component Id="ApplicationShortcut" Guid="*">
                <Shortcut Id="ApplicationStartMenuShortcut"
                    Name="WeMa IA"
                    Description="Runs WeMa IA"
                    Target="[!Path]"
                    Icon="ProductIcon"
                    WorkingDirectory="INSTALLDIR">
                    <ShortcutProperty Key="System.AppUserModel.ID" Value="com.wema-ia.app"/>
                </Shortcut>
                <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall"/>
                <RegistryValue Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="Start Menu Shortcut" Type="integer" Value="1" KeyPath="yes"/>
           </Component>
        </DirectoryRef>

<Feature
                Id="MainProgram"
                Title="Application"
                Description="!(loc.InstallAppFeature)"
                Level="1"
                ConfigurableDirectory="INSTALLDIR"
                AllowAdvertise="no"
                Display="expand"
                Absent="disallow">

            <ComponentRef Id="RegistryEntries"/>

<ComponentRef Id="I2dc095f0de4c4e6ca1e914d8fcee9148"/>
<ComponentRef Id="I35012fb3522d4051b23bf96cdb52fd6b"/>
<ComponentRef Id="Ia74ef34374044c1a8657c34bb3e89be8"/>
<ComponentRef Id="I231b7d891d0b497a9811a6946c47a4b6"/>
<ComponentRef Id="I02395db624e1460096be74d077f0a214"/>
<ComponentRef Id="Ic18658dfd29d45bd99a4dfb4d51141d2"/>
<ComponentRef Id="Ib35afaf72a734f859b29b3f7aabbf1c4"/>
<ComponentRef Id="I470562c6573d4f979d05945c0fb762f1"/>
<ComponentRef Id="I79229b990a634dba82a2dcc12399fff4"/>
<ComponentRef Id="Ic4c0805a8ec54645922f029a525ca771"/>
<ComponentRef Id="I061a25a7990b47968dd6fe77e5e6dc8b"/>
<ComponentRef Id="I6ff15e74032847e0bae3249493261081"/>
<ComponentRef Id="I498fdc7ec236453a9b415537b43f13a8"/>
<ComponentRef Id="Ie6e1cc26881c4c25b5c018419681cc9a"/>
<ComponentRef Id="I4149503be5534fe0967c07827a1a0ea7"/>
<ComponentRef Id="I09a4f3bef29742319255193e2fc5f604"/>
<ComponentRef Id="I1ca1902cd8254a7c835e5cb424741e5e"/>
<ComponentRef Id="I7d5367e6e80a4ee8aef68d3fe0a21022"/>
<ComponentRef Id="I2b6b3014e27045e78d5aafff85d4a3d2"/>
<ComponentRef Id="Ic702c0abd34a44e7a64c5c8299b70faa"/>
<ComponentRef Id="I2f12c716d87a4cd38c181d44170087cc"/>
<ComponentRef Id="Ic96e5ba6e13e462195cc5ec74342c853"/>
<ComponentRef Id="Ie163c0f2c5c34a308d685387aa74ae41"/>
<ComponentRef Id="Iad522ec5680343fda31cbe3b12546370"/>
<ComponentRef Id="I66e4548eb6e34ce2aaff34f4d143d7ce"/>
<ComponentRef Id="I7c19eec119df4522acc0b069ad298e91"/>
<ComponentRef Id="Ic0c1b6bd24d4483aa19a408cd7b27a8e"/>
<ComponentRef Id="Ifc565b5799fe49bf994e1016c8bc0552"/>
<ComponentRef Id="I1c3db327973b486db3959b2224746118"/>
<ComponentRef Id="I5ca21735c6c241b1b2c82aef26b9c94b"/>
<ComponentRef Id="Iad5420ad32a2461588c15f27cbd643d1"/>
<ComponentRef Id="I5be77a97390c413789ab3dc6b38d2b8c"/>
<ComponentRef Id="I32df3b7d0dac4529b027d14b0033eb1a"/>
<ComponentRef Id="I463e42a27d50450aab558c1cf5e0d219"/>
<ComponentRef Id="I72ec6aa785eb4e68abfcc0ad5e2e4b99"/>
<ComponentRef Id="I9b08c53219fa4b6e959909d8798d4a9d"/>
<ComponentRef Id="If3e6f9e627e74afb865b9c5d1223a0c9"/>
<ComponentRef Id="I7f3c09102f1c4378ab39e274fc244c6a"/>
<ComponentRef Id="Iec598a412391445789c646d655620691"/>
<ComponentRef Id="I99a0c3377b074f0585d2df46c85e951c"/>
<ComponentRef Id="I019e705c53f7401db4016aa4042958bc"/>
<ComponentRef Id="I0920d737588b462dad295c9dbf1079c2"/>

            <Feature Id="ShortcutsFeature"
                Title="Shortcuts"
                Level="1">
                <ComponentRef Id="Path"/>
                <ComponentRef Id="CMP_UninstallShortcut" />
                <ComponentRef Id="ApplicationShortcut" />
                <ComponentRef Id="ApplicationShortcutDesktop" />
            </Feature>

            <Feature
                Id="Environment"
                Title="PATH Environment Variable"
                Description="!(loc.PathEnvVarFeature)"
                Level="1"
                Absent="allow">
            <ComponentRef Id="Path"/>
</Feature>
        </Feature>

        <Feature Id="External" AllowAdvertise="no" Absent="disallow">
</Feature>

        <!-- WebView2 -->
        <Property Id="WVRTINSTALLED">
            <RegistrySearch Id="WVRTInstalledSystem" Root="HKLM" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw" Win64="no" />
            <RegistrySearch Id="WVRTInstalledUser" Root="HKCU" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw"/>
        </Property>

        <CustomAction Id='DownloadAndInvokeBootstrapper' Directory="INSTALLDIR" Execute="deferred" ExeCommand='powershell.exe -NoProfile -windowstyle hidden try [\{] [\[]Net.ServicePointManager[\]]::SecurityProtocol = [\[]Net.SecurityProtocolType[\]]::Tls12 [\}] catch [\{][\}]; Invoke-WebRequest -Uri "https://go.microsoft.com/fwlink/p/?LinkId=2124703" -OutFile "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" ; Start-Process -FilePath "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" -ArgumentList (&apos;/silent&apos;, &apos;/install&apos;) -Wait' Return='check'/>
        <InstallExecuteSequence>
            <Custom Action='DownloadAndInvokeBootstrapper' Before='InstallFinalize'>
                <![CDATA[NOT(REMOVE OR WVRTINSTALLED)]]>
            </Custom>
        </InstallExecuteSequence>

        <!-- Embedded webview bootstrapper mode -->

        <!-- Embedded offline installer -->



        <InstallExecuteSequence>
          <Custom Action="LaunchApplication" After="InstallFinalize">AUTOLAUNCHAPP AND NOT Installed</Custom>
        </InstallExecuteSequence>

        <SetProperty Id="ARPINSTALLLOCATION" Value="[INSTALLDIR]" After="CostFinalize"/>
    </Product>
</Wix>
