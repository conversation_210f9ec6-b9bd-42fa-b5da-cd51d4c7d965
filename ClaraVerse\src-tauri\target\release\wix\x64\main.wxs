<?if $(sys.B<PERSON><PERSON>AR<PERSON>)="x86"?>
    <?define Win64 = "no" ?>
    <?define PlatformProgramFilesFolder = "ProgramFilesFolder" ?>
<?elseif $(sys.BUILDARCH)="x64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?elseif $(sys.BUILDARCH)="arm64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?else?>
    <?error Unsupported value of sys.BUILDARCH=$(sys.BUILDARCH)?>
<?endif?>

<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Product
            Id="*"
            Name="WeMa IA"
            UpgradeCode="7adcaf1a-334a-54b2-baee-a32f3344ceea"
            Language="!(loc.TauriLanguage)"
            Manufacturer="wema-ia"
            Version="0.1.2">

        <Package Id="*"
                 Keywords="Installer"
                 InstallerVersion="450"
                 Languages="0"
                 Compressed="yes"
                 InstallScope="perMachine"
                 SummaryCodepage="!(loc.TauriCodepage)"/>

        <!-- https://docs.microsoft.com/en-us/windows/win32/msi/reinstallmode -->
        <!-- reinstall all files; rewrite all registry entries; reinstall all shortcuts -->
        <Property Id="REINSTALLMODE" Value="amus" />

        <!-- Auto launch app after installation, useful for passive mode which usually used in updates -->
        <Property Id="AUTOLAUNCHAPP" Secure="yes" />
        <!-- Property to forward cli args to the launched app to not lose those of the pre-update instance -->
        <Property Id="LAUNCHAPPARGS" Secure="yes" />

            <MajorUpgrade Schedule="afterInstallInitialize" AllowDowngrades="yes" />

        <InstallExecuteSequence>
            <RemoveShortcuts>Installed AND NOT UPGRADINGPRODUCTCODE</RemoveShortcuts>
        </InstallExecuteSequence>

        <Media Id="1" Cabinet="app.cab" EmbedCab="yes" />


        <Icon Id="ProductIcon" SourceFile="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\resources\icon.ico"/>
        <Property Id="ARPPRODUCTICON" Value="ProductIcon" />
        <Property Id="ARPNOREPAIR" Value="yes" Secure="yes" />      <!-- Remove repair -->
        <SetProperty Id="ARPNOMODIFY" Value="1" After="InstallValidate" Sequence="execute"/>


        <Property Id="INSTALLDIR">
          <!-- First attempt: Search for "InstallDir" -->
          <RegistrySearch Id="PrevInstallDirWithName" Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="InstallDir" Type="raw" />

          <!-- Second attempt: If the first fails, search for the default key value (this is how the nsis installer currently stores the path) -->
          <RegistrySearch Id="PrevInstallDirNoName" Root="HKCU" Key="Software\wema-ia\WeMa IA" Type="raw" />
        </Property>

        <!-- launch app checkbox -->
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOXTEXT" Value="!(loc.LaunchApp)" />
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOX" Value="1"/>
        <CustomAction Id="LaunchApplication" Impersonate="yes" FileKey="Path" ExeCommand="[LAUNCHAPPARGS]" Return="asyncNoWait" />

        <UI>
            <!-- launch app checkbox -->
            <Publish Dialog="ExitDialog" Control="Finish" Event="DoAction" Value="LaunchApplication">WIXUI_EXITDIALOGOPTIONALCHECKBOX = 1 and NOT Installed</Publish>

            <Property Id="WIXUI_INSTALLDIR" Value="INSTALLDIR" />

            <!-- Skip license dialog -->
            <Publish Dialog="WelcomeDlg"
                     Control="Next"
                     Event="NewDialog"
                     Value="InstallDirDlg"
                     Order="2">1</Publish>
            <Publish Dialog="InstallDirDlg"
                     Control="Back"
                     Event="NewDialog"
                     Value="WelcomeDlg"
                     Order="2">1</Publish>
        </UI>

        <UIRef Id="WixUI_InstallDir" />

        <Directory Id="TARGETDIR" Name="SourceDir">
            <Directory Id="DesktopFolder" Name="Desktop">
                <Component Id="ApplicationShortcutDesktop" Guid="*">
                    <Shortcut Id="ApplicationDesktopShortcut" Name="WeMa IA" Description="Runs WeMa IA" Target="[!Path]" WorkingDirectory="INSTALLDIR" />
                    <RemoveFolder Id="DesktopFolder" On="uninstall" />
                    <RegistryValue Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="Desktop Shortcut" Type="integer" Value="1" KeyPath="yes" />
                </Component>
            </Directory>
            <Directory Id="$(var.PlatformProgramFilesFolder)" Name="PFiles">
                <Directory Id="INSTALLDIR" Name="WeMa IA"/>
            </Directory>
            <Directory Id="ProgramMenuFolder">
                <Directory Id="ApplicationProgramsFolder" Name="WeMa IA"/>
            </Directory>
        </Directory>

        <DirectoryRef Id="INSTALLDIR">
            <Component Id="RegistryEntries" Guid="*">
                <RegistryKey Root="HKCU" Key="Software\wema-ia\WeMa IA">
                    <RegistryValue Name="InstallDir" Type="string" Value="[INSTALLDIR]" KeyPath="yes" />
                </RegistryKey>
                <!-- Change the Root to HKCU for perUser installations -->
</Component>
            <Component Id="Path" Guid="3b783432-6e5b-5f62-a8d8-17ab766c4f33" Win64="$(var.Win64)">
                <File Id="Path" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\app.exe" KeyPath="yes" Checksum="yes"/>
</Component>
            <Directory Id="Ic9c6224a6bde4c97aec419fb43426dee" Name="_up_"><Component Id="Iae77f41880134844a5e7ce7bdd7fd6a6" Guid="35edcb72-17ef-491f-8317-d8e533237248" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iae77f41880134844a5e7ce7bdd7fd6a6" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\start_backend.bat" /></Component><Directory Id="I43473b1ddf80465a8d13240c7d82c6e7" Name="py_backend"><Component Id="Ib9ba667f87ed4496a70e9fbf7473bad0" Guid="b6f65afc-2133-432d-a971-1971a2f8f19c" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ib9ba667f87ed4496a70e9fbf7473bad0" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\.env" /></Component><Component Id="I01ab8c1939704ff693a4edbf8da3796d" Guid="48eec538-2e4c-484b-9b7e-d1d853012ff4" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I01ab8c1939704ff693a4edbf8da3796d" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\.env.template" /></Component><Component Id="I0f7d54bf9d74422594cad411eacf1470" Guid="74dfa41a-4af0-44c7-820e-52566b37ddde" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I0f7d54bf9d74422594cad411eacf1470" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Dockerfile" /></Component><Component Id="I496a8062d1554132b4958fd751010ded" Guid="472c7ac7-a583-42c9-8f27-c20685a7e7f3" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I496a8062d1554132b4958fd751010ded" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Speech2Text.py" /></Component><Component Id="Iaf3d6c2991bf4c0eb047e690014a5f5f" Guid="3510e607-acb0-4d9b-b88c-c0298734f5c9" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iaf3d6c2991bf4c0eb047e690014a5f5f" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\TTS_README.md" /></Component><Component Id="I169f62a0e7cc4ec0aac4ad443bdb351b" Guid="34b903d9-9d96-448f-b94f-542c133c2efa" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I169f62a0e7cc4ec0aac4ad443bdb351b" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Text2Speech.py" /></Component><Component Id="I731fcd54a1b24fd7a275ab094cbf6619" Guid="5b250b49-8eb6-4297-84c0-e20343e4a495" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I731fcd54a1b24fd7a275ab094cbf6619" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\auth_service.py" /></Component><Component Id="I497b8a8698914289a22b5b44604e2c26" Guid="ea3c2d2e-8332-4a41-aebf-f767fd0000cc" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I497b8a8698914289a22b5b44604e2c26" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\compression_routes.py" /></Component><Component Id="I068c9b578cfb44838de83e521197fc74" Guid="d0145c40-c48b-4fcb-890b-f088745f560f" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I068c9b578cfb44838de83e521197fc74" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\diffusers_api.py" /></Component><Component Id="Iebfd7833140e4ee0959bb64e2f54f087" Guid="bd001483-b2e0-4d9b-b7a6-9cf911060301" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iebfd7833140e4ee0959bb64e2f54f087" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\documents.db" /></Component><Component Id="Ia2beae983ba849128969b25978c378ab" Guid="6892db81-a543-42e6-b634-8994b93c7cb3" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ia2beae983ba849128969b25978c378ab" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\install_kokoro.py" /></Component><Component Id="I6c119408bd2a488ab0e9f50c442ad81f" Guid="90440abd-e4cd-4a85-bccd-09eb0b0bd920" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I6c119408bd2a488ab0e9f50c442ad81f" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\langfuse_service.py" /></Component><Component Id="I4c8cfcdaea3b4eccb8b1737b3efffab8" Guid="b1c71e73-18dc-424e-8814-5aa57339b53c" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I4c8cfcdaea3b4eccb8b1737b3efffab8" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\main.py" /></Component><Component Id="Ibc90e6ea569f49f6b3ab2acf7b4d8d8d" Guid="0282a1e4-4542-4aec-938c-58ba78ccd3bb" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ibc90e6ea569f49f6b3ab2acf7b4d8d8d" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\network_config.py" /></Component><Component Id="I71790900155e445e82a9dc6dd1ef69e5" Guid="324de39c-f866-44e7-9c8e-1196cad869c6" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I71790900155e445e82a9dc6dd1ef69e5" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\ocr_processor_premium.py" /></Component><Component Id="I91015f6f675b4b7a8caeb29f9016e023" Guid="dd0dc1ab-52d7-483f-8b1b-c7e13149a300" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I91015f6f675b4b7a8caeb29f9016e023" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\ocr_service.py" /></Component><Component Id="I82e3065e60c04f64af75fbfb8645c7f0" Guid="17581954-1189-4f8e-8f3b-c9894eca8bbf" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I82e3065e60c04f64af75fbfb8645c7f0" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\perfect_compressor.py" /></Component><Component Id="Ifa261862c22940dfbd7aceccc84654a7" Guid="e0761aa4-3108-44a2-9d95-8d1ebe946bc6" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ifa261862c22940dfbd7aceccc84654a7" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\requirements.txt" /></Component><Component Id="Ic94a9d9119a1487db21f30ca5e6639b0" Guid="d631155b-f259-4bb5-9460-17211aa1badb" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ic94a9d9119a1487db21f30ca5e6639b0" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\start_server.py" /></Component><Component Id="Iaee7bca8d666451bb2dab8cd86575171" Guid="eeddedaf-79ff-4636-956e-30e46dd20feb" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iaee7bca8d666451bb2dab8cd86575171" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_compression_tool.py" /></Component><Component Id="Id6fb07d103e449449453348852d97461" Guid="578f6a04-643b-4cad-aa92-f37e3db5bd29" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Id6fb07d103e449449453348852d97461" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_non_streaming.py" /></Component><Component Id="I92d9ff74d7ce434cba0d008a7a08e2e0" Guid="8839f59b-3c7a-40d8-bc3a-596c9909859f" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I92d9ff74d7ce434cba0d008a7a08e2e0" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_optimizations.py" /></Component><Component Id="I6aebff3a722d4a829d829d058426e2af" Guid="80c18ea5-5f77-4acc-9b27-1d65348b83dc" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I6aebff3a722d4a829d829d058426e2af" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_streaming_optimized.py" /></Component><Directory Id="I200d96111cb3461ba4f0f72671df84a7" Name="__pycache__"><Component Id="I95b3759ac8f54babae6ed4306b17c889" Guid="b685eea0-a804-415a-ac8d-10566b720de1" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I95b3759ac8f54babae6ed4306b17c889" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\langfuse_service.cpython-312.pyc" /></Component><Component Id="I429790fa6d1f4c87a7ebc669d2690b89" Guid="20bc8033-ddc6-4c00-a7eb-ceffd8ab3f76" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I429790fa6d1f4c87a7ebc669d2690b89" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\main.cpython-312.pyc" /></Component><Component Id="Iad65a067fc8e4686b2456264b7b8f375" Guid="d2330657-4857-43a2-8c0b-7f1a185c1a39" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iad65a067fc8e4686b2456264b7b8f375" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\network_config.cpython-312.pyc" /></Component><Component Id="I8c7138b5e8f04a5597685e1c6e9d4390" Guid="31ad5a09-55cb-4f06-8f22-b41c81de0e6e" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I8c7138b5e8f04a5597685e1c6e9d4390" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\ocr_processor_premium.cpython-312.pyc" /></Component><Component Id="I31eb44b91c1d4df2af530cd1972bc7ba" Guid="338a2362-d6c6-4b64-b592-6b72d47683bb" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I31eb44b91c1d4df2af530cd1972bc7ba" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\ocr_service.cpython-312.pyc" /></Component><Component Id="Iaccc440490b74ec2929061f60966b841" Guid="2a5bfd1f-f98e-4e4c-b7c5-10998cb2ae5c" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iaccc440490b74ec2929061f60966b841" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\perfect_compressor.cpython-312.pyc" /></Component><Component Id="Ie2c748746186408fa290834622e01c1c" Guid="5cf6fb26-4f9c-4969-8dca-b1c2b511eda5" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ie2c748746186408fa290834622e01c1c" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\rag_premium_service.cpython-312.pyc" /></Component></Directory><Directory Id="I7669475e5f3b4951a0df97535840a089" Name="qdrant_storage"><Component Id="I2a08c7edbc124e8f86fbb8691d927040" Guid="91e72d1c-e0a1-4847-8ff0-e70214519ec8" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I2a08c7edbc124e8f86fbb8691d927040" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\qdrant_storage\.lock" /></Component><Component Id="I128fa146def64bc58a05b2db937367b6" Guid="ea2d45b9-8fe9-4c86-83c2-82cdc4d5b69a" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I128fa146def64bc58a05b2db937367b6" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\qdrant_storage\meta.json" /></Component></Directory><Directory Id="I8346bbf7f02546a9bd4253d3c1b17b83" Name="services"><Component Id="I27fd3961afee42ad891f1c46c1263872" Guid="52b2a87e-f9da-472f-a9dd-b6c0fdf4ab04" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I27fd3961afee42ad891f1c46c1263872" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\internet_search_service.py" /></Component><Component Id="I115eaebaee0240ab9f6aefd5e5745f2f" Guid="d99b31a5-5369-4c1b-867b-126aa166a12c" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I115eaebaee0240ab9f6aefd5e5745f2f" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\startup_service.py" /></Component><Component Id="I2fc7e824b4fa44c696011df91ab04603" Guid="d6b4168e-ed8a-4567-8401-c50ed5b45562" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I2fc7e824b4fa44c696011df91ab04603" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\web_scraper_service.py" /></Component><Directory Id="I51e8ead3518843049b3d378aa4e6d842" Name="__pycache__"><Component Id="Ica53dd414a21456a87fd04516a7b3b9b" Guid="a02bc989-4d70-43e6-ba04-0fa8f478d239" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ica53dd414a21456a87fd04516a7b3b9b" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\internet_search_service.cpython-312.pyc" /></Component><Component Id="I155cc20caf3e422990c655eddf0e07ad" Guid="52f6b9ec-0c7a-45e6-a2d3-c7823317cf32" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I155cc20caf3e422990c655eddf0e07ad" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\startup_service.cpython-312.pyc" /></Component><Component Id="I11b5fe79d1a943ca9cdc690c4709430a" Guid="a830203e-02cc-428d-93ea-36fe6ba5f9a6" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I11b5fe79d1a943ca9cdc690c4709430a" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\web_scraper_service.cpython-312.pyc" /></Component></Directory></Directory><Directory Id="Iac805f3eef464ba9895a2aa31237a8e4" Name="test_qdrant_temp"><Component Id="Icea27e46760847a781353d0254fdb995" Guid="ac5a5cd9-efba-4bc2-8f85-076d4c311c74" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Icea27e46760847a781353d0254fdb995" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_qdrant_temp\.lock" /></Component><Directory Id="I8a0ad956912640a0896a50aadc7adb84" Name="collection"><Directory Id="Ibf826510749d4c9d8916534cc2c3b43f" Name="test_collection"><Component Id="I780f318539d04fe5a0d031c9d01f5a2d" Guid="d11b4239-81fa-49b4-92a2-a269b66e8d09" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I780f318539d04fe5a0d031c9d01f5a2d" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_qdrant_temp\collection\test_collection\storage.sqlite" /></Component></Directory></Directory></Directory><Directory Id="I9e6699630b164a24b6daed87ea8dc955" Name="tools"><Component Id="I4b427fd453bf4e6bbaf0e860fc8c5796" Guid="b3ecce98-97b0-4051-8bf7-bfe4828743d6" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I4b427fd453bf4e6bbaf0e860fc8c5796" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\tools\internet_search_tools.py" /></Component><Directory Id="I9d16fb868d864417a38e585d6df7e317" Name="__pycache__"><Component Id="I62f4d28bb31146dca9e87e85490da9b3" Guid="a511e3cf-d6a2-44f7-bd64-4a9c3de2f165" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I62f4d28bb31146dca9e87e85490da9b3" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\tools\__pycache__\internet_search_tools.cpython-312.pyc" /></Component></Directory></Directory></Directory></Directory>
            <Component Id="CMP_UninstallShortcut" Guid="*">

                <Shortcut Id="UninstallShortcut"
						  Name="Uninstall WeMa IA"
						  Description="Uninstalls WeMa IA"
						  Target="[System64Folder]msiexec.exe"
						  Arguments="/x [ProductCode]" />

				<RemoveFolder Id="INSTALLDIR"
							  On="uninstall" />

				<RegistryValue Root="HKCU"
							   Key="Software\wema-ia\WeMa IA"
							   Name="Uninstaller Shortcut"
							   Type="integer"
							   Value="1"
							   KeyPath="yes" />
            </Component>
        </DirectoryRef>

        <DirectoryRef Id="ApplicationProgramsFolder">
            <Component Id="ApplicationShortcut" Guid="*">
                <Shortcut Id="ApplicationStartMenuShortcut"
                    Name="WeMa IA"
                    Description="Runs WeMa IA"
                    Target="[!Path]"
                    Icon="ProductIcon"
                    WorkingDirectory="INSTALLDIR">
                    <ShortcutProperty Key="System.AppUserModel.ID" Value="com.wema-ia.app"/>
                </Shortcut>
                <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall"/>
                <RegistryValue Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="Start Menu Shortcut" Type="integer" Value="1" KeyPath="yes"/>
           </Component>
        </DirectoryRef>

<Feature
                Id="MainProgram"
                Title="Application"
                Description="!(loc.InstallAppFeature)"
                Level="1"
                ConfigurableDirectory="INSTALLDIR"
                AllowAdvertise="no"
                Display="expand"
                Absent="disallow">

            <ComponentRef Id="RegistryEntries"/>

<ComponentRef Id="Iae77f41880134844a5e7ce7bdd7fd6a6"/>
<ComponentRef Id="Ib9ba667f87ed4496a70e9fbf7473bad0"/>
<ComponentRef Id="I01ab8c1939704ff693a4edbf8da3796d"/>
<ComponentRef Id="I0f7d54bf9d74422594cad411eacf1470"/>
<ComponentRef Id="I496a8062d1554132b4958fd751010ded"/>
<ComponentRef Id="Iaf3d6c2991bf4c0eb047e690014a5f5f"/>
<ComponentRef Id="I169f62a0e7cc4ec0aac4ad443bdb351b"/>
<ComponentRef Id="I731fcd54a1b24fd7a275ab094cbf6619"/>
<ComponentRef Id="I497b8a8698914289a22b5b44604e2c26"/>
<ComponentRef Id="I068c9b578cfb44838de83e521197fc74"/>
<ComponentRef Id="Iebfd7833140e4ee0959bb64e2f54f087"/>
<ComponentRef Id="Ia2beae983ba849128969b25978c378ab"/>
<ComponentRef Id="I6c119408bd2a488ab0e9f50c442ad81f"/>
<ComponentRef Id="I4c8cfcdaea3b4eccb8b1737b3efffab8"/>
<ComponentRef Id="Ibc90e6ea569f49f6b3ab2acf7b4d8d8d"/>
<ComponentRef Id="I71790900155e445e82a9dc6dd1ef69e5"/>
<ComponentRef Id="I91015f6f675b4b7a8caeb29f9016e023"/>
<ComponentRef Id="I82e3065e60c04f64af75fbfb8645c7f0"/>
<ComponentRef Id="Ifa261862c22940dfbd7aceccc84654a7"/>
<ComponentRef Id="Ic94a9d9119a1487db21f30ca5e6639b0"/>
<ComponentRef Id="Iaee7bca8d666451bb2dab8cd86575171"/>
<ComponentRef Id="Id6fb07d103e449449453348852d97461"/>
<ComponentRef Id="I92d9ff74d7ce434cba0d008a7a08e2e0"/>
<ComponentRef Id="I6aebff3a722d4a829d829d058426e2af"/>
<ComponentRef Id="I95b3759ac8f54babae6ed4306b17c889"/>
<ComponentRef Id="I429790fa6d1f4c87a7ebc669d2690b89"/>
<ComponentRef Id="Iad65a067fc8e4686b2456264b7b8f375"/>
<ComponentRef Id="I8c7138b5e8f04a5597685e1c6e9d4390"/>
<ComponentRef Id="I31eb44b91c1d4df2af530cd1972bc7ba"/>
<ComponentRef Id="Iaccc440490b74ec2929061f60966b841"/>
<ComponentRef Id="Ie2c748746186408fa290834622e01c1c"/>
<ComponentRef Id="I2a08c7edbc124e8f86fbb8691d927040"/>
<ComponentRef Id="I128fa146def64bc58a05b2db937367b6"/>
<ComponentRef Id="I27fd3961afee42ad891f1c46c1263872"/>
<ComponentRef Id="I115eaebaee0240ab9f6aefd5e5745f2f"/>
<ComponentRef Id="I2fc7e824b4fa44c696011df91ab04603"/>
<ComponentRef Id="Ica53dd414a21456a87fd04516a7b3b9b"/>
<ComponentRef Id="I155cc20caf3e422990c655eddf0e07ad"/>
<ComponentRef Id="I11b5fe79d1a943ca9cdc690c4709430a"/>
<ComponentRef Id="Icea27e46760847a781353d0254fdb995"/>
<ComponentRef Id="I780f318539d04fe5a0d031c9d01f5a2d"/>
<ComponentRef Id="I4b427fd453bf4e6bbaf0e860fc8c5796"/>
<ComponentRef Id="I62f4d28bb31146dca9e87e85490da9b3"/>

            <Feature Id="ShortcutsFeature"
                Title="Shortcuts"
                Level="1">
                <ComponentRef Id="Path"/>
                <ComponentRef Id="CMP_UninstallShortcut" />
                <ComponentRef Id="ApplicationShortcut" />
                <ComponentRef Id="ApplicationShortcutDesktop" />
            </Feature>

            <Feature
                Id="Environment"
                Title="PATH Environment Variable"
                Description="!(loc.PathEnvVarFeature)"
                Level="1"
                Absent="allow">
            <ComponentRef Id="Path"/>
</Feature>
        </Feature>

        <Feature Id="External" AllowAdvertise="no" Absent="disallow">
</Feature>

        <!-- WebView2 -->
        <Property Id="WVRTINSTALLED">
            <RegistrySearch Id="WVRTInstalledSystem" Root="HKLM" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw" Win64="no" />
            <RegistrySearch Id="WVRTInstalledUser" Root="HKCU" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw"/>
        </Property>

        <CustomAction Id='DownloadAndInvokeBootstrapper' Directory="INSTALLDIR" Execute="deferred" ExeCommand='powershell.exe -NoProfile -windowstyle hidden try [\{] [\[]Net.ServicePointManager[\]]::SecurityProtocol = [\[]Net.SecurityProtocolType[\]]::Tls12 [\}] catch [\{][\}]; Invoke-WebRequest -Uri "https://go.microsoft.com/fwlink/p/?LinkId=2124703" -OutFile "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" ; Start-Process -FilePath "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" -ArgumentList (&apos;/silent&apos;, &apos;/install&apos;) -Wait' Return='check'/>
        <InstallExecuteSequence>
            <Custom Action='DownloadAndInvokeBootstrapper' Before='InstallFinalize'>
                <![CDATA[NOT(REMOVE OR WVRTINSTALLED)]]>
            </Custom>
        </InstallExecuteSequence>

        <!-- Embedded webview bootstrapper mode -->

        <!-- Embedded offline installer -->



        <InstallExecuteSequence>
          <Custom Action="LaunchApplication" After="InstallFinalize">AUTOLAUNCHAPP AND NOT Installed</Custom>
        </InstallExecuteSequence>

        <SetProperty Id="ARPINSTALLLOCATION" Value="[INSTALLDIR]" After="CostFinalize"/>
    </Product>
</Wix>
