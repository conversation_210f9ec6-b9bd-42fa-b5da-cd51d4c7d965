<?if $(sys.B<PERSON><PERSON>AR<PERSON>)="x86"?>
    <?define Win64 = "no" ?>
    <?define PlatformProgramFilesFolder = "ProgramFilesFolder" ?>
<?elseif $(sys.BUILDARCH)="x64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?elseif $(sys.BUILDARCH)="arm64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?else?>
    <?error Unsupported value of sys.BUILDARCH=$(sys.BUILDARCH)?>
<?endif?>

<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Product
            Id="*"
            Name="WeMa IA"
            UpgradeCode="7adcaf1a-334a-54b2-baee-a32f3344ceea"
            Language="!(loc.TauriLanguage)"
            Manufacturer="wema-ia"
            Version="0.1.2">

        <Package Id="*"
                 Keywords="Installer"
                 InstallerVersion="450"
                 Languages="0"
                 Compressed="yes"
                 InstallScope="perMachine"
                 SummaryCodepage="!(loc.TauriCodepage)"/>

        <!-- https://docs.microsoft.com/en-us/windows/win32/msi/reinstallmode -->
        <!-- reinstall all files; rewrite all registry entries; reinstall all shortcuts -->
        <Property Id="REINSTALLMODE" Value="amus" />

        <!-- Auto launch app after installation, useful for passive mode which usually used in updates -->
        <Property Id="AUTOLAUNCHAPP" Secure="yes" />
        <!-- Property to forward cli args to the launched app to not lose those of the pre-update instance -->
        <Property Id="LAUNCHAPPARGS" Secure="yes" />

            <MajorUpgrade Schedule="afterInstallInitialize" AllowDowngrades="yes" />

        <InstallExecuteSequence>
            <RemoveShortcuts>Installed AND NOT UPGRADINGPRODUCTCODE</RemoveShortcuts>
        </InstallExecuteSequence>

        <Media Id="1" Cabinet="app.cab" EmbedCab="yes" />


        <Icon Id="ProductIcon" SourceFile="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\resources\icon.ico"/>
        <Property Id="ARPPRODUCTICON" Value="ProductIcon" />
        <Property Id="ARPNOREPAIR" Value="yes" Secure="yes" />      <!-- Remove repair -->
        <SetProperty Id="ARPNOMODIFY" Value="1" After="InstallValidate" Sequence="execute"/>


        <Property Id="INSTALLDIR">
          <!-- First attempt: Search for "InstallDir" -->
          <RegistrySearch Id="PrevInstallDirWithName" Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="InstallDir" Type="raw" />

          <!-- Second attempt: If the first fails, search for the default key value (this is how the nsis installer currently stores the path) -->
          <RegistrySearch Id="PrevInstallDirNoName" Root="HKCU" Key="Software\wema-ia\WeMa IA" Type="raw" />
        </Property>

        <!-- launch app checkbox -->
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOXTEXT" Value="!(loc.LaunchApp)" />
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOX" Value="1"/>
        <CustomAction Id="LaunchApplication" Impersonate="yes" FileKey="Path" ExeCommand="[LAUNCHAPPARGS]" Return="asyncNoWait" />

        <UI>
            <!-- launch app checkbox -->
            <Publish Dialog="ExitDialog" Control="Finish" Event="DoAction" Value="LaunchApplication">WIXUI_EXITDIALOGOPTIONALCHECKBOX = 1 and NOT Installed</Publish>

            <Property Id="WIXUI_INSTALLDIR" Value="INSTALLDIR" />

            <!-- Skip license dialog -->
            <Publish Dialog="WelcomeDlg"
                     Control="Next"
                     Event="NewDialog"
                     Value="InstallDirDlg"
                     Order="2">1</Publish>
            <Publish Dialog="InstallDirDlg"
                     Control="Back"
                     Event="NewDialog"
                     Value="WelcomeDlg"
                     Order="2">1</Publish>
        </UI>

        <UIRef Id="WixUI_InstallDir" />

        <Directory Id="TARGETDIR" Name="SourceDir">
            <Directory Id="DesktopFolder" Name="Desktop">
                <Component Id="ApplicationShortcutDesktop" Guid="*">
                    <Shortcut Id="ApplicationDesktopShortcut" Name="WeMa IA" Description="Runs WeMa IA" Target="[!Path]" WorkingDirectory="INSTALLDIR" />
                    <RemoveFolder Id="DesktopFolder" On="uninstall" />
                    <RegistryValue Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="Desktop Shortcut" Type="integer" Value="1" KeyPath="yes" />
                </Component>
            </Directory>
            <Directory Id="$(var.PlatformProgramFilesFolder)" Name="PFiles">
                <Directory Id="INSTALLDIR" Name="WeMa IA"/>
            </Directory>
            <Directory Id="ProgramMenuFolder">
                <Directory Id="ApplicationProgramsFolder" Name="WeMa IA"/>
            </Directory>
        </Directory>

        <DirectoryRef Id="INSTALLDIR">
            <Component Id="RegistryEntries" Guid="*">
                <RegistryKey Root="HKCU" Key="Software\wema-ia\WeMa IA">
                    <RegistryValue Name="InstallDir" Type="string" Value="[INSTALLDIR]" KeyPath="yes" />
                </RegistryKey>
                <!-- Change the Root to HKCU for perUser installations -->
</Component>
            <Component Id="Path" Guid="3b783432-6e5b-5f62-a8d8-17ab766c4f33" Win64="$(var.Win64)">
                <File Id="Path" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\app.exe" KeyPath="yes" Checksum="yes"/>
</Component>
            <Directory Id="Ib90f62a6bcca45ed8c92f239e96a904d" Name="_up_"><Directory Id="I29cf94ec856748abb00e7a4fe752a978" Name="py_backend"><Component Id="I65126abc8c4d4b4eac735e62414cc7a2" Guid="b1f6a6ac-eb3b-410b-b4b1-29f8ff110172" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I65126abc8c4d4b4eac735e62414cc7a2" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\.env" /></Component><Component Id="I588f2ea6bbb9418990b7190e7d169dbe" Guid="74b90afd-b6be-45a1-9e82-6585f52a1c33" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I588f2ea6bbb9418990b7190e7d169dbe" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\.env.template" /></Component><Component Id="I728ef6f70b324c03957200caf642306d" Guid="44938356-11db-4621-bc8e-6cc19cd448fe" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I728ef6f70b324c03957200caf642306d" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Dockerfile" /></Component><Component Id="I5aa966c39e2e413bb20d4e3ea24b1d12" Guid="0e3171fa-**************-1dd311a04152" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I5aa966c39e2e413bb20d4e3ea24b1d12" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Speech2Text.py" /></Component><Component Id="Ica8629448d9345b6a8d5be2892b460c2" Guid="5a3ea22d-de4b-44f5-821b-673c969c9fd6" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ica8629448d9345b6a8d5be2892b460c2" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\TTS_README.md" /></Component><Component Id="Ife00bf255752426babff47d12b4a7c11" Guid="4e45e4be-41bb-4dc4-a003-fc1246b762e2" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ife00bf255752426babff47d12b4a7c11" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Text2Speech.py" /></Component><Component Id="Icf0875221cf349daaf28ac0298b9d41e" Guid="412f2428-d80e-45e4-816c-9bd81696181c" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Icf0875221cf349daaf28ac0298b9d41e" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\auth_service.py" /></Component><Component Id="If2c9a77066654adfa3af751840f0fa49" Guid="b4d59700-4745-4864-83f6-27816204a824" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_If2c9a77066654adfa3af751840f0fa49" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\clara_backend.db" /></Component><Component Id="Id87241739c67468e90db12310527983d" Guid="3ff488ae-1a48-4f0b-8278-ea6dacd1d671" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Id87241739c67468e90db12310527983d" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\compression_routes.py" /></Component><Component Id="Ib96985b0682d411396f27fddfac2edc7" Guid="efae7b86-d21a-456a-b68a-0cc9bfe5eecb" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ib96985b0682d411396f27fddfac2edc7" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\diffusers_api.py" /></Component><Component Id="I92f370a1e6d0491289af016d9368b999" Guid="ecafe977-3ce0-4af1-8c7d-aebd99714919" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I92f370a1e6d0491289af016d9368b999" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\documents.db" /></Component><Component Id="Ica4a8e9c98f04721b1d17271ac03259f" Guid="eb4a1db9-0edc-4ec5-aa63-0993d535b6ba" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ica4a8e9c98f04721b1d17271ac03259f" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\install_kokoro.py" /></Component><Component Id="Id12e807f86f2438da9b5000507e8b320" Guid="abcf2543-0fbd-4c29-95bd-448d6f46f04f" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Id12e807f86f2438da9b5000507e8b320" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\langfuse_service.py" /></Component><Component Id="Ia5f0284c77af45188feed3f52acf3d52" Guid="8b5bfb82-b1cc-45d0-95ef-4d7131ea391d" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ia5f0284c77af45188feed3f52acf3d52" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\main.py" /></Component><Component Id="If7ca340da69844b296b05a262104d2f6" Guid="8304d300-60ae-4084-a027-d0412ccbdcf6" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_If7ca340da69844b296b05a262104d2f6" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\network_config.py" /></Component><Component Id="I24fc5afc305a4f739579f2346896a59f" Guid="bb943608-2b70-4726-ae38-a139a8be94fa" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I24fc5afc305a4f739579f2346896a59f" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\ocr_processor_premium.py" /></Component><Component Id="Iedf84e01eb2442aab796380b0b65818f" Guid="55a465a2-1ad4-47d9-8fa0-de482ce47904" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iedf84e01eb2442aab796380b0b65818f" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\ocr_service.py" /></Component><Component Id="I416e0546672f4eb5940153b4c3631be8" Guid="03ea82d4-7d29-406f-9d65-e6ace4a63344" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I416e0546672f4eb5940153b4c3631be8" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\perfect_compressor.py" /></Component><Component Id="Id2f24f1c77fe4755a8d13413c1797f22" Guid="3d5931e7-a8f4-428d-a675-c2374dc008a1" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Id2f24f1c77fe4755a8d13413c1797f22" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\requirements.txt" /></Component><Component Id="Ic13c7ea8fd2c4978853dac19a4be2aa8" Guid="3ba713d3-0142-45a5-8257-bf93f2d1f376" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ic13c7ea8fd2c4978853dac19a4be2aa8" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\start_server.py" /></Component><Component Id="If20c8f69f99241ad912663c82a8428a5" Guid="083a3263-e518-4e04-99ba-eb39ca460046" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_If20c8f69f99241ad912663c82a8428a5" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_compression_tool.py" /></Component><Component Id="I0710414835d34e73be47c544eef31752" Guid="56071bb2-0418-4c5a-85b7-6a8a58cca098" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I0710414835d34e73be47c544eef31752" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_non_streaming.py" /></Component><Component Id="Id2ee83cd26874f40a57a533574c1f873" Guid="ea479bf3-d721-4ba3-9e00-d5726fd2d0c0" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Id2ee83cd26874f40a57a533574c1f873" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_optimizations.py" /></Component><Component Id="I922344cb441040a29c4cd609a0c85c43" Guid="76ac1129-b716-42b7-ae2d-587506ac8b8e" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I922344cb441040a29c4cd609a0c85c43" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_streaming_optimized.py" /></Component><Directory Id="I4a7769ff222b4105a6bb541ceb853a55" Name="__pycache__"><Component Id="Iece3cf8dcfea44109030688426b2c06e" Guid="b604f533-ddd2-4c52-83da-3756961fe6ab" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iece3cf8dcfea44109030688426b2c06e" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\langfuse_service.cpython-312.pyc" /></Component><Component Id="I91fe15039bf94fc6912ee8b50a86c142" Guid="2a328628-a448-4e8e-a569-2b42b1544942" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I91fe15039bf94fc6912ee8b50a86c142" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\main.cpython-312.pyc" /></Component><Component Id="Id060f04a74074ad489d454a17bf279b1" Guid="d0f2e20d-d860-4580-b450-5ffe341c24cf" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Id060f04a74074ad489d454a17bf279b1" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\network_config.cpython-312.pyc" /></Component><Component Id="Ib0d900029e5f41ee8218ace0eff65d31" Guid="0e266433-8086-49bd-871c-c21457dcc7f7" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ib0d900029e5f41ee8218ace0eff65d31" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\ocr_processor_premium.cpython-312.pyc" /></Component><Component Id="Ic343739c86784aa599c6083c98cf5d40" Guid="113c6131-a177-48a3-98bc-5e7ce8ce0fd1" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ic343739c86784aa599c6083c98cf5d40" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\ocr_service.cpython-312.pyc" /></Component><Component Id="Iaf0bb1cb429548ddaf4136be1b93c7fd" Guid="b8364de6-cf2d-4458-8faf-187cd731c039" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iaf0bb1cb429548ddaf4136be1b93c7fd" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\perfect_compressor.cpython-312.pyc" /></Component><Component Id="Ia4b7139fb07d4b7e8cbb6ec3e0624b44" Guid="38a890da-bb69-45c7-a7f2-e4e508f83176" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ia4b7139fb07d4b7e8cbb6ec3e0624b44" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\rag_premium_service.cpython-312.pyc" /></Component></Directory><Directory Id="I97032ac67c2a4c4e952acdd9d4bc9b2a" Name="qdrant_storage"><Component Id="Id7e63a85c103475e9b4cf1fd9c04f514" Guid="94b83dc9-96d3-4a68-8140-215ab23afcbf" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Id7e63a85c103475e9b4cf1fd9c04f514" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\qdrant_storage\.lock" /></Component><Component Id="If313781aee43401cbb32cee78a3385cb" Guid="6c112543-0f43-4a9a-8ea3-07f9d93d79d8" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_If313781aee43401cbb32cee78a3385cb" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\qdrant_storage\meta.json" /></Component></Directory><Directory Id="I9d5907ecdd444bed9c71857193ceae4a" Name="services"><Component Id="I5d0ee10e924146198f0d70fde595a155" Guid="987145a7-21eb-4d1f-b2ce-ba396059f036" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I5d0ee10e924146198f0d70fde595a155" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\internet_search_service.py" /></Component><Component Id="I3041877c04e54528b4ba9ca9ed2728a4" Guid="d86c98b9-acd5-4d3c-b1f5-37b6ffaf0d22" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I3041877c04e54528b4ba9ca9ed2728a4" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\startup_service.py" /></Component><Component Id="Id8841d64897b4af6984f442d7babefba" Guid="b324736c-da0f-4c63-8d8b-97c6e7bf3909" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Id8841d64897b4af6984f442d7babefba" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\web_scraper_service.py" /></Component><Directory Id="I206193825b154f1a9df7ad6be86c0909" Name="__pycache__"><Component Id="I0ab241a3879346fabc7d47a967a2e734" Guid="b57576dd-a9b6-4119-ba90-38654bd57769" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I0ab241a3879346fabc7d47a967a2e734" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\internet_search_service.cpython-312.pyc" /></Component><Component Id="I51cb660e37af40c3a04a45dea5879ace" Guid="85269863-01fc-40d4-af00-5fb16aaad30c" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I51cb660e37af40c3a04a45dea5879ace" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\startup_service.cpython-312.pyc" /></Component><Component Id="I249d3812b35c4610855ed4fbead1e531" Guid="1ce78771-36c9-4e4e-8c73-187d030911ba" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I249d3812b35c4610855ed4fbead1e531" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\web_scraper_service.cpython-312.pyc" /></Component></Directory></Directory><Directory Id="I9339a9d24ba14c23b8b4805db75cb0da" Name="test_qdrant_temp"><Component Id="Ia19c7d95476445008bc50e56afdc9ce4" Guid="188fda5a-48cf-43fc-9357-13c8c528d49f" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ia19c7d95476445008bc50e56afdc9ce4" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_qdrant_temp\.lock" /></Component><Directory Id="I6d92b48681354f6a85c45d08e60bce87" Name="collection"><Directory Id="I846bccef02624dc7a0731eff026379df" Name="test_collection"><Component Id="I8a95265b023447008a403bf9c10e7a18" Guid="ece6e241-2028-41e8-a84b-ba67bc28f69d" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I8a95265b023447008a403bf9c10e7a18" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_qdrant_temp\collection\test_collection\storage.sqlite" /></Component></Directory></Directory></Directory><Directory Id="I6f1a925b0f4c415ca39f432f47fde56e" Name="tools"><Component Id="I5da404b34c954416a55d92983974dbcd" Guid="032322ee-f9a6-460e-bc1c-6987f86ec9db" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I5da404b34c954416a55d92983974dbcd" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\tools\internet_search_tools.py" /></Component><Directory Id="I0ad10fb40fa243ada2e47631b7919731" Name="__pycache__"><Component Id="Ib4db3bee22b64ca684ef3d05d46b53c6" Guid="520a5b14-01a9-44e3-a942-8f07c614a39f" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ib4db3bee22b64ca684ef3d05d46b53c6" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\tools\__pycache__\internet_search_tools.cpython-312.pyc" /></Component></Directory></Directory></Directory></Directory><Directory Id="I54b5c4cc387b44dc8e8a5f9455e7bfaf" Name="python-embed"><Component Id="Ie75dc965d4944602846a5aaee99dc4ee" Guid="41e93b3c-710b-4d62-bdef-6cb630d8a03c" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ie75dc965d4944602846a5aaee99dc4ee" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\python-embed\python.zip" /></Component></Directory>
            <Component Id="CMP_UninstallShortcut" Guid="*">

                <Shortcut Id="UninstallShortcut"
						  Name="Uninstall WeMa IA"
						  Description="Uninstalls WeMa IA"
						  Target="[System64Folder]msiexec.exe"
						  Arguments="/x [ProductCode]" />

				<RemoveFolder Id="INSTALLDIR"
							  On="uninstall" />

				<RegistryValue Root="HKCU"
							   Key="Software\wema-ia\WeMa IA"
							   Name="Uninstaller Shortcut"
							   Type="integer"
							   Value="1"
							   KeyPath="yes" />
            </Component>
        </DirectoryRef>

        <DirectoryRef Id="ApplicationProgramsFolder">
            <Component Id="ApplicationShortcut" Guid="*">
                <Shortcut Id="ApplicationStartMenuShortcut"
                    Name="WeMa IA"
                    Description="Runs WeMa IA"
                    Target="[!Path]"
                    Icon="ProductIcon"
                    WorkingDirectory="INSTALLDIR">
                    <ShortcutProperty Key="System.AppUserModel.ID" Value="com.wema-ia.app"/>
                </Shortcut>
                <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall"/>
                <RegistryValue Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="Start Menu Shortcut" Type="integer" Value="1" KeyPath="yes"/>
           </Component>
        </DirectoryRef>

<Feature
                Id="MainProgram"
                Title="Application"
                Description="!(loc.InstallAppFeature)"
                Level="1"
                ConfigurableDirectory="INSTALLDIR"
                AllowAdvertise="no"
                Display="expand"
                Absent="disallow">

            <ComponentRef Id="RegistryEntries"/>

<ComponentRef Id="I65126abc8c4d4b4eac735e62414cc7a2"/>
<ComponentRef Id="I588f2ea6bbb9418990b7190e7d169dbe"/>
<ComponentRef Id="I728ef6f70b324c03957200caf642306d"/>
<ComponentRef Id="I5aa966c39e2e413bb20d4e3ea24b1d12"/>
<ComponentRef Id="Ica8629448d9345b6a8d5be2892b460c2"/>
<ComponentRef Id="Ife00bf255752426babff47d12b4a7c11"/>
<ComponentRef Id="Icf0875221cf349daaf28ac0298b9d41e"/>
<ComponentRef Id="If2c9a77066654adfa3af751840f0fa49"/>
<ComponentRef Id="Id87241739c67468e90db12310527983d"/>
<ComponentRef Id="Ib96985b0682d411396f27fddfac2edc7"/>
<ComponentRef Id="I92f370a1e6d0491289af016d9368b999"/>
<ComponentRef Id="Ica4a8e9c98f04721b1d17271ac03259f"/>
<ComponentRef Id="Id12e807f86f2438da9b5000507e8b320"/>
<ComponentRef Id="Ia5f0284c77af45188feed3f52acf3d52"/>
<ComponentRef Id="If7ca340da69844b296b05a262104d2f6"/>
<ComponentRef Id="I24fc5afc305a4f739579f2346896a59f"/>
<ComponentRef Id="Iedf84e01eb2442aab796380b0b65818f"/>
<ComponentRef Id="I416e0546672f4eb5940153b4c3631be8"/>
<ComponentRef Id="Id2f24f1c77fe4755a8d13413c1797f22"/>
<ComponentRef Id="Ic13c7ea8fd2c4978853dac19a4be2aa8"/>
<ComponentRef Id="If20c8f69f99241ad912663c82a8428a5"/>
<ComponentRef Id="I0710414835d34e73be47c544eef31752"/>
<ComponentRef Id="Id2ee83cd26874f40a57a533574c1f873"/>
<ComponentRef Id="I922344cb441040a29c4cd609a0c85c43"/>
<ComponentRef Id="Iece3cf8dcfea44109030688426b2c06e"/>
<ComponentRef Id="I91fe15039bf94fc6912ee8b50a86c142"/>
<ComponentRef Id="Id060f04a74074ad489d454a17bf279b1"/>
<ComponentRef Id="Ib0d900029e5f41ee8218ace0eff65d31"/>
<ComponentRef Id="Ic343739c86784aa599c6083c98cf5d40"/>
<ComponentRef Id="Iaf0bb1cb429548ddaf4136be1b93c7fd"/>
<ComponentRef Id="Ia4b7139fb07d4b7e8cbb6ec3e0624b44"/>
<ComponentRef Id="Id7e63a85c103475e9b4cf1fd9c04f514"/>
<ComponentRef Id="If313781aee43401cbb32cee78a3385cb"/>
<ComponentRef Id="I5d0ee10e924146198f0d70fde595a155"/>
<ComponentRef Id="I3041877c04e54528b4ba9ca9ed2728a4"/>
<ComponentRef Id="Id8841d64897b4af6984f442d7babefba"/>
<ComponentRef Id="I0ab241a3879346fabc7d47a967a2e734"/>
<ComponentRef Id="I51cb660e37af40c3a04a45dea5879ace"/>
<ComponentRef Id="I249d3812b35c4610855ed4fbead1e531"/>
<ComponentRef Id="Ia19c7d95476445008bc50e56afdc9ce4"/>
<ComponentRef Id="I8a95265b023447008a403bf9c10e7a18"/>
<ComponentRef Id="I5da404b34c954416a55d92983974dbcd"/>
<ComponentRef Id="Ib4db3bee22b64ca684ef3d05d46b53c6"/>
<ComponentRef Id="Ie75dc965d4944602846a5aaee99dc4ee"/>

            <Feature Id="ShortcutsFeature"
                Title="Shortcuts"
                Level="1">
                <ComponentRef Id="Path"/>
                <ComponentRef Id="CMP_UninstallShortcut" />
                <ComponentRef Id="ApplicationShortcut" />
                <ComponentRef Id="ApplicationShortcutDesktop" />
            </Feature>

            <Feature
                Id="Environment"
                Title="PATH Environment Variable"
                Description="!(loc.PathEnvVarFeature)"
                Level="1"
                Absent="allow">
            <ComponentRef Id="Path"/>
</Feature>
        </Feature>

        <Feature Id="External" AllowAdvertise="no" Absent="disallow">
</Feature>

        <!-- WebView2 -->
        <Property Id="WVRTINSTALLED">
            <RegistrySearch Id="WVRTInstalledSystem" Root="HKLM" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw" Win64="no" />
            <RegistrySearch Id="WVRTInstalledUser" Root="HKCU" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw"/>
        </Property>

        <CustomAction Id='DownloadAndInvokeBootstrapper' Directory="INSTALLDIR" Execute="deferred" ExeCommand='powershell.exe -NoProfile -windowstyle hidden try [\{] [\[]Net.ServicePointManager[\]]::SecurityProtocol = [\[]Net.SecurityProtocolType[\]]::Tls12 [\}] catch [\{][\}]; Invoke-WebRequest -Uri "https://go.microsoft.com/fwlink/p/?LinkId=2124703" -OutFile "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" ; Start-Process -FilePath "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" -ArgumentList (&apos;/silent&apos;, &apos;/install&apos;) -Wait' Return='check'/>
        <InstallExecuteSequence>
            <Custom Action='DownloadAndInvokeBootstrapper' Before='InstallFinalize'>
                <![CDATA[NOT(REMOVE OR WVRTINSTALLED)]]>
            </Custom>
        </InstallExecuteSequence>

        <!-- Embedded webview bootstrapper mode -->

        <!-- Embedded offline installer -->



        <InstallExecuteSequence>
          <Custom Action="LaunchApplication" After="InstallFinalize">AUTOLAUNCHAPP AND NOT Installed</Custom>
        </InstallExecuteSequence>

        <SetProperty Id="ARPINSTALLLOCATION" Value="[INSTALLDIR]" After="CostFinalize"/>
    </Product>
</Wix>
