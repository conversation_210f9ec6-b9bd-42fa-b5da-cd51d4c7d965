<?if $(sys.B<PERSON><PERSON>AR<PERSON>)="x86"?>
    <?define Win64 = "no" ?>
    <?define PlatformProgramFilesFolder = "ProgramFilesFolder" ?>
<?elseif $(sys.BUILDARCH)="x64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?elseif $(sys.BUILDARCH)="arm64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?else?>
    <?error Unsupported value of sys.BUILDARCH=$(sys.BUILDARCH)?>
<?endif?>

<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Product
            Id="*"
            Name="WeMa IA"
            UpgradeCode="7adcaf1a-334a-54b2-baee-a32f3344ceea"
            Language="!(loc.TauriLanguage)"
            Manufacturer="wema-ia"
            Version="0.1.2">

        <Package Id="*"
                 Keywords="Installer"
                 InstallerVersion="450"
                 Languages="0"
                 Compressed="yes"
                 InstallScope="perMachine"
                 SummaryCodepage="!(loc.TauriCodepage)"/>

        <!-- https://docs.microsoft.com/en-us/windows/win32/msi/reinstallmode -->
        <!-- reinstall all files; rewrite all registry entries; reinstall all shortcuts -->
        <Property Id="REINSTALLMODE" Value="amus" />

        <!-- Auto launch app after installation, useful for passive mode which usually used in updates -->
        <Property Id="AUTOLAUNCHAPP" Secure="yes" />
        <!-- Property to forward cli args to the launched app to not lose those of the pre-update instance -->
        <Property Id="LAUNCHAPPARGS" Secure="yes" />

            <MajorUpgrade Schedule="afterInstallInitialize" AllowDowngrades="yes" />

        <InstallExecuteSequence>
            <RemoveShortcuts>Installed AND NOT UPGRADINGPRODUCTCODE</RemoveShortcuts>
        </InstallExecuteSequence>

        <Media Id="1" Cabinet="app.cab" EmbedCab="yes" />


        <Icon Id="ProductIcon" SourceFile="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\resources\icon.ico"/>
        <Property Id="ARPPRODUCTICON" Value="ProductIcon" />
        <Property Id="ARPNOREPAIR" Value="yes" Secure="yes" />      <!-- Remove repair -->
        <SetProperty Id="ARPNOMODIFY" Value="1" After="InstallValidate" Sequence="execute"/>


        <Property Id="INSTALLDIR">
          <!-- First attempt: Search for "InstallDir" -->
          <RegistrySearch Id="PrevInstallDirWithName" Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="InstallDir" Type="raw" />

          <!-- Second attempt: If the first fails, search for the default key value (this is how the nsis installer currently stores the path) -->
          <RegistrySearch Id="PrevInstallDirNoName" Root="HKCU" Key="Software\wema-ia\WeMa IA" Type="raw" />
        </Property>

        <!-- launch app checkbox -->
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOXTEXT" Value="!(loc.LaunchApp)" />
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOX" Value="1"/>
        <CustomAction Id="LaunchApplication" Impersonate="yes" FileKey="Path" ExeCommand="[LAUNCHAPPARGS]" Return="asyncNoWait" />

        <UI>
            <!-- launch app checkbox -->
            <Publish Dialog="ExitDialog" Control="Finish" Event="DoAction" Value="LaunchApplication">WIXUI_EXITDIALOGOPTIONALCHECKBOX = 1 and NOT Installed</Publish>

            <Property Id="WIXUI_INSTALLDIR" Value="INSTALLDIR" />

            <!-- Skip license dialog -->
            <Publish Dialog="WelcomeDlg"
                     Control="Next"
                     Event="NewDialog"
                     Value="InstallDirDlg"
                     Order="2">1</Publish>
            <Publish Dialog="InstallDirDlg"
                     Control="Back"
                     Event="NewDialog"
                     Value="WelcomeDlg"
                     Order="2">1</Publish>
        </UI>

        <UIRef Id="WixUI_InstallDir" />

        <Directory Id="TARGETDIR" Name="SourceDir">
            <Directory Id="DesktopFolder" Name="Desktop">
                <Component Id="ApplicationShortcutDesktop" Guid="*">
                    <Shortcut Id="ApplicationDesktopShortcut" Name="WeMa IA" Description="Runs WeMa IA" Target="[!Path]" WorkingDirectory="INSTALLDIR" />
                    <RemoveFolder Id="DesktopFolder" On="uninstall" />
                    <RegistryValue Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="Desktop Shortcut" Type="integer" Value="1" KeyPath="yes" />
                </Component>
            </Directory>
            <Directory Id="$(var.PlatformProgramFilesFolder)" Name="PFiles">
                <Directory Id="INSTALLDIR" Name="WeMa IA"/>
            </Directory>
            <Directory Id="ProgramMenuFolder">
                <Directory Id="ApplicationProgramsFolder" Name="WeMa IA"/>
            </Directory>
        </Directory>

        <DirectoryRef Id="INSTALLDIR">
            <Component Id="RegistryEntries" Guid="*">
                <RegistryKey Root="HKCU" Key="Software\wema-ia\WeMa IA">
                    <RegistryValue Name="InstallDir" Type="string" Value="[INSTALLDIR]" KeyPath="yes" />
                </RegistryKey>
                <!-- Change the Root to HKCU for perUser installations -->
</Component>
            <Component Id="Path" Guid="3b783432-6e5b-5f62-a8d8-17ab766c4f33" Win64="$(var.Win64)">
                <File Id="Path" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\app.exe" KeyPath="yes" Checksum="yes"/>
</Component>
            <Directory Id="Ied5f2d2b5c6b41238c4e980f02a26494" Name="_up_"><Directory Id="I6d4268c962b940809a42e2a6e248be58" Name="py_backend"><Component Id="I002820c1d223405ea2eca1a8b1ff14be" Guid="0b6ec047-8d61-43a4-acac-d7fca0dc7afe" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I002820c1d223405ea2eca1a8b1ff14be" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\.env" /></Component><Component Id="I8bfbdb362caa4c9483b7ff9ad865d1e0" Guid="e02f3848-9ce2-42ce-a7de-92512c66dc90" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I8bfbdb362caa4c9483b7ff9ad865d1e0" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\.env.template" /></Component><Component Id="Iba3afd4fc4ad4c0eadef8e2589493223" Guid="6c857209-8b85-46c0-b863-7d0fa9bd9c25" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iba3afd4fc4ad4c0eadef8e2589493223" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Dockerfile" /></Component><Component Id="Ia1f55808ef1d4598b4e29d2a902f8f7e" Guid="71121823-16e1-4d4b-991b-4c3ca4f77d92" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ia1f55808ef1d4598b4e29d2a902f8f7e" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Speech2Text.py" /></Component><Component Id="Id3111b80e20d4890862c704afdc86a80" Guid="90aa35ec-99ce-407b-9d81-644fbb15ba83" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Id3111b80e20d4890862c704afdc86a80" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\TTS_README.md" /></Component><Component Id="I4b4770e1a3744050a92bbe658a041dd0" Guid="e4a46d36-9705-4b3d-a485-c9e66f6d91b9" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I4b4770e1a3744050a92bbe658a041dd0" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Text2Speech.py" /></Component><Component Id="Ia596bc16296943b0916e75079ba22c1f" Guid="0553411c-cdd9-479d-b528-6fb692e7ea42" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ia596bc16296943b0916e75079ba22c1f" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\auth_service.py" /></Component><Component Id="I7425bd90bdc3453c8709e2205728f6b4" Guid="5e8b7fb4-8e5e-4a6a-9811-5a51f7677b88" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I7425bd90bdc3453c8709e2205728f6b4" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\compression_routes.py" /></Component><Component Id="I61c9412f16db42f5968aff733f2fe8cd" Guid="f8eb4685-50ee-40c2-a92a-32a6653f2693" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I61c9412f16db42f5968aff733f2fe8cd" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\diffusers_api.py" /></Component><Component Id="Iaad3781df1964b77a8a0c9cbebe9bd9f" Guid="65581084-ccb5-4f1c-b0d6-1bd3db9e74dc" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iaad3781df1964b77a8a0c9cbebe9bd9f" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\documents.db" /></Component><Component Id="Iec4cfcc02b8344c99f67699b4118834b" Guid="707c849c-d722-4758-97bb-2e67959ce0d8" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iec4cfcc02b8344c99f67699b4118834b" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\install_kokoro.py" /></Component><Component Id="I017b620cfec941adb5e91f63358a8858" Guid="fe7d5d48-56dd-4374-a87f-f0e2111fce1d" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I017b620cfec941adb5e91f63358a8858" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\langfuse_service.py" /></Component><Component Id="Ia2f3489bfdd94575813fe49ace6afdd3" Guid="d0761ff6-b6a1-4a5c-bcf3-2d2a9e25a7b3" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ia2f3489bfdd94575813fe49ace6afdd3" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\main.py" /></Component><Component Id="I537fc59de2314a3c96096d4a5c631f35" Guid="e693775e-4c3f-4657-9073-d7bdb11f08bb" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I537fc59de2314a3c96096d4a5c631f35" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\network_config.py" /></Component><Component Id="I861f76092a4e4d0791bbb8f80961b339" Guid="bf0b0e41-f707-4928-95a1-c10709c5c491" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I861f76092a4e4d0791bbb8f80961b339" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\ocr_processor_premium.py" /></Component><Component Id="Iab989908af6f4d178c5770e0cbc6b0b2" Guid="bd866431-353c-4ebb-a102-4476e848dbe0" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iab989908af6f4d178c5770e0cbc6b0b2" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\ocr_service.py" /></Component><Component Id="I4fad55807d21474db63f6258ce1811c7" Guid="d0379fa2-d28f-4dfd-aa1b-4bceaf8943d2" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I4fad55807d21474db63f6258ce1811c7" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\perfect_compressor.py" /></Component><Component Id="I56a3076bc9ab460ab9baee8df983b0b5" Guid="1fa926a7-ec96-4a1c-b1d0-24b10be2cb7b" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I56a3076bc9ab460ab9baee8df983b0b5" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\requirements.txt" /></Component><Component Id="Ib8b2ca787c1042ee81ce65bad639689d" Guid="f6d9998f-1569-4758-a180-3a62b2eb28fc" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ib8b2ca787c1042ee81ce65bad639689d" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\start_server.py" /></Component><Component Id="I78b55b052c3f443c8d1ada6514719df6" Guid="b94518f2-83bd-4752-9a0b-624617a9736b" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I78b55b052c3f443c8d1ada6514719df6" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_compression_tool.py" /></Component><Component Id="I75810520d1084c51a685be60d9e8a5ca" Guid="f7c95138-c110-4abe-833d-2b8b07b20b58" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I75810520d1084c51a685be60d9e8a5ca" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_non_streaming.py" /></Component><Component Id="I36fb4e0ceb4f46a99de096f6b8dd7a57" Guid="71fae08a-b9df-43a2-a1e5-fefc2e8a145b" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I36fb4e0ceb4f46a99de096f6b8dd7a57" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_optimizations.py" /></Component><Component Id="I2a1d477231ce43f8b185cf75fee47ffb" Guid="b02746c3-91ba-4dbc-84df-5ab6b22453c8" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I2a1d477231ce43f8b185cf75fee47ffb" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_streaming_optimized.py" /></Component><Directory Id="I9dc8c54b2e71423cb54e62d18f1e8f1a" Name="__pycache__"><Component Id="I67fdab80ae614625ab1a55b2033b90f7" Guid="ce047953-d527-4895-bbb2-e728ea56984b" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I67fdab80ae614625ab1a55b2033b90f7" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\langfuse_service.cpython-312.pyc" /></Component><Component Id="I7f46595059db444db733b541d6d92fe2" Guid="2c64d8c8-dadd-4b66-8074-6538237a2c3b" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I7f46595059db444db733b541d6d92fe2" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\main.cpython-312.pyc" /></Component><Component Id="I2a5b68e6ae844045afafd2741f690991" Guid="a7c7f730-a26a-4496-b7f9-f74d6c05c841" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I2a5b68e6ae844045afafd2741f690991" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\network_config.cpython-312.pyc" /></Component><Component Id="I29949b2e9ad1412fbf1f38a16d2475e3" Guid="ef1c5612-bd9e-4db1-8e55-c4b00128de78" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I29949b2e9ad1412fbf1f38a16d2475e3" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\ocr_processor_premium.cpython-312.pyc" /></Component><Component Id="If09f881a2e4a4ec59343fb1c1427036a" Guid="9e97c1d5-01d7-4ebd-9a44-3f7c8b55f8f7" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_If09f881a2e4a4ec59343fb1c1427036a" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\ocr_service.cpython-312.pyc" /></Component><Component Id="Iafca7c1020d74b85b1e6f75cee4862af" Guid="d8686c5b-5afc-4d3a-80fb-460e05a953f8" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iafca7c1020d74b85b1e6f75cee4862af" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\perfect_compressor.cpython-312.pyc" /></Component><Component Id="I64dd781975f141adaa845cab5c0b571b" Guid="60ccb02a-92e9-4f13-8e3d-202a8d726efc" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I64dd781975f141adaa845cab5c0b571b" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\rag_premium_service.cpython-312.pyc" /></Component></Directory><Directory Id="Ibe958f743b48445abea6f379a687ba55" Name="qdrant_storage"><Component Id="Ife6e479763eb4c4b9e17a6b99561e9e9" Guid="9eb3832b-be66-4918-9cf9-3d626ba970b5" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ife6e479763eb4c4b9e17a6b99561e9e9" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\qdrant_storage\.lock" /></Component><Component Id="I67e656f523624c3d9caad3b483440563" Guid="7dd25961-8cbc-47c0-927f-fdb49301c7ec" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I67e656f523624c3d9caad3b483440563" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\qdrant_storage\meta.json" /></Component></Directory><Directory Id="Idb2a7c896418454c9a7442152cf8decd" Name="services"><Component Id="I75487ea2461a40b78577d1acf7bf77a6" Guid="0a2c37d2-f69c-42c0-99ff-40442333264c" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I75487ea2461a40b78577d1acf7bf77a6" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\internet_search_service.py" /></Component><Component Id="Ie6002bd7c63f4a77aee94a6a415201fe" Guid="7299e54d-2e8a-4504-a01e-a4dfdf33c695" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ie6002bd7c63f4a77aee94a6a415201fe" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\startup_service.py" /></Component><Component Id="I10374bdc2e4248508121f822f2d507c8" Guid="967cde7f-979c-418a-a445-ead39b70cf73" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I10374bdc2e4248508121f822f2d507c8" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\web_scraper_service.py" /></Component><Directory Id="Ib21e5a761e4641d197a8d55da6130625" Name="__pycache__"><Component Id="I838de1ff44ce4309a012e68e70125a88" Guid="997b37f5-1921-41b8-8ee2-a94dc0f68f5e" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I838de1ff44ce4309a012e68e70125a88" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\internet_search_service.cpython-312.pyc" /></Component><Component Id="I7b3ab9f0c78d49939848aa575161000f" Guid="fdb10cb5-08e7-4d86-accd-9dd3dbee1a7c" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I7b3ab9f0c78d49939848aa575161000f" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\startup_service.cpython-312.pyc" /></Component><Component Id="I98f881aec6fa447aa7417f091814c177" Guid="09623213-85ef-4635-ba0e-44c46cb7416e" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I98f881aec6fa447aa7417f091814c177" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\web_scraper_service.cpython-312.pyc" /></Component></Directory></Directory><Directory Id="I8d5506c1f3234c3885095b8e9b032f32" Name="test_qdrant_temp"><Component Id="I0a58e0602b104be09550fa561080c94a" Guid="bf872191-a87d-45a8-8335-34c109f20b5e" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I0a58e0602b104be09550fa561080c94a" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_qdrant_temp\.lock" /></Component><Directory Id="I53e7279e23d54a588b3761ab272d1983" Name="collection"><Directory Id="Ie2b4f956cb47482c8afb177d38eb0ecf" Name="test_collection"><Component Id="Idb410d4d30244e389f7bc3b61af66abe" Guid="0b47b497-5474-42b5-ad63-0cbb39c98b32" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Idb410d4d30244e389f7bc3b61af66abe" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_qdrant_temp\collection\test_collection\storage.sqlite" /></Component></Directory></Directory></Directory><Directory Id="I6dc915ecf2ff40a5a0cb2226b973a17c" Name="tools"><Component Id="Ibfd4e9b9665f497c94a1d73d2ad42b86" Guid="e9bd1aba-9d59-4a1d-89aa-9a2c0ff746f3" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ibfd4e9b9665f497c94a1d73d2ad42b86" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\tools\internet_search_tools.py" /></Component><Directory Id="I0b2e8ed8bdb5428cb5c4d30eb342c40b" Name="__pycache__"><Component Id="Iaf99683446ce45a6affbf6ee1572fb14" Guid="99882ca9-f7e0-4b97-9129-73afba77bf3b" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iaf99683446ce45a6affbf6ee1572fb14" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\tools\__pycache__\internet_search_tools.cpython-312.pyc" /></Component></Directory></Directory></Directory></Directory>
            <Component Id="CMP_UninstallShortcut" Guid="*">

                <Shortcut Id="UninstallShortcut"
						  Name="Uninstall WeMa IA"
						  Description="Uninstalls WeMa IA"
						  Target="[System64Folder]msiexec.exe"
						  Arguments="/x [ProductCode]" />

				<RemoveFolder Id="INSTALLDIR"
							  On="uninstall" />

				<RegistryValue Root="HKCU"
							   Key="Software\wema-ia\WeMa IA"
							   Name="Uninstaller Shortcut"
							   Type="integer"
							   Value="1"
							   KeyPath="yes" />
            </Component>
        </DirectoryRef>

        <DirectoryRef Id="ApplicationProgramsFolder">
            <Component Id="ApplicationShortcut" Guid="*">
                <Shortcut Id="ApplicationStartMenuShortcut"
                    Name="WeMa IA"
                    Description="Runs WeMa IA"
                    Target="[!Path]"
                    Icon="ProductIcon"
                    WorkingDirectory="INSTALLDIR">
                    <ShortcutProperty Key="System.AppUserModel.ID" Value="com.wema-ia.app"/>
                </Shortcut>
                <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall"/>
                <RegistryValue Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="Start Menu Shortcut" Type="integer" Value="1" KeyPath="yes"/>
           </Component>
        </DirectoryRef>

<Feature
                Id="MainProgram"
                Title="Application"
                Description="!(loc.InstallAppFeature)"
                Level="1"
                ConfigurableDirectory="INSTALLDIR"
                AllowAdvertise="no"
                Display="expand"
                Absent="disallow">

            <ComponentRef Id="RegistryEntries"/>

<ComponentRef Id="I002820c1d223405ea2eca1a8b1ff14be"/>
<ComponentRef Id="I8bfbdb362caa4c9483b7ff9ad865d1e0"/>
<ComponentRef Id="Iba3afd4fc4ad4c0eadef8e2589493223"/>
<ComponentRef Id="Ia1f55808ef1d4598b4e29d2a902f8f7e"/>
<ComponentRef Id="Id3111b80e20d4890862c704afdc86a80"/>
<ComponentRef Id="I4b4770e1a3744050a92bbe658a041dd0"/>
<ComponentRef Id="Ia596bc16296943b0916e75079ba22c1f"/>
<ComponentRef Id="I7425bd90bdc3453c8709e2205728f6b4"/>
<ComponentRef Id="I61c9412f16db42f5968aff733f2fe8cd"/>
<ComponentRef Id="Iaad3781df1964b77a8a0c9cbebe9bd9f"/>
<ComponentRef Id="Iec4cfcc02b8344c99f67699b4118834b"/>
<ComponentRef Id="I017b620cfec941adb5e91f63358a8858"/>
<ComponentRef Id="Ia2f3489bfdd94575813fe49ace6afdd3"/>
<ComponentRef Id="I537fc59de2314a3c96096d4a5c631f35"/>
<ComponentRef Id="I861f76092a4e4d0791bbb8f80961b339"/>
<ComponentRef Id="Iab989908af6f4d178c5770e0cbc6b0b2"/>
<ComponentRef Id="I4fad55807d21474db63f6258ce1811c7"/>
<ComponentRef Id="I56a3076bc9ab460ab9baee8df983b0b5"/>
<ComponentRef Id="Ib8b2ca787c1042ee81ce65bad639689d"/>
<ComponentRef Id="I78b55b052c3f443c8d1ada6514719df6"/>
<ComponentRef Id="I75810520d1084c51a685be60d9e8a5ca"/>
<ComponentRef Id="I36fb4e0ceb4f46a99de096f6b8dd7a57"/>
<ComponentRef Id="I2a1d477231ce43f8b185cf75fee47ffb"/>
<ComponentRef Id="I67fdab80ae614625ab1a55b2033b90f7"/>
<ComponentRef Id="I7f46595059db444db733b541d6d92fe2"/>
<ComponentRef Id="I2a5b68e6ae844045afafd2741f690991"/>
<ComponentRef Id="I29949b2e9ad1412fbf1f38a16d2475e3"/>
<ComponentRef Id="If09f881a2e4a4ec59343fb1c1427036a"/>
<ComponentRef Id="Iafca7c1020d74b85b1e6f75cee4862af"/>
<ComponentRef Id="I64dd781975f141adaa845cab5c0b571b"/>
<ComponentRef Id="Ife6e479763eb4c4b9e17a6b99561e9e9"/>
<ComponentRef Id="I67e656f523624c3d9caad3b483440563"/>
<ComponentRef Id="I75487ea2461a40b78577d1acf7bf77a6"/>
<ComponentRef Id="Ie6002bd7c63f4a77aee94a6a415201fe"/>
<ComponentRef Id="I10374bdc2e4248508121f822f2d507c8"/>
<ComponentRef Id="I838de1ff44ce4309a012e68e70125a88"/>
<ComponentRef Id="I7b3ab9f0c78d49939848aa575161000f"/>
<ComponentRef Id="I98f881aec6fa447aa7417f091814c177"/>
<ComponentRef Id="I0a58e0602b104be09550fa561080c94a"/>
<ComponentRef Id="Idb410d4d30244e389f7bc3b61af66abe"/>
<ComponentRef Id="Ibfd4e9b9665f497c94a1d73d2ad42b86"/>
<ComponentRef Id="Iaf99683446ce45a6affbf6ee1572fb14"/>

            <Feature Id="ShortcutsFeature"
                Title="Shortcuts"
                Level="1">
                <ComponentRef Id="Path"/>
                <ComponentRef Id="CMP_UninstallShortcut" />
                <ComponentRef Id="ApplicationShortcut" />
                <ComponentRef Id="ApplicationShortcutDesktop" />
            </Feature>

            <Feature
                Id="Environment"
                Title="PATH Environment Variable"
                Description="!(loc.PathEnvVarFeature)"
                Level="1"
                Absent="allow">
            <ComponentRef Id="Path"/>
</Feature>
        </Feature>

        <Feature Id="External" AllowAdvertise="no" Absent="disallow">
</Feature>

        <!-- WebView2 -->
        <Property Id="WVRTINSTALLED">
            <RegistrySearch Id="WVRTInstalledSystem" Root="HKLM" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw" Win64="no" />
            <RegistrySearch Id="WVRTInstalledUser" Root="HKCU" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw"/>
        </Property>

        <CustomAction Id='DownloadAndInvokeBootstrapper' Directory="INSTALLDIR" Execute="deferred" ExeCommand='powershell.exe -NoProfile -windowstyle hidden try [\{] [\[]Net.ServicePointManager[\]]::SecurityProtocol = [\[]Net.SecurityProtocolType[\]]::Tls12 [\}] catch [\{][\}]; Invoke-WebRequest -Uri "https://go.microsoft.com/fwlink/p/?LinkId=2124703" -OutFile "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" ; Start-Process -FilePath "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" -ArgumentList (&apos;/silent&apos;, &apos;/install&apos;) -Wait' Return='check'/>
        <InstallExecuteSequence>
            <Custom Action='DownloadAndInvokeBootstrapper' Before='InstallFinalize'>
                <![CDATA[NOT(REMOVE OR WVRTINSTALLED)]]>
            </Custom>
        </InstallExecuteSequence>

        <!-- Embedded webview bootstrapper mode -->

        <!-- Embedded offline installer -->



        <InstallExecuteSequence>
          <Custom Action="LaunchApplication" After="InstallFinalize">AUTOLAUNCHAPP AND NOT Installed</Custom>
        </InstallExecuteSequence>

        <SetProperty Id="ARPINSTALLLOCATION" Value="[INSTALLDIR]" After="CostFinalize"/>
    </Product>
</Wix>
