#!/usr/bin/env node

/**
 * 🚀 CRÉATEUR D'EXTRACTEUR PORTABLE ANTI-ANTIVIRUS
 * Crée un EXE qui extrait et lance WeMa IA directement
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 CRÉATION EXTRACTEUR PORTABLE ANTI-ANTIVIRUS');
console.log('Objectif: EXE qui extrait et lance directement');
console.log('='.repeat(60));

// 1. BUILD PORTABLE D'ABORD
console.log('📦 Build version portable...');

try {
    execSync('npm run electron:build-win', { stdio: 'inherit' });
    console.log('✅ Build portable terminé');
} catch (error) {
    console.error('❌ Erreur build:', error.message);
    process.exit(1);
}

// 2. TROUVER LE DOSSIER PORTABLE
const portableDir = 'release/win-unpacked';
if (!fs.existsSync(portableDir)) {
    console.error('❌ Dossier portable non trouvé:', portableDir);
    process.exit(1);
}

console.log('📁 Dossier portable trouvé:', portableDir);

// 3. CRÉER SCRIPT BATCH LAUNCHER
console.log('🔧 Création launcher batch...');

const launcherBat = `@echo off
title WeMa IA - Demarrage
echo Demarrage de WeMa IA...
cd /d "%~dp0"
start "" "WeMa IA.exe"
exit
`;

fs.writeFileSync(path.join(portableDir, 'WeMa_IA_Launcher.bat'), launcherBat);

// 4. CRÉER SCRIPT EXTRACTEUR POWERSHELL
console.log('🔧 Création extracteur PowerShell...');

const extractorPs1 = `
# WeMa IA Extracteur Portable
$ErrorActionPreference = "SilentlyContinue"

Write-Host "WeMa IA - Extraction en cours..." -ForegroundColor Green

# Créer dossier temporaire
$tempDir = "$env:TEMP\\WeMa_IA_" + (Get-Random)
New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

# Extraire archive (sera intégrée)
$archivePath = "$PSScriptRoot\\wema-ia-portable.7z"
$7zipPath = "$env:ProgramFiles\\7-Zip\\7z.exe"

if (Test-Path $7zipPath) {
    & "$7zipPath" x "$archivePath" -o"$tempDir" -y | Out-Null
} else {
    # Fallback: extraction PowerShell native
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::ExtractToDirectory($archivePath, $tempDir)
}

# Lancer WeMa IA
$exePath = "$tempDir\\WeMa IA.exe"
if (Test-Path $exePath) {
    Start-Process -FilePath $exePath -WorkingDirectory $tempDir
    Write-Host "WeMa IA lance avec succes!" -ForegroundColor Green
} else {
    Write-Host "Erreur: Impossible de trouver WeMa IA.exe" -ForegroundColor Red
    Read-Host "Appuyez sur Entree pour fermer"
}
`;

fs.writeFileSync('wema-ia-extractor.ps1', extractorPs1);

// 5. CRÉER ARCHIVE 7ZIP DU PORTABLE
console.log('📦 Création archive 7zip...');

try {
    // Utiliser 7zip si disponible
    execSync(`"C:\\Program Files\\7-Zip\\7z.exe" a -t7z -mx=1 wema-ia-portable.7z "${portableDir}\\*"`, { stdio: 'inherit' });
    console.log('✅ Archive 7zip créée');
} catch (error) {
    console.log('⚠️ 7zip non trouvé, utilisation PowerShell...');
    
    // Fallback: PowerShell compression
    const compressScript = `
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::CreateFromDirectory("${portableDir}", "wema-ia-portable.zip")
    `;
    
    fs.writeFileSync('compress.ps1', compressScript);
    execSync('powershell -ExecutionPolicy Bypass -File compress.ps1', { stdio: 'inherit' });
    fs.unlinkSync('compress.ps1');
    
    // Renommer en .7z pour compatibilité
    if (fs.existsSync('wema-ia-portable.zip')) {
        fs.renameSync('wema-ia-portable.zip', 'wema-ia-portable.7z');
    }
}

// 6. CRÉER EXE EXTRACTEUR AVEC PS2EXE (si disponible)
console.log('🔧 Tentative création EXE extracteur...');

try {
    // Vérifier si ps2exe est disponible
    execSync('powershell -Command "Get-Module -ListAvailable ps2exe"', { stdio: 'pipe' });
    
    // Créer EXE
    execSync('powershell -Command "ps2exe -inputFile wema-ia-extractor.ps1 -outputFile WeMa_IA_Portable.exe -noConsole -title \\"WeMa IA Portable\\" -description \\"Assistant IA Portable\\" -version 0.1.2"', { stdio: 'inherit' });
    
    console.log('✅ EXE extracteur créé: WeMa_IA_Portable.exe');
    
} catch (error) {
    console.log('⚠️ PS2EXE non disponible, gardons le script PowerShell');
    console.log('📝 Utilisez: powershell -ExecutionPolicy Bypass -File wema-ia-extractor.ps1');
}

// 7. INFORMATIONS FINALES
console.log('');
console.log('🎉 EXTRACTEUR PORTABLE CRÉÉ !');
console.log('='.repeat(60));
console.log('📁 FICHIERS CRÉÉS:');
console.log('  - wema-ia-portable.7z (archive complète)');
console.log('  - wema-ia-extractor.ps1 (extracteur PowerShell)');
if (fs.existsSync('WeMa_IA_Portable.exe')) {
    console.log('  - WeMa_IA_Portable.exe (extracteur EXE)');
}
console.log('');
console.log('🎯 UTILISATION:');
console.log('1. Distribuez WeMa_IA_Portable.exe (ou le .ps1)');
console.log('2. L\'utilisateur double-clique');
console.log('3. Extraction automatique + lancement');
console.log('4. Pas d\'installation, pas de traces');
console.log('');
console.log('🛡️ AVANTAGES ANTI-ANTIVIRUS:');
console.log('✅ Pas d\'installateur NSIS suspect');
console.log('✅ Extraction temporaire');
console.log('✅ Signature différente');
console.log('✅ Comportement légitime');

// Copier vers Downloads
try {
    const downloadsPath = path.join(require('os').homedir(), 'Downloads');
    if (fs.existsSync('WeMa_IA_Portable.exe')) {
        fs.copyFileSync('WeMa_IA_Portable.exe', path.join(downloadsPath, 'WeMa_IA_Portable.exe'));
        console.log('📥 Copié vers Downloads: WeMa_IA_Portable.exe');
    }
    fs.copyFileSync('wema-ia-extractor.ps1', path.join(downloadsPath, 'WeMa_IA_Extractor.ps1'));
    console.log('📥 Copié vers Downloads: WeMa_IA_Extractor.ps1');
} catch (e) {
    console.log('⚠️ Impossible de copier vers Downloads');
}
