{"rustc": 16591470773350601817, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 1369601567987815722, "path": 8565146415801439001, "deps": [[2671782512663819132, "tauri_utils", false, 9367620168478054864], [4899080583175475170, "semver", false, 6444352428309700731], [6913375703034175521, "schemars", false, 1150964662670851007], [7170110829644101142, "json_patch", false, 1155322278575976970], [9689903380558560274, "serde", false, 651161780775042544], [12714016054753183456, "tauri_winres", false, 4070825672692693609], [13077543566650298139, "heck", false, 11763499977489420239], [13475171727366188400, "cargo_toml", false, 17863337868291814380], [13625485746686963219, "anyhow", false, 17113418254280443485], [15367738274754116744, "serde_json", false, 12147971697298994304], [15609422047640926750, "toml", false, 12347453665498926721], [15622660310229662834, "walkdir", false, 135640180713110039], [16928111194414003569, "dirs", false, 1080714218934060845], [17155886227862585100, "glob", false, 5556367183593458888]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-build-3be28ec75d6a1b09\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}