{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/chunk-D6G4REZN.mjs"], "sourcesContent": ["import {\n  __name,\n  lineBreakRegex\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/common/svgDrawCommon.ts\nimport { sanitizeUrl } from \"@braintree/sanitize-url\";\nvar drawRect = /* @__PURE__ */ __name((element, rectData) => {\n  const rectElement = element.append(\"rect\");\n  rectElement.attr(\"x\", rectData.x);\n  rectElement.attr(\"y\", rectData.y);\n  rectElement.attr(\"fill\", rectData.fill);\n  rectElement.attr(\"stroke\", rectData.stroke);\n  rectElement.attr(\"width\", rectData.width);\n  rectElement.attr(\"height\", rectData.height);\n  if (rectData.name) {\n    rectElement.attr(\"name\", rectData.name);\n  }\n  if (rectData.rx) {\n    rectElement.attr(\"rx\", rectData.rx);\n  }\n  if (rectData.ry) {\n    rectElement.attr(\"ry\", rectData.ry);\n  }\n  if (rectData.attrs !== void 0) {\n    for (const attrKey in rectData.attrs) {\n      rectElement.attr(attrKey, rectData.attrs[attrKey]);\n    }\n  }\n  if (rectData.class) {\n    rectElement.attr(\"class\", rectData.class);\n  }\n  return rectElement;\n}, \"drawRect\");\nvar drawBackgroundRect = /* @__PURE__ */ __name((element, bounds) => {\n  const rectData = {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    stroke: bounds.stroke,\n    class: \"rect\"\n  };\n  const rectElement = drawRect(element, rectData);\n  rectElement.lower();\n}, \"drawBackgroundRect\");\nvar drawText = /* @__PURE__ */ __name((element, textData) => {\n  const nText = textData.text.replace(lineBreakRegex, \" \");\n  const textElem = element.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.attr(\"class\", \"legend\");\n  textElem.style(\"text-anchor\", textData.anchor);\n  if (textData.class) {\n    textElem.attr(\"class\", textData.class);\n  }\n  const tspan = textElem.append(\"tspan\");\n  tspan.attr(\"x\", textData.x + textData.textMargin * 2);\n  tspan.text(nText);\n  return textElem;\n}, \"drawText\");\nvar drawImage = /* @__PURE__ */ __name((elem, x, y, link) => {\n  const imageElement = elem.append(\"image\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = sanitizeUrl(link);\n  imageElement.attr(\"xlink:href\", sanitizedLink);\n}, \"drawImage\");\nvar drawEmbeddedImage = /* @__PURE__ */ __name((element, x, y, link) => {\n  const imageElement = element.append(\"use\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = sanitizeUrl(link);\n  imageElement.attr(\"xlink:href\", `#${sanitizedLink}`);\n}, \"drawEmbeddedImage\");\nvar getNoteRect = /* @__PURE__ */ __name(() => {\n  const noteRectData = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    fill: \"#EDF2AE\",\n    stroke: \"#666\",\n    anchor: \"start\",\n    rx: 0,\n    ry: 0\n  };\n  return noteRectData;\n}, \"getNoteRect\");\nvar getTextObj = /* @__PURE__ */ __name(() => {\n  const testObject = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    \"text-anchor\": \"start\",\n    style: \"#666\",\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true\n  };\n  return testObject;\n}, \"getTextObj\");\n\nexport {\n  drawRect,\n  drawBackgroundRect,\n  drawText,\n  drawImage,\n  drawEmbeddedImage,\n  getNoteRect,\n  getTextObj\n};\n"], "mappings": ";;;;;;;;;;;;AAMA,0BAA4B;AAC5B,IAAI,WAA2B,OAAO,CAAC,SAAS,aAAa;AAC3D,QAAM,cAAc,QAAQ,OAAO,MAAM;AACzC,cAAY,KAAK,KAAK,SAAS,CAAC;AAChC,cAAY,KAAK,KAAK,SAAS,CAAC;AAChC,cAAY,KAAK,QAAQ,SAAS,IAAI;AACtC,cAAY,KAAK,UAAU,SAAS,MAAM;AAC1C,cAAY,KAAK,SAAS,SAAS,KAAK;AACxC,cAAY,KAAK,UAAU,SAAS,MAAM;AAC1C,MAAI,SAAS,MAAM;AACjB,gBAAY,KAAK,QAAQ,SAAS,IAAI;AAAA,EACxC;AACA,MAAI,SAAS,IAAI;AACf,gBAAY,KAAK,MAAM,SAAS,EAAE;AAAA,EACpC;AACA,MAAI,SAAS,IAAI;AACf,gBAAY,KAAK,MAAM,SAAS,EAAE;AAAA,EACpC;AACA,MAAI,SAAS,UAAU,QAAQ;AAC7B,eAAW,WAAW,SAAS,OAAO;AACpC,kBAAY,KAAK,SAAS,SAAS,MAAM,OAAO,CAAC;AAAA,IACnD;AAAA,EACF;AACA,MAAI,SAAS,OAAO;AAClB,gBAAY,KAAK,SAAS,SAAS,KAAK;AAAA,EAC1C;AACA,SAAO;AACT,GAAG,UAAU;AACb,IAAI,qBAAqC,OAAO,CAAC,SAAS,WAAW;AACnE,QAAM,WAAW;AAAA,IACf,GAAG,OAAO;AAAA,IACV,GAAG,OAAO;AAAA,IACV,OAAO,OAAO,QAAQ,OAAO;AAAA,IAC7B,QAAQ,OAAO,QAAQ,OAAO;AAAA,IAC9B,MAAM,OAAO;AAAA,IACb,QAAQ,OAAO;AAAA,IACf,OAAO;AAAA,EACT;AACA,QAAM,cAAc,SAAS,SAAS,QAAQ;AAC9C,cAAY,MAAM;AACpB,GAAG,oBAAoB;AACvB,IAAI,WAA2B,OAAO,CAAC,SAAS,aAAa;AAC3D,QAAM,QAAQ,SAAS,KAAK,QAAQ,gBAAgB,GAAG;AACvD,QAAM,WAAW,QAAQ,OAAO,MAAM;AACtC,WAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,WAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,WAAS,KAAK,SAAS,QAAQ;AAC/B,WAAS,MAAM,eAAe,SAAS,MAAM;AAC7C,MAAI,SAAS,OAAO;AAClB,aAAS,KAAK,SAAS,SAAS,KAAK;AAAA,EACvC;AACA,QAAM,QAAQ,SAAS,OAAO,OAAO;AACrC,QAAM,KAAK,KAAK,SAAS,IAAI,SAAS,aAAa,CAAC;AACpD,QAAM,KAAK,KAAK;AAChB,SAAO;AACT,GAAG,UAAU;AACb,IAAI,YAA4B,OAAO,CAAC,MAAM,GAAG,GAAG,SAAS;AAC3D,QAAM,eAAe,KAAK,OAAO,OAAO;AACxC,eAAa,KAAK,KAAK,CAAC;AACxB,eAAa,KAAK,KAAK,CAAC;AACxB,QAAM,oBAAgB,iCAAY,IAAI;AACtC,eAAa,KAAK,cAAc,aAAa;AAC/C,GAAG,WAAW;AACd,IAAI,oBAAoC,OAAO,CAAC,SAAS,GAAG,GAAG,SAAS;AACtE,QAAM,eAAe,QAAQ,OAAO,KAAK;AACzC,eAAa,KAAK,KAAK,CAAC;AACxB,eAAa,KAAK,KAAK,CAAC;AACxB,QAAM,oBAAgB,iCAAY,IAAI;AACtC,eAAa,KAAK,cAAc,IAAI,aAAa,EAAE;AACrD,GAAG,mBAAmB;AACtB,IAAI,cAA8B,OAAO,MAAM;AAC7C,QAAM,eAAe;AAAA,IACnB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AACA,SAAO;AACT,GAAG,aAAa;AAChB,IAAI,aAA6B,OAAO,MAAM;AAC5C,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,EACT;AACA,SAAO;AACT,GAAG,YAAY;", "names": []}