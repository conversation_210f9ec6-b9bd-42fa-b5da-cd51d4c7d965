{"rustc": 16591470773350601817, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 1369601567987815722, "path": 7532663446879285561, "deps": [[9620753569207166497, "zerovec_derive", false, 16072503808869615710], [10706449961930108323, "yoke", false, 15092657081915781781], [17046516144589451410, "zerofrom", false, 11321860112542772230]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\zerovec-9de7d7188b2e9227\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}