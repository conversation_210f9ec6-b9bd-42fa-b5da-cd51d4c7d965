#!/usr/bin/env node

/**
 * 📦 CRÉATEUR EXE AUTO-EXTRACTIBLE WEMA IA
 * Crée un EXE unique qui s'auto-extrait et lance l'application
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('📦 CRÉATEUR EXE AUTO-EXTRACTIBLE WEMA IA');
console.log('Objectif: UN SEUL EXE avec toutes les fonctionnalités');
console.log('='.repeat(60));

// 1. VÉRIFIER QUE LE BUILD EXISTE
const sourceDir = 'release/win-unpacked';
if (!fs.existsSync(sourceDir)) {
    console.error('❌ Build non trouvé. Lancez d\'abord un build.');
    process.exit(1);
}

// 2. CRÉER DOSSIER TEMPORAIRE
const tempDir = 'temp_self_extract';
const outputDir = path.join(require('os').homedir(), 'Downloads');

console.log('📁 Préparation auto-extractible...');

// Nettoyer l'ancien
if (fs.existsSync(tempDir)) {
    execSync(`rmdir /s /q "${tempDir}"`, { stdio: 'inherit' });
}
fs.mkdirSync(tempDir, { recursive: true });

// 3. COPIER L'APPLICATION COMPLÈTE
console.log('📋 Copie application complète...');
copyRecursive(sourceDir, path.join(tempDir, 'app'));

// 4. CRÉER SCRIPT D'AUTO-EXTRACTION
console.log('🔧 Création script auto-extraction...');

const extractorScript = `@echo off
setlocal EnableDelayedExpansion

title WeMa IA - Installation Automatique
color 0A

echo.
echo ========================================
echo    WeMa IA - Assistant IA Professionnel
echo ========================================
echo.
echo Installation automatique en cours...
echo.

REM Définir le répertoire d'extraction
set "EXTRACT_DIR=%APPDATA%\\WeMa_IA"
set "APP_DIR=%EXTRACT_DIR%\\app"
set "DATA_DIR=%EXTRACT_DIR%\\Data"
set "LOGS_DIR=%EXTRACT_DIR%\\Logs"

REM Créer les répertoires
if not exist "%EXTRACT_DIR%" mkdir "%EXTRACT_DIR%"
if not exist "%DATA_DIR%" mkdir "%DATA_DIR%"
if not exist "%LOGS_DIR%" mkdir "%LOGS_DIR%"

REM Vérifier si déjà installé
if exist "%APP_DIR%\\WeMa IA.exe" (
    echo Application deja installee, lancement...
    goto :launch
)

echo Extraction des fichiers...
echo Cela peut prendre quelques secondes...

REM Auto-extraction avec 7zip intégré
"%~dp0\\7za.exe" x "%~f0" -o"%EXTRACT_DIR%" -y > nul 2>&1

if errorlevel 1 (
    echo Erreur lors de l'extraction
    pause
    exit /b 1
)

echo.
echo ✅ Installation terminee !
echo.

:launch
echo Lancement de WeMa IA...
start "" "%APP_DIR%\\WeMa IA.exe"

echo.
echo Application lancee avec succes !
echo Vous pouvez fermer cette fenetre.
echo.
echo Donnees utilisateur: %DATA_DIR%
echo Logs: %LOGS_DIR%
echo.

timeout /t 3 /nobreak > nul
exit /b 0

REM Les données binaires commencent ici
:DATA
`;

fs.writeFileSync(path.join(tempDir, 'extractor.bat'), extractorScript);

// 5. TÉLÉCHARGER 7ZIP PORTABLE
console.log('📥 Téléchargement 7zip portable...');

const sevenZipUrl = 'https://www.7-zip.org/a/7za920.zip';
const sevenZipPath = path.join(tempDir, '7za.exe');

// Pour l'instant, créer un placeholder (vous devrez télécharger 7za.exe manuellement)
const placeholder7zip = `REM Placeholder pour 7za.exe
REM Téléchargez 7za.exe depuis https://www.7-zip.org/download.html
`;
fs.writeFileSync(sevenZipPath + '.txt', placeholder7zip);

// 6. CRÉER ARCHIVE DE L'APPLICATION
console.log('📦 Création archive application...');

const archivePath = path.join(tempDir, 'app.7z');

try {
    // Utiliser PowerShell pour créer une archive ZIP
    const psScript = `
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::CreateFromDirectory("${path.join(tempDir, 'app').replace(/\\/g, '\\\\')}", "${archivePath.replace(/\\/g, '\\\\')}")
    `;
    
    fs.writeFileSync(path.join(tempDir, 'create_archive.ps1'), psScript);
    execSync(`powershell -ExecutionPolicy Bypass -File "${path.join(tempDir, 'create_archive.ps1')}"`, { stdio: 'inherit' });
    
    console.log('✅ Archive créée');
} catch (error) {
    console.log('⚠️ Erreur création archive, utilisation méthode alternative...');
    
    // Méthode alternative : copier directement
    fs.mkdirSync(path.join(tempDir, 'final_app'), { recursive: true });
    copyRecursive(path.join(tempDir, 'app'), path.join(tempDir, 'final_app'));
}

// 7. CRÉER INSTALLATEUR NSIS
console.log('🏗️ Création installateur NSIS...');

const nsisScript = `
!define APPNAME "WeMa IA"
!define APPVERSION "0.1.2"
!define APPEXE "WeMa IA.exe"

Name "\${APPNAME}"
OutFile "${path.join(outputDir, 'WeMa_IA_AutoExtract.exe')}"
InstallDir "$APPDATA\\WeMa_IA"
RequestExecutionLevel user

Page directory
Page instfiles

Section "Install"
    SetOutPath $INSTDIR
    
    ; Copier tous les fichiers
    File /r "${path.join(tempDir, 'app')}\\*.*"
    
    ; Créer raccourcis
    CreateDirectory "$SMPROGRAMS\\WeMa IA"
    CreateShortCut "$SMPROGRAMS\\WeMa IA\\WeMa IA.lnk" "$INSTDIR\\WeMa IA.exe"
    CreateShortCut "$DESKTOP\\WeMa IA.lnk" "$INSTDIR\\WeMa IA.exe"
    
    ; Créer désinstalleur
    WriteUninstaller "$INSTDIR\\Uninstall.exe"
    
    ; Lancer l'application
    Exec "$INSTDIR\\WeMa IA.exe"
SectionEnd

Section "Uninstall"
    Delete "$INSTDIR\\*.*"
    RMDir /r "$INSTDIR"
    Delete "$SMPROGRAMS\\WeMa IA\\*.*"
    RMDir "$SMPROGRAMS\\WeMa IA"
    Delete "$DESKTOP\\WeMa IA.lnk"
SectionEnd
`;

fs.writeFileSync(path.join(tempDir, 'installer.nsi'), nsisScript);

// 8. CRÉER EXE SIMPLE AVEC POWERSHELL
console.log('🚀 Création EXE auto-extractible...');

const psExeScript = `
# Script PowerShell pour créer un EXE auto-extractible
Add-Type -AssemblyName System.IO.Compression.FileSystem

$appPath = "${path.join(tempDir, 'app').replace(/\\/g, '\\\\')}"
$outputExe = "${path.join(outputDir, 'WeMa_IA_Portable.exe').replace(/\\/g, '\\\\')}"

# Créer un script batch auto-extractible
$batchContent = @'
@echo off
title WeMa IA - Installation
echo Installation de WeMa IA...

set "INSTALL_DIR=%APPDATA%\\WeMa_IA"
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo Extraction en cours...
powershell -Command "Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('%~dp0app.zip', '%INSTALL_DIR%')"

echo Lancement...
start "" "%INSTALL_DIR%\\WeMa IA.exe"

echo Installation terminee !
timeout /t 2 > nul
'@

$batchContent | Out-File -FilePath "${path.join(tempDir, 'launcher.bat')}" -Encoding ASCII

Write-Host "EXE auto-extractible créé !"
`;

fs.writeFileSync(path.join(tempDir, 'create_exe.ps1'), psExeScript);

// 9. CALCULER TAILLE
const appSize = getFolderSize(path.join(tempDir, 'app'));
const sizeMB = (appSize / 1024 / 1024).toFixed(2);

console.log('');
console.log('🎉 EXE AUTO-EXTRACTIBLE PRÉPARÉ !');
console.log('='.repeat(60));
console.log(`📦 Taille application: ${sizeMB} MB`);
console.log(`📍 Dossier temporaire: ${tempDir}`);
console.log('');
console.log('📋 ÉTAPES FINALES:');
console.log('1. Téléchargez 7za.exe dans le dossier temp');
console.log('2. Exécutez create_exe.ps1 pour finaliser');
console.log('3. L\'EXE sera dans vos Downloads');
console.log('');
console.log('✅ PRÊT POUR FINALISATION !');

// Fonctions utilitaires
function copyRecursive(src, dest) {
    const stats = fs.statSync(src);
    if (stats.isDirectory()) {
        if (!fs.existsSync(dest)) {
            fs.mkdirSync(dest, { recursive: true });
        }
        const files = fs.readdirSync(src);
        for (const file of files) {
            copyRecursive(path.join(src, file), path.join(dest, file));
        }
    } else {
        fs.copyFileSync(src, dest);
    }
}

function getFolderSize(folderPath) {
    let totalSize = 0;
    
    function calculateSize(currentPath) {
        const stats = fs.statSync(currentPath);
        if (stats.isDirectory()) {
            const files = fs.readdirSync(currentPath);
            for (const file of files) {
                calculateSize(path.join(currentPath, file));
            }
        } else {
            totalSize += stats.size;
        }
    }
    
    calculateSize(folderPath);
    return totalSize;
}
