{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/chunk-O4NI6UNU.mjs"], "sourcesContent": ["import {\n  __name,\n  assignWithDepth_default,\n  common_default,\n  detectType,\n  directiveRegex,\n  log,\n  sanitizeDirective\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/utils.ts\nimport { sanitizeUrl } from \"@braintree/sanitize-url\";\nimport {\n  curveBasis,\n  curveBasisClosed,\n  curveBasisOpen,\n  curveBumpX,\n  curveBumpY,\n  curveBundle,\n  curveCardinalClosed,\n  curveCardinalOpen,\n  curveCardinal,\n  curveCatmullRomClosed,\n  curveCatmullRomOpen,\n  curveCatmullRom,\n  curveLinear,\n  curveLinearClosed,\n  curveMonotoneX,\n  curveMonotoneY,\n  curveNatural,\n  curveStep,\n  curveStepAfter,\n  curveStepBefore,\n  select\n} from \"d3\";\nimport memoize from \"lodash-es/memoize.js\";\nimport merge from \"lodash-es/merge.js\";\nvar ZERO_WIDTH_SPACE = \"\\u200B\";\nvar d3CurveTypes = {\n  curveBasis,\n  curveBasisClosed,\n  curveBasisOpen,\n  curveBumpX,\n  curveBumpY,\n  curveBundle,\n  curveCardinalClosed,\n  curveCardinalOpen,\n  curveCardinal,\n  curveCatmullRomClosed,\n  curveCatmullRomOpen,\n  curveCatmullRom,\n  curveLinear,\n  curveLinearClosed,\n  curveMonotoneX,\n  curveMonotoneY,\n  curveNatural,\n  curveStep,\n  curveStepAfter,\n  curveStepBefore\n};\nvar directiveWithoutOpen = /\\s*(?:(\\w+)(?=:):|(\\w+))\\s*(?:(\\w+)|((?:(?!}%{2}).|\\r?\\n)*))?\\s*(?:}%{2})?/gi;\nvar detectInit = /* @__PURE__ */ __name(function(text, config) {\n  const inits = detectDirective(text, /(?:init\\b)|(?:initialize\\b)/);\n  let results = {};\n  if (Array.isArray(inits)) {\n    const args = inits.map((init) => init.args);\n    sanitizeDirective(args);\n    results = assignWithDepth_default(results, [...args]);\n  } else {\n    results = inits.args;\n  }\n  if (!results) {\n    return;\n  }\n  let type = detectType(text, config);\n  const prop = \"config\";\n  if (results[prop] !== void 0) {\n    if (type === \"flowchart-v2\") {\n      type = \"flowchart\";\n    }\n    results[type] = results[prop];\n    delete results[prop];\n  }\n  return results;\n}, \"detectInit\");\nvar detectDirective = /* @__PURE__ */ __name(function(text, type = null) {\n  try {\n    const commentWithoutDirectives = new RegExp(\n      `[%]{2}(?![{]${directiveWithoutOpen.source})(?=[}][%]{2}).*\n`,\n      \"ig\"\n    );\n    text = text.trim().replace(commentWithoutDirectives, \"\").replace(/'/gm, '\"');\n    log.debug(\n      `Detecting diagram directive${type !== null ? \" type:\" + type : \"\"} based on the text:${text}`\n    );\n    let match;\n    const result = [];\n    while ((match = directiveRegex.exec(text)) !== null) {\n      if (match.index === directiveRegex.lastIndex) {\n        directiveRegex.lastIndex++;\n      }\n      if (match && !type || type && match[1]?.match(type) || type && match[2]?.match(type)) {\n        const type2 = match[1] ? match[1] : match[2];\n        const args = match[3] ? match[3].trim() : match[4] ? JSON.parse(match[4].trim()) : null;\n        result.push({ type: type2, args });\n      }\n    }\n    if (result.length === 0) {\n      return { type: text, args: null };\n    }\n    return result.length === 1 ? result[0] : result;\n  } catch (error) {\n    log.error(\n      `ERROR: ${error.message} - Unable to parse directive type: '${type}' based on the text: '${text}'`\n    );\n    return { type: void 0, args: null };\n  }\n}, \"detectDirective\");\nvar removeDirectives = /* @__PURE__ */ __name(function(text) {\n  return text.replace(directiveRegex, \"\");\n}, \"removeDirectives\");\nvar isSubstringInArray = /* @__PURE__ */ __name(function(str, arr) {\n  for (const [i, element] of arr.entries()) {\n    if (element.match(str)) {\n      return i;\n    }\n  }\n  return -1;\n}, \"isSubstringInArray\");\nfunction interpolateToCurve(interpolate, defaultCurve) {\n  if (!interpolate) {\n    return defaultCurve;\n  }\n  const curveName = `curve${interpolate.charAt(0).toUpperCase() + interpolate.slice(1)}`;\n  return d3CurveTypes[curveName] ?? defaultCurve;\n}\n__name(interpolateToCurve, \"interpolateToCurve\");\nfunction formatUrl(linkStr, config) {\n  const url = linkStr.trim();\n  if (!url) {\n    return void 0;\n  }\n  if (config.securityLevel !== \"loose\") {\n    return sanitizeUrl(url);\n  }\n  return url;\n}\n__name(formatUrl, \"formatUrl\");\nvar runFunc = /* @__PURE__ */ __name((functionName, ...params) => {\n  const arrPaths = functionName.split(\".\");\n  const len = arrPaths.length - 1;\n  const fnName = arrPaths[len];\n  let obj = window;\n  for (let i = 0; i < len; i++) {\n    obj = obj[arrPaths[i]];\n    if (!obj) {\n      log.error(`Function name: ${functionName} not found in window`);\n      return;\n    }\n  }\n  obj[fnName](...params);\n}, \"runFunc\");\nfunction distance(p1, p2) {\n  if (!p1 || !p2) {\n    return 0;\n  }\n  return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));\n}\n__name(distance, \"distance\");\nfunction traverseEdge(points) {\n  let prevPoint;\n  let totalDistance = 0;\n  points.forEach((point) => {\n    totalDistance += distance(point, prevPoint);\n    prevPoint = point;\n  });\n  const remainingDistance = totalDistance / 2;\n  return calculatePoint(points, remainingDistance);\n}\n__name(traverseEdge, \"traverseEdge\");\nfunction calcLabelPosition(points) {\n  if (points.length === 1) {\n    return points[0];\n  }\n  return traverseEdge(points);\n}\n__name(calcLabelPosition, \"calcLabelPosition\");\nvar roundNumber = /* @__PURE__ */ __name((num, precision = 2) => {\n  const factor = Math.pow(10, precision);\n  return Math.round(num * factor) / factor;\n}, \"roundNumber\");\nvar calculatePoint = /* @__PURE__ */ __name((points, distanceToTraverse) => {\n  let prevPoint = void 0;\n  let remainingDistance = distanceToTraverse;\n  for (const point of points) {\n    if (prevPoint) {\n      const vectorDistance = distance(point, prevPoint);\n      if (vectorDistance === 0) {\n        return prevPoint;\n      }\n      if (vectorDistance < remainingDistance) {\n        remainingDistance -= vectorDistance;\n      } else {\n        const distanceRatio = remainingDistance / vectorDistance;\n        if (distanceRatio <= 0) {\n          return prevPoint;\n        }\n        if (distanceRatio >= 1) {\n          return { x: point.x, y: point.y };\n        }\n        if (distanceRatio > 0 && distanceRatio < 1) {\n          return {\n            x: roundNumber((1 - distanceRatio) * prevPoint.x + distanceRatio * point.x, 5),\n            y: roundNumber((1 - distanceRatio) * prevPoint.y + distanceRatio * point.y, 5)\n          };\n        }\n      }\n    }\n    prevPoint = point;\n  }\n  throw new Error(\"Could not find a suitable point for the given distance\");\n}, \"calculatePoint\");\nvar calcCardinalityPosition = /* @__PURE__ */ __name((isRelationTypePresent, points, initialPosition) => {\n  log.info(`our points ${JSON.stringify(points)}`);\n  if (points[0] !== initialPosition) {\n    points = points.reverse();\n  }\n  const distanceToCardinalityPoint = 25;\n  const center = calculatePoint(points, distanceToCardinalityPoint);\n  const d = isRelationTypePresent ? 10 : 5;\n  const angle = Math.atan2(points[0].y - center.y, points[0].x - center.x);\n  const cardinalityPosition = { x: 0, y: 0 };\n  cardinalityPosition.x = Math.sin(angle) * d + (points[0].x + center.x) / 2;\n  cardinalityPosition.y = -Math.cos(angle) * d + (points[0].y + center.y) / 2;\n  return cardinalityPosition;\n}, \"calcCardinalityPosition\");\nfunction calcTerminalLabelPosition(terminalMarkerSize, position, _points) {\n  const points = structuredClone(_points);\n  log.info(\"our points\", points);\n  if (position !== \"start_left\" && position !== \"start_right\") {\n    points.reverse();\n  }\n  const distanceToCardinalityPoint = 25 + terminalMarkerSize;\n  const center = calculatePoint(points, distanceToCardinalityPoint);\n  const d = 10 + terminalMarkerSize * 0.5;\n  const angle = Math.atan2(points[0].y - center.y, points[0].x - center.x);\n  const cardinalityPosition = { x: 0, y: 0 };\n  if (position === \"start_left\") {\n    cardinalityPosition.x = Math.sin(angle + Math.PI) * d + (points[0].x + center.x) / 2;\n    cardinalityPosition.y = -Math.cos(angle + Math.PI) * d + (points[0].y + center.y) / 2;\n  } else if (position === \"end_right\") {\n    cardinalityPosition.x = Math.sin(angle - Math.PI) * d + (points[0].x + center.x) / 2 - 5;\n    cardinalityPosition.y = -Math.cos(angle - Math.PI) * d + (points[0].y + center.y) / 2 - 5;\n  } else if (position === \"end_left\") {\n    cardinalityPosition.x = Math.sin(angle) * d + (points[0].x + center.x) / 2 - 5;\n    cardinalityPosition.y = -Math.cos(angle) * d + (points[0].y + center.y) / 2 - 5;\n  } else {\n    cardinalityPosition.x = Math.sin(angle) * d + (points[0].x + center.x) / 2;\n    cardinalityPosition.y = -Math.cos(angle) * d + (points[0].y + center.y) / 2;\n  }\n  return cardinalityPosition;\n}\n__name(calcTerminalLabelPosition, \"calcTerminalLabelPosition\");\nfunction getStylesFromArray(arr) {\n  let style = \"\";\n  let labelStyle = \"\";\n  for (const element of arr) {\n    if (element !== void 0) {\n      if (element.startsWith(\"color:\") || element.startsWith(\"text-align:\")) {\n        labelStyle = labelStyle + element + \";\";\n      } else {\n        style = style + element + \";\";\n      }\n    }\n  }\n  return { style, labelStyle };\n}\n__name(getStylesFromArray, \"getStylesFromArray\");\nvar cnt = 0;\nvar generateId = /* @__PURE__ */ __name(() => {\n  cnt++;\n  return \"id-\" + Math.random().toString(36).substr(2, 12) + \"-\" + cnt;\n}, \"generateId\");\nfunction makeRandomHex(length) {\n  let result = \"\";\n  const characters = \"0123456789abcdef\";\n  const charactersLength = characters.length;\n  for (let i = 0; i < length; i++) {\n    result += characters.charAt(Math.floor(Math.random() * charactersLength));\n  }\n  return result;\n}\n__name(makeRandomHex, \"makeRandomHex\");\nvar random = /* @__PURE__ */ __name((options) => {\n  return makeRandomHex(options.length);\n}, \"random\");\nvar getTextObj = /* @__PURE__ */ __name(function() {\n  return {\n    x: 0,\n    y: 0,\n    fill: void 0,\n    anchor: \"start\",\n    style: \"#666\",\n    width: 100,\n    height: 100,\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    valign: void 0,\n    text: \"\"\n  };\n}, \"getTextObj\");\nvar drawSimpleText = /* @__PURE__ */ __name(function(elem, textData) {\n  const nText = textData.text.replace(common_default.lineBreakRegex, \" \");\n  const [, _fontSizePx] = parseFontSize(textData.fontSize);\n  const textElem = elem.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.style(\"text-anchor\", textData.anchor);\n  textElem.style(\"font-family\", textData.fontFamily);\n  textElem.style(\"font-size\", _fontSizePx);\n  textElem.style(\"font-weight\", textData.fontWeight);\n  textElem.attr(\"fill\", textData.fill);\n  if (textData.class !== void 0) {\n    textElem.attr(\"class\", textData.class);\n  }\n  const span = textElem.append(\"tspan\");\n  span.attr(\"x\", textData.x + textData.textMargin * 2);\n  span.attr(\"fill\", textData.fill);\n  span.text(nText);\n  return textElem;\n}, \"drawSimpleText\");\nvar wrapLabel = memoize(\n  (label, maxWidth, config) => {\n    if (!label) {\n      return label;\n    }\n    config = Object.assign(\n      { fontSize: 12, fontWeight: 400, fontFamily: \"Arial\", joinWith: \"<br/>\" },\n      config\n    );\n    if (common_default.lineBreakRegex.test(label)) {\n      return label;\n    }\n    const words = label.split(\" \").filter(Boolean);\n    const completedLines = [];\n    let nextLine = \"\";\n    words.forEach((word, index) => {\n      const wordLength = calculateTextWidth(`${word} `, config);\n      const nextLineLength = calculateTextWidth(nextLine, config);\n      if (wordLength > maxWidth) {\n        const { hyphenatedStrings, remainingWord } = breakString(word, maxWidth, \"-\", config);\n        completedLines.push(nextLine, ...hyphenatedStrings);\n        nextLine = remainingWord;\n      } else if (nextLineLength + wordLength >= maxWidth) {\n        completedLines.push(nextLine);\n        nextLine = word;\n      } else {\n        nextLine = [nextLine, word].filter(Boolean).join(\" \");\n      }\n      const currentWord = index + 1;\n      const isLastWord = currentWord === words.length;\n      if (isLastWord) {\n        completedLines.push(nextLine);\n      }\n    });\n    return completedLines.filter((line) => line !== \"\").join(config.joinWith);\n  },\n  (label, maxWidth, config) => `${label}${maxWidth}${config.fontSize}${config.fontWeight}${config.fontFamily}${config.joinWith}`\n);\nvar breakString = memoize(\n  (word, maxWidth, hyphenCharacter = \"-\", config) => {\n    config = Object.assign(\n      { fontSize: 12, fontWeight: 400, fontFamily: \"Arial\", margin: 0 },\n      config\n    );\n    const characters = [...word];\n    const lines = [];\n    let currentLine = \"\";\n    characters.forEach((character, index) => {\n      const nextLine = `${currentLine}${character}`;\n      const lineWidth = calculateTextWidth(nextLine, config);\n      if (lineWidth >= maxWidth) {\n        const currentCharacter = index + 1;\n        const isLastLine = characters.length === currentCharacter;\n        const hyphenatedNextLine = `${nextLine}${hyphenCharacter}`;\n        lines.push(isLastLine ? nextLine : hyphenatedNextLine);\n        currentLine = \"\";\n      } else {\n        currentLine = nextLine;\n      }\n    });\n    return { hyphenatedStrings: lines, remainingWord: currentLine };\n  },\n  (word, maxWidth, hyphenCharacter = \"-\", config) => `${word}${maxWidth}${hyphenCharacter}${config.fontSize}${config.fontWeight}${config.fontFamily}`\n);\nfunction calculateTextHeight(text, config) {\n  return calculateTextDimensions(text, config).height;\n}\n__name(calculateTextHeight, \"calculateTextHeight\");\nfunction calculateTextWidth(text, config) {\n  return calculateTextDimensions(text, config).width;\n}\n__name(calculateTextWidth, \"calculateTextWidth\");\nvar calculateTextDimensions = memoize(\n  (text, config) => {\n    const { fontSize = 12, fontFamily = \"Arial\", fontWeight = 400 } = config;\n    if (!text) {\n      return { width: 0, height: 0 };\n    }\n    const [, _fontSizePx] = parseFontSize(fontSize);\n    const fontFamilies = [\"sans-serif\", fontFamily];\n    const lines = text.split(common_default.lineBreakRegex);\n    const dims = [];\n    const body = select(\"body\");\n    if (!body.remove) {\n      return { width: 0, height: 0, lineHeight: 0 };\n    }\n    const g = body.append(\"svg\");\n    for (const fontFamily2 of fontFamilies) {\n      let cHeight = 0;\n      const dim = { width: 0, height: 0, lineHeight: 0 };\n      for (const line of lines) {\n        const textObj = getTextObj();\n        textObj.text = line || ZERO_WIDTH_SPACE;\n        const textElem = drawSimpleText(g, textObj).style(\"font-size\", _fontSizePx).style(\"font-weight\", fontWeight).style(\"font-family\", fontFamily2);\n        const bBox = (textElem._groups || textElem)[0][0].getBBox();\n        if (bBox.width === 0 && bBox.height === 0) {\n          throw new Error(\"svg element not in render tree\");\n        }\n        dim.width = Math.round(Math.max(dim.width, bBox.width));\n        cHeight = Math.round(bBox.height);\n        dim.height += cHeight;\n        dim.lineHeight = Math.round(Math.max(dim.lineHeight, cHeight));\n      }\n      dims.push(dim);\n    }\n    g.remove();\n    const index = isNaN(dims[1].height) || isNaN(dims[1].width) || isNaN(dims[1].lineHeight) || dims[0].height > dims[1].height && dims[0].width > dims[1].width && dims[0].lineHeight > dims[1].lineHeight ? 0 : 1;\n    return dims[index];\n  },\n  (text, config) => `${text}${config.fontSize}${config.fontWeight}${config.fontFamily}`\n);\nvar InitIDGenerator = class {\n  constructor(deterministic = false, seed) {\n    this.count = 0;\n    this.count = seed ? seed.length : 0;\n    this.next = deterministic ? () => this.count++ : () => Date.now();\n  }\n  static {\n    __name(this, \"InitIDGenerator\");\n  }\n};\nvar decoder;\nvar entityDecode = /* @__PURE__ */ __name(function(html) {\n  decoder = decoder || document.createElement(\"div\");\n  html = escape(html).replace(/%26/g, \"&\").replace(/%23/g, \"#\").replace(/%3B/g, \";\");\n  decoder.innerHTML = html;\n  return unescape(decoder.textContent);\n}, \"entityDecode\");\nfunction isDetailedError(error) {\n  return \"str\" in error;\n}\n__name(isDetailedError, \"isDetailedError\");\nvar insertTitle = /* @__PURE__ */ __name((parent, cssClass, titleTopMargin, title) => {\n  if (!title) {\n    return;\n  }\n  const bounds = parent.node()?.getBBox();\n  if (!bounds) {\n    return;\n  }\n  parent.append(\"text\").text(title).attr(\"text-anchor\", \"middle\").attr(\"x\", bounds.x + bounds.width / 2).attr(\"y\", -titleTopMargin).attr(\"class\", cssClass);\n}, \"insertTitle\");\nvar parseFontSize = /* @__PURE__ */ __name((fontSize) => {\n  if (typeof fontSize === \"number\") {\n    return [fontSize, fontSize + \"px\"];\n  }\n  const fontSizeNumber = parseInt(fontSize ?? \"\", 10);\n  if (Number.isNaN(fontSizeNumber)) {\n    return [void 0, void 0];\n  } else if (fontSize === String(fontSizeNumber)) {\n    return [fontSizeNumber, fontSize + \"px\"];\n  } else {\n    return [fontSizeNumber, fontSize];\n  }\n}, \"parseFontSize\");\nfunction cleanAndMerge(defaultData, data) {\n  return merge({}, defaultData, data);\n}\n__name(cleanAndMerge, \"cleanAndMerge\");\nvar utils_default = {\n  assignWithDepth: assignWithDepth_default,\n  wrapLabel,\n  calculateTextHeight,\n  calculateTextWidth,\n  calculateTextDimensions,\n  cleanAndMerge,\n  detectInit,\n  detectDirective,\n  isSubstringInArray,\n  interpolateToCurve,\n  calcLabelPosition,\n  calcCardinalityPosition,\n  calcTerminalLabelPosition,\n  formatUrl,\n  getStylesFromArray,\n  generateId,\n  random,\n  runFunc,\n  entityDecode,\n  insertTitle,\n  parseFontSize,\n  InitIDGenerator\n};\nvar encodeEntities = /* @__PURE__ */ __name(function(text) {\n  let txt = text;\n  txt = txt.replace(/style.*:\\S*#.*;/g, function(s) {\n    return s.substring(0, s.length - 1);\n  });\n  txt = txt.replace(/classDef.*:\\S*#.*;/g, function(s) {\n    return s.substring(0, s.length - 1);\n  });\n  txt = txt.replace(/#\\w+;/g, function(s) {\n    const innerTxt = s.substring(1, s.length - 1);\n    const isInt = /^\\+?\\d+$/.test(innerTxt);\n    if (isInt) {\n      return \"\\uFB02\\xB0\\xB0\" + innerTxt + \"\\xB6\\xDF\";\n    } else {\n      return \"\\uFB02\\xB0\" + innerTxt + \"\\xB6\\xDF\";\n    }\n  });\n  return txt;\n}, \"encodeEntities\");\nvar decodeEntities = /* @__PURE__ */ __name(function(text) {\n  return text.replace(/ﬂ°°/g, \"&#\").replace(/ﬂ°/g, \"&\").replace(/¶ß/g, \";\");\n}, \"decodeEntities\");\nvar getEdgeId = /* @__PURE__ */ __name((from, to, {\n  counter = 0,\n  prefix,\n  suffix\n}, id) => {\n  if (id) {\n    return id;\n  }\n  return `${prefix ? `${prefix}_` : \"\"}${from}_${to}_${counter}${suffix ? `_${suffix}` : \"\"}`;\n}, \"getEdgeId\");\nfunction handleUndefinedAttr(attrValue) {\n  return attrValue ?? null;\n}\n__name(handleUndefinedAttr, \"handleUndefinedAttr\");\n\nexport {\n  ZERO_WIDTH_SPACE,\n  removeDirectives,\n  interpolateToCurve,\n  getStylesFromArray,\n  generateId,\n  random,\n  wrapLabel,\n  calculateTextHeight,\n  calculateTextWidth,\n  isDetailedError,\n  parseFontSize,\n  cleanAndMerge,\n  utils_default,\n  encodeEntities,\n  decodeEntities,\n  getEdgeId,\n  handleUndefinedAttr\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,0BAA4B;AA0B5B,IAAI,mBAAmB;AACvB,IAAI,eAAe;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,uBAAuB;AAC3B,IAAI,aAA6B,OAAO,SAAS,MAAM,QAAQ;AAC7D,QAAM,QAAQ,gBAAgB,MAAM,6BAA6B;AACjE,MAAI,UAAU,CAAC;AACf,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,UAAM,OAAO,MAAM,IAAI,CAAC,SAAS,KAAK,IAAI;AAC1C,sBAAkB,IAAI;AACtB,cAAU,wBAAwB,SAAS,CAAC,GAAG,IAAI,CAAC;AAAA,EACtD,OAAO;AACL,cAAU,MAAM;AAAA,EAClB;AACA,MAAI,CAAC,SAAS;AACZ;AAAA,EACF;AACA,MAAI,OAAO,WAAW,MAAM,MAAM;AAClC,QAAM,OAAO;AACb,MAAI,QAAQ,IAAI,MAAM,QAAQ;AAC5B,QAAI,SAAS,gBAAgB;AAC3B,aAAO;AAAA,IACT;AACA,YAAQ,IAAI,IAAI,QAAQ,IAAI;AAC5B,WAAO,QAAQ,IAAI;AAAA,EACrB;AACA,SAAO;AACT,GAAG,YAAY;AACf,IAAI,kBAAkC,OAAO,SAAS,MAAM,OAAO,MAAM;AArFzE,MAAAA,KAAA;AAsFE,MAAI;AACF,UAAM,2BAA2B,IAAI;AAAA,MACnC,eAAe,qBAAqB,MAAM;AAAA;AAAA,MAE1C;AAAA,IACF;AACA,WAAO,KAAK,KAAK,EAAE,QAAQ,0BAA0B,EAAE,EAAE,QAAQ,OAAO,GAAG;AAC3E,QAAI;AAAA,MACF,8BAA8B,SAAS,OAAO,WAAW,OAAO,EAAE,sBAAsB,IAAI;AAAA,IAC9F;AACA,QAAI;AACJ,UAAM,SAAS,CAAC;AAChB,YAAQ,QAAQ,eAAe,KAAK,IAAI,OAAO,MAAM;AACnD,UAAI,MAAM,UAAU,eAAe,WAAW;AAC5C,uBAAe;AAAA,MACjB;AACA,UAAI,SAAS,CAAC,QAAQ,UAAQA,MAAA,MAAM,CAAC,MAAP,gBAAAA,IAAU,MAAM,UAAS,UAAQ,WAAM,CAAC,MAAP,mBAAU,MAAM,QAAO;AACpF,cAAM,QAAQ,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC;AAC3C,cAAM,OAAO,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,MAAM,CAAC,EAAE,KAAK,CAAC,IAAI;AACnF,eAAO,KAAK,EAAE,MAAM,OAAO,KAAK,CAAC;AAAA,MACnC;AAAA,IACF;AACA,QAAI,OAAO,WAAW,GAAG;AACvB,aAAO,EAAE,MAAM,MAAM,MAAM,KAAK;AAAA,IAClC;AACA,WAAO,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAAA,EAC3C,SAAS,OAAO;AACd,QAAI;AAAA,MACF,UAAU,MAAM,OAAO,uCAAuC,IAAI,yBAAyB,IAAI;AAAA,IACjG;AACA,WAAO,EAAE,MAAM,QAAQ,MAAM,KAAK;AAAA,EACpC;AACF,GAAG,iBAAiB;AACpB,IAAI,mBAAmC,OAAO,SAAS,MAAM;AAC3D,SAAO,KAAK,QAAQ,gBAAgB,EAAE;AACxC,GAAG,kBAAkB;AACrB,IAAI,qBAAqC,OAAO,SAAS,KAAK,KAAK;AACjE,aAAW,CAAC,GAAG,OAAO,KAAK,IAAI,QAAQ,GAAG;AACxC,QAAI,QAAQ,MAAM,GAAG,GAAG;AACtB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT,GAAG,oBAAoB;AACvB,SAAS,mBAAmB,aAAa,cAAc;AACrD,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AACA,QAAM,YAAY,QAAQ,YAAY,OAAO,CAAC,EAAE,YAAY,IAAI,YAAY,MAAM,CAAC,CAAC;AACpF,SAAO,aAAa,SAAS,KAAK;AACpC;AACA,OAAO,oBAAoB,oBAAoB;AAC/C,SAAS,UAAU,SAAS,QAAQ;AAClC,QAAM,MAAM,QAAQ,KAAK;AACzB,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AACA,MAAI,OAAO,kBAAkB,SAAS;AACpC,eAAO,iCAAY,GAAG;AAAA,EACxB;AACA,SAAO;AACT;AACA,OAAO,WAAW,WAAW;AAC7B,IAAI,UAA0B,OAAO,CAAC,iBAAiB,WAAW;AAChE,QAAM,WAAW,aAAa,MAAM,GAAG;AACvC,QAAM,MAAM,SAAS,SAAS;AAC9B,QAAM,SAAS,SAAS,GAAG;AAC3B,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,IAAI,SAAS,CAAC,CAAC;AACrB,QAAI,CAAC,KAAK;AACR,UAAI,MAAM,kBAAkB,YAAY,sBAAsB;AAC9D;AAAA,IACF;AAAA,EACF;AACA,MAAI,MAAM,EAAE,GAAG,MAAM;AACvB,GAAG,SAAS;AACZ,SAAS,SAAS,IAAI,IAAI;AACxB,MAAI,CAAC,MAAM,CAAC,IAAI;AACd,WAAO;AAAA,EACT;AACA,SAAO,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;AACtE;AACA,OAAO,UAAU,UAAU;AAC3B,SAAS,aAAa,QAAQ;AAC5B,MAAI;AACJ,MAAI,gBAAgB;AACpB,SAAO,QAAQ,CAAC,UAAU;AACxB,qBAAiB,SAAS,OAAO,SAAS;AAC1C,gBAAY;AAAA,EACd,CAAC;AACD,QAAM,oBAAoB,gBAAgB;AAC1C,SAAO,eAAe,QAAQ,iBAAiB;AACjD;AACA,OAAO,cAAc,cAAc;AACnC,SAAS,kBAAkB,QAAQ;AACjC,MAAI,OAAO,WAAW,GAAG;AACvB,WAAO,OAAO,CAAC;AAAA,EACjB;AACA,SAAO,aAAa,MAAM;AAC5B;AACA,OAAO,mBAAmB,mBAAmB;AAC7C,IAAI,cAA8B,OAAO,CAAC,KAAK,YAAY,MAAM;AAC/D,QAAM,SAAS,KAAK,IAAI,IAAI,SAAS;AACrC,SAAO,KAAK,MAAM,MAAM,MAAM,IAAI;AACpC,GAAG,aAAa;AAChB,IAAI,iBAAiC,OAAO,CAAC,QAAQ,uBAAuB;AAC1E,MAAI,YAAY;AAChB,MAAI,oBAAoB;AACxB,aAAW,SAAS,QAAQ;AAC1B,QAAI,WAAW;AACb,YAAM,iBAAiB,SAAS,OAAO,SAAS;AAChD,UAAI,mBAAmB,GAAG;AACxB,eAAO;AAAA,MACT;AACA,UAAI,iBAAiB,mBAAmB;AACtC,6BAAqB;AAAA,MACvB,OAAO;AACL,cAAM,gBAAgB,oBAAoB;AAC1C,YAAI,iBAAiB,GAAG;AACtB,iBAAO;AAAA,QACT;AACA,YAAI,iBAAiB,GAAG;AACtB,iBAAO,EAAE,GAAG,MAAM,GAAG,GAAG,MAAM,EAAE;AAAA,QAClC;AACA,YAAI,gBAAgB,KAAK,gBAAgB,GAAG;AAC1C,iBAAO;AAAA,YACL,GAAG,aAAa,IAAI,iBAAiB,UAAU,IAAI,gBAAgB,MAAM,GAAG,CAAC;AAAA,YAC7E,GAAG,aAAa,IAAI,iBAAiB,UAAU,IAAI,gBAAgB,MAAM,GAAG,CAAC;AAAA,UAC/E;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,gBAAY;AAAA,EACd;AACA,QAAM,IAAI,MAAM,wDAAwD;AAC1E,GAAG,gBAAgB;AACnB,IAAI,0BAA0C,OAAO,CAAC,uBAAuB,QAAQ,oBAAoB;AACvG,MAAI,KAAK,cAAc,KAAK,UAAU,MAAM,CAAC,EAAE;AAC/C,MAAI,OAAO,CAAC,MAAM,iBAAiB;AACjC,aAAS,OAAO,QAAQ;AAAA,EAC1B;AACA,QAAM,6BAA6B;AACnC,QAAM,SAAS,eAAe,QAAQ,0BAA0B;AAChE,QAAM,IAAI,wBAAwB,KAAK;AACvC,QAAM,QAAQ,KAAK,MAAM,OAAO,CAAC,EAAE,IAAI,OAAO,GAAG,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC;AACvE,QAAM,sBAAsB,EAAE,GAAG,GAAG,GAAG,EAAE;AACzC,sBAAoB,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK;AACzE,sBAAoB,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK;AAC1E,SAAO;AACT,GAAG,yBAAyB;AAC5B,SAAS,0BAA0B,oBAAoB,UAAU,SAAS;AACxE,QAAM,SAAS,gBAAgB,OAAO;AACtC,MAAI,KAAK,cAAc,MAAM;AAC7B,MAAI,aAAa,gBAAgB,aAAa,eAAe;AAC3D,WAAO,QAAQ;AAAA,EACjB;AACA,QAAM,6BAA6B,KAAK;AACxC,QAAM,SAAS,eAAe,QAAQ,0BAA0B;AAChE,QAAM,IAAI,KAAK,qBAAqB;AACpC,QAAM,QAAQ,KAAK,MAAM,OAAO,CAAC,EAAE,IAAI,OAAO,GAAG,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC;AACvE,QAAM,sBAAsB,EAAE,GAAG,GAAG,GAAG,EAAE;AACzC,MAAI,aAAa,cAAc;AAC7B,wBAAoB,IAAI,KAAK,IAAI,QAAQ,KAAK,EAAE,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK;AACnF,wBAAoB,IAAI,CAAC,KAAK,IAAI,QAAQ,KAAK,EAAE,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK;AAAA,EACtF,WAAW,aAAa,aAAa;AACnC,wBAAoB,IAAI,KAAK,IAAI,QAAQ,KAAK,EAAE,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK,IAAI;AACvF,wBAAoB,IAAI,CAAC,KAAK,IAAI,QAAQ,KAAK,EAAE,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK,IAAI;AAAA,EAC1F,WAAW,aAAa,YAAY;AAClC,wBAAoB,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK,IAAI;AAC7E,wBAAoB,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK,IAAI;AAAA,EAChF,OAAO;AACL,wBAAoB,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK;AACzE,wBAAoB,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,KAAK;AAAA,EAC5E;AACA,SAAO;AACT;AACA,OAAO,2BAA2B,2BAA2B;AAC7D,SAAS,mBAAmB,KAAK;AAC/B,MAAI,QAAQ;AACZ,MAAI,aAAa;AACjB,aAAW,WAAW,KAAK;AACzB,QAAI,YAAY,QAAQ;AACtB,UAAI,QAAQ,WAAW,QAAQ,KAAK,QAAQ,WAAW,aAAa,GAAG;AACrE,qBAAa,aAAa,UAAU;AAAA,MACtC,OAAO;AACL,gBAAQ,QAAQ,UAAU;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AACA,SAAO,EAAE,OAAO,WAAW;AAC7B;AACA,OAAO,oBAAoB,oBAAoB;AAC/C,IAAI,MAAM;AACV,IAAI,aAA6B,OAAO,MAAM;AAC5C;AACA,SAAO,QAAQ,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,GAAG,EAAE,IAAI,MAAM;AAClE,GAAG,YAAY;AACf,SAAS,cAAc,QAAQ;AAC7B,MAAI,SAAS;AACb,QAAM,aAAa;AACnB,QAAM,mBAAmB,WAAW;AACpC,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAU,WAAW,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,gBAAgB,CAAC;AAAA,EAC1E;AACA,SAAO;AACT;AACA,OAAO,eAAe,eAAe;AACrC,IAAI,SAAyB,OAAO,CAAC,YAAY;AAC/C,SAAO,cAAc,QAAQ,MAAM;AACrC,GAAG,QAAQ;AACX,IAAI,aAA6B,OAAO,WAAW;AACjD,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF,GAAG,YAAY;AACf,IAAI,iBAAiC,OAAO,SAAS,MAAM,UAAU;AACnE,QAAM,QAAQ,SAAS,KAAK,QAAQ,eAAe,gBAAgB,GAAG;AACtE,QAAM,CAAC,EAAE,WAAW,IAAI,cAAc,SAAS,QAAQ;AACvD,QAAM,WAAW,KAAK,OAAO,MAAM;AACnC,WAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,WAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,WAAS,MAAM,eAAe,SAAS,MAAM;AAC7C,WAAS,MAAM,eAAe,SAAS,UAAU;AACjD,WAAS,MAAM,aAAa,WAAW;AACvC,WAAS,MAAM,eAAe,SAAS,UAAU;AACjD,WAAS,KAAK,QAAQ,SAAS,IAAI;AACnC,MAAI,SAAS,UAAU,QAAQ;AAC7B,aAAS,KAAK,SAAS,SAAS,KAAK;AAAA,EACvC;AACA,QAAM,OAAO,SAAS,OAAO,OAAO;AACpC,OAAK,KAAK,KAAK,SAAS,IAAI,SAAS,aAAa,CAAC;AACnD,OAAK,KAAK,QAAQ,SAAS,IAAI;AAC/B,OAAK,KAAK,KAAK;AACf,SAAO;AACT,GAAG,gBAAgB;AACnB,IAAI,YAAY;AAAA,EACd,CAAC,OAAO,UAAU,WAAW;AAC3B,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AACA,aAAS,OAAO;AAAA,MACd,EAAE,UAAU,IAAI,YAAY,KAAK,YAAY,SAAS,UAAU,QAAQ;AAAA,MACxE;AAAA,IACF;AACA,QAAI,eAAe,eAAe,KAAK,KAAK,GAAG;AAC7C,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,MAAM,MAAM,GAAG,EAAE,OAAO,OAAO;AAC7C,UAAM,iBAAiB,CAAC;AACxB,QAAI,WAAW;AACf,UAAM,QAAQ,CAAC,MAAM,UAAU;AAC7B,YAAM,aAAa,mBAAmB,GAAG,IAAI,KAAK,MAAM;AACxD,YAAM,iBAAiB,mBAAmB,UAAU,MAAM;AAC1D,UAAI,aAAa,UAAU;AACzB,cAAM,EAAE,mBAAmB,cAAc,IAAI,YAAY,MAAM,UAAU,KAAK,MAAM;AACpF,uBAAe,KAAK,UAAU,GAAG,iBAAiB;AAClD,mBAAW;AAAA,MACb,WAAW,iBAAiB,cAAc,UAAU;AAClD,uBAAe,KAAK,QAAQ;AAC5B,mBAAW;AAAA,MACb,OAAO;AACL,mBAAW,CAAC,UAAU,IAAI,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAAA,MACtD;AACA,YAAM,cAAc,QAAQ;AAC5B,YAAM,aAAa,gBAAgB,MAAM;AACzC,UAAI,YAAY;AACd,uBAAe,KAAK,QAAQ;AAAA,MAC9B;AAAA,IACF,CAAC;AACD,WAAO,eAAe,OAAO,CAAC,SAAS,SAAS,EAAE,EAAE,KAAK,OAAO,QAAQ;AAAA,EAC1E;AAAA,EACA,CAAC,OAAO,UAAU,WAAW,GAAG,KAAK,GAAG,QAAQ,GAAG,OAAO,QAAQ,GAAG,OAAO,UAAU,GAAG,OAAO,UAAU,GAAG,OAAO,QAAQ;AAC9H;AACA,IAAI,cAAc;AAAA,EAChB,CAAC,MAAM,UAAU,kBAAkB,KAAK,WAAW;AACjD,aAAS,OAAO;AAAA,MACd,EAAE,UAAU,IAAI,YAAY,KAAK,YAAY,SAAS,QAAQ,EAAE;AAAA,MAChE;AAAA,IACF;AACA,UAAM,aAAa,CAAC,GAAG,IAAI;AAC3B,UAAM,QAAQ,CAAC;AACf,QAAI,cAAc;AAClB,eAAW,QAAQ,CAAC,WAAW,UAAU;AACvC,YAAM,WAAW,GAAG,WAAW,GAAG,SAAS;AAC3C,YAAM,YAAY,mBAAmB,UAAU,MAAM;AACrD,UAAI,aAAa,UAAU;AACzB,cAAM,mBAAmB,QAAQ;AACjC,cAAM,aAAa,WAAW,WAAW;AACzC,cAAM,qBAAqB,GAAG,QAAQ,GAAG,eAAe;AACxD,cAAM,KAAK,aAAa,WAAW,kBAAkB;AACrD,sBAAc;AAAA,MAChB,OAAO;AACL,sBAAc;AAAA,MAChB;AAAA,IACF,CAAC;AACD,WAAO,EAAE,mBAAmB,OAAO,eAAe,YAAY;AAAA,EAChE;AAAA,EACA,CAAC,MAAM,UAAU,kBAAkB,KAAK,WAAW,GAAG,IAAI,GAAG,QAAQ,GAAG,eAAe,GAAG,OAAO,QAAQ,GAAG,OAAO,UAAU,GAAG,OAAO,UAAU;AACnJ;AACA,SAAS,oBAAoB,MAAM,QAAQ;AACzC,SAAO,wBAAwB,MAAM,MAAM,EAAE;AAC/C;AACA,OAAO,qBAAqB,qBAAqB;AACjD,SAAS,mBAAmB,MAAM,QAAQ;AACxC,SAAO,wBAAwB,MAAM,MAAM,EAAE;AAC/C;AACA,OAAO,oBAAoB,oBAAoB;AAC/C,IAAI,0BAA0B;AAAA,EAC5B,CAAC,MAAM,WAAW;AAChB,UAAM,EAAE,WAAW,IAAI,aAAa,SAAS,aAAa,IAAI,IAAI;AAClE,QAAI,CAAC,MAAM;AACT,aAAO,EAAE,OAAO,GAAG,QAAQ,EAAE;AAAA,IAC/B;AACA,UAAM,CAAC,EAAE,WAAW,IAAI,cAAc,QAAQ;AAC9C,UAAM,eAAe,CAAC,cAAc,UAAU;AAC9C,UAAM,QAAQ,KAAK,MAAM,eAAe,cAAc;AACtD,UAAM,OAAO,CAAC;AACd,UAAM,OAAO,eAAO,MAAM;AAC1B,QAAI,CAAC,KAAK,QAAQ;AAChB,aAAO,EAAE,OAAO,GAAG,QAAQ,GAAG,YAAY,EAAE;AAAA,IAC9C;AACA,UAAM,IAAI,KAAK,OAAO,KAAK;AAC3B,eAAW,eAAe,cAAc;AACtC,UAAI,UAAU;AACd,YAAM,MAAM,EAAE,OAAO,GAAG,QAAQ,GAAG,YAAY,EAAE;AACjD,iBAAW,QAAQ,OAAO;AACxB,cAAM,UAAU,WAAW;AAC3B,gBAAQ,OAAO,QAAQ;AACvB,cAAM,WAAW,eAAe,GAAG,OAAO,EAAE,MAAM,aAAa,WAAW,EAAE,MAAM,eAAe,UAAU,EAAE,MAAM,eAAe,WAAW;AAC7I,cAAM,QAAQ,SAAS,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,QAAQ;AAC1D,YAAI,KAAK,UAAU,KAAK,KAAK,WAAW,GAAG;AACzC,gBAAM,IAAI,MAAM,gCAAgC;AAAA,QAClD;AACA,YAAI,QAAQ,KAAK,MAAM,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC;AACtD,kBAAU,KAAK,MAAM,KAAK,MAAM;AAChC,YAAI,UAAU;AACd,YAAI,aAAa,KAAK,MAAM,KAAK,IAAI,IAAI,YAAY,OAAO,CAAC;AAAA,MAC/D;AACA,WAAK,KAAK,GAAG;AAAA,IACf;AACA,MAAE,OAAO;AACT,UAAM,QAAQ,MAAM,KAAK,CAAC,EAAE,MAAM,KAAK,MAAM,KAAK,CAAC,EAAE,KAAK,KAAK,MAAM,KAAK,CAAC,EAAE,UAAU,KAAK,KAAK,CAAC,EAAE,SAAS,KAAK,CAAC,EAAE,UAAU,KAAK,CAAC,EAAE,QAAQ,KAAK,CAAC,EAAE,SAAS,KAAK,CAAC,EAAE,aAAa,KAAK,CAAC,EAAE,aAAa,IAAI;AAC9M,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,CAAC,MAAM,WAAW,GAAG,IAAI,GAAG,OAAO,QAAQ,GAAG,OAAO,UAAU,GAAG,OAAO,UAAU;AACrF;AA3bA;AA4bA,IAAI,mBAAkB,WAAM;AAAA,EAC1B,YAAY,gBAAgB,OAAO,MAAM;AACvC,SAAK,QAAQ;AACb,SAAK,QAAQ,OAAO,KAAK,SAAS;AAClC,SAAK,OAAO,gBAAgB,MAAM,KAAK,UAAU,MAAM,KAAK,IAAI;AAAA,EAClE;AAIF,GAFI,OAAO,IAAM,iBAAiB,GAPZ;AAUtB,IAAI;AACJ,IAAI,eAA+B,OAAO,SAAS,MAAM;AACvD,YAAU,WAAW,SAAS,cAAc,KAAK;AACjD,SAAO,OAAO,IAAI,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,QAAQ,GAAG;AACjF,UAAQ,YAAY;AACpB,SAAO,SAAS,QAAQ,WAAW;AACrC,GAAG,cAAc;AACjB,SAAS,gBAAgB,OAAO;AAC9B,SAAO,SAAS;AAClB;AACA,OAAO,iBAAiB,iBAAiB;AACzC,IAAI,cAA8B,OAAO,CAAC,QAAQ,UAAU,gBAAgB,UAAU;AAjdtF,MAAAA;AAkdE,MAAI,CAAC,OAAO;AACV;AAAA,EACF;AACA,QAAM,UAASA,MAAA,OAAO,KAAK,MAAZ,gBAAAA,IAAe;AAC9B,MAAI,CAAC,QAAQ;AACX;AAAA,EACF;AACA,SAAO,OAAO,MAAM,EAAE,KAAK,KAAK,EAAE,KAAK,eAAe,QAAQ,EAAE,KAAK,KAAK,OAAO,IAAI,OAAO,QAAQ,CAAC,EAAE,KAAK,KAAK,CAAC,cAAc,EAAE,KAAK,SAAS,QAAQ;AAC1J,GAAG,aAAa;AAChB,IAAI,gBAAgC,OAAO,CAAC,aAAa;AACvD,MAAI,OAAO,aAAa,UAAU;AAChC,WAAO,CAAC,UAAU,WAAW,IAAI;AAAA,EACnC;AACA,QAAM,iBAAiB,SAAS,YAAY,IAAI,EAAE;AAClD,MAAI,OAAO,MAAM,cAAc,GAAG;AAChC,WAAO,CAAC,QAAQ,MAAM;AAAA,EACxB,WAAW,aAAa,OAAO,cAAc,GAAG;AAC9C,WAAO,CAAC,gBAAgB,WAAW,IAAI;AAAA,EACzC,OAAO;AACL,WAAO,CAAC,gBAAgB,QAAQ;AAAA,EAClC;AACF,GAAG,eAAe;AAClB,SAAS,cAAc,aAAa,MAAM;AACxC,SAAO,cAAM,CAAC,GAAG,aAAa,IAAI;AACpC;AACA,OAAO,eAAe,eAAe;AACrC,IAAI,gBAAgB;AAAA,EAClB,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,iBAAiC,OAAO,SAAS,MAAM;AACzD,MAAI,MAAM;AACV,QAAM,IAAI,QAAQ,oBAAoB,SAAS,GAAG;AAChD,WAAO,EAAE,UAAU,GAAG,EAAE,SAAS,CAAC;AAAA,EACpC,CAAC;AACD,QAAM,IAAI,QAAQ,uBAAuB,SAAS,GAAG;AACnD,WAAO,EAAE,UAAU,GAAG,EAAE,SAAS,CAAC;AAAA,EACpC,CAAC;AACD,QAAM,IAAI,QAAQ,UAAU,SAAS,GAAG;AACtC,UAAM,WAAW,EAAE,UAAU,GAAG,EAAE,SAAS,CAAC;AAC5C,UAAM,QAAQ,WAAW,KAAK,QAAQ;AACtC,QAAI,OAAO;AACT,aAAO,QAAmB,WAAW;AAAA,IACvC,OAAO;AACL,aAAO,OAAe,WAAW;AAAA,IACnC;AAAA,EACF,CAAC;AACD,SAAO;AACT,GAAG,gBAAgB;AACnB,IAAI,iBAAiC,OAAO,SAAS,MAAM;AACzD,SAAO,KAAK,QAAQ,QAAQ,IAAI,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG;AAC1E,GAAG,gBAAgB;AACnB,IAAI,YAA4B,OAAO,CAAC,MAAM,IAAI;AAAA,EAChD,UAAU;AAAA,EACV;AAAA,EACA;AACF,GAAG,OAAO;AACR,MAAI,IAAI;AACN,WAAO;AAAA,EACT;AACA,SAAO,GAAG,SAAS,GAAG,MAAM,MAAM,EAAE,GAAG,IAAI,IAAI,EAAE,IAAI,OAAO,GAAG,SAAS,IAAI,MAAM,KAAK,EAAE;AAC3F,GAAG,WAAW;AACd,SAAS,oBAAoB,WAAW;AACtC,SAAO,aAAa;AACtB;AACA,OAAO,qBAAqB,qBAAqB;", "names": ["_a"]}