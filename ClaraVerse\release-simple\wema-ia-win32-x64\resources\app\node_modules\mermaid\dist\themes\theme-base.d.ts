export function getThemeVariables(userOverrides: any): Theme;
declare class Theme {
    /** # Base variables */
    /**
     * - Background - used to know what the background color is of the diagram. This is used for
     *   deducing colors for instance line color. Default value is #f4f4f4.
     */
    background: string;
    primaryColor: string;
    noteBkgColor: string;
    noteTextColor: string;
    THEME_COLOR_LIMIT: number;
    fontFamily: string;
    fontSize: string;
    updateColors(): void;
    primaryTextColor: any;
    secondaryColor: any;
    tertiaryColor: any;
    primaryBorderColor: any;
    secondaryBorderColor: any;
    tertiaryBorderColor: any;
    noteBorderColor: any;
    secondaryTextColor: any;
    tertiaryTextColor: any;
    lineColor: any;
    arrowheadColor: any;
    textColor: any;
    border2: any;
    nodeBkg: any;
    mainBkg: any;
    nodeBorder: any;
    clusterBkg: any;
    clusterBorder: any;
    defaultLinkColor: any;
    titleColor: any;
    edgeLabelBackground: any;
    nodeTextColor: any;
    actorBorder: any;
    actorBkg: any;
    actorTextColor: any;
    actorLineColor: any;
    labelBoxBkgColor: any;
    signalColor: any;
    signalTextColor: any;
    labelBoxBorderColor: any;
    labelTextColor: any;
    loopTextColor: any;
    activationBorderColor: any;
    activationBkgColor: any;
    sequenceNumberColor: any;
    sectionBkgColor: any;
    altSectionBkgColor: any;
    sectionBkgColor2: any;
    excludeBkgColor: any;
    taskBorderColor: any;
    taskBkgColor: any;
    activeTaskBorderColor: any;
    activeTaskBkgColor: any;
    gridColor: any;
    doneTaskBkgColor: any;
    doneTaskBorderColor: any;
    critBorderColor: any;
    critBkgColor: any;
    todayLineColor: any;
    taskTextColor: any;
    taskTextOutsideColor: any;
    taskTextLightColor: any;
    taskTextDarkColor: any;
    taskTextClickableColor: any;
    personBorder: any;
    personBkg: any;
    rowOdd: any;
    rowEven: any;
    transitionColor: any;
    transitionLabelColor: any;
    stateLabelColor: any;
    stateBkg: any;
    labelBackgroundColor: any;
    compositeBackground: any;
    altBackground: any;
    compositeTitleBackground: any;
    compositeBorder: any;
    innerEndBackground: any;
    errorBkgColor: any;
    errorTextColor: any;
    specialStateColor: any;
    cScale0: any;
    cScale1: any;
    cScale2: any;
    cScale3: any;
    cScale4: any;
    cScale5: any;
    cScale6: any;
    cScale7: any;
    cScale8: any;
    cScale9: any;
    cScale10: any;
    cScale11: any;
    scaleLabelColor: any;
    classText: any;
    fillType0: any;
    fillType1: any;
    fillType2: any;
    fillType3: any;
    fillType4: any;
    fillType5: any;
    fillType6: any;
    fillType7: any;
    pie1: any;
    pie2: any;
    pie3: any;
    pie4: any;
    pie5: any;
    pie6: any;
    pie7: any;
    pie8: any;
    pie9: any;
    pie10: any;
    pie11: any;
    pie12: any;
    pieTitleTextSize: any;
    pieTitleTextColor: any;
    pieSectionTextSize: any;
    pieSectionTextColor: any;
    pieLegendTextSize: any;
    pieLegendTextColor: any;
    pieStrokeColor: any;
    pieStrokeWidth: any;
    pieOuterStrokeWidth: any;
    pieOuterStrokeColor: any;
    pieOpacity: any;
    radar: any;
    archEdgeColor: any;
    archEdgeArrowColor: any;
    archEdgeWidth: any;
    archGroupBorderColor: any;
    archGroupBorderWidth: any;
    quadrant1Fill: any;
    quadrant2Fill: any;
    quadrant3Fill: any;
    quadrant4Fill: any;
    quadrant1TextFill: any;
    quadrant2TextFill: any;
    quadrant3TextFill: any;
    quadrant4TextFill: any;
    quadrantPointFill: any;
    quadrantPointTextFill: any;
    quadrantXAxisTextFill: any;
    quadrantYAxisTextFill: any;
    quadrantInternalBorderStrokeFill: any;
    quadrantExternalBorderStrokeFill: any;
    quadrantTitleFill: any;
    xyChart: any;
    requirementBackground: any;
    requirementBorderColor: any;
    requirementBorderSize: any;
    requirementTextColor: any;
    relationColor: any;
    relationLabelBackground: any;
    relationLabelColor: any;
    git0: any;
    git1: any;
    git2: any;
    git3: any;
    git4: any;
    git5: any;
    git6: any;
    git7: any;
    gitInv0: any;
    gitInv1: any;
    gitInv2: any;
    gitInv3: any;
    gitInv4: any;
    gitInv5: any;
    gitInv6: any;
    gitInv7: any;
    branchLabelColor: any;
    gitBranchLabel0: any;
    gitBranchLabel1: any;
    gitBranchLabel2: any;
    gitBranchLabel3: any;
    gitBranchLabel4: any;
    gitBranchLabel5: any;
    gitBranchLabel6: any;
    gitBranchLabel7: any;
    tagLabelColor: any;
    tagLabelBackground: any;
    tagLabelBorder: any;
    tagLabelFontSize: any;
    commitLabelColor: any;
    commitLabelBackground: any;
    commitLabelFontSize: any;
    attributeBackgroundColorOdd: any;
    attributeBackgroundColorEven: any;
    calculate(overrides: any): void;
}
export {};
