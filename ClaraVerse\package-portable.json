{"appId": "com.wema-ia.app", "productName": "WeMa IA", "directories": {"buildResources": "assets", "output": "release"}, "compression": "store", "portable": {"artifactName": "WeMa_IA_Portable_${version}.${ext}"}, "win": {"target": [{"target": "portable", "arch": ["x64"]}], "icon": "public/wema-icon-256.ico", "extraResources": [{"from": "py_backend", "to": "py_backend", "filter": ["**/*"]}], "files": ["dist/**/*", "public/electron.cjs", "public/wema-logo-light.png", "public/wema-logo-dark.png", "public/wema-icon-256.ico", "!node_modules", "!src", "!**/*.map", "!**/*.ts", "!**/*.tsx"]}}