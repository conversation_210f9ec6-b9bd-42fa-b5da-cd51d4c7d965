"""
🌐 Service de scraping web pour WeMa IA
Extrait le contenu complet des pages web trouvées par la recherche
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from urllib.parse import urljoin, urlparse
import re
from bs4 import BeautifulSoup
import time

logger = logging.getLogger(__name__)

@dataclass
class ScrapedContent:
    url: str
    title: str
    content: str
    meta_description: Optional[str] = None
    word_count: int = 0
    scrape_time: float = 0
    error: Optional[str] = None

class WebScraperService:
    """Service de scraping web intelligent"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 WeMa-IA/1.0'
        self.timeout = aiohttp.ClientTimeout(total=8)  # Timeout plus court
        
    async def __aenter__(self):
        """Gestionnaire de contexte async"""
        self.session = aiohttp.ClientSession(
            timeout=self.timeout,
            headers={'User-Agent': self.user_agent}
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Fermeture de la session"""
        if self.session:
            await self.session.close()
    
    async def scrape_urls(self, urls: List[str], max_concurrent: int = 3) -> List[ScrapedContent]:
        """Scraper plusieurs URLs en parallèle"""
        if not urls:
            return []
        
        logger.info(f"🌐 Scraping de {len(urls)} URLs...")
        
        # Limiter le nombre d'URLs pour éviter la surcharge
        urls_to_scrape = urls[:2]  # Max 2 URLs pour plus de rapidité
        
        # Créer un semaphore pour limiter la concurrence
        semaphore = asyncio.Semaphore(max_concurrent)
        
        # Lancer le scraping en parallèle
        tasks = [self._scrape_single_url(url, semaphore) for url in urls_to_scrape]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filtrer les résultats valides
        scraped_contents = []
        for result in results:
            if isinstance(result, ScrapedContent) and not result.error:
                scraped_contents.append(result)
        
        logger.info(f"✅ Scraping terminé: {len(scraped_contents)}/{len(urls_to_scrape)} URLs réussies")
        return scraped_contents
    
    async def _scrape_single_url(self, url: str, semaphore: asyncio.Semaphore) -> ScrapedContent:
        """Scraper une seule URL"""
        async with semaphore:
            start_time = time.time()
            
            try:
                logger.debug(f"🌐 Scraping: {url}")
                
                # Vérifier que l'URL est valide
                if not self._is_valid_url(url):
                    return ScrapedContent(
                        url=url,
                        title="",
                        content="",
                        error="URL invalide"
                    )
                
                # Effectuer la requête
                async with self.session.get(url) as response:
                    if response.status != 200:
                        return ScrapedContent(
                            url=url,
                            title="",
                            content="",
                            error=f"HTTP {response.status}"
                        )
                    
                    # Vérifier le type de contenu
                    content_type = response.headers.get('content-type', '').lower()
                    if 'text/html' not in content_type:
                        return ScrapedContent(
                            url=url,
                            title="",
                            content="",
                            error="Contenu non-HTML"
                        )
                    
                    # Lire le contenu avec gestion d'encodage robuste
                    try:
                        html_content = await response.text(encoding='utf-8')
                    except UnicodeDecodeError:
                        try:
                            # Essayer avec l'encodage détecté par aiohttp
                            html_content = await response.text()
                        except UnicodeDecodeError:
                            # En dernier recours, lire les bytes et décoder avec erreurs ignorées
                            raw_content = await response.read()
                            html_content = raw_content.decode('utf-8', errors='ignore')
                    
                    # Parser avec BeautifulSoup
                    soup = BeautifulSoup(html_content, 'html.parser')
                    
                    # Extraire le contenu
                    title = self._extract_title(soup)
                    content = self._extract_content(soup)
                    meta_description = self._extract_meta_description(soup)
                    
                    scrape_time = time.time() - start_time
                    
                    return ScrapedContent(
                        url=url,
                        title=title,
                        content=content,
                        meta_description=meta_description,
                        word_count=len(content.split()),
                        scrape_time=scrape_time
                    )
                    
            except asyncio.TimeoutError:
                return ScrapedContent(
                    url=url,
                    title="",
                    content="",
                    error="Timeout"
                )
            except Exception as e:
                logger.warning(f"⚠️ Erreur scraping {url}: {e}")
                return ScrapedContent(
                    url=url,
                    title="",
                    content="",
                    error=str(e)
                )
    
    def _is_valid_url(self, url: str) -> bool:
        """Vérifier si l'URL est valide et scrapable"""
        try:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                return False
            
            # Exclure certains types de fichiers
            excluded_extensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.zip', '.rar']
            if any(url.lower().endswith(ext) for ext in excluded_extensions):
                return False
            
            return True
        except:
            return False
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """Extraire le titre de la page"""
        # Essayer différents sélecteurs
        title_selectors = [
            'title',
            'h1',
            '[property="og:title"]',
            '[name="twitter:title"]'
        ]
        
        for selector in title_selectors:
            element = soup.select_one(selector)
            if element:
                title = element.get_text(strip=True) if hasattr(element, 'get_text') else element.get('content', '')
                if title:
                    return title[:200]  # Limiter la longueur
        
        return "Sans titre"
    
    def _extract_meta_description(self, soup: BeautifulSoup) -> Optional[str]:
        """Extraire la meta description"""
        meta_selectors = [
            '[name="description"]',
            '[property="og:description"]',
            '[name="twitter:description"]'
        ]
        
        for selector in meta_selectors:
            element = soup.select_one(selector)
            if element:
                description = element.get('content', '')
                if description:
                    return description[:300]
        
        return None
    
    def _extract_content(self, soup: BeautifulSoup) -> str:
        """Extraire le contenu principal de la page"""
        # Supprimer les éléments indésirables
        for element in soup(['script', 'style', 'nav', 'header', 'footer', 'aside', 'iframe', 'noscript']):
            element.decompose()
        
        # Essayer différents sélecteurs pour le contenu principal
        content_selectors = [
            'article',
            '[role="main"]',
            'main',
            '.content',
            '.post-content',
            '.entry-content',
            '.article-content',
            '#content',
            '.main-content'
        ]
        
        content_text = ""
        
        # Essayer les sélecteurs spécifiques d'abord
        for selector in content_selectors:
            elements = soup.select(selector)
            if elements:
                for element in elements:
                    text = element.get_text(separator=' ', strip=True)
                    if len(text) > len(content_text):
                        content_text = text
                break
        
        # Si aucun sélecteur spécifique ne fonctionne, utiliser le body
        if not content_text:
            body = soup.find('body')
            if body:
                content_text = body.get_text(separator=' ', strip=True)
        
        # Nettoyer le texte
        content_text = self._clean_text(content_text)
        
        # Limiter la longueur (garder les 3000 premiers caractères)
        if len(content_text) > 3000:
            content_text = content_text[:3000] + "..."
        
        return content_text
    
    def _clean_text(self, text: str) -> str:
        """Nettoyer le texte extrait"""
        if not text:
            return ""
        
        # Supprimer les espaces multiples
        text = re.sub(r'\s+', ' ', text)
        
        # Supprimer les caractères de contrôle
        text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
        
        # Supprimer les lignes vides multiples
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        return text.strip()
    
    def format_scraped_content(self, scraped_contents: List[ScrapedContent]) -> str:
        """Formater le contenu scrapé pour l'IA"""
        if not scraped_contents:
            return ""
        
        formatted = "📄 **Contenu détaillé des pages web:**\n\n"
        
        for i, content in enumerate(scraped_contents, 1):
            formatted += f"**{i}. {content.title}**\n"
            formatted += f"🔗 {content.url}\n"
            
            if content.meta_description:
                formatted += f"📝 {content.meta_description}\n"
            
            if content.content:
                # Prendre les premiers paragraphes les plus informatifs
                paragraphs = content.content.split('\n')
                relevant_paragraphs = []
                
                for paragraph in paragraphs:
                    if len(paragraph.strip()) > 50:  # Ignorer les paragraphes trop courts
                        relevant_paragraphs.append(paragraph.strip())
                        if len(relevant_paragraphs) >= 3:  # Max 3 paragraphes par page
                            break
                
                if relevant_paragraphs:
                    formatted += f"📖 {' '.join(relevant_paragraphs)}\n"
            
            formatted += f"📊 {content.word_count} mots\n\n"
        
        return formatted

# Instance globale
web_scraper_service = WebScraperService()

async def scrape_search_results(urls: List[str]) -> List[ScrapedContent]:
    """Fonction utilitaire pour scraper les résultats de recherche"""
    async with WebScraperService() as scraper:
        return await scraper.scrape_urls(urls)

# Instance globale
web_scraper_service = WebScraperService()

async def scrape_search_results(urls: List[str]) -> List[ScrapedContent]:
    """Fonction utilitaire pour scraper les résultats de recherche"""
    async with WebScraperService() as scraper:
        return await scraper.scrape_urls(urls)
