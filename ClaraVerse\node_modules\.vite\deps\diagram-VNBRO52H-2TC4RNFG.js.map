{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/diagram-VNBRO52H.mjs"], "sourcesContent": ["import {\n  populateCommonDb\n} from \"./chunk-4BMEZGHF.mjs\";\nimport {\n  cleanAndMerge\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-7B677QYD.mjs\";\nimport {\n  __name,\n  clear,\n  configureSvgSize,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/packet/db.ts\nvar defaultPacketData = {\n  packet: []\n};\nvar data = structuredClone(defaultPacketData);\nvar DEFAULT_PACKET_CONFIG = defaultConfig_default.packet;\nvar getConfig2 = /* @__PURE__ */ __name(() => {\n  const config = cleanAndMerge({\n    ...DEFAULT_PACKET_CONFIG,\n    ...getConfig().packet\n  });\n  if (config.showBits) {\n    config.paddingY += 10;\n  }\n  return config;\n}, \"getConfig\");\nvar getPacket = /* @__PURE__ */ __name(() => data.packet, \"getPacket\");\nvar pushWord = /* @__PURE__ */ __name((word) => {\n  if (word.length > 0) {\n    data.packet.push(word);\n  }\n}, \"pushWord\");\nvar clear2 = /* @__PURE__ */ __name(() => {\n  clear();\n  data = structuredClone(defaultPacketData);\n}, \"clear\");\nvar db = {\n  pushWord,\n  getPacket,\n  getConfig: getConfig2,\n  clear: clear2,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription\n};\n\n// src/diagrams/packet/parser.ts\nimport { parse } from \"@mermaid-js/parser\";\nvar maxPacketSize = 1e4;\nvar populate = /* @__PURE__ */ __name((ast) => {\n  populateCommonDb(ast, db);\n  let lastByte = -1;\n  let word = [];\n  let row = 1;\n  const { bitsPerRow } = db.getConfig();\n  for (let { start, end, label } of ast.blocks) {\n    if (end && end < start) {\n      throw new Error(`Packet block ${start} - ${end} is invalid. End must be greater than start.`);\n    }\n    if (start !== lastByte + 1) {\n      throw new Error(\n        `Packet block ${start} - ${end ?? start} is not contiguous. It should start from ${lastByte + 1}.`\n      );\n    }\n    lastByte = end ?? start;\n    log.debug(`Packet block ${start} - ${lastByte} with label ${label}`);\n    while (word.length <= bitsPerRow + 1 && db.getPacket().length < maxPacketSize) {\n      const [block, nextBlock] = getNextFittingBlock({ start, end, label }, row, bitsPerRow);\n      word.push(block);\n      if (block.end + 1 === row * bitsPerRow) {\n        db.pushWord(word);\n        word = [];\n        row++;\n      }\n      if (!nextBlock) {\n        break;\n      }\n      ({ start, end, label } = nextBlock);\n    }\n  }\n  db.pushWord(word);\n}, \"populate\");\nvar getNextFittingBlock = /* @__PURE__ */ __name((block, row, bitsPerRow) => {\n  if (block.end === void 0) {\n    block.end = block.start;\n  }\n  if (block.start > block.end) {\n    throw new Error(`Block start ${block.start} is greater than block end ${block.end}.`);\n  }\n  if (block.end + 1 <= row * bitsPerRow) {\n    return [block, void 0];\n  }\n  return [\n    {\n      start: block.start,\n      end: row * bitsPerRow - 1,\n      label: block.label\n    },\n    {\n      start: row * bitsPerRow,\n      end: block.end,\n      label: block.label\n    }\n  ];\n}, \"getNextFittingBlock\");\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"packet\", input);\n    log.debug(ast);\n    populate(ast);\n  }, \"parse\")\n};\n\n// src/diagrams/packet/renderer.ts\nvar draw = /* @__PURE__ */ __name((_text, id, _version, diagram2) => {\n  const db2 = diagram2.db;\n  const config = db2.getConfig();\n  const { rowHeight, paddingY, bitWidth, bitsPerRow } = config;\n  const words = db2.getPacket();\n  const title = db2.getDiagramTitle();\n  const totalRowHeight = rowHeight + paddingY;\n  const svgHeight = totalRowHeight * (words.length + 1) - (title ? 0 : rowHeight);\n  const svgWidth = bitWidth * bitsPerRow + 2;\n  const svg = selectSvgElement(id);\n  svg.attr(\"viewbox\", `0 0 ${svgWidth} ${svgHeight}`);\n  configureSvgSize(svg, svgHeight, svgWidth, config.useMaxWidth);\n  for (const [word, packet] of words.entries()) {\n    drawWord(svg, packet, word, config);\n  }\n  svg.append(\"text\").text(title).attr(\"x\", svgWidth / 2).attr(\"y\", svgHeight - totalRowHeight / 2).attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").attr(\"class\", \"packetTitle\");\n}, \"draw\");\nvar drawWord = /* @__PURE__ */ __name((svg, word, rowNumber, { rowHeight, paddingX, paddingY, bitWidth, bitsPerRow, showBits }) => {\n  const group = svg.append(\"g\");\n  const wordY = rowNumber * (rowHeight + paddingY) + paddingY;\n  for (const block of word) {\n    const blockX = block.start % bitsPerRow * bitWidth + 1;\n    const width = (block.end - block.start + 1) * bitWidth - paddingX;\n    group.append(\"rect\").attr(\"x\", blockX).attr(\"y\", wordY).attr(\"width\", width).attr(\"height\", rowHeight).attr(\"class\", \"packetBlock\");\n    group.append(\"text\").attr(\"x\", blockX + width / 2).attr(\"y\", wordY + rowHeight / 2).attr(\"class\", \"packetLabel\").attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").text(block.label);\n    if (!showBits) {\n      continue;\n    }\n    const isSingleBlock = block.end === block.start;\n    const bitNumberY = wordY - 2;\n    group.append(\"text\").attr(\"x\", blockX + (isSingleBlock ? width / 2 : 0)).attr(\"y\", bitNumberY).attr(\"class\", \"packetByte start\").attr(\"dominant-baseline\", \"auto\").attr(\"text-anchor\", isSingleBlock ? \"middle\" : \"start\").text(block.start);\n    if (!isSingleBlock) {\n      group.append(\"text\").attr(\"x\", blockX + width).attr(\"y\", bitNumberY).attr(\"class\", \"packetByte end\").attr(\"dominant-baseline\", \"auto\").attr(\"text-anchor\", \"end\").text(block.end);\n    }\n  }\n}, \"drawWord\");\nvar renderer = { draw };\n\n// src/diagrams/packet/styles.ts\nvar defaultPacketStyleOptions = {\n  byteFontSize: \"10px\",\n  startByteColor: \"black\",\n  endByteColor: \"black\",\n  labelColor: \"black\",\n  labelFontSize: \"12px\",\n  titleColor: \"black\",\n  titleFontSize: \"14px\",\n  blockStrokeColor: \"black\",\n  blockStrokeWidth: \"1\",\n  blockFillColor: \"#efefef\"\n};\nvar styles = /* @__PURE__ */ __name(({ packet } = {}) => {\n  const options = cleanAndMerge(defaultPacketStyleOptions, packet);\n  return `\n\t.packetByte {\n\t\tfont-size: ${options.byteFontSize};\n\t}\n\t.packetByte.start {\n\t\tfill: ${options.startByteColor};\n\t}\n\t.packetByte.end {\n\t\tfill: ${options.endByteColor};\n\t}\n\t.packetLabel {\n\t\tfill: ${options.labelColor};\n\t\tfont-size: ${options.labelFontSize};\n\t}\n\t.packetTitle {\n\t\tfill: ${options.titleColor};\n\t\tfont-size: ${options.titleFontSize};\n\t}\n\t.packetBlock {\n\t\tstroke: ${options.blockStrokeColor};\n\t\tstroke-width: ${options.blockStrokeWidth};\n\t\tfill: ${options.blockFillColor};\n\t}\n\t`;\n}, \"styles\");\n\n// src/diagrams/packet/diagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer,\n  styles\n};\nexport {\n  diagram\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,IAAI,oBAAoB;AAAA,EACtB,QAAQ,CAAC;AACX;AACA,IAAI,OAAO,gBAAgB,iBAAiB;AAC5C,IAAI,wBAAwB,sBAAsB;AAClD,IAAI,aAA6B,OAAO,MAAM;AAC5C,QAAM,SAAS,cAAc;AAAA,IAC3B,GAAG;AAAA,IACH,GAAG,UAAU,EAAE;AAAA,EACjB,CAAC;AACD,MAAI,OAAO,UAAU;AACnB,WAAO,YAAY;AAAA,EACrB;AACA,SAAO;AACT,GAAG,WAAW;AACd,IAAI,YAA4B,OAAO,MAAM,KAAK,QAAQ,WAAW;AACrE,IAAI,WAA2B,OAAO,CAAC,SAAS;AAC9C,MAAI,KAAK,SAAS,GAAG;AACnB,SAAK,OAAO,KAAK,IAAI;AAAA,EACvB;AACF,GAAG,UAAU;AACb,IAAI,SAAyB,OAAO,MAAM;AACxC,QAAM;AACN,SAAO,gBAAgB,iBAAiB;AAC1C,GAAG,OAAO;AACV,IAAI,KAAK;AAAA,EACP;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAIA,IAAI,gBAAgB;AACpB,IAAI,WAA2B,OAAO,CAAC,QAAQ;AAC7C,mBAAiB,KAAK,EAAE;AACxB,MAAI,WAAW;AACf,MAAI,OAAO,CAAC;AACZ,MAAI,MAAM;AACV,QAAM,EAAE,WAAW,IAAI,GAAG,UAAU;AACpC,WAAS,EAAE,OAAO,KAAK,MAAM,KAAK,IAAI,QAAQ;AAC5C,QAAI,OAAO,MAAM,OAAO;AACtB,YAAM,IAAI,MAAM,gBAAgB,KAAK,MAAM,GAAG,8CAA8C;AAAA,IAC9F;AACA,QAAI,UAAU,WAAW,GAAG;AAC1B,YAAM,IAAI;AAAA,QACR,gBAAgB,KAAK,MAAM,OAAO,KAAK,4CAA4C,WAAW,CAAC;AAAA,MACjG;AAAA,IACF;AACA,eAAW,OAAO;AAClB,QAAI,MAAM,gBAAgB,KAAK,MAAM,QAAQ,eAAe,KAAK,EAAE;AACnE,WAAO,KAAK,UAAU,aAAa,KAAK,GAAG,UAAU,EAAE,SAAS,eAAe;AAC7E,YAAM,CAAC,OAAO,SAAS,IAAI,oBAAoB,EAAE,OAAO,KAAK,MAAM,GAAG,KAAK,UAAU;AACrF,WAAK,KAAK,KAAK;AACf,UAAI,MAAM,MAAM,MAAM,MAAM,YAAY;AACtC,WAAG,SAAS,IAAI;AAChB,eAAO,CAAC;AACR;AAAA,MACF;AACA,UAAI,CAAC,WAAW;AACd;AAAA,MACF;AACA,OAAC,EAAE,OAAO,KAAK,MAAM,IAAI;AAAA,IAC3B;AAAA,EACF;AACA,KAAG,SAAS,IAAI;AAClB,GAAG,UAAU;AACb,IAAI,sBAAsC,OAAO,CAAC,OAAO,KAAK,eAAe;AAC3E,MAAI,MAAM,QAAQ,QAAQ;AACxB,UAAM,MAAM,MAAM;AAAA,EACpB;AACA,MAAI,MAAM,QAAQ,MAAM,KAAK;AAC3B,UAAM,IAAI,MAAM,eAAe,MAAM,KAAK,8BAA8B,MAAM,GAAG,GAAG;AAAA,EACtF;AACA,MAAI,MAAM,MAAM,KAAK,MAAM,YAAY;AACrC,WAAO,CAAC,OAAO,MAAM;AAAA,EACvB;AACA,SAAO;AAAA,IACL;AAAA,MACE,OAAO,MAAM;AAAA,MACb,KAAK,MAAM,aAAa;AAAA,MACxB,OAAO,MAAM;AAAA,IACf;AAAA,IACA;AAAA,MACE,OAAO,MAAM;AAAA,MACb,KAAK,MAAM;AAAA,MACX,OAAO,MAAM;AAAA,IACf;AAAA,EACF;AACF,GAAG,qBAAqB;AACxB,IAAI,SAAS;AAAA,EACX,OAAuB,OAAO,OAAO,UAAU;AAC7C,UAAM,MAAM,MAAM,MAAM,UAAU,KAAK;AACvC,QAAI,MAAM,GAAG;AACb,aAAS,GAAG;AAAA,EACd,GAAG,OAAO;AACZ;AAGA,IAAI,OAAuB,OAAO,CAAC,OAAO,IAAI,UAAU,aAAa;AACnE,QAAM,MAAM,SAAS;AACrB,QAAM,SAAS,IAAI,UAAU;AAC7B,QAAM,EAAE,WAAW,UAAU,UAAU,WAAW,IAAI;AACtD,QAAM,QAAQ,IAAI,UAAU;AAC5B,QAAM,QAAQ,IAAI,gBAAgB;AAClC,QAAM,iBAAiB,YAAY;AACnC,QAAM,YAAY,kBAAkB,MAAM,SAAS,MAAM,QAAQ,IAAI;AACrE,QAAM,WAAW,WAAW,aAAa;AACzC,QAAM,MAAM,iBAAiB,EAAE;AAC/B,MAAI,KAAK,WAAW,OAAO,QAAQ,IAAI,SAAS,EAAE;AAClD,mBAAiB,KAAK,WAAW,UAAU,OAAO,WAAW;AAC7D,aAAW,CAAC,MAAM,MAAM,KAAK,MAAM,QAAQ,GAAG;AAC5C,aAAS,KAAK,QAAQ,MAAM,MAAM;AAAA,EACpC;AACA,MAAI,OAAO,MAAM,EAAE,KAAK,KAAK,EAAE,KAAK,KAAK,WAAW,CAAC,EAAE,KAAK,KAAK,YAAY,iBAAiB,CAAC,EAAE,KAAK,qBAAqB,QAAQ,EAAE,KAAK,eAAe,QAAQ,EAAE,KAAK,SAAS,aAAa;AAChM,GAAG,MAAM;AACT,IAAI,WAA2B,OAAO,CAAC,KAAK,MAAM,WAAW,EAAE,WAAW,UAAU,UAAU,UAAU,YAAY,SAAS,MAAM;AACjI,QAAM,QAAQ,IAAI,OAAO,GAAG;AAC5B,QAAM,QAAQ,aAAa,YAAY,YAAY;AACnD,aAAW,SAAS,MAAM;AACxB,UAAM,SAAS,MAAM,QAAQ,aAAa,WAAW;AACrD,UAAM,SAAS,MAAM,MAAM,MAAM,QAAQ,KAAK,WAAW;AACzD,UAAM,OAAO,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,KAAK,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,SAAS,EAAE,KAAK,SAAS,aAAa;AAClI,UAAM,OAAO,MAAM,EAAE,KAAK,KAAK,SAAS,QAAQ,CAAC,EAAE,KAAK,KAAK,QAAQ,YAAY,CAAC,EAAE,KAAK,SAAS,aAAa,EAAE,KAAK,qBAAqB,QAAQ,EAAE,KAAK,eAAe,QAAQ,EAAE,KAAK,MAAM,KAAK;AACnM,QAAI,CAAC,UAAU;AACb;AAAA,IACF;AACA,UAAM,gBAAgB,MAAM,QAAQ,MAAM;AAC1C,UAAM,aAAa,QAAQ;AAC3B,UAAM,OAAO,MAAM,EAAE,KAAK,KAAK,UAAU,gBAAgB,QAAQ,IAAI,EAAE,EAAE,KAAK,KAAK,UAAU,EAAE,KAAK,SAAS,kBAAkB,EAAE,KAAK,qBAAqB,MAAM,EAAE,KAAK,eAAe,gBAAgB,WAAW,OAAO,EAAE,KAAK,MAAM,KAAK;AAC3O,QAAI,CAAC,eAAe;AAClB,YAAM,OAAO,MAAM,EAAE,KAAK,KAAK,SAAS,KAAK,EAAE,KAAK,KAAK,UAAU,EAAE,KAAK,SAAS,gBAAgB,EAAE,KAAK,qBAAqB,MAAM,EAAE,KAAK,eAAe,KAAK,EAAE,KAAK,MAAM,GAAG;AAAA,IAClL;AAAA,EACF;AACF,GAAG,UAAU;AACb,IAAI,WAAW,EAAE,KAAK;AAGtB,IAAI,4BAA4B;AAAA,EAC9B,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,gBAAgB;AAClB;AACA,IAAI,SAAyB,OAAO,CAAC,EAAE,OAAO,IAAI,CAAC,MAAM;AACvD,QAAM,UAAU,cAAc,2BAA2B,MAAM;AAC/D,SAAO;AAAA;AAAA,eAEM,QAAQ,YAAY;AAAA;AAAA;AAAA,UAGzB,QAAQ,cAAc;AAAA;AAAA;AAAA,UAGtB,QAAQ,YAAY;AAAA;AAAA;AAAA,UAGpB,QAAQ,UAAU;AAAA,eACb,QAAQ,aAAa;AAAA;AAAA;AAAA,UAG1B,QAAQ,UAAU;AAAA,eACb,QAAQ,aAAa;AAAA;AAAA;AAAA,YAGxB,QAAQ,gBAAgB;AAAA,kBAClB,QAAQ,gBAAgB;AAAA,UAChC,QAAQ,cAAc;AAAA;AAAA;AAGhC,GAAG,QAAQ;AAGX,IAAI,UAAU;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}