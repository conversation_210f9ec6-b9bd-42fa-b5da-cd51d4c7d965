{"rustc": 16591470773350601817, "features": "[\"connect\", \"default\", \"handshake\", \"stream\"]", "declared_features": "[\"__rustls-tls\", \"connect\", \"default\", \"handshake\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"webpki-roots\"]", "target": 2433367608443825, "profile": 2040997289075261528, "path": 12168125735174357373, "deps": [[5986029879202738730, "log", false, 2351528888146949315], [8258418851280347661, "tungstenite", false, 2349924300771542039], [10629569228670356391, "futures_util", false, 18155214397916674359], [12393800526703971956, "tokio", false, 2142785373852924899]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tokio-tungstenite-7f14d504b18d3b37\\dep-lib-tokio_tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}