"use strict";(self.webpackChunkdocumentation=self.webpackChunkdocumentation||[]).push([[237],{2237:(e,t,n)=>{n.r(t),n.d(t,{default:()=>d});n(6540);var i=n(1312),o=n(5500),s=n(1656),a=n(3363),r=n(4848);function d(){const e=(0,i.T)({id:"theme.NotFound.title",message:"Page Not Found"});return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.be,{title:e}),(0,r.jsx)(s.A,{children:(0,r.jsx)(a.A,{})})]})}},3363:(e,t,n)=>{n.d(t,{A:()=>r});n(6540);var i=n(4164),o=n(1312),s=n(1107),a=n(4848);function r({className:e}){return(0,a.jsx)("main",{className:(0,i.A)("container margin-vert--xl",e),children:(0,a.jsx)("div",{className:"row",children:(0,a.jsxs)("div",{className:"col col--6 col--offset-3",children:[(0,a.jsx)(s.A,{as:"h1",className:"hero__title",children:(0,a.jsx)(o.A,{id:"theme.NotFound.title",description:"The title of the 404 page",children:"Page Not Found"})}),(0,a.jsx)("p",{children:(0,a.jsx)(o.A,{id:"theme.NotFound.p1",description:"The first paragraph of the 404 page",children:"We could not find what you were looking for."})}),(0,a.jsx)("p",{children:(0,a.jsx)(o.A,{id:"theme.NotFound.p2",description:"The 2nd paragraph of the 404 page",children:"Please contact the owner of the site that linked you to the original URL and let them know their link is broken."})})]})})})}}}]);