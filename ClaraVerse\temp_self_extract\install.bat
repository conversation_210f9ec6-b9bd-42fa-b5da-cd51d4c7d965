@echo off
title WeMa IA - Installation Automatique
color 0A

echo.
echo ========================================
echo    WeMa IA - Assistant IA Professionnel
echo ========================================
echo.

set "INSTALL_DIR=%APPDATA%\WeMa_IA"
set "APP_EXE=%INSTALL_DIR%\WeMa IA.exe"

REM Vérifier si déjà installé
if exist "%APP_EXE%" (
    echo Application deja installee !
    echo Lancement...
    start "" "%APP_EXE%"
    timeout /t 2 /nobreak > nul
    exit /b 0
)

echo Installation en cours...
echo Cela peut prendre quelques secondes...

REM Créer répertoire d'installation
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Extraire l'archive intégrée
echo Extraction des fichiers...
powershell -Command "Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('%~dp0wema_app.zip', '%INSTALL_DIR%')"

if errorlevel 1 (
    echo Erreur lors de l'extraction !
    pause
    exit /b 1
)

echo.
echo ✅ Installation terminee !
echo.

REM Créer raccourcis
echo Création des raccourcis...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('$env:USERPROFILE\Desktop\WeMa IA.lnk'); $Shortcut.TargetPath = '$env:APPDATA\WeMa_IA\WeMa IA.exe'; $Shortcut.Save()"

echo Lancement de WeMa IA...
start "" "%APP_EXE%"

echo.
echo 🎉 WeMa IA est maintenant installé et lancé !
echo.
echo 📍 Emplacement: %INSTALL_DIR%
echo 🖥️ Raccourci créé sur le bureau
echo.
echo Vous pouvez fermer cette fenetre.
timeout /t 3 /nobreak > nul
exit /b 0
