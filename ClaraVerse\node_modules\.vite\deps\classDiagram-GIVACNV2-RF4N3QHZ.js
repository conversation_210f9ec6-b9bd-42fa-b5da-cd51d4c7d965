import {
  ClassDB,
  classDiagram_default,
  classRenderer_v3_unified_default,
  styles_default
} from "./chunk-Q34NYN5A.js";
import "./chunk-4EHU5JI2.js";
import "./chunk-D2URHVFK.js";
import "./chunk-Z7UQ7MK4.js";
import "./chunk-MSYDKK65.js";
import "./chunk-IEEBWJWQ.js";
import "./chunk-F5VURT3L.js";
import "./chunk-GG5ZG5BN.js";
import "./chunk-XFNEK3YH.js";
import "./chunk-JLJSF2GM.js";
import "./chunk-LQXCE4Y7.js";
import {
  __name
} from "./chunk-AEQEJSV4.js";
import "./chunk-I6RL7E24.js";
import "./chunk-LTOQ36XE.js";
import "./chunk-256EKJAK.js";

// node_modules/mermaid/dist/chunks/mermaid.core/classDiagram-GIVACNV2.mjs
var diagram = {
  parser: classDiagram_default,
  get db() {
    return new ClassDB();
  },
  renderer: classRenderer_v3_unified_default,
  styles: styles_default,
  init: __name((cnf) => {
    if (!cnf.class) {
      cnf.class = {};
    }
    cnf.class.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;
  }, "init")
};
export {
  diagram
};
//# sourceMappingURL=classDiagram-GIVACNV2-RF4N3QHZ.js.map
