﻿# Header, don't edit
NLF v6
# Language ID
3098
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
1251
# RTL - anything else than RTL means LTR
-
# <AUTHOR> <EMAIL>
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
$(^Name) Инсталација
# ^UninstallCaption
$(^Name) Деинсталација
# ^LicenseSubCaption
: Договор о праву коришћења
# ^ComponentsSubCaption
: Опције инсталације
# ^DirSubCaption
: Избор фолдера за инсталацију
# ^InstallingSubCaption
: Инсталација
# ^CompletedSubCaption
: Завршена инсталација
# ^UnComponentsSubCaption
: Опције деинсталације
# ^UnDirSubCaption
: Избор фолдера за деинсталацију
# ^ConfirmSubCaption
: Потврђивање
# ^UninstallingSubCaption
: Деинсталација
# ^UnCompletedSubCaption
: Завршена деинсталација
# ^BackBtn
< Назад
# ^NextBtn
Напред >
# ^AgreeBtn
Прихватам
# ^AcceptBtn
Прихватам услове договора о праву коришћења
# ^DontAcceptBtn
Не прихватам услове договора о праву коришћења
# ^InstallBtn
Инсталирај
# ^UninstallBtn
Деинсталирај
# ^CancelBtn
Одустани
# ^CloseBtn
Затвори
# ^BrowseBtn
Избор...
# ^ShowDetailsBtn
Детаљи
# ^ClickNext
Притисните дугме „Напред“ за наставак.
# ^ClickInstall
Притисните дугме „Инсталирај“ за почетак инсталације.
# ^ClickUninstall
Притисните дугме „Деинсталирај“ за почетак деинсталације.
# ^Name
Име
# ^Completed
Завршено
# ^LicenseText
Пажљиво прочитајте договор о праву коришћења пре инсталације програма $(^NameDA). Ако прихватате све услове договора, притисните дугме „Прихватам“.
# ^LicenseTextCB
Пажљиво прочитајте договор о праву коришћења пре инсталације програма $(^NameDA). Ако прихватате све услове договора, обележите квадратић испод. $_CLICK
# ^LicenseTextRB
Пажљиво прочитајте договор о праву коришћења пре инсталације програма $(^NameDA). Ако прихватате све услове договора, изаберите прву опцију испод. $_CLICK
# ^UnLicenseText
Пажљиво прочитајте договор о праву коришћења пре деинсталације програма $(^NameDA). Ако прихватате све услове договора, притисните дугме „Прихватам“.
# ^UnLicenseTextCB
Пажљиво прочитајте договор о праву коришћења пре деинсталације програма $(^NameDA). Ако прихватате све услове договора, обележите квадратић испод. $_CLICK
# ^UnLicenseTextRB
Пажљиво прочитајте договор о праву коришћења пре деинсталације програма $(^NameDA). Ако прихватате све услове договора, изаберите прву опцију испод. $_CLICK
# ^Custom
Прилагођавање
# ^ComponentsText
Изаберите компоненте за инсталацију. Инсталирају се само означене компоненте. $_CLICK
# ^ComponentsSubText1
Изаберите тип инсталације:
# ^ComponentsSubText2_NoInstTypes
Изаберите компоненте за инсталацију: 
# ^ComponentsSubText2
Или, изаберите опционе компоненте које желите да инсталирате: 
# ^UnComponentsText
Изаберите компоненте за деинсталацију. Деинсталирају се само означене компоненте. $_CLICK
# ^UnComponentsSubText1
Изаберите тип деинсталације: 
# ^UnComponentsSubText2_NoInstTypes
Изаберите компоненте за деинсталацију: 
# ^UnComponentsSubText2
Или, изаберите опционе компоненте које желите да деинсталирате: 
# ^DirText
Програм $(^NameDA) ће бити инсталиран у наведени фолдер. За инсталацију у други фолдер притисните дугме „Избор...“ и изаберите фолдер. $_CLICK
# ^DirSubText
Фолдер
# ^DirBrowseText
Изаберите фолдер у који ћете инсталирати програм $(^NameDA):
# ^UnDirText
Програм $(^NameDA) ће бити деинсталиран из наведеног фолдера. За деинсталацију из другог фолдера притисните дугме „Избор...“ и изаберите фолдер. $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
Изаберите фолдер из кога ћете деинсталирати програм $(^NameDA):
# ^SpaceAvailable
"Слободан простор: "
# ^SpaceRequired
"Потребан простор: "
# ^UninstallingText
Програм $(^NameDA) ће бити деинсталиран из наведеног фолдера. $_CLICK
# ^UninstallingSubText
Деинсталација из: 
# ^FileError
Грешка при отварању фајла за писање: \r\n\t"$0"\r\nПритисните дугме „Одустани“ за прекид инсталације,\r\n„Понови“ за поновни покушај писања у фајл, или\r\n„Игнориши“ за прескакање овог фајла.
# ^FileError_NoIgnore
Грешка при отварању фајла за писање: \r\n\t"$0"\r\nПритисните дугме „Понови“ за поновни покушај писања у фајл, или\r\n„Одустани“ за прекид инсталирања.
# ^CantWrite
"Немогуће писање: "
# ^CopyFailed
Неуспешно копирање
# ^CopyTo
"Копирање у "
# ^Registering
"Регистровање: "
# ^Unregistering
"Дерегистровање: "
# ^SymbolNotFound
"Симбол није нађен: "
# ^CouldNotLoad
"Немогуће учитавање: "
# ^CreateFolder
"Креирање фолдера: "
# ^CreateShortcut
"Креирање пречице: "
# ^CreatedUninstaller
"Креирање деинсталера: "
# ^Delete
"Брисање фајла: "
# ^DeleteOnReboot
"Брисање при рестарту: "
# ^ErrorCreatingShortcut
"Грешка при креирању пречице: "
# ^ErrorCreating
"Грешка при креирању: "
# ^ErrorDecompressing
Грешка при отпакивању података! Оштећен инсталациони програм?
# ^ErrorRegistering
Грешка при регистровању библиотеке
# ^ExecShell
"Извршавање у окружењу: "
# ^Exec
"Извршавање: "
# ^Extract
"Отпакивање: "
# ^ErrorWriting
"Отпакивање: грешка при упису у фајл "
# ^InvalidOpcode
Оштећен инсталациони програм: неисправна команда 
# ^NoOLE
"Нема OLE подршке за: "
# ^OutputFolder
"Излазни фолдер: "
# ^RemoveFolder
"Брисање фолдера: "
# ^RenameOnReboot
"Преименовање при рестартовању: "
# ^Rename
"Преименован: "
# ^Skipped
"Прескочен: "
# ^CopyDetails
Копирај детаље у клипборд
# ^LogInstall
Води записник о процесу инсталације
# ^Byte
B
# ^Kilo
 k
# ^Mega
 M
# ^Giga
 G
