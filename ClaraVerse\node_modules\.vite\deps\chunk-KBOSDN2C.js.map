{"version": 3, "sources": ["../../@mermaid-js/parser/dist/mermaid-parser.core.mjs"], "sourcesContent": ["import {\n  GitGraphModule,\n  createGitGraphServices\n} from \"./chunks/mermaid-parser.core/chunk-2NYFTIL2.mjs\";\nimport {\n  InfoModule,\n  createInfoServices\n} from \"./chunks/mermaid-parser.core/chunk-EXZZNE6F.mjs\";\nimport {\n  PacketModule,\n  createPacketServices\n} from \"./chunks/mermaid-parser.core/chunk-V4Q32G6S.mjs\";\nimport {\n  PieModule,\n  createPieServices\n} from \"./chunks/mermaid-parser.core/chunk-ROXG7S4E.mjs\";\nimport {\n  ArchitectureModule,\n  createArchitectureServices\n} from \"./chunks/mermaid-parser.core/chunk-C4OEIS7N.mjs\";\nimport {\n  RadarModule,\n  createRadarServices\n} from \"./chunks/mermaid-parser.core/chunk-2O5ZK7RR.mjs\";\nimport {\n  AbstractMermaidTokenBuilder,\n  AbstractMermaidValueConverter,\n  Architecture,\n  ArchitectureGeneratedModule,\n  Branch,\n  Commit,\n  CommonTokenBuilder,\n  CommonValueConverter,\n  GitGraph,\n  GitGraphGeneratedModule,\n  Info,\n  InfoGeneratedModule,\n  Merge,\n  MermaidGeneratedSharedModule,\n  Packet,\n  PacketBlock,\n  PacketGeneratedModule,\n  Pie,\n  PieGeneratedModule,\n  PieSection,\n  Radar,\n  RadarGeneratedModule,\n  Statement,\n  __name,\n  isArchitecture,\n  isBranch,\n  isCommit,\n  isCommon,\n  isGitGraph,\n  isInfo,\n  isMerge,\n  isPacket,\n  isPacketBlock,\n  isPie,\n  isPieSection\n} from \"./chunks/mermaid-parser.core/chunk-7PKI6E2E.mjs\";\n\n// src/parse.ts\nvar parsers = {};\nvar initializers = {\n  info: /* @__PURE__ */ __name(async () => {\n    const { createInfoServices: createInfoServices2 } = await import(\"./chunks/mermaid-parser.core/info-4N47QTOZ.mjs\");\n    const parser = createInfoServices2().Info.parser.LangiumParser;\n    parsers.info = parser;\n  }, \"info\"),\n  packet: /* @__PURE__ */ __name(async () => {\n    const { createPacketServices: createPacketServices2 } = await import(\"./chunks/mermaid-parser.core/packet-KVYON367.mjs\");\n    const parser = createPacketServices2().Packet.parser.LangiumParser;\n    parsers.packet = parser;\n  }, \"packet\"),\n  pie: /* @__PURE__ */ __name(async () => {\n    const { createPieServices: createPieServices2 } = await import(\"./chunks/mermaid-parser.core/pie-R6RNRRYF.mjs\");\n    const parser = createPieServices2().Pie.parser.LangiumParser;\n    parsers.pie = parser;\n  }, \"pie\"),\n  architecture: /* @__PURE__ */ __name(async () => {\n    const { createArchitectureServices: createArchitectureServices2 } = await import(\"./chunks/mermaid-parser.core/architecture-4AB2E3PP.mjs\");\n    const parser = createArchitectureServices2().Architecture.parser.LangiumParser;\n    parsers.architecture = parser;\n  }, \"architecture\"),\n  gitGraph: /* @__PURE__ */ __name(async () => {\n    const { createGitGraphServices: createGitGraphServices2 } = await import(\"./chunks/mermaid-parser.core/gitGraph-O2Q2CXLX.mjs\");\n    const parser = createGitGraphServices2().GitGraph.parser.LangiumParser;\n    parsers.gitGraph = parser;\n  }, \"gitGraph\"),\n  radar: /* @__PURE__ */ __name(async () => {\n    const { createRadarServices: createRadarServices2 } = await import(\"./chunks/mermaid-parser.core/radar-MK3ICKWK.mjs\");\n    const parser = createRadarServices2().Radar.parser.LangiumParser;\n    parsers.radar = parser;\n  }, \"radar\")\n};\nasync function parse(diagramType, text) {\n  const initializer = initializers[diagramType];\n  if (!initializer) {\n    throw new Error(`Unknown diagram type: ${diagramType}`);\n  }\n  if (!parsers[diagramType]) {\n    await initializer();\n  }\n  const parser = parsers[diagramType];\n  const result = parser.parse(text);\n  if (result.lexerErrors.length > 0 || result.parserErrors.length > 0) {\n    throw new MermaidParseError(result);\n  }\n  return result.value;\n}\n__name(parse, \"parse\");\nvar MermaidParseError = class extends Error {\n  constructor(result) {\n    const lexerErrors = result.lexerErrors.map((err) => err.message).join(\"\\n\");\n    const parserErrors = result.parserErrors.map((err) => err.message).join(\"\\n\");\n    super(`Parsing failed: ${lexerErrors} ${parserErrors}`);\n    this.result = result;\n  }\n  static {\n    __name(this, \"MermaidParseError\");\n  }\n};\nexport {\n  AbstractMermaidTokenBuilder,\n  AbstractMermaidValueConverter,\n  Architecture,\n  ArchitectureGeneratedModule,\n  ArchitectureModule,\n  Branch,\n  Commit,\n  CommonTokenBuilder,\n  CommonValueConverter,\n  GitGraph,\n  GitGraphGeneratedModule,\n  GitGraphModule,\n  Info,\n  InfoGeneratedModule,\n  InfoModule,\n  Merge,\n  MermaidGeneratedSharedModule,\n  MermaidParseError,\n  Packet,\n  PacketBlock,\n  PacketGeneratedModule,\n  PacketModule,\n  Pie,\n  PieGeneratedModule,\n  PieModule,\n  PieSection,\n  Radar,\n  RadarGeneratedModule,\n  RadarModule,\n  Statement,\n  createArchitectureServices,\n  createGitGraphServices,\n  createInfoServices,\n  createPacketServices,\n  createPieServices,\n  createRadarServices,\n  isArchitecture,\n  isBranch,\n  isCommit,\n  isCommon,\n  isGitGraph,\n  isInfo,\n  isMerge,\n  isPacket,\n  isPacketBlock,\n  isPie,\n  isPieSection,\n  parse\n};\n"], "mappings": ";;;;;AA+DA,IAAI,UAAU,CAAC;AACf,IAAI,eAAe;AAAA,EACjB,MAAsB,OAAO,YAAY;AACvC,UAAM,EAAE,oBAAoB,oBAAoB,IAAI,MAAM,OAAO,6BAAgD;AACjH,UAAM,SAAS,oBAAoB,EAAE,KAAK,OAAO;AACjD,YAAQ,OAAO;AAAA,EACjB,GAAG,MAAM;AAAA,EACT,QAAwB,OAAO,YAAY;AACzC,UAAM,EAAE,sBAAsB,sBAAsB,IAAI,MAAM,OAAO,+BAAkD;AACvH,UAAM,SAAS,sBAAsB,EAAE,OAAO,OAAO;AACrD,YAAQ,SAAS;AAAA,EACnB,GAAG,QAAQ;AAAA,EACX,KAAqB,OAAO,YAAY;AACtC,UAAM,EAAE,mBAAmB,mBAAmB,IAAI,MAAM,OAAO,4BAA+C;AAC9G,UAAM,SAAS,mBAAmB,EAAE,IAAI,OAAO;AAC/C,YAAQ,MAAM;AAAA,EAChB,GAAG,KAAK;AAAA,EACR,cAA8B,OAAO,YAAY;AAC/C,UAAM,EAAE,4BAA4B,4BAA4B,IAAI,MAAM,OAAO,qCAAwD;AACzI,UAAM,SAAS,4BAA4B,EAAE,aAAa,OAAO;AACjE,YAAQ,eAAe;AAAA,EACzB,GAAG,cAAc;AAAA,EACjB,UAA0B,OAAO,YAAY;AAC3C,UAAM,EAAE,wBAAwB,wBAAwB,IAAI,MAAM,OAAO,iCAAoD;AAC7H,UAAM,SAAS,wBAAwB,EAAE,SAAS,OAAO;AACzD,YAAQ,WAAW;AAAA,EACrB,GAAG,UAAU;AAAA,EACb,OAAuB,OAAO,YAAY;AACxC,UAAM,EAAE,qBAAqB,qBAAqB,IAAI,MAAM,OAAO,8BAAiD;AACpH,UAAM,SAAS,qBAAqB,EAAE,MAAM,OAAO;AACnD,YAAQ,QAAQ;AAAA,EAClB,GAAG,OAAO;AACZ;AACA,eAAe,MAAM,aAAa,MAAM;AACtC,QAAM,cAAc,aAAa,WAAW;AAC5C,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,MAAM,yBAAyB,WAAW,EAAE;AAAA,EACxD;AACA,MAAI,CAAC,QAAQ,WAAW,GAAG;AACzB,UAAM,YAAY;AAAA,EACpB;AACA,QAAM,SAAS,QAAQ,WAAW;AAClC,QAAM,SAAS,OAAO,MAAM,IAAI;AAChC,MAAI,OAAO,YAAY,SAAS,KAAK,OAAO,aAAa,SAAS,GAAG;AACnE,UAAM,IAAI,kBAAkB,MAAM;AAAA,EACpC;AACA,SAAO,OAAO;AAChB;AACA,OAAO,OAAO,OAAO;AA/GrB;AAgHA,IAAI,qBAAoB,mBAAc,MAAM;AAAA,EAC1C,YAAY,QAAQ;AAClB,UAAM,cAAc,OAAO,YAAY,IAAI,CAAC,QAAQ,IAAI,OAAO,EAAE,KAAK,IAAI;AAC1E,UAAM,eAAe,OAAO,aAAa,IAAI,CAAC,QAAQ,IAAI,OAAO,EAAE,KAAK,IAAI;AAC5E,UAAM,mBAAmB,WAAW,IAAI,YAAY,EAAE;AACtD,SAAK,SAAS;AAAA,EAChB;AAIF,GAFI,OAAO,IAAM,mBAAmB,GARZ;", "names": []}