#!/usr/bin/env node

/**
 * 📦 CRÉATEUR PORTABLE PROFESSIONNEL WEMA IA
 * Crée un dossier portable propre et léger
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('📦 CRÉATEUR PORTABLE PROFESSIONNEL WEMA IA');
console.log('='.repeat(50));

// 1. VÉRIFIER QUE L'EXE EXISTE
const sourcePath = 'release/win-unpacked';
if (!fs.existsSync(sourcePath)) {
    console.error('❌ Dossier win-unpacked non trouvé. Lancez d\'abord un build.');
    process.exit(1);
}

// 2. CRÉER DOSSIER PORTABLE
const portableDir = 'WeMa_IA_Portable';
const downloadsPath = path.join(require('os').homedir(), 'Downloads', portableDir);

console.log('📁 Création du dossier portable...');

// Supprimer l'ancien s'il existe
if (fs.existsSync(downloadsPath)) {
    execSync(`rmdir /s /q "${downloadsPath}"`, { stdio: 'inherit' });
}

// Créer la structure
fs.mkdirSync(downloadsPath, { recursive: true });
fs.mkdirSync(path.join(downloadsPath, 'Data'), { recursive: true });
fs.mkdirSync(path.join(downloadsPath, 'Logs'), { recursive: true });

// 3. COPIER LES FICHIERS ESSENTIELS
console.log('📋 Copie des fichiers essentiels...');

const essentialFiles = [
    'WeMa IA.exe',
    'resources',
    'locales',
    '*.dll',
    '*.pak',
    '*.dat',
    '*.bin',
    '*.json'
];

// Copier tous les fichiers du dossier source
function copyRecursive(src, dest) {
    const stats = fs.statSync(src);
    if (stats.isDirectory()) {
        if (!fs.existsSync(dest)) {
            fs.mkdirSync(dest, { recursive: true });
        }
        const files = fs.readdirSync(src);
        for (const file of files) {
            copyRecursive(path.join(src, file), path.join(dest, file));
        }
    } else {
        fs.copyFileSync(src, dest);
    }
}

copyRecursive(sourcePath, downloadsPath);

// 4. CRÉER FICHIERS DE CONFIGURATION
console.log('⚙️ Création des fichiers de configuration...');

// Launcher script
const launcherScript = `@echo off
title WeMa IA - Assistant IA Professionnel
echo.
echo ========================================
echo    WeMa IA - Assistant IA Professionnel
echo ========================================
echo.
echo Demarrage de l'application...
echo.

REM Definir le repertoire de donnees
set WEMA_DATA_DIR=%~dp0Data
set WEMA_LOGS_DIR=%~dp0Logs

REM Creer les repertoires s'ils n'existent pas
if not exist "%WEMA_DATA_DIR%" mkdir "%WEMA_DATA_DIR%"
if not exist "%WEMA_LOGS_DIR%" mkdir "%WEMA_LOGS_DIR%"

REM Lancer l'application
start "" "%~dp0WeMa IA.exe"

echo Application lancee avec succes !
echo.
echo Vous pouvez fermer cette fenetre.
pause > nul
`;

fs.writeFileSync(path.join(downloadsPath, 'Lancer WeMa IA.bat'), launcherScript);

// README
const readme = `# WeMa IA - Assistant IA Professionnel (Version Portable)

## 🚀 DÉMARRAGE RAPIDE

1. Double-cliquez sur "Lancer WeMa IA.bat"
2. L'application se lance automatiquement
3. Vos données sont sauvegardées dans le dossier "Data"

## 📁 STRUCTURE DU DOSSIER

- **WeMa IA.exe** : Application principale
- **Lancer WeMa IA.bat** : Script de lancement recommandé
- **Data/** : Vos documents et conversations
- **Logs/** : Fichiers de logs pour le support
- **resources/** : Fichiers système (ne pas modifier)

## 🛠️ CONFIGURATION

### Backend Python
- Port par défaut : 5001
- Serveur LM Studio : ***********:1234
- OCR : Support PDF, Word, PowerPoint, images

### Fonctionnalités
- ✅ Chat avec documents
- ✅ OCR multi-format
- ✅ Pipeline RAG
- ✅ Compression intelligente
- ✅ Interface moderne

## 🆘 SUPPORT

En cas de problème :
1. Vérifiez les logs dans le dossier "Logs"
2. Redémarrez l'application
3. Contactez le support technique

## 📝 VERSION

Version : 0.1.2
Date : ${new Date().toLocaleDateString('fr-FR')}
Taille : Optimisée pour la distribution

---
© 2025 WeMa IA Team - Assistant IA Professionnel
`;

fs.writeFileSync(path.join(downloadsPath, 'README.md'), readme);

// 5. CALCULER LA TAILLE
console.log('📏 Calcul de la taille...');

function getFolderSize(folderPath) {
    let totalSize = 0;
    
    function calculateSize(currentPath) {
        const stats = fs.statSync(currentPath);
        if (stats.isDirectory()) {
            const files = fs.readdirSync(currentPath);
            for (const file of files) {
                calculateSize(path.join(currentPath, file));
            }
        } else {
            totalSize += stats.size;
        }
    }
    
    calculateSize(folderPath);
    return totalSize;
}

const totalSize = getFolderSize(downloadsPath);
const sizeMB = (totalSize / 1024 / 1024).toFixed(2);

console.log('');
console.log('🎉 DOSSIER PORTABLE CRÉÉ !');
console.log('='.repeat(50));
console.log(`📍 Emplacement: ${downloadsPath}`);
console.log(`📦 Taille totale: ${sizeMB} MB`);
console.log('');
console.log('📋 CONTENU:');
console.log('- WeMa IA.exe (application principale)');
console.log('- Lancer WeMa IA.bat (script de lancement)');
console.log('- README.md (documentation)');
console.log('- Data/ (dossier utilisateur)');
console.log('- Logs/ (fichiers de logs)');
console.log('');
console.log('✅ PRÊT POUR DISTRIBUTION PROFESSIONNELLE !');

if (sizeMB < 100) {
    console.log('🏆 EXCELLENT: Très léger !');
} else if (sizeMB < 300) {
    console.log('✅ BIEN: Taille acceptable');
} else {
    console.log('⚠️ ATTENTION: Encore un peu lourd');
}
