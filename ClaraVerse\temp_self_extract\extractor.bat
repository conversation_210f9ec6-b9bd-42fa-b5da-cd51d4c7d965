@echo off
setlocal EnableDelayedExpansion

title WeMa IA - Installation Automatique
color 0A

echo.
echo ========================================
echo    WeMa IA - Assistant IA Professionnel
echo ========================================
echo.
echo Installation automatique en cours...
echo.

REM Définir le répertoire d'extraction
set "EXTRACT_DIR=%APPDATA%\WeMa_IA"
set "APP_DIR=%EXTRACT_DIR%\app"
set "DATA_DIR=%EXTRACT_DIR%\Data"
set "LOGS_DIR=%EXTRACT_DIR%\Logs"

REM Créer les répertoires
if not exist "%EXTRACT_DIR%" mkdir "%EXTRACT_DIR%"
if not exist "%DATA_DIR%" mkdir "%DATA_DIR%"
if not exist "%LOGS_DIR%" mkdir "%LOGS_DIR%"

REM Vérifier si déjà installé
if exist "%APP_DIR%\WeMa IA.exe" (
    echo Application deja installee, lancement...
    goto :launch
)

echo Extraction des fichiers...
echo Cela peut prendre quelques secondes...

REM Auto-extraction avec 7zip intégré
"%~dp0\7za.exe" x "%~f0" -o"%EXTRACT_DIR%" -y > nul 2>&1

if errorlevel 1 (
    echo Erreur lors de l'extraction
    pause
    exit /b 1
)

echo.
echo ✅ Installation terminee !
echo.

:launch
echo Lancement de WeMa IA...
start "" "%APP_DIR%\WeMa IA.exe"

echo.
echo Application lancee avec succes !
echo Vous pouvez fermer cette fenetre.
echo.
echo Donnees utilisateur: %DATA_DIR%
echo Logs: %LOGS_DIR%
echo.

timeout /t 3 /nobreak > nul
exit /b 0

REM Les données binaires commencent ici
:DATA
