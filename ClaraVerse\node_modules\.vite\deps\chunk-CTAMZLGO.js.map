{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/chunk-7B677QYD.mjs"], "sourcesContent": ["import {\n  __name,\n  getConfig2 as getConfig\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/selectSvgElement.ts\nimport { select } from \"d3\";\nvar selectSvgElement = /* @__PURE__ */ __name((id) => {\n  const { securityLevel } = getConfig();\n  let root = select(\"body\");\n  if (securityLevel === \"sandbox\") {\n    const sandboxElement = select(`#i${id}`);\n    const doc = sandboxElement.node()?.contentDocument ?? document;\n    root = select(doc.body);\n  }\n  const svg = root.select(`#${id}`);\n  return svg;\n}, \"selectSvgElement\");\n\nexport {\n  selectSvgElement\n};\n"], "mappings": ";;;;;;;AAOA,IAAI,mBAAmC,OAAO,CAAC,OAAO;AAPtD;AAQE,QAAM,EAAE,cAAc,IAAI,WAAU;AACpC,MAAI,OAAO,eAAO,MAAM;AACxB,MAAI,kBAAkB,WAAW;AAC/B,UAAM,iBAAiB,eAAO,KAAK,EAAE,EAAE;AACvC,UAAM,QAAM,oBAAe,KAAK,MAApB,mBAAuB,oBAAmB;AACtD,WAAO,eAAO,IAAI,IAAI;AAAA,EACxB;AACA,QAAM,MAAM,KAAK,OAAO,IAAI,EAAE,EAAE;AAChC,SAAO;AACT,GAAG,kBAAkB;", "names": []}