{"rustc": 16591470773350601817, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 14595086563058826864, "deps": [[2671782512663819132, "tauri_utils", false, 11873741019147838691], [3060637413840920116, "proc_macro2", false, 17853383480193718097], [4974441333307933176, "syn", false, 8086222198350423974], [13077543566650298139, "heck", false, 11763499977489420239], [14455244907590647360, "tauri_codegen", false, 4418117764596235323], [17990358020177143287, "quote", false, 11535381162450460229]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-7255802dcd0e2b97\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}