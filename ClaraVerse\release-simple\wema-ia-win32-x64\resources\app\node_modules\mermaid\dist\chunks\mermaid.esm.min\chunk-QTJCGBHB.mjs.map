{"version": 3, "sources": ["../../../src/rendering-util/rendering-elements/edgeMarker.ts", "../../../src/rendering-util/rendering-elements/edges.js", "../../../src/rendering-util/rendering-elements/markers.js"], "sourcesContent": ["import type { SVG } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport type { EdgeData } from '../../types.js';\n/**\n * Adds SVG markers to a path element based on the arrow types specified in the edge.\n *\n * @param svgPath - The SVG path element to add markers to.\n * @param edge - The edge data object containing the arrow types.\n * @param url - The URL of the SVG marker definitions.\n * @param id - The ID prefix for the SVG marker definitions.\n * @param diagramType - The type of diagram being rendered.\n */\nexport const addEdgeMarkers = (\n  svgPath: SVG,\n  edge: Pick<EdgeData, 'arrowTypeStart' | 'arrowTypeEnd'>,\n  url: string,\n  id: string,\n  diagramType: string,\n  strokeColor?: string\n) => {\n  if (edge.arrowTypeStart) {\n    addEdgeMarker(svgPath, 'start', edge.arrowTypeStart, url, id, diagramType, strokeColor);\n  }\n  if (edge.arrowTypeEnd) {\n    addEdgeMarker(svgPath, 'end', edge.arrowTypeEnd, url, id, diagramType, strokeColor);\n  }\n};\n\nconst arrowTypesMap = {\n  arrow_cross: { type: 'cross', fill: false },\n  arrow_point: { type: 'point', fill: true },\n  arrow_barb: { type: 'barb', fill: true },\n  arrow_circle: { type: 'circle', fill: false },\n  aggregation: { type: 'aggregation', fill: false },\n  extension: { type: 'extension', fill: false },\n  composition: { type: 'composition', fill: true },\n  dependency: { type: 'dependency', fill: true },\n  lollipop: { type: 'lollipop', fill: false },\n  only_one: { type: 'onlyOne', fill: false },\n  zero_or_one: { type: 'zeroOrOne', fill: false },\n  one_or_more: { type: 'oneOrMore', fill: false },\n  zero_or_more: { type: 'zeroOrMore', fill: false },\n  requirement_arrow: { type: 'requirement_arrow', fill: false },\n  requirement_contains: { type: 'requirement_contains', fill: false },\n} as const;\n\nconst addEdgeMarker = (\n  svgPath: SVG,\n  position: 'start' | 'end',\n  arrowType: string,\n  url: string,\n  id: string,\n  diagramType: string,\n  strokeColor?: string\n) => {\n  const arrowTypeInfo = arrowTypesMap[arrowType as keyof typeof arrowTypesMap];\n\n  if (!arrowTypeInfo) {\n    log.warn(`Unknown arrow type: ${arrowType}`);\n    return; // unknown arrow type, ignore\n  }\n\n  const endMarkerType = arrowTypeInfo.type;\n  const suffix = position === 'start' ? 'Start' : 'End';\n  const originalMarkerId = `${id}_${diagramType}-${endMarkerType}${suffix}`;\n\n  // If stroke color is specified and non-empty, create or use a colored variant of the marker\n  if (strokeColor && strokeColor.trim() !== '') {\n    // Create a sanitized color value for use in IDs\n    const colorId = strokeColor.replace(/[^\\dA-Za-z]/g, '_');\n    const coloredMarkerId = `${originalMarkerId}_${colorId}`;\n\n    // Check if the colored marker already exists\n    if (!document.getElementById(coloredMarkerId)) {\n      // Get the original marker\n      const originalMarker = document.getElementById(originalMarkerId);\n      if (originalMarker) {\n        // Clone the marker and create colored version\n        const coloredMarker = originalMarker.cloneNode(true) as Element;\n        coloredMarker.id = coloredMarkerId;\n\n        // Apply colors to the paths inside the marker\n        const paths = coloredMarker.querySelectorAll('path, circle, line');\n        paths.forEach((path) => {\n          path.setAttribute('stroke', strokeColor);\n\n          // Apply fill only to markers that should be filled\n          if (arrowTypeInfo.fill) {\n            path.setAttribute('fill', strokeColor);\n          }\n        });\n\n        // Add the new colored marker to the defs section\n        originalMarker.parentNode?.appendChild(coloredMarker);\n      }\n    }\n\n    // Use the colored marker\n    svgPath.attr(`marker-${position}`, `url(${url}#${coloredMarkerId})`);\n  } else {\n    // Always use the original marker for unstyled edges\n    svgPath.attr(`marker-${position}`, `url(${url}#${originalMarkerId})`);\n  }\n};\n", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { evaluate } from '../../diagrams/common/common.js';\nimport { log } from '../../logger.js';\nimport { createText } from '../createText.js';\nimport utils from '../../utils.js';\nimport { getLineFunctionsWithOffset } from '../../utils/lineWithOffset.js';\nimport { getSubGraphTitleMargins } from '../../utils/subGraphTitleMargins.js';\n\nimport {\n  curveBasis,\n  curveLinear,\n  curveCardinal,\n  curveBumpX,\n  curveBumpY,\n  curveCatmullRom,\n  curveMonotoneX,\n  curveMonotoneY,\n  curveNatural,\n  curveStep,\n  curveStepAfter,\n  curveStepBefore,\n  line,\n  select,\n} from 'd3';\nimport rough from 'roughjs';\nimport createLabel from './createLabel.js';\nimport { addEdgeMarkers } from './edgeMarker.ts';\nimport { isLabelStyle } from './shapes/handDrawnShapeStyles.js';\n\nconst edgeLabels = new Map();\nconst terminalLabels = new Map();\n\nexport const clear = () => {\n  edgeLabels.clear();\n  terminalLabels.clear();\n};\n\nexport const getLabelStyles = (styleArray) => {\n  let styles = styleArray ? styleArray.reduce((acc, style) => acc + ';' + style, '') : '';\n  return styles;\n};\n\nexport const insertEdgeLabel = async (elem, edge) => {\n  let useHtmlLabels = evaluate(getConfig().flowchart.htmlLabels);\n\n  const labelElement = await createText(elem, edge.label, {\n    style: getLabelStyles(edge.labelStyle),\n    useHtmlLabels,\n    addSvgBackground: true,\n    isNode: false,\n  });\n  log.info('abc82', edge, edge.labelType);\n\n  // Create outer g, edgeLabel, this will be positioned after graph layout\n  const edgeLabel = elem.insert('g').attr('class', 'edgeLabel');\n\n  // Create inner g, label, this will be positioned now for centering the text\n  const label = edgeLabel.insert('g').attr('class', 'label');\n  label.node().appendChild(labelElement);\n\n  // Center the label\n  let bbox = labelElement.getBBox();\n  if (useHtmlLabels) {\n    const div = labelElement.children[0];\n    const dv = select(labelElement);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n  label.attr('transform', 'translate(' + -bbox.width / 2 + ', ' + -bbox.height / 2 + ')');\n\n  // Make element accessible by id for positioning\n  edgeLabels.set(edge.id, edgeLabel);\n\n  // Update the abstract data of the edge with the new information about its width and height\n  edge.width = bbox.width;\n  edge.height = bbox.height;\n\n  let fo;\n  if (edge.startLabelLeft) {\n    // Create the actual text element\n    const startLabelElement = await createLabel(\n      edge.startLabelLeft,\n      getLabelStyles(edge.labelStyle)\n    );\n    const startEdgeLabelLeft = elem.insert('g').attr('class', 'edgeTerminals');\n    const inner = startEdgeLabelLeft.insert('g').attr('class', 'inner');\n    fo = inner.node().appendChild(startLabelElement);\n    const slBox = startLabelElement.getBBox();\n    inner.attr('transform', 'translate(' + -slBox.width / 2 + ', ' + -slBox.height / 2 + ')');\n    if (!terminalLabels.get(edge.id)) {\n      terminalLabels.set(edge.id, {});\n    }\n    terminalLabels.get(edge.id).startLeft = startEdgeLabelLeft;\n    setTerminalWidth(fo, edge.startLabelLeft);\n  }\n  if (edge.startLabelRight) {\n    // Create the actual text element\n    const startLabelElement = await createLabel(\n      edge.startLabelRight,\n      getLabelStyles(edge.labelStyle)\n    );\n    const startEdgeLabelRight = elem.insert('g').attr('class', 'edgeTerminals');\n    const inner = startEdgeLabelRight.insert('g').attr('class', 'inner');\n    fo = startEdgeLabelRight.node().appendChild(startLabelElement);\n    inner.node().appendChild(startLabelElement);\n    const slBox = startLabelElement.getBBox();\n    inner.attr('transform', 'translate(' + -slBox.width / 2 + ', ' + -slBox.height / 2 + ')');\n\n    if (!terminalLabels.get(edge.id)) {\n      terminalLabels.set(edge.id, {});\n    }\n    terminalLabels.get(edge.id).startRight = startEdgeLabelRight;\n    setTerminalWidth(fo, edge.startLabelRight);\n  }\n  if (edge.endLabelLeft) {\n    // Create the actual text element\n    const endLabelElement = await createLabel(edge.endLabelLeft, getLabelStyles(edge.labelStyle));\n    const endEdgeLabelLeft = elem.insert('g').attr('class', 'edgeTerminals');\n    const inner = endEdgeLabelLeft.insert('g').attr('class', 'inner');\n    fo = inner.node().appendChild(endLabelElement);\n    const slBox = endLabelElement.getBBox();\n    inner.attr('transform', 'translate(' + -slBox.width / 2 + ', ' + -slBox.height / 2 + ')');\n\n    endEdgeLabelLeft.node().appendChild(endLabelElement);\n\n    if (!terminalLabels.get(edge.id)) {\n      terminalLabels.set(edge.id, {});\n    }\n    terminalLabels.get(edge.id).endLeft = endEdgeLabelLeft;\n    setTerminalWidth(fo, edge.endLabelLeft);\n  }\n  if (edge.endLabelRight) {\n    // Create the actual text element\n    const endLabelElement = await createLabel(edge.endLabelRight, getLabelStyles(edge.labelStyle));\n    const endEdgeLabelRight = elem.insert('g').attr('class', 'edgeTerminals');\n    const inner = endEdgeLabelRight.insert('g').attr('class', 'inner');\n\n    fo = inner.node().appendChild(endLabelElement);\n    const slBox = endLabelElement.getBBox();\n    inner.attr('transform', 'translate(' + -slBox.width / 2 + ', ' + -slBox.height / 2 + ')');\n\n    endEdgeLabelRight.node().appendChild(endLabelElement);\n    if (!terminalLabels.get(edge.id)) {\n      terminalLabels.set(edge.id, {});\n    }\n    terminalLabels.get(edge.id).endRight = endEdgeLabelRight;\n    setTerminalWidth(fo, edge.endLabelRight);\n  }\n  return labelElement;\n};\n\n/**\n * @param {any} fo\n * @param {any} value\n */\nfunction setTerminalWidth(fo, value) {\n  if (getConfig().flowchart.htmlLabels && fo) {\n    fo.style.width = value.length * 9 + 'px';\n    fo.style.height = '12px';\n  }\n}\n\nexport const positionEdgeLabel = (edge, paths) => {\n  log.debug('Moving label abc88 ', edge.id, edge.label, edgeLabels.get(edge.id), paths);\n  let path = paths.updatedPath ? paths.updatedPath : paths.originalPath;\n  const siteConfig = getConfig();\n  const { subGraphTitleTotalMargin } = getSubGraphTitleMargins(siteConfig);\n  if (edge.label) {\n    const el = edgeLabels.get(edge.id);\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils.calcLabelPosition(path);\n      log.debug(\n        'Moving label ' + edge.label + ' from (',\n        x,\n        ',',\n        y,\n        ') to (',\n        pos.x,\n        ',',\n        pos.y,\n        ') abc88'\n      );\n      if (paths.updatedPath) {\n        x = pos.x;\n        y = pos.y;\n      }\n    }\n    el.attr('transform', `translate(${x}, ${y + subGraphTitleTotalMargin / 2})`);\n  }\n\n  if (edge.startLabelLeft) {\n    const el = terminalLabels.get(edge.id).startLeft;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils.calcTerminalLabelPosition(edge.arrowTypeStart ? 10 : 0, 'start_left', path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr('transform', `translate(${x}, ${y})`);\n  }\n  if (edge.startLabelRight) {\n    const el = terminalLabels.get(edge.id).startRight;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils.calcTerminalLabelPosition(\n        edge.arrowTypeStart ? 10 : 0,\n        'start_right',\n        path\n      );\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr('transform', `translate(${x}, ${y})`);\n  }\n  if (edge.endLabelLeft) {\n    const el = terminalLabels.get(edge.id).endLeft;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils.calcTerminalLabelPosition(edge.arrowTypeEnd ? 10 : 0, 'end_left', path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr('transform', `translate(${x}, ${y})`);\n  }\n  if (edge.endLabelRight) {\n    const el = terminalLabels.get(edge.id).endRight;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils.calcTerminalLabelPosition(edge.arrowTypeEnd ? 10 : 0, 'end_right', path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr('transform', `translate(${x}, ${y})`);\n  }\n};\n\nconst outsideNode = (node, point) => {\n  const x = node.x;\n  const y = node.y;\n  const dx = Math.abs(point.x - x);\n  const dy = Math.abs(point.y - y);\n  const w = node.width / 2;\n  const h = node.height / 2;\n  return dx >= w || dy >= h;\n};\n\nexport const intersection = (node, outsidePoint, insidePoint) => {\n  log.debug(`intersection calc abc89:\n  outsidePoint: ${JSON.stringify(outsidePoint)}\n  insidePoint : ${JSON.stringify(insidePoint)}\n  node        : x:${node.x} y:${node.y} w:${node.width} h:${node.height}`);\n  const x = node.x;\n  const y = node.y;\n\n  const dx = Math.abs(x - insidePoint.x);\n  const w = node.width / 2;\n  let r = insidePoint.x < outsidePoint.x ? w - dx : w + dx;\n  const h = node.height / 2;\n\n  const Q = Math.abs(outsidePoint.y - insidePoint.y);\n  const R = Math.abs(outsidePoint.x - insidePoint.x);\n\n  if (Math.abs(y - outsidePoint.y) * w > Math.abs(x - outsidePoint.x) * h) {\n    // Intersection is top or bottom of rect.\n    let q = insidePoint.y < outsidePoint.y ? outsidePoint.y - h - y : y - h - outsidePoint.y;\n    r = (R * q) / Q;\n    const res = {\n      x: insidePoint.x < outsidePoint.x ? insidePoint.x + r : insidePoint.x - R + r,\n      y: insidePoint.y < outsidePoint.y ? insidePoint.y + Q - q : insidePoint.y - Q + q,\n    };\n\n    if (r === 0) {\n      res.x = outsidePoint.x;\n      res.y = outsidePoint.y;\n    }\n    if (R === 0) {\n      res.x = outsidePoint.x;\n    }\n    if (Q === 0) {\n      res.y = outsidePoint.y;\n    }\n\n    log.debug(`abc89 top/bottom calc, Q ${Q}, q ${q}, R ${R}, r ${r}`, res);\n\n    return res;\n  } else {\n    // Intersection on sides of rect\n    if (insidePoint.x < outsidePoint.x) {\n      r = outsidePoint.x - w - x;\n    } else {\n      r = x - w - outsidePoint.x;\n    }\n    let q = (Q * r) / R;\n    let _x = insidePoint.x < outsidePoint.x ? insidePoint.x + R - r : insidePoint.x - R + r;\n    let _y = insidePoint.y < outsidePoint.y ? insidePoint.y + q : insidePoint.y - q;\n    log.debug(`sides calc abc89, Q ${Q}, q ${q}, R ${R}, r ${r}`, { _x, _y });\n    if (r === 0) {\n      _x = outsidePoint.x;\n      _y = outsidePoint.y;\n    }\n    if (R === 0) {\n      _x = outsidePoint.x;\n    }\n    if (Q === 0) {\n      _y = outsidePoint.y;\n    }\n\n    return { x: _x, y: _y };\n  }\n};\n\nconst cutPathAtIntersect = (_points, boundaryNode) => {\n  log.warn('abc88 cutPathAtIntersect', _points, boundaryNode);\n  let points = [];\n  let lastPointOutside = _points[0];\n  let isInside = false;\n  _points.forEach((point) => {\n    log.info('abc88 checking point', point, boundaryNode);\n\n    if (!outsideNode(boundaryNode, point) && !isInside) {\n      const inter = intersection(boundaryNode, lastPointOutside, point);\n      log.debug('abc88 inside', point, lastPointOutside, inter);\n      log.debug('abc88 intersection', inter, boundaryNode);\n\n      let pointPresent = false;\n      points.forEach((p) => {\n        pointPresent = pointPresent || (p.x === inter.x && p.y === inter.y);\n      });\n\n      if (!points.some((e) => e.x === inter.x && e.y === inter.y)) {\n        points.push(inter);\n      } else {\n        log.warn('abc88 no intersect', inter, points);\n      }\n      isInside = true;\n    } else {\n      log.warn('abc88 outside', point, lastPointOutside);\n      lastPointOutside = point;\n      if (!isInside) {\n        points.push(point);\n      }\n    }\n  });\n  log.debug('returning points', points);\n  return points;\n};\n\nfunction extractCornerPoints(points) {\n  const cornerPoints = [];\n  const cornerPointPositions = [];\n  for (let i = 1; i < points.length - 1; i++) {\n    const prev = points[i - 1];\n    const curr = points[i];\n    const next = points[i + 1];\n    if (\n      prev.x === curr.x &&\n      curr.y === next.y &&\n      Math.abs(curr.x - next.x) > 5 &&\n      Math.abs(curr.y - prev.y) > 5\n    ) {\n      cornerPoints.push(curr);\n      cornerPointPositions.push(i);\n    } else if (\n      prev.y === curr.y &&\n      curr.x === next.x &&\n      Math.abs(curr.x - prev.x) > 5 &&\n      Math.abs(curr.y - next.y) > 5\n    ) {\n      cornerPoints.push(curr);\n      cornerPointPositions.push(i);\n    }\n  }\n  return { cornerPoints, cornerPointPositions };\n}\n\nconst findAdjacentPoint = function (pointA, pointB, distance) {\n  const xDiff = pointB.x - pointA.x;\n  const yDiff = pointB.y - pointA.y;\n  const length = Math.sqrt(xDiff * xDiff + yDiff * yDiff);\n  const ratio = distance / length;\n  return { x: pointB.x - ratio * xDiff, y: pointB.y - ratio * yDiff };\n};\n\nconst fixCorners = function (lineData) {\n  const { cornerPointPositions } = extractCornerPoints(lineData);\n  const newLineData = [];\n  for (let i = 0; i < lineData.length; i++) {\n    if (cornerPointPositions.includes(i)) {\n      const prevPoint = lineData[i - 1];\n      const nextPoint = lineData[i + 1];\n      const cornerPoint = lineData[i];\n\n      const newPrevPoint = findAdjacentPoint(prevPoint, cornerPoint, 5);\n      const newNextPoint = findAdjacentPoint(nextPoint, cornerPoint, 5);\n\n      const xDiff = newNextPoint.x - newPrevPoint.x;\n      const yDiff = newNextPoint.y - newPrevPoint.y;\n      newLineData.push(newPrevPoint);\n\n      const a = Math.sqrt(2) * 2;\n      let newCornerPoint = { x: cornerPoint.x, y: cornerPoint.y };\n      if (Math.abs(nextPoint.x - prevPoint.x) > 10 && Math.abs(nextPoint.y - prevPoint.y) >= 10) {\n        log.debug(\n          'Corner point fixing',\n          Math.abs(nextPoint.x - prevPoint.x),\n          Math.abs(nextPoint.y - prevPoint.y)\n        );\n        const r = 5;\n        if (cornerPoint.x === newPrevPoint.x) {\n          newCornerPoint = {\n            x: xDiff < 0 ? newPrevPoint.x - r + a : newPrevPoint.x + r - a,\n            y: yDiff < 0 ? newPrevPoint.y - a : newPrevPoint.y + a,\n          };\n        } else {\n          newCornerPoint = {\n            x: xDiff < 0 ? newPrevPoint.x - a : newPrevPoint.x + a,\n            y: yDiff < 0 ? newPrevPoint.y - r + a : newPrevPoint.y + r - a,\n          };\n        }\n      } else {\n        log.debug(\n          'Corner point skipping fixing',\n          Math.abs(nextPoint.x - prevPoint.x),\n          Math.abs(nextPoint.y - prevPoint.y)\n        );\n      }\n      newLineData.push(newCornerPoint, newNextPoint);\n    } else {\n      newLineData.push(lineData[i]);\n    }\n  }\n  return newLineData;\n};\n\nexport const insertEdge = function (elem, edge, clusterDb, diagramType, startNode, endNode, id) {\n  const { handDrawnSeed } = getConfig();\n  let points = edge.points;\n  let pointsHasChanged = false;\n  const tail = startNode;\n  var head = endNode;\n  const edgeClassStyles = [];\n  for (const key in edge.cssCompiledStyles) {\n    if (isLabelStyle(key)) {\n      continue;\n    }\n    edgeClassStyles.push(edge.cssCompiledStyles[key]);\n  }\n\n  if (head.intersect && tail.intersect) {\n    points = points.slice(1, edge.points.length - 1);\n    points.unshift(tail.intersect(points[0]));\n    log.debug(\n      'Last point APA12',\n      edge.start,\n      '-->',\n      edge.end,\n      points[points.length - 1],\n      head,\n      head.intersect(points[points.length - 1])\n    );\n    points.push(head.intersect(points[points.length - 1]));\n  }\n  if (edge.toCluster) {\n    log.info('to cluster abc88', clusterDb.get(edge.toCluster));\n    points = cutPathAtIntersect(edge.points, clusterDb.get(edge.toCluster).node);\n\n    pointsHasChanged = true;\n  }\n\n  if (edge.fromCluster) {\n    log.debug(\n      'from cluster abc88',\n      clusterDb.get(edge.fromCluster),\n      JSON.stringify(points, null, 2)\n    );\n    points = cutPathAtIntersect(points.reverse(), clusterDb.get(edge.fromCluster).node).reverse();\n\n    pointsHasChanged = true;\n  }\n\n  let lineData = points.filter((p) => !Number.isNaN(p.y));\n  lineData = fixCorners(lineData);\n  let curve = curveBasis;\n  curve = curveLinear;\n  switch (edge.curve) {\n    case 'linear':\n      curve = curveLinear;\n      break;\n    case 'basis':\n      curve = curveBasis;\n      break;\n    case 'cardinal':\n      curve = curveCardinal;\n      break;\n    case 'bumpX':\n      curve = curveBumpX;\n      break;\n    case 'bumpY':\n      curve = curveBumpY;\n      break;\n    case 'catmullRom':\n      curve = curveCatmullRom;\n      break;\n    case 'monotoneX':\n      curve = curveMonotoneX;\n      break;\n    case 'monotoneY':\n      curve = curveMonotoneY;\n      break;\n    case 'natural':\n      curve = curveNatural;\n      break;\n    case 'step':\n      curve = curveStep;\n      break;\n    case 'stepAfter':\n      curve = curveStepAfter;\n      break;\n    case 'stepBefore':\n      curve = curveStepBefore;\n      break;\n    default:\n      curve = curveBasis;\n  }\n\n  const { x, y } = getLineFunctionsWithOffset(edge);\n  const lineFunction = line().x(x).y(y).curve(curve);\n\n  let strokeClasses;\n  switch (edge.thickness) {\n    case 'normal':\n      strokeClasses = 'edge-thickness-normal';\n      break;\n    case 'thick':\n      strokeClasses = 'edge-thickness-thick';\n      break;\n    case 'invisible':\n      strokeClasses = 'edge-thickness-invisible';\n      break;\n    default:\n      strokeClasses = 'edge-thickness-normal';\n  }\n  switch (edge.pattern) {\n    case 'solid':\n      strokeClasses += ' edge-pattern-solid';\n      break;\n    case 'dotted':\n      strokeClasses += ' edge-pattern-dotted';\n      break;\n    case 'dashed':\n      strokeClasses += ' edge-pattern-dashed';\n      break;\n    default:\n      strokeClasses += ' edge-pattern-solid';\n  }\n  let svgPath;\n  let linePath = lineFunction(lineData);\n  const edgeStyles = Array.isArray(edge.style) ? edge.style : [edge.style];\n  let strokeColor = edgeStyles.find((style) => style?.startsWith('stroke:'));\n\n  if (edge.look === 'handDrawn') {\n    const rc = rough.svg(elem);\n    Object.assign([], lineData);\n\n    const svgPathNode = rc.path(linePath, {\n      roughness: 0.3,\n      seed: handDrawnSeed,\n    });\n\n    strokeClasses += ' transition';\n\n    svgPath = select(svgPathNode)\n      .select('path')\n      .attr('id', edge.id)\n      .attr('class', ' ' + strokeClasses + (edge.classes ? ' ' + edge.classes : ''))\n      .attr('style', edgeStyles ? edgeStyles.reduce((acc, style) => acc + ';' + style, '') : '');\n    let d = svgPath.attr('d');\n    svgPath.attr('d', d);\n    elem.node().appendChild(svgPath.node());\n  } else {\n    const stylesFromClasses = edgeClassStyles.join(';');\n    const styles = edgeStyles ? edgeStyles.reduce((acc, style) => acc + style + ';', '') : '';\n    let animationClass = '';\n    if (edge.animate) {\n      animationClass = ' edge-animation-fast';\n    }\n    if (edge.animation) {\n      animationClass = ' edge-animation-' + edge.animation;\n    }\n\n    const pathStyle = stylesFromClasses ? stylesFromClasses + ';' + styles + ';' : styles;\n    svgPath = elem\n      .append('path')\n      .attr('d', linePath)\n      .attr('id', edge.id)\n      .attr(\n        'class',\n        ' ' + strokeClasses + (edge.classes ? ' ' + edge.classes : '') + (animationClass ?? '')\n      )\n      .attr('style', pathStyle);\n    strokeColor = pathStyle.match(/stroke:([^;]+)/)?.[1];\n  }\n\n  // DEBUG code, DO NOT REMOVE\n  // adds a red circle at each edge coordinate\n  // cornerPoints.forEach((point) => {\n  //   elem\n  //     .append('circle')\n  //     .style('stroke', 'blue')\n  //     .style('fill', 'blue')\n  //     .attr('r', 3)\n  //     .attr('cx', point.x)\n  //     .attr('cy', point.y);\n  // });\n  // lineData.forEach((point) => {\n  //   elem\n  //     .append('circle')\n  //     .style('stroke', 'blue')\n  //     .style('fill', 'blue')\n  //     .attr('r', 3)\n  //     .attr('cx', point.x)\n  //     .attr('cy', point.y);\n  // });\n\n  let url = '';\n  if (getConfig().flowchart.arrowMarkerAbsolute || getConfig().state.arrowMarkerAbsolute) {\n    url =\n      window.location.protocol +\n      '//' +\n      window.location.host +\n      window.location.pathname +\n      window.location.search;\n    url = url.replace(/\\(/g, '\\\\(').replace(/\\)/g, '\\\\)');\n  }\n  log.info('arrowTypeStart', edge.arrowTypeStart);\n  log.info('arrowTypeEnd', edge.arrowTypeEnd);\n\n  addEdgeMarkers(svgPath, edge, url, id, diagramType, strokeColor);\n\n  let paths = {};\n  if (pointsHasChanged) {\n    paths.updatedPath = points;\n  }\n  paths.originalPath = edge.points;\n  return paths;\n};\n", "/** Setup arrow head and define the marker. The result is appended to the svg. */\nimport { log } from '../../logger.js';\n\n// Only add the number of markers that the diagram needs\nconst insertMarkers = (elem, markerArray, type, id) => {\n  markerArray.forEach((markerName) => {\n    markers[markerName](elem, type, id);\n  });\n};\n\nconst extension = (elem, type, id) => {\n  log.trace('Making markers for ', id);\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-extensionStart')\n    .attr('class', 'marker extension ' + type)\n    .attr('refX', 18)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 1,7 L18,13 V 1 Z');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-extensionEnd')\n    .attr('class', 'marker extension ' + type)\n    .attr('refX', 1)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 1,1 V 13 L18,7 Z'); // this is actual shape for arrowhead\n};\n\nconst composition = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-compositionStart')\n    .attr('class', 'marker composition ' + type)\n    .attr('refX', 18)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L1,7 L9,1 Z');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-compositionEnd')\n    .attr('class', 'marker composition ' + type)\n    .attr('refX', 1)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L1,7 L9,1 Z');\n};\nconst aggregation = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-aggregationStart')\n    .attr('class', 'marker aggregation ' + type)\n    .attr('refX', 18)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L1,7 L9,1 Z');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-aggregationEnd')\n    .attr('class', 'marker aggregation ' + type)\n    .attr('refX', 1)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L1,7 L9,1 Z');\n};\nconst dependency = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-dependencyStart')\n    .attr('class', 'marker dependency ' + type)\n    .attr('refX', 6)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 5,7 L9,13 L1,7 L9,1 Z');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-dependencyEnd')\n    .attr('class', 'marker dependency ' + type)\n    .attr('refX', 13)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L14,7 L9,1 Z');\n};\nconst lollipop = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-lollipopStart')\n    .attr('class', 'marker lollipop ' + type)\n    .attr('refX', 13)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('stroke', 'black')\n    .attr('fill', 'transparent')\n    .attr('cx', 7)\n    .attr('cy', 7)\n    .attr('r', 6);\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-lollipopEnd')\n    .attr('class', 'marker lollipop ' + type)\n    .attr('refX', 1)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('stroke', 'black')\n    .attr('fill', 'transparent')\n    .attr('cx', 7)\n    .attr('cy', 7)\n    .attr('r', 6);\n};\nconst point = (elem, type, id) => {\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-pointEnd')\n    .attr('class', 'marker ' + type)\n    .attr('viewBox', '0 0 10 10')\n    .attr('refX', 5)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 8)\n    .attr('markerHeight', 8)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 0 0 L 10 5 L 0 10 z')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 1)\n    .style('stroke-dasharray', '1,0');\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-pointStart')\n    .attr('class', 'marker ' + type)\n    .attr('viewBox', '0 0 10 10')\n    .attr('refX', 4.5)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 8)\n    .attr('markerHeight', 8)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 0 5 L 10 10 L 10 0 z')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 1)\n    .style('stroke-dasharray', '1,0');\n};\nconst circle = (elem, type, id) => {\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-circleEnd')\n    .attr('class', 'marker ' + type)\n    .attr('viewBox', '0 0 10 10')\n    .attr('refX', 11)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 11)\n    .attr('markerHeight', 11)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('cx', '5')\n    .attr('cy', '5')\n    .attr('r', '5')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 1)\n    .style('stroke-dasharray', '1,0');\n\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-circleStart')\n    .attr('class', 'marker ' + type)\n    .attr('viewBox', '0 0 10 10')\n    .attr('refX', -1)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 11)\n    .attr('markerHeight', 11)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('cx', '5')\n    .attr('cy', '5')\n    .attr('r', '5')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 1)\n    .style('stroke-dasharray', '1,0');\n};\nconst cross = (elem, type, id) => {\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-crossEnd')\n    .attr('class', 'marker cross ' + type)\n    .attr('viewBox', '0 0 11 11')\n    .attr('refX', 12)\n    .attr('refY', 5.2)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 11)\n    .attr('markerHeight', 11)\n    .attr('orient', 'auto')\n    .append('path')\n    // .attr('stroke', 'black')\n    .attr('d', 'M 1,1 l 9,9 M 10,1 l -9,9')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 2)\n    .style('stroke-dasharray', '1,0');\n\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-crossStart')\n    .attr('class', 'marker cross ' + type)\n    .attr('viewBox', '0 0 11 11')\n    .attr('refX', -1)\n    .attr('refY', 5.2)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 11)\n    .attr('markerHeight', 11)\n    .attr('orient', 'auto')\n    .append('path')\n    // .attr('stroke', 'black')\n    .attr('d', 'M 1,1 l 9,9 M 10,1 l -9,9')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 2)\n    .style('stroke-dasharray', '1,0');\n};\nconst barb = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-barbEnd')\n    .attr('refX', 19)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 14)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 19,7 L9,13 L14,7 L9,1 Z');\n};\n// erDiagram specific markers\nconst only_one = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-onlyOneStart')\n    .attr('class', 'marker onlyOne ' + type)\n    .attr('refX', 0)\n    .attr('refY', 9)\n    .attr('markerWidth', 18)\n    .attr('markerHeight', 18)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M9,0 L9,18 M15,0 L15,18');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-onlyOneEnd')\n    .attr('class', 'marker onlyOne ' + type)\n    .attr('refX', 18)\n    .attr('refY', 9)\n    .attr('markerWidth', 18)\n    .attr('markerHeight', 18)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M3,0 L3,18 M9,0 L9,18');\n};\n\nconst zero_or_one = (elem, type, id) => {\n  const startMarker = elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-zeroOrOneStart')\n    .attr('class', 'marker zeroOrOne ' + type)\n    .attr('refX', 0)\n    .attr('refY', 9)\n    .attr('markerWidth', 30)\n    .attr('markerHeight', 18)\n    .attr('orient', 'auto');\n  startMarker\n    .append('circle')\n    .attr('fill', 'white') // Fill white for now?\n    .attr('cx', 21)\n    .attr('cy', 9)\n    .attr('r', 6);\n  startMarker.append('path').attr('d', 'M9,0 L9,18');\n\n  const endMarker = elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-zeroOrOneEnd')\n    .attr('class', 'marker zeroOrOne ' + type)\n    .attr('refX', 30)\n    .attr('refY', 9)\n    .attr('markerWidth', 30)\n    .attr('markerHeight', 18)\n    .attr('orient', 'auto');\n  endMarker\n    .append('circle')\n    .attr('fill', 'white') // Fill white for now?\n    .attr('cx', 9)\n    .attr('cy', 9)\n    .attr('r', 6);\n  endMarker.append('path').attr('d', 'M21,0 L21,18');\n};\n\nconst one_or_more = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-oneOrMoreStart')\n    .attr('class', 'marker oneOrMore ' + type)\n    .attr('refX', 18)\n    .attr('refY', 18)\n    .attr('markerWidth', 45)\n    .attr('markerHeight', 36)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-oneOrMoreEnd')\n    .attr('class', 'marker oneOrMore ' + type)\n    .attr('refX', 27)\n    .attr('refY', 18)\n    .attr('markerWidth', 45)\n    .attr('markerHeight', 36)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18');\n};\n\nconst zero_or_more = (elem, type, id) => {\n  const startMarker = elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-zeroOrMoreStart')\n    .attr('class', 'marker zeroOrMore ' + type)\n    .attr('refX', 18)\n    .attr('refY', 18)\n    .attr('markerWidth', 57)\n    .attr('markerHeight', 36)\n    .attr('orient', 'auto');\n  startMarker.append('circle').attr('fill', 'white').attr('cx', 48).attr('cy', 18).attr('r', 6);\n  startMarker.append('path').attr('d', 'M0,18 Q18,0 36,18 Q18,36 0,18');\n\n  const endMarker = elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-zeroOrMoreEnd')\n    .attr('class', 'marker zeroOrMore ' + type)\n    .attr('refX', 39)\n    .attr('refY', 18)\n    .attr('markerWidth', 57)\n    .attr('markerHeight', 36)\n    .attr('orient', 'auto');\n  endMarker.append('circle').attr('fill', 'white').attr('cx', 9).attr('cy', 18).attr('r', 6);\n  endMarker.append('path').attr('d', 'M21,18 Q39,0 57,18 Q39,36 21,18');\n};\n\nconst requirement_arrow = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-requirement_arrowEnd')\n    .attr('refX', 20)\n    .attr('refY', 10)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 20)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr(\n      'd',\n      `M0,0\n      L20,10\n      M20,10\n      L0,20`\n    );\n};\nconst requirement_contains = (elem, type, id) => {\n  const containsNode = elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-requirement_containsStart')\n    .attr('refX', 0)\n    .attr('refY', 10)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 20)\n    .attr('orient', 'auto')\n    .append('g');\n\n  containsNode.append('circle').attr('cx', 10).attr('cy', 10).attr('r', 9).attr('fill', 'none');\n\n  containsNode.append('line').attr('x1', 1).attr('x2', 19).attr('y1', 10).attr('y2', 10);\n\n  containsNode.append('line').attr('y1', 1).attr('y2', 19).attr('x1', 10).attr('x2', 10);\n};\n\n// TODO rename the class diagram markers to something shape descriptive and semantic free\nconst markers = {\n  extension,\n  composition,\n  aggregation,\n  dependency,\n  lollipop,\n  point,\n  circle,\n  cross,\n  barb,\n  only_one,\n  zero_or_one,\n  one_or_more,\n  zero_or_more,\n  requirement_arrow,\n  requirement_contains,\n};\nexport default insertMarkers;\n"], "mappings": "+aAYO,IAAMA,GAAiBC,EAAA,CAC5BC,EACAC,EACAC,EACAC,EACAC,EACAC,IACG,CACCJ,EAAK,gBACPK,GAAcN,EAAS,QAASC,EAAK,eAAgBC,EAAKC,EAAIC,EAAaC,CAAW,EAEpFJ,EAAK,cACPK,GAAcN,EAAS,MAAOC,EAAK,aAAcC,EAAKC,EAAIC,EAAaC,CAAW,CAEtF,EAd8B,kBAgBxBE,GAAgB,CACpB,YAAa,CAAE,KAAM,QAAS,KAAM,EAAM,EAC1C,YAAa,CAAE,KAAM,QAAS,KAAM,EAAK,EACzC,WAAY,CAAE,KAAM,OAAQ,KAAM,EAAK,EACvC,aAAc,CAAE,KAAM,SAAU,KAAM,EAAM,EAC5C,YAAa,CAAE,KAAM,cAAe,KAAM,EAAM,EAChD,UAAW,CAAE,KAAM,YAAa,KAAM,EAAM,EAC5C,YAAa,CAAE,KAAM,cAAe,KAAM,EAAK,EAC/C,WAAY,CAAE,KAAM,aAAc,KAAM,EAAK,EAC7C,SAAU,CAAE,KAAM,WAAY,KAAM,EAAM,EAC1C,SAAU,CAAE,KAAM,UAAW,KAAM,EAAM,EACzC,YAAa,CAAE,KAAM,YAAa,KAAM,EAAM,EAC9C,YAAa,CAAE,KAAM,YAAa,KAAM,EAAM,EAC9C,aAAc,CAAE,KAAM,aAAc,KAAM,EAAM,EAChD,kBAAmB,CAAE,KAAM,oBAAqB,KAAM,EAAM,EAC5D,qBAAsB,CAAE,KAAM,uBAAwB,KAAM,EAAM,CACpE,EAEMD,GAAgBP,EAAA,CACpBC,EACAQ,EACAC,EACAP,EACAC,EACAC,EACAC,IACG,CACH,IAAMK,EAAgBH,GAAcE,CAAuC,EAE3E,GAAI,CAACC,EAAe,CAClBC,EAAI,KAAK,uBAAuBF,CAAS,EAAE,EAC3C,MACF,CAEA,IAAMG,EAAgBF,EAAc,KAE9BG,EAAmB,GAAGV,CAAE,IAAIC,CAAW,IAAIQ,CAAa,GAD/CJ,IAAa,QAAU,QAAU,KACuB,GAGvE,GAAIH,GAAeA,EAAY,KAAK,IAAM,GAAI,CAE5C,IAAMS,EAAUT,EAAY,QAAQ,eAAgB,GAAG,EACjDU,EAAkB,GAAGF,CAAgB,IAAIC,CAAO,GAGtD,GAAI,CAAC,SAAS,eAAeC,CAAe,EAAG,CAE7C,IAAMC,EAAiB,SAAS,eAAeH,CAAgB,EAC/D,GAAIG,EAAgB,CAElB,IAAMC,EAAgBD,EAAe,UAAU,EAAI,EACnDC,EAAc,GAAKF,EAGLE,EAAc,iBAAiB,oBAAoB,EAC3D,QAASC,GAAS,CACtBA,EAAK,aAAa,SAAUb,CAAW,EAGnCK,EAAc,MAChBQ,EAAK,aAAa,OAAQb,CAAW,CAEzC,CAAC,EAGDW,EAAe,YAAY,YAAYC,CAAa,CACtD,CACF,CAGAjB,EAAQ,KAAK,UAAUQ,CAAQ,GAAI,OAAON,CAAG,IAAIa,CAAe,GAAG,CACrE,MAEEf,EAAQ,KAAK,UAAUQ,CAAQ,GAAI,OAAON,CAAG,IAAIW,CAAgB,GAAG,CAExE,EAzDsB,iBCjBtB,IAAMM,EAAa,IAAI,IACjBC,EAAiB,IAAI,IAEdC,GAAQC,EAAA,IAAM,CACzBH,EAAW,MAAM,EACjBC,EAAe,MAAM,CACvB,EAHqB,SAKRG,EAAiBD,EAACE,GAChBA,EAAaA,EAAW,OAAO,CAACC,EAAKC,IAAUD,EAAM,IAAMC,EAAO,EAAE,EAAI,GADzD,kBAKjBC,GAAkBL,EAAA,MAAOM,EAAMC,IAAS,CACnD,IAAIC,EAAgBC,EAASC,EAAU,EAAE,UAAU,UAAU,EAEvDC,EAAe,MAAMC,EAAWN,EAAMC,EAAK,MAAO,CACtD,MAAON,EAAeM,EAAK,UAAU,EACrC,cAAAC,EACA,iBAAkB,GAClB,OAAQ,EACV,CAAC,EACDK,EAAI,KAAK,QAASN,EAAMA,EAAK,SAAS,EAGtC,IAAMO,EAAYR,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,WAAW,EAGtDS,EAAQD,EAAU,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EACzDC,EAAM,KAAK,EAAE,YAAYJ,CAAY,EAGrC,IAAIK,EAAOL,EAAa,QAAQ,EAChC,GAAIH,EAAe,CACjB,IAAMS,EAAMN,EAAa,SAAS,CAAC,EAC7BO,EAAKC,EAAOR,CAAY,EAC9BK,EAAOC,EAAI,sBAAsB,EACjCC,EAAG,KAAK,QAASF,EAAK,KAAK,EAC3BE,EAAG,KAAK,SAAUF,EAAK,MAAM,CAC/B,CACAD,EAAM,KAAK,YAAa,aAAe,CAACC,EAAK,MAAQ,EAAI,KAAO,CAACA,EAAK,OAAS,EAAI,GAAG,EAGtFnB,EAAW,IAAIU,EAAK,GAAIO,CAAS,EAGjCP,EAAK,MAAQS,EAAK,MAClBT,EAAK,OAASS,EAAK,OAEnB,IAAII,EACJ,GAAIb,EAAK,eAAgB,CAEvB,IAAMc,EAAoB,MAAMC,EAC9Bf,EAAK,eACLN,EAAeM,EAAK,UAAU,CAChC,EACMgB,EAAqBjB,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,eAAe,EACnEkB,EAAQD,EAAmB,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAClEH,EAAKI,EAAM,KAAK,EAAE,YAAYH,CAAiB,EAC/C,IAAMI,EAAQJ,EAAkB,QAAQ,EACxCG,EAAM,KAAK,YAAa,aAAe,CAACC,EAAM,MAAQ,EAAI,KAAO,CAACA,EAAM,OAAS,EAAI,GAAG,EACnF3B,EAAe,IAAIS,EAAK,EAAE,GAC7BT,EAAe,IAAIS,EAAK,GAAI,CAAC,CAAC,EAEhCT,EAAe,IAAIS,EAAK,EAAE,EAAE,UAAYgB,EACxCG,EAAiBN,EAAIb,EAAK,cAAc,CAC1C,CACA,GAAIA,EAAK,gBAAiB,CAExB,IAAMc,EAAoB,MAAMC,EAC9Bf,EAAK,gBACLN,EAAeM,EAAK,UAAU,CAChC,EACMoB,EAAsBrB,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,eAAe,EACpEkB,EAAQG,EAAoB,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EACnEP,EAAKO,EAAoB,KAAK,EAAE,YAAYN,CAAiB,EAC7DG,EAAM,KAAK,EAAE,YAAYH,CAAiB,EAC1C,IAAMI,EAAQJ,EAAkB,QAAQ,EACxCG,EAAM,KAAK,YAAa,aAAe,CAACC,EAAM,MAAQ,EAAI,KAAO,CAACA,EAAM,OAAS,EAAI,GAAG,EAEnF3B,EAAe,IAAIS,EAAK,EAAE,GAC7BT,EAAe,IAAIS,EAAK,GAAI,CAAC,CAAC,EAEhCT,EAAe,IAAIS,EAAK,EAAE,EAAE,WAAaoB,EACzCD,EAAiBN,EAAIb,EAAK,eAAe,CAC3C,CACA,GAAIA,EAAK,aAAc,CAErB,IAAMqB,EAAkB,MAAMN,EAAYf,EAAK,aAAcN,EAAeM,EAAK,UAAU,CAAC,EACtFsB,EAAmBvB,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,eAAe,EACjEkB,EAAQK,EAAiB,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAChET,EAAKI,EAAM,KAAK,EAAE,YAAYI,CAAe,EAC7C,IAAMH,EAAQG,EAAgB,QAAQ,EACtCJ,EAAM,KAAK,YAAa,aAAe,CAACC,EAAM,MAAQ,EAAI,KAAO,CAACA,EAAM,OAAS,EAAI,GAAG,EAExFI,EAAiB,KAAK,EAAE,YAAYD,CAAe,EAE9C9B,EAAe,IAAIS,EAAK,EAAE,GAC7BT,EAAe,IAAIS,EAAK,GAAI,CAAC,CAAC,EAEhCT,EAAe,IAAIS,EAAK,EAAE,EAAE,QAAUsB,EACtCH,EAAiBN,EAAIb,EAAK,YAAY,CACxC,CACA,GAAIA,EAAK,cAAe,CAEtB,IAAMqB,EAAkB,MAAMN,EAAYf,EAAK,cAAeN,EAAeM,EAAK,UAAU,CAAC,EACvFuB,EAAoBxB,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,eAAe,EAClEkB,EAAQM,EAAkB,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAEjEV,EAAKI,EAAM,KAAK,EAAE,YAAYI,CAAe,EAC7C,IAAMH,EAAQG,EAAgB,QAAQ,EACtCJ,EAAM,KAAK,YAAa,aAAe,CAACC,EAAM,MAAQ,EAAI,KAAO,CAACA,EAAM,OAAS,EAAI,GAAG,EAExFK,EAAkB,KAAK,EAAE,YAAYF,CAAe,EAC/C9B,EAAe,IAAIS,EAAK,EAAE,GAC7BT,EAAe,IAAIS,EAAK,GAAI,CAAC,CAAC,EAEhCT,EAAe,IAAIS,EAAK,EAAE,EAAE,SAAWuB,EACvCJ,EAAiBN,EAAIb,EAAK,aAAa,CACzC,CACA,OAAOI,CACT,EA5G+B,mBAkH/B,SAASe,EAAiBN,EAAIW,EAAO,CAC/BrB,EAAU,EAAE,UAAU,YAAcU,IACtCA,EAAG,MAAM,MAAQW,EAAM,OAAS,EAAI,KACpCX,EAAG,MAAM,OAAS,OAEtB,CALSpB,EAAA0B,EAAA,oBAOF,IAAMM,GAAoBhC,EAAA,CAACO,EAAM0B,IAAU,CAChDpB,EAAI,MAAM,sBAAuBN,EAAK,GAAIA,EAAK,MAAOV,EAAW,IAAIU,EAAK,EAAE,EAAG0B,CAAK,EACpF,IAAIC,EAAOD,EAAM,YAAcA,EAAM,YAAcA,EAAM,aACnDE,EAAazB,EAAU,EACvB,CAAE,yBAAA0B,CAAyB,EAAIC,GAAwBF,CAAU,EACvE,GAAI5B,EAAK,MAAO,CACd,IAAM+B,EAAKzC,EAAW,IAAIU,EAAK,EAAE,EAC7BgC,EAAIhC,EAAK,EACTiC,EAAIjC,EAAK,EACb,GAAI2B,EAAM,CACR,IAAMO,EAAMC,EAAM,kBAAkBR,CAAI,EACxCrB,EAAI,MACF,gBAAkBN,EAAK,MAAQ,UAC/BgC,EACA,IACAC,EACA,SACAC,EAAI,EACJ,IACAA,EAAI,EACJ,SACF,EACIR,EAAM,cACRM,EAAIE,EAAI,EACRD,EAAIC,EAAI,EAEZ,CACAH,EAAG,KAAK,YAAa,aAAaC,CAAC,KAAKC,EAAIJ,EAA2B,CAAC,GAAG,CAC7E,CAEA,GAAI7B,EAAK,eAAgB,CACvB,IAAM+B,EAAKxC,EAAe,IAAIS,EAAK,EAAE,EAAE,UACnCgC,EAAIhC,EAAK,EACTiC,EAAIjC,EAAK,EACb,GAAI2B,EAAM,CACR,IAAMO,EAAMC,EAAM,0BAA0BnC,EAAK,eAAiB,GAAK,EAAG,aAAc2B,CAAI,EAC5FK,EAAIE,EAAI,EACRD,EAAIC,EAAI,CACV,CACAH,EAAG,KAAK,YAAa,aAAaC,CAAC,KAAKC,CAAC,GAAG,CAC9C,CACA,GAAIjC,EAAK,gBAAiB,CACxB,IAAM+B,EAAKxC,EAAe,IAAIS,EAAK,EAAE,EAAE,WACnCgC,EAAIhC,EAAK,EACTiC,EAAIjC,EAAK,EACb,GAAI2B,EAAM,CACR,IAAMO,EAAMC,EAAM,0BAChBnC,EAAK,eAAiB,GAAK,EAC3B,cACA2B,CACF,EACAK,EAAIE,EAAI,EACRD,EAAIC,EAAI,CACV,CACAH,EAAG,KAAK,YAAa,aAAaC,CAAC,KAAKC,CAAC,GAAG,CAC9C,CACA,GAAIjC,EAAK,aAAc,CACrB,IAAM+B,EAAKxC,EAAe,IAAIS,EAAK,EAAE,EAAE,QACnCgC,EAAIhC,EAAK,EACTiC,EAAIjC,EAAK,EACb,GAAI2B,EAAM,CACR,IAAMO,EAAMC,EAAM,0BAA0BnC,EAAK,aAAe,GAAK,EAAG,WAAY2B,CAAI,EACxFK,EAAIE,EAAI,EACRD,EAAIC,EAAI,CACV,CACAH,EAAG,KAAK,YAAa,aAAaC,CAAC,KAAKC,CAAC,GAAG,CAC9C,CACA,GAAIjC,EAAK,cAAe,CACtB,IAAM+B,EAAKxC,EAAe,IAAIS,EAAK,EAAE,EAAE,SACnCgC,EAAIhC,EAAK,EACTiC,EAAIjC,EAAK,EACb,GAAI2B,EAAM,CACR,IAAMO,EAAMC,EAAM,0BAA0BnC,EAAK,aAAe,GAAK,EAAG,YAAa2B,CAAI,EACzFK,EAAIE,EAAI,EACRD,EAAIC,EAAI,CACV,CACAH,EAAG,KAAK,YAAa,aAAaC,CAAC,KAAKC,CAAC,GAAG,CAC9C,CACF,EA9EiC,qBAgF3BG,GAAc3C,EAAA,CAAC4C,EAAMC,IAAU,CACnC,IAAMN,EAAIK,EAAK,EACTJ,EAAII,EAAK,EACTE,EAAK,KAAK,IAAID,EAAM,EAAIN,CAAC,EACzBQ,EAAK,KAAK,IAAIF,EAAM,EAAIL,CAAC,EACzBQ,EAAIJ,EAAK,MAAQ,EACjBK,EAAIL,EAAK,OAAS,EACxB,OAAOE,GAAME,GAAKD,GAAME,CAC1B,EARoB,eAUPC,GAAelD,EAAA,CAAC4C,EAAMO,EAAcC,IAAgB,CAC/DvC,EAAI,MAAM;AAAA,kBACM,KAAK,UAAUsC,CAAY,CAAC;AAAA,kBAC5B,KAAK,UAAUC,CAAW,CAAC;AAAA,oBACzBR,EAAK,CAAC,MAAMA,EAAK,CAAC,MAAMA,EAAK,KAAK,MAAMA,EAAK,MAAM,EAAE,EACvE,IAAML,EAAIK,EAAK,EACTJ,EAAII,EAAK,EAETE,EAAK,KAAK,IAAIP,EAAIa,EAAY,CAAC,EAC/BJ,EAAIJ,EAAK,MAAQ,EACnBS,EAAID,EAAY,EAAID,EAAa,EAAIH,EAAIF,EAAKE,EAAIF,EAChDG,EAAIL,EAAK,OAAS,EAElBU,EAAI,KAAK,IAAIH,EAAa,EAAIC,EAAY,CAAC,EAC3CG,EAAI,KAAK,IAAIJ,EAAa,EAAIC,EAAY,CAAC,EAEjD,GAAI,KAAK,IAAIZ,EAAIW,EAAa,CAAC,EAAIH,EAAI,KAAK,IAAIT,EAAIY,EAAa,CAAC,EAAIF,EAAG,CAEvE,IAAIO,EAAIJ,EAAY,EAAID,EAAa,EAAIA,EAAa,EAAIF,EAAIT,EAAIA,EAAIS,EAAIE,EAAa,EACvFE,EAAKE,EAAIC,EAAKF,EACd,IAAMG,EAAM,CACV,EAAGL,EAAY,EAAID,EAAa,EAAIC,EAAY,EAAIC,EAAID,EAAY,EAAIG,EAAIF,EAC5E,EAAGD,EAAY,EAAID,EAAa,EAAIC,EAAY,EAAIE,EAAIE,EAAIJ,EAAY,EAAIE,EAAIE,CAClF,EAEA,OAAIH,IAAM,IACRI,EAAI,EAAIN,EAAa,EACrBM,EAAI,EAAIN,EAAa,GAEnBI,IAAM,IACRE,EAAI,EAAIN,EAAa,GAEnBG,IAAM,IACRG,EAAI,EAAIN,EAAa,GAGvBtC,EAAI,MAAM,4BAA4ByC,CAAC,OAAOE,CAAC,OAAOD,CAAC,OAAOF,CAAC,GAAII,CAAG,EAE/DA,CACT,KAAO,CAEDL,EAAY,EAAID,EAAa,EAC/BE,EAAIF,EAAa,EAAIH,EAAIT,EAEzBc,EAAId,EAAIS,EAAIG,EAAa,EAE3B,IAAIK,EAAKF,EAAID,EAAKE,EACdG,EAAKN,EAAY,EAAID,EAAa,EAAIC,EAAY,EAAIG,EAAIF,EAAID,EAAY,EAAIG,EAAIF,EAClFM,EAAKP,EAAY,EAAID,EAAa,EAAIC,EAAY,EAAII,EAAIJ,EAAY,EAAII,EAC9E,OAAA3C,EAAI,MAAM,uBAAuByC,CAAC,OAAOE,CAAC,OAAOD,CAAC,OAAOF,CAAC,GAAI,CAAE,GAAAK,EAAI,GAAAC,CAAG,CAAC,EACpEN,IAAM,IACRK,EAAKP,EAAa,EAClBQ,EAAKR,EAAa,GAEhBI,IAAM,IACRG,EAAKP,EAAa,GAEhBG,IAAM,IACRK,EAAKR,EAAa,GAGb,CAAE,EAAGO,EAAIC,CAAM,CACxB,CACF,EA/D4B,gBAiEtBC,GAAqB5D,EAAA,CAAC6D,EAASC,IAAiB,CACpDjD,EAAI,KAAK,2BAA4BgD,EAASC,CAAY,EAC1D,IAAIC,EAAS,CAAC,EACVC,EAAmBH,EAAQ,CAAC,EAC5BI,EAAW,GACf,OAAAJ,EAAQ,QAAShB,GAAU,CAGzB,GAFAhC,EAAI,KAAK,uBAAwBgC,EAAOiB,CAAY,EAEhD,CAACnB,GAAYmB,EAAcjB,CAAK,GAAK,CAACoB,EAAU,CAClD,IAAMC,EAAQhB,GAAaY,EAAcE,EAAkBnB,CAAK,EAChEhC,EAAI,MAAM,eAAgBgC,EAAOmB,EAAkBE,CAAK,EACxDrD,EAAI,MAAM,qBAAsBqD,EAAOJ,CAAY,EAEnD,IAAIK,EAAe,GACnBJ,EAAO,QAASK,GAAM,CACpBD,EAAeA,GAAiBC,EAAE,IAAMF,EAAM,GAAKE,EAAE,IAAMF,EAAM,CACnE,CAAC,EAEIH,EAAO,KAAM,GAAM,EAAE,IAAMG,EAAM,GAAK,EAAE,IAAMA,EAAM,CAAC,EAGxDrD,EAAI,KAAK,qBAAsBqD,EAAOH,CAAM,EAF5CA,EAAO,KAAKG,CAAK,EAInBD,EAAW,EACb,MACEpD,EAAI,KAAK,gBAAiBgC,EAAOmB,CAAgB,EACjDA,EAAmBnB,EACdoB,GACHF,EAAO,KAAKlB,CAAK,CAGvB,CAAC,EACDhC,EAAI,MAAM,mBAAoBkD,CAAM,EAC7BA,CACT,EAlC2B,sBAoC3B,SAASM,GAAoBN,EAAQ,CACnC,IAAMO,EAAe,CAAC,EAChBC,EAAuB,CAAC,EAC9B,QAASC,EAAI,EAAGA,EAAIT,EAAO,OAAS,EAAGS,IAAK,CAC1C,IAAMC,EAAOV,EAAOS,EAAI,CAAC,EACnBE,EAAOX,EAAOS,CAAC,EACfG,EAAOZ,EAAOS,EAAI,CAAC,GAEvBC,EAAK,IAAMC,EAAK,GAChBA,EAAK,IAAMC,EAAK,GAChB,KAAK,IAAID,EAAK,EAAIC,EAAK,CAAC,EAAI,GAC5B,KAAK,IAAID,EAAK,EAAID,EAAK,CAAC,EAAI,GAK5BA,EAAK,IAAMC,EAAK,GAChBA,EAAK,IAAMC,EAAK,GAChB,KAAK,IAAID,EAAK,EAAID,EAAK,CAAC,EAAI,GAC5B,KAAK,IAAIC,EAAK,EAAIC,EAAK,CAAC,EAAI,KAE5BL,EAAa,KAAKI,CAAI,EACtBH,EAAqB,KAAKC,CAAC,EAE/B,CACA,MAAO,CAAE,aAAAF,EAAc,qBAAAC,CAAqB,CAC9C,CA1BSvE,EAAAqE,GAAA,uBA4BT,IAAMO,GAAoB5E,EAAA,SAAU6E,EAAQC,EAAQC,EAAU,CAC5D,IAAMC,EAAQF,EAAO,EAAID,EAAO,EAC1BI,EAAQH,EAAO,EAAID,EAAO,EAC1BK,EAAS,KAAK,KAAKF,EAAQA,EAAQC,EAAQA,CAAK,EAChDE,EAAQJ,EAAWG,EACzB,MAAO,CAAE,EAAGJ,EAAO,EAAIK,EAAQH,EAAO,EAAGF,EAAO,EAAIK,EAAQF,CAAM,CACpE,EAN0B,qBAQpBG,GAAapF,EAAA,SAAUqF,EAAU,CACrC,GAAM,CAAE,qBAAAd,CAAqB,EAAIF,GAAoBgB,CAAQ,EACvDC,EAAc,CAAC,EACrB,QAASd,EAAI,EAAGA,EAAIa,EAAS,OAAQb,IACnC,GAAID,EAAqB,SAASC,CAAC,EAAG,CACpC,IAAMe,EAAYF,EAASb,EAAI,CAAC,EAC1BgB,EAAYH,EAASb,EAAI,CAAC,EAC1BiB,EAAcJ,EAASb,CAAC,EAExBkB,EAAed,GAAkBW,EAAWE,EAAa,CAAC,EAC1DE,EAAef,GAAkBY,EAAWC,EAAa,CAAC,EAE1DT,EAAQW,EAAa,EAAID,EAAa,EACtCT,EAAQU,EAAa,EAAID,EAAa,EAC5CJ,EAAY,KAAKI,CAAY,EAE7B,IAAME,EAAI,KAAK,KAAK,CAAC,EAAI,EACrBC,EAAiB,CAAE,EAAGJ,EAAY,EAAG,EAAGA,EAAY,CAAE,EAC1D,GAAI,KAAK,IAAID,EAAU,EAAID,EAAU,CAAC,EAAI,IAAM,KAAK,IAAIC,EAAU,EAAID,EAAU,CAAC,GAAK,GAAI,CACzF1E,EAAI,MACF,sBACA,KAAK,IAAI2E,EAAU,EAAID,EAAU,CAAC,EAClC,KAAK,IAAIC,EAAU,EAAID,EAAU,CAAC,CACpC,EACA,IAAMlC,EAAI,EACNoC,EAAY,IAAMC,EAAa,EACjCG,EAAiB,CACf,EAAGb,EAAQ,EAAIU,EAAa,EAAIrC,EAAIuC,EAAIF,EAAa,EAAIrC,EAAIuC,EAC7D,EAAGX,EAAQ,EAAIS,EAAa,EAAIE,EAAIF,EAAa,EAAIE,CACvD,EAEAC,EAAiB,CACf,EAAGb,EAAQ,EAAIU,EAAa,EAAIE,EAAIF,EAAa,EAAIE,EACrD,EAAGX,EAAQ,EAAIS,EAAa,EAAIrC,EAAIuC,EAAIF,EAAa,EAAIrC,EAAIuC,CAC/D,CAEJ,MACE/E,EAAI,MACF,+BACA,KAAK,IAAI2E,EAAU,EAAID,EAAU,CAAC,EAClC,KAAK,IAAIC,EAAU,EAAID,EAAU,CAAC,CACpC,EAEFD,EAAY,KAAKO,EAAgBF,CAAY,CAC/C,MACEL,EAAY,KAAKD,EAASb,CAAC,CAAC,EAGhC,OAAOc,CACT,EAjDmB,cAmDNQ,GAAa9F,EAAA,SAAUM,EAAMC,EAAMwF,EAAWC,EAAaC,EAAWC,EAASC,EAAI,CAC9F,GAAM,CAAE,cAAAC,CAAc,EAAI1F,EAAU,EAChCqD,EAASxD,EAAK,OACd8F,EAAmB,GACjBC,EAAOL,EACb,IAAIM,EAAOL,EACX,IAAMM,EAAkB,CAAC,EACzB,QAAWC,KAAOlG,EAAK,kBACjBmG,GAAaD,CAAG,GAGpBD,EAAgB,KAAKjG,EAAK,kBAAkBkG,CAAG,CAAC,EAG9CF,EAAK,WAAaD,EAAK,YACzBvC,EAASA,EAAO,MAAM,EAAGxD,EAAK,OAAO,OAAS,CAAC,EAC/CwD,EAAO,QAAQuC,EAAK,UAAUvC,EAAO,CAAC,CAAC,CAAC,EACxClD,EAAI,MACF,mBACAN,EAAK,MACL,MACAA,EAAK,IACLwD,EAAOA,EAAO,OAAS,CAAC,EACxBwC,EACAA,EAAK,UAAUxC,EAAOA,EAAO,OAAS,CAAC,CAAC,CAC1C,EACAA,EAAO,KAAKwC,EAAK,UAAUxC,EAAOA,EAAO,OAAS,CAAC,CAAC,CAAC,GAEnDxD,EAAK,YACPM,EAAI,KAAK,mBAAoBkF,EAAU,IAAIxF,EAAK,SAAS,CAAC,EAC1DwD,EAASH,GAAmBrD,EAAK,OAAQwF,EAAU,IAAIxF,EAAK,SAAS,EAAE,IAAI,EAE3E8F,EAAmB,IAGjB9F,EAAK,cACPM,EAAI,MACF,qBACAkF,EAAU,IAAIxF,EAAK,WAAW,EAC9B,KAAK,UAAUwD,EAAQ,KAAM,CAAC,CAChC,EACAA,EAASH,GAAmBG,EAAO,QAAQ,EAAGgC,EAAU,IAAIxF,EAAK,WAAW,EAAE,IAAI,EAAE,QAAQ,EAE5F8F,EAAmB,IAGrB,IAAIhB,EAAWtB,EAAO,OAAQK,GAAM,CAAC,OAAO,MAAMA,EAAE,CAAC,CAAC,EACtDiB,EAAWD,GAAWC,CAAQ,EAC9B,IAAIsB,EAAQC,EAEZ,OADAD,EAAQE,EACAtG,EAAK,MAAO,CAClB,IAAK,SACHoG,EAAQE,EACR,MACF,IAAK,QACHF,EAAQC,EACR,MACF,IAAK,WACHD,EAAQG,EACR,MACF,IAAK,QACHH,EAAQI,EACR,MACF,IAAK,QACHJ,EAAQK,EACR,MACF,IAAK,aACHL,EAAQM,EACR,MACF,IAAK,YACHN,EAAQO,EACR,MACF,IAAK,YACHP,EAAQQ,EACR,MACF,IAAK,UACHR,EAAQS,EACR,MACF,IAAK,OACHT,EAAQU,EACR,MACF,IAAK,YACHV,EAAQW,EACR,MACF,IAAK,aACHX,EAAQY,EACR,MACF,QACEZ,EAAQC,CACZ,CAEA,GAAM,CAAE,EAAArE,EAAG,EAAAC,CAAE,EAAIgF,GAA2BjH,CAAI,EAC1CkH,GAAeC,EAAK,EAAE,EAAEnF,CAAC,EAAE,EAAEC,CAAC,EAAE,MAAMmE,CAAK,EAE7CgB,EACJ,OAAQpH,EAAK,UAAW,CACtB,IAAK,SACHoH,EAAgB,wBAChB,MACF,IAAK,QACHA,EAAgB,uBAChB,MACF,IAAK,YACHA,EAAgB,2BAChB,MACF,QACEA,EAAgB,uBACpB,CACA,OAAQpH,EAAK,QAAS,CACpB,IAAK,QACHoH,GAAiB,sBACjB,MACF,IAAK,SACHA,GAAiB,uBACjB,MACF,IAAK,SACHA,GAAiB,uBACjB,MACF,QACEA,GAAiB,qBACrB,CACA,IAAIC,EACAC,EAAWJ,GAAapC,CAAQ,EAC9ByC,EAAa,MAAM,QAAQvH,EAAK,KAAK,EAAIA,EAAK,MAAQ,CAACA,EAAK,KAAK,EACnEwH,EAAcD,EAAW,KAAM1H,GAAUA,GAAO,WAAW,SAAS,CAAC,EAEzE,GAAIG,EAAK,OAAS,YAAa,CAC7B,IAAMyH,EAAKC,GAAM,IAAI3H,CAAI,EACzB,OAAO,OAAO,CAAC,EAAG+E,CAAQ,EAE1B,IAAM6C,EAAcF,EAAG,KAAKH,EAAU,CACpC,UAAW,GACX,KAAMzB,CACR,CAAC,EAEDuB,GAAiB,cAEjBC,EAAUzG,EAAO+G,CAAW,EACzB,OAAO,MAAM,EACb,KAAK,KAAM3H,EAAK,EAAE,EAClB,KAAK,QAAS,IAAMoH,GAAiBpH,EAAK,QAAU,IAAMA,EAAK,QAAU,GAAG,EAC5E,KAAK,QAASuH,EAAaA,EAAW,OAAO,CAAC3H,EAAKC,IAAUD,EAAM,IAAMC,EAAO,EAAE,EAAI,EAAE,EAC3F,IAAI+H,EAAIP,EAAQ,KAAK,GAAG,EACxBA,EAAQ,KAAK,IAAKO,CAAC,EACnB7H,EAAK,KAAK,EAAE,YAAYsH,EAAQ,KAAK,CAAC,CACxC,KAAO,CACL,IAAMQ,EAAoB5B,EAAgB,KAAK,GAAG,EAC5C6B,EAASP,EAAaA,EAAW,OAAO,CAAC3H,EAAKC,KAAUD,EAAMC,GAAQ,IAAK,EAAE,EAAI,GACnFkI,EAAiB,GACjB/H,EAAK,UACP+H,EAAiB,wBAEf/H,EAAK,YACP+H,EAAiB,mBAAqB/H,EAAK,WAG7C,IAAMgI,EAAYH,EAAoBA,EAAoB,IAAMC,EAAS,IAAMA,EAC/ET,EAAUtH,EACP,OAAO,MAAM,EACb,KAAK,IAAKuH,CAAQ,EAClB,KAAK,KAAMtH,EAAK,EAAE,EAClB,KACC,QACA,IAAMoH,GAAiBpH,EAAK,QAAU,IAAMA,EAAK,QAAU,KAAO+H,GAAkB,GACtF,EACC,KAAK,QAASC,CAAS,EAC1BR,EAAcQ,EAAU,MAAM,gBAAgB,IAAI,CAAC,CACrD,CAuBA,IAAIC,EAAM,IACN9H,EAAU,EAAE,UAAU,qBAAuBA,EAAU,EAAE,MAAM,uBACjE8H,EACE,OAAO,SAAS,SAChB,KACA,OAAO,SAAS,KAChB,OAAO,SAAS,SAChB,OAAO,SAAS,OAClBA,EAAMA,EAAI,QAAQ,MAAO,KAAK,EAAE,QAAQ,MAAO,KAAK,GAEtD3H,EAAI,KAAK,iBAAkBN,EAAK,cAAc,EAC9CM,EAAI,KAAK,eAAgBN,EAAK,YAAY,EAE1CkI,GAAeb,EAASrH,EAAMiI,EAAKrC,EAAIH,EAAa+B,CAAW,EAE/D,IAAI9F,EAAQ,CAAC,EACb,OAAIoE,IACFpE,EAAM,YAAc8B,GAEtB9B,EAAM,aAAe1B,EAAK,OACnB0B,CACT,EAnN0B,cCrb1B,IAAMyG,GAAgBC,EAAA,CAACC,EAAMC,EAAaC,EAAMC,IAAO,CACrDF,EAAY,QAASG,GAAe,CAClCC,GAAQD,CAAU,EAAEJ,EAAME,EAAMC,CAAE,CACpC,CAAC,CACH,EAJsB,iBAMhBG,GAAYP,EAAA,CAACC,EAAME,EAAMC,IAAO,CACpCI,EAAI,MAAM,sBAAuBJ,CAAE,EACnCH,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,iBAAiB,EAC9C,KAAK,QAAS,oBAAsBA,CAAI,EACxC,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,GAAG,EACvB,KAAK,eAAgB,GAAG,EACxB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,oBAAoB,EAEjCF,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,eAAe,EAC5C,KAAK,QAAS,oBAAsBA,CAAI,EACxC,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,oBAAoB,CACnC,EA3BkB,aA6BZM,GAAcT,EAAA,CAACC,EAAME,EAAMC,IAAO,CACtCH,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,mBAAmB,EAChD,KAAK,QAAS,sBAAwBA,CAAI,EAC1C,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,GAAG,EACvB,KAAK,eAAgB,GAAG,EACxB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,0BAA0B,EAEvCF,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,iBAAiB,EAC9C,KAAK,QAAS,sBAAwBA,CAAI,EAC1C,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,0BAA0B,CACzC,EA1BoB,eA2BdO,GAAcV,EAAA,CAACC,EAAME,EAAMC,IAAO,CACtCH,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,mBAAmB,EAChD,KAAK,QAAS,sBAAwBA,CAAI,EAC1C,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,GAAG,EACvB,KAAK,eAAgB,GAAG,EACxB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,0BAA0B,EAEvCF,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,iBAAiB,EAC9C,KAAK,QAAS,sBAAwBA,CAAI,EAC1C,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,0BAA0B,CACzC,EA1BoB,eA2BdQ,GAAaX,EAAA,CAACC,EAAME,EAAMC,IAAO,CACrCH,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,kBAAkB,EAC/C,KAAK,QAAS,qBAAuBA,CAAI,EACzC,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,GAAG,EACvB,KAAK,eAAgB,GAAG,EACxB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,yBAAyB,EAEtCF,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,gBAAgB,EAC7C,KAAK,QAAS,qBAAuBA,CAAI,EACzC,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,2BAA2B,CAC1C,EA1BmB,cA2BbS,GAAWZ,EAAA,CAACC,EAAME,EAAMC,IAAO,CACnCH,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,gBAAgB,EAC7C,KAAK,QAAS,mBAAqBA,CAAI,EACvC,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,GAAG,EACvB,KAAK,eAAgB,GAAG,EACxB,KAAK,SAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,SAAU,OAAO,EACtB,KAAK,OAAQ,aAAa,EAC1B,KAAK,KAAM,CAAC,EACZ,KAAK,KAAM,CAAC,EACZ,KAAK,IAAK,CAAC,EAEdF,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,cAAc,EAC3C,KAAK,QAAS,mBAAqBA,CAAI,EACvC,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,GAAG,EACvB,KAAK,eAAgB,GAAG,EACxB,KAAK,SAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,SAAU,OAAO,EACtB,KAAK,OAAQ,aAAa,EAC1B,KAAK,KAAM,CAAC,EACZ,KAAK,KAAM,CAAC,EACZ,KAAK,IAAK,CAAC,CAChB,EAlCiB,YAmCXU,GAAQb,EAAA,CAACC,EAAME,EAAMC,IAAO,CAChCH,EACG,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,WAAW,EACxC,KAAK,QAAS,UAAYA,CAAI,EAC9B,KAAK,UAAW,WAAW,EAC3B,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,gBAAgB,EACpC,KAAK,cAAe,CAAC,EACrB,KAAK,eAAgB,CAAC,EACtB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,uBAAuB,EACjC,KAAK,QAAS,iBAAiB,EAC/B,MAAM,eAAgB,CAAC,EACvB,MAAM,mBAAoB,KAAK,EAClCF,EACG,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,aAAa,EAC1C,KAAK,QAAS,UAAYA,CAAI,EAC9B,KAAK,UAAW,WAAW,EAC3B,KAAK,OAAQ,GAAG,EAChB,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,gBAAgB,EACpC,KAAK,cAAe,CAAC,EACrB,KAAK,eAAgB,CAAC,EACtB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,wBAAwB,EAClC,KAAK,QAAS,iBAAiB,EAC/B,MAAM,eAAgB,CAAC,EACvB,MAAM,mBAAoB,KAAK,CACpC,EAjCc,SAkCRW,GAASd,EAAA,CAACC,EAAME,EAAMC,IAAO,CACjCH,EACG,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,YAAY,EACzC,KAAK,QAAS,UAAYA,CAAI,EAC9B,KAAK,UAAW,WAAW,EAC3B,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,gBAAgB,EACpC,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,KAAM,GAAG,EACd,KAAK,KAAM,GAAG,EACd,KAAK,IAAK,GAAG,EACb,KAAK,QAAS,iBAAiB,EAC/B,MAAM,eAAgB,CAAC,EACvB,MAAM,mBAAoB,KAAK,EAElCF,EACG,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,cAAc,EAC3C,KAAK,QAAS,UAAYA,CAAI,EAC9B,KAAK,UAAW,WAAW,EAC3B,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,gBAAgB,EACpC,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,KAAM,GAAG,EACd,KAAK,KAAM,GAAG,EACd,KAAK,IAAK,GAAG,EACb,KAAK,QAAS,iBAAiB,EAC/B,MAAM,eAAgB,CAAC,EACvB,MAAM,mBAAoB,KAAK,CACpC,EAtCe,UAuCTY,GAAQf,EAAA,CAACC,EAAME,EAAMC,IAAO,CAChCH,EACG,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,WAAW,EACxC,KAAK,QAAS,gBAAkBA,CAAI,EACpC,KAAK,UAAW,WAAW,EAC3B,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,GAAG,EAChB,KAAK,cAAe,gBAAgB,EACpC,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EAEb,KAAK,IAAK,2BAA2B,EACrC,KAAK,QAAS,iBAAiB,EAC/B,MAAM,eAAgB,CAAC,EACvB,MAAM,mBAAoB,KAAK,EAElCF,EACG,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,aAAa,EAC1C,KAAK,QAAS,gBAAkBA,CAAI,EACpC,KAAK,UAAW,WAAW,EAC3B,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,GAAG,EAChB,KAAK,cAAe,gBAAgB,EACpC,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EAEb,KAAK,IAAK,2BAA2B,EACrC,KAAK,QAAS,iBAAiB,EAC/B,MAAM,eAAgB,CAAC,EACvB,MAAM,mBAAoB,KAAK,CACpC,EApCc,SAqCRa,GAAOhB,EAAA,CAACC,EAAME,EAAMC,IAAO,CAC/BH,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,UAAU,EACvC,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,cAAe,gBAAgB,EACpC,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,2BAA2B,CAC1C,EAba,QAePc,GAAWjB,EAAA,CAACC,EAAME,EAAMC,IAAO,CACnCH,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,eAAe,EAC5C,KAAK,QAAS,kBAAoBA,CAAI,EACtC,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,yBAAyB,EAEtCF,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,aAAa,EAC1C,KAAK,QAAS,kBAAoBA,CAAI,EACtC,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,uBAAuB,CACtC,EA1BiB,YA4BXe,GAAclB,EAAA,CAACC,EAAME,EAAMC,IAAO,CACtC,IAAMe,EAAclB,EACjB,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,iBAAiB,EAC9C,KAAK,QAAS,oBAAsBA,CAAI,EACxC,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACxBgB,EACG,OAAO,QAAQ,EACf,KAAK,OAAQ,OAAO,EACpB,KAAK,KAAM,EAAE,EACb,KAAK,KAAM,CAAC,EACZ,KAAK,IAAK,CAAC,EACdA,EAAY,OAAO,MAAM,EAAE,KAAK,IAAK,YAAY,EAEjD,IAAMC,EAAYnB,EACf,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,eAAe,EAC5C,KAAK,QAAS,oBAAsBA,CAAI,EACxC,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACxBiB,EACG,OAAO,QAAQ,EACf,KAAK,OAAQ,OAAO,EACpB,KAAK,KAAM,CAAC,EACZ,KAAK,KAAM,CAAC,EACZ,KAAK,IAAK,CAAC,EACdA,EAAU,OAAO,MAAM,EAAE,KAAK,IAAK,cAAc,CACnD,EApCoB,eAsCdC,GAAcrB,EAAA,CAACC,EAAME,EAAMC,IAAO,CACtCH,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,iBAAiB,EAC9C,KAAK,QAAS,oBAAsBA,CAAI,EACxC,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,EAAE,EACf,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,8CAA8C,EAE3DF,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,eAAe,EAC5C,KAAK,QAAS,oBAAsBA,CAAI,EACxC,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,EAAE,EACf,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,0CAA0C,CACzD,EA1BoB,eA4BdmB,GAAetB,EAAA,CAACC,EAAME,EAAMC,IAAO,CACvC,IAAMe,EAAclB,EACjB,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,kBAAkB,EAC/C,KAAK,QAAS,qBAAuBA,CAAI,EACzC,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,EAAE,EACf,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACxBgB,EAAY,OAAO,QAAQ,EAAE,KAAK,OAAQ,OAAO,EAAE,KAAK,KAAM,EAAE,EAAE,KAAK,KAAM,EAAE,EAAE,KAAK,IAAK,CAAC,EAC5FA,EAAY,OAAO,MAAM,EAAE,KAAK,IAAK,+BAA+B,EAEpE,IAAMC,EAAYnB,EACf,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,gBAAgB,EAC7C,KAAK,QAAS,qBAAuBA,CAAI,EACzC,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,EAAE,EACf,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACxBiB,EAAU,OAAO,QAAQ,EAAE,KAAK,OAAQ,OAAO,EAAE,KAAK,KAAM,CAAC,EAAE,KAAK,KAAM,EAAE,EAAE,KAAK,IAAK,CAAC,EACzFA,EAAU,OAAO,MAAM,EAAE,KAAK,IAAK,iCAAiC,CACtE,EA1BqB,gBA4BfG,GAAoBvB,EAAA,CAACC,EAAME,EAAMC,IAAO,CAC5CH,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,uBAAuB,EACpD,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,EAAE,EACf,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KACC,IACA;AAAA;AAAA;AAAA,YAIF,CACJ,EAlB0B,qBAmBpBqB,GAAuBxB,EAAA,CAACC,EAAME,EAAMC,IAAO,CAC/C,IAAMqB,EAAexB,EAClB,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAMG,EAAK,IAAMD,EAAO,4BAA4B,EACzD,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,EAAE,EACf,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,GAAG,EAEbsB,EAAa,OAAO,QAAQ,EAAE,KAAK,KAAM,EAAE,EAAE,KAAK,KAAM,EAAE,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,OAAQ,MAAM,EAE5FA,EAAa,OAAO,MAAM,EAAE,KAAK,KAAM,CAAC,EAAE,KAAK,KAAM,EAAE,EAAE,KAAK,KAAM,EAAE,EAAE,KAAK,KAAM,EAAE,EAErFA,EAAa,OAAO,MAAM,EAAE,KAAK,KAAM,CAAC,EAAE,KAAK,KAAM,EAAE,EAAE,KAAK,KAAM,EAAE,EAAE,KAAK,KAAM,EAAE,CACvF,EAjB6B,wBAoBvBnB,GAAU,CACd,UAAAC,GACA,YAAAE,GACA,YAAAC,GACA,WAAAC,GACA,SAAAC,GACA,MAAAC,GACA,OAAAC,GACA,MAAAC,GACA,KAAAC,GACA,SAAAC,GACA,YAAAC,GACA,YAAAG,GACA,aAAAC,GACA,kBAAAC,GACA,qBAAAC,EACF,EACOE,GAAQ3B", "names": ["addEdgeMarkers", "__name", "svgPath", "edge", "url", "id", "diagramType", "strokeColor", "addEdgeMarker", "arrowTypesMap", "position", "arrowType", "arrowTypeInfo", "log", "endMarkerType", "originalMarkerId", "colorId", "coloredMarkerId", "originalMarker", "<PERSON><PERSON><PERSON><PERSON>", "path", "edgeLabels", "terminalLabels", "clear", "__name", "getLabelStyles", "styleArray", "acc", "style", "insertEdgeLabel", "elem", "edge", "useHtmlLabels", "evaluate", "getConfig", "labelElement", "createText", "log", "edgeLabel", "label", "bbox", "div", "dv", "select_default", "fo", "startLabelElement", "createLabel_default", "startEdgeLabelLeft", "inner", "slBox", "setTerminalWidth", "startEdgeLabelRight", "endLabelElement", "endEdgeLabelLeft", "endEdgeLabelRight", "value", "positionEdgeLabel", "paths", "path", "siteConfig", "subGraphTitleTotalMargin", "getSubGraphTitleMargins", "el", "x", "y", "pos", "utils_default", "outsideNode", "node", "point", "dx", "dy", "w", "h", "intersection", "outsidePoint", "insidePoint", "r", "Q", "R", "q", "res", "_x", "_y", "cutPathAtIntersect", "_points", "boundaryNode", "points", "lastPointOutside", "isInside", "inter", "pointPresent", "p", "extractCornerPoints", "cornerPoints", "cornerPointPositions", "i", "prev", "curr", "next", "findAdjacentPoint", "pointA", "pointB", "distance", "xDiff", "yDiff", "length", "ratio", "fixCorners", "lineData", "newLineData", "prevPoint", "nextPoint", "cornerPoint", "newPrevPoint", "newNextPoint", "a", "newCornerPoint", "insertEdge", "clusterDb", "diagramType", "startNode", "endNode", "id", "handDrawnSeed", "pointsHas<PERSON><PERSON>ed", "tail", "head", "edgeClassStyles", "key", "isLabelStyle", "curve", "basis_default", "linear_default", "cardinal_default", "bumpX", "bumpY", "catmullRom_default", "monotoneX", "monotoneY", "natural_default", "step_default", "stepAfter", "stepBefore", "getLineFunctionsWithOffset", "lineFunction", "line_default", "strokeClasses", "svgPath", "linePath", "edgeStyles", "strokeColor", "rc", "at", "svgPathNode", "d", "stylesFromClasses", "styles", "animationClass", "pathStyle", "url", "addEdgeMarkers", "insertMarkers", "__name", "elem", "markerArray", "type", "id", "markerName", "markers", "extension", "log", "composition", "aggregation", "dependency", "lollipop", "point", "circle", "cross", "barb", "only_one", "zero_or_one", "startMarker", "<PERSON><PERSON><PERSON><PERSON>", "one_or_more", "zero_or_more", "requirement_arrow", "requirement_contains", "containsNode", "markers_default"]}