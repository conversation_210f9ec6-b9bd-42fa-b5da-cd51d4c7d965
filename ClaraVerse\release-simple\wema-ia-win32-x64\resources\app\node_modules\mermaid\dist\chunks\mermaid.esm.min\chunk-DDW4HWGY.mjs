import{a as u,b as n,c as i,d as o,e as h,f as r,g as s,k as l,n as d,p as m}from"./chunk-JVB3IFOF.mjs";import{a as c}from"./chunk-GTKDMUJJ.mjs";var A=class extends m{static{c(this,"ArchitectureTokenBuilder")}static{r(this,"ArchitectureTokenBuilder")}constructor(){super(["architecture"])}},C=class extends d{static{c(this,"ArchitectureValueConverter")}static{r(this,"ArchitectureValueConverter")}runCustomConverter(t,e,a){if(t.name==="ARCH_ICON")return e.replace(/[()]/g,"").trim();if(t.name==="ARCH_TEXT_ICON")return e.replace(/["()]/g,"");if(t.name==="ARCH_TITLE")return e.replace(/[[\]]/g,"").trim()}},v={parser:{TokenBuilder:r(()=>new A,"TokenBuilder"),ValueConverter:r(()=>new C,"ValueConverter")}};function T(t=o){let e=i(n(t),s),a=i(u({shared:e}),l,v);return e.ServiceRegistry.register(a),{shared:e,Architecture:a}}c(T,"createArchitectureServices");r(T,"createArchitectureServices");export{v as a,T as b};
