{"version": 3, "sources": ["../../../../parser/dist/chunks/mermaid-parser.core/chunk-C4OEIS7N.mjs"], "sourcesContent": ["import {\n  AbstractMermaidTokenBuilder,\n  AbstractMermaidValueConverter,\n  ArchitectureGeneratedModule,\n  MermaidGeneratedSharedModule,\n  __name\n} from \"./chunk-7PKI6E2E.mjs\";\n\n// src/language/architecture/module.ts\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject\n} from \"langium\";\n\n// src/language/architecture/tokenBuilder.ts\nvar ArchitectureTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"ArchitectureTokenBuilder\");\n  }\n  constructor() {\n    super([\"architecture\"]);\n  }\n};\n\n// src/language/architecture/valueConverter.ts\nvar ArchitectureValueConverter = class extends AbstractMermaidValueConverter {\n  static {\n    __name(this, \"ArchitectureValueConverter\");\n  }\n  runCustomConverter(rule, input, _cstNode) {\n    if (rule.name === \"ARCH_ICON\") {\n      return input.replace(/[()]/g, \"\").trim();\n    } else if (rule.name === \"ARCH_TEXT_ICON\") {\n      return input.replace(/[\"()]/g, \"\");\n    } else if (rule.name === \"ARCH_TITLE\") {\n      return input.replace(/[[\\]]/g, \"\").trim();\n    }\n    return void 0;\n  }\n};\n\n// src/language/architecture/module.ts\nvar ArchitectureModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */ __name(() => new ArchitectureTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */ __name(() => new ArchitectureValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createArchitectureServices(context = EmptyFileSystem) {\n  const shared = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Architecture = inject(\n    createDefaultCoreModule({ shared }),\n    ArchitectureGeneratedModule,\n    ArchitectureModule\n  );\n  shared.ServiceRegistry.register(Architecture);\n  return { shared, Architecture };\n}\n__name(createArchitectureServices, \"createArchitectureServices\");\n\nexport {\n  ArchitectureModule,\n  createArchitectureServices\n};\n"], "mappings": "iJAiBA,IAAIA,EAA2B,cAAcC,CAA4B,CAjBzE,MAiByE,CAAAC,EAAA,iCACvE,MAAO,CACLA,EAAO,KAAM,0BAA0B,CACzC,CACA,aAAc,CACZ,MAAM,CAAC,cAAc,CAAC,CACxB,CACF,EAGIC,EAA6B,cAAcC,CAA8B,CA3B7E,MA2B6E,CAAAF,EAAA,mCAC3E,MAAO,CACLA,EAAO,KAAM,4BAA4B,CAC3C,CACA,mBAAmBG,EAAMC,EAAOC,EAAU,CACxC,GAAIF,EAAK,OAAS,YAChB,OAAOC,EAAM,QAAQ,QAAS,EAAE,EAAE,KAAK,EAClC,GAAID,EAAK,OAAS,iBACvB,OAAOC,EAAM,QAAQ,SAAU,EAAE,EAC5B,GAAID,EAAK,OAAS,aACvB,OAAOC,EAAM,QAAQ,SAAU,EAAE,EAAE,KAAK,CAG5C,CACF,EAGIE,EAAqB,CACvB,OAAQ,CACN,aAA8BN,EAAO,IAAM,IAAIF,EAA4B,cAAc,EACzF,eAAgCE,EAAO,IAAM,IAAIC,EAA8B,gBAAgB,CACjG,CACF,EACA,SAASM,EAA2BC,EAAUC,EAAiB,CAC7D,IAAMC,EAASC,EACbC,EAA8BJ,CAAO,EACrCK,CACF,EACMC,EAAeH,EACnBI,EAAwB,CAAE,OAAAL,CAAO,CAAC,EAClCM,EACAV,CACF,EACA,OAAAI,EAAO,gBAAgB,SAASI,CAAY,EACrC,CAAE,OAAAJ,EAAQ,aAAAI,CAAa,CAChC,CAZSd,EAAAO,EAAA,8BAaTP,EAAOO,EAA4B,4BAA4B", "names": ["ArchitectureTokenBuilder", "AbstractMermaidTokenBuilder", "__name", "ArchitectureValueConverter", "AbstractMermaidValueConverter", "rule", "input", "_cstNode", "ArchitectureModule", "createArchitectureServices", "context", "EmptyFileSystem", "shared", "inject", "createDefaultSharedCoreModule", "MermaidGeneratedSharedModule", "Architecture", "createDefaultCoreModule", "ArchitectureGeneratedModule"]}