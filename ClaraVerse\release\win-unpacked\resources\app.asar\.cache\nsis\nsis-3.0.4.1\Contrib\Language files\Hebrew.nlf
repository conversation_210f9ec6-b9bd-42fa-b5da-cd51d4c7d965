﻿# Hebrew NSIS language file
NLF v6
# Language ID
1037
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
1255
# RTL - anything else than RTL means LTR
RTL
# Translation by <PERSON> (aka <PERSON>CH<PERSON><PERSON>), fixed by <PERSON><PERSON>
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
התקנת $(^Name)
# ^UninstallCaption
הסרת $(^Name)
# ^LicenseSubCaption
: הסכם רישוי
# ^ComponentsSubCaption
: אפשרויות התקנה
# ^DirSubCaption
: תיקיית התקנה
# ^InstallingSubCaption
: מתקין
# ^CompletedSubCaption
: ההתקנה הושלמה
# ^UnComponentsSubCaption
: אפשרויות הסרה
# ^UnDirSubCaption
: תיקייה להסרה
# ^ConfirmSubCaption
: אישור הסרה
# ^UninstallingSubCaption
: מסיר
# ^UnCompletedSubCaption
: ההסרה הושלמה
# ^BackBtn
< ה&קודם
# ^NextBtn
ה&בא >
# ^AgreeBtn
אני &מסכים
# ^AcceptBtn
אני &מסכים לתנאי הסכם הרישוי
# ^DontAcceptBtn
אני &לא מסכים לתנאי הסכם הרישוי
# ^InstallBtn
&התקן
# ^UninstallBtn
&הסר
# ^CancelBtn
ביטול
# ^CloseBtn
סגור&
# ^BrowseBtn
&עיין...
# ^ShowDetailsBtn
ה&צג פרטים
# ^ClickNext
לחץ על הבא כדי להמשיך.
# ^ClickInstall
לחץ על התקן כדי להתחיל את ההתקנה.
# ^ClickUninstall
לחץ על הסר כדי להתחיל את ההסרה.
# ^Name
שם
# ^Completed
הפעולה הושלמה
# ^LicenseText
אנא סקור את הסכם הרישוי לפני התקנת $(^NameDA). אם הינך מקבל את כל תנאי ההסכם, לחץ 'אני מסכים'.
# ^LicenseTextCB
אנא סקור את הסכם הרישוי לפני התקנת $(^NameDA). אם הינך מקבל את כל תנאי ההסכם, סמן את תיבת הסימון שלהלן. $_CLICK
# ^LicenseTextRB
אנא סקור את הסכם הרישוי לפני התקנת $(^NameDA). אם הינך מקבל את כל תנאי ההסכם, בחר באפשרות הראשונה שלהלן. $_CLICK
# ^UnLicenseText
אנא סקור את הסכם הרישוי לפני הסרת $(^NameDA). אם הינך מסכים לכל תנאי ההסכם, לחץ 'אני מסכים'.
# ^UnLicenseTextCB
אנא סקור את הסכם הרישוי לפני הסרת $(^NameDA). אם הינך מסכים לכל תנאי ההסכם, סמן את תיבת הסימון שלהלן. $_CLICK
# ^UnLicenseTextRB
אנא סקור את הסכם הרישוי לפני הסרת $(^NameDA). אם הינך מסכים לכל תנאי ההסכם, בחר באפשרות הראשונה שלהלן. $_CLICK
# ^Custom
מותאם אישית
# ^ComponentsText
סמן את הרכיבים שברצונך להתקין ובטל את הסימון של רכיבים שאין ברצונך להתקין. $_CLICK
# ^ComponentsSubText1
בחר סוג התקנה:
# ^ComponentsSubText2_NoInstTypes
בחר רכיבים להתקנה:
# ^ComponentsSubText2
או, בחר רכיבי רשות להתקנה:
# ^UnComponentsText
סמן את הרכיבים שברצונך להסיר ובטל את הסימון של רכיבים שאין ברצונך להסיר. $_CLICK
# ^UnComponentsSubText1
בחר סוג הסרה:
# ^UnComponentsSubText2_NoInstTypes
בחר רכיבים להסרה:
# ^UnComponentsSubText2
או, בחר רכיבי רשות להסרה:
# ^DirText
תוכנית זו תתקין את $(^NameDA) לתיקייה שלהלן. כדי להתקין לתיקייה אחרת, לחץ על 'עיין' ובחר תיקייה אחרת. $_CLICK
# ^DirSubText
תיקיית יעד
# ^DirBrowseText
בחר תיקייה להתקנת $(^NameDA):
# ^UnDirText
תוכנית זו תסיר את $(^NameDA) מהתיקייה שלהלן. כדי להסיר מתיקייה אחרת, לחץ על 'עיין' ובחר תיקייה אחרת. $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
בחר תיקייה ממנה תוסר $(^NameDA):
# ^SpaceAvailable
"מקום פנוי: "
# ^SpaceRequired
"מקום דרוש: "
# ^UninstallingText
הסרת $(^NameDA) תתבצע מהתיקייה שלהלן. $_CLICK
# ^UninstallingSubText
מסיר מ:
# ^FileError
ארעה שגיאה בעת פתיחת קובץ לכתיבה:\r\n\t"$0"\r\nלחץ על ביטול כדי לבטל את ההתקנה,\r\nנסה שנית כדי לנסות לפתוח את הקובץ שוב, או\r\nהתעלם כדי לדלג על הקובץ
# ^FileError_NoIgnore
ארעה שגיאה בעת פתיחת קובץ לכתיבה:\r\n\t"$0"\r\nלחץ על נסה שנית כדי לנסות לפתוח את הקובץ  שוב, או\r\nביטול כדי לבטל את התתקנה
# ^CantWrite
"לא ניתן לכתוב: "
# ^CopyFailed
ההעתקה נכשלה
# ^CopyTo
העתק ל-
# ^Registering
"רושם: "
# ^Unregistering
"ביטול רישום: "
# ^SymbolNotFound
"סמל לא נמצא: "
# ^CouldNotLoad
"לא ניתן לטעון: "
# ^CreateFolder
"צור תיקייה: "
# ^CreateShortcut
"צור קיצור דרך: "
# ^CreatedUninstaller
"מסיר התקנה נוצר: "
# ^Delete
"מחק קובץ: "
# ^DeleteOnReboot
"מחק אחרי אתחול: "
# ^ErrorCreatingShortcut
"שגיאה בעת יצירת קיצור דרך: "
# ^ErrorCreating
"שגיאה בעת יצירת: "
# ^ErrorDecompressing
שגיאה בעת פרישת מידע! התקנה פגומה?
# ^ErrorRegistering
שגיאה בעת רישום DLL
# ^ExecShell
"בצע פעולת-קובץ: "
# ^Exec
"בצע: "
# ^Extract
"פרוש: "
# ^ErrorWriting
"פרוש: שגיאה בעת כתיבה לקובץ "
# ^InvalidOpcode
התקנה פגומה! פקודת ביצוע שגויה
# ^NoOLE
"אין OLE ל: "
# ^OutputFolder
"תיקיית פלט: "
# ^RemoveFolder
"הסר תיקייה: "
# ^RenameOnReboot
"שנה שם לאחר אתחול: "
# ^Rename
"שנה שם: "
# ^Skipped
"דלג: "
# ^CopyDetails
העתק פרטים ללוח
# ^LogInstall
שמור רישום פעילויות ההתקנה
# ^Byte
"ב
# ^Kilo
" ק"
# ^Mega
" מ"
# ^Giga
" ג"