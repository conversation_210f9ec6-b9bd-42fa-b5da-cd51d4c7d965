{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2040997289075261528, "path": 11183322622621637024, "deps": [[376837177317575824, "build_script_build", false, 3130993089654848238], [4143744114649553716, "raw_window_handle", false, 9360050597165366007], [5986029879202738730, "log", false, 2351528888146949315], [10281541584571964250, "windows_sys", false, 4599155384506209632]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\softbuffer-2fb69eefc318208d\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}