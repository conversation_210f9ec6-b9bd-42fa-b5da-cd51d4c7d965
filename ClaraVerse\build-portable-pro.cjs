﻿#!/usr/bin/env node
/**
 *  WeMa IA - Build Portable Professionnel
 * Crée un EXE portable qui fonctionne PARFAITEMENT
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class PortableProfessionalBuilder {
  constructor() {
    this.projectRoot = process.cwd();
    this.buildDir = path.join(this.projectRoot, 'build-portable-pro');
    this.releaseDir = path.join(this.projectRoot, 'release');
  }

  /**
   *  Nettoyage initial
   */
  cleanup() {
    console.log(' Nettoyage...');
    
    if (fs.existsSync(this.buildDir)) {
      fs.rmSync(this.buildDir, { recursive: true, force: true });
    }
    
    if (fs.existsSync(this.releaseDir)) {
      fs.rmSync(this.releaseDir, { recursive: true, force: true });
    }
    
    fs.mkdirSync(this.buildDir, { recursive: true });
    fs.mkdirSync(this.releaseDir, { recursive: true });
  }

  /**
   *  Build React optimisé
   */
  buildReact() {
    console.log(' Build React optimisé...');
    
    try {
      execSync('npm run build', { 
        stdio: 'inherit',
        env: { ...process.env, NODE_ENV: 'production' }
      });
      console.log(' Build React terminé');
    } catch (error) {
      console.error(' Erreur build React:', error.message);
      process.exit(1);
    }
  }

  /**
   *  Préparer structure portable
   */
  preparePortableStructure() {
    console.log(' Préparation structure portable...');
    
    // Copier dist React
    const distSrc = path.join(this.projectRoot, 'dist');
    const distDest = path.join(this.buildDir, 'dist');
    fs.cpSync(distSrc, distDest, { recursive: true });
    
    // Copier backend Python COMPLET
    const backendSrc = path.join(this.projectRoot, 'py_backend');
    const backendDest = path.join(this.buildDir, 'py_backend');
    fs.cpSync(backendSrc, backendDest, { recursive: true });
    
    // Copier fichiers Electron
    fs.copyFileSync(
      path.join(this.projectRoot, 'public', 'electron.cjs'),
      path.join(this.buildDir, 'main.js')
    );
    
    fs.copyFileSync(
      path.join(this.projectRoot, 'public', 'preload.js'),
      path.join(this.buildDir, 'preload.js')
    );
    
    // Copier logos
    const logos = ['wema-logo-light.png', 'wema-logo-dark.png', 'wema-icon-256.ico'];
    logos.forEach(logo => {
      const src = path.join(this.projectRoot, 'public', logo);
      const dest = path.join(this.buildDir, logo);
      if (fs.existsSync(src)) {
        fs.copyFileSync(src, dest);
      }
    });
    
    console.log(' Structure portable préparée');
  }

  /**
   *  Créer package.json portable
   */
  createPortablePackageJson() {
    console.log(' Création package.json portable...');
    
    const packageJson = {
      "name": "wema-ia-portable",
      "version": "0.1.2",
      "description": "WeMa IA - Assistant IA Professionnel",
      "main": "main.js",
      "author": "WeMa IA Team",
      "license": "MIT",
      "build": {
        "appId": "com.wema-ia.portable",
        "productName": "WeMa IA",
        "compression": "store",
        "directories": {
          "output": "../release"
        },
        "files": [
          "**/*"
        ],
        "extraResources": [
          {
            "from": "py_backend",
            "to": "py_backend",
            "filter": ["**/*"]
          }
        ],
        "win": {
          "target": [
            {
              "target": "portable",
              "arch": ["x64"]
            }
          ],
          "icon": "wema-icon-256.ico"
        },
        "portable": {
          "artifactName": "WeMa_IA_Professional.exe"
        }
      }
    };
    
    fs.writeFileSync(
      path.join(this.buildDir, 'package.json'),
      JSON.stringify(packageJson, null, 2)
    );
    
    console.log(' Package.json portable créé');
  }

  /**
   *  Build Electron portable
   */
  buildElectronPortable() {
    console.log(' Build Electron portable...');
    
    try {
      execSync('npx electron-builder --win portable', {
        stdio: 'inherit',
        cwd: this.buildDir,
        env: { ...process.env, NODE_ENV: 'production' }
      });
      console.log(' Build Electron portable terminé');
    } catch (error) {
      console.error(' Erreur build Electron:', error.message);
      process.exit(1);
    }
  }

  /**
   *  Finaliser portable
   */
  finalizePortable() {
    console.log(' Finalisation portable...');
    
    const exePath = path.join(this.releaseDir, 'WeMa_IA_Professional.exe');
    
    if (fs.existsSync(exePath)) {
      const stats = fs.statSync(exePath);
      const sizeMB = (stats.size / 1024 / 1024).toFixed(0);
      
      // Copier vers Downloads
      const downloadsPath = path.join(require('os').homedir(), 'Downloads');
      const finalPath = path.join(downloadsPath, `WeMa_IA_Professional_${sizeMB}MB.exe`);
      
      fs.copyFileSync(exePath, finalPath);
      
      console.log(` EXE final: ${finalPath}`);
      console.log(` Taille: ${sizeMB} MB`);
    }
  }

  /**
   *  Lancer le build complet
   */
  async run() {
    try {
      console.log(' WeMa IA - Build Portable Professionnel');
      console.log('=====================================');
      
      this.cleanup();
      this.buildReact();
      this.preparePortableStructure();
      this.createPortablePackageJson();
      this.buildElectronPortable();
      this.finalizePortable();
      
      console.log(' BUILD PORTABLE PROFESSIONNEL TERMINÉ !');
      
    } catch (error) {
      console.error(' Erreur:', error.message);
      process.exit(1);
    }
  }
}

// Exécuter le build
if (require.main === module) {
  const builder = new PortableProfessionalBuilder();
  builder.run();
}

module.exports = PortableProfessionalBuilder;
