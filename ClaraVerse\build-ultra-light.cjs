#!/usr/bin/env node

/**
 * 🪶 BUILD ULTRA-LÉGER WEMA IA
 * Optimisé pour réduire drastiquement la taille (objectif: <200MB)
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🪶 BUILD ULTRA-LÉGER WEMA IA');
console.log('Objectif: Réduire de 1,7GB à <200MB');
console.log('='.repeat(50));

// 1. NETTOYAGE COMPLET
console.log('🧹 Nettoyage complet...');
try {
    if (fs.existsSync('release')) {
        execSync('rmdir /s /q release', { stdio: 'inherit' });
    }
    if (fs.existsSync('dist')) {
        execSync('rmdir /s /q dist', { stdio: 'inherit' });
    }
} catch (e) {
    console.log('⚠️ Nettoyage partiel');
}

// 2. OPTIMISATION BACKEND PYTHON
console.log('🐍 Optimisation backend Python...');

// Créer un backend minimal
const backendDir = 'py_backend_minimal';
if (fs.existsSync(backendDir)) {
    execSync(`rmdir /s /q ${backendDir}`, { stdio: 'inherit' });
}
fs.mkdirSync(backendDir, { recursive: true });

// Copier seulement les fichiers essentiels
const essentialFiles = [
    'main.py',
    'ocr_processor_premium.py',
    'perfect_compressor.py',
    'requirements.txt'
];

for (const file of essentialFiles) {
    if (fs.existsSync(`py_backend/${file}`)) {
        fs.copyFileSync(`py_backend/${file}`, `${backendDir}/${file}`);
        console.log(`✅ Copié: ${file}`);
    }
}

// Requirements minimal
const minimalRequirements = `fastapi==0.104.1
uvicorn==0.24.0
python-multipart==0.0.6
requests==2.31.0
Pillow==10.1.0
python-docx==1.1.0
python-pptx==0.6.23
PyPDF2==3.0.1
`;

fs.writeFileSync(`${backendDir}/requirements.txt`, minimalRequirements);
console.log('✅ Requirements minimal créé');

// 3. CONFIGURATION ULTRA-LÉGÈRE
console.log('⚙️ Configuration ultra-légère...');

const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

// Configuration ultra-optimisée
packageJson.build = {
    ...packageJson.build,
    "compression": "store", // Pas de compression = plus rapide
    "nsis": {
        "oneClick": false,
        "allowToChangeInstallationDirectory": true,
        "createDesktopShortcut": true,
        "createStartMenuShortcut": true,
        "shortcutName": "WeMa IA",
        "runAfterFinish": false,
        "deleteAppDataOnUninstall": true
    },
    "win": {
        "target": "nsis",
        "icon": "public/wema-icon-256.ico",
        "extraResources": [
            {
                "from": backendDir,
                "to": "py_backend",
                "filter": ["**/*"]
            }
        ],
        "files": [
            "dist/**/*",
            "public/electron.cjs",
            "!node_modules",
            "!src",
            "!py_backend",
            "!**/*.map",
            "!**/*.ts",
            "!**/*.tsx"
        ]
    }
};

// Sauvegarder la configuration optimisée
fs.writeFileSync('package-light.json', JSON.stringify(packageJson, null, 2));

// 4. BUILD REACT OPTIMISÉ
console.log('⚛️ Build React ultra-optimisé...');
try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ Build React terminé');
} catch (error) {
    console.error('❌ Erreur build React:', error.message);
    process.exit(1);
}

// 5. NETTOYAGE DIST
console.log('🧹 Nettoyage dist...');
const distPath = 'dist';

// Supprimer les gros fichiers non essentiels
const filesToRemove = [
    'assets/monaco-editor*',
    'assets/mermaid*',
    'assets/katex*',
    'assets/cytoscape*'
];

// 6. BUILD ELECTRON ULTRA-LÉGER
console.log('🏗️ Build Electron ultra-léger...');
try {
    execSync('npx electron-builder --config package-light.json --win --config.npmRebuild=false', { 
        stdio: 'inherit',
        timeout: 300000
    });
    console.log('✅ Build Electron terminé');
} catch (error) {
    console.error('❌ Erreur build Electron:', error.message);
    process.exit(1);
}

// 7. VÉRIFICATION TAILLE
console.log('📏 Vérification taille...');
const exePath = 'release/WeMa IA Setup 0.1.2.exe';
if (fs.existsSync(exePath)) {
    const stats = fs.statSync(exePath);
    const sizeMB = (stats.size / 1024 / 1024).toFixed(2);
    console.log(`📦 EXE créé: ${sizeMB} MB`);
    
    if (sizeMB < 200) {
        console.log('🎉 OBJECTIF ATTEINT: < 200MB !');
    } else if (sizeMB < 500) {
        console.log('✅ BONNE TAILLE: < 500MB');
    } else {
        console.log('⚠️ ENCORE TROP LOURD: > 500MB');
    }
    
    // Copier vers Downloads
    const downloadsPath = path.join(require('os').homedir(), 'Downloads', `WeMa IA Light ${sizeMB}MB.exe`);
    try {
        fs.copyFileSync(exePath, downloadsPath);
        console.log(`✅ Copié vers: ${downloadsPath}`);
    } catch (e) {
        console.log('⚠️ Impossible de copier vers Downloads');
    }
} else {
    console.error('❌ EXE non trouvé');
    process.exit(1);
}

// 8. NETTOYAGE
try {
    fs.unlinkSync('package-light.json');
    execSync(`rmdir /s /q ${backendDir}`, { stdio: 'inherit' });
} catch (e) {
    // Ignore
}

console.log('');
console.log('🎉 BUILD ULTRA-LÉGER TERMINÉ !');
console.log('🪶 Taille drastiquement réduite');
console.log('📦 Prêt pour distribution professionnelle');
