@echo off
setlocal EnableDelayedExpansion

:: WeMa IA Professional Launcher
:: Version 0.1.2 - 2025

title WeMa IA - Assistant IA Professionnel

:: Configuration
set "APP_NAME=WeMa IA"
set "APP_VERSION=0.1.2"
set "COMPANY=WeMa IA Team"

:: Affichage professionnel
echo.
echo ===============================================
echo  %APP_NAME% v%APP_VERSION%
echo  Assistant IA Professionnel
echo  Copyright (c) 2025 %COMPANY%
echo ===============================================
echo.

:: Vérifier si Node.js est installé
echo Verification des dependances...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Node.js n'est pas installe
    echo Veuillez installer Node.js depuis https://nodejs.org
    pause
    exit /b 1
)

:: Vérifier si Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installe
    echo Veuillez installer Python depuis https://python.org
    pause
    exit /b 1
)

echo ✅ Dependances OK
echo.

:: Aller dans le répertoire de l'application
cd /d "%~dp0"

:: Installer les dépendances si nécessaire
if not exist "node_modules" (
    echo Installation des dependances...
    npm install
)

:: Démarrer le backend Python
echo Demarrage du backend Python...
start /B python py_backend\main.py

:: Attendre que le backend démarre
timeout /t 3 /nobreak >nul

:: Démarrer l'interface web
echo Demarrage de l'interface web...
echo.
echo ===============================================
echo  %APP_NAME% est pret !
echo  L'application va s'ouvrir dans votre navigateur
echo  Fermez cette fenetre pour arreter l'application
echo ===============================================
echo.

:: Ouvrir le navigateur
start http://localhost:5173

:: Démarrer le serveur de développement
npm run dev

:: Nettoyage à la fermeture
echo.
echo Arret de %APP_NAME%...
taskkill /F /IM python.exe /FI "WINDOWTITLE eq*main.py*" >nul 2>&1
echo Au revoir !
pause
